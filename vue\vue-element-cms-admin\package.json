{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@aws-sdk/client-s3": "^3.238.0", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.21.1", "clipboard": "^2.0.8", "core-js": "^3.24.0", "echarts": "^5.1.2", "el-tree-transfer": "^2.4.7", "element-resize-detector": "^1.2.2", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "html2canvas": "^1.3.2", "js-cookie": "2.2.0", "jszip": "^3.10.0", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "sortablejs": "^1.13.0", "spark-md5": "^3.0.2", "vue": "2.6.10", "vue-count-to": "^1.0.13", "vue-ele-gallery": "^2.1.7", "vue-i18n": "^8.23.0", "vue-loader": "^14.1.1", "vue-pdf": "^4.3.0", "vue-qr": "^2.5.0", "vue-router": "3.0.6", "vue-video-player": "^5.0.2", "vuex": "3.1.0", "xlsx": "^0.16.9"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "^26.6.3", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "^7.3.1", "screenfull": "^5.1.0", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10", "webpack": "^4.46.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}
<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-07 10:39:46
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-14 17:33:16
 * @FilePath: /vue-element-cms-admin/src/views/exercise/detail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-card v-loading="cardLoading" class="box-card">
      <div slot="header">
        <span>{{ pageTitle }}</span>
      </div>
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="基础信息" name="exerciseInfo">
          <el-form ref="form" :model="form" label-width="80px">
            <el-form-item label="选择题库" prop="questionBankCategoryId">
              <el-select
                v-model="form.questionBankCategoryId"
                :disabled="isEdit"
                clearable
                filterable
                placeholder="选择题库"
                size="small"
                @change="handleQuestionBankSelectChange"
              >
                <el-option
                  v-for="item in questionBankList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :title="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="封面" prop="thumbnailUrl">
              <lz-upload-images
                ref="previewFile"
                :limit="1"
                :file-size="500"
                :file-type="['jpg', 'png', 'jpeg']"
                :source-list="previewFileList"
                @response-fn="handleImageResponse"
                @remove-upload="handleRemoveUploadImage"
              />
            </el-form-item>
            <el-form-item label="介绍" prop="introduce">
              <el-input v-model="form.introduce" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item prop="introduce">
              <el-button :loading="saveLoading" round type="primary" @click="handleExerciseInfoSure">保 存</el-button>
            </el-form-item>
          </el-form>

        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="用户管理" name="exerciseUser">
          <div class="header_flex_box">
            <el-input
              v-model="selectUsersListQuery.Filter"
              size="small"
              class="small_input"
              clearable
              placeholder="输入名称搜索"
            />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshSelectUserList">搜索
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectUsers()">选择学员
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">按班级选择学员
            </el-button>
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleUserDelete">批量删除</el-button>
          </div>
          <el-table
            v-loading="selectUsersListLoading"
            :data="selectUsersList"
            max-height="700px"
            size="small"
            @sort-change="handleSelectUserSortChange"
            @selection-change="handleDeleteUserChange"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column label="姓名" prop="name" sortable="name" />
            <el-table-column label="用户名" prop="userName" sortable="userName" />
            <el-table-column label="部门" prop="className" sortable="className" />
            <el-table-column label="允许学习" prop="cloudLearn" sortable="cloudLearn">
              <template slot-scope="{ row }">
                {{ row.cloudLearn ? "是" : "否" }}
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="selectUsersListQuery.totalCount > 0"
            :total="selectUsersListQuery.totalCount"
            :page.sync="selectUsersListQuery.page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :limit.sync="selectUsersListQuery.MaxResultCount"
            @pagination="getExerciseUserList"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <el-form>
        <el-form-item label="是否允许学习">
          <el-checkbox v-model="cloudLearn" />
        </el-form-item>
      </el-form>
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择学生" :visible.sync="selectUserDialog" :close-on-click-modal="false" width="1000px">
      <select-user v-if="selectUserDialog" :all-org="allOrg" @response-fn="handleSelectUserResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="selectUserDialogLoading" round type="primary" @click="handleSelectUserSure">确 定</el-button>
        <el-button round @click="selectUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { addExercise, editExercise, exerciseBankList, exerciseDeleteUsers, exerciseDetail, exerciseUpdateUsers, exerciseUsers } from '@/api/exercise'
import { loadNodes, classesUsers } from '@/api/user'
import ChooseClass from '@/components/ChooseClass'
import SelectUser from '@/components/ChooseUserPermission/user.vue'
import LzUploadImages from '@/components/LzUploadImages'

import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import { getCategorys } from '@/api/questionBankCategory'
export default {
  name: 'ExerciseDetial',
  directives: {
    permission
  },
  components: {
    Pagination,
    ChooseClass,
    SelectUser,
    LzUploadImages
  },

  data() {
    return {
      pageTitle: this.$route.query.name ? this.$route.query.name : '添加',
      // 获取详情LOADING
      cardLoading: false,
      saveLoading: false,
      previewFileList: [],

      form: {
        questionBankCategoryId: '',
        name: '',
        exerciseBankUsers: [],
        introduce: '',
        thumbnailUrl: ''
      },
      questionBankList: [],
      // 班级数据
      allOrg: [],
      // 全部学生
      allStudent: [],
      // 选择用户Dialog
      chooseUserDialog: false,
      selectUsers: [],

      isEdit: !!this.$route.query.id,

      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],
      cloudLearn: true,

      activeTabName: 'exerciseInfo',
      selectUserDialog: false,
      selectUserDialogLoading: false,
      currentSelectUsers: [],

      selectUsersList: [],
      selectUsersListLoading: false,
      selectUsersListQuery: {
        Filter: '',
        ExerciseBankId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      currentDeleteUsers: []

    }
  },
  created() {
    this.loadClassList()
    if (this.$route.query.id) {
      this.getExerciseDetail()
      this.getExerciseUserList()
      this.getAllQuestionBankList()
    }
    this.getQuestionBankList()
    // this.getUnCourseList()
  },
  methods: {
    handleTabClick(tab, event) { },
    handleSelectUsers() {
      this.currentSelectUsers = []
      this.selectUserDialog = true
    },
    handleSelectUserResponse(val) {
      this.currentSelectUsers = val
    },
    handleSelectUserSure() {
      if (this.currentSelectUsers.length) {
        this.selectUserDialogLoading = true
        var form = {
          exerciseBankId: this.$route.query.id,
          exerciseBankUsers: []
        }
        this.currentSelectUsers.forEach((item) => {
          form.exerciseBankUsers.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            cloudLearn: item.cloudLearn
          })
        })
        console.log(form)
        var _this = this
        exerciseUpdateUsers(form)
          .then((res) => {
            setTimeout(function() {
              _this.selectUserDialog = false
              _this.selectUserDialogLoading = false
              _this.$message.success('操作成功，稍后刷新查看结果')
              _this.getExerciseUserList()
            }, 1000 * 5)
          })
          .catch(() => {
            this.selectUserDialogLoading = false
          })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleDeleteUserChange(val) {
      this.currentDeleteUsers = val
    },
    handleUserDelete() {
      if (this.currentDeleteUsers.length) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            var form = {
              exerciseBankId: this.$route.query.id,
              exerciseBankUserIds: []
            }
            this.currentDeleteUsers.forEach((item) => {
              form.exerciseBankUserIds.push(item.id)
            })
            exerciseDeleteUsers(form)
              .then((res) => {
                this.$message.success('删除成功')
                this.currentDeleteUsers = []
                this.getExerciseUserList()
              })
              .catch(() => {
                this.$message.error('删除失败')
              })
          })
          .catch(() => {
            this.$message.info('已取消删除')
          })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleRefreshSelectUserList() {
      this.selectUsersListQuery.page = 1
      this.getExerciseUserList()
    },
    handleExerciseInfoSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveLoading = true
          if (this.$route.query.id) {
            editExercise(this.$route.query.id, this.form)
              .then((res) => {
                this.$message.success('保存成功')
                this.saveLoading = false
              })
              .catch(() => {
                this.$message.error('保存失败')
                this.saveLoading = false
              })
          } else {
            addExercise(this.form)
              .then((res) => {
                this.$message.success('添加成功')
                this.saveLoading = false
                this.$router.go(-1)
              })
              .catch(() => {
                this.$message.error('添加失败')
                this.saveLoading = false
              })
          }
        } else {
          return false
        }
      })
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var form = {
        exerciseBankId: this.$route.query.id,
        exerciseBankUsers: []
      }
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        res.items.forEach(item => {
          form.exerciseBankUsers.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            cloudLearn: this.cloudLearn
          })
        })
      }
      var setA = new Set()
      form.exerciseBankUsers = form.exerciseBankUsers.filter(item => {
        const result = setA.has(item.userId)
        setA.add(item.id)
        return !result
      })
      exerciseUpdateUsers(form).then(res => {
        this.$message.success('添加成功')
        this.classLoading = false
        this.chooseClassDialog = false
        this.getExerciseUserList()
      }).catch(() => {
        this.classLoading = false
        this.$message.error('添加失败')
      })
    },
    getRowKeys(row) {
      return row.id
    },
    handleSelectUserSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getExerciseUserList()
        return
      }
      this.selectUsersListQuery.Sorting = prop + ' ' + order
      this.getExerciseUserList()
    },
    handleQuestionBankSelectChange(val) {
      var item = this.questionBankList.filter(item => item.id === val)
      if (item && item.length > 0) {
        this.form.name = item[0].name
      } else {
        this.form.name = ''
      }
    },
    getExerciseUserList() {
      this.selectUsersListLoading = true
      this.selectUsersListQuery.SkipCount = (this.selectUsersListQuery.page - 1) * this.selectUsersListQuery.MaxResultCount
      exerciseUsers(this.selectUsersListQuery).then((res) => {
        this.selectUsersList = res.items
        this.selectUsersListQuery.totalCount = res.totalCount
        this.selectUsersListLoading = false
      })
    },
    async loadClassList() {
      loadNodes().then((res) => {
        this.allOrg = res.items
      })
    },
    // 上传图片成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.form.thumbnailUrl = url
    },
    // 上传图片删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.form.thumbnailUrl = ''
    },
    getQuestionBankList() {
      var form = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      exerciseBankList(form).then(res => {
        this.questionBankList = res.items
      })
    },
    getAllQuestionBankList() {
      getCategorys().then(res => {
        this.questionBankList = res.items.filter(item => item.parentId === null)
      })
    },
    getExerciseDetail() {
      console.log(this.$route.query.id)
      exerciseDetail(this.$route.query.id).then((res) => {
        this.form.questionBankCategoryId = res.questionBankCategoryId
        this.form.name = res.name
        // this.form.introduce = ''
        this.$set(this.form, 'introduce', res.introduce)
        this.form.thumbnailUrl = res.thumbnailUrl
        this.form.id = res.id
        if (this.form.thumbnailUrl) {
          this.previewFileList.push({
            url: this.form.thumbnailUrl
          })
        }
      })
    }
  }
}
</script>

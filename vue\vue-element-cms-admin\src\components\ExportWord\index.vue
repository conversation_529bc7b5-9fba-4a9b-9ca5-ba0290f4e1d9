<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-28 10:13:33
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-08-24 13:10:05
 * @FilePath: /vue-element-cms-admin/src/components/ExportWord/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="display: inline-block">
    <el-button size="mini" style="margin: 0 10px" round type="primary" icon="el-icon-download" @click="initData">成绩详细导出
    </el-button>
  </div>
</template>
<script>
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import {
  studentExamDetail,
  examQuestionsDetail,
  studentExamResultList

} from '@/api/examusers'
export default {
  name: 'ExportWord',
  components: {
  },
  props: {
    examid: {
      required: true,
      type: String,
      default: function() {
        return ''
      }
    },
    examname: {
      required: true,
      type: String,
      default: function() {
        return ''
      }
    }
  },
  data() {
    return {
      userData: [],
      questionString: '',
      replyContent: [],
      // 单选集合
      singleQuestions: [],
      // 单选分数
      singleScore: 0,
      // 单选题得分
      singleLastScore: 0,
      // 判断集合
      judgeQuestions: [],
      // 判断分数
      judgeScore: 0,
      // 判断题得分
      judgeLastScore: 0,
      // 多选集合
      multipleQuestions: [],
      // 多选分数
      multipleScore: 0,
      // 多选题得分
      multipleLastScore: 0,
      // 填空集合
      blankQuestions: [],
      // 填空分数
      blankScore: 0,
      // 填空最后得分
      blankLastScore: 0,
      // 问答集合
      replayQuestions: [],
      // 问答分数
      replayScore: 0,
      // 问答最后得分
      replayLastScore: 0,
      // 新的合成数据
      newQuestionList: [],
      questionList: [],

      data: [],
      stringList: []
    }
  },
  mounted() {
  },
  methods: {
    async initData() {
      const maxCount = 500
      const totalRes = await studentExamResultList({ ExaminationId: this.examid, SkipCount: 0, MaxResultCount: maxCount })

      const times = totalRes.totalCount % maxCount === 0 ? totalRes.totalCount / maxCount : (Math.floor(totalRes.totalCount / maxCount) + 1)
      for (let i = 0; i < times; i++) {
        var data = {
          ExaminationId: this.examid,
          Filter: '',
          Sorting: '',
          SkipCount: i * maxCount,
          MaxResultCount: maxCount
        }
        const res = await studentExamResultList(data)
        this.userData = this.userData.concat(res.items)
      }
      for await (var userItem of this.userData) {
        this.newQuestionList = []
        this.judgeQuestions = []
        this.singleQuestions = []
        this.multipleQuestions = []
        this.blankQuestions = []
        this.replayQuestions = []

        this.replyContent = []
        this.multipleLastScore = 0
        this.judgeLastScore = 0
        this.singleLastScore = 0
        this.blankLastScore = 0
        this.replayLastScore = 0
        var form = {
          id: this.examid,
          userId: userItem.userId
        }
        const replayRes = await studentExamDetail(form)
        if (replayRes) {
          this.replyContent = JSON.parse(replayRes.replyContent)
        } else {
          this.replyContent = []
        }
        var questionData = []
        if (!this.questionList.length) {
          const questionRes = await examQuestionsDetail(this.examid)
          this.questionList = questionRes
        }
        questionData = JSON.parse(JSON.stringify(this.questionList))
        // const questionRes = await examQuestionsDetail(this.examid)
        // questionData = questionRes
        // questionData = this.questionList
        questionData.forEach(questionItem => {
          // 设置回显model 多选是数组
          if (questionItem.questionType === 1 || questionItem.questionType === 3) {
            this.$set(questionItem, 'answerModel', [])
          } else {
            this.$set(questionItem, 'answerModel', null)
          }
          this.$set(questionItem, 'IsRight', false)
          if (this.replyContent !== null) {
            this.replyContent.forEach(replyItem => {
              // 如果ID相同判断类型  回显model赋值
              if (questionItem.id === replyItem.Q) {
                if (questionItem.questionType === 1 && replyItem.O) {
                  questionItem.answerModel = replyItem.O
                  this.multipleLastScore += replyItem.S
                } else if (questionItem.questionType === 0 && replyItem.O) {
                  questionItem.answerModel = replyItem.O[0]
                  this.singleLastScore += replyItem.S
                } else if (questionItem.questionType === 2) {
                  questionItem.answerModel = replyItem.J
                  this.judgeLastScore += replyItem.S
                } else if (questionItem.questionType === 3 && replyItem.BA) {
                  questionItem.answerModel = replyItem.BA
                  this.blankLastScore += replyItem.S
                } else if (questionItem.questionType === 6) {
                  questionItem.answerModel = replyItem.RA
                  this.replayLastScore += replyItem.S
                }
                questionItem.finalScore = replyItem.S
                questionItem.IsRight = replyItem.R
                this.multipleLastScore = this.multipleLastScore ? this.multipleLastScore : 0
                this.singleLastScore = this.singleLastScore ? this.singleLastScore : 0
                this.judgeLastScore = this.judgeLastScore ? this.judgeLastScore : 0
                this.blankLastScore = this.blankLastScore ? this.blankLastScore : 0
                this.replayLastScore = this.replayLastScore ? this.replayLastScore : 0
              }
            })
          } else {
            this.multipleLastScore = 0
            this.singleLastScore = 0
            this.judgeLastScore = 0
            this.blankLastScore = 0
            this.replayLastScore = 0
          }
        })

        questionData.forEach(questionItem => {
          if (questionItem.questionType === 0) {
            this.singleQuestions.push(questionItem)
            this.singleScore = questionItem.score
          } else if (questionItem.questionType === 1) {
            this.multipleQuestions.push(questionItem)
            this.multipleScore = questionItem.score
          } else if (questionItem.questionType === 2) {
            this.judgeQuestions.push(questionItem)
            this.judgeScore = questionItem.score
          } else if (questionItem.questionType === 3) {
            this.blankQuestions.push(questionItem)
            this.blankScore = questionItem.score
          } else if (questionItem.questionType === 6) {
            this.replayQuestions.push(questionItem)
            this.replayScore = questionItem.score
          }
        })
        if (this.judgeQuestions.length) {
          this.newQuestionList.push({
            type: 2,
            score: (this.judgeScore).toFixed(1),
            totalScore: (this.judgeScore * this.judgeQuestions.length).toFixed(1),
            lastScore: this.judgeLastScore,
            items: this.judgeQuestions
          })
        }
        if (this.singleQuestions.length) {
          this.newQuestionList.push({
            type: 0,
            score: (this.singleScore).toFixed(1),
            totalScore: (this.singleScore * this.singleQuestions.length).toFixed(1),
            lastScore: this.singleLastScore,
            items: this.singleQuestions
          })
        }
        if (this.multipleQuestions.length) {
          this.newQuestionList.push({
            type: 1,
            score: (this.multipleScore).toFixed(1),
            totalScore: (this.multipleScore * this.multipleQuestions.length).toFixed(1),
            lastScore: this.multipleLastScore,
            items: this.multipleQuestions
          })
        }
        if (this.blankQuestions.length) {
          this.newQuestionList.push({
            type: 3,
            score: (this.blankScore).toFixed(1),
            totalScore: (this.blankScore * this.blankQuestions.length).toFixed(1),
            lastScore: this.blankLastScore,
            items: this.blankQuestions
          })
        }
        if (this.replayQuestions.length) {
          this.newQuestionList.push({
            type: 6,
            score: (this.replayScore).toFixed(1),
            totalScore: (this.replayScore * this.replayQuestions.length).toFixed(1),
            lastScore: this.replayLastScore,
            items: this.replayQuestions
          })
        }
        var totalScore = (this.replayScore * this.replayQuestions.length + this.blankScore * this.blankQuestions.length + this.multipleScore * this.multipleQuestions.length + this.singleScore * this.singleQuestions.length + this.judgeScore * this.judgeQuestions.length).toFixed(1)
        this.data.push({
          userName: userItem.userName,
          name: userItem.name,
          lastScore: userItem.lastScore,
          totalScore: totalScore,
          id: userItem.userId,
          list: this.newQuestionList
        })
      }
      this.exportWord()
    },
    exportWord() {
      this.stringList = []
      this.data.forEach(dataItem => {
        this.questionString = '用户名：' + dataItem.userName + '       姓名：' + dataItem.name + '       考核名称：' + this.examname + '       考核总分：' + dataItem.totalScore + '分       考核成绩：' + dataItem.lastScore + '分\r\n'
        dataItem.list.forEach(questionItem => {
          if (questionItem.type === 2) {
            this.questionString += '\r\n判断题（每题' + questionItem.score + '分，共' + questionItem.items.length + '题，得分' + questionItem.lastScore.toFixed(1) + '分,满分' + questionItem.totalScore + '分）\r\n \r\n'
          } else if (questionItem.type === 0) {
            this.questionString += '\r\n单选题（每题' + questionItem.score + '分，共' + questionItem.items.length + '题，得分' + questionItem.lastScore.toFixed(1) + '分，满分' + questionItem.totalScore + '分）\r\n \r\n'
          } else if (questionItem.type === 1) {
            this.questionString += '\r\n多选题（每题' + questionItem.score + '分，共' + questionItem.items.length + '题，得分' + questionItem.lastScore.toFixed(1) + '分，满分' + questionItem.totalScore + '分）\r\n \r\n'
          } else if (questionItem.type === 3) {
            this.questionString += '\r\n填空题（每题' + questionItem.score + '分，共' + questionItem.items.length + '题，得分' + questionItem.lastScore.toFixed(1) + '分，满分' + questionItem.totalScore + '分）\r\n \r\n'
          } else if (questionItem.type === 6) {
            this.questionString += '\r\n问答题（每题' + questionItem.score + '分，共' + questionItem.items.length + '题，得分' + questionItem.lastScore.toFixed(1) + '分，满分' + questionItem.totalScore + '分）\r\n \r\n'
          }
          questionItem.items.forEach(item => {
            var questionInfo = JSON.parse(item.questionStem)
            // item.questionStem = JSON.parse(item.questionStem)
            var answerString = ''
            if (item.questionType === 2) {
              if (item.answerModel === 1) {
                answerString = '正确'
              } else if (item.answerModel === 0) {
                answerString = '错误'
              }
              this.questionString += item.order === null ? '' : item.order + '、' + questionInfo.Title + '（' + answerString + '）' + '[判断题]（分值' + questionItem.score + '分）'
              this.questionString += item.IsRight ? '回答正确（+' + questionItem.score + '分）\r\n \r\n' : '回答错误（+ 0分）' + '\r\n'
              if (!item.IsRight) {
                if (item.answerModels.judgeAnswer === 1) {
                  this.questionString += '正确答案：正确\r\n \r\n'
                } else {
                  this.questionString += '正确答案：错误\r\n \r\n'
                }
              }
            } else if (questionItem.type === 1) {
              item.answerModel.forEach((a, index) => {
                if (index === 0) {
                  answerString = this.numSwitchChar(a)
                } else {
                  answerString += this.numSwitchChar(a)
                }
              })
              this.questionString += item.order === null ? '' : item.order + '、' + questionInfo.Title + '（' + answerString + '）' + '[多选题]（分值' + questionItem.score + '分）\r\n'
              questionInfo.Options.forEach((t, index) => {
                this.questionString += this.numSwitchChar(t.Order) + '、' + t.Title + '\n'
              })
              this.questionString += item.IsRight ? '回答正确（+' + questionItem.score + '分）\r\n \r\n' : '回答错误（+ 0分）' + '\r\n'
              if (!item.IsRight) {
                var rightAnswer = ''
                item.answerModels.optionAnswerOrders.forEach((a, index) => {
                  if (index === 0) {
                    rightAnswer = this.numSwitchChar(a)
                  } else {
                    rightAnswer += this.numSwitchChar(a)
                  }
                })
                this.questionString += '正确答案：' + rightAnswer + '\r\n \r\n'
              }
            } else if (questionItem.type === 0) {
              answerString = this.numSwitchChar(item.answerModel)
              this.questionString += item.order === null ? '' : item.order + '、' + questionInfo.Title + '（' + answerString + '）' + '[单选题]（分值' + questionItem.score + '分）\r\n'
              questionInfo.Options.forEach((t, index) => {
                this.questionString += this.numSwitchChar(t.Order) + '、' + t.Title + '\n'
              })
              this.questionString += item.IsRight ? '回答正确（+' + questionItem.score + '分）\r\n \r\n' : '回答错误（+ 0分）\r\n'
              if (!item.IsRight) {
                var rightAnswer = ''
                item.answerModels.optionAnswerOrders.forEach((a, index) => {
                  if (index === 0) {
                    rightAnswer = this.numSwitchChar(a)
                  } else {
                    rightAnswer += this.numSwitchChar(a)
                  }
                })
                this.questionString += '正确答案：' + rightAnswer + '\r\n \r\n'
              }
            } else if (questionItem.type === 3) {
              item.answerModel.forEach((a, index) => {
                if (index === 0) {
                  answerString = a
                } else {
                  answerString += '\r\n' + a
                }
              })
              this.questionString += item.order === null ? '' : item.order + '、' + questionInfo.Title + '[填空题]（分值' + questionItem.score + '分）\r\n'
              // this.questionString += (answerString + questionItem.lastScore ? '\r\n已判分（+' + questionItem.lastScore + '分）\r\n \r\n' : '\r\n需要人工判分\r\n \r\n')
              this.questionString += answerString + '\r\n'
              if (questionItem.lastScore > 0) {
                this.questionString += '已判分（+' + questionItem.lastScore + '分）\r\n \r\n'
              } else {
                this.questionString += '需要人工判分（待判分）\r\n \r\n'
              }
            } else if (questionItem.type === 6) {
              this.questionString += item.order === null ? '' : item.order + '、' + questionInfo.Title + '[问答题]（分值' + questionItem.score + '分）\r\n'
              // this.questionString += (item.answerModel + questionItem.lastScore > 0 ? '\r\n已判分（+' + questionItem.score + '分）\r\n \r\n' : '\r\n需要人工判分（待判分）\r\n \r\n')
              this.questionString += item.answerModel ? item.answerModel : ' ' + '\r\n'
              if (questionItem.lastScore > 0) {
                this.questionString += '已判分（+' + questionItem.lastScore + '分）\r\n \r\n'
              } else {
                this.questionString += '需要人工判分（待判分）\r\n \r\n'
              }
            }
          })
        })
        this.stringList.push({
          userName: dataItem.userName,
          name: dataItem.name,
          id: dataItem.id,
          string: this.questionString
        })
      })
      this.packageFile()
    },
    async packageFile() {
      const that = this
      const zip = new JSZip()
      const cache = {}
      await setTimeout(_ => {
        const arr = that.stringList
        arr.forEach((item, index) => {
          const fileName = item.userName + '_' + item.name
          zip.file(fileName + '.txt', new Blob([item.string]), {
            base64: false
          })
          cache[fileName] = item.string
        })
        zip.generateAsync({
          type: 'blob'
        }).then(content => {
          FileSaver.saveAs(content, this.examname + '_答题详情.zip')
        })
      }, 0)
    },
    numSwitchChar(index) {
      if (!index) {
        return ''
      }
      switch (index) {
        case 1:
          return 'A'
        case 2:
          return 'B'
        case 3:
          return 'C'
        case 4:
          return 'D'
        case 5:
          return 'E'
        case 6:
          return 'F'
        case 7:
          return 'G'
        case 8:
          return 'H'
        case 9:
          return 'I'
        case 10:
          return 'J'
        case 11:
          return 'K'
        case 12:
          return 'L'
        case 13:
          return 'M'
        case 14:
          return 'N'
        default:
          break
      }
    }
  }
}
</script>

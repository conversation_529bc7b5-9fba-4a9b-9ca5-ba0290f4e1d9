﻿.demo {
    padding: 2em 0;
}

.box {
    text-align: center;
    overflow: hidden;
    position: relative;
    /*width: 20%;*/
    margin-bottom: 5px;
}

    .box:before {
        content: "";
        width: 0;
        height: 100%;
        background: #000;
        padding: 14px 18px;
        position: absolute;
        top: 0;
        left: 50%;
        opacity: 0;
        transition: all 500ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
    }

    .box:hover:before {
        width: 100%;
        left: 0;
        opacity: 0.5;
    }

    .box img {
        width: 100%;
        height: auto;
    }

    .box .box-content {
        width: 100%;
        /*padding: 14px 18px;*/
        color: #fff;
        position: absolute;
        top: 38%;
        left: 0;
        text-align: center;
    }

    .box .title {
        font-size: 25px;
        font-weight: 600;
        line-height: 30px;
        text-transform: uppercase;
        margin: 0;
        opacity: 0;
        transition: all 0.5s ease 0s;
    }

    .box .post {
        font-size: 15px;
        text-transform: capitalize;
        opacity: 0;
        transition: all 0.5s ease 0s;
    }

    .box:hover .title,
    .box:hover .post {
        opacity: 1;
        transition-delay: 0.7s;
    }

    .box .icon {
        padding: 0;
        margin: 0;
        list-style: none;
        margin-top: 5px;
    }

        .box .icon li {
            display: inline-block;
        }

            .box .icon li a {
                display: block;
                width: 20px;
                height: 20px;
                line-height: 20px;
                border-radius: 50%;
                /*background: #000;*/
                font-size: 14px;
                font-weight: 700;
                color: #fff;
                margin-right: 5px;
                opacity: 0;
                transform: translateY(50px);
                transition: all 0.5s ease 0s;
            }

    .box:hover .icon li a {
        opacity: 1;
        transform: translateY(0px);
        transition-delay: 0.5s;
    }

    .box:hover .icon li:last-child a {
        transition-delay: 0.8s;
    }

@media only screen and (max-width:990px) {
    .box {
        margin-bottom: 30px;
    }
}

<template>
  <div class="login_container">
    <div class="left_container">
      <el-image class="login_bg" fit="cover" :src="require('@/assets/image/login_bg.png')" />
      <img class="login_icon" :src="require('@/assets/image/login_icon.png')">
      <img class="right_icon" :src="require('@/assets/image/right_icon.png')">
    </div>
    <div class="right_container">
      <!-- <div class="login_content"> -->
      <el-form ref="loginForm" class="form_style login_form" :model="loginForm" :rules="loginRules">
        <div class="title">登录</div>
        <div class="form_label">用户名</div>
        <el-form-item prop="username" class="login_user_item">
          <el-input
            v-model="loginForm.username"
            class="input_style login_input login_user"
            autocomplete="on"
            placeholder="请输入用户名"
          >
            <i slot="prefix" style="display: flex; align-items: center;height: 100%">
              <span class="svg-container">
                <svg-icon icon-class="user" />
              </span>
            </i>
          </el-input>
        </el-form-item>
        <div class="form_label">密码</div>
        <el-form-item prop="password" class="login_pass_item">
          <el-input
            v-model="loginForm.password"
            class="input_style login_input login_pass"
            show-password
            autocomplete="on"
            placeholder="请输入密码"
            @keyup.enter.native="handleLogin"
          >
            <i slot="prefix" style="display: flex; align-items: center;height: 100%">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
            </i>
          </el-input>
        </el-form-item>
        <el-link class="link_style" :underline="false" style="float: right" @click="handleForgetPasswordClick">忘记密码?
        </el-link>
        <el-button class="color_btn login_btn" round :loading="loading" type="primary" @click.native.prevent="handleLogin">
          {{ $t("login.logIn") }}
        </el-button>
      </el-form>
      <!-- </div> -->
      <!-- <div class="footer">
        <div class="footer_span">上海景格科技股份有限公司 版权所有</div>
        <div class="footer_span">Copyright © 2020 - 2021 Jingge. All Rights Reserved.</div>
      </div> -->
    </div>
    <el-dialog
      custom-class="customDialog"
      title="忘记密码"
      :center="true"
      :visible.sync="ForgetPasswordDialog"
      :close-on-click-modal="false"
      width="350px"
    >
      <el-form ref="forgetPasswordForm" :model="forgetPasswordForm" :rules="forgetPasswordRules" class="form_style forget-form">
        <el-form-item prop="phoneNumber">
          <el-input v-model="forgetPasswordForm.phoneNumber" class="input_style" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item prop="resetToken">
          <el-row :gutter="20">
            <el-col :xs="12" :sm="14">
              <el-input v-model="forgetPasswordForm.resetToken" class="input_style" placeholder="请输入验证码" />
            </el-col>
            <el-col :xs="12" :sm="10">
              <el-button
                type="primary"
                class="color_btn verificateCodeButton"
                :disabled="verificateCodeButtonDisabled"
                :loading="loadingForm"
                @click="getVerificateCode()"
              >{{ verificateCodeButtonTitle }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="forgetPasswordForm.password" class="input_style" placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item prop="checkPassword">
          <el-input
            v-model="forgetPasswordForm.checkPassword"
            class="input_style"
            placeholder="请输入新密码"
            show-password
            @keyup.enter.native="handleForgetPassword"
          />
        </el-form-item>
      </el-form>
      <el-button
        :loading="loadingForm"
        type="primary"
        class="color_btn loginRegistButton"
        @click.native.prevent="handleForgetPassword"
      >保 存</el-button>
    </el-dialog>
  </div>
</template>
<script>
import config from '@/config/config'
import {
  getTenantName,
  getForgetCode,
  restPassword
} from '@/api/user'
export default {
  name: 'Login',
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('请输入6位以上密码'))
      } else {
        callback()
      }
    }
    const validatePhoneNumber = (rule, value, callback) => {
      if (value == null && value === '' && value.length === 0) {
        callback(new Error('请输入手机号'))
      } else {
        var reg_tel = /^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])\d{8}$/
        var reg_Tel = new RegExp(reg_tel)
        if (!reg_Tel.test(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      }
    }
    const validateCode = (rule, value, callback) => {
      if (value.length !== 6) {
        callback(new Error('请输入正确的验证码'))
      } else {
        var numReg = /^[0-9]+$/
        var numRe = new RegExp(numReg)
        if (!numRe.test(value)) {
          callback(new Error('请输入正确的验证码'))
        } else {
          callback()
        }
      }
    }
    const validateCheckPassword = (rule, value, callback) => {
      if (value == null && value === '' && value.length === 0) {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.forgetPasswordForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      loginForm: {
        tenant: undefined,
        username: '',
        password: '',
        client_id: config.client.client_id,
        client_secret: config.client.client_secret,
        grant_type: config.client.grant_type
      },
      loginRules: {
        username: [{
          required: true,
          message: '请输入账号',
          trigger: 'blur'
        }],
        password: [{
          required: true,
          message: '请输入密码',
          trigger: 'blur'
          // validator: validatePassword
        }]
      },
      ForgetPasswordDialog: false,
      // 验证码重新发送间隔时间
      resendVerificateCodeTime: 120,
      // 发送短信按钮是否禁用
      verificateCodeButtonDisabled: false,
      loadingForm: false,
      // 按钮标题
      verificateCodeButtonTitle: '发送验证码',
      forgetPasswordForm: {
        password: '',
        phoneNumber: '',
        resetToken: '',
        resetTokenId: 'ba65e752-96d7-adce-11ed-39f8aeb418c2'
      },
      forgetPasswordRules: {
        phoneNumber: [{
          required: true,
          trigger: 'blur',
          validator: validatePhoneNumber
        }],
        password: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }],
        resetToken: [{
          required: true,
          trigger: 'blur',
          validator: validateCode
        }],
        checkPassword: [{
          required: true,
          trigger: 'blur',
          validator: validateCheckPassword
        }]
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          var findTenantForm = {
            userName: this.loginForm.username
          }
          getTenantName(findTenantForm).then((res) => {
            this.loginForm.tenant = res
            this.$store.dispatch('user/userLogin', this.loginForm).then(() => {
              this.$router.push({
                path: this.redirect || '/',
                query: this.otherQuery
              })
              this.loading = false
            }).catch(() => {
              this.loading = false
            })
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    handleForgetPasswordClick() {
      if (this.$refs.forgetPasswordForm) {
        this.$refs.forgetPasswordForm.resetFields()
      }
      this.restForgetPasswordForm()
      this.ForgetPasswordDialog = true
    },
    handleForgetPassword() {
      this.$refs.forgetPasswordForm.validate(valid => {
        if (valid) {
          this.loadingForm = true
          restPassword(this.forgetPasswordForm)
            .then(res => {
              if (!res.error) {
                this.loadingForm = false
                this.$message.success('修改成功')
                this.ForgetPasswordDialog = false
              } else {
                this.loadingForm = false
                this.$message.error(res.error.message)
              }
            })
            .catch(err => {
              this.loadingForm = false
            })
        } else {
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    getVerificateCode() {
      this.$refs.forgetPasswordForm.validateField(
        'phoneNumber',
        phoneNumber => {
          if (!phoneNumber) {
            this.loadingForm = true
            this.resendVerificateCodeTime = 120
            this.timer()
            this.loadingForm = false
            var data = {
              phone: this.forgetPasswordForm.phoneNumber,
              appName: 'VMSWeb'
            }
            getForgetCode(data).then(res => {
              this.forgetPasswordForm.resetTokenId = res
            })
          }
        }
      )
    },
    timer() {
      if (this.resendVerificateCodeTime > 0) {
        this.verificateCodeButtonDisabled = true
        this.resendVerificateCodeTime--
        this.verificateCodeButtonTitle = this.resendVerificateCodeTime + ' 秒'
        setTimeout(this.timer, 1000)
      } else {
        this.resendVerificateCodeTime = 0
        this.verificateCodeButtonTitle = '发送验证码'
        this.verificateCodeButtonDisabled = false
      }
    },
    restForgetPasswordForm() {
      this.forgetPasswordForm = {
        password: '',
        phoneNumber: '',
        resetToken: '',
        resetTokenId: 'ba65e752-96d7-adce-11ed-39f8aeb418c2'
      }
    }
  }
}

</script>
<style lang="scss">
  $main_color: #003686;
  $secondary_color: #005DC2 ;
  $el-color: #606266;
  $border_color: #DCDFE6;
  $main_border_color: #003686;
  .login_container {
    display: flex;
    height: 100%;
  }

  .color_btn {
    background: linear-gradient(to right, $secondary_color, $main_color);
    border: none;
    color: white;
    // height: 48px;
  }
  .color_btn:hover,
  .color_btn:focus {
    color: white;
    background: linear-gradient(to right, $secondary_color, $main_color);
  }
  // .input_style .el-input__inner {
  //   height: 48px;
  // }
  .form_style {
    .el-form-item.is-error .el-input__inner {
      border-color: $border_color !important;
    }
    .el-form-item.is-error .el-input__inner:focus,.el-form-item.is-error .el-input__inner:focus-within{
      border-color: $main_border_color !important;
    }
    .el-form-item__error {
      padding-top: 8px;
    }
  }
  .left_container,
  .right_container {
    flex: 1;
    height: 100%;
    background-color: #fff;
    position: relative;
  }
  .login_bg {
    width: 100%;
    height: 100%
  }
  .right_container {
    display: flex;
    background-color: white;
  }
  .login_icon {
    position: absolute;
    top: 44px;
    left: 55px;
    width: 180px;
    height: auto;
    z-index: 99;
  }
  // .login_content {
  //   display: flex;
  //   height: calc(100% - 70px);
  // }
  .right_icon {
    width: 100px;
    height: 100px;
    position: absolute;
    top: calc(50% - 25px);
    right: -50px;
    z-index: 99;
  }

  .login_form {
    align-self: center;
    width: 480px;
    margin: 0 auto;
  }

  .login_form .title {
    font-size: 38px;
    text-align: left;
    margin-bottom: 80px;
  }

  .form_label {
    margin-bottom: 20px;
    font-size: 14px;
    // color: $el-color;
  }

  .login_input .el-input__inner {
    padding-left: 40px;
  }

  .input_style .el-input__inner:focus {
    border-color: $main_border_color !important;
  }

  .svg-container {
    font-size: 20px;
    vertical-align: middle;
    color: rgb(115, 116, 138);
    margin: 0 5px;
  }

  .login_user .el-input__inner:focus-within,
  .login_pass .el-input__inner:focus-within {
    border-color: $main_border_color !important;
  }

  .login_user_item {
    margin-bottom: 40px;
  }

  .login_user_item .login_user:focus-within,
  .login_pass_item .login_pass:focus-within {
    border-color: $main_border_color !important;

    .svg-icon {
      color: $main_color;
    }
  }

  .login_pass_item {
    margin-bottom: 20px;
  }

  .link_style:hover {
    color: $main_color !important;
  }

  .login_btn {
    width: 100%;
    margin-top: 40px;
  }

  .footer {
    text-align: center;
    position: absolute;
    bottom: 10px;
    width: 100%;
    // height: 70px;
    // margin-bottom: 10px;
  }

  .footer_span {
    font-size: 12px;
    margin-bottom: 10px;
    color: #ccc;
  }

  .customDialog {
    border-radius: 15px;
    min-width: 300px;
    max-width: 350px;
  }
  .forget-form .el-form-item {
    margin-bottom: 30px;
  }
  .verificateCodeButton.is-disabled {
    background: linear-gradient(to right, $secondary_color, $main_color) !important;
  }
  .loginRegistButton {
    width: 100%;
    margin-bottom: 20px;
  }

</style>

<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="8" :md="8" :lg="6" :xl="4">
        <el-card class="box-card" style="max-height: 800px; overflow: auto">
          <el-tree ref="tree" node-key="id" :data="orgDatas" :props="defaultProps" highlight-current
            style="margin-top: 5px" @node-click="handleNodeClick" />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="16" :md="16" :lg="18" :xl="20">
        <el-card class="box-card">
          <div class="header_flex_box">
            <el-cascader v-model="listQuery.CourseCategoryId" filterable clearable :options="courseCategoryList"
              :props="{ 'label': 'name', 'value': 'id', 'children': 'children', 'checkStrictly': true, 'emitPath': false }"
              size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
            <!-- <el-select
              v-model="listQuery.CourseCategoryId"
              size="small"
              placeholder="选择课程分类"
              @change="handleRefreshList"
            >
              <el-option label="全部" :value="''" />
              <el-option
                v-for="item in courseCategoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select> -->
            <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索
            </el-button>
            <el-button :loading="sureLoading" :disabled="sureLoading" round size="small" type="success"
              icon="el-icon-check" @click="handleSubmitCoursePermission">确定授权</el-button>
          </div>
          <el-table v-loading="listLoading" :data="list" highlight-current-row height="620px" @sort-change="sortChange"
            @selection-change="handleCourseSelectedChange">
            <el-table-column type="selection" width="44" align="center" />
            <el-table-column label="课程封面" prop="courseCoverUrl" sortable="courseCoverUrl" width="160">
              <template slot-scope="{ row }">
                <el-image :src="row.courseCoverUrl" class="course-cover" fit="cover">
                  <div slot="error">
                    <div class="image-slot">
                      <i class="el-icon-picture-outline" />
                    </div>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="课程名称" prop="courseName" sortable="courseName" min-width="200" />
            <el-table-column label="学习期限" prop="learningPeriod" sortable="learningPeriod" width="100" />

            <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
              <template slot-scope="{ row }">
                {{ row.creationTime | formatDateTime }}
              </template>
            </el-table-column>
            <el-table-column width="150">
              <template slot="header" slot-scope="{ row }">
                <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">允许学习
                </el-checkbox>
              </template>
              <template slot-scope="{ row }">
                <el-checkbox v-model="row.cloudLearn" @change="handleCloudLearnChange">允许学习
                </el-checkbox>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
            :limit.sync="listQuery.MaxResultCount" @pagination="getCourseCenterList" />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :title="progressTitle" :visible="sureLoading" :show-close="false" width="600px">
      <el-progress :percentage="progressValue" color="#409eff" />
    </el-dialog>
  </div>
</template>
<script>
import { orgsData, getFilterUsers } from '@/api/user'
import {
  courseCategoryList,
  courseCenterList,
  coursePermission
} from '@/api/course'
import Pagination from '@/components/Pagination'
export default {
  name: 'CoursePermission',
  components: {
    Pagination
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'displayName',
        id: 'id'
        // isLeaf: "leaf"
      },
      // 班级树数据
      orgDatas: [],
      // 所有学生数据
      allStudents: [],
      // 分类
      courseCategoryList: [],

      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseCategoryId: '',
        FreeModel: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      sureLoading: false,
      isIndeterminate: false,
      checkAll: false,
      selectedCourse: [],
      selectedOrgId: null,

      selectedUsers: [],

      progressValue: 0,
      progressTitle: '正在获取部门用户中，请等待...'
    }
  },
  created() {
    this.getTreeDatas()
    // this.getAllStudents()
    this.getCourseCategoryList()
    this.getCourseCenterList()
  },
  methods: {
    async handleSubmitCoursePermission() {
      if (this.selectedCourse.length === 0) {
        this.$message.warning('请选择至少一门课程')
        return
      }
      if (!this.selectedOrgId) {
        this.$message.warning('请选择部门')
        return
      }

      this.selectedUsers = []
      var loopTime = []
      var successPermission = 0
      this.sureLoading = true

      const maxCount = 500
      const totalRes = await getFilterUsers({ OUId: this.selectedOrgId, SkipCount: 0, MaxResultCount: maxCount, GetChildren: true })

      const times = totalRes.totalCount % maxCount === 0 ? totalRes.totalCount / maxCount : (Math.floor(totalRes.totalCount / maxCount) + 1)
      for (let i = 0; i < times; i++) {
        var data = {
          OUId: this.selectedOrgId,
          GetChildren: true,
          Filter: '',
          Sorting: 'creationtime desc',
          SkipCount: i * maxCount,
          MaxResultCount: maxCount
        }
        const res = await getFilterUsers(data)
        this.progressValue = parseInt((((i + 1) * maxCount / totalRes.totalCount) * 100).toFixed(0))
        res.items.forEach((sitem) => {
          this.selectedUsers.push({
            id: sitem.id,
            classId: sitem.extraProperties ? sitem.extraProperties.OUId : null,
            className: sitem.extraProperties ? sitem.extraProperties.OUName : '',
            userId: sitem.id,
            userName: sitem.userName,
            name: sitem.name
          })
        })
      }

      const count = 2000
      const len = this.selectedUsers.length % count === 0 ? this.selectedUsers.length / count : (Math.floor(this.selectedUsers.length / count) + 1)

      this.progressValue = 0
      this.progressTitle = '正在进行授权中，请等待...'
      for (let i = 0; i < len; i++) {
        loopTime.push(i)
      }

      for await (var item of this.selectedCourse) {
        var form = {
          courseCenterId: item.id,
          courseCenterUsers: []
        }

        this.selectedUsers.forEach(sitem => {
          sitem.courseCenterId = item.id
          sitem.courseId = item.courseId
          sitem.cloudLearn = item.cloudLearn
        })
        for await (var i of loopTime) {
          form.courseCenterUsers = this.selectedUsers.slice(i * count, (i + 1) * count > this.selectedUsers.length ? this.selectedUsers.length : (i + 1) * count)
          await coursePermission(form).then(res => {
            successPermission++
            this.progressValue = parseInt((successPermission / (len * this.selectedCourse.length) * 100).toFixed(0))
          })
        }
      }
      this.$message.success('操作成功，数据正在后台导入中，请稍后查看结果')
      setTimeout(() => {
        this.$router.push({
          name: 'CourseCenter'
        })
      }, 3000)
    },
    handleCourseSelectedChange(val) {
      this.selectedCourse = val
    },
    handleCheckAllChange(val) {
      this.list.forEach((item, i) => {
        // this.selectedCourse.forEach((sitem, j) => {
        //   if (item.id === sitem.id) {
        // this.$set(this.selectedCourse, j, { ...sitem, cloudLearn: val })
        this.$set(this.list, i, { ...item, cloudLearn: val })
        //   }
        // })
      })
      // this.selectedStus = JSON.parse(JSON.stringify(this.selectedStus))
      this.isIndeterminate = false
    },
    handleCloudLearnChange(val) {
      // this.selectedStus = JSON.parse(JSON.stringify(this.selectedStus))
      const newArr = this.list.filter(item => item.cloudLearn === true)
      const checkedCount = newArr.length
      this.checkAll = checkedCount === this.list.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.list.length
    },
    async getTreeDatas() {
      const res = await orgsData()
      this.orgDatas = res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
          // .map(ele => {
          //     return { id: ele.id, parentId: ele.parentId, label: ele.name}})
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, [])
    },
    handleRefreshList() {

      this.listQuery.page = 1
      this.getCourseCenterList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getCourseCenterList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseCenterList()
    },
    // async getAllStudents() {
    //   this.sureLoading = true
    //   const response = await getAllStudents()
    //   this.allStudents = response.items
    //   this.sureLoading = false
    // },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getCourseCenterList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseCenterList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleNodeClick(data, node) {
      if (data) {
        this.selectedOrgId = data.id
      } else {
        this.selectedOrgId = null
      }
      // if (node.level === 1) {
      //   this.selectedOrgId = null
      // } else {
      //   this.selectedOrgId = data.id
      // }
    }
  }
}
</script>

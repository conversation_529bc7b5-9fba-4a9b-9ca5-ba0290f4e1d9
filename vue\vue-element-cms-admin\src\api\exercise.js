import axios from '@/axios'

export function exerciseList(data) {
  return axios.gets('/api/exams/exercises/bank', data)
}
export function addExercise(data) {
  return axios.posts('/api/exams/exercises/bank', data)
}
export function editExercise(id, data) {
  return axios.puts(`/api/exams/exercises/bank/${id}`, data)
}
export function deleteExercise(id) {
  return axios.deletes(`/api/exams/exercises/bank/${id}`)
}
export function exerciseAllUsers(id) {
  return axios.gets('/api/exams/exercises/bank/all-users?knowledgeCenterId=' + id)
}

export function exerciseDetail(id) {
  return axios.gets(`/api/exams/exercises/bank/${id}`)
}
// 抽题策略提交
export function drawQuertion(data) {
  return axios.posts('/api/exams/exercises/bank/update-roles', data)
}

//
export function exerciseRecord(data) {
  return axios.gets('/api/exams/exercises/records', data)
}
// 获取题库
export function exerciseBankList(data) {
  return axios.gets('/api/exams/exercises/bank/unselect', data)
}

// 获取用户
export function exerciseUsers(data) {
  return axios.gets('/api/exams/exercises/bank/users', data)
}
// 删除用户
export function exerciseDeleteUsers(data) {
  return axios.posts('/api/exams/exercises/bank/delete-users', data)
}

export function exerciseUpdateUsers(data) {
  return axios.posts('/api/exams/exercises/bank/update-users', data)
}

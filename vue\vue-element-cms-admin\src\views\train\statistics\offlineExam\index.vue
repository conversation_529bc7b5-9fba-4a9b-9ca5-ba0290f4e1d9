<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-27 13:26:38
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-12 11:23:33
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/offlineExam/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <export-excel
      :header="['考核名称']"
      :filter-val="['name']"
      :paging="true"
      :query="{'TrainId': trainId}"
      :api-fn="trainsOffLineExamList"
    />
    <el-table v-loading="listLoading" :data="list" highlight-current-row>
      <el-table-column label="考核名称" prop="name">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewCourseDetail(row)">{{ row.name }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getTrainOffLineExam"
    />
    <el-dialog v-if="trainExamUserDialog" title="线下考核学生详情" :visible.sync="trainExamUserDialog" top="5vh" width="1200px">
      <t-offline-exam :exam-id="examId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="trainExamUserDialog = false">关  闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsOffLineExamList } from '@/api/train'
import Pagination from '@/components/Pagination'
import TOfflineExam from './detail.vue'
export default {
  name: 'TrainOfflineExam',
  components: {
    Pagination,
    TOfflineExam
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: '',
        TrainId: this.trainId,
        page: 1,
        totalCount: 0
      },
      trainExamUserDialog: false,
      examId: ''
    }
  },
  created() {
    this.getTrainOffLineExam()
  },
  methods: {
    trainsOffLineExamList(args) {
      return trainsOffLineExamList(args)
    },
    handleViewCourseDetail(row) {
      this.examId = row.id
      this.trainExamUserDialog = true
    },
    // 获取培训线下考核
    getTrainOffLineExam() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsOffLineExamList(this.listQuery).then((res) => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {})
    }
  }
}
</script>


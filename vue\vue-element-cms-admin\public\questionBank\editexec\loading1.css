﻿/* loading */
#sys-loading {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10000000;
    background-color: hsla(0,0%,100%,.93);
    opacity: 1
}
 
#sys-loading.finished {
    opacity: 0
}
 
#sys-loading .spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    margin: -30px auto auto -15px;
    /*width: 200px;
    height: 100px;*/
    font-size: 10px
}
 
#sys-loading .spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}
 
#sys-loading .spinner .rect3 {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}
 
#sys-loading .spinner .rect4 {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}
 
#sys-loading .spinner .rect5 {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}
 
@-webkit-keyframes stretchdelay {
    0%,40%,to {
        -webkit-transform: scaleY(.6);
        transform: scaleY(.6)
    }
 
    20% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}
 
@keyframes stretchdelay {
    0%,40%,to {
        transform: scaleY(.6);
        -webkit-transform: scaleY(.6)
    }
 
    20% {
        transform: scaleY(1);
        -webkit-transform: scaleY(1)
    }
}
 
@-webkit-keyframes finishanim {
    0% {
        opacity: 1
    }
 
    99% {
        opacity: 0
    }
 
    to {
        display: none
    }
}
 
/* --------------- loading -1  ---------------------  */
@-webkit-keyframes line-scale-pulse-out-rapid {
  0% {
    -webkit-transform: scaley(1);
            transform: scaley(1); }
 
  80% {
    -webkit-transform: scaley(0.3);
            transform: scaley(0.3); }
 
  90% {
    -webkit-transform: scaley(1);
            transform: scaley(1); } }
 
@keyframes line-scale-pulse-out-rapid {
  0% {
    -webkit-transform: scaley(1);
            transform: scaley(1); }
 
  80% {
    -webkit-transform: scaley(0.3);
            transform: scaley(0.3); }
 
  90% {
    -webkit-transform: scaley(1);
            transform: scaley(1); } }
 
.line-scale-pulse-out-rapid > div {
  background-color: #67CF22;
  width: 4px;
  height: 35px;
  border-radius: 2px;
  margin: 2px;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  display: inline-block;
  -webkit-animation: line-scale-pulse-out-rapid 0.9s 0s infinite cubic-bezier(.11, .49, .38, .78);
          animation: line-scale-pulse-out-rapid 0.9s 0s infinite cubic-bezier(.11, .49, .38, .78); }
  .line-scale-pulse-out-rapid > div:nth-child(2), .line-scale-pulse-out-rapid > div:nth-child(4) {
    -webkit-animation-delay: 0.25s !important;
            animation-delay: 0.25s !important; }
  .line-scale-pulse-out-rapid > div:nth-child(1), .line-scale-pulse-out-rapid > div:nth-child(5) {
    -webkit-animation-delay: 0.5s !important;
            animation-delay: 0.5s !important; }
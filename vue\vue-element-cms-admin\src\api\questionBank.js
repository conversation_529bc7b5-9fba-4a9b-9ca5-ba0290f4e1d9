/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-03-24 08:11:43
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-13 15:43:20
 * @FilePath: /vue-element-cms-admin/src/api/questionBank.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/axios'

export function getQuestionBanks(getInput) {
  return axios.gets('/api/exams/questionBankTenant', getInput)
}

export function getDetailQuestionBank(id) {
  return axios.gets(`/api/exams/questionBankTenant/${id}`)
}

export function getQuesCategoryId(id) {
  return axios.gets(`/api/exams/questionBankTenant/quesCategoryId/${id}`)
}

export function createQuestionBank(data) {
  return axios.posts('/api/exams/questionBankTenant', data)
}

export function updateQuestionBank(id, data) {
  return axios.puts(`/api/exams/questionBankTenant/${id}`, data)
}

// export function deleteQuestionBank(id) {
//   return axios.deletes(`/api/exams/questionBankTenant/${id}`)
// }
export function deleteQuestionBank(data) {
  return axios.posts(`api/exams/questionBankTenant/delete`, data)
}

export function getQuesCount(input) {
  return axios.gets(`/api/exams/questionBankTenant`, input)
}

export function questionBankCategory(parentId) {
  return axios.gets(`/api/exams/questionBank/category/list`, { parentId: parentId })
}

export function questionBankCount(data) {
  return axios.gets(`/api/exams/questionBankTenant/getQuesCount`, data)
}
export function impportQuertion(data) {
  return axios.posts(`/api/exams/questionBankTenant/import`, data)
}

// tagtype
export function tagTypes(data) {
  return axios.gets(`api/exams/questionBank/tagTypes`, data)
}
export function tagType(id) {
  return axios.gets(`api/exams/questionBank/tagTypes/${id}`)
}

export function tagTypeCreate(data) {
  return axios.posts('api/exams/questionBank/tagTypes', data)
}

export function tagTypeUpdate(id, data) {
  return axios.puts(`api/exams/questionBank/tagTypes/${id}`, data)
}
export function tagTypeDelete(id) {
  return axios.deletes(`/api/exams/questionBank/tagTypes/${id}`)
}
// tag
export function tags(data) {
  return axios.gets(`api/exams/questionBank/tags`, data)
}
export function quesTagIds(id) {
  return axios.gets(`api/exams/questionBank/tags/get-tag-by-quesid?questionId=${id}`)
}
export function tag(id) {
  return axios.gets(`api/exams/questionBank/tags/${id}`)
}
export function tagCreate(data) {
  return axios.posts('api/exams/questionBank/tags', data)
}

export function tagUpdate(id, data) {
  return axios.puts(`api/exams/questionBank/tags/${id}`, data)
}
export function tagDelete(id) {
  return axios.deletes(`/api/exams/questionBank/tags/${id}`)
}

export default
{
  getQuestionBanks, getDetailQuestionBank, createQuestionBank, updateQuestionBank, deleteQuestionBank, getQuesCategoryId, getQuesCount, impportQuertion,
  tagTypes, tagType, tagTypeCreate, tagTypeUpdate, tagTypeDelete,
  tags, tag, tagCreate, tagUpdate, tagDelete, quesTagIds
}

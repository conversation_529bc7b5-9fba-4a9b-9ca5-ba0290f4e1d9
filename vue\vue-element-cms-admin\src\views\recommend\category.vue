<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-input  size="small"  class="small_input" clearable placeholder="输入名称搜索" v-model="listQuery.Filter" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        <el-button round v-permission="['CourseManagement.RecommendCategorys.Create']" size="small"  type="primary" icon="el-icon-plus" @click="handleRecommendCategoryEdit(0,0)">添加</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list"  @sort-change="sortChange" highlight-current-row>
        <el-table-column label="排序" prop="sort" sortable="sort" width="120"/>
        <el-table-column label="分类名称" prop="name" sortable="name" />
        <el-table-column label="是否显示" prop="isShow" sortable="isShow" width="120">
          <template slot-scope="{ row }">
            <el-tag size="mini" v-if="row.isShow" type="success">是</el-tag>
            <el-tag size="mini" v-else type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320">
          <template slot-scope="{row}">
            <el-button round size="mini" v-permission="['CourseManagement.RecommendCategorys.Update']"  type="primary" icon="el-icon-edit"  @click="handleRecommendCategoryEdit(1,row)">编辑
            </el-button>
            <el-button round size="mini" v-permission="['CourseManagement.RecommendCourses']"  type="primary" icon="el-icon-edit"  @click="handleRecommendCourseClick(row)">课程管理
            </el-button>
            <el-button round size="mini" v-permission="['CourseManagement.RecommendCategorys.Delete']"  type="danger" icon="el-icon-delete"  @click="handleRecommendCategoryDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getRecommendCategoryList" />
    </el-card>
    <el-dialog :title="dialogTitle" :close-on-click-modal="false" :visible.sync="recommendCategoryDialog" width="450px">
      <el-form ref="form" :model="form" :rules="formRules" label-width="80px" label-position="right">
        <el-form-item label="名称" prop="name">
          <el-input   v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort"  :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="是否显示" prop="isShow">
          <el-radio-group  v-model="form.isShow">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round size="small" type="primary" :loading="dialogLoading" @click="recommendCategoryDialogSure">确 定</el-button>
        <el-button round size="small" @click="recommendCategoryDialog = false">取 消</el-button>
        
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import {
    recommendCategoryList,
    recommendCategoryEdit,
    recommendCategoryAdd,
    recommendCategoryDelete
  } from '@/api/course'
  import Pagination from '@/components/Pagination'
  import permission from '@/directive/permission'
  export default {
    name: 'WebRecommendCategory',
    directives: {
      permission
    },
    components: {
      Pagination
    },
    data() {
      return {
        list: [],
        listLoading: false,
        listQuery: {
          Filter: '',
          Sorting: 'sort',
          SkipCount: 0,
          MaxResultCount: 10,
          page: 1,
          totalCount: 0
        },
        // 编辑
        editId: '',
        isEdit: false,
        // dialog
        dialogTitle: '添加',
        recommendCategoryDialog: false,
        dialogLoading: false,
        // form
        form: {
          name: '',
          sort: 1,
          isShow: true
        },
        formRules: {
          name: [{
              required: true,
              message: '请输入课程类别名称',
              trigger: 'blur'
            },
            {
              min: 1,
              max: 50,
              message: '长度在 1 到 50 个字符',
              trigger: 'blur'
            }
          ]
        }

      }
    },
    created() {
      this.getRecommendCategoryList()
    },
    methods: {
      // 编辑添加点击
      handleRecommendCategoryEdit(t, row) {
        this.form = {
          name: '',
          sort: 1,
          isShow: true
        }
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
        if (t === 0) {
          this.dialogTitle = '添加'
          this.isEdit = false
          this.form.sort = this.listQuery.totalCount + 1
        } else {
          this.dialogTitle = '编辑'
          this.isEdit = true
          this.editId = row.id
          this.form.name = row.name
          this.form.sort = row.sort
          this.form.isShow = row.isShow
        }
        this.recommendCategoryDialog = true
      },
      handleRecommendCourseClick(row) {
        this.$router.push({
          name: 'WebRecommendCourse',
          query: {id: row.id, title: row.name}
        })
      },
      // 删除
      handleRecommendCategoryDelete(row) {
        this.$confirm("是否确定删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          recommendCategoryDelete(row.id).then(res => {
            this.$message.success('删除成功')
            this.getRecommendCategoryList()
          }).catch(() => {
            this.$message.error('删除失败')
          })
        }).catch(() => {
          this.$message.info('已取消删除')
        });
      },
      // 编辑添加确定
      recommendCategoryDialogSure() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.dialogLoading) {
              return
            }
            this.dialogLoading = true
            if (this.isEdit) {
              recommendCategoryEdit(this.editId, this.form).then(res => {
                this.$message.success('编辑成功')
                this.recommendCategoryDialog = false
                this.getRecommendCategoryList()
                this.dialogLoading = false
              }).catch(() => {
                this.$message.error('编辑失败')
                this.dialogLoading = false
              })
            } else {
              recommendCategoryAdd(this.form).then(res => {
                this.$message.success('添加成功')
                this.recommendCategoryDialog = false
                this.getRecommendCategoryList()
                this.dialogLoading = false
              }).catch(() => {
                this.$message.error('添加失败')
                this.dialogLoading = false
              })
            }
          } else {
            return false;
          }
        });
      },
      handleSearch() {
        this.getRecommendCategoryList()
      },
      sortChange(data) {
        const {
          prop,
          order
        } = data;
        if (!prop || !order) {
          this.getRecommendCategoryList();
          return;
        }
        this.listQuery.Sorting = prop + " " + order;
        this.getRecommendCategoryList();
      },
      getRecommendCategoryList() {
        this.listLoading = true
        this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
        recommendCategoryList(this.listQuery).then(res => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
      }
    }
  }

</script>

<template>
  <div class="app-container">
    <el-card shadow="always">
      <div slot="header" class="clearfix" style="height: 20px">
        <span class="role-span">用户审核管理</span>
      </div>
      <div class="header_between_box">
        <el-radio-group v-model="auditStatus" size="small" style="margin-right: 15px">
          <el-radio-button :label="0">全部</el-radio-button>
          <el-radio-button :label="1">已审核</el-radio-button>
          <el-radio-button :label="2">未审核</el-radio-button>
        </el-radio-group>
        <div class="header_flex_box">
          <el-input v-model="listQuery.Filter" size="small" placeholder="搜索..." class="small_input" />
          <el-button round size="mini" type="success" icon="el-icon-search">搜索</el-button>
        </div>
      </div>
      <el-table :data="list" size="small" highlight-current-row>
        <el-table-column prop="userName" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="role" label="角色" />
        <el-table-column prop="time" label="注册日期">
          <template slot-scope="{row}">
            {{ row.time | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="{row}">
            <el-tag v-if="row.status === 1">未审核</el-tag>
            <el-tag v-if="row.status === 2" type="success">审核通过</el-tag>
            <el-tag v-if="row.status === 3" type="info">审核不通过</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template>
            <el-button round type="success" size="mini" icon="el-icon-check">审核通过</el-button>
            <el-button round type="danger" size="mini" icon="el-icon-close">审核不通过</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.PageIndex"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
export default {
  name: 'UserAudit',
  components: {
    Pagination
  },
  data() {
    return {
      // 用户审核状
      auditStatus: 0,
      // 审核列表
      list: [
        {
          userName: '工单',
          name: '张三',
          role: '学生',
          status: 1,
          time: '2021-04-23T16:17:05.575'
        }
      ],
      // 分页 搜索
      listQuery: {
        totalCount: 3,
        PageIndex: 1,
        MaxResultCount: 10,
        Filter: ''
      }
    }
  },
  methods: {
    // 获取审核数据列表
    getList() {

    }
  }
}
</script>

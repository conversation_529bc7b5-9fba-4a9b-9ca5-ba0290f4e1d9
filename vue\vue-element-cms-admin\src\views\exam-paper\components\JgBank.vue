<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-card class="shadow_none card-padding-0">
          <div slot="header" class="clearfix">
            <el-button type="text" style="padding: 0;" @click="handleNodeClick()">全部</el-button>
          </div>
          <el-tree
            class="course-dir-tree"
            style="height: 811px;overflow: auto"
            default-expand-all
            :data="data"
            :props="defaultProps"
            highlight-current
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card class="shadow_none">
          <div slot="header" class="flex_between_box">
            <span class="role-span">{{ knoweldgeTitle }}</span>
            <div>
              <el-input
                v-model="questionListform.Filter"
                clearable
                size="small"
                placeholder="搜索..."
                style="width: 200px"
                class="filter-item"
                @input="questionListSearchChange"
                @keyup.enter.native="questionListSearchHandle"
              />
              <el-button
                class="filter-item"
                round
                size="small"
                type="success"
                icon="el-icon-search"
                @click="questionListSearchHandle"
              >搜索
              </el-button>
            </div>
          </div>
          <el-row>
            <span style="margin-right: 20px">难度</span>
            <el-radio-group v-model="questionListform.Difficulty" @change="questionDifficultyChange">
              <el-radio :label="null" aria-checked="true">全部</el-radio>
              <el-radio :label="1">易</el-radio>
              <el-radio :label="2">偏易</el-radio>
              <el-radio :label="3">适中</el-radio>
              <el-radio :label="4">偏难</el-radio>
              <el-radio :label="5">难</el-radio>
            </el-radio-group>
          </el-row>

          <el-table
            ref="questionList"
            v-loading="questionBankLoading"
            :data="banklist"
            style="width: 100%"
            height="641px"
            @selection-change="handleQuestionChange"
            @row-click="handleQuestionRowClick"
            @sort-change="questionSortChange"
          >
            <el-table-column type="selection" width="55" />
            <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
            <el-table-column prop="questionType" label="题型" sortable="" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.questionType === 0" type="success" size="small">单选题</el-tag>
                <el-tag v-if="scope.row.questionType === 1" size="small">多选题</el-tag>
                <el-tag v-if="scope.row.questionType === 2" size="small" type="warning">判断题</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="questionStem" label="题干" sortable="">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleQuestionCheck(row)">{{
                  JSON.parse(row.questionStem).Title
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="{ row }">
                <el-button type="primary" round size="mini" icon="el-icon-view" @click="handleQuestionCheck(row)">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="qTotalCount > 0"
            :total="qTotalCount"
            :page.sync="qPage"
            :limit.sync="questionListform.MaxResultCount"
            @pagination="getQuestionBankList"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      v-if="questionDetailDialog"
      class="questionDetailDialog"
      title="题目详情"
      :visible.sync="questionDetailDialog"
      width="800px"
      :append-to-body="true"
    >
      <exam-preview :data="questionDetailItem" />
    </el-dialog>
  </div>
</template>
<script>
import {
  questionBankTree,
  questionBankList
} from '@/api/jgQuestionBank'
import Pagination from '@/components/Pagination'
import ExamPreview from '@/components/ExamPreview'
export default {
  name: 'JgBank',
  components: {
    Pagination,
    ExamPreview
  },
  props: {
    questionType: {
      required: true,
      type: Number,
      default: function() {
        return 0
      }
    }
  },
  data() {
    return {
      knoweldgeTitle: '全部',
      // 试卷信息
      form: {
        code: '',
        name: '',
        totalScore: '',
        id: ''
      },
      // 题库LIST
      banklist: [],
      // 题库loading
      questionBankLoading: false,
      // 题库TreeList
      data: [],
      // 题库TreeProp
      defaultProps: {
        children: 'childs',
        label: 'name'
      },

      // 题库列表参数
      questionListform: {
        QuestionBankPackageParentId: '',
        QuestionBankPackageId: '',
        Difficulty: null,
        QuestionType: this.questionType,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10
      },
      // 题库分页总条数
      qTotalCount: 0,
      // 题库分页 当前页数
      qPage: 1,
      // 题库查看题目 Dialog
      questionDetailDialog: false,
      // 题库查看题目 详情信息
      questionDetailItem: null,
      // 题库查看题目的类型
      questionDetailType: null
    }
  },
  created() {
    this.getTreeList()
    this.getQuestionBankList()
  },
  methods: {
    getTreeList() {
      this.data = []
      questionBankTree().then((res) => {
        res.forEach((item) => {
          this.data.push(item)
        })
      })
    },
    // 获取题库列表
    getQuestionBankList() {
      this.questionBankLoading = true
      this.questionListform.SkipCount =
          (this.qPage - 1) * this.questionListform.MaxResultCount
      questionBankList(this.questionListform).then((res) => {
        this.banklist = res.items
        this.qTotalCount = res.totalCount
        this.questionBankLoading = false
      }).catch(() => {
        this.questionBankLoading = false
      })
    },
    questionListSearchHandle() {
      this.qPage = 1
      this.getQuestionBankList()
    },
    // 要替换的题库列表搜索框清空 请求列表
    questionListSearchChange(val) {
      if (val === '' || val.length === 0) {
        this.qPage = 1
        this.getQuestionBankList()
      }
    },
    handleQuestionCheck(row) {
      this.questionDetailItem = row
      this.questionDetailType = row.questionType
      this.questionDetailDialog = true
    },
    // 题库节点点击
    handleNodeClick(data) {
      if (data === undefined) {
        this.knoweldgeTitle = '全部'
        this.questionListform.QuestionBankPackageParentId = null
        this.questionListform.QuestionBankPackageId = null
      } else {
        this.knoweldgeTitle = data.name
        this.questionListform.QuestionBankPackageParentId =
            data.parentId === null ? data.id : data.parentId
        this.questionListform.QuestionBankPackageId =
            data.parentId === null ? null : data.id
      }
      this.getQuestionBankList()
    },
    // 替换试题点击变化
    handleQuestionChange(val) {
      this.$emit('jg-select-change', val[0])
      this.newQuestionDetail = val
      if (val.length > 1) {
        this.$refs.questionList.clearSelection()
        this.$refs.questionList.toggleRowSelection(val.pop())
      }
    },
    // 替换试题行点击
    handleQuestionRowClick(row) {
      this.$refs.questionList.clearSelection()
      this.$refs.questionList.toggleRowSelection(row)
      this.$emit('jg-select-change', row)
    },
    // 题目难度变化
    questionDifficultyChange() {
      this.getQuestionBankList()
    },
    // 题目类型变化
    questionTypeChange() {
      this.getQuestionBankList()
    },
    questionSortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getQuestionBankList()
        return
      }
      this.questionListform.Sorting = prop + ' ' + order
      this.getQuestionBankList()
    }
  }
}
</script>

<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 17:28:59
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-06-23 15:00:01
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/live/detail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <el-table v-loading="listLoading" :data="list" highlight-current-row>
      <el-table-column prop="userName" label="用户名" width="120" />
      <el-table-column prop="name" label="姓名" width="140" />
      <el-table-column prop="className" label="部门" sortable="className" show-overflow-tooltip />
      <el-table-column label="课程课时" sortable="classHour" prop="classHour" width="140">
        <template slot-scope="{row}">
          {{ classHour }}
        </template>
      </el-table-column>
      <el-table-column label="获取课时" sortable="classHour" prop="classHour" width="160" />

      <el-table-column label="通过考核" sortable="userName" prop="userName" width="160">
        <template slot-scope="{row}">
          {{ row.examPass ? '是': '否' }}
        </template>
      </el-table-column>
      <el-table-column label="参与时长" sortable="liveViewDuration" prop="liveViewDuration" width="120">
        <template slot-scope="{row}">
          {{ row.liveViewDuration | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column label="回放观看时长" sortable="backPlayDuration" prop="backPlayDuration" width="160">
        <template slot-scope="{row}">
          <span>{{ row.backPlayDuration | formatSecond }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getLiveUser"
    />
  </div>
</template>
<script>
import { liveUserList } from '@/api/live'
import Pagination from '@/components/Pagination'
export default {
  name: 'TCourseDetail',
  components: {
    Pagination
  },
  props: {
    liveId: {
      reuqerd: true,
      type: String,
      default: ''
    },
    classHour: {
      reuqerd: true,
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        LiveStreamId: this.liveId,
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: 'userNumber desc',
        page: 1,
        totalCount: 0
      }

    }
  },
  created() {
    this.getLiveUser()
  },
  methods: {
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getLiveUser()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getLiveUser()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getLiveUser()
    },
    // 获取课程下所有学生
    getLiveUser() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      liveUserList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    }
  }
}
</script>


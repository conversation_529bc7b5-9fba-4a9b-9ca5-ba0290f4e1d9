/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.0 (2019-10-17)
 */
!function(m){"use strict";function f(t){return e({validate:!1,root_name:"#document"}).parse(t)}function g(t){return t.replace(/<\/?[A-Z]+/g,function(t){return t.toLowerCase()})}var o,i=function(t){function e(){return n}var n=t;return{get:e,set:function(t){n=t},clone:function(){return i(e())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),p=tinymce.util.Tools.resolve("tinymce.util.Tools"),e=tinymce.util.Tools.resolve("tinymce.html.DomParser"),h=tinymce.util.Tools.resolve("tinymce.html.Node"),y=tinymce.util.Tools.resolve("tinymce.html.Serializer"),v=function(t){return t.getParam("fullpage_hide_in_source_view")},r=function(t){return t.getParam("fullpage_default_xml_pi")},a=function(t){return t.getParam("fullpage_default_encoding")},c=function(t){return t.getParam("fullpage_default_font_family")},u=function(t){return t.getParam("fullpage_default_font_size")},s=function(t){return t.getParam("fullpage_default_text_color")},d=function(t){return t.getParam("fullpage_default_title")},_=function(t){return t.getParam("fullpage_default_doctype","<!DOCTYPE html>")},b=f,n=function(t,e){var n,i,l=f(e),r={};function o(t,e){return t.attr(e)||""}return r.fontface=c(t),r.fontsize=u(t),7===(n=l.firstChild).type&&(r.xml_pi=!0,(i=/encoding="([^"]+)"/.exec(n.value))&&(r.docencoding=i[1])),(n=l.getAll("#doctype")[0])&&(r.doctype="<!DOCTYPE"+n.value+">"),(n=l.getAll("title")[0])&&n.firstChild&&(r.title=n.firstChild.value),p.each(l.getAll("meta"),function(t){var e,n=t.attr("name"),i=t.attr("http-equiv");n?r[n.toLowerCase()]=t.attr("content"):"Content-Type"===i&&(e=/charset\s*=\s*(.*)\s*/gi.exec(t.attr("content")))&&(r.docencoding=e[1])}),(n=l.getAll("html")[0])&&(r.langcode=o(n,"lang")||o(n,"xml:lang")),r.stylesheets=[],p.each(l.getAll("link"),function(t){"stylesheet"===t.attr("rel")&&r.stylesheets.push(t.attr("href"))}),(n=l.getAll("body")[0])&&(r.langdir=o(n,"dir"),r.style=o(n,"style"),r.visited_color=o(n,"vlink"),r.link_color=o(n,"link"),r.active_color=o(n,"alink")),r},x=function(t,r,e){var o,n,i,a,l,c=t.dom;function u(t,e,n){t.attr(e,n||undefined)}function s(t){n.firstChild?n.insert(t,n.firstChild):n.append(t)}o=f(e),(n=o.getAll("head")[0])||(a=o.getAll("html")[0],n=new h("head",1),a.firstChild?a.insert(n,a.firstChild,!0):a.append(n)),a=o.firstChild,r.xml_pi?(l='version="1.0"',r.docencoding&&(l+=' encoding="'+r.docencoding+'"'),7!==a.type&&(a=new h("xml",7),o.insert(a,o.firstChild,!0)),a.value=l):a&&7===a.type&&a.remove(),a=o.getAll("#doctype")[0],r.doctype?(a||(a=new h("#doctype",10),r.xml_pi?o.insert(a,o.firstChild):s(a)),a.value=r.doctype.substring(9,r.doctype.length-1)):a&&a.remove(),a=null,p.each(o.getAll("meta"),function(t){"Content-Type"===t.attr("http-equiv")&&(a=t)}),r.docencoding?(a||((a=new h("meta",1)).attr("http-equiv","Content-Type"),a.shortEnded=!0,s(a)),a.attr("content","text/html; charset="+r.docencoding)):a&&a.remove(),a=o.getAll("title")[0],r.title?(a?a.empty():s(a=new h("title",1)),a.append(new h("#text",3)).value=r.title):a&&a.remove(),p.each("keywords,description,author,copyright,robots".split(","),function(t){var e,n,i=o.getAll("meta"),l=r[t];for(e=0;e<i.length;e++)if((n=i[e]).attr("name")===t)return void(l?n.attr("content",l):n.remove());l&&((a=new h("meta",1)).attr("name",t),a.attr("content",l),a.shortEnded=!0,s(a))});var d={};return p.each(o.getAll("link"),function(t){"stylesheet"===t.attr("rel")&&(d[t.attr("href")]=t)}),p.each(r.stylesheets,function(t){d[t]||((a=new h("link",1)).attr({rel:"stylesheet",text:"text/css",href:t}),a.shortEnded=!0,s(a)),delete d[t]}),p.each(d,function(t){t.remove()}),(a=o.getAll("body")[0])&&(u(a,"dir",r.langdir),u(a,"style",r.style),u(a,"vlink",r.visited_color),u(a,"link",r.link_color),u(a,"alink",r.active_color),c.setAttribs(t.getBody(),{style:r.style,dir:r.dir,vLink:r.visited_color,link:r.link_color,aLink:r.active_color})),(a=o.getAll("html")[0])&&(u(a,"lang",r.langcode),u(a,"xml:lang",r.langcode)),n.firstChild||n.remove(),(i=y({validate:!1,indent:!0,indent_before:"head,html,body,meta,title,script,link,style",indent_after:"head,html,body,meta,title,script,link,style"}).serialize(o)).substring(0,i.indexOf("</body>"))},C=Object.prototype.hasOwnProperty,k=(o=function(t,e){return e},function(){for(var t=new Array(arguments.length),e=0;e<t.length;e++)t[e]=arguments[e];if(0===t.length)throw new Error("Can't merge zero objects");for(var n={},i=0;i<t.length;i++){var l=t[i];for(var r in l)C.call(l,r)&&(n[r]=o(n[r],l[r]))}return n}),l=function(i,l){var r=n(i,l.get()),t=k({title:"",keywords:"",description:"",robots:"",author:"",docencoding:""},r);i.windowManager.open({title:"Metadata and Document Properties",size:"normal",body:{type:"panel",items:[{name:"title",type:"input",label:"Title"},{name:"keywords",type:"input",label:"Keywords"},{name:"description",type:"input",label:"Description"},{name:"robots",type:"input",label:"Robots"},{name:"author",type:"input",label:"Author"},{name:"docencoding",type:"input",label:"Encoding"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:t,onSubmit:function(t){var e=t.getData(),n=x(i,p.extend(r,e),l.get());l.set(n),t.close()}})},w=function(t,e){t.addCommand("mceFullPageProperties",function(){l(t,e)})},A=function(t,e){return p.each(t,function(t){e=e.replace(t,function(t){return"\x3c!--mce:protected "+escape(t)+"--\x3e"})}),e},P=function(t){return t.replace(/<!--mce:protected ([\s\S]*?)-->/g,function(t,e){return unescape(e)})},T=p.each,O=function(t){var e,n="",i="";if(r(t)){var l=a(t);n+='<?xml version="1.0" encoding="'+(l||"ISO-8859-1")+'" ?>\n'}return n+=_(t),n+="\n<html>\n<head>\n",(e=d(t))&&(n+="<title>"+e+"</title>\n"),(e=a(t))&&(n+='<meta http-equiv="Content-Type" content="text/html; charset='+e+'" />\n'),(e=c(t))&&(i+="font-family: "+e+";"),(e=u(t))&&(i+="font-size: "+e+";"),(e=s(t))&&(i+="color: "+e+";"),n+="</head>\n<body"+(i?' style="'+i+'"':"")+">\n"},D=function(e,n,i){e.on("BeforeSetContent",function(t){!function(t,e,n,i){var l,r,o,a,c="",u=t.dom;if(!(i.selection||(o=A(t.settings.protect,i.content),"raw"===i.format&&e.get()||i.source_view&&v(t)))){0!==o.length||i.source_view||(o=p.trim(e.get())+"\n"+p.trim(o)+"\n"+p.trim(n.get())),-1!==(l=(o=o.replace(/<(\/?)BODY/gi,"<$1body")).indexOf("<body"))?(l=o.indexOf(">",l),e.set(g(o.substring(0,l+1))),-1===(r=o.indexOf("</body",l))&&(r=o.length),i.content=p.trim(o.substring(l+1,r)),n.set(g(o.substring(r)))):(e.set(O(t)),n.set("\n</body>\n</html>")),a=b(e.get()),T(a.getAll("style"),function(t){t.firstChild&&(c+=t.firstChild.value)});var s=a.getAll("body")[0];s&&u.setAttribs(t.getBody(),{style:s.attr("style")||"",dir:s.attr("dir")||"",vLink:s.attr("vlink")||"",link:s.attr("link")||"",aLink:s.attr("alink")||""}),u.remove("fullpage_styles");var d=t.getDoc().getElementsByTagName("head")[0];if(c)u.add(d,"style",{id:"fullpage_styles"}).appendChild(m.document.createTextNode(c));var f={};p.each(d.getElementsByTagName("link"),function(t){"stylesheet"===t.rel&&t.getAttribute("data-mce-fullpage")&&(f[t.href]=t)}),p.each(a.getAll("link"),function(t){var e=t.attr("href");if(!e)return!0;f[e]||"stylesheet"!==t.attr("rel")||u.add(d,"link",{rel:"stylesheet",text:"text/css",href:e,"data-mce-fullpage":"1"}),delete f[e]}),p.each(f,function(t){t.parentNode.removeChild(t)})}}(e,n,i,t)}),e.on("GetContent",function(t){!function(t,e,n,i){i.selection||i.source_view&&v(t)||(i.content=P(p.trim(e)+"\n"+p.trim(i.content)+"\n"+p.trim(n)))}(e,n.get(),i.get(),t)})},E=function(t){t.ui.registry.addButton("fullpage",{tooltip:"Metadata and document properties",icon:"document-properties",onAction:function(){t.execCommand("mceFullPageProperties")}}),t.ui.registry.addMenuItem("fullpage",{text:"Metadata and document properties",icon:"document-properties",onAction:function(){t.execCommand("mceFullPageProperties")}})};!function z(){t.add("fullpage",function(t){var e=i(""),n=i("");w(t,e),E(t),D(t,e,n)})}()}(window);
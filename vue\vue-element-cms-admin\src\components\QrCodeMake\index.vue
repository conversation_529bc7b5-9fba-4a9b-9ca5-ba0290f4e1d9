<template>
  <div>
    <div class="qr-code-make">
      <vue-qr class="qr_div" :text="qrUrl" :margin="0" color-dark="#000000" color-light="#fff" :logo-src="require('@/assets/image/qrlogo.jpg')" :logo-scale="0.2" :size="300" />
    </div>
    <div style="display: flex; justify-content: center">
      <el-button size="small" round plain type="primary" icon="el-icon-download" @click="handleDownload">下载</el-button>
      <!-- <el-button size="small" round plain class="copy_url" type="primary" icon="el-icon-link" @click="handleCopyUrl">复制链接</el-button> -->
    </div>
  </div>

</template>
<script>
import vueQr from 'vue-qr'
import Clipboard from 'clipboard'
import html2canvas from 'html2canvas'
export default {
  name: 'QrCodeMake',
  components: {
    vueQr
  },
  props: {
    qrUrl: {
      type: String,
      require: true,
      default: ''
    }
  },
  methods: {
    handleDownload() {
      html2canvas(document.querySelector('.qr-code-make')).then(canvas => {
        // 转成图片，生成图片地址
        var imgUrl = canvas.toDataURL('image/png')
        this.saveFile(imgUrl, new Date().toLocaleString())
      })
    },
    saveFile(data, filename) {
      const save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')
      save_link.href = data
      save_link.download = filename

      const event = document.createEvent('MouseEvents')
      event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
      save_link.dispatchEvent(event)
    },
    handleCopyUrl() {
      const that = this
      const clipboard = new Clipboard('.copy_url', {
        text: function(trigger) {
          return that.qrUrl
        }
      })
      clipboard.on('success', e => {
        this.$message.success('复制成功')
        clipboard.destroy() // 使用destroy可以清楚缓存
      })
      clipboard.on('error', e => {
        this.$message.error('复制失败')
        clipboard.destroy()
      })
    }
  }
}
</script>
<style scoped>
.qr-code-make {
  text-align: center;
  padding: 15px;
  margin: 20px auto;
}
.qr_div {
  min-width: 300px;
  min-height: 300px;
}
.text_div {
  width: 200px;
  display: block;
  font-weight:800;
  font-size: 20px;
  margin-bottom: 5px;
}
</style>

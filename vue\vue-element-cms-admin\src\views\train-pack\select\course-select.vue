<template>
  <div class="app-container">

    <el-card v-loading="courseSelectLoading" class="box-card">
      <div slot="header">
        <span> 培训包课程管理—— {{ pageTitle }}</span>
      </div>
      <el-descriptions
        style="margin-bottom: 20px;"
        label-class-name="my-label"
        content-class-name="my-content"
        :column="4"
        border
      >
        <el-descriptions-item label="已选课程数">{{ selectCourseList.length }} 个</el-descriptions-item>
        <el-descriptions-item label="总课时数">{{ totalClassHour }} 课时</el-descriptions-item>
        <el-descriptions-item label="总时长">{{ totalResourceDuration | formatSecond }}</el-descriptions-item>
        <el-descriptions-item label="视频总时长">{{ totalVideoDuration | formatSecond }}</el-descriptions-item>
      </el-descriptions>
      <div style="margin-bottom: 20px;">
        <el-button round type="success" size="small" icon="el-icon-plus" @click="handleChooseCourseClick"> 添加课程 </el-button>
        <span
          style="display: inline-block;font-size: 13px;margin-left: 10px;color: #909399;"
        >按住鼠标上下拖动资源可对资源进行排序</span>
      </div>

      <el-table ref="courseTable" row-key="courseId" :data="selectCourseList" size="medium" height="600px" highlight-current-row>
        <el-table-column label="排序" width="60px">
          <template>
            <svg-icon icon-class="drag" class="meta-item__icon" />
          </template>
        </el-table-column>
        <el-table-column label="课程封面" width="200">
          <template slot-scope="{ row }">
            <el-image v-if="row.ExtraProperties" :src="row.ExtraProperties.CourseCoverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" min-width="200">
          <template slot-scope="{row}">
            <span style="margin-right: 10px">{{ row.courseName }}</span>
            <span v-if="row.courseExpireState === 1" class="text-danger"> 已到期 </span>
            <!-- <span v-if="row.courseExpireState === 1" class="text-danger">{{ row.expireDate | formatDate }} </span> -->
            <span v-if="row.courseExpireState==0&&row.leftDays>0&&row.leftDays<15" class="text-danger">剩余天数 {{ row.leftDays }} 天</span>
            <!-- <el-tag v-if="row.courseSource === 0 && !checkExpireDate(row.expireDate)" type="info">已过期</el-tag> -->
          </template>
        </el-table-column>
        <el-table-column label="总时长" prop="resourceDuration" width="150">
          <template slot-scope="{row}">
            <span>{{ row.resourceDuration | formatSecond }}</span>
          </template>
        </el-table-column>
        <el-table-column label="课时数" prop="classHour" width="150" />
        <!-- <el-table-column label="来源" prop="courseSource" width="150">
          <template slot-scope="{row}">
            <span v-if="row.courseSource === 1">自建课程</span>
            <span v-if="row.courseSource === 0">景格课程</span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="320">
          <template slot-scope="{row}">
            <el-button round type="primary" size="mini" icon="el-icon-top" @click="handleMoveCourse(0,row)">置顶</el-button>
            <el-button round type="primary" size="mini" icon="el-icon-bottom" @click="handleMoveCourse(1,row)">置底</el-button>
            <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleRemoveCourse(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px;text-align: center;">
        <el-button round type="primary" size="small" icon="el-icon-check" @click="handlePackSelectCourseSure">确定选择</el-button>
        <el-button round type="" size="small" icon="el-icon-back" @click="handleGoBack">取消</el-button>
      </div>
    </el-card>
    <el-dialog title="选择课程" :close-on-click-modal="false" :visible.sync="courseSelectDialog" width="1100px" top="5vh">
      <div class="filtter_div">
        <span class="filtter_span">来源：</span>
        <!-- <el-radio-group v-model="resourceFrom" @change="handleResourceFromChange">
          <el-radio class="filtter_radio" :label="1">自建课程</el-radio>
          <el-radio class="filtter_radio" :label="0">景格课程</el-radio>
        </el-radio-group> -->
        <ul class="items">
          <li :class="resourceFrom === 1 ? 'item selected' : 'item'" @click="handleResourceFromChange(1)">自建课程</li>
          <!-- <li :class="resourceFrom === 0 ? 'item selected' : 'item'" @click="handleResourceFromChange(0)">景格课程</li> -->
        </ul>
        <div style="float: right">
          <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
          <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList()">搜索</el-button>
        </div>

      </div>
      <div class="filtter_div">
        <span class="filtter_span">分类：</span>
        <ul class="items">
          <li :class="listQuery.CourseCategoryId === '' ? 'item selected' : 'item'" @click="handleResourceCategoryChange('')">全部</li>
          <li v-for="item in categoryList" :key="item.id" :class="item.id === listQuery.CourseCategoryId ? 'item selected' : 'item'" @click="handleResourceCategoryChange(item.id)">{{ item.name }}</li>
        </ul>
        <!-- <el-radio-group v-model="listQuery.CourseCategoryId" @change="handleResourceCategoryChange">
          <el-radio class="filtter_radio" :label="''">全部</el-radio>
          <el-radio v-for="item in categoryList" :key="item.id" class="filtter_radio" :label="item.id">
            {{ item.name }}</el-radio>
        </el-radio-group> -->
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        @sort-change="sortChange"
        @selection-change="handleChooseCourseChange"
      >
        <el-table-column type="selection" :selectable="checkedCourseValid" width="55" align="center" />
        <el-table-column label="课程封面" prop="coverUrl" sortable="coverUrl" width="200">
          <template slot-scope="{ row }">
            <el-image :src="row.coverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" :prop="resourceFrom === 1 ? 'name' : 'courseName'" sortable="name" />
        <el-table-column label="课程讲师" prop="lecturer" sortable="lecturer" width="150" />
        <el-table-column label="课时" prop="classHour" sortable="classHour" width="120" />
        <el-table-column v-if="resourceFrom === 1" label="创建时间" prop="creationTime" sortable="creationTime" width="200">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column v-if="resourceFrom === 0" label="授权到期时间" prop="dueTime" sortable="dueTime" width="200">
          <template slot-scope="{row}">
            <span v-if="row.isPerpetualLicense===false" :class="row.courseExpireState===1?'text-danger':''"> {{ row.dueTime | formatDate }} </span>
            <span v-if="row.courseExpireState===1" class="text-danger">已到期</span>
            <span v-if="row.courseExpireState===0&&row.leftDays>0&&row.leftDays<15" class="text-danger">剩余天数 {{ row.leftDays }} 天</span>
            <span v-if="row.isPerpetualLicense===true"> 永久授权</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseCourseSure">确 定</el-button>
        <el-button round @click="courseSelectDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  courseList,
  courseCategoryList,
  jgCourseList,
  jgCourseCategoryList
} from '@/api/course'
import { trainsCoursePackUpdate, trainsCoursePackList } from '@/api/train'
import Sortable from 'sortablejs'
import Pagination from '@/components/Pagination'
export default {
  name: 'CourseSelect',
  components: {
    Pagination
  },
  data() {
    return {
      pageTitle: this.$route.query.name,
      courseSelectLoading: false,
      list: [],

      listLoading: false,
      listQuery: {
        Filter: '',
        Status: 1,
        CourseCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      categoryList: [],
      selsectCategory: '',
      resourceFrom: 1,

      courseSelectDialog: false,
      currentSelectCourse: [],
      selectCourseList: [],

      totalClassHour: 0,
      totalResourceDuration: 0,
      totalVideoDuration: 0,
      // 列表拖拽排序
      sortable: null
    }
  },
  created() {
    this.getTrainsCoursePackList()
    this.getCourseCategoryList()
    this.getCourseList()
  },
  methods: {
    handleChooseCourseClick() {
      this.currentSelectCourse = []
      // 刷新课程列表
      this.listQuery.CourseCategoryId = ''
      this.listQuery.page = 1
      this.resourceFrom = 1
      this.getCourseCategoryList()
      this.getCourseList()
      this.courseSelectDialog = true
    },
    handleResourceFromChange(val) {
      this.resourceFrom = val
      this.listQuery.page = 1
      this.listQuery.CourseCategoryId = ''
      if (this.resourceFrom === 1) {
        this.getCourseCategoryList()
        this.getCourseList()
      } else {
        this.getJGCourseCategoryList()
        this.getJGCourseList()
      }
    },
    handleResourceCategoryChange(val) {
      this.listQuery.CourseCategoryId = val
      this.listQuery.page = 1
      this.getList()
    },
    checkedCourseValid(row) {
      if (this.resourceFrom === 0) {
        if (row.courseExpireState) {
          return false
        }
      }
      if (this.resourceFrom === 1) {
        for (let i = 0; i < this.selectCourseList.length; i++) {
          if (this.selectCourseList[i].courseId === row.id) {
            return false
          }
        }
      }

      return true
    },
    handleChooseCourseChange(val) {
      this.currentSelectCourse = val
    },
    handleChooseCourseSure() {
      if (this.currentSelectCourse.length) {
        if (this.resourceFrom === 1) {
          this.currentSelectCourse.forEach((item, index) => {
            this.selectCourseList.push({
              trainPackageId: this.$route.query.id,
              courseId: item.id,
              courseName: item.name,
              courseSource: 1,
              classHour: item.classHour,
              resourceDuration: item.resourceDuration,
              videoDuration: item.videoDuration,
              expireDate: null,
              ExtraProperties: { CourseCoverUrl: item.coverUrl }
            })
          })
        } else {
          this.currentSelectCourse.forEach((item, index) => {
            this.selectCourseList.push({
              trainPackageId: this.$route.query.id,
              courseId: item.courseId,
              courseName: item.courseName,
              courseSource: 0,
              classHour: item.classHour,
              resourceDuration: item.resourceDuration,
              videoDuration: item.videoDuration,
              expireDate: item.dueTime,
              ExtraProperties: { CourseCoverUrl: item.coverUrl }
            })
          })
        }
        this.totalClassHour = 0
        this.totalResourceDuration = 0
        this.totalVideoDuration = 0
        // 去重
        this.selectCourseList = this.selectCourseList.reduce((prev, cur, index, arr) => {
          const findIndex = prev.findIndex(i => i.courseId === cur.courseId)
          if (findIndex === -1) {
            cur.order = index + 1
            prev.push(cur)
            this.totalClassHour += cur.classHour
            this.totalResourceDuration += cur.resourceDuration
            this.totalVideoDuration += cur.videoDuration
          }
          return prev
        }, [])
        this.$nextTick(() => {
          this.setSort()
        })
        this.courseSelectDialog = false
      } else {
        this.$message.warning('请选择课程')
      }
    },
    handleMoveCourse(t, row) {
      if (t === 0) {
        this.selectCourseList.unshift(this.selectCourseList.splice(this.selectCourseList.findIndex(item => item.courseId === row.courseId), 1)[0])
      } else {
        this.selectCourseList.push(this.selectCourseList.splice(this.selectCourseList.findIndex(item => item.courseId === row.courseId), 1)[0])
      }
      this.selectCourseList.forEach((item, index) => {
        item.order = index
      })
    },
    handleRemoveCourse(row) {
      this.selectCourseList.splice(this.selectCourseList.findIndex(item => item.courseId === row.courseId), 1)
      this.totalClassHour -= row.classHour
      this.totalResourceDuration -= row.resourceDuration
      this.totalVideoDuration -= row.videoDuration
      this.selectCourseList.forEach((item, index) => {
        item.order = index + 1
      })
    },
    handlePackSelectCourseSure() {
      if (this.selectCourseList.length) {
        this.courseSelectLoading = true
        var form = {
          trainPackageId: this.$route.query.id,
          trainPackageCourses: this.selectCourseList
        }
        trainsCoursePackUpdate(form).then(res => {
          this.courseSelectLoading = false
          this.$message.success('保存成功')
          this.handleGoBack()
        }).catch(res => {
          this.courseSelectLoading = false
          this.$message.error('保存失败')
        })
      } else {
        this.$message.warning('课程数量为空,请选择课程')
      }
    },
    getList() {
      if (this.resourceFrom === 0) {
        this.getJGCourseList()
      } else {
        this.getCourseList()
      }
    },
    handleGoBack() {
      this.$router.push({
        name: 'PackEdit',
        query: { id: this.$route.query.id, from: -1 }

      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getTrainsCoursePackList() {
      trainsCoursePackList(this.$route.query.id).then(res => {
        res.items.forEach((item, index) => {
          this.selectCourseList.push({
            trainPackageId: this.$route.query.id,
            courseId: item.courseId,
            courseName: item.courseName,
            courseSource: item.courseSource,
            classHour: item.classHour,
            resourceDuration: item.resourceDuration,
            videoDuration: item.videoDuration,
            expireDate: item.expireDate,
            leftDays: item.leftDays,
            courseExpireState: item.courseExpireState,
            ExtraProperties: item.extraProperties,
            order: item.order
          })
          this.totalClassHour += item.classHour
          this.totalResourceDuration += item.resourceDuration
          this.totalVideoDuration += item.videoDuration
        })
        this.$nextTick(() => {
          this.setSort()
        })
      })
    },
    getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      courseCategoryList(data).then(res => {
        this.categoryList = res.items
      }).catch(() => {

      })
    },
    getJGCourseCategoryList() {
      jgCourseCategoryList().then(res => {
        this.categoryList = res.items
      })
    },
    getCourseList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.$message.error('获取列表失败')
        this.listLoading = false
      })
    },
    getJGCourseList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      jgCourseList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },

    setSort() {
      const el = this.$refs.courseTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function(dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const targetRow = this.selectCourseList.splice(evt.oldIndex, 1)[0]
          this.selectCourseList.splice(evt.newIndex, 0, targetRow)
          // for show the changes, you can delete in you code
          this.selectCourseList.forEach((item, index) => {
            item.order = index + 1
          })
        }
      })
    }
  }
}
</script>
<style >

.filtter_div {
  /* height: 32px; */
  line-height: 30px;
  /* position: absolute; */
  width: 1430px;
  /* margin-bottom: 7px; */
  padding: 10px;
  overflow: hidden;
  transition: all .2s;
  box-shadow: 0 12px 20px 0 rgb(95 101 105 / 0%);
  border-radius: 8px;
  display: flex;
  width: 1050px;
}
.items {
  list-style: none;
  flex: 1;
  margin: 0;
  padding: 0;
  cursor: pointer;
}
.item {
  float: left;
  padding: 0 8px;
  margin: 0 12px 8px 0;
  border-radius: 6px;
  line-height: 30px;
  color: black;
  cursor: pointer;
}
.selected {
  background: #1890ff;
  color: white;
}
.filtter_span {
  width: 60px;
  margin-right: 6px;
  font-weight: 700;
  font-size: 16px;
    /* margin-right: 15px;
    width: 80px; */
  }

  /* .filtter_radio {
    margin-bottom: 10px;
  } */
  .my-content{
font-size: 20px;
text-align: center !important;
  }
  .my-label{
text-align: center !important;
  }
</style>

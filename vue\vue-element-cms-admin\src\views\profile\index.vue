<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <user-card />
      </el-col>

      <el-col :span="18" :xs="24">
        <el-card>
          <el-tabs v-model="activeTab" @tab-click="handleClick">
            <el-tab-pane label="个人信息" name="account">
              <account />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="ChangePassword">
              <change-password />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UserCard from './components/UserCard'
import ChangePassword from './components/ChangePassword'
// import Timeline from './components/Timeline'
import Account from './components/Account'

export default {
  name: 'Profile',
  components: { UserCard, Account, ChangePassword },
  data() {
    return {
      activeTab: 'account'
    }
  },
  computed: {
    ...mapGetters(['name', 'avatar', 'roles'])

  },
  created() {
    // this.getUser()
  },
  methods: {
    getUser() {
      // "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif"
      this.$axios
        .gets('/api/appuser/my-profile')
        .then((Response) => {
          // if (Response.userImg == null) {
          //   Response.userImg = require('@/assets/image/user_n.png')
          //   if (Response.surname === '0') {
          //     Response.userImg = require('@/assets/image/user-man.png')
          //   } else if (Response.surname === '1') {
          //     Response.userImg = require('@/assets/image/user_woman.png')
          //   }
          //   this.$store.commit('user/SET_AVATAR', Response.userImg)
          // }
          this.user = {
            name: Response.name,
            email: Response.email,
            avatar: this.$store.getters.avatar,
            // avatar: Response.userImg,
            phoneNumber: Response.phoneNumber,
            userName: Response.userName,
            surname: this.$store.getters.surname
            // surname: Response.surname
          }
        })
        .catch(() => {})
    },
    handleClick(tab, event) {
    }
  }
}
</script>

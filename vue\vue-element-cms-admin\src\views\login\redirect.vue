
<template>
  <div />
</template>
<script>
export default {
  name: "AuthRedirect",
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.$store.dispatch("user/saveUserToken",query.token);
          this.init(query.params);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  methods: {
    init(url) {
      this.$router.push({
        path: url || '/',
      });
    },
  },
};
</script>
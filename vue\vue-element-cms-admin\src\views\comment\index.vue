<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-select v-model="listQuery.CourseId" size="small" clearable @change="handleCourseSearchChange">
          <el-option v-for="item in courseList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <export-excel :header="['课程名称', '类型', '内容', '提交人', '提交时间']"
          :filter-val="['courseName', 'commentType', 'content', 'author', 'creationTime']"
          :field="{ 1: ['评论', '提问', '回答'], 4: [2] }" :api-fn="courseCommentList" />
        <!-- <el-button v-permission="['CourseManagement.CourseComments.Create']" size="small" round type="primary" icon="el-icon-plus" @click="handleCourseCommentEdit(0, 0)">添加</el-button> -->
      </div>
      <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
        <el-table-column label="课程名称" prop="courseName" sortable="courseName" show-overflow-tooltip width="150" />
        <el-table-column label="类型" prop="commentType" sortable="commentType" width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.commentType === 0" type="info" size="mini">评论</el-tag>
            <el-tag v-else-if="row.commentType === 1" type="success" size="mini">提问</el-tag>
            <el-tag v-else type="primary" size="mini">回答</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="内容" prop="content" sortable="content" show-overflow-tooltip />
        <el-table-column label="提交人" prop="author" sortable="author" width="100" />
        <el-table-column label="提交时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="{ row }">
            <el-button v-if="row.commentType === 1" v-permission="['CourseManagement.CourseComments']" size="mini" round
              type="primary" icon="el-icon-chat-line-square" @click="handleReplayDetail(row)">回复</el-button>
            <!-- <el-button v-permission="['CourseManagement.CourseComments.Update']" size="mini" round type="primary" icon="el-icon-edit" @click="handleCourseCommentEdit(1, row)">编辑</el-button> -->
            <el-button v-permission="['CourseManagement.CourseComments.Delete']" size="mini" round type="danger"
              icon="el-icon-delete" @click="handleCourseCommentDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getCourseCommentList" />
    </el-card>
    <el-dialog :title="isEdit ? '编辑' : '新增'" :close-on-click-modal="false" :visible.sync="commentDialog" width="450px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程" prop="courseId">
          <el-select v-model="form.courseId" @change="courseChange">
            <el-option v-for="item in courseList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item> -->
        <el-form-item label="内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="2" />
        </el-form-item>
        <!-- <el-form-item label="用户" prop="author">
          <el-input v-model="form.author" />
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round :loading="dialogLoading" size="small" type="primary" @click="handleCommentSure">确 定</el-button>
        <el-button round size="small" @click="commentDialog = false">取 消</el-button>

      </span>
    </el-dialog>
    <el-dialog title="回复列表" :close-on-click-modal="false" :visible.sync="replayDialog" width="900px">
      <export-excel :header="['回复人', '回复内容', '回复时间']" :filter-val="['author', 'content', 'creationTime']" :field="{ 2: [2] }"
        :query="{ 'ParentId': replayListQuery.ParentId }" :api-fn="courseCommentList" />
      <el-table v-loading="replayListLoading" :data="replayList" size="small" highlight-current-row
        @sort-change="sortChange">
        <el-table-column label="回复人" prop="author" sortable="author" width="150" />
        <el-table-column label="回复内容" prop="content" sortable="content" show-overflow-tooltip />
        <el-table-column label="回复时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{ row }">
            <el-button v-permission="['CourseManagement.CourseComments.Delete']" round size="mini" type="danger"
              icon="el-icon-delete" @click="handleReplayDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="replayListQuery.totalCount > 0" :total="replayListQuery.totalCount"
        :page.sync="replayListQuery.page" :limit.sync="replayListQuery.MaxResultCount" @pagination="getReplayList" />
    </el-dialog>
  </div>
</template>
<script>
import {
  courseList,
  courseCommentList,
  addCourseComment,
  editCourseComment,
  deletesCourseComment
} from '@/api/course'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'Comment',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseId: '',
        ParentId: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      courseList: [],

      replayList: [],
      replayListQuery: {
        Filter: '',
        CourseId: '',
        ParentId: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      replayListLoading: false,
      replayDialog: false,

      isEdit: false,
      commentDialog: false,
      dialogTitle: '新增',
      dialogLoading: false,
      form: {
        courseId: '5c35d565-3e2a-92d5-5171-3a02b1f97228',
        // title: '',
        content: '回复新评论',
        // author: '',
        courseName: '测试课程',
        parentId: 'e4801efb-5afb-d5cb-a097-3a02b2c3552c',
        commentType: '2'
      },
      rules: {
        title: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 50 个字符',
          trigger: 'blur'
        }],
        content: [{
          required: true,
          message: '请输入评论',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 100 个字符',
          trigger: 'blur'
        }
        ],
        courseId: [{
          required: true,
          message: '请选择课程',
          trigger: 'change'
        }],
        author: [{
          required: true,
          message: '请输入用户',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ]
      }
    }
  },
  created() {
    this.getCourseCommentList()
    this.getCourseList()
  },
  methods: {
    courseCommentList(args) {
      return courseCommentList(args)
    },
    courseChange(v) {
      this.courseList.map((item) => {
        if (item.id === v) {
          this.form.courseName = item.name
          return
        }
      })
    },
    handleCourseSearchChange(val) {
      this.getCourseCommentList()
    },
    handleCourseCommentEdit(t, row) {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      // this.resetForm()
      this.isEdit = !!t
      if (this.isEdit) {
        this.form.courseId = row.courseId
        this.form.courseName = row.courseName
        this.form.parentId = row.parentId
        this.form.content = row.content
        this.form.commentType = row.commentType
        this.form.id = row.id
      }
      this.commentDialog = true
    },
    handleCommentSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.isEdit) {
            editCourseComment(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.commentDialog = false
              this.dialogLoading = false
              this.getCourseCommentList()
            }).catch(() => {
              this.$message.error('编辑失败')
              this.dialogLoading = false
            })
          } else {
            addCourseComment(this.form).then(res => {
              this.$message.success('添加成功')
              this.commentDialog = false
              this.dialogLoading = false
              this.getCourseCommentList()
            }).catch(() => {
              this.$message.error('添加失败')
              this.dialogLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleCourseCommentDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesCourseComment(row.id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getCourseCommentList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    handleReplayDetail(row) {
      this.replayListQuery.ParentId = row.id
      this.replayListQuery.CourseId = row.courseId
      this.getReplayList()
      this.replayDialog = true
    },
    handleReplayDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesCourseComment(row.id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getReplayList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getCourseCommentList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseCommentList()
    },
    resetForm() {
      this.form = {
        courseId: '',
        // title: '',
        content: '',
        // author: '',
        courseName: '',
        parentId: null,
        commentType: '0'
      }
    },
    getCourseCommentList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseCommentList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    getReplayList() {
      this.replayListLoading = true
      this.replayListQuery.SkipCount =
        (this.replayListQuery.page - 1) * this.replayListQuery.MaxResultCount
      courseCommentList(this.replayListQuery)
        .then((res) => {
          this.replayList = res.items
          this.replayListQuery.totalCount = res.totalCount
          this.replayListLoading = false
        })
        .catch(() => {
          this.replayListLoading = false
        })
    },
    getCourseList() {
      var data = {
        Filter: '',
        Status: '',
        CourseCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      courseList(data)
        .then((res) => {
          this.courseList = res.items
        })
        .catch(() => {
          this.$message.error('获取课程列表失败')
        })
    }
  }
}
</script>

/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-01 08:04:14
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-19 16:39:15
 * @FilePath: /vue-element-cms-admin/src/api/examPaper.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/axios'

// 试卷列表
export function getExamPaperList(data) {
  return axios.gets('/api/exams/examPaper', data)
}
//
export function examPaperDetailInfo(id) {
  return axios.gets(`/api/exams/examPaper/${id}`)
}

// 公布成绩
export function publishScore(id) {
  return axios.posts(`/api/exams/examinations/${id}/publish-score`)
}
// 组卷
export function createExamPaper(data) {
  return axios.posts('/api/exams/examPaper', data)
}
// 重命名试卷
export function renameExamPaper(data) {
  return axios.puts(`/api/exams/examPaper/${data.id}`, data)
}

// 编辑试卷 获取题目
export function examPaperQuestionlist(data) {
  return axios.gets(`/api/exams/examPaper/questions`, data)
}
// 编辑试卷 重题检查
export function examPaperRepeatQuestionlist(id) {
  return axios.gets(`/api/exams/examPaper/${id}/repeat-questions`)
}
// 编辑试卷 替换题
export function changeExamPaperQuestion(examPaperId, oldId, newId) {
  return axios.puts(`/api/exams/examPaper/replace/${examPaperId}/${oldId}/${newId}`)
}

// 删除试卷

export function deleteExamPaper(examPaperId) {
  return axios.deletes(`/api/exams/examPaper/${examPaperId}`)
}
// 创建考核添加学生

export function createExamPaperWithStudent(data) {
  return axios.posts(`/api/exams/examinations`, data)
}
// 编辑考核
export function editExamPaperWithStudent(id, data) {
  return axios.puts(`/api/exams/examinations/${id}`, data)
}

export function updateExamAllowsubmittimes(data) {
  return axios.posts(`/api/exams/examinations/update-allowsubmittimes`, data)
}

// 考核列表
export function examList(data) {
  return axios.gets(`/api/exams/examinations`, data)
}
// 通过ID 获取考核详细信息
export function examDetailInfo(id) {
  return axios.gets(`/api/exams/examinations/${id}`)
}

// 通过ID 发布考核
export function examPublish(id) {
  return axios.posts(`/api/exams/examinations/${id}/publish`)
}
// 删除考核
export function deleteExamination(id) {
  return axios.deletes(`/api/exams/examinations/${id}`)
}
// 获取考核题目
export function examQuestions(id) {
  return axios.gets(`/api/exams/examinations/${id}/questions`)
}

// 课程包是否使用该试卷
export function packageExamHasUse(examPaperId) {
  return axios.gets(`/api/cms/trains/package-exams/has-exampaper`, { examPaperId: examPaperId })
}
//
export function trainsExamHasUse(examPaperId) {
  return axios.gets(`/api/cms/trains/exams/has-exampaper`, { examPaperId: examPaperId })
}

// 试卷分类
export function examPaperCategoryList(data) {
  return axios.gets(`/api/exams/exampaper/category`, data)
}

export function examPaperCategoryAdd(data) {
  return axios.posts(`/api/exams/exampaper/category`, data)
}

export function examPaperCategoryAll() {
  return axios.gets(`/api/exams/exampaper/category/all`)
}

export function examPaperCategoryEdit(id, data) {
  return axios.puts(`/api/exams/exampaper/category/${id}`, data)
}

export function examPaperCategoryDelete(id) {
  return axios.deletes(`/api/exams/exampaper/category/${id}`)
}

export function examPaperCategoryDetail(id) {
  return axios.gets(`/api/exams/exampaper/category/${id}`)
}

// 试卷标签
export function examPaperTagList(data) {
  return axios.gets(`/api/exams/exampaper/tag`, data)
}

export function examPaperTagAdd(data) {
  return axios.posts(`/api/exams/exampaper/tag`, data)
}

export function examPaperTagAll() {
  return axios.gets(`/api/exams/exampaper/tag/all`)
}

export function examPaperTagDetail(id) {
  return axios.gets(`/api/exams/exampaper/tag/${id}`)
}

export function examPaperTagEdit(id, data) {
  return axios.puts(`/api/exams/exampaper/tag/${id}`, data)
}

export function examPaperTagDelete(id) {
  return axios.deletes(`/api/exams/exampaper/tag/${id}`)
}

export function examPaperImportAchive(data) {
  return axios.posts(`/api/exams/examusers/import-records`, data)
}

export function examPaperImportFile(data) {
  return axios.posts(`/api/exams/files`, data)
}

export function examPaperImportFileList(data) {
  return axios.gets(`/api/exams/files`, data)
}

export function examPaperImportFileDelete(id) {
  return axios.deletes(`/api/exams/files/${id}`)
}

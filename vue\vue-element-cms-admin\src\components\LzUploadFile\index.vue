<template>
  <div class="ele-upload-file">
    <!-- 上传组件 -->
    <el-upload
      action="#"
      :multiple="multiple"
      :http-request="baidubceUpload"
      :on-remove="handleRemoveUpload"
      :file-list="fileList"
      :before-upload="handleBeforeUpload"
      :on-change="handleChange"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :on-progress="handleUploadProgress"
      :limit="limit"
      :on-preview="handlePreviewResource"
      :file-type="fileType"
    >
      <el-button v-if="btnType" type="text" icon="el-icon-picture-outline" size="mini" circle />
      <el-button v-else class="upload_btn" :round="isRound" :loading="uploading" size="small" type="primary" icon="el-icon-upload">{{ btnTitle }}</el-button>
      <el-progress
        v-if="uploading"
        class="upload_progress"
        :percentage="uploadProcess.process"
        style="width: 300px;margin-top: 20px;text-align:left;"
      />
      <!-- v-if="uploading" -->
      <div v-if="showTip" slot="tip" class="el-upload__tip upload_tip">
        请上传
        <b style="color: #f56c6c">{{
          fileType.length ? fileType.join("/") : ""
        }}</b>
        格式文件
        <template v-if="fileSize">
          ，且大小不超过
          <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
      </div>
    </el-upload>
    <el-dialog class="preview_dialog" append-to-body :title="previewTitle" :visible.sync="previewDialog" width="1000px" top="5vh" @before-close="previewDialogClose">
      <preview-resource v-if="previewDialog" ref="previewResource" :type="previewType" :url="previewUrl" />
    </el-dialog>
  </div>
</template>

<script>
import PreviewResource from '@/components/PreviewResource'
import SparkMD5 from 'spark-md5'
import moment from 'moment'
import { resourcePath } from '@/api/upload'
import { butcketStsToken, reourceUploadPath, cloudResource, cloudResourceUrl } from '@/api/upload'
import bucketConfig from '@/config/config'
export default {
  name: 'LzUploadFile',
  components: {
    PreviewResource
  },
  props: {
    // 是否支持多选文件 (同官网)
    multiple: {
      type: Boolean,
      default: false
    },
    fileSize: {
      type: Number,
      require: true,
      default: 3
    },
    limit: {
      type: Number,
      require: false,
      default: 10
    },
    fileList: {
      type: Array,
      require: false,
      default: function() {
        return []
      }
    },
    isRound: {
      type: Boolean,
      require: false,
      default: false
    },
    btnType: {
      type: Boolean,
      require: false,
      default: false
    },
    currentIndex: {
      type: Number,
      require: false,
      default: 0
    },
    btnTitle: {
      type: String,
      require: false,
      default: function() {
        return '点击上传'
      }
    },
    fileType: {
      type: Array,
      require: true,
      default: function() {
        return ['jpg', 'jpeg', 'png', 'pdf', 'mp4']
      }
    },
    // 资源是否公开
    isPublic: {
      type: Boolean,
      default: true
    },
    isShowTip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // isShowTip: false,
      previewTitle: '',
      previewDialog: false,
      previewType: '',
      previewUrl: '',
      uploading: false,
      uploadProcess: {
        fileCount: 0,
        uploadFileCount: 0,
        process: 0,
        partlength: 0,
        uploadpart: 0
      },
      stsToken: {},
      long: 0
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType.length || this.fileSize)
    }
  },
  watch: {
    'uploadProcess.uploadFileCount': function(val, oldVal) {
      if (val === this.uploadProcess.fileCount) {
        this.uploadProcess.process = 0
        this.uploading = false
        this.uploadProcess.fileCount = 0
        this.uploadProcess.uploadFileCount = 0
      }
    },
    'uploadProcess.uploadpart': function(val, oldVal) {
      this.uploadProcess.process = Number((Math.round((this.uploadProcess.uploadpart / this.uploadProcess.partlength) * 100 * 100) / 100).toFixed(0))
      if (this.uploadProcess.process === 100) {
        this.uploading = false
        this.uploadProcess.process = 0
        this.uploadProcess.partlength = 0
        this.uploadProcess.uploadpart = 0
      }
    }
  },
  created() {
    this.getstsToken()
  },
  methods: {
    getstsToken() {
      butcketStsToken().then(res => {
        this.stsToken.accessKeyId = res.accessKeyId
        this.stsToken.secretAccessKey = res.secretAccessKey
        this.stsToken.sessionToken = res.sessionToken
      }).catch(err => {
        this.$message.error('获取sts失败，无法上传')
      })
    },
    // 上传大小
    handleBeforeUpload(file) {
      let fileFormat = true
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        fileFormat = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
      }

      if (!fileFormat) {
        this.$message.error(
          `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.long = 0
      this.uploading = true
      this.uploadProcess.fileCount++
      return true
    },
    handleChange(file, fileList) {
      fileList.splice(fileList.indexOf(file))
      // this.uploadProcess.fileCount = fileList.length
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`最多上传${this.limit}个文件`)
    },
    handleRemoveUpload(file, fileList) {
      this.$emit('remove-upload', file)
      this.uploadProcess.uploadFileCount--
      this.uploadProcess.fileCount--
    },
    // 上传失败
    handleUploadError(err) {
      this.uploading = false
      this.$message.error('上传失败, 请重试')
      this.uploadProcess.uploadFileCount--
      this.uploadProcess.fileCount--
      this.$emit('response-error', err)
    },
    handleUploadSuccess() {
      this.uploading = false
    },
    // 上传成功 回调
    uploadSuccessFn(url, uploadForm) {
      this.$message.success('上传成功')
      this.uploadProcess.uploadFileCount++
      switch (uploadForm.fileType) {
        case '.zip':
          uploadForm.resType = 'html'
          break
        case '.mp4':
          uploadForm.resType = 'video'
          break
        case '.pdf':
          uploadForm.resType = 'pdf'
          break
      }
      this.$emit('response-fn', url, uploadForm, this.currentIndex)
    },
    handleUploadProgress(event, file, fileList) {
    },
    handlePreviewResource(row) {
      this.checkFileFormat(row.fileType || row.extend)
      this.previewTitle = row.fileName
      if (this.previewType === 'undefined') {
        this.$message.error('不支持此类型文件预览')
      } else {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        var form = {
          url: row.url
        }
        resourcePath(form).then(res => {
          loading.close()
          this.previewUrl = res
          this.previewDialog = true
        }).catch(() => {
          loading.close()
          this.$message.error('获取资源地址失败')
        })
      }
    },
    previewDialogClose() {
      if (this.previewType === 'video') {
        this.$refs.previewResource.$emit('videoPause') // 向子组件传递方法
      }

      this.previewDialog = false
    },
    // 检查文件格式
    checkFileFormat(fileExtension) {
      this.previewType = 'undefined'
      var imageList = ['jpg', 'png', 'jpeg']
      for (let i = 0; i < imageList.length; i++) {
        const imageItem = imageList[i]
        if (fileExtension.indexOf(imageItem) > -1) {
          this.previewType = 'image'
          break
        }
      }
      var videoList = ['mp4']
      for (let i = 0; i < videoList.length; i++) {
        const videoItem = videoList[i]
        if (fileExtension.indexOf(videoItem) > -1) {
          this.previewType = 'video'
          break
        }
      }
      var fileList = ['pdf']
      for (let i = 0; i < fileList.length; i++) {
        const fileItem = fileList[i]
        if (fileExtension.indexOf(fileItem) > -1) {
          this.previewType = 'pdf'
          break
        }
      }
    },
    // 根据HASH查询
    baidubceUpload(oldFile) {
      var blobSlice =
          File.prototype.slice ||
          File.prototype.mozSlice ||
          File.prototype.webkitSlice
      var newfile = oldFile.file
      var chunkSize = 2097152 // Read in chunks of 2MB
      var chunks = Math.ceil(newfile.size / chunkSize)
      var currentChunk = 0
      var spark = new SparkMD5.ArrayBuffer()
      var fileReader = new FileReader()
      const ele = this
      fileReader.onload = function(e) {
        spark.append(e.target.result) // Append array buffer
        currentChunk++
        if (currentChunk < chunks) {
          loadNext()
        } else {
          var hash = spark.end()
          var form = {
            hash: hash
          }
          cloudResourceUrl(form).then(response => {
            if (response) {
              if (response.isPublic === ele.isPublic) {
                var clouldForm = {
                  hash: response.hash,
                  fileName: newfile.name.substring(0, newfile.name.lastIndexOf('.')),
                  fileType: response.fileType,
                  url: response.url,
                  size: response.size,
                  durationInSecond: response.durationInSecond,
                  thumbnailUrl: response.thumbnailUrl,
                  isPublic: response.isPublic,
                  id: response.id,
                  tranStatus: 0,
                  documentId: response.documentId,
                  jobId: '',
                  recordingTime: ele.formatDateTime(newfile.lastModifiedDate)
                }
                if (response.transcoding) {
                  response.transcoding.forEach(function(item, i) {
                    if (item.definition === 1) {
                      clouldForm.jobId = item.jobId
                      clouldForm.tranStatus = item.tranStatus
                      if (clouldForm.tranStatus === 2) {
                        clouldForm.url = item.transUrl
                      }
                    }
                  })
                }

                ele.uploadSuccessFn(response.url, clouldForm)
              } else {
                ele.uploadFile(oldFile, hash)
              }
            } else {
              ele.uploadFile(oldFile, hash)
            }
          }).catch(err => {
            ele.uploadFile(oldFile, hash)
          })
        }
      }

      fileReader.onerror = function() {
        console.warn('oops, something went wrong.')
      }

      function loadNext() {
        var start = currentChunk * chunkSize
        var end =
            start + chunkSize >= newfile.size
              ? newfile.size
              : start + chunkSize

        fileReader.readAsArrayBuffer(blobSlice.call(newfile, start, end))
      }

      loadNext()
    },
    // 资源上传百度云
    async uploadFile(oldFile, md5String) {
      var file = oldFile.file
      let fileExtension = ''
      var name = md5String
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        name = md5String
      }
      // 判断是否是图片
      var fileTypeList = ['jpg', 'png', 'jpeg']

      fileTypeList.some(type => {
        if (file.type.indexOf(type) > -1) {
          this.isPublic = true
          return
        }
      })
      // 获取上传地址
      var pathForm = {
        extend: '.' + fileExtension,
        hash: md5String,
        isPublic: this.isPublic
      }
      var bucket = ''
      var key = ''
      await reourceUploadPath(pathForm).then(res => {
        bucket = res.slice(0, res.indexOf('/'))
        key = res.slice(res.indexOf('/') + 1)
      }).catch(err => {
        this.$message.error('获取上传路径失败，无法上传')
      })
      var renameFile = new File([file], name + '.' + file.name.slice(file.name.lastIndexOf('.') + 1), {
        type: file.type
      })

      // 获取视频 音频时长
      var fileurl = URL.createObjectURL(renameFile)
      // 经测试，发现audio也可获取视频的时长
      var audioElement = new Audio(fileurl)
      var duration = 0
      audioElement.addEventListener('loadedmetadata', function(_event) {
        duration = audioElement.duration
        this.long = duration
      })

      var config = {
        credentials: {
          ak: this.stsToken.accessKeyId, // 您的AK
          sk: this.stsToken.secretAccessKey // 您的SK
        },
        sessionToken: this.stsToken.sessionToken,
        endpoint: bucketConfig.bucketSpace.domainName // 传入Bucket所在区域域名
      }
      const url = config.endpoint + '/' + bucket + '/' + key
      // eslint-disable-next-line no-undef
      const client = new baidubce.sdk.BosClient(config)

      var reader = new FileReader()
      reader.readAsArrayBuffer(renameFile)
      let blob = null
      const ele = this
      // ele.uploadProcess.process = 0
      // ele.uploadProcess.uploadpart = 0
      // ele.uploadProcess.partlength = 0
      reader.onload = function(e) {
        if (typeof e.target.result === 'object') {
          blob = new Blob([e.target.result])
        } else {
          blob = e.target.result
        }
        let uploadId
        var options = {
          'Content-Disposition': 'attachment;filename="' + encodeURIComponent(file.name) + '"' // 指示回复的内容该以何种形式展示
        }
        var async = require('async')
        client
          .initiateMultipartUpload(bucket, key, options)
          .then(function(response) {
            uploadId = response.body.uploadId // 开始上传，获取服务器生成的uploadId
            /* eslint-disable no-undef */
            /* eslint-disable new-cap */
            const deferred = new baidubce.sdk.Q.defer()
            const tasks = ele.getTasks(blob, uploadId, bucket, key)
            const state = {
              lengthComputable: true,
              loaded: 0,
              total: tasks.length
            }
            ele.uploadProcess.partlength = state.total + ele.uploadProcess.partlength
            // 为了管理分块上传，使用了async（https://github.com/caolan/async）库来进行异步处理
            const THREADS = 2 // 同时上传的分块数量
            async.mapLimit(
              tasks,
              THREADS,
              ele.uploadPartFile(state, client),
              function(err, results) {
                if (err) {
                  deferred.reject(err)
                } else {
                  deferred.resolve(results)
                }
              }
            )
            return deferred.promise
          })
          .then(function(allResponse) {
            const partList = []
            allResponse.forEach(function(response, index) {
              // 生成分块清单
              partList.push({
                partNumber: index + 1,
                eTag: response.http_headers.etag
              })
            })
            return client.completeMultipartUpload(
              bucket,
              key,
              uploadId,
              partList
            ) // 完成上传
          })
          .then(function(res) {
            // 上传完成
            // ele.uploading = false
            var clouldForm = {
              hash: md5String,
              fileName: file.name.substring(0, file.name.lastIndexOf('.')),
              fileType: '.' + fileExtension,
              url: url,
              size: file.size,
              durationInSecond: Math.round(duration),
              thumbnailUrl: '',
              isPublic: ele.isPublic,
              tranStatus: 0, // 4 待转码
              recordingTime: ele.formatDateTime(file.lastModified)
            }
            ele.uploadSuccessFn(url, clouldForm)
            cloudResource(clouldForm).then(res => {

            }).catch(() => {

            })
          })
          .catch(function(err) {
            // 上传失败，添加您的代码
            ele.handleUploadError()
            console.error(err)
          })
        // }
      }
    },
    // 百度上传方法
    getTasks(file, uploadId, bucketName, key) {
      let leftSize = file.size
      let offset = 0
      let partNumber = 1
      const PART_SIZE = 5 * 1024 * 1024 // 指定分块大小
      const tasks = []

      while (leftSize > 0) {
        const partSize = Math.min(leftSize, PART_SIZE)
        tasks.push({
          file: file,
          uploadId: uploadId,
          bucketName: bucketName,
          key: key,
          partNumber: partNumber,
          partSize: partSize,
          start: offset,
          stop: offset + partSize - 1
        })

        leftSize -= partSize
        offset += partSize
        partNumber += 1
      }
      return tasks
    },
    updateUploadProcess() {
      this.uploadProcess.process = Number((Math.round((this.uploadProcess.uploadpart / this.uploadProcess.partlength) * 100 * 100) / 100).toFixed(0))
      if (this.uploadProcess.process === 100) {
        this.uploading = false
        this.uploadProcess = {
          fileCount: 0,
          uploadFileCount: 0,
          process: 0,
          partlength: 0,
          uploadpart: 0
        }
      }
      // var _p =
      //   (this.uploadProcess.uploadpart / this.uploadProcess.partlength) * 100
      // if (_p > 100) {
      //   _p = 100
      // }
      // if (this.uploadProcess.process <= _p) {
      //   this.uploadProcess.process = this.uploadProcess.process + 2
      // }
      // if (this.uploadProcess.process === 100) {
      //   clearInterval(this.timer)
      // }
    },
    // 百度上传方法
    uploadPartFile(state, client) {
      var ele = this
      return function(task, callback) {
        const blob = task.file.slice(task.start, task.stop + 1)
        client
          .uploadPartFromBlob(
            task.bucketName,
            task.key,
            task.uploadId,
            task.partNumber,
            task.partSize,
            blob
          )
          .then(function(res) {
            ++state.loaded
            ele.uploadProcess.uploadpart++
            callback(null, res)
          })
          .catch(function(err) {
            callback(err)
          })
      }
    },
    formatDateTime(value) {
      if (value) {
        return moment(value).format('yyyy-MM-DD HH:mm:ss')
      }
    }
  }
}
</script>
<style>
.upload_btn {
  /* margin-left: 10px; */
  display: block;
}
</style>

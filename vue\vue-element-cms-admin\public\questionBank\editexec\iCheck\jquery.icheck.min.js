﻿/*!
 * iCheck v1.0.2, http://git.io/arlzeA
 * ===================================
 * Powerful jQuery and Zepto plugin for checkboxes and radio buttons customization
 *
 * (c) 2013 <PERSON><PERSON>, http://fronteed.com
 * MIT Licensed
 */
(function(n){function c(n,r,u){var c=n[0],s=/er/.test(u)?_indeterminate:/bl/.test(u)?i:t,h=u==_update?{checked:c[t],disabled:c[i],indeterminate:n.attr(_indeterminate)=="true"||n.attr(_determinate)=="false"}:c[s],l;if(/^(ch|di|in)/.test(u)&&!h)o(n,s);else if(/^(un|en|de)/.test(u)&&h)e(n,s);else if(u==_update)for(l in h)h[l]?o(n,l,!0):e(n,l,!0);else r&&u!="toggle"||(r||n[_callback]("ifClicked"),h?c[_type]!==f&&e(n,s):o(n,s))}function o(o,h,c){var v=o[0],p=o.parent(),b=h==t,k=h==_indeterminate,nt=h==i,g=k?_determinate:b?a:"enabled",tt=u(o,g+s(v[_type])),it=u(o,h+s(v[_type])),d,w;v[h]!==!0&&(!c&&h==t&&v[_type]==f&&v.name&&(d=o.closest("form"),w='input[name="'+v.name+'"]',w=d.length?d.find(w):n(w),w.each(function(){this!==v&&n(this).data(r)&&e(n(this),h)})),k?(v[h]=!0,v[t]&&e(o,t,"force")):(c||(v[h]=!0),b&&v[_indeterminate]&&e(o,_indeterminate,!1)),y(o,b,h,c));v[i]&&!!u(o,_cursor,!0)&&p.find("."+l).css(_cursor,"default");p[_add](it||u(o,h)||"");!p.attr("role")||k||p.attr("aria-"+(nt?i:t),"true");p[_remove](tt||u(o,g)||"")}function e(n,r,f){var e=n[0],o=n.parent(),v=r==t,h=r==_indeterminate,p=r==i,c=h?_determinate:v?a:"enabled",w=u(n,c+s(e[_type])),b=u(n,r+s(e[_type]));e[r]!==!1&&((h||!f||f=="force")&&(e[r]=!1),y(n,v,c,f));e[i]||!u(n,_cursor,!0)||o.find("."+l).css(_cursor,"pointer");o[_remove](b||u(n,r)||"");!o.attr("role")||h||o.attr("aria-"+(p?i:t),"false");o[_add](w||u(n,c)||"")}function v(t,i){t.data(r)&&(t.parent().html(t.attr("style",t.data(r).s||"")),i&&t[_callback](i),t.off(".i").unwrap(),n(_label+'[for="'+t[0].id+'"]').add(t.closest(_label)).off(".i"))}function u(n,t,i){if(n.data(r))return n.data(r).o[t+(i?"":"Class")]}function s(n){return n.charAt(0).toUpperCase()+n.slice(1)}function y(n,t,i,r){r||(t&&n[_callback]("ifToggled"),n[_callback]("ifChanged")[_callback]("if"+s(i)))}var r="iCheck",l=r+"-helper",h="checkbox",f="radio",t="checked",a="un"+t,i="disabled";_determinate="determinate";_indeterminate="in"+_determinate;_update="update";_type="type";_click="click";_touch="touchbegin.i touchend.i";_add="addClass";_remove="removeClass";_callback="trigger";_label="label";_cursor="cursor";_mobile=/ipad|iphone|ipod|android|blackberry|windows phone|opera mini|silk/i.test(navigator.userAgent);n.fn[r]=function(u,s){var b='input[type="'+h+'"], input[type="'+f+'"]',y=n(),g=function(t){t.each(function(){var t=n(this);y=t.is(b)?y.add(t):y.add(t.find(b))})};if(/^(check|uncheck|toggle|indeterminate|determinate|disable|enable|update|destroy)$/i.test(u))return u=u.toLowerCase(),g(this),y.each(function(){var t=n(this);u=="destroy"?v(t,"ifDestroyed"):c(t,!0,u);n.isFunction(s)&&s()});if(typeof u!="object"&&u)return this;var a=n.extend({checkedClass:t,disabledClass:i,indeterminateClass:_indeterminate,labelHover:!0},u),k=a.handle,w=a.hoverClass||"hover",it=a.focusClass||"focus",nt=a.activeClass||"active",tt=!!a.labelHover,d=a.labelHoverClass||"hover",p=(""+a.increaseArea).replace("%","")|0;return(k==h||k==f)&&(b='input[type="'+k+'"]'),p<-50&&(p=-50),g(this),y.each(function(){var s=n(this);v(s);var y=this,k=y.id,g=-p+"%",rt=100+p*2+"%",ut={position:"absolute",top:g,left:g,display:"block",width:rt,height:rt,margin:0,padding:0,background:"#fff",border:0,opacity:0},st=_mobile?{position:"absolute",visibility:"hidden"}:p?ut:{position:"absolute",opacity:0},ht=y[_type]==h?a.checkboxClass||"i"+h:a.radioClass||"i"+f,b=n(_label+'[for="'+k+'"]').add(s.closest(_label)),ft=!!a.aria,et=r+"-"+Math.random().toString(36).substr(2,6),u='<div class="'+ht+'" '+(ft?'role="'+y[_type]+'" ':""),ot;if(ft&&b.each(function(){u+='aria-labelledby="';this.id?u+=this.id:(this.id=et,u+=et);u+='"'}),u=s.wrap(u+"/>")[_callback]("ifCreated").parent().append(a.insert),ot=n('<ins class="'+l+'"/>').css(ut).appendTo(u),s.data(r,{o:a,s:s.attr("style")}).css(st),!a.inheritClass||u[_add](y.className||""),!!a.inheritID&&k&&u.attr("id",r+"-"+k),u.css("position")=="static"&&u.css("position","relative"),c(s,!0,_update),b.length)b.on(_click+".i mouseover.i mouseout.i "+_touch,function(t){var r=t[_type],f=n(this);if(!y[i]){if(r==_click){if(n(t.target).is("a"))return;c(s,!1,!0)}else tt&&(/ut|nd/.test(r)?(u[_remove](w),f[_remove](d)):(u[_add](w),f[_add](d)));if(_mobile)t.stopPropagation();else return!1}});s.on(_click+".i focus.i blur.i keyup.i keydown.i keypress.i",function(n){var i=n[_type],r=n.keyCode;if(i==_click)return!1;if(i=="keydown"&&r==32)return y[_type]==f&&y[t]||(y[t]?e(s,t):o(s,t)),!1;i=="keyup"&&y[_type]==f?y[t]||o(s,t):/us|ur/.test(i)&&u[i=="blur"?_remove:_add](it)});ot.on(_click+" mousedown mouseup mouseover mouseout "+_touch,function(n){var t=n[_type],r=/wn|up/.test(t)?nt:w;if(!y[i])if(t==_click?c(s,!1,!0):(/wn|er|in/.test(t)?u[_add](r):u[_remove](r+" "+nt),b.length&&tt&&r==w&&b[/ut|nd/.test(t)?_remove:_add](d)),_mobile)n.stopPropagation();else return!1})})}})(window.jQuery||window.Zepto);
//# sourceMappingURL=jquery.icheck.min.js.map

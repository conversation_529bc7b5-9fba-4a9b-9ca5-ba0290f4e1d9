<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-05 15:08:19
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-13 13:40:39
 * @FilePath: /vue-element-cms-admin/src/views/exam-paper/paper-tags.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-card shadow="always" class="box-card el-card_header_border_0">
      <div slot="header">
         <el-input v-model="listQuery.Filter" clearable size="small" placeholder="搜索..." class="small_input"
              @keyup.enter.native="handleRefreshList" />
            <el-button round size="mini" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button type="primary" round size="small" icon="el-icon-plus" @click="handleEdit(0, 0)">添加</el-button>
        <export-excel :header="['名称']"
            :filter-val="['name']"
            :paging="false"
            :apiFn="examPaperTagAll" />
      </div>
      <el-table :data="list" v-loading="listLoading" size="small" @sort-change="sortChange">
        <el-table-column label="名称" prop="name" sortable="name"></el-table-column>
        <el-table-column label="操作" width="200px">
          <template slot-scope="{row}">
            <el-button round type="primary" size="mini" icon="el-icon-edit" @click="handleEdit(1, row)">编辑</el-button>
            <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getList" />
    </el-card>
    <el-dialog :close-on-click-modal="false" :visible.sync="classDialog" :title="isEdit ? '编辑' : '添加'" width="520px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" style="width: 380px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="classDialog = false">取消</el-button>
        <el-button round :loading="formLoading" type="primary" @click="handleSave">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import { examPaperTagAdd, examPaperTagEdit, examPaperTagList, examPaperTagAll, examPaperTagDelete } from '@/api/examPaper'
export default {
  name: 'ExamPaperTag',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        MaxResultCount: 10,
        SkipCount: 0,
        Sorting: '',
        page: 1,
        totalCount: 0
      },
      classDialog: false,
      isEdit: false,
      form: {
        name: ''
      },
      formLoading: false,
      rules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }]
      }
    }
  },

  created() {
    this.getList()
  },
  methods: {
    examPaperTagAll() {
      return examPaperTagAll()
    },
    handleEdit(t, row) {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.form = {
        name: ''
      }
      this.isEdit = t ? true : false
      if (this.isEdit) {
        this.form.id = row.id
        this.form.name = row.name
      }
      this.classDialog = true
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            console.log(this.form)
            examPaperTagEdit(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.classDialog = false
              this.formLoading = false
              this.getList()
            }).catch(() => {
              this.formLoading = false
              this.$message.error('编辑失败')
            })
          } else {
            examPaperTagAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.classDialog = false
              this.formLoading = false
              this.getList()
            }).catch(() => {
              this.$message.error('添加失败')
              this.formLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('是否删除 ' + row.name + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        examPaperTagDelete(row.id).then((response) => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      examPaperTagList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).then(res => {
        this.listLoading = false
      })
    }
  }
}

</script>

<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header">
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['CourseManagement.TrainPackages.Create']" size="small" round type="primary" icon="el-icon-plus" @click="handleAddClick">添加</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange" highlight-current-row>
        <el-table-column label="培训包名称" prop="name" sortable="name" min-width="200" />
        <el-table-column label="分类" prop="categoryName" width="200" />
        <el-table-column label="课程数" prop="courseCount" sortable="courseCount" width="100" />
        <el-table-column label="总课时数" prop="classHour" sortable="classHour" width="100" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="180">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDatetime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="{row}">
            <el-button v-permission="['CourseManagement.TrainPackages.Update']" round type="primary" size="mini" icon="el-icon-edit" @click="handleEditClick(row)">编辑</el-button>
            <el-button v-permission="['CourseManagement.TrainPackages.Delete']" round type="danger" size="mini" icon="el-icon-delete" @click="handleDeleteClick(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getTrainsTrainPackList"
      />
    </el-card>
    <el-dialog title="添加" :close-on-click-modal="false" :visible.sync="trainPackDialog" width="600px">
      <el-form ref="form" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-cascader v-model="form.categoryId" filterable clearable :options="categoryList" :props="{'label': 'name','value': 'id','children': 'children','checkStrictly': true,'emitPath': false}" size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
          <!-- <el-select v-model="form.categoryId" clearable placeholder="选择课程分类" style="width: 100%">
            <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" :loading="dialogLoading" @click="trainPackDialogSure">确 定</el-button>
        <el-button round @click="trainPackDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsPackList, trainsPackAdd, trainsPackDelete } from '@/api/train'
import { courseCategoryList } from '@/api/course'
import permission from '@/directive/permission'
import Pagination from '@/components/Pagination'
export default {
  name: 'TrainPack',
  directives: { permission },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 添加Dialog
      trainPackDialog: false,
      dialogLoading: false,
      // 分类列表
      categoryList: [],
      // 添加form
      form: {
        name: '',
        categoryId: ''
      },
      formRules: {
        name: [{
          required: true,
          message: '请输入名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 100,
          message: '长度在 1 到 100 个字符',
          trigger: 'blur'
        }],
        categoryId: [{
          required: true,
          message: '请选择课程分类',
          trigger: 'change'
        }]
      }
    }
  },
  created() {
    this.getTrainsTrainPackList()
    this.getCourseCategoryList()
  },
  methods: {
    handleAddClick() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.trainPackDialog = true
    },
    handleDeleteClick(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        trainsPackDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getTrainsTrainPackList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleEditClick(row) {
      this.$router.push({
        name: 'PackEdit',
        query: { id: row.id }
      })
    },
    trainPackDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          trainsPackAdd(this.form).then(res => {
            this.dialogLoading = false
            this.trainPackDialog = false
            this.$message.success('添加成功')
            this.getTrainsTrainPackList()
          }).catch(() => {
            this.dialogLoading = false
          })
        } else {
          return false
        }
      })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getTrainsTrainPackList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getTrainsTrainPackList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getTrainsTrainPackList()
    },
    getTrainsTrainPackList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsPackList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.categoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    }
  }
}
</script>

<template>
  <div>
    <el-row :gutter="15">
      <el-col :span="6">
        <el-card class="shadow_none card-padding-0">
          <div slot="header" class="clearfix">
            <el-button
              type="text"
              style="padding: 0;"
              @click="handleNodeClick()"
            >全部</el-button>
          </div>
          <el-tree
            class="course-dir-tree"
            style="height: 811px;overflow: auto"
            :data="treeData"
            node-key="id"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="shadow_none">
          <div slot="header" class="flex_between_box">
            <span>{{ currentCategory }}</span>
            <div>
              <el-input v-model="listQuery.Filter" clearable size="small" placeholder="搜索..." style="width: 200px" class="filter-item" @keyup.enter.native="handleSearch" />
              <el-button class="filter-item" size="small" round type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            </div>
          </div>
          <!-- <el-row>
            <span style="margin-right: 20px">题型</span>
            <el-radio-group v-model="listQuery.questionType" @change="radioChange">
              <el-radio :label="''" aria-checked="true">全部</el-radio>
              <el-radio :label="0">单选题</el-radio>
              <el-radio :label="1">多选题</el-radio>
              <el-radio :label="2">判断</el-radio>
            </el-radio-group>
          </el-row> -->
          <el-row>
            <span style="margin-right: 20px">难度</span>
            <el-radio-group v-model="listQuery.difficulty" @change="radioChange">
              <el-radio :label="''" aria-checked="true">全部</el-radio>
              <el-radio :label="1">易</el-radio>
              <el-radio :label="2">偏易</el-radio>
              <el-radio :label="3">适中</el-radio>
              <el-radio :label="4">偏难</el-radio>
              <el-radio :label="5">难</el-radio>
            </el-radio-group>
          </el-row>
          <el-table ref="questionList" v-loading="listLoading" :data="list" height="605px" highlight-current-row @sort-change="sortChange" @selection-change="handleQuestionChange" @row-click="handleQuestionRowClick">
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="questionType"
              label="类型"
              sortable="questionType"

              width="120px"
            >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.questionType === 0" type="success" size="medium">单选题</el-tag>
                <el-tag v-if="scope.row.questionType === 1" size="medium">多选题</el-tag>
                <el-tag v-if="scope.row.questionType === 2" size="medium" type="warning">判断题</el-tag>
                <el-tag v-if="scope.row.questionType === 3" type="danger" size="medium">填空题</el-tag>
                <el-tag v-if="scope.row.questionType === 6" type="info" size="medium">问答题</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="questionStem"
              label="题干"
              sortable="questionStem"
            >
              <template slot-scope="{ row }">
                <span class="link-type" @click="handlePreviewQuestion(row)">{{
                  JSON.parse(row.questionStem).Title
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop=""
              label="操作"
              width="120px"
            >
              <template slot-scope="{ row }">
                <el-button type="primary" round size="mini" icon="el-icon-view" @click="handlePreviewQuestion(row)">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="totalCount > 0"
            :total="totalCount"
            :page.sync="page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getQuestionBankList"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      v-if="questionDetailDialog"
      class="questionDetailDialog"
      title="题目预览"
      :visible.sync="questionDetailDialog"
      width="950px"
      :append-to-body="true"
    >
      <question-preview :data="questionDetailItem" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="questionDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import QuestionPreview from '@/components/QuestionPreview'
import apiQuestionBank from '@/api/questionBank'
import apiQuesBankCategory from '@/api/questionBankCategory'
export default {
  name: 'TenantBank',
  components: {
    Pagination, QuestionPreview
  },
  props: {
    questionType: {
      required: true,
      type: Number,
      default: function() {
        return 0
      }
    }
  },
  data() {
    return {
      listQuery: {
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        questionType: this.questionType,
        difficulty: '',
        questionBankCategoryId: null
      },
      totalCount: 0,
      page: 1,
      list: [],
      listLoading: false,
      treeData: [],

      currentCategory: '全部',
      questionDetailDialog: false,
      questionDetailItem: {}
    }
  },
  mounted() {
    this.getCategorys()
    this.getQuestionBankList()
  },
  methods: {
    handleNodeClick(data) {
      if (data !== undefined) {
        this.listQuery.questionBankCategoryId = data.id
        this.currentCategory = data.label
      } else {
        this.listQuery.questionBankCategoryId = null
        this.currentCategory = '全部分类'
      }
      this.getQuestionBankList()
    },
    getCategorys() {
      apiQuesBankCategory.getCategorys().then(response => {
        this.loadTree(response.items)
      })
    },
    loadTree(items) {
      if (items === undefined) {
        return
      }
      this.treeData = []
      items.forEach((item) => {
        if (item.parentId === null) {
          var element = {
            id: item.id,
            label: item.name,
            parentId: item.parentId,
            children: []
          }
          element.hasChildren = false
          this.treeData.push(element)
        }
      })
      this.setChildren(this.treeData, items)
    },
    setChildren(roots, items) {
      roots.forEach((element) => {
        items.forEach((item) => {
          if (item.parentId === element.id) {
            if (!element.children) element.children = []
            element.children.push({
              id: item.id,
              label: item.name,
              parentId: item.parentId
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },
    handleSearch() {
      this.page = 1
      this.getQuestionBankList()
    },

    handlePreviewQuestion(row) {
      this.questionDetailItem = row
      this.questionDetailDialog = true
    },
    // 替换试题点击变化
    handleQuestionChange(val) {
      this.$emit('tenant-select-change', val[0])
      this.newQuestionDetail = val
      if (val.length > 1) {
        this.$refs.questionList.clearSelection()
        this.$refs.questionList.toggleRowSelection(val.pop())
      }
    },
    // 替换试题行点击
    handleQuestionRowClick(row) {
      this.newQuestionDetail = []
      this.$refs.questionList.clearSelection()
      this.$refs.questionList.toggleRowSelection(row)
      this.newQuestionDetail.push(row)
      this.$emit('tenant-select-change', row)
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getQuestionBankList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getQuestionBankList()
    },
    getQuestionBankList() {
      this.listLoading = true
      this.listQuery.SkipCount =
          (this.page - 1) * this.listQuery.MaxResultCount
      apiQuestionBank.getQuestionBanks(this.listQuery).then(response => {
        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    radioChange() {
      this.getQuestionBankList()
    }
  }
}
</script>
<style  scoped>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
.iframe_box {
  width: 100%;
  height: 85vh;
  border: none;
}
.import-dialog >>> .el-dialog__body{
  padding-top: 10px;
  padding-bottom: 2px;
}

.el-card .el-row {
  position: relative;
  margin: 0px 0px 20px 0px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>

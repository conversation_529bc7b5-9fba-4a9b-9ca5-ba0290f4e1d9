import axios from 'axios'

const api_domain = 'https://open.geekbang.org'
const app_id = '6b704181-3558-4886-9861-24280eae3337'
const app_secret = '0c8e139c851232fc4f493a6f1afa9ab7c5e43427'

let enterprise_token = '' // 企业token

// 1. 获取企业token
export async function getEnterpriseToken() {
  console.log('获取企业token')

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async(resolve) => {
    const res = await axios({
      method: 'post',
      url: api_domain + '/serv/v1/es/auth',
      data: {
        app_id: app_id,
        app_secret: app_secret
      }
    })
    console.log(res)

    enterprise_token = res.data?.data?.token || ''

    resolve(enterprise_token)
  })
}
// user_list:[
//     {
//       "user_no": "174d311a-d7a8-9823-9332-3a04255f7608", //汇智学园里的 userId
//       "user_name":"15152215037" //汇智学园里的 userName 或者 name，我用的 userName
//     },
//     {
//       "user_no": "d327ad8b-5f6a-7330-1e1c-3a032ac02bba",
//       "user_name": "test2"
//     }
//   ]

/**
 * 2. 员工信息同步接口
 * @param {*} user_list
 * @param {*} $action
 * @returns
 */
async function setSyncUser(user_list, $action) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async(resolve) => {
    const res = await axios({
      method: 'post',
      url: api_domain + '/serv/v1/es/sync_user',
      data: {
        token: enterprise_token,
        action: $action || 'add', // add del
        user_list: user_list
      }
    })
    console.log(res)

    resolve(res.data?.data?.result || false)
  })
}

/**
 * 批量注册 （极客可以批量注册，但是一次注册数目，不能太多，暂定50人，然后每次注册时间要间隔2分钟，不然接口很容易响应错误）
 * @param {*} totalList 账号数组
 */
export function batchRegistGeek(totalList) {
  let index = 0
  const t = window.setInterval(async() => {
    if (index >= totalList.length) {
      window.clearInterval(t)
      return
    }

    const index2 = index + 50
    const index3 = index2 > totalList.length ? totalList.length : index2

    const arr = totalList.slice(index, index3)

    const result = await setSyncUser(arr)

    // console.log(`${result ? '成功' : '失败'}注册 序号 ${index + 1} - ${index3}`, 'bold');
    if (result) {
      console.log(index, index3 - 1, arr)
      // eslint-disable-next-line require-atomic-updates
      index = index3
    }
  }, 1000 * 5 * 2)
}

// 使用
// getEnterpriseToken()
// batchRegist( /*totalList 数组*/ )
// totalList:[
//     {
//       "user_no": "174d311a-d7a8-9823-9332-3a04255f7608", //汇智学园里的 userId
//       "user_name":"15152215037" //汇智学园里的 userName 或者 name，我用的 userName
//     },
//     {
//       "user_no": "d327ad8b-5f6a-7330-1e1c-3a032ac02bba",
//       "user_name": "test2"
//     }
//   ]

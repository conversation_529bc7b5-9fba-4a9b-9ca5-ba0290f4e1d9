/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-03-31 11:17:21
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-06-28 11:47:55
 * @FilePath: /vue-element-cms-admin/public/questionBank/baiduUpload/localUpload.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
LocalUpload = function () {

    var InitUploadCommon = function (config) {
        //_config=config;

        $("#" + config.browse_button).change(function (e) {
            uploadFile(this.files[0], config)
        })
        function uploadFile(fileInfo, _config) {
            const formData = new FormData()
            formData.append('FileContainerName', 'default')
            formData.append('FileType', 2)
            formData.append('GenerateUniqueFileName', false)
            formData.append('OwnerUserId', localStorage.getItem('userId'))
            formData.append('File', fileInfo)

            xhrPostFormData('/api/file-management/file', formData, function (res) {
                htmlCanvas(res.downloadInfo.downloadUrl, _config)
            }, function (res) {
            })
        }
        function htmlCanvas(url, _config) {
            $('#' + _config.result + " .imgDiv1").remove();

            if (_config.result == "result_img") {
                var data = {
                    type: 5
                }
                $('.ui-questions-content-list').append(template("drag_choice", data));
            }

            var html = '';
            html += '<div class="box" style="margin-bottom:0px">';
            html += '<img src=' + url + ' class="img-thumbnail img-rounded img-responsive"  style="width: 160px; height: 100px;" />';
            html += ' <div class="box-content">';
            html += ' <ul class="icon">';
            html += ' <li><a href="javascript:;" class="viewPic"><img src="/questionBank/editexec/img/exc_see.png" class="icn_img" data-tisp="查看大图" /></a></li>';
            //html += ' <li><a href="javascript:;" class="delPic_img"><img src="/Common/editexec/img/exc_delpic.png" class="icn_img" data-tisp="删除" /></a></li>';
            html += ' </ul>';
            html += ' </div>';
            html += ' </div>';

            var html1 = '';
            html1 += '<div class="col-md-3 box" style="margin-bottom:0px">';
            html1 += '<img src=' + url + ' class="img-thumbnail img-rounded img-responsive"  style="width: 160px; height: 100px;" />';
            html1 += ' <div class="box-content">';
            html1 += ' <ul class="icon">';
            html1 += ' <li><a href="javascript:;" class="viewPic"><img src="/questionBank/editexec/img/exc_see.png" class="icn_img" data-tisp="查看大图" /></a></li>';
            html1 += ' <li><a href="javascript:;" class="delPic_img"><img src="/questionBank/editexec/img/exc_delpic.png" class="icn_img" data-tisp="删除" /></a></li>';
            html1 += ' </ul>';
            html1 += ' </div>';
            html1 += ' </div>';

            var resHtml = '';
            resHtml += '<div class="col-md-3">';
            resHtml += '<img src=' + url + ' class="img-thumbnail img-rounded img-responsive"  style="width: 160px; height: 100px;" />';
            resHtml += ' </div>';

            if (_config.result == "result_img") {
                $('.imgadds').each(function () {
                    if ($(this).html().indexOf('img') < 0) {
                        $(this).append(html);
                    }
                })
            }
            else {
                $('#' + _config.result).append(resHtml);
            }
            //同名文件取消可以再次上传
            $('#' + _config.browse_button).val("");

            $('.ui-img-list[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').append(html1);
            var $itemInput = $('.cq-answer-content[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li').find('.input-check').find('input');
            var $imageInput = $('.cq-answer-content[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li').find('.ui-img-option-list');
            if ($itemInput.size()) {
                $imageInput.append(html1);
            }
        }

    



    }
    //初始化组件
    var InitCommonFun = function (config) { }
    InitCommonFun.prototype = {
        init: function (config) {
            this.InitUpload = new InitUploadCommon(config);
        },
    };
    return InitCommonFun;
}();
import axios from '@/axios'

// 获取所有学生成绩列表
export function studentExamResultList(data) {
  return axios.gets('/api/exams/examusers/results', data)
}

// 学生答题详情 data (id,userId)
export function studentExamDetail(data) {
  return axios.gets('/api/exams/examusers/record', data)
}

// 获取试卷题目详情
export function examQuestionsDetail(id) {
  return axios.gets(`/api/exams/examinations/${id}/questions`)
}

export function submitRecord(data) {
  return axios.posts(`api/exams/examusers/submit-record`, data)
}

export function submitRecords(data) {
  return axios.posts(`api/exams/examusers/submit-records`, data)
}


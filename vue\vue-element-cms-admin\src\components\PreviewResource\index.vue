<template>
  <div class="resource_preview">
    <video-player v-if="type === 'video'" ref="videoPlayer" class="video-player vjs-custom-skin" :playsinline="true" :options="playerOptions" />
    <!-- <el-image v-if="type === 'image'" class="image_preview" fit="contain" :src="url" /> -->
    <img v-if="type === 'image'" class="image_preview" fit="contain" :src="url">
    <div v-if="type === 'pdf'" class="pdf_preview">
      <iframe
        v-if="url != '' && url != null"
        style="width: 100%; height: 100%"
        :src="'./pdfjs/web/viewer.html?file=' + encodeURIComponent(url)"
      />
    </div>
    <iframe
      v-if="type === 'h5'"
      ref=""
      class="h5_preview"
      :src="url"
    />
    <iframe
      v-if="type === 'hundun-video'"
      ref=""
      class="h5_preview"
      :src="'./js/hundun/video/index.html?url=' + url"
    />
    <iframe
      v-if="type === 'geektime-video'"
      ref=""
      class="h5_preview"
      :src="'./js/geektime/video/index.html?url=' + url"
    />
    <iframe
      v-if="type === 'geektime-html'"
      ref=""
      class="h5_preview"
      :src="'./js/geektime/html/index.html?url=' + url"
    />
    <iframe
      v-if="type === 'ximalaya'"
      ref=""
      class="h5_preview"
      :src="'./js/ximalaya/index.html?url=' + url"
    />
    <iframe
      v-if="type === 'videoh5'"
      ref=""
      class="h5_preview"
      :src="'./js/videoh5/index.html?url=' + url"
    />
    <iframe
      v-if="type === 'pdfh5'"
      ref=""
      class="h5_preview"
      :src="'./js/pdfh5/index.html?url=' + url"
    />
  </div>

</template>
<script>
import { videoPlayer } from 'vue-video-player'
import pdf from 'vue-pdf'
import 'video.js/dist/video-js.css'
export default {
  name: 'PreviewResource',
  components: {
    videoPlayer,
    pdf
  },
  props: {
    url: {
      type: String,
      require: true,
      default: ''
    },
    type: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      playerOptions: {
        playbackRates: [0.7, 1.0, 1.5, 2.0], // 播放速度
        autoplay: false, // 如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [{
          src: this.url, // 路径
          type: 'video/mp4' // 类型
        }],
        // poster: '../../static/images/test.jpg', // 你的封面地址
        // width: document.documentElement.clientWidth,
        notSupportedMessage: '此视频暂无法播放，请稍后再试' // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        // controlBar: {
        //   timeDivider: true,
        //   durationDisplay: true,
        //   remainingTimeDisplay: false,
        //   fullscreenToggle: true // 全屏按钮
        // }
      },
      pageNum: 1,
      pageTotalNum: 1, // 总页数
      loadedRatio: 0 // 当前页面的加载进度，范围是0-1 ，等于1的时候代表当前页已经完全加载完成了
    }
  },
  mounted() {
    this.$on('videoPause', () => {
      this.$refs.videoPlayer.player.pause()
    })
    if (this.type === 'pdf') {
      this.getNumPages()
    }
  },
  methods: {
    getNumPages() {
      const loadingTask = pdf.createLoadingTask(this.url)
      loadingTask.promise.then(pdf => {
        this.pageTotalNum = pdf.numPages
      }).catch(err => {
        console.error('pdf 加载失败', err)
      })
    }
  }
}
</script>
<style scoped>
.video-player ::v-deep .vjs-big-play-button{
  border-radius: 50%;
  width: 50px;
  height: 50px;
  /* margin: 0 auto; */
  top: 50%;
  left: 50%;
  transform: translateY(-50%);
}
 .video-player ::v-deep .vjs-time-control{
   display:block;
  }
.pdf_preview {
  height: 66vh;
  overflow-y: scroll;
  /* display: flex;
  align-items: center; */
}
.h5_preview{
  width: 100%;
  height: 700px;
  border: 1px solid #eee;
}
.btn_group {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.image_preview {
  width: 100%;
  height: 100%
}
</style>

!function e(t,n,o){function i(r,u){if(!n[r]){if(!t[r]){var d="function"==typeof require&&require;if(!u&&d)return d(r,!0);if(l)return l(r,!0);var f=new Error("Cannot find module '"+r+"'");throw f.code="MODULE_NOT_FOUND",f}var s=n[r]={exports:{}};t[r][0].call(s.exports,function(e){var n=t[r][1][e];return i(n||e)},s,s.exports,e,t,n,o)}return n[r].exports}for(var l="function"==typeof require&&require,r=0;r<o.length;r++)i(o[r]);return i}({1:[function(e,t,n){(function(t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}n.__esModule=!0;var u="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,d=o(u),f=e(4),s=o(f),a=e(2),c=o(a),p=function(e){function t(){var n;i(this,t);var o=l(this,e.call(this)),r=o;if(d["default"].browser.IS_IE8){r=s["default"].createElement("custom");for(var u in t.prototype)"constructor"!==u&&(r[u]=t.prototype[u])}return r.levels_=[],r.selectedIndex_=-1,Object.defineProperty(r,"selectedIndex",{get:function(){return r.selectedIndex_}}),Object.defineProperty(r,"length",{get:function(){return r.levels_.length}}),n=r,l(o,n)}return r(t,e),t.prototype.addQualityLevel=function(e){var t=this.getQualityLevelById(e.id);if(t)return t;var n=this.levels_.length;return t=new c["default"](e),""+n in this||Object.defineProperty(this,n,{get:function(){return this.levels_[n]}}),this.levels_.push(t),this.trigger({qualityLevel:t,type:"addqualitylevel"}),t},t.prototype.removeQualityLevel=function(e){for(var t=null,n=0,o=this.length;n<o;n++)if(this[n]===e){t=this.levels_.splice(n,1)[0],this.selectedIndex_===n?this.selectedIndex_=-1:this.selectedIndex_>n&&this.selectedIndex_--;break}return t&&this.trigger({qualityLevel:e,type:"removequalitylevel"}),t},t.prototype.getQualityLevelById=function(e){for(var t=0,n=this.length;t<n;t++){var o=this[t];if(o.id===e)return o}return null},t.prototype.dispose=function(){this.selectedIndex_=-1,this.levels_.length=0},t}(d["default"].EventTarget);p.prototype.allowedEvents_={change:"change",addqualitylevel:"addqualitylevel",removequalitylevel:"removequalitylevel"};for(var y in p.prototype.allowedEvents_)p.prototype["on"+y]=null;n["default"]=p}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(e,t,n){(function(t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.__esModule=!0;var l="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,r=o(l),u=e(4),d=o(u),f=function s(e){i(this,s);var t=this;if(r["default"].browser.IS_IE8){t=d["default"].createElement("custom");for(var n in s.prototype)"constructor"!==n&&(t[n]=s.prototype[n])}return t.id=e.id,t.label=t.id,t.width=e.width,t.height=e.height,t.bitrate=e.bandwidth,t.enabled_=e.enabled,Object.defineProperty(t,"enabled",{get:function(){return t.enabled_()},set:function(e){t.enabled_(e)}}),t};n["default"]=f}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],3:[function(e,t,n){},{}],4:[function(e,t,n){(function(n){var o,i=void 0!==n?n:"undefined"!=typeof window?window:{},l=e(3);"undefined"!=typeof document?o=document:(o=i["__GLOBAL_DOCUMENT_CACHE@4"])||(o=i["__GLOBAL_DOCUMENT_CACHE@4"]=l),t.exports=o}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],5:[function(e,t,n){(function(t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}n.__esModule=!0;var i="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,l=o(i),r=e(1),u=o(r),d=l["default"].registerPlugin||l["default"].plugin,f=function(e,t){var n=e.qualityLevels,o=new u["default"],i=function l(){o.dispose(),e.qualityLevels=n,e.off("dispose",l)};return e.on("dispose",i),e.qualityLevels=function(){return o},e.qualityLevels.VERSION="2.0.4",o},s=function(e){return f(this,l["default"].mergeOptions({},e))};d("qualityLevels",s),s.VERSION="2.0.4",n["default"]=s}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[5]);
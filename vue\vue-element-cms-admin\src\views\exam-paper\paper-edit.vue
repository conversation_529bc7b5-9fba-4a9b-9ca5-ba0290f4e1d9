<template>
  <div class="app-container">
    <!-- <div>
      <el-form ref="form" :inline="true" :model="form" label-width="80px">
        <el-form-item label="试卷编号">
          <el-input v-model="form.code" :disabled="true" class="input_color" />
        </el-form-item>
        <el-form-item label="试卷名称">
          <el-input v-model="form.name" :disabled="true" />
        </el-form-item>
        <el-form-item label="试卷总分">
          <el-input v-model="form.totalScore" :disabled="true" />
        </el-form-item>
      </el-form>
    </div> -->
    <el-card class="box-card" shadow="always">
      <div slot="header" class="clearfix">
        <span class="role-span">编辑试卷_{{ form.name }} ( 总分：{{ form.totalScore }}分 )</span>
        <div style="float: right">
          <el-input
            v-model="listQuery.Filter"
            clearable
            size="small"
            placeholder="搜索..."
            style="width: 200px"
            class="filter-item"
            @input="searchContentChange"
            @keyup.enter.native="searchHandle"
          />
          <el-button class="filter-item" size="small" round type="success" icon="el-icon-search" @click="searchHandle">
            搜索
          </el-button>
          <el-button
            class="filter-item"
            :loading="repeatLoading"
            size="small"
            round
            type="primary"
            icon="el-icon-check"
            @click="handleCheckRepeat"
          >重题检查

          </el-button>
        </div>
      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" style="width: 100%" highlight-current-row @sort-change="sortChange">
        <el-table-column label="序号" prop="order" width="50px" align="center" />
        <el-table-column prop="questionType" label="题型" sortable="" header-align="left" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.questionType === 0" type="success" size="small">单选题</el-tag>
            <el-tag v-if="scope.row.questionType === 1" size="small">多选题</el-tag>
            <el-tag v-if="scope.row.questionType === 2" size="small" type="warning">判断题</el-tag>
            <el-tag v-if="scope.row.questionType === 3" size="small" type="danger">填空题</el-tag>
            <el-tag v-if="scope.row.questionType === 6" size="small" type="info">问答题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="questionStem" label="题干" sortable="" header-align="left">
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleCheck(row)">{{
              JSON.parse(row.questionStem).Title
            }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="difficulty" label="题目难度" sortable="" header-align="left" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.difficulty === null" size="medium" type="info" effect="dark">全部</el-tag>
            <el-tag v-if="scope.row.difficulty === 1" size="medium" type="info">易</el-tag>
            <el-tag v-if="scope.row.difficulty === 2" size="medium" type="info">偏易</el-tag>
            <el-tag v-if="scope.row.difficulty === 3" size="medium" type="info">适中</el-tag>
            <el-tag v-if="scope.row.difficulty === 4" size="medium" type="info">偏难</el-tag>
            <el-tag v-if="scope.row.difficulty === 5" size="medium" type="info">难</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分数" sortable="" width="80">
          <template slot-scope="{ row }">
            <span>{{ row.score }}分</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="left" width="120">
          <template slot-scope="{ row }">
            <el-button type="primary" round size="mini" icon="el-icon-sort" @click="handleEdit(row)">替换</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="totalCount > 0"
        :total="totalCount"
        :page.sync="page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog
      v-if="editExaminationDialog"
      :close-on-click-modal="false"
      class="editExaminationDialog"
      title="题库选题替换"
      :visible.sync="editExaminationDialog"
      top="5vh"
      width="1200px"
    >
      <tenant-bank :question-type="questionType" @tenant-select-change="handleTenantSelectedChange" />
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" :loading="sureBtnLoading" round type="primary" @click="sureBtn">确 定</el-button>
        <el-button size="medium" round @click="editExaminationDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="questionDetailDialog"
      class="questionDetailDialog"
      title="题目详情"
      :visible.sync="questionDetailDialog"
      width="800px"
    >
      <exam-preview :data="questionDetailItem" />
    </el-dialog>
    <el-dialog
      v-if="questionRepeatDialog"
      class="questionRepeatDialog"
      title="重题详情"
      :visible.sync="questionRepeatDialog"
      width="900px"
    >
      <el-table v-loading="repeatListLoading" :data="repeatList" size="medium" style="width: 100%" highlight-current-row>
        <el-table-column label="序号" prop="order" width="50px" align="center" />
        <el-table-column prop="questionType" label="题型" header-align="left" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.questionType === 0" type="success" size="small">单选题</el-tag>
            <el-tag v-if="scope.row.questionType === 1" size="small">多选题</el-tag>
            <el-tag v-if="scope.row.questionType === 2" size="small" type="warning">判断题</el-tag>
            <el-tag v-if="scope.row.questionType === 3" type="danger" size="small">填空题</el-tag>
            <el-tag v-if="scope.row.questionType === 6" type="info" size="small">问答题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="questionStem" label="题干" header-align="left">
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleCheck(row)">{{
              JSON.parse(row.questionStem).Title
            }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="difficulty" label="题目难度" header-align="left" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.difficulty === null" size="medium" type="info" effect="dark">全部</el-tag>
            <el-tag v-if="scope.row.difficulty === 1" size="medium" type="info">易</el-tag>
            <el-tag v-if="scope.row.difficulty === 2" size="medium" type="info">偏易</el-tag>
            <el-tag v-if="scope.row.difficulty === 3" size="medium" type="info">适中</el-tag>
            <el-tag v-if="scope.row.difficulty === 4" size="medium" type="info">偏难</el-tag>
            <el-tag v-if="scope.row.difficulty === 5" size="medium" type="info">难</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分数" width="80">
          <template slot-scope="{ row }">
            <span>{{ row.score }}分</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="left" width="120">
          <template slot-scope="{ row }">
            <el-button type="primary" round size="mini" icon="el-icon-sort" @click="handleEdit(row)">替换</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import ExamPreview from '@/components/ExamPreview'
import {
  Message
} from 'element-ui'
import {
  examPaperQuestionlist,
  examPaperRepeatQuestionlist,
  changeExamPaperQuestion,
  examPaperDetailInfo
} from '@/api/examPaper'
import TenantBank from './components/TenantBank.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'PaperEdit',
  components: {
    Pagination,
    ExamPreview,
    TenantBank
  },
  data() {
    return {
      knoweldgeTitle: '全部',
      // 试卷信息
      form: {
        code: '',
        name: '',
        totalScore: '',
        id: ''
      },
      // 试卷LIST
      list: [],
      // 重题List
      repeatList: [],
      // 重题Dialog
      questionRepeatDialog: false,
      // 重题loading
      repeatListLoading: false,
      // 重题检查按钮Loading
      repeatLoading: false,
      listLoading: false,
      // 替换的原题ID
      editQuestionId: '',
      // 点击新题替换的详细信息 取ID
      newQuestionDetail: null,
      // 替换试题dialog
      editExaminationDialog: false,
      // 替换确定按钮loading
      sureBtnLoading: false,
      // 试卷列表参数
      listQuery: {
        ExamPaperId: '',
        Filter: '',
        Sorting: 'creationTime desc',
        MaxResultCount: 10,
        SkipCount: 0
      },
      // 试卷分页总条数
      totalCount: 0,
      // 试卷分页 当前页数
      page: 1,
      // 题库查看题目 Dialog
      questionDetailDialog: false,
      // 题库查看题目 详情信息
      questionDetailItem: null,
      // 题库查看题目的类型
      questionDetailType: null,
      questionType: 0,

      activeMenu: 'TenantBank'
    }
  },
  computed: {
    ...mapGetters(['tenantPermission'])
  },
  created() {
    this.getExamPaperInfo()
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      this.listQuery.ExamPaperId = this.$route.query.examPaperId
      this.listQuery.SkipCount =
          (this.page - 1) * this.listQuery.MaxResultCount
      examPaperQuestionlist(this.listQuery).then((res) => {
        this.list = res.items
        this.totalCount = res.totalCount
        this.listLoading = false
      })
    },
    // 获取试卷详细信息
    getExamPaperInfo() {
      examPaperDetailInfo(this.$route.query.examPaperId)
        .then((res) => {
          this.form = res
        })
        .catch(() => {
          Message.error('获取试卷信息失败')
        })
    },
    // 搜索
    searchHandle() {
      this.page = 1
      this.getList()
    },
    async handleCheckRepeat() {
      this.repeatLoading = true
      try {
        const res = await examPaperRepeatQuestionlist(this.$route.query.examPaperId)
        this.repeatList = res.items
        this.repeatListLoading = false
        this.repeatLoading = false
        if (this.repeatList && this.repeatList.length > 0) {
          this.questionRepeatDialog = true
        } else {
          Message.success('没有重复的题目')
        }
      } catch (e) {
        this.repeatLoading = false
        this.repeatListLoading = false
      }
    },
    // 编辑列表搜索框清空 请求列表
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    handleTenantSelectedChange(selectedList) {
      this.newQuestionDetail = selectedList
    },
    handleJgSelectedChange(selectedList) {
      this.newQuestionDetail = selectedList
    },
    // 替换试题dialog
    handleEdit(row) {
      // 要替换试题的id
      this.editQuestionId = row.id
      this.questionType = row.questionType
      this.editExaminationDialog = true
    },
    // 确定替换试题
    sureBtn() {
      this.sureBtnLoading = true
      if (this.newQuestionDetail && this.newQuestionDetail.id) {
        changeExamPaperQuestion(
          this.$route.query.examPaperId,
          this.editQuestionId,
          this.newQuestionDetail.id
        )
          .then((res) => {
            this.sureBtnLoading = false
            this.editExaminationDialog = false
            this.$notify({
              title: '成功',
              message: '替换成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            this.getQuestionRepeatList()
          })
          .catch(() => {
            this.$notify({
              title: '失败',
              message: '替换失败',
              type: 'error',
              duration: 2000
            })
            this.sureBtnLoading = false
          })
      } else {
        Message.warning('请选择需要替换的题')
        this.sureBtnLoading = false
      }
    },
    getQuestionRepeatList() {
      this.repeatListLoading = true
      return new Promise((resolve) => {
        examPaperRepeatQuestionlist(this.$route.query.examPaperId).then(res => {
          this.repeatList = res.items
          this.repeatListLoading = false
          this.repeatLoading = false
          resolve()
        }).catch(() => {
          this.repeatLoading = false
          this.repeatListLoading = false
        })
      })
    },

    handleCheck(row) {
      this.questionDetailItem = row
      this.questionDetailType = row.questionType
      this.questionDetailDialog = true
    },
    // 列表排序
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    }

  }
}

</script>
<style scoped>
  .input_color ::v-deep .el-input__inner {
    color: #337ab7;
  }

</style>

<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header">
        <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        <el-button v-permission="['CourseManagement.CourseCategorys.Create']" round size="small" type="primary" icon="el-icon-plus" @click="handleCourseCategoryEdit(0,0)">添加</el-button>

      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" highlight-current-row @sort-change="sortChange">
        <el-table-column label="排序" prop="sort" sortable="sort" width="120" />
        <el-table-column label="分类名称" prop="name" sortable="name" />
        <el-table-column label="是否显示" prop="hide" sortable="hide" width="160">
          <template slot-scope="{row}">
            <el-tag v-if="row.hide" type="info">隐藏</el-tag>
            <el-tag v-else type="success">显示</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="{row}">
            <el-button v-permission="['CourseManagement.CourseCategorys.Update']" round type="primary" icon="el-icon-edit" size="mini" @click="handleCourseCategoryEdit(1,row)">编辑
            </el-button>
            <el-button v-permission="['CourseManagement.CourseCategorys.Delete']" round type="danger" icon="el-icon-delete" size="mini" @click="handleCourseCategoryDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getCourseCategoryList"
      />
    </el-card>
    <el-dialog :title="dialogTitle" :close-on-click-modal="false" :visible.sync="courseCategoryDialog" width="500px">
      <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="是否显示" prop="hide">
          <el-radio-group v-model="form.hide">
            <el-radio :label="false">是</el-radio>
            <el-radio :label="true">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="一级目录" prop="hide">
          <el-radio-group v-model="form.isTop">
            <el-radio :label="false">是</el-radio>
            <el-radio :label="true">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!form.isTop" label="一级目录" prop="hide">
          <el-select v-model="form.parentId" style="width: 380px">
            <el-option v-for="item in parentList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" :loading="dialogLoading" @click="courseCategoryDialogSure">确 定</el-button>
        <el-button round @click="courseCategoryDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  courseCategoryList,
  courseCategoryEdit,
  courseCategoryAdd,
  courseCategoryDelete
} from '@/api/course'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'CourseClassify',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      parentList: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'sort',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 编辑
      editId: '',
      isEdit: false,
      // dialog
      dialogTitle: '添加',
      courseCategoryDialog: false,
      dialogLoading: false,
      // form
      form: {
        name: '',
        hide: false,
        sort: 1,
        isTop: true,
        parentId: ''
      },
      formRules: {
        name: [{
          required: true,
          message: '请输入课程类别名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 30,
          message: '长度在 1 到 30 个字符',
          trigger: 'blur'
        }
        ]
      }

    }
  },
  created() {
    this.getCourseCategoryList()
  },
  methods: {
    // 编辑添加点击
    handleCourseCategoryEdit(t, row) {
      this.form = {
        name: '',
        hide: false,
        sort: 1
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      if (t === 0) {
        this.dialogTitle = '添加'
        this.isEdit = false
        this.form.sort = this.listQuery.totalCount + 1
      } else {
        this.dialogTitle = '编辑'
        this.isEdit = true
        this.editId = row.id
        this.form.name = row.name
        this.form.sort = row.sort
        this.form.hide = row.hide
      }
      this.courseCategoryDialog = true
    },
    // 删除
    handleCourseCategoryDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseCategoryDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getCourseCategoryList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 编辑添加确定
    courseCategoryDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.isEdit) {
            courseCategoryEdit(this.editId, this.form).then(res => {
              this.$message.success('编辑成功')
              this.dialogLoading = false
              this.courseCategoryDialog = false
              this.getCourseCategoryList()
            }).catch(() => {
              this.$message.error('编辑失败')
            })
          } else {
            courseCategoryAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.dialogLoading = false
              this.courseCategoryDialog = false
              this.getCourseCategoryList()
            }).catch(() => {
              this.$message.error('添加失败')
            })
          }
        } else {
          return false
        }
      })
    },
    handleSearch() {
      this.getCourseCategoryList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getCourseCategoryList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseCategoryList()
    },
    getCourseCategoryList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseCategoryList(this.listQuery).then(res => {
        this.list = res.items
        this.parentList = res.items.filter(item => item.parentId === null)
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      })
    }
  }
}

</script>

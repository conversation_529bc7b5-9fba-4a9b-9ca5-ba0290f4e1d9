<template>
  <div class="app-container">

    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>身份管理设置</span>
      </div>
      <el-form ref="form" :model="userSettingform" label-position="right" label-width="240px">
        <el-form-item label="密码设置" />
        <el-form-item label="所需长度">
          <el-input-number v-model="userSettingform.password.requiredLength" :min="2" :max="128" label="所需长度" />
        </el-form-item>
        <el-form-item label="必需的唯一字符">
          <el-input-number v-model="userSettingform.password.requiredUniqueChars" :min="1" :max="128" label="必需的唯一字符" />
        </el-form-item>
        <el-form-item label="需要非字母数字字符">
          <el-checkbox v-model="userSettingform.password.requireNonAlphanumeric">需要非字母数字字符</el-checkbox>
        </el-form-item>
        <el-form-item label="需要小写字符">
          <el-checkbox v-model="userSettingform.password.requireLowercase">需要小写字符</el-checkbox>
        </el-form-item>
        <el-form-item label="需要大写字符">
          <el-checkbox v-model="userSettingform.password.requireUppercase">需要大写字符</el-checkbox>
        </el-form-item>
        <el-form-item label="需要数字">
          <el-checkbox v-model="userSettingform.password.requireDigit">需要数字</el-checkbox>
        </el-form-item>

        <el-form-item label="锁定设置" />
        <el-form-item label="允许对新用户">
          <el-checkbox v-model="userSettingform.lockout.allowedForNewUsers">允许对新用户</el-checkbox>
        </el-form-item>
        <el-form-item label="锁定时间">
          <el-input-number v-model="userSettingform.lockout.lockoutDuration" :min="0" :max="10000" label="锁定时间" />
        </el-form-item>
        <el-form-item label="锁定前最大失败的访问尝试次数">
          <el-input-number v-model="userSettingform.lockout.maxFailedAccessAttempts" :min="0" :max="20" label="锁定前最大失败的访问尝试次数" />
        </el-form-item>

        <!-- <el-form-item label="登录设置" />
        <el-form-item label="要求确认邮箱地址">
          <el-checkbox v-model="userSettingform.signIn.requireConfirmedEmail">要求确认邮箱地址</el-checkbox>
        </el-form-item>
        <el-form-item label="要求确认手机号码">
          <el-checkbox v-model="userSettingform.signIn.requireConfirmedPhoneNumber">要求确认手机号码</el-checkbox>
        </el-form-item>
        <el-form-item label="启用电话号码确认">
          <el-checkbox v-model="userSettingform.signIn.enablePhoneNumberConfirmation">启用电话号码确认</el-checkbox>
        </el-form-item>

        <el-form-item label="用户设置" />
        <el-form-item label="启用邮箱地址更新">
          <el-checkbox v-model="userSettingform.user.isEmailUpdateEnabled">启用邮箱地址更新</el-checkbox>
        </el-form-item>
        <el-form-item label="用户名更新已启用">
          <el-checkbox v-model="userSettingform.user.isUserNameUpdateEnabled">用户名更新已启用</el-checkbox>
        </el-form-item> -->

        <el-form-item>
          <el-button round type="primary" :loading="formLoading" @click="updateUserSetting">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Set',
  components: {},
  directives: {},
  data() {
    return {
      tabPosition: 'left',
      formLoading: true,
      userSettingform: {
        password: {
          requiredLength: 2,
          requiredUniqueChars: 0,
          requireNonAlphanumeric: false,
          requireLowercase: false,
          requireUppercase: false,
          requireDigit: false
        },
        lockout: {
          allowedForNewUsers: false,
          lockoutDuration: 0,
          maxFailedAccessAttempts: 0
        },
        signIn: {
          requireConfirmedEmail: false,
          enablePhoneNumberConfirmation: false,
          requireConfirmedPhoneNumber: false
        },
        user: {
          isUserNameUpdateEnabled: false,
          isEmailUpdateEnabled: false
        }
      }
    }
  },
  created() {
    this.loadUserSetting()
  },
  methods: {
    loadUserSetting() {
      this.formLoading = true
      this.$axios
        .gets('/api/appuser/settings')
        .then(response => {
          this.userSettingform = response
          this.formLoading = false
        })
    },
    updateUserSetting() {
      this.formLoading = true
      this.$axios
        .puts('/api/appuser/settings', this.userSettingform)
        .then(response => {
          this.formLoading = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
        })
    }
  }
}
</script>

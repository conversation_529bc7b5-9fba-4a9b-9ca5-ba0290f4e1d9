<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="8" :md="8" :lg="6" :xl="4">
        <el-card class="box-card" style="max-height: 800px; overflow: auto">
          <el-tree
            :data="orgDatas"
            :props="defaultProps"
            highlight-current
            style="margin-top: 5px"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="16" :md="16" :lg="18" :xl="20">
        <el-card class="box-card">
          <div class="header_flex_box">
            <el-input
              v-model="listQuery.Filter"
              clearable
              size="small"
              placeholder="搜索..."
              class="small_input"
              @keyup.enter.native="handleFilter"
            />
            <el-button
              round
              size="mini"
              type="success"
              icon="el-icon-search"
              @click="handleFilter"
            >搜索</el-button>
            <el-button
              v-permission="['AppUserManagement.Classes.Create']"
              round
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="handleCreate"
            >新增</el-button>
          </div>

          <el-table
            ref="multipleTable"
            v-loading="listLoading"
            row-key="id"
            :data="list"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="44px" />
            <el-table-column label="名称" prop="name">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleUpdate(row)">{{
                  row.name
                }}</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="200">
              <template slot-scope="{ row }">
                <el-button
                  v-permission="['AppUserManagement.Classes.Update']"
                  round
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  @click="handleUpdate(row)"
                >编辑</el-button>
                <el-button
                  v-permission="['AppUserManagement.Classes.Delete']"
                  round
                  type="danger"
                  size="mini"
                  :disabled="row.name === 'admin'"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="totalCount > 0"
            :total="totalCount"
            :page.sync="page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :title="formTitle"
      width="520px"
      @close="cancel()"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" style="width: 380px" />
        </el-form-item>

        <el-form-item label="上级">
          <el-radio-group v-model="form.isTop" width="140px">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="form.isTop === false"
          style="margin-bottom: 0"
          label="上级"
          prop="parentId"
        >
          <!-- <el-cascader
            v-model="form.parentId"
            :options="nodesOrgs"
            :props="cascaderProps"
            :show-all-levels="false"
            clearable
            filterable
          /> -->
          <el-select v-model="form.parentId" style="width: 380px">
            <el-option
              v-for="item in nodesOrgs"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="cancel">取消</el-button>
        <el-button
          round
          :loading="formLoading"
          type="primary"
          @click="save"
        >确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import { classesData, addClasses, editClasses, deleteClasses, classesDetail } from '@/api/user'
export default {
  name: 'Organization',
  components: { Pagination },
  directives: { permission },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      defaultProps: {
        // children: 'children',
        // label: 'name',
        // id: 'id'
        children: 'children',
        label: 'name'
        // isLeaf: "leaf"
      },
      form: {
        id: null,
        name: null,
        parentId: null,
        isTop: true
      },
      orgName: '',
      list: [],
      orgs: [],
      orgDatas: [],
      totalCount: 0,
      listLoading: true,
      formLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        ParentId: '',
        SkipCount: 0,
        MaxResultCount: 10
      },
      page: 1,
      dialogFormVisible: false,
      multipleSelection: [],
      formTitle: '',
      isEdit: false,
      nodesOrgs: []
    }
  },
  created() {
    this.getList()
    this.getTreeDatas()
  },
  methods: {
    // async getTreeDatas() {
    //   const res = await orgsData()
    //   this.orgDatas = res.items.reduce((total, item, index, list) => {
    //     if (item.parentId === null) {
    //       total.push({
    //         ...item,
    //         children: list.filter((f) => f.parentId === item.id)
    //       })
    //     } else {
    //       item.children = list.filter((f) => f.parentId === item.id)
    //     }
    //     return total
    //   }, [])
    //   const result = this.orgDatas.map(item => {
    //     const children = item.children.map(every => {
    //       var { children, ...tmp } = every
    //       return tmp
    //     })
    //     return {
    //       ...item,
    //       children: children
    //     }
    //   })
    //   this.nodesOrgs = result
    // },

    getTreeDatas() {
      classesData().then((response) => {
        this.nodesOrgs = response.items.filter(item => {
          return item.parentId === null
        })
        this.orgDatas = response.items.reduce((total, item, index, list) => {
          if (item.parentId === null) {
            total.push({
              ...item,
              children: list.filter((f) => f.parentId === item.id)
            })
          } else {
            item.children = list.filter((f) => f.parentId === item.id)
          }
          return total
        }, [])
        //  var orgs = []
        //   response.items.forEach(item => {
        //     if (item.parentId === null) {
        //       var element = {
        //         id: item.id,
        //         label: item.name,
        //         children: []
        //       }
        //       orgs.push(element)
        //     }
        //   })
        //   response.items.forEach(allItem => {
        //     orgs.forEach(item => {
        //       if (item.id === allItem.parentId && allItem.parentId !== null) {
        //         item.children.push({
        //           id: allItem.id,
        //           label: allItem.name
        //         })
        //       }
        //     })
        //   })
        //   this.orgDatas = orgs
      })
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      classesDetail(this.listQuery).then((response) => {
        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    save() {
      if (this.form.isTop) {
        this.form.parentId = null
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            editClasses(this.form.id, this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
              this.getTreeDatas()
            })
              .catch(() => {
                this.formLoading = false
              })
          } else {
            addClasses(this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
              this.getTreeDatas()
            })
              .catch(() => {
                this.formLoading = false
              })
          }
        }
      })
    },
    handleCreate() {
      this.formTitle = '新增'
      this.isEdit = false
      this.dialogFormVisible = true
    },
    handleDelete(row) {
      this.$confirm('是否删除 ' + row.name + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteClasses(row.id).then((response) => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getTreeDatas()
        })
      })
    },
    handleUpdate(row) {
      this.formTitle = '修改'
      this.isEdit = true

      if (row) {
        this.form.id = row.id
        this.form.name = row.name
        this.form.isTop = true
        if (row.parentId !== null) {
          this.form.isTop = false
          this.nodesOrgs.forEach(item => {
            if (item.id === row.parentId) {
              this.form.parentId = row.parentId
            }
          })
        }

        this.dialogFormVisible = true
      } else {
        if (this.multipleSelection.length !== 1) {
          this.$message({
            message: '编辑必须选择单行',
            type: 'warning'
          })
          return
        } else {
          this.form.id = row.id
          this.form.name = row.name
          this.form.isTop = true
          if (row.parentId !== null) {
            this.form.isTop = false
            this.nodesOrgs.forEach(item => {
              if (item.id === row.parentId) {
                this.form.parentId = row.parentId
              }
            })
          }
          this.dialogFormVisible = true
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRowClick(row, column, event) {
      this.$refs.multipleTable.clearSelection()
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    cancel() {
      this.dialogFormVisible = false
      this.$refs.form.clearValidate()
      this.restForm()
    },
    handleNodeClick(data) {
      this.listQuery.ParentId = data.id
      this.getList()
    },
    restForm() {
      this.form = {
        id: null,
        name: null,
        parentId: null,
        isTop: true
      }
    }
  }
}
</script>

<html lang="en">
<!--<![endif]-->

<head>
  <meta charset="utf-8" />
  <title>编辑题目</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <meta http-equiv="Content-type" content="text/html; charset=utf-8">
  <link href="./base.css?v=3.2.0" rel="stylesheet" />
  <link href="./blue.css" rel="stylesheet" />
  <link href="./content.css?v=3.1.0" rel="stylesheet" />
  <link href="./iCheck/square/blue.css" rel="stylesheet" />
  <link href="./select2/select2.min.css" rel="stylesheet" />
  <link href="./editselect/jquery-editable-select.css" rel="stylesheet" />
  <link href="../baiduUpload/baiduUpload.css?v=3.1.0" rel="stylesheet" />
  <link href="../baiduUpload/box.css" rel="stylesheet" />
  <link href="./Edit.css?v=3.1.0" rel="stylesheet" />

  <script src="./jquery.min.js"></script>
  <script src="./iCheck/jquery.icheck.min.js"></script>
  <script src="./spark-md5.js"></script>
  <script src="../baidubce-sdk.bundle.min.js"></script>
  <script src="../baiduUpload/localUpload.js"></script>
  <script src="../baseAjaxRequest.js"></script>
  <script src="../portexc/codemirror2.js"></script>
  <script language="javascript" type="text/javascript">
    String.prototype.replaceAll = function (FindText, RepText) {
      regExp = new RegExp(FindText, "g");
      return this.replace(regExp, RepText);
    }

  </script>

</head>

<body>
  <div class="portlet">
    <div class="portlet-body">
      <div class="pagebox" id="pageContentId">
        <form id="exform" role="form" novalidate class="form-validation">
          <input type="hidden" id="bankids" value="@Model.Quesbankids" />
          <input type="hidden" id="userid" value="@Model.UserId" />

          <div class="home-desktop" id="desktop_scroll">
            <div>
              <div class="create-questions-content">
                <div class="exam-nav speical speical-radius">
                  <div class="exam-item">
                    <h4 class="exam-item-title">常用题型<i class="fa fa-chevron-down"></i></h4>
                    <ul class="exam-nav-list ui_sortable_exam" id="ui_sortable_exam">
                      <li data-uid="0" data-tempId="drag_choice">
                        <a href="javascript:;" data-checkType="0">
                          <img src="./img/exc_sg.png" class="sg_sg" /><span
                            style="margin-left:2px">单选题&nbsp;&nbsp;&nbsp;</span>
                        </a>
                      </li>
                      <li data-uid="1" data-tempId="drag_choice">
                        <a href="javascript:;" data-checkType="1">
                          <img src="./img/exc_ml.png" class="sg_sg" /><span
                            style="margin-left:2px">多选题&nbsp;&nbsp;&nbsp;</span>
                        </a>
                      </li>
                      <li data-uid="2" data-tempId="drag_choice">
                        <a href="javascript:;" data-checkType="2">
                          <img src="./img/exc_jg.png" class="sg_sg" /><span
                            style="margin-left:2px">判断题&nbsp;&nbsp;&nbsp;</span>
                        </a>
                      </li>
                      <li data-uid="2" data-tempId="drag_choice">
                        <a href="javascript:;" data-checkType="3">
                          <img src="./img/exc_blank.png" class="sg_sg" /><span
                            style="margin-left:2px">填空题&nbsp;&nbsp;&nbsp;</span>
                        </a>
                      </li>
                      <li data-uid="2" data-tempId="drag_choice">
                        <a href="javascript:;" data-checkType="6">
                          <img src="./img/exc_blank.png" class="sg_sg" /><span
                            style="margin-left:2px">简答题&nbsp;&nbsp;&nbsp;</span>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div id="imgTemplate" class="cbp-popup-wrap cbp-popup-lightbox cbp-popup-ready imgbox-overlay"
                  data-action="close" style="display: none;">
                  <div class="cbp-popup-content">
                    <div class="cbp-popup-lightbox-figure">
                      <div class="bac_over"></div>
                      <img src="" class="cbp-popup-lightbox-img" id="tempImg" data-action="next"
                        style="margin-top:60px;">
                    </div>
                  </div>
                  <div class="cbp-popup-loadingBox"></div>
                  <div class="cbp-popup-navigation-wrap">
                    <div class="cbp-popup-navigation">
                      <div class="cbp-popup-close" title="Close (Esc arrow key)" data-action="close"></div>
                    </div>
                  </div>
                </div>
                <!--出题-->
                <div class="create-questions">
                  <!-- <div class="speical speical-radius" style="margin-left:0px;">
                                        <div class="questions-head-title" data-tisp="名称最大长度为30个字">
                                            <h4 class="h4-bg T_edit T-center" data-Tid="10001" id="questionTitle" style="font-size: 18px;">@Model.Title</h4>
                                        </div>
                                        <div class="cq-title cq-title T_edit" style="display:none" data-Tid="10002" id="questionExamTitle">练习要点</div>
                                    </div>

                                    @*<div class="speical speical-radius" style="padding:20px;margin-left:0;margin-top:20px;font-size:16px;">
                                        课程：
                                        <select class="form-contorl" id="catagory">
                                            <option value="-1">请选择课程</option>
                                            @foreach (var item in Model.StructList_Par)
                                            {
                                                <option value="@item.Id" @(Model.StructId == item.Id ? "selected" : "")>@item.Title</option>
                                            }
                                        </select>
                                        <button type="button" class="cotrlBtn exam-save-btn btnBlue sv_btn" id="add_type" style="margin-left:15px">添加课程</button>
                                        <input type="hidden" id="catagory_val" value="@Model.StructId" />
                                    </div>*@ -->

                  <ul class="ui-questions-content-list" id="loadQuestions"></ul>
                  <ul class="ui-foot-all-list"></ul>
                </div>
              </div>
            </div>
          </div>
        </form>

        <div class="sv_btn_line" style="text-align:center">
          <button type="button" class="cotrlBtn exam-save-btn btnBlue sv_btn" data-type="1"
            id="saveQuestion">保存</button>
        </div>

        <!--题目模板-->
        <script type="text/html" id="drag_choice">
          <li class="ui-module items-questions speical speical-radius">
            <div class="theme-type">
              <div class="module-menu">
                <input type="hidden" class="questype" value="{{type}}" />
                {{if type == 5 || type == 4 }}
                {{else}}
                <h4></h4>
                {{/if}}
                <div class="module-ctrl">
                  <a href="javascript:void(0);" class="ui-up-btn" data-tisp="上移">
                    <img src="./img/exc_up.png" />
                  </a>
                  <a href="javascript:void(0);" class="ui-down-btn" data-tisp="下移">
                    <img src="./img/exc_down.png" />
                  </a>
                  <a href="javascript:void(0);" class="ui-del-btn" data-tisp="删除">
                    <img src="./img/exc_del.png" />
                  </a>
                </div>
              </div>
              <div class="ui-drag-area" style="min-width: 300px;">
                {{if type == 5}}
                <div class="btn-file1">
                  <div id="progess_img" style="display:inline-block"></div>
                  <div id="result_img" class="row imgResult imgadds"
                    style="background:#f1f1f1;border:1px dashed #e2e2e2;margin-left:0px;margin-right:0px;display: inline-block;">
                  </div>
                </div>
                <input class="cq-type" type="hidden" value="{{type}}" />
                <input class="cq-score" type="hidden" data-oval="0" value="0" />
                {{else}}
                <div class="">
                  <div class="drag_line1">
                    <div class="cq-title T_edit T_plugins ui-tip-title ques_head_title" data-tisp="按住可以拖动题目排序"
                      data-qtype="{{type}}" data-qstype="1" data-Tid="{{itmetid}}" data-kid="0"
                      style="font-size: 16px;">
                      {{if type==0}}单选题名称{{else if type==1}}多选题名称{{else if type==2}}判断题名称{{else if type==4}}文本{{else if type==3}}填空题名称{{else if type==6}}简答题名称{{/if}}
                    </div>
                  </div>
                  {{if type != 4 && type != 5 }}
                  <div class="drag_line2">
                    <span class="sg_score" data-Tid="{{itmetid}}">
                      <input class="cq-score" type="hidden" value="5" data-oval="5"
                        onkeyup="this.value=this.value.replace(/\D/g,'')"
                        onafterpaste="this.value=this.value.replace(/\D/g,'')" max="100" />

                      <input class="cq-type" type="hidden" value="{{type}}" />
                    </span>
                  </div>
                  {{else}}
                  <input class="cq-type" type="hidden" value="{{type}}" />
                  <input class="cq-score" type="hidden" data-oval="0" value="0" />
                  {{/if}}
                </div>
                {{/if}}
              </div>

              {{if type!=4 && type!=5 }}
              <div class="row ui-img-list" data-Tid="{{itmetid}}">
              </div>

              <div class="cq-items-content">
                {{if type!=3&& type != 6}}
                <ul class="cq-unset-list" data-checkType="{{type}}" data-nameStr="{{name}}" data-tid="{{itmetid}}">
                  {{each items as itemData i}}
                  <li>
                    {{if (taskType == 6 && dirId > 0) || (taskType == 4 && dirId <= 0) }}
                    <label class="input-check"><input disabled
                        type="{{if type==0}}radio{{else if type==1}}checkbox{{else if type==2}}radio{{/if}}"
                        name="{{name}}" value="{{itemData.value}}"></label>
                    {{else}}
                    <label class="input-check"><input
                        type="{{if type==0}}radio{{else if type==1}}checkbox{{else if type==2}}radio{{/if}}"
                        name="{{name}}" value="{{itemData.value}}"></label>
                    {{/if}}

                    {{if type==2}}

                    <div class="cq-answer-content T_plugins" data-qtype="{{type}}" data-Tid="{{itemData.tid}}">
                      {{if i==0}}正确{{else}}错误{{/if}}</div>

                    {{else}}

                    <div class="cq-answer-content T_edit T_plugins" data-qtype="{{type}}" data-Tid="{{itemData.tid}}">
                      选项{{i+1}}</div>

                    {{/if}}
                    <div class="row ui-img-option-list" data-qtype="{{type}}" data-Tid="{{itemData.tid}}">
                    </div>
                  </li>
                  {{/each}}
                </ul>
                {{/if}}
                <div class="cq-items-ctrl">
                  {{if type==2 || type== 4}}
                  {{else if type!=3 && type!= 6}}
                  <a href="javascript:void(0);" class="ui-add-item-btn" data-tisp="添加">
                    <img src="./img/exc_plus.png" class="sg_add" />
                  </a>
                  <a href="javascript:void(0);" class="ui-add-answer-btn icon_anysis" data-tisp="添加/取消答案解析22">
                    <img src="./img/exc_any.png" class="sg_anylisis" />
                  </a>
                  {{/if}}
                </div>
                <div class="cq-items-ctrl">
                  {{if type==3 || type== 6}}
                  <ul class="cq-unset-list" data-checkType="{{type}}" data-nameStr="{{name}}" data-tid="{{itmetid}}">
                    </ul>

                  <!-- <a href="javascript:void(0);" class="ui-add-item-btn" data-tisp="添加">
                    <img src="./img/exc_plus.png" class="sg_add" />
                  </a> -->
                  <a href="javascript:void(0);" class="ui-add-answer-btn icon_anysis" data-tisp="添加/取消答案解析">
                    <img src="./img/exc_any.png" class="sg_anylisis" />
                  </a>
                  {{/if}}
                </div>
                <div class="cq-items-ctrl configs">
                  难易程度：
                  <div class="config_line"><input type="radio" name="config_d_{{itmetid}}" class="config_diff"
                      value="1" /><span class="config_txt">易</span></div>
                  <div class="config_line"><input type="radio" name="config_d_{{itmetid}}" class="config_diff"
                      value="2" /><span class="config_txt">偏易</span></div>
                  <div class="config_line"><input type="radio" name="config_d_{{itmetid}}" class="config_diff"
                      value="3" /><span class="config_txt">适中</span></div>
                  <div class="config_line"><input type="radio" name="config_d_{{itmetid}}" class="config_diff"
                      value="4" /><span class="config_txt">偏难</span></div>
                  <div class="config_line"><input type="radio" name="config_d_{{itmetid}}" class="config_diff"
                      value="5" /><span class="config_txt">难</span></div>

                </div>

              </div>
              {{else}}
              {{/if}}

            </div>
          </li>

        </script>

        <!--题目加载模板-->
        <script id="loadQuesTemp" type="text/html">
          {{each questionLists as item i}}
          <li class="ui-module items-questions speical speical-radius" data-mid="{{item.quesId}}">
            <div class="theme-type">
              <div class="module-menu">
                <input type="hidden" class="questype" value="{{item.quesType}}" />
                {{if item.quesType!=4 && item.quesType!=5  && item.quesType!=8}}
                <h4>{{item.order}}</h4>
                {{/if}}
                <div class="module-ctrl">
                  <a href="javascript:void(0);" class="ui-up-btn" data-tisp="上移">
                    <img src="./img/exc_up.png" />
                  </a>
                  <a href="javascript:void(0);" class="ui-down-btn" data-tisp="下移">
                    <img src="./img/exc_down.png" />
                  </a>
                  <a href="javascript:void(0);" class="ui-del-btn" data-tisp="删除">
                    <img src="./img/exc_del.png" />
                  </a>
                </div>
              </div>
              <div class="ui-drag-area">
                <div class="">
                  <div class="drag_line1">
                    {{if item.quesType!=5}}
                    <div class="cq-title T_edit T_plugins ui-tip-title" data-tisp="按住可以拖动题目排序"
                      data-qtype="{{item.quesType}}" data-qstype="1" data-from="0" data-Tid="{{item.quesId}}"
                      style="font-size: 16px;">{{#item.quesTitle}}</div>
                    {{else}}
                    <div class="btn-file1">
                      <div id="progess_img" style="display:inline-block"></div>
                      <div id="result_img" class="row imgResult imgadds"
                        style="background:#f1f1f1;border:1px dashed #e2e2e2;margin-left:0px;margin-right:0px;display: inline-block;">
                        <div class="box">
                          <a href="{{item.imageUrls}}" class="imgsee"><img src="{{item.imageUrls}}"
                              class="img-thumbnail img-rounded img-responsive"
                              style="width: 160px; height: 100px;" /></a>
                          <div class="box-content">
                            <ul class="icon">
                              <li><a href="javascript:;" class="viewPic"><img src="./img/exc_see.png" class="icn_img"
                                    data-tisp="查看大图" /></a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    {{/if}}
                    <input class="cq-type" type="hidden" value="{{item.quesType}}" />
                    <input id="quesId" type="hidden" value="{{item.quesId}}" />
                  </div>
                </div>
              </div>

              {{if item.quesType!=4 && item.quesType!=5 && item.quesType!=8}}
              <div class="row ui-img-list" data-Tid="{{item.quesId}}">
                {{each item.viewImageList as src j}}
                <div class="col-md-3 box">
                  <a href="{{src}}" class="imgsee"><img src="{{src}}" class="img-thumbnail img-rounded img-responsive"
                      style="width: 160px; height: 100px;" /></a>
                  <div class="box-content">
                    <ul class="icon">
                      <li>
                        <a href="javascript:;" rel="nofollow" class="viewPic" data-tisp="查看大图">
                          <img src="./img/exc_see.png" class="icn_img" />
                        </a>
                      </li>
                      <li>
                        <a href="javascript:;" class="delPic">
                          <img src="./img/exc_delpic.png" class="icn_img" data-tisp="删除" />
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                {{/each}}
              </div>
              {{if item.quesType==0 || item.quesType==1 || item.quesType==2}}
              <div class="cq-items-content">
                <ul class="cq-unset-list" data-checkType="{{item.quesType}}" data-nameStr="{{item.quesId}}"
                  data-tid="{{item.quesId}}">
                  {{each item.quesOptions as option j}}
                  <li>
                    <label class="input-check">
                      <input type="{{if item.quesType == 1}}checkbox{{else}}radio{{/if}}"
                        {{if option.isAnswer}}checked{{/if}} name="{{item.quesId}}" value="{{option.optionId}}" />
                    </label>

                    {{if item.quesType == 2}}
                    <div class="cq-answer-content T_plugins" data-qtype="{{item.quesType}}"
                      data-Tid="{{option.optionId}}">{{option.optionTitle}}</div>
                    {{else}}
                    <div class="cq-answer-content T_edit T_plugins" data-qtype="{{item.quesType}}"
                      data-Tid="{{option.optionId}}">{{option.optionTitle}}</div>
                    {{/if}}

                    <div class="row ui-img-option-list" data-Tid="{{option.optionId}}">
                      {{each option.viewImageList as src k}}
                      <div class="col-md-3 box">
                        <a href="{{src}}" class="imgsee"><img src="{{src}}"
                            class="img-thumbnail img-rounded img-responsive" style="width: 160px; height: 100px;" /></a>
                        <div class="box-content">
                          <ul class="icon">
                            <li><a href="javascript:;" class="viewPic"><img src="./img/exc_see.png" class="icn_img"
                                  data-tisp="查看大图" /></a></li>
                            <li><a href="javascript:;" class="delPic"><img src="./img/exc_delpic.png" class="icn_img"
                                  data-tisp="删除" /></a></li>
                          </ul>
                        </div>
                      </div>
                      {{/each}}
                    </div>
                  </li>
                  {{/each}}
                </ul>
                {{if item.explain!=null && item.explain!=""}}
                <textarea class="exam-textarea analysis_contx" placeholder="请在此填写答案解析">{{item.explain}}</textarea>
                {{/if}}
                <div class="cq-items-ctrl">
                  {{if item.quesType != 2 && item.quesType != 4}}
                  <a href="javascript:void(0);" class="ui-add-item-btn" data-tisp="添加">
                    <img src="./img/exc_plus.png" class="sg_add" />
                  </a>
                  {{/if}}
                  {{if taskType == 4 && dirId <= 0}}
                  {{else}}
                  <a href="javascript:void(0);" class="ui-add-answer-btn icon_anysis" data-tisp="添加/取消答案解析">
                    <img src="./img/exc_any.png" class="sg_anylisis" />
                  </a>
                  {{/if}}
                </div>
              </div>
              {{else}}
              <div class="cq-items-content">
                <ul class="cq-unset-list" data-checkType="{{item.quesType}}" data-nameStr="{{item.quesTitle}}"
                  data-tid="{{item.quesId}}">
                  <li></li>
                </ul>

                {{if item.quesType==6}}
                {{if item.explain!=null && item.explain!=""}}
                <textarea class="exam-textarea analysis_contx" placeholder="请在此填写答案解析">{{item.explain}}</textarea>
                {{/if}}
                {{/if}}

                <div class="cq-items-ctrl" style="{{if item.quesType!=3&& item.quesType!=6}}height:200px;overflow-y:scroll{{/if}}">

                  {{if item.quesType==3}}
                  <!--  填空题不编辑答案 -->
                  <!-- <div class="con_blk">
                    <div class="con_blk1">
                      <button type="button" data-setid="{{item.quesId}}" id="setans_{{item.quesId}}"
                        data-questitle="填空题题目" class="ansset btn_blkans">编辑答案</button>
                    </div>
                    <div class="con_blk2 con_blk_txt_{{item.quesId}}">
                      {{if item.blkGradeType==0}}
                      {{each item.ptBlankQuesOptions as option1 s}}
                      <span class="span_blk">第 {{s+1}} 空：<input type="text" readonly class="txt_blkans"
                          value="{{option1.answer}}" />( {{option1.score.toFixed(1)}} 分)</span>
                      {{/each}}
                      {{/if}}
                    </div>
                    <div class="row ui-img-option-list" data-tid="{{item.quesId}}"></div>
                  </div> -->
                  {{if item.explain!=null && item.explain!=""}}
                  <textarea class="exam-textarea analysis_contx" placeholder="请在此填写答案解析">{{item.explain}}</textarea>
                  {{/if}}
                  {{/if}}

                  {{if item.quesType==7}}
                  <div class="con_tb">
                    <table class="tbset" border="1" cellspacing="0" contentEditable="true" data-id="{{item.quesId}}"
                      id="tbset_{{item.quesId}}" data-maxline="{{item.maxLine}}" data-tdline="{{item.maxLine}}">
                      {{each item.maxLines as line m}}
                      <tr class="trset">
                        {{each item.ptableQuesOptions as td n}}
                        {{if td.tdRow == line}}
                        <td class="tdset tdsets_{{line}}{{td.tdColumn}}" data-row="{{line}}" data-col="{{td.tdColumn}}"
                          rowspan="{{td.rowspan}}" colspan="{{td.colspan}}" data-ans="{{td.answer}}"
                          data-sc="{{td.score.toFixed(1)}}" data-any="">
                          <div style="height:50px;vertical-align: middle;line-height: 50px;" contentEditable="true">
                            {{if td.title!=null && td.title!=""}}{{td.title}}{{else}}答案：{{td.answer}}({{td.score}}
                            分){{/if}} </div>
                        </td>
                        {{/if}}
                        {{/each}}
                      </tr>
                      {{/each}}
                    </table>
                  </div>
                  {{if item.explain!=null && item.explain!=""}}
                  <textarea class="exam-textarea analysis_contx" placeholder="请在此填写答案解析">{{item.explain}}</textarea>
                  {{/if}}
                  {{/if}}

                  <a href="javascript:void(0);" class="ui-add-answer-btn icon_anysis" data-tisp="添加/取消答案解析">
                    <img src="./img/exc_any.png" class="sg_anylisis" />
                  </a>
                </div>
              </div>
              {{/if}}
              <div class="cq-items-ctrl configs">
                难易程度：
                <div class="config_line"><input type="radio" name="config_d_{{item.quesId}}" class="config_diff"
                    value="1" {{if item.difficulty ==1}}checked{{/if}} /><span class="config_txt">易</span></div>
                <div class="config_line"><input type="radio" name="config_d_{{item.quesId}}" class="config_diff"
                    value="2" {{if item.difficulty == 2}}checked{{/if}} /><span class="config_txt">偏易</span></div>
                <div class="config_line"><input type="radio" name="config_d_{{item.quesId}}" class="config_diff"
                    value="3" {{if item.difficulty == 3}}checked{{/if}} /><span class="config_txt">适中</span></div>
                <div class="config_line"><input type="radio" name="config_d_{{item.quesId}}" class="config_diff"
                    value="4" {{if item.difficulty == 4}}checked{{/if}} /><span class="config_txt">偏难</span></div>
                <div class="config_line"><input type="radio" name="config_d_{{item.quesId}}" class="config_diff"
                    value="5" {{if item.difficulty == 5}}checked{{/if}} /><span class="config_txt">难</span></div>
              </div>
              {{/if}}
            </div>
          </li>
          {{/each}}

        </script>

        <script type="text/html" id="drag_T_edit">
          <div class="cq-into-edit">
            <div class="add-edit cq-edit-title" contenteditable="true" style="word-wrap:break-word;font-size:16px;">
              {{title}}</div>
            <div id="progess"></div>
            <div id="result" class="row imgResult"
              style="background:#f1f1f1;border:1px dashed #e2e2e2;margin-left:0px;margin-right:0px;">
            </div>
          </div>

        </script>

        <script type="text/html" id="T_edit_plugins">
          {{if type ==2 && qstype!=1}}
          {{else}}
          <div class="edit-plug-box">
            {{if type != 4}}
            <span class="btn-file">
              <a href="javascript:void(0);">
                <input type="file" id="file" accept="image/*;capture = camera" class="imgupstyle" data-tisp="插入图片"
                  style="cursor:pointer;" />
                <img src="./img/exc_img.png" style="vertical-align:middle" /><span class="icon_alg_txt">插入图片</span>
              </a>
              <input type="hidden" id="upload" value="" />
            </span>
            {{else}}
            {{/if}}
            <span class="module-ctrl">
              <a href="javascript:void(0);" class="ui-op-up-btn" data-tisp="上移">
                <img src="./img/exc_up.png" style="vertical-align:middle" /><span class="icon_alg_txt">上移</span>
              </a>
              <a href="javascript:void(0);" class="ui-op-down-btn" data-tisp="下移">
                <img src="./img/exc_down.png" style="vertical-align:middle" /><span class="icon_alg_txt">下移</span>
              </a>
              <a href="javascript:void(0);" class="ui-op-del-btn" data-tisp="删除">
                <img src="./img/exc_del.png" style="vertical-align:middle" /><span class="icon_alg_txt"
                  style="color:red">删除</span>
              </a>
            </span>
          </div>
          {{/if}}

        </script>

        <script type="text/html" id="ui_additem_content">
          {{each items as itemData i}}
          <li>

            {{if (taskType == 6 && dirId > 0) || (taskType == 4 && dirId <= 0) }}
            <label class="input-check"><input disabled type="{{if type==0}}radio{{else if type==1}}checkbox{{/if}}"
                name="{{name}}" value="{{itemData.value}}"></label>
            {{else}}
            <label class="input-check"><input type="{{if type==0}}radio{{else if type==1}}checkbox{{/if}}"
                name="{{name}}" value="{{itemData.value}}"></label>
            {{/if}}

            <div class="cq-answer-content T_edit T_plugins" data-Tid="{{itemData.tid}}">选项{{i+1+index}}</div>
            <div class="row ui-img-option-list" data-Tid="{{itemData.tid}}">
            </div>
          </li>
          {{/each}}

        </script>

        <script type="text/html" id="analysis_tmp">
          <textarea class="exam-textarea analysis_contx" maxlength="500" placeholder="请在此填写答案解析"></textarea>

        </script>

        <script src="./jquery.min.js"></script>
        <script src="./jquery-ui.min.js"></script>
        <script src="./iCheck/jquery.icheck.min.js"></script>
        <script src="./layer.js?v=3.2.0"></script>
        <script src="./template.js"></script>
        <script src="./select2/select2.min.js"></script>
        <script src="./editselect/jquery-editable-select.js"></script>
        <script src="../baiduUpload/localUpload.js"></script>

        <script type="text/javascript">
          var catagorys = [];
          var exam = {
            init: function () {
              this.dragFn();
              this.sortFn();
              this.fixFn();
              this.menuFn();
              this.titleEditFn();
              this.listAllCtrlFn('.ui-questions-content-list', '.ui-up-btn', '.ui-down-btn', '.ui-clone-btn',
                '.ui-del-btn');
              this.topicACtrlFn('.ui-questions-content-list', '.ui-add-item-btn', '.ui-batch-item-btn',
                '.ui-add-answer-btn');
              this.moveTispFn(
                '.ui-up-btn,.ui-down-btn,.ui-clone-btn,.ui-del-btn,.ui-tip-title,.imgupstyle,.ui-op-up-btn,.ui-op-down-btn,.ui-op-del-btn,.questions-head-title'
              );
              this.moveTispFn('.ui-add-item-btn,.ui-batch-item-btn,.ui-add-answer-btn');

            },
            //拖拽
            dragFn: function () {
              var _this = this;
              var data = {},
                addname = 0;
              $(".ui_sortable_exam li").draggable({
                /* containment:'#pageContentId',*/
                connectToSortable: '.ui-questions-content-list',
                cursorAt: {
                  top: 18,
                  left: 20
                },
                helper: function (event) {
                  addname++;
                  var qtype = $(this).children('a').attr('data-checkType');
                  var opi = "op";
                  if (qtype == 2) {
                    options = [{
                      value: '0',
                      tid: addname + opi + parseInt(1000 * Math.random())
                    }, {
                      value: '0',
                      tid: addname + opi + parseInt(1000 * Math.random())
                    }];
                  } else {
                    options = [{
                      value: '0',
                      tid: addname + opi + parseInt(1000 * Math.random())
                    }, {
                      value: '0',
                      tid: addname + opi + parseInt(1000 * Math.random())
                    }, {
                      value: '0',
                      tid: addname + opi + parseInt(1000 * Math.random())
                    }, {
                      value: '0',
                      tid: addname + opi + parseInt(1000 * Math.random())
                    }];
                  }
                  data = {
                    type: qtype,
                    name: 'q' + $(this).attr('data-uid') + '_' + addname,
                    itmetid: addname + parseInt(1000 * Math.random()),
                    items: options,
                    taskType: $("#taskType").val(),
                    dirId: $("#dirid").val()
                  }
                  return template($(this).attr('data-tempId'), data);
                },
                revert: 'invalid',
                start: function (event) {
                  _this.titleDelFn();
                },
                drag: function (event) {},
                stop: function (event) {
                  _this.orderFn($('.ui-questions-content-list'));
                  LoadCheck();
                  CountScore();

                  ////图像图片上传
                  //JingGeBaiduUploadInit("upload_img", "file_img", "result_img", "progess_img");
                  $(".catagory_child").editableSelect({
                    effects: 'slide'
                  });

                  $(".es-list").empty();
                  for (var i = 0; i < catagorys.length; i++) {
                    $(".es-list").append("<li  class='es-visible'' value='" + catagorys[i].title + "'>" +
                      catagorys[i].title + "</li>");
                  }

                }
              }).on('click', function (e) {
                addname++;
                var qtype = $(this).children('a').attr('data-checkType');
                var opi = "op";
                if (qtype == 2) {
                  options = [{
                    value: '0',
                    tid: addname + opi + parseInt(1000 * Math.random())
                  }, {
                    value: '0',
                    tid: addname + opi + parseInt(1000 * Math.random())
                  }];
                } else {
                  options = [{
                    value: '0',
                    tid: addname + opi + parseInt(1000 * Math.random())
                  }, {
                    value: '0',
                    tid: addname + opi + parseInt(1000 * Math.random())
                  }, {
                    value: '0',
                    tid: addname + opi + parseInt(1000 * Math.random())
                  }, {
                    value: '0',
                    tid: addname + opi + parseInt(1000 * Math.random())
                  }];
                }
                data = {
                  type: qtype, //
                  name: 'q' + $(this).attr('data-uid') + '_' + addname,
                  itmetid: addname + parseInt(1000 * Math.random()),
                  items: options,
                  taskType: $("#taskType").val(),
                  dirId: $("#dirid").val()
                }

                if (data.type != 5 && data.type != "Image") {
                  $('.ui-questions-content-list').append(template($(this).attr('data-tempId'), data));
                  _this.orderFn($('.ui-questions-content-list'));
                  _this.sortFn();

                  LoadCheck();
                  CountScore();
                  scrollToEnd();
                }

                ////图像图片上传
                //JingGeBaiduUploadInit("upload_img", "file_img", "result_img", "progess_img");
                $(".catagory_child").editableSelect({
                  effects: 'slide'
                });

                $(".es-list").empty();
                for (var i = 0; i < catagorys.length; i++) {
                  $(".es-list").append("<li  class='es-visible'' value='" + catagorys[i].title + "'>" +
                    catagorys[i].title + "</li>");
                }

              }).disableSelection();
            },
            sortFn: function () {
              var _this = this;
              $('.ui-questions-content-list').sortable({
                handle: '.ui-drag-area',
                items: '>li',
                containment: '#pageContentId',
                opacity: 0.7,
                placeholder: 'ui-state-highlight',
                start: function (event) {
                  exam.titleDelFn();
                },
                stop: function () {
                  _this.orderFn($(this));
                },
                revert: 'invalid'
              });
            },
            orderFn: function (obj) {
              var count = 1;
              obj.find('li.items-questions').each(function (i) {
                $(this).removeAttr('style');
                var quesType = $(this).find('.module-menu .questype').val();
                if (quesType != 4 && quesType != 5) {
                  $(this).find('.module-menu h4').html(count);
                  count++;
                }
              });
            },
            fixFn: function () {
              var _this = this;
              $(window).scroll(function () {
                _this.titleDelFn();
                var parentLeft = $('.exam-nav').parent().offset().left;
                $('.exam-nav').css({
                  'position': 'fixed',
                  'top': 0 + 'px',
                  'left': parentLeft + 'px'
                });
                if ($('.exam-nav').offset().top + 20 + $('.conditionItems').outerHeight() + $('.title')
                  .outerHeight() <= $(this).scrollTop()) {
                  $('.exam-nav').css({
                    'position': 'fixed',
                    'top': 0 + 'px',
                    'left': parentLeft + 'px'
                  });
                  $('.exam-nav').addClass('scrollCurr');
                } else {
                  $('.exam-nav').removeAttr('style');
                  $('.exam-nav').removeClass('scrollCurr');
                }

              });
            },
            //题目类型菜单
            menuFn: function () {
              $('.exam-item-title').on('click', function () {
                if ($(this).hasClass('curr')) {
                  $(this).removeClass('curr');
                  $(this).find('i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
                  $(this).next('ul.exam-nav-list').stop().slideDown();
                } else {
                  $(this).addClass('curr');
                  $(this).find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
                  $(this).next('ul.exam-nav-list').stop().slideUp();
                }
              });
            },
            titleEditFn: function () {
              $(document).on('click', '.T_edit', function (event) {
                $('.cq-into-edit').remove();
                var data = {
                  title: ''
                }
                if (!$('.cq-into-edit').size()) {
                  $('body').append(template('drag_T_edit', data));
                  $('.cq-into-edit').attr('data-gettid', $(this).attr('data-tid'));
                }

                var d_type = {
                  type: $(this).attr('data-qtype'),
                  qstype: $(this).attr('data-qstype')
                }

                if ($(this).hasClass('T_plugins')) {
                  $('.cq-into-edit').append(template('T_edit_plugins', d_type));
                }

                //判断题选项不需要编辑
                $('.cq-into-edit').css({
                  'top': ($(this).offset().top - 1) + 'px',
                  'left': ($(this).offset().left) + 'px',
                  'width': $(this).outerWidth() + 'px',
                });
                if ($(this).hasClass('T-center')) {
                  $('.cq-into-edit .cq-edit-title').css({
                    'text-align': 'center'
                  });
                } else {
                  $('.cq-into-edit .cq-edit-title').css({
                    'text-align': 'left'
                  });
                }
                if ($(this).attr('data-font')) {
                  $('.cq-into-edit .cq-edit-title').css({
                    'font-size': $(this).attr('data-font') + 'px'
                  });
                } else {
                  $('.cq-into-edit .cq-edit-title').css({
                    'font-size': ''
                  });
                }
                $('.cq-into-edit .cq-edit-title').css({
                  'min-height': parseInt($(this).height()) - parseInt(10) + 'px',
                  'padding-top': ($(this).outerHeight() - $(this).height()) / 2 + 5 + 'px',
                  'padding-bottom': ($(this).outerHeight() - $(this).height()) / 2 + 'px'
                }).html($(this).html()).focus();

                $(document).one('click', function () {
                  $('.cq-into-edit').remove();
                });
                $(document).on('click', '.cq-into-edit', function (e) {
                  e.stopPropagation();
                });
                event.stopPropagation();

                //图片上传                      
                // JingGeBaiduUploadInit("upload", "file", "result", "progess");
                var jgLocalUpload = new LocalUpload();
                jgLocalUpload.init({
                  browse_button: "file", //作用的Dom对象ID
                  result: "result"
                });

              });
              //.cq-into-edit
              $(document).on('blur', '.cq-into-edit', function () {
                var content = $('.cq-into-edit .cq-edit-title').text();
                if ($.trim(content) == "") return;
                var res = $('.cq-into-edit .cq-edit-title').html();
                if (res.length > 250) {
                  layer.msg("输入内容长度超过250，会自动截取前250个字");
                  res = res.substring(0, 250);
                }
                $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').html(res);
                var $itemInput = $('.cq-answer-content[data-tid=' + $('.cq-into-edit').attr('data-gettid') +
                  ']').closest('li').find('.input-check').find('input');
                if ($itemInput.size()) {
                  $itemInput.val(res);
                }
              });
            },
            titleDelFn: function () {
              if ($('.cq-into-edit').size()) {
                $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').html($(
                  '.cq-into-edit .cq-edit-title').html());
                $('.cq-into-edit').hide();
              }
            },
            moveTispFn: function (obj) {
              $(document).on('mousemove', obj, function (e) {
                var strTx = $(this).attr('data-tisp');
                var str = $('<div class="move-tisp-box"></div>');
                str.html(strTx);
                if (!$('.move-tisp-box').size()) {
                  str.appendTo('body');
                }
                $('.move-tisp-box').css({
                  top: (e.pageY + 15) + 'px',
                  left: (e.pageX + 15) + 'px'
                });
              });
              $(document).on('mouseout', obj, function (e) {
                $('.move-tisp-box').remove();
              });
            },
            listAllCtrlFn: function (parentObj, upObj, downObj, cloneObj, delObj) {
              var _this = this;

              $(document).on('click', parentObj + ' ' + upObj, function (e) {
                var $parentItems = $(this).closest('li.ui-module');
                if ($parentItems.prev('li.ui-module').size()) {
                  $parentItems.fadeOut().fadeIn();
                  $parentItems.insertBefore($parentItems.prev('li.ui-module'));
                  _this.orderFn($(parentObj));
                  _this.titleDelFn();
                } else {
                  layer.msg('已经是顶部了');
                }
              });
              //
              $(document).on('click', parentObj + ' ' + downObj, function (e) {
                var $parentItems = $(this).closest('li.ui-module');
                if ($parentItems.next('li.ui-module').size()) {
                  $parentItems.fadeOut().fadeIn();
                  $parentItems.insertAfter($parentItems.next('li.ui-module'));
                  _this.orderFn($(parentObj));
                  _this.titleDelFn();
                } else {
                  layer.msg('已经是底部了');
                }
              });
              //复制
              $(document).on('click', parentObj + ' ' + cloneObj, function (e) {
                var $parentItems = $(this).closest('li.ui-module');
                $parentItems.clone(true).insertAfter($parentItems);
                _this.orderFn($(parentObj));
                _this.titleDelFn();

                CountScore();
              });
              //删除
              $(document).on('click', parentObj + ' ' + delObj, function (e) {
                var $parentItems = $(this).closest('li.ui-module');

                layer.confirm('确定删除该题目吗?', {
                  icon: 3,
                  title: '提示'
                }, function (index) {
                  $parentItems.remove();
                  layer.msg('操作成功');
                  $('.move-tisp-box').remove();
                  _this.orderFn($(parentObj));
                  _this.titleDelFn();
                  CountScore();

                  layer.close(index);
                });

              });
            },
            topicACtrlFn: function (parentObj, addObj, batchAddObj, addAnswerObj) {
              var $tid = 100 + parseInt(1000 * Math.random());
              $(document).on('click', parentObj + ' ' + addObj, function (e) {
                var $parentItems = $(this).closest('li.ui-module').find('.cq-unset-list');
                var $name = $.trim($parentItems.attr('data-nameStr'));
                $tid++;
                var data = {
                  type: parseInt($parentItems.attr('data-checktype')),
                  name: $name,
                  index: $parentItems.children('li:last').index() + 1,
                  items: [{
                    value: '0',
                    tid: $tid
                  }],
                  taskType: $("#taskType").val(),
                  dirId: $("#dirid").val()
                }
                $parentItems.append(template('ui_additem_content', data));

                LoadCheck();
                CountScore();
              });
              $(document).on('click', parentObj + ' ' + batchAddObj, function (e) {
                var $parentItems = $(this).closest('li.ui-module').find('.cq-unset-list');
                layer.msg('操作成功');
              });
              $(document).on('click', parentObj + ' ' + addAnswerObj, function (e) {
                $(this).closest('li').css({
                  'height': 'auto'
                });
                var $parentItems = $(this).closest('li.ui-module').find('.cq-unset-list');
                var $parentItems_blank = $(this).closest('li.ui-module').find('.describe-edit-content-blank');
                if (!$(this).closest('li.ui-module').find('.analysis_contx').size()) {
                  $parentItems.after(template('analysis_tmp', {}));
                  $parentItems_blank.after(template('analysis_tmp', {}));
                } else {
                  $(this).closest('li.ui-module').find('.analysis_contx').remove();
                }
              });
            },
          }
          $(document).ready(function () {
            exam.init();
            LoadQues();
            window.localStorage.setItem("isSaved", 0)
            $(".catagory_child").editableSelect({
              effects: 'slide'
            });
            //导航滚动固定
            istopscroll(".exam-nav");
          });

          function istopscroll(ee) {
            var divh = $(ee).offset().top;
            $(window).scroll(function () {
              var wsh = $(window).scrollTop();
              if (wsh >= 90) {
                $(ee).css({
                  "position": "fixed"
                });
                $('.exam-nav').addClass('scrollCurr');
              } else {
                $(ee).css({
                  "position": "absolute"
                });
                $('.exam-nav').addClass('scrollCurr');
              }
            });
          }
          //icheck
          function LoadCheck() {
            $('input').iCheck({
              labelHover: false,
              cursor: true,
              checkboxClass: 'icheckbox_square-blue',
              radioClass: 'iradio_square-blue',
              disabledRadioClass: '',
              increaseArea: '20%'
            });
          }
          //上移
          $("body").on('click', '.ui-op-up-btn', function () {
            var $itemli = $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li');
            if ($itemli.prev('li').size()) {
              $itemli.fadeOut().fadeIn();
              $itemli.prev().before($itemli);
              $(".cq-into-edit").hide();
              OrderCheck();
            } else {
              layer.msg('已经是顶部了');
            }
          });
          //下移
          $("body").on('click', '.ui-op-down-btn', function () {
            var $itemli = $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li');
            if ($itemli.next('li').size()) {
              $itemli.fadeOut().fadeIn();
              $itemli.next().after($itemli);
              $(".cq-into-edit").hide();
              OrderCheck();
            } else {
              layer.msg('已经是底部了');
            }
          });
          //删除
          $("body").on('click', '.ui-op-del-btn', function () {
            layer.confirm('确定删除吗?', {
              icon: 3,
              title: '提示'
            }, function (index) {

              var count = 0;
              $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li').parent().find(
                'li').each(function () {
                count++;
              })
              if (count <= 2) {
                layer.msg("题目至少需要两个选项");
                return;
              }

              var $itemli = $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li');
              $itemli.remove();
              $(".cq-into-edit").hide();
              OrderCheck();
              CountScore();

              layer.close(index);
            });

          });
          //排序调整
          function OrderCheck() {
            var count = 1;
            $("#loadQuestions").find('li.items-questions').each(function (i) {
              $(this).removeAttr('style');
              var quesType = $(this).find('.module-menu .questype').val();
              if (quesType != 4) {
                $(this).find('.module-menu h4').html(count);
                count++;
              }
            });
          }
          //删除图片
          $("body").on("click", ".delPic", function (event) {
            var $parentBox = $(this).closest(".box");
            $parentBox.remove();
            event.stopPropagation();
          });
          //删除图片
          $("body").on("click", ".delPic_img", function (event) {
            var $parentBox = $(this).closest(".box");
            $parentBox.remove();
            $('#file_img').show();
            event.stopPropagation();
          });
          //查看大图
          $("body").on("click", ".viewPic", function () {
            var $parentImg = $(this).closest(".box").find("img");
            var src = $parentImg.attr("src");
            var width = $parentImg.width;
            var height = $parentImg.height;
            if (width > 800) width = 800;
            if (height > 500) height = 500;
            layer.open({
              type: 1,
              area: [width + 'px', height + 'px'],
              title: false,
              closeBtn: true,
              shadeClose: true,
              skin: 'yourclass',
              content: '<img src="' + src +
                '" style="text-align:center;margin: 0 auto;max-height:800px;max-width:800px;" />'
            });
          });
          $("body").on("ifClicked", ".iradio_square-blue", function () {
            if ($(this).hasClass('checked')) {
              $(this).iCheck('uncheck');
            } else {
              $(this).iCheck('check');
            }
          });
          //问题列表
          function LoadQues() {
            var that = this;
            var msgid = layer.msg('题目加载中', {
              time: false //取消自动关闭
            });
            //加载初始数据
            var quesIds = localStorage.getItem('keystamp');
            $("#bankids").val(quesIds);
            //分批查询
            var key_arr = quesIds.split(',');
            var key_group = [];
            for (var i = 0; i < key_arr.length; i++) {
              var j = parseInt(i / 50);
              if (key_group[j] == undefined) {
                key_group[j] = '';
              }
              key_group[j] = key_group[j] + key_arr[i] + ',';
            }

            var _questions = {
              questionLists: []
            };
            var _index=1;
            for (var i = 0; i < key_group.length; i++) {
              xhrGet('/api/exams/questionBankTenant/edit', {
                quesIds: key_group[i]
              }, function (res) {
                res.questionLists.forEach(element => {
                  element.order=_index;
                  _questions.questionLists.push(element)
                  _index++;
                });
              }, function (res) {}, false)
            }
            $("#loadQuestions").append(template('loadQuesTemp', _questions));
            //配置
            that.LoadCheck();
            // JingGeBaiduUploadInit("upload", "file", "result", "progess");
            var jgLocalUpload = new LocalUpload();
            jgLocalUpload.init({
              browse_button: "file", //作用的Dom对象ID
              result: "result"
            });

            var jgLocalUpload2 = new LocalUpload();
            jgLocalUpload2.init({
              browse_button: "file_img", //作用的Dom对象ID
              result: "result_img"
            });
            layer.close(msgid);
            window.top.addEventListener('importSubmit', (e) => {
              that.loadImportQuestion(e.detail)
            })

          }
          $("body").on("blur", ".cq-score", function () {
            var oldVal = ($(this).attr("data-oval")); //获取原值
            var newVal = ($(this).val()); //获取当前值
            if (oldVal != newVal) {
              if (parseInt($(this).val()) >= 0) {
                CountScore();
              } else {
                layer.msg("请输入正确的数值");
                $(this).val(0);
                CountScore();
              }
            }
          });
          //分数统计
          function CountScore() {
            var result = 0;
            $(".cq-score").each(function () {
              result = result + parseInt($(this).val());
            });
            $("#TotalScore").val(result);
            $("#ScoreInfo").val(result);
          }
          //保存
          $('#saveQuestion').click(function () {
            // debugger                        
            var btnType = $(this).data("type");

            var score = 0;
            var optionRes = 0;
            var diff = 0; //判断是否题目难度都填了
            var flag = true; // 填空题，题干需要有下划线
            var dataBase = {},
              questionItems = [],
              examTitle = "";
            dataBase.questionBankCategoryId = localStorage.getItem('categoryId')
            //封装所有题列表，遍历提取值analysis（答案）、题列表（数组对象）；
            $('.ui-questions-content-list').children('li').each(function (i) {
              //debugger    
              var dataTx = {},
                qListItems = [];

              dataTx.QItemsTitle = $(this).find('.cq-title').text();
              dataTx.QItemsTitle = dataTx.QItemsTitle.replaceAll('"', '“');
              dataTx.QItemsTitle = dataTx.QItemsTitle.replaceAll("'", "‘");
              dataTx.QItemsType = $(this).find('.cq-type').val();
              dataTx.KeyId = $(this).find('#quesId').val();

              if(dataTx.QItemsType == 3 ){ //填空题 需要下划线
                var reg = /_{2,}/g
                flag  =  reg.test(dataTx.QItemsTitle) 
              }
              //难易程度
              dataTx.Difficulty = $(this).find('.config_diff:checked').val();
              if (dataTx.Difficulty == null || dataTx.Difficulty == undefined) {
                diff = 1;
              }
              var qImgUrls = "";
              if (dataTx.QItemsType == 5 || dataTx.QItemsType == "Image") {
                $(this).find(".img-thumbnail").each(function () {
                  qImgUrls += $(this).attr("src");
                });
                dataTx.QImgSrc = qImgUrls;
                dataTx.QItemsTitle = "image";
              } else {
                $(this).find('.ui-img-list').find(".img-thumbnail").each(function () {
                  qImgUrls += $(this).attr("src") + ",";
                });
                if (qImgUrls !== "") {
                  qImgUrls = qImgUrls.substring(0, qImgUrls.length - 1);
                }
                dataTx.QImgSrc = qImgUrls;
              }
              score = parseInt(score) + parseInt(dataTx.QItemsScore);

              //封装单题，遍历提取值name、value、checkCurr（选中状态）；
              var optionItem = 0;
              $(this).find('ul.cq-unset-list').children('li').each(function (j) {
                var listItems = {};
                listItems.name = $(this).find('input').attr('name');
                listItems.value = $(this).find('.cq-answer-content').text();
                listItems.checkCurr = $(this).find('input').prop('checked');

                listItems.value = listItems.value.replaceAll('"', '“');
                listItems.value = listItems.value.replaceAll("'", "‘");

                if (listItems.checkCurr) {
                  optionItem = 1;
                }

                var opImgUrls = "";
                $(this).find('.ui-img-option-list').find(".img-thumbnail").each(function () {
                  opImgUrls += $(this).attr("src") + ",";
                });
                if (opImgUrls !== "") {
                  opImgUrls = opImgUrls.substring(0, opImgUrls.length - 1);
                }
                listItems.OptionImgSrc = opImgUrls;
                qListItems.push(listItems);
              });

              //问卷调查不需要判断，文本类型不需要 ,填空题和简答题没有答案
              if (($("#taskType").val() == 4 && $("#dirid").val() == 0) ||
                (dataTx.QItemsType == "Text" || dataTx.QItemsType == 4 || dataTx.QItemsType == 3|| dataTx.QItemsType == 6|| dataTx.QItemsType == "Image" || dataTx
                  .QItemsType == 5)) {
                optionItem = 1;
              }
              
              if (optionItem === 0) {
                optionRes = 1;
                $("html,body").animate({
                  scrollTop: $(this).find('ul.cq-unset-list').offset().top - 140
                }, 1500);
                return false;
              }

              dataTx.analysis = $(this).find('.analysis_contx').val() || "";
              if (dataTx.analysis.length > 500) {
                dataTx.analysis = dataTx.analysis.substring(0, 500);
              }
              if (dataTx.analysis.length > 0) {
                dataTx.analysis = dataTx.analysis.replaceAll('"', '“');
                dataTx.analysis = dataTx.analysis.replaceAll("'", "‘");
              }
              dataTx.qListItems = qListItems;
              questionItems.push(dataTx);
            });

            if (optionRes === 1) {
              layer.msg("请将问题选项或正确选项补充完整");
              return;
            }
            if (diff == 1) {
              layer.msg("请将问题的难易程度补充完整");
              return;
            }
            if(!flag){
              layer.msg("填空题需要两个以上的连续的下划线");
              return;
            }
            dataBase.questionItems = questionItems;

            var formData = dataBase;
            layer.msg("题目创建中,请耐心等待");
            var layerload = layer.load(1, {
              shade: [0.6, '#393D49']
            });

            xhrPost('/api/exams/questionBankTenant/save', JSON.stringify(formData), function (data) {
              layer.close(layerload);
              layer.msg("保存成功");
              // 题目保存成功 通知vue关闭dialog
              window.top.dispatchEvent(new CustomEvent('bankSaveSuccess', {
                detail: 'success'
              }))
            }, function () {
              layer.close(layerload);
              layer.msg("保存失败");
            })

            //  $.ajax({
            //      url: '/api/exams/questionBankTenant/save',
            //      data: formData,
            //      type: 'POST'
            //  }).success(function (data) {
            //      layer.close(layerload);
            //      if (data.result !="") {
            //          window.localStorage.setItem("isSaved",1)
            //          layer.msg("保存成功");
            //          $("#bankids").val(data.result);
            //          LoadQues();

            //          if (btnType == 1) {
            //              $('.return_a', parent.document).click();
            //              googleBrowser.CloseForm(); return;
            //          }
            //      }
            //  }).error(function () {
            //      layer.close(layerload);
            //      layer.msg("创建失败");
            //  })

          });

          function scrollToEnd() { //滚动到底部
            var h = $(document).height() - $(window).height();
            //$(document).scrollTop(h);
            $("html,body").animate({
              scrollTop: h + 'px'
            }, 1000);
          };

        </script>

      </div>
    </div>
  </div>
</body>

</html>

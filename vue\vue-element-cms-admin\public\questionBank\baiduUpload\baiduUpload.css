﻿ .empty,
    .f-status {
        text-align: center;
    }

    .f-progress {
        text-align: right;
    }

    .btn-file {
        position: relative;
        overflow: hidden;
    }

        .btn-file input[type=file] {
             position: absolute;
    top: -2px;
    right: 10px;
    /* font-size: 100px; */
    text-align: right;
    filter: alpha(opacity=0);
    opacity: 0;
    outline: none;
    background: white;
    cursor: inherit;
    display: block;
    margin-right: 2px;
    width: 80px;
        }

         .btn-file1 input[type=file] {
            position: absolute;
            top: 0;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            background: white;
            cursor: inherit;
            display: block;
            margin-right:6px;
        }

    table th,
    .f-progress,
    .f-size {
        white-space: nowrap;
    }

    .f-name .alert-danger {
        padding: 0 5px;
        margin: 0;
    }

    tr.ignored {
        text-decoration: line-through;
        color: gray;
    }

    .table > tbody > tr > td.f-progress {
        vertical-align: middle;
    }

    .progress {
        margin-bottom: 0;
        height: 10px;
    }

    .progress-bar {
        -webkit-transition: none;
        transition: none;
    }

    .logo {
        padding: 10px 0;
        margin: 20px 0;
        background: url(../../App/Main/views/resources/resources/logo2x.png) left center no-repeat;
        background-size: 210px;
        padding-left: 220px;
    }

        .logo h2 {
            margin: 5px 0;
            font-size: 24px;
        }

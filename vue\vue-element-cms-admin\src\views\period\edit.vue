<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="header_flex_box">
        <span>{{ pageTitle }}</span>
      </div>
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="基础信息" name="courseInfo">
          <el-form ref="form" :model="form" :rules="formRules" label-width="100px">
            <el-form-item label="封面">
              <lz-upload-images ref="previewFile" :limit="1" :file-size="500" :file-type="['jpg', 'png', 'jpeg']"
                :source-list="previewFileList" @response-fn="handleImageResponse"
                @remove-upload="handleRemoveUploadImage" />
            </el-form-item>
            <el-form-item label="课程名称" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="课程类型" prop="creditHourType">
              <el-radio-group v-model="form.creditHourType" :disabled="isEdit">
                <el-radio :label="0">面授课程</el-radio>
                <el-radio :label="1">网络课程</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="form.startTime" type="datetime" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" placeholder="选择日期时间" />
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="form.endTime" type="datetime" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" placeholder="选择日期时间" />
            </el-form-item>
            <el-form-item label="课程介绍" prop="introduce">
              <el-input v-model="form.introduce" type="textarea" :rows="2" />
            </el-form-item>
            <el-form-item label="用户规模" prop="userCount">
              <el-input-number v-model="form.userCount" :step="1" step-strictly :min="0" />
            </el-form-item>
            <el-form-item label="时长" prop="timeLong">
              <el-input-number v-model="form.timeLong" :step="1" step-strictly :min="0">分钟</el-input-number>
            </el-form-item>
            <el-form-item label="课时" prop="classHour">
              <el-input-number v-model="form.classHour" :step="0.1" step-strictly :min="0" />
            </el-form-item>
            <el-form-item label="讲师" prop="lecturer">
              <el-input v-model="form.lecturer" />
            </el-form-item>
            <el-form-item>
              <el-button :loading="formLoading" round type="primary" @click="handleSaveSure">保 存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="用户管理" name="courseUser">
          <div class="header_flex_box">
            <el-input v-model="selectUsersListQuery.Filter" size="small" class="small_input" clearable
              placeholder="输入名称搜索" />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshSelectUserList">搜索
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectUser">选择学员</el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">按班级选择学员
            </el-button>
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleUserDelete">批量删除</el-button>
            <!-- <export-excel :header="['序号', '姓名', '用户名', '部门', '角色']"
              :filter-val="['userNumber', 'name', 'userName', 'className', 'userRole']" :query="{'StreamId': $route.query.id}" :field="{ 4: ['学生', '助教'] }"
              :api-fn="UserList" /> -->
          </div>
          <el-table v-loading="selectUsersListLoading" :data="selectUsersList" max-height="700px" size="small"
            @sort-change="handleSelectUserSortChange" @selection-change="handleDeleteUserChange">
            <el-table-column type="selection" align="center" />
            <el-table-column label="用户名" prop="userName" sortable="userName" />
            <el-table-column label="姓名" prop="userTrueName" sortable="userTrueName" />
            <el-table-column label="部门" prop="className" sortable="className" />
            <el-table-column label="学时" prop="classHour" sortable="classHour" />
          </el-table>
          <pagination v-show="selectUsersListQuery.totalCount > 0" :total="selectUsersListQuery.totalCount"
            :page.sync="selectUsersListQuery.page" :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :limit.sync="selectUsersListQuery.MaxResultCount" @pagination="getUserList" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-dialog title="选择用户" :visible.sync="selectUserDialog" :close-on-click-modal="false" width="1000px">
      <el-form>
        <el-form-item label="导入学时">
          <el-input-number v-model="importClassHour" size="small" :step="0.1" step-strictly :min="0" />
        </el-form-item>
      </el-form>
      <select-user v-if="selectUserDialog" @response-fn="handleSelectUserResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="selectUserDialogLoading" round type="primary" @click="handleSelectUserSure">确 定</el-button>
        <el-button round @click="selectUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <el-form>
        <el-form-item label="导入学时">
          <el-input-number v-model="importClassHour" size="small" :step="0.1" step-strictly :min="0" />
        </el-form-item>
      </el-form>
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import { addOffLineCourse, classesUsers, deleteOffLineCourseUser, editOffLineCourse, offLineCourseDetail, offLineCourseUserList } from '@/api/user'
import LzUploadImages from '@/components/LzUploadImages'
import SelectUser from '@/components/ChooseUser/select.vue'
import ChooseClass from '@/components/ChooseClass'
import { importOffLineCourseHour } from '@/api/train'
import { parseTimeDate } from '@/utils'
export default {
  name: 'Period',
  directives: { permission },
  components: {
    Pagination,
    LzUploadImages,
    SelectUser,
    ChooseClass
  },
  data() {
    return {
      pageTitle: this.$route.query.id ? this.$route.query.name : '添加',
      isEdit: !!this.$route.query.id,
      previewFileList: [],
      form: {
        coverUrl: '',
        name: '',
        introduce: '',
        lecturer: '',
        classHour: '',
        startTime: '',
        endTime: '',
        timeLong: 0,
        userCount: 0,
        creditHourType: 0
      },
      formRules: {
        name: [
          { required: true, message: '请输入课程名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度应小于 50 个字符', trigger: 'blur' }
        ]
      },
      formLoading: false,

      activeTabName: 'courseInfo',

      selectUserDialog: false,
      selectUserDialogLoading: false,
      currentSelectUsers: [],
      selectUsersList: [],
      selectUsersListLoading: false,
      selectUsersListQuery: {
        Filter: '',
        OfflineCourseId: this.$route.query.id,
        TrainId: '',
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      currentDeleteUsers: [],

      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],

      importClassHour: 0
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getDetail()
      this.getUserList()
    }
  },
  methods: {
    // 上传文件成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.form.coverUrl = url
    },
    // 上传文件删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.form.coverUrl = ''
    },
    handleTabClick(val) {

    },
    handleSaveSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            editOffLineCourse(this.form.id, this.form).then(res => {
              this.formLoading = false
              this.$message.success('编辑成功')
            }).catch(() => {
              this.formLoading = false
              this.$message.error('编辑失败')
            })
          } else {
            addOffLineCourse(this.form).then(res => {
              this.formLoading = false
              this.$message.success('添加成功')
              this.$router.go(-1)
            })
          }
        } else {
          return false
        }
      })
    },

    // 选择用户
    handleSelectUser(t) {
      this.currentSelectUsers = []
      this.importClassHour = this.form.classHour
      this.selectUserDialog = true
    },
    handleSelectUserResponse(val) {
      this.currentSelectUsers = val
    },
    async handleSelectUserSure() {
      if (this.currentSelectUsers.length) {
        this.selectUserDialogLoading = true
        var formArr = []
        this.currentSelectUsers.forEach(item => {
          formArr.push({
            offlineCourseId: this.$route.query.id,
            id: item.id,
            userId: item.id,
            userName: item.userName,
            userTrueName: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            classHour: this.importClassHour
          })
        })
        for await (var i of formArr) {
          await importOffLineCourseHour(i).then(res => {

          }).catch(() => {

          })
        }
        // importOffLineCourseHour(form).then(res => {
        this.$message.success('添加成功')
        this.selectUserDialogLoading = false
        this.selectUserDialog = false
        this.getUserList()
        // }).catch(() => {
        //   this.selectUserDialogLoading = false
        //   this.$message.error('添加失败')
        // })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleSelectClass() {
      this.selectedClass = []
      this.importClassHour = this.form.classHour
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var formArr = []
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        res.items.forEach(item => {
          formArr.push({
            offlineCourseId: this.$route.query.id,
            id: item.id,
            userId: item.id,
            userName: item.userName,
            userTrueName: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            classHour: this.importClassHour
          })
        })
      }
      var setA = new Set()
      formArr = formArr.filter(item => {
        const result = setA.has(item.userId)
        setA.add(item.id)
        return !result
      })
      for await (var i of formArr) {
        await importOffLineCourseHour(i).then(res => {

        }).catch(() => {

        })
      }
      // importOffLineCourseHour(form).then(res => {
      this.$message.success('添加成功')
      this.classLoading = false
      this.chooseClassDialog = false
      this.getUserList()
      // }).catch(() => {
      //   this.classLoading = false
      //   this.$message.error('添加失败')
      // })
    },
    handleDeleteUserChange(val) {
      this.currentDeleteUsers = val
    },
    handleUserDelete() {
      if (this.currentDeleteUsers.length) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          var formArr = []
          this.currentDeleteUsers.forEach(item => {
            formArr.push(item.id)
          })
          for await (var i of formArr) {
            await deleteOffLineCourseUser(i).then(res => {

            }).catch(() => {

            })
          }
          this.$message.success('删除成功')
          this.currentDeleteUsers = []
          this.getUserList()
          // deleteLiveUsers(form).then(res => {
          //   this.$message.success('删除成功')
          //   this.currentDeleteUsers = []
          //   this.getUserList()
          // }).catch(() => {
          //   this.$message.error('删除失败')
          // })
        }).catch(() => {
          this.$message.info('已取消删除')
        })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleSelectUserSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getUserList()
        return
      }
      this.selectUsersListQuery.Sorting = prop + ' ' + order
      this.getUserList()
    },
    handleRefreshSelectUserList() {
      this.selectUsersListQuery.page = 1
      this.getUserList()
    },
    getUserList() {
      this.selectUsersListLoading = true
      this.selectUsersListQuery.SkipCount = (this.selectUsersListQuery.page - 1) * this.selectUsersListQuery.MaxResultCount
      offLineCourseUserList(this.selectUsersListQuery).then(res => {
        this.selectUsersList = res.items
        this.selectUsersListQuery.totalCount = res.totalCount
        this.selectUsersListLoading = false
      })
    },
    getDetail() {
      offLineCourseDetail(this.$route.query.id).then(res => {
        this.form.id = res.id
        this.form.classHour = res.classHour
        this.form.coverUrl = res.coverUrl
        this.form.creditHourType = res.creditHourType
        this.form.startTime = parseTimeDate(res.startTime)
        this.form.lecturer = res.lecturer
        this.form.name = res.name
        this.form.timeLong = res.timeLong
        this.form.introduce = res.introduce
        this.form.endTime = parseTimeDate(res.endTime)
        this.form.userCount = res.userCount
        if (this.form.coverUrl) {
          this.previewFileList = []
          this.previewFileList.push({
            url: this.form.coverUrl
          })
        }
      })
    }
  }
}
</script>

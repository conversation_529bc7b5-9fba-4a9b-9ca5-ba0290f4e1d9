<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-23 09:19:50
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-07 15:32:17
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/user/examList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <export-excel
      :header="['考核名称', '开始时间', '结束时间', '总分', '得分', '提交次数']"
      :filter-val="['name', 'startDate','endDate', 'totalScore', 'lastScore', 'submitTimes']"
      :field="{ 1: [2], 2: [2], 3: ['分'], 4: ['分'], 5: ['次'] }"
      :api-fn="trainsUserExamList"
      :paging="false"
    />
    <el-table v-loading="listLoading" :data="list" size="small" highlight-current-row>
      <el-table-column label="考核名称" prop="name" />
      <el-table-column label="开始时间" prop="startDate" width="180">
        <template slot-scope="{row}">
          <span>{{ row.startDate | formatDateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" prop="endDate" width="180">
        <template slot-scope="{row}">
          <span>{{ row.endDate | formatDateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总分" prop="totalScore" width="80">
        <template slot-scope="{row}">
          <span>{{ row.totalScore }} 分</span>
        </template>
      </el-table-column>
      <el-table-column label="得分" prop="lastScore" width="80">
        <template slot-scope="{row}">
          <span>{{ row.lastScore }} 分</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="课程资源总数" prop="courseResCount" /> -->
      <el-table-column label="提交次数" prop="submitTimes" width="80">
        <template slot-scope="{row}">
          <span>{{ row.submitTimes }} 次</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="{row}">
          <el-button :disabled="!row.submitTimes" type="primary" round size="mini" icon="el-icon-view" @click="handleViewExamAchieveDetail(false,row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 考核点击 答题详情 -->
    <el-dialog v-if="examResultDialog" title="考试详情" append-to-body :visible.sync="examResultDialog" top="5vh" width="1200px">
      <train-exam-result :is-exam="false" :user-info="userInfo" :data="examData" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="examResultDialog = false">关  闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsUserExamList } from '@/api/train'
import TrainExamResult from './exam'
export default {
  name: 'TrainExamList',
  components: {
    TrainExamResult
  },
  props: {
    userInfo: {
      reuqerd: true,
      type: Object,
      default: () => {
        return {
          userId: '',
          userName: '',
          name: '',
          className: ''
        }
      }
    },
    trainId: {
      reuqerd: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,

      examResultDialog: false,
      examData: null
    }
  },
  created() {
    this.getExamAchieve({ trainId: this.trainId, userId: this.userInfo.userId })
  },
  methods: {
    trainsUserExamList() {
      return trainsUserExamList({ trainId: this.trainId, userId: this.userInfo.userId })
    },
    handleViewExamAchieveDetail(t, row) {
      this.examData = row
      this.examResultDialog = true
    },
    getExamAchieve(data) {
      this.listLoading = true
      trainsUserExamList(data).then(res => {
        this.list = res.items
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>

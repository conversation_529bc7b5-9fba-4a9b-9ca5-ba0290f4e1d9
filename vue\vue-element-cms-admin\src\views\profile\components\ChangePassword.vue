<template>
  <el-form ref="user" :rules="passwordRules" :model="user" label-position="top">
    <el-form-item label="旧密码" prop="currentPassword">
      <el-input v-model="user.currentPassword" />
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword">
      <el-input v-model="user.newPassword" />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :loading="formLoading"
        @click="submit"
      >修改密码</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      user: {
        currentPassword: '',
        newPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' },
          {
            min: 6,
            max: 20,
            message: '长度在 6 到 20 个字符',
            trigger: 'blur'
          }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      formLoading: false
    }
  },
  methods: {
    submit() {
      this.$refs['user'].validate((valid) => {
        if (valid) {
          this.formLoading = true
          this.$axios
            .posts('/api/appuser/my-profile/change-password', this.user)
            .then(Response => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
            })
            .catch(() => { this.formLoading = false })
        }
      })
    }
  }
}
</script>

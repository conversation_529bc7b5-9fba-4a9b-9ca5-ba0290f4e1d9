
import axios from '@/axios'
// --------------------------------------------------------------------
// 课程列表
export function courseList(data) {
  return axios.gets('/api/cms/course', data)
}
// 景格课程列表
export function jgCourseList(data) {
  return axios.gets('/api/cms/course/auth/tenant/get-courses', data)
}
// 添加课程
/**
 *
 * @param {
"tenantId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 "number": "string",
 "name": "string",
 "coverUrl": "string",
 "version": "string",
 "courseCategoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 "lecturer": "string",
 "classHour": 0,
 "tag": "string",
 "introduce": "string",
 "status": 0,
 "resourceDuration": 0,
 "videoDuration": 0
 } data
 * @returns
 */
export function courseAdd(data) {
  return axios.posts('/api/cms/course', data)
}

// 课程详情
export function courseDetail(id) {
  return axios.gets(`/api/cms/course/${id}`)
}
// 课程时长
export function courseDuration(id) {
  return axios.gets(`/api/cms/course/get-resource-duration/${id}`)
}
// 景格课程详情
export function jgCourseDetail(id) {
  return axios.gets(`/api/cms/public/courses/${id}`)
}
// 修改课程
export function courseEdit(id, data) {
  return axios.puts(`/api/cms/course/${id}`, data)
}
// 删除课程
export function courseDelete(id) {
  return axios.deletes(`/api/cms/course/${id}`)
}

// 课程上架
export function coursePublish(id, status) {
  return axios.puts(`/api/cms/course/${id}/publish?status=${status}`)
}
// -----------------------------------------------------------------------------------------
// 课程分类列表
export function courseCategoryList(data) {
  return axios.gets('/api/cms/course/category', data)
}
// 景格课程分类
export function jgCourseCategoryList() {
  return axios.gets('/api/cms/public/courses/category')
}
// 获取授权课程资源播放地址
export function getCourseResourceUrl(id) {
  return axios.gets('api/cms/course/auth/tenant/get-resource-url?id=' + id)
}

// 课程分类添加
/**
 *
 * @param {
 * "name": "string",
 * "sort": 0
 *} data
 * @returns
 */
export function courseCategoryAdd(data) {
  return axios.posts('/api/cms/course/category', data)
}
// 课程分类详情
export function courseCategoryDetail(id) {
  return axios.gets(`/api/cms/course/category/${id}`)
}
// 课程分类修改
export function courseCategoryEdit(id, data) {
  return axios.puts(`/api/cms/course/category/${id}`, data)
}
// 课程分类删除
export function courseCategoryDelete(id) {
  return axios.deletes(`/api/cms/course/category/${id}`)
}
// -----------------------------------------------------------------------------------------
// 课程目录
/**
 *
 * @param {
 * courseId:
 * parentId:
 * } data
 * @returns
 */
export function courseDirectoryList(data) {
  return axios.gets(`/api/cms/course/directory/parent`, data)
}
// 所有课程目录
export function courseDirectoryAllList(id) {
  return axios.gets(`/api/cms/course/directory/all?courseId=${id}`)
}

export function courseDirectoryDetail(id) {
  return axios.gets(`/api/cms/course/directory/${id}`)
}

export function courseDirectoryEdit(id, data) {
  return axios.puts(`/api/cms/course/directory/${id}`, data)
}
export function courseDirectoryDelete(id) {
  return axios.deletes(`/api/cms/course/directory/${id}`)
}
// 目录拖动排序
export function courseDirectorySort(id, data) {
  return axios.puts(`/api/cms/course/directory/${id}/dirSort`, data)
}
// 增加节点
/**
 *
 * @param {
 *"courseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *"parentId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *"name": "string",
 *"sort": 0,
 *"code": "string"
 *} data
 * @returns
 */
export function courseDirectoryAdd(data) {
  return axios.posts(`/api/cms/course/directory`, data)
}

// -----------------------------------------------------------------------------------------
// 课程资源列表
export function courseResourceList(data) {
  return axios.gets(`/api/cms/course/resource`, data)
}
// 课程资源添加
export function courseResourceAdd(data) {
  return axios.posts(`/api/cms/course/resource`, data)
}
// 课程资源批量导入
export function courseResourceBatchAdd(data) {
  return axios.posts(`/api/cms/course/resource/batch`, data)
}
// 课程资源详情
export function courseResourceDetail(id) {
  return axios.gets(`/api/cms/course/resource/${id}`)
}
export function getCourseResourceHtmlUrl(id) {
  return axios.gets('api/cms/course/resource/get-html-url?url=' + id)
}
// 修改课程资源
export function courseResourceEdit(id, data) {
  return axios.puts(`/api/cms/course/resource/${id}`, data)
}
// 删除课程资源
export function courseResourceDelete(id) {
  return axios.deletes(`/api/cms/course/resource/${id}`)
}
// 课程编辑 资源排序
export function courseResourceSetOrder(data) {
  return axios.posts(`/api/cms/course/resource/set-order`, data)
}

// -----------------------------------------------------------------------------------------
// 课程中心

// 已上架未添加到课程中心列表
export function unCourseCenterList(data) {
  return axios.gets('/api/cms/course/center/unCourse', data)
}
// 课程中心列表
export function courseCenterList(data) {
  return axios.gets('/api/cms/course/center', data)
}

export function uncourseCenterList(data) {
  return axios.gets('/api/cms/course/center/unCourse', data)
}
// 课程中心添加
export function courseCenterAdd(data) {
  return axios.posts('/api/cms/course/center', data)
}
// 课程中心详情
export function courseCenterDetail(id) {
  return axios.gets(`/api/cms/course/center/${id}`)
}
// 课程中心修改
export function courseCenterEdit(id, data) {
  return axios.puts(`/api/cms/course/center/${id}`, data)
}
// 课程中心删除
export function courseCenterDelete(id) {
  return axios.deletes(`/api/cms/course/center/${id}`)
}
// 课程学员分页
export function courseCenterUserList(data) {
  return axios.gets(`/api/cms/course/center/users`, data)
}

// 批量删除课程中心学员
export function courseCenterUserDelete(data) {
  return axios.posts(`/api/cms/course/center/delete-users`, data)
}

// 课程全部学员
export function courseCenterAllUser(id) {
  return axios.gets(`/api/cms/course/center/all-users?courseCenterId=${id}`)
}
export function courseCenterUserReg(data) {
  return axios.posts(`/api/cms/course/center/user-reg`, data)
}
// 学习记录
export function courseRecordList(data) {
  return axios.gets(`/api/cms/course/user-records`, data)
}
export function userDetailRecordList(data) {
  return axios.gets(`/api/cms/course/user-records/detail`, data)
}

export function updateCourseRecords(data) {
  return axios.posts(`/api/cms/course/user-records/update`, data)
}
export function updateCourseRecord(data) {
  return axios.posts(`/api/cms/course/user-records/update-user`, data)
}

// 课程中心批量授权提交
export function coursePermission(data) {
  return axios.posts(`api/cms/course/center/update-users`, data)
}

// 课程考评
export function courseExamInfo(data) {
  return axios.gets(`/api/cms/course/center/exams`, data)
}

// 课程考评提交
export function courseExamEdit(data) {
  return axios.posts(`/api/cms/course/center/update-exams`, data)
}

// -----------------------------------------------------------
// RecommendCourse
// 热门课程
export function unrecommendCourseList(data) {
  return axios.gets('/api/cms/website/recommendCourse/unRecommend', data)
}
export function recommendCourseList(data) {
  return axios.gets('/api/cms/website/recommendCourse', data)
}
// 添加
/**
 *
 * @param {
 *"recommendCategoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *"courseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *"courseCenterId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
 *"sort": 0
 *} data
 * @returns
 */
export function recommendCourseAdd(data) {
  return axios.posts('/api/cms/website/recommendCourse', data)
}
// 详情
export function recommendCourseDetail(id) {
  return axios.gets(`/api/cms/website/recommendCourse/${id}`)
}
// 修改
export function recommendCourseEdit(id, data) {
  return axios.puts(`/api/cms/website/recommendCourse/${id}`, data)
}
// 删除
export function recommendCourseDelete(id) {
  return axios.deletes(`/api/cms/website/recommendCourse/${id}`)
}

// -----------------------------------------------------------
// 官网轮播图
export function cmsRotationChartList(data) {
  return axios.gets('/api/cms/website/rotationChart', data)
}
// 添加
/**
 *
 * @param {
 *"title": "string",
 *"url": "string",
 *"href": "string",
 *"sort": 0,
 *"isShow": true,
 *"introduce": "string"
 *} data
 * @returns
 */
export function cmsRotationChartAdd(data) {
  return axios.posts('/api/cms/website/rotationChart', data)
}
// 详情
export function cmsRotationChartDetail(id) {
  return axios.gets(`/api/cms/website/rotationChart/${id}`)
}
// 修改
export function cmsRotationChartEdit(id, data) {
  return axios.puts(`/api/cms/website/rotationChart/${id}`, data)
}
// 删除
export function cmsRotationChartDelete(id) {
  return axios.deletes(`/api/cms/website/rotationChart/${id}`)
}

// --------------------------------------------------------------------
// 热门分类
export function recommendCategoryList(data) {
  return axios.gets('/api/cms/website/recommendCategory', data)
}
// 添加
/**
 *
 * @param {
 *"name": "string",
 *"sort": 0,
 *"isShow": true
 *} data
 * @returns
 */
export function recommendCategoryAdd(data) {
  return axios.posts('/api/cms/website/recommendCategory', data)
}
// 详情
export function recommendCategoryDetail(id) {
  return axios.gets(`/api/cms/website/recommendCategory/${id}`)
}
// 修改
export function recommendCategoryEdit(id, data) {
  return axios.puts(`/api/cms/website/recommendCategory/${id}`, data)
}
// 删除
export function recommendCategoryDelete(id) {
  return axios.deletes(`/api/cms/website/recommendCategory/${id}`)
}

// -------------------------------------------------------
// 课程评论
export function courseCommentList(data) {
  return axios.gets(`/api/cms/course-comment`, data)
}
export function addCourseComment(data) {
  return axios.posts(`/api/cms/course-comment`, data)
}
export function editCourseComment(id, data) {
  return axios.puts(`/api/cms/course-comment/${id}`, data)
}
export function deletesCourseComment(id) {
  return axios.deletes(`/api/cms/course-comment/${id}`)
}
// -------------------------------------------------------
// 课程评分
export function courseScoreList(data) {
  return axios.gets(`/api/cms/course-score`, data)
}
export function addCourseScore(data) {
  return axios.posts(`/api/cms/course-score`, data)
}
export function editCourseScore(data) {
  return axios.puts(`/api/cms/course-score`, data)
}
export function deletesCourseScore(id) {
  return axios.deletes(`/api/cms/course-score/${id}`)
}

<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 16:57:16
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-07 15:34:17
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/user/sign.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <export-excel
      :header="['课程名称', '学时']"
      :filter-val="['courseName', 'classHour']"
      :api-fn="trainsOffLineCourseRecord"
      :paging="false"
    />
    <el-table :data="list">
      <el-table-column label="课程名称" prop="courseName" min-width="200" />
      <el-table-column label="学时" prop="classHour" />
    </el-table>
  </div>
</template>
<script>
import { trainsOffLineCourseRecord } from '@/api/train'
export default {
  name: 'TrainSign',
  components: {

  },
  props: {
    userId: {
      reuqerd: true,
      type: String,
      default: ''
    },
    trainId: {
      reuqerd: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false

    }
  },
  created() {
    this.getOffLineCourseRecord()
  },
  methods: {
    trainsOffLineCourseRecord() {
      return trainsOffLineCourseRecord({ TrainId: this.trainId, UserId: this.userId })
    },
    getOffLineCourseRecord(id) {
      this.listLoading = true
      trainsOffLineCourseRecord({ TrainId: this.trainId, UserId: this.userId }).then(res => {
        this.list = res.items
        this.listLoading = false
      }).catch(() => { this.listLoading = false })
    }
  }
}
</script>

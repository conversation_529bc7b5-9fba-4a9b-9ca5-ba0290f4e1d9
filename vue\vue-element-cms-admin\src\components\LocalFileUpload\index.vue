<template>
  <div class="ele-upload-file">
    <el-upload
      class="upload-demo"
      action="#"
      :multiple="multiple"
      :http-request="handleUpload"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-change="handleChange"
      :on-error="handleUploadError"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :auto-upload="true"
      :show-file-list="showFileList"
    >
      <el-button v-if="btnType" type="text" icon="el-icon-picture-outline" size="mini" circle />
      <el-button v-else :loading="uploading" :disabled="uploading" class="upload_btn" :round="isRound" size="small" type="primary" icon="el-icon-upload">{{ btnTitle }}</el-button>
      <el-progress
        v-if="uploading"
        class="upload_progress"
        :percentage="uploadProcess.process"
        style="width: 300px;margin-top: 20px;text-align:left;"
      />
      <div v-if="isShowTip" slot="tip" class="el-upload__tip">
        请上传
        <b style="color: #f56c6c">{{
          fileType.length ? fileType.join("/") : ""
        }}</b>
        格式文件
        <template v-if="fileSize">
          ，且大小不超过
          <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template></div>
    </el-upload>
    <el-dialog
      class="preview_dialog"
      append-to-body
      :title="previewTitle"
      :visible.sync="previewDialog"
      width="1000px"
      top="5vh"
      @before-close="previewDialogClose"
    >
      <preview-resource
        v-if="previewDialog"
        ref="previewResource"
        :type="previewType"
        :url="previewUrl"
      />
    </el-dialog>
  </div>
</template>
<script>
import { uploadFile, getFileDownloadInfo, uploadFileProgress } from '@/api/upload'
import PreviewResource from '@/components/PreviewResource'
export default {
  name: 'LocalFileUpload',
  components: {
    PreviewResource
  },
  props: {
    // 是否支持多选文件 (同官网)
    multiple: {
      type: Boolean,
      default: false
    },
    fileSize: {
      type: Number,
      require: true,
      default: 1024
    },
    limit: {
      type: Number,
      require: false,
      default: 5
    },
    fileList: {
      type: Array,
      require: false,
      default: function() {
        return []
      }
    },
    isRound: {
      type: Boolean,
      require: false,
      default: false
    },
    btnType: {
      type: Boolean,
      require: false,
      default: false
    },
    currentIndex: {
      type: Number,
      require: false,
      default: 0
    },
    btnTitle: {
      type: String,
      require: false,
      default: function() {
        return '点击上传'
      }
    },
    fileType: {
      type: Array,
      require: true,
      default: function() {
        return ['jpg', 'jpeg', 'png', 'pdf', 'mp4']
      }
    },
    // 资源是否公开
    isPublic: {
      type: Boolean,
      default: true
    },
    isShowTip: {
      type: Boolean,
      default: false
    },
    showFileList: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      previewTitle: '',
      previewDialog: false,
      previewType: '',
      previewUrl: '',
      preUploadList: [],
      uploadProcess: {
        process: 0,
        partlength: 0,
        uploadpart: 0,
        uploadFileCount: 0,
        uploadCount: 0
      },
      uploading: false
    }
  },
  created() {},
  methods: {
    handleChange(file, fileList) {
      // fileList.splice(fileList.indexOf(file))
    },
    getVideoDuration(file) {
      var renameFile = new File([file], file.name, {
        type: file.type
      })
      // 获取视频 音频时长
      var fileurl = URL.createObjectURL(renameFile)
      // 经测试，发现audio也可获取视频的时长
      var audioElement = new Audio(fileurl)
      var duration = 0
      audioElement.addEventListener('loadedmetadata', function(_event) {
        duration = audioElement.duration
        return duration
        // this.long = duration
      })
    },
    handleUpload(upFile) {
      const file = upFile.file
      const formData = new FormData()
      formData.append('FileContainerName', 'default')
      formData.append('FileType', 2)
      formData.append('GenerateUniqueFileName', false)
      formData.append('OwnerUserId', this.$store.getters.userId)
      formData.append('File', file)
      var _fileName = file.name
      var duration = 0
      if (file.type === 'video/mp4') {
        var renameFile = new File([file], file.name, {
          type: file.type
        })
        // 获取视频 音频时长
        var fileurl = URL.createObjectURL(renameFile)
        // 经测试，发现audio也可获取视频的时长
        var audioElement = new Audio(fileurl)
        audioElement.addEventListener('loadedmetadata', function(_event) {
          duration = audioElement.duration
        })
      }
      var fileLoaded = {}
      // var fileTotal = {}
      uploadFileProgress(formData, progressEvent => {
        this.$set(fileLoaded, file.uid, progressEvent.loaded)
        // this.$set(fileTotal, file.uid, progressEvent.total)
        this.uploadProcess.uploadpart = 0

        for (var key in fileLoaded) {
          this.uploadProcess.uploadpart += fileLoaded[key]
        }
        // this.uploadProcess.partlength = 0
        // for (var key in fileTotal) {
        //   this.uploadProcess.partlength += fileTotal[key]
        // }
        // this.uploadProcess.uploadpart += progressEvent.loaded

        this.uploadProcess.process = Number((Math.round((this.uploadProcess.uploadpart / this.uploadProcess.partlength) * 100 * 100) / 100).toFixed(0))
      }).then((response) => {
        this.uploadProcess.uploadFileCount--
        if (this.uploadProcess.uploadFileCount === 0) {
          this.uploading = false
          this.uploadProcess = {
            process: 0,
            partlength: 0,
            uploadpart: 0,
            uploadFileCount: 0
          }
        }
        this.handleUploadSuccess(response, duration, _fileName)
      })
        .catch((err) => {
          this.uploading = false
          this.uploadProcess = {
            process: 0,
            partlength: 0,
            uploadpart: 0,
            uploadFileCount: 0
          }
          this.handleUploadError(err)
        })
    },
    handleUploadSuccess(response, duration, _fileName) {
      let fileExtension = ''
      if (response.fileInfo.fileName.lastIndexOf('.') > -1) {
        fileExtension = response.fileInfo.fileName.slice(response.fileInfo.fileName.lastIndexOf('.') + 1)
      }
      var filetype = this.checkFileFormat(fileExtension)
      const _url = filetype === 'image' ? response.downloadInfo.downloadUrl : response.fileInfo.id
      const uploadForm = {
        url: _url,
        localUrl: response.downloadInfo.downloadUrl,
        fileName: _fileName.substring(
          0,
          _fileName.lastIndexOf('.')
        ),
        name: _fileName,
        hash: response.fileInfo.hash,
        // extend: '.' + fileExtension,
        size: response.fileInfo.byteSize,
        fileType: '.' + fileExtension,
        resType: '',
        tranStatus: 0,
        documentId: null,
        jobId: 'local',
        durationInSecond: duration
      }
      switch (fileExtension) {
        case 'zip':
          uploadForm.resType = 'html'
          break
        case 'mp4':
          uploadForm.resType = 'video'
          break
        case 'pdf':
          uploadForm.resType = 'pdf'
          break
      }
      // console.log('handleUploadSuccess', uploadForm)
      this.$emit('response-fn', _url, uploadForm, this.currentIndex)
    },
    handleUploadError(err) {
      this.$message.error('上传失败, 请重试')
      this.$emit('response-error', err)
    },
    handleRemove(file, fileList) {
      this.$emit('remove-upload', file)
    },
    async handlePreview(file) {
      const res = await getFileDownloadInfo(file.url)
      let fileExtension = ''
      if (res.expectedFileName.lastIndexOf('.') > -1) {
        fileExtension = res.expectedFileName.slice(res.expectedFileName.lastIndexOf('.') + 1)
      }
      this.previewType = this.checkFileFormat(fileExtension)
      // if (this.previewType === 'video') {
      this.previewUrl = res.downloadUrl
      // } else {
      //   var fileContent = await getFileContent(res.downloadUrl)
      //   const fileBlob = []
      //   fileBlob.push(fileContent.data)
      //   this.previewUrl = URL.createObjectURL(new Blob(fileBlob))
      // }
      this.previewDialog = true
    },
    // 检查文件格式
    checkFileFormat(fileExtension) {
      this.previewType = 'undefined'
      var imageList = ['jpg', 'png', 'jpeg']
      for (let i = 0; i < imageList.length; i++) {
        const imageItem = imageList[i]
        if (fileExtension.indexOf(imageItem) > -1) {
          return 'image'
        }
      }
      var videoList = ['mp4']
      for (let i = 0; i < videoList.length; i++) {
        const videoItem = videoList[i]
        if (fileExtension.indexOf(videoItem) > -1) {
          return 'video'
        }
      }
      var _fileList = ['pdf']
      for (let i = 0; i < _fileList.length; i++) {
        const fileItem = _fileList[i]
        if (fileExtension.indexOf(fileItem) > -1) {
          return 'pdf'
        }
      }
    },
    previewDialogClose() {
      if (this.previewType === 'video') {
        this.$refs.previewResource.$emit('videoPause') // 向子组件传递方法
      }

      this.previewDialog = false
    },
    handleExceed(files, fileList) {
      this.$message.error(`最多上传${this.limit}个文件`)
    },
    handleBeforeUpload(file) {
      let fileFormat = true
      let fileExtension = ''
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      fileFormat = this.fileType.some(type => {
        if (file.type.indexOf(type) > -1) return true
        if (fileExtension && fileExtension.indexOf(type) > -1) return true
        return false
      })

      if (!fileFormat) {
        this.$message.warning(
          `${file.name} 文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.warning(`${file.name} 上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      var filetype = this.checkFileFormat(fileExtension)
      if (filetype !== 'image') {
        this.uploading = true
      }
      this.uploadProcess.uploadFileCount++
      this.uploadProcess.partlength += file.size
      return true
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    }
  }
}
</script>
<style scoped>
.upload_btn {
  /* margin-left: 10px; */
  display: block;
}
</style>

<template>
  <div :current-paper="currentPaper">
    <el-cascader v-model="listQuery.ExamPaperCategoryId" filterable clearable :options="categoryList"
      :props="{ 'label': 'name', 'value': 'id', 'checkStrictly': true, 'emitPath': false }" size="small"
      style="margin: 0 10px" clearable placeholder="请选择试卷分类..." @change="handleRefreshList" />
    <el-select v-model="listQuery.Tag" size="small" filterable clearable @change="handleRefreshList" placeholder="请选择试卷标签...">
      <el-option v-for="item in tagsList" :key="item.id" :value="item.name">{{ item.name }}</el-option>
    </el-select>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <el-table ref="examPaperTable" v-loading="listLoading" :data="list" @sort-change="sortChange"
      @selection-change="handleChooseExamPaperChange" @row-click="handleRowClick">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
      <el-table-column prop="code" label="试卷编号" sortable="code" header-align="left" width="200">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleCheck(row)">{{
              row.code
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="name" label="试卷名称" sortable="name" header-align="left" /> -->
      <el-table-column prop="name" label="试卷名称" sortable="name" header-align="left">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleCheck(row)">{{
              row.name
          }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="totalScore" label="总分" sortable="totalScore" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.totalScore }} 分</span>
        </template>
      </el-table-column>
      <el-table-column prop="questionNumber" label="题数" sortable="questionNumber" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.questionNumber }} 题</span>
        </template>
      </el-table-column>
      <el-table-column prop="creationTime" sortable="creationTime" label="创建日期" width="135px">
        <template slot-scope="scope">
          <span>{{ scope.row.creationTime | formatDatetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount" @pagination="getList" />
    <el-dialog v-if="examPaperPreviewDialog" class="examPaperPreviewDialog" title="试卷预览"
      :visible.sync="examPaperPreviewDialog" :append-to-body="true" top="5vh" width="950px">
      <div class="title">
        <span>{{ examPaperDetail.title }}</span>
        <span class="totalscore"> ( 共 {{ examPaperDetail.count }} 题 ，{{ examPaperDetail.totalScore }}分)</span>
      </div>
      <div v-loading="examPaperPreviewLoading">
        <div v-for="(item, index) in examPaperDetail.questionList" :key="item.id">
          <exam-preview :order="index + 1" :data="item" />
        </div>
      </div>
      <!-- <div v-for="(item,index) in examPaperDetail.questionList" :key="item.id">
        <exam-preview :order="index + 1" :data="item" />
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button round size="medium" @click="examPaperPreviewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getExamPaperList, examPaperQuestionlist, examPaperCategoryAll, examPaperTagAll } from '@/api/examPaper'
import Pagination from '@/components/Pagination'
import ExamPreview from '@/components/ExamPreview'
export default {
  name: 'ChooseExamPaper',
  components: {
    Pagination,
    ExamPreview
  },
  props: {
    currentPaper: {
      required: false,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      categoryList: [],
      tagsList: [],
      listLoading: false,
      list: [],
      listQuery: {
        Filter: '',
        ExamPaperType: '',
        ExamPaperCategoryId: '',
        Tag: '',
        Sorting: 'creationTime desc',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1
      },
      // 预览试卷
      examPaperPreviewDialog: false,
      // 试卷预览 loading
      examPaperPreviewLoading: false,
      // 试卷预览信息
      examPaperDetail: {
        title: '',
        totalScore: 0,
        count: 0,
        questionList: []
      }
    }
  },
  mounted() {
    this.getList()
    this.getCategoryList()
    this.getTagList()
  },
  methods: {
    handleChooseExamPaperChange(val) {
      this.$emit('response', val[0])
      if (val.length > 1) {
        this.$refs.examPaperTable.clearSelection()
        this.$refs.examPaperTable.toggleRowSelection(val.pop())
        // this.$emit('response', val.pop())
      }
      // else {
      //   if (val.length) {
      //     this.$emit('response', val[0])
      //   }
      // }
    },
    handleRowClick(row) {
      this.$refs.examPaperTable.clearSelection()
      this.$refs.examPaperTable.toggleRowSelection(row)
      this.$emit('response', row)
    },
    // 预览
    async handleCheck(row) {
      this.examPaperDetail.questionList = []
      this.examPaperPreviewLoading = true
      this.examPaperPreviewDialog = true
      this.examPaperDetail.title = row.name
      this.examPaperDetail.count = row.questionNumber
      this.examPaperDetail.totalScore = row.totalScore
      var form = {
        ExamPaperId: row.id,
        Filter: '',
        Sorting: '',
        MaxResultCount: 500,
        SkipCount: 0
      }
      await examPaperQuestionlist(form).then(res => {
        this.examPaperPreviewLoading = false
        this.examPaperDetail.questionList = res.items
      })
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      getExamPaperList(this.listQuery).then(res => {
        this.list = res.items
        this.listLoading = false
        this.listQuery.totalCount = res.totalCount
        this.$nextTick(() => {
          if (this.currentPaper && this.currentPaper.length > 0) {
            for (var i = 0; i < this.list.length; i++) {
              if (this.currentPaper === this.list[i].id) {
                this.$refs.examPaperTable.toggleRowSelection(this.list[i])
              }
            }
          }
        })
      }).catch(() => {
        this.listLoading = false
      })
    },
    getTagList() {
      examPaperTagAll().then(res => {
        this.tagsList = res.items
      })
    },
    async getCategoryList() {
      const res = await examPaperCategoryAll()

      this.categoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    }
  }
}
</script>
<style scoped>
.examPaperPreviewDialog ::v-deep .el-dialog__body {
  padding: 30px 40px;
}

.examPaperPreviewDialog .title {
  text-align: center;
  height: 50px;
  font-size: 16px;
  font-weight: bold;
}

.examPaperPreviewDialog .totalscore {
  font-size: 14px;
  font-weight: initial;
}
</style>

// base color
$blue:#324157;//324157
$light-blue:#3A71A8;//3A71A8
$red:#C03639;
$pink: #E65D6E;
$green: #68e194;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar default
$menuText:#bfcbd9; // bfcbd9 606266
$menuActiveText:#409EFF;//409EFF F15412
//子菜单选中后一级菜单字体颜色
$subMenuActiveText:#f5f4f4; // https://github.com/ElemeFE/element/issues/12951

//菜单背景
$menuBg:#304156;//304156 fff
//菜单hover背景
$menuHover:#263445;//263445 efefef
//子菜单背景
$subMenuBg:#1f2d3d;//1f2d3d efefef
//子菜单hover
$subMenuHover:#001528;//001528 e9e9e9

// sidebar
// $menuText:#000;
// $menuActiveText:#44C6C5;
// $subMenuActiveText:#44C6C5; // https://github.com/ElemeFE/element/issues/12951

// $menuBg:#ECECEC;
// $menuHover:#fff;

// $subMenuBg:#ECECEC;
// $subMenuHover:#fff;

$sideBarWidth: 220px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}

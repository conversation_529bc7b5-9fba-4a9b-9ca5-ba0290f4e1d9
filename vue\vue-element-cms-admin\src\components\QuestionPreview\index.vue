<template>
  <div :order="order" :data="data" class="question">
    <div class="question_title">
      <span v-if="order" class="q_order">{{ order }}、</span>
      <span class="q_title">{{ item.Title }}</span>
      <span v-if="data.score" class="q_score">({{ data.score }} 分) </span>
    </div>
    <div class="question_img">
      <ele-gallery
        :lazy="true"
        :width="200"
        :height="120"
        :source="item.imgs"
      />
      <!-- <el-image
        v-for="img in item.imgs"
        :key="img"
        :src="img"
        fit="cover"
        :preview-src-list="item.imgs"
        @click.stop="handleClickItem"
      /> -->
    </div>

    <div class="question_options">
      <el-radio-group v-if="type === 0" v-model="answerModel">
        <el-radio
          v-for="(option, index) in item.Options"
          :key="option.Title"
          :label="index + 1"
        ><span>{{ numSwitchChar(index + 1) }} 、</span>{{ option.Title }}
          <div class="question_img">
            <ele-gallery
              :lazy="true"
              :width="200"
              :height="120"
              :source="option.imgs"
            />
          </div>
        </el-radio>
      </el-radio-group>
      <el-checkbox-group v-if="type === 1" v-model="answerModel">
        <el-checkbox
          v-for="(option, index) in item.Options"
          :key="option.Title"
          :label="index + 1"
        ><span>{{ numSwitchChar(index + 1) }} 、</span>{{ option.Title }}
          <div class="question_img">
            <ele-gallery
              :lazy="true"
              :width="200"
              :height="120"
              :source="option.imgs"
            />
          </div>
        </el-checkbox>
      </el-checkbox-group>
      <!-- 判断 -->
      <el-radio-group v-if="type === 2" v-model="answerModel">
        <el-radio :label="1">A、正确</el-radio>
        <el-radio :label="0">B、错误</el-radio>
      </el-radio-group>
    </div>

    <div class="answer_detail">
      <p v-if="type === 0 || type === 1 || type === 2">
        正确答案 :<span> {{ rightAnswer }}</span>
      </p>
      <p v-show="data.analysis!=null">
        答案解析 : {{ data.analysis ? data.analysis : "无" }}
      </p>
    </div>
  </div>
</template>
<script>
import EleGallery from 'vue-ele-gallery'

export default {
  name: 'QuestionPreview',
  components: {
    EleGallery
  },
  props: {
    order: {
      required: false,
      type: Number,
      default: null
    },
    data: {
      required: true,
      type: Object,
      default: null
    }
    // type: {
    //   required: false,
    //   type: Number,
    //   default: null
    // }
  },
  data() {
    return {
      rightAnswer: '',
      item: null, // 题干
      type: null, // 题型
      answerModel: null // 答案
    }
  },
  created() {
    this.changeData()
    this.getRightAnswer()
  },
  methods: {
    changeData() {
      this.item = JSON.parse(this.data.questionStem)
      this.type = this.data.questionType
      //   if (this.item.Title_Imgs !== null && this.item.Title_Imgs.length > 0) {
      //     const imgs = this.item.Title_Imgs.split(',')
      //     this.item.imgs = imgs
      //   }
      // 字符串转数组
      if (this.item.Title_Imgs !== null && Object.prototype.toString.call(this.item.Title_Imgs) === '[object Array]') {
        this.item.imgs = this.item.Title_Imgs
      } else if (this.item.Title_Imgs !== null && Object.prototype.toString.call(this.item.Title_Imgs) === '[object String]' && this.item.Title_Imgs.length > 0) {
        var imgs = this.item.Title_Imgs.split(',')
        for (var i = 0; i < imgs.length; i++) {
          if (imgs[i] === '' || imgs[i] === null || typeof (imgs[i]) === 'undefined') {
            imgs.splice(i, 1)
            i = i - 1
          }
        }
        this.item.imgs = imgs
      }
      if (this.type === 0 || this.type === 1) {
        this.item.Options.forEach((op, i) => {
          if (op.Images !== null && Object.prototype.toString.call(op.Images) === '[object Array]') {
            op.imgs = op.Images
          } else if (op.Images !== null && Object.prototype.toString.call(op.Images) === '[object String]' && op.Images.length > 0) {
            const imgs = op.Images.split(',')
            for (var i = 0; i < imgs.length; i++) {
              if (imgs[i] === '' || imgs[i] === null || typeof (imgs[i]) === 'undefined') {
                imgs.splice(i, 1)
                i = i - 1
              }
            }
            op.imgs = imgs
          }
        })
      }
      // Answer
      if (this.data.answer === '') {
        var answer = this.data.answerModels
        if (this.data.questionType === 0) {
          if (answer.optionAnswers && answer.optionAnswers.length > 0) {
            this.answerModel = answer.optionAnswers[0].order
          } else {
            this.answerModel = ''
          }
        } else if (this.data.questionType === 1) {
          this.answerModel = []
          answer.optionAnswers.forEach((answerItem) => {
            this.answerModel.push(answerItem.order)
          })
        } else if (this.data.questionType === 2) {
          this.answerModel = answer.judgeAnswer
        }
      } else {
        var answer = JSON.parse(this.data.answer)
        if (this.data.questionType === 0) {
          if (answer.OptionAnswers && answer.OptionAnswers.length > 0) {
            this.answerModel = answer.OptionAnswers[0].Order
          } else {
            this.answerModel = ''
          }
        } else if (this.data.questionType === 1) {
          this.answerModel = []
          answer.OptionAnswers.forEach((answerItem) => {
            this.answerModel.push(answerItem.Order)
          })
        } else if (this.data.questionType === 2) {
          this.answerModel = answer.JudgeAnswer
        }
      }
    },
    getRightAnswer() {
      if (
        Object.prototype.toString.call(this.answerModel) === '[object Array]'
      ) {
        this.answerModel.forEach((item, index) => {
          if (index === 0) {
            this.rightAnswer = this.numSwitchChar(item)
          } else {
            this.rightAnswer += '、' + this.numSwitchChar(item)
          }
        })
      } else {
        if (this.data.questionType === 0) {
          this.rightAnswer = this.numSwitchChar(Number(this.answerModel))
        } else if (this.data.questionType === 2) {
          if (Number(this.answerModel) === 1) {
            this.rightAnswer = '正确'
          } else {
            this.rightAnswer = '错误'
          }
        }
      }
    },
    handleClickItem() {
      this.$nextTick(() => {
        // 获取遮罩层dom
        const domImageMask = document.querySelector('.el-image-viewer__mask')
        if (!domImageMask) {
          return
        }
        domImageMask.addEventListener('click', () => {
          // 点击遮罩层时调用关闭按钮的 click 事件
          document.querySelector('.el-image-viewer__close').click()
        })
      })
    },
    numSwitchChar(index) {
      switch (index) {
        case 1:
          return 'A'
        case 2:
          return 'B'
        case 3:
          return 'C'
        case 4:
          return 'D'
        case 5:
          return 'E'
        case 6:
          return 'F'
        case 7:
          return 'G'
        case 8:
          return 'H'
        case 9:
          return 'I'
        case 10:
          return 'J'
        case 11:
          return 'K'
        case 12:
          return 'L'
        case 13:
          return 'M'
        case 14:
          return 'N'
        default:
          break
      }
    }
  }
}
</script>
<style scoped>
.el-radio,
.el-checkbox {
  display: block;
  margin-bottom: 10px;
}
.el-radio-group ::v-deep .el-radio,
.el-checkbox-group ::v-deep .el-checkbox {
  margin-top: 10px;
  margin-bottom: 10px;
}
.el-radio ::v-deep .el-radio__inner {
  margin-bottom: -2px;
}
.question ::v-deep .el-radio__label {
  text-overflow: ellipsis;
  white-space: normal;
  line-height: 18px;
  vertical-align: top;
  display: inline-block;
}
.question_options .el-checkbox {
  display: flex
}
.question_options ::v-deep .el-checkbox__inner {
  margin-top: 2px;
}
/* .question ::v-deep .el-checkbox__label {
  text-overflow: ellipsis;
  white-space: normal;
  vertical-align: top;
} */
.answer_detail {
  width: 100%;
  background-color: rgb(242 245 250);
  padding: 15px 10px;
  color: #909399;
  margin-top: 30px;
}
.answer_detail p {
  margin: 0 0 10px 0;
  line-height: 25px;
}
.answer_detail span {
  color: #606266;
}

.question .question_title {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  padding-bottom: 10px;
}
.question .question_title .q_title {
  margin-left: 0px;
}
.question .question_title .q_score {
  font-size: 14px;
  font-weight: initial;
}
.question_img {
  margin-top: 10px;
}
/* .question {
  margin-bottom: 30px;
} */
/* .question_img ::v-deep .el-image {
  max-height: 120px;
  border: 1px solid #eee;
  margin-right: 10px;
  display: inline-block;
}
.question_img ::v-deep .el-image .el-image__inner {
  max-height: 120px;
  width: auto;
}
.question_img ::v-deep .el-icon-circle-close {
  color: white;
} */
</style>

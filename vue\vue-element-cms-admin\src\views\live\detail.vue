<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>直播详情——{{ form.title }}</span>
      </div>
      <el-descriptions title="基础信息" border :column="2" label-class-name="descriptions_label">
        <el-descriptions-item label="直播名称">{{
          form.title
        }}</el-descriptions-item>
        <el-descriptions-item label="讲师">{{
          form.lecturer
        }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{
          form.startTime | formatDatetime
        }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{
          form.endTime | formatDatetime
        }}</el-descriptions-item>
        <el-descriptions-item label="直播时长">{{ form.timeLong }} 分钟</el-descriptions-item>
        <el-descriptions-item label="有效期">{{ form.closeDate | formatDatetime }}</el-descriptions-item>
        <el-descriptions-item label="课时">{{ form.classHour }}</el-descriptions-item>
        <el-descriptions-item label="合格时长">{{ form.passDuration / 60 }}</el-descriptions-item>
        <el-descriptions-item label="直播规模">{{ form.userCount }} 人</el-descriptions-item>
        <el-descriptions-item label="考评通过才能获取学时">{{ form.passExamGetHour ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="是否学习完成才能考核">{{ form.canExamAfterLearn ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="描述">{{
          form.description
        }}</el-descriptions-item>
      </el-descriptions>
      <el-row style="margin: 20px 0;">
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="mini" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button size="mini" round type="primary" @click="handleUserDetailExport">导出
        </el-button>
        <el-upload
          ref="fileUpload"
          round
          class="upload-demo"
          style="display: inline-block;"
          action=""
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-exceed="handleExceed"
          :show-file-list="false"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
          :auto-upload="false"
        >
          <el-button
            :loading="importLoading"
            :disabled="importLoading"
            round
            size="mini"

            style="margin: 0 10px"
            type="primary"
          >导入时长</el-button>
        </el-upload>
        <el-button round size="mini" type="primary" @click="updateClassHour">更新学时</el-button>
        <el-button size="mini" round type="primary" icon="" @click="loadLiveUserRecords">同步直播时长</el-button>
        <el-button size="mini" round type="primary" icon="" @click="loadLiveUserBackRecords">同步当天回放时长</el-button>
        <el-button size="mini" round type="primary" icon="" @click="generatePureVideo">回放生成视频</el-button>
        <el-button size="mini" round type="primary" icon="" @click="getPureVideo">下载回放视频</el-button>
        <el-button size="mini" round type="primary" icon="" @click="replaceBackVideo">替换回放Id</el-button>
        <el-button size="mini" round type="primary" icon="" @click="replaceBackUrl">替换回放Url</el-button>
        <el-button size="mini" round type="primary" icon="" @click="publicLiveExam">发布直播评测</el-button>
        <el-button size="mini" round type="danger" icon="" @click="handerDeleteUnJoinLiveUsers">删除未参与直播用户</el-button>
      </el-row>
      <el-table v-loading="listLoading" :data="list" size="small" highlight-current-row @sort-change="sortChange">
        <el-table-column label="序号" prop="userNumber" sortable="userNumber" width="120" />
        <el-table-column label="姓名" prop="name" sortable="name" show-overflow-tooltip width="180" />
        <el-table-column label="用户名" prop="userName" sortable="userName" show-overflow-tooltip width="180" />
        <el-table-column label="部门" prop="classId" sortable="classId" min-width="150" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ cacheFindParent(row.classId).join('/') }}
          </template>
        </el-table-column>

        <el-table-column label="角色" prop="userRole" sortable="userRole" show-overflow-tooltip width="100">
          <template slot-scope="{ row }">
            {{ row.userRole == 0 ? "学生" : "助教" }}
          </template>
        </el-table-column>
        <el-table-column label="已获学时" prop="classHour" sortable="classHour" show-overflow-tooltip width="100" />
        <el-table-column label="测评成绩" prop="examUserScore" sortable="examUserScore" show-overflow-tooltip width="100" />
        <el-table-column label="考核状态" prop="examPass" sortable="examPass" show-overflow-tooltip width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.examPass" size="small" type="success">已通过</el-tag>
            <el-tag v-else-if="!hasExam" size="small" type="info">无</el-tag>
            <el-tag v-else size="small" type="info">未通过</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="有效期"
          prop="closeDate"
          sortable="closeDate"
          show-overflow-tooltip
          width="140"
        >
          <template slot-scope="{ row }">
            {{ row.closeDate | formatDateTime }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="合格时长"
          prop="passDuration"
          sortable="passDuration"
          show-overflow-tooltip
          width="140"
        >
          <template slot-scope="{ row }">
            {{ row.passDuration/60 }}分钟
          </template>
        </el-table-column> -->
        <el-table-column
          label="参与时长"
          prop="liveViewDuration"
          sortable="liveViewDuration"
          show-overflow-tooltip
          width="160"
        >
          <template slot-scope="{ row }">
            {{ row.liveViewDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column
          label="回放观看时长"
          prop="backPlayDuration"
          sortable="backPlayDuration"
          show-overflow-tooltip
          width="160"
        >
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleUserLiveDetail(row)">{{
              row.backPlayDuration | formatSecond
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getLiveUser"
      />
    </el-card>
    <el-dialog title="回放历史详情" :visible.sync="detailDialog" width="1000px">
      <el-table
        v-loading="detailListLoading"
        :data="detailList"
        size="small"
        highlight-current-row
        @sort-change="detailSortChange"
      >
        <el-table-column label="用户名" prop="userName" sortable="userName" show-overflow-tooltip width="180" />
        <el-table-column label="开始时间" prop="playBeginTime" sortable="playBeginTime" show-overflow-tooltip width="140">
          <template slot-scope="{ row }">
            {{ row.playBeginTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="playEndTime" sortable="playEndTime" show-overflow-tooltip width="140">
          <template slot-scope="{ row }">
            {{ row.playEndTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="观看时长" prop="playLength" sortable="playLength" show-overflow-tooltip width="100">
          <template slot-scope="{ row }">
            {{ row.playLength | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="IP" prop="userIp" sortable="userIp" show-overflow-tooltip width="120" />
        <el-table-column label="地域" prop="area" sortable="area" show-overflow-tooltip width="140" />
        <el-table-column label="客户端" prop="clientType" sortable="clientType" show-overflow-tooltip width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.clientType === 1" type="info">iphone</el-tag>
            <el-tag v-else-if="row.clientType === 2" type="info">ipad</el-tag>
            <el-tag v-else-if="row.clientType === 3" type="info">Android</el-tag>
            <el-tag v-else-if="row.clientType === 4" type="info">手机M站</el-tag>
            <el-tag v-else-if="row.clientType === 5" type="info">PC 网页</el-tag>
            <el-tag v-else-if="row.clientType === 6" type="info">APP内嵌M站</el-tag>
            <el-tag v-else-if="row.clientType === 7" type="info">小程序</el-tag>
            <el-tag v-else-if="row.clientType === 8" type="info">Android 小程序</el-tag>
            <el-tag v-else-if="row.clientType === 9" type="info">iOS小程序</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="detailListQuery.totalCount > 0"
        :total="detailListQuery.totalCount"
        :page.sync="detailListQuery.page"
        :limit.sync="detailListQuery.MaxResultCount"
        @pagination="getDetailUserLive"
      />
    </el-dialog>
    <el-dialog title="替换回放" :visible.sync="replaceBackVideoDialog" width="400px">
      <el-form :model="replaceForm" size="small">
        <el-form-item label="视频ID">
          <el-input v-model="replaceForm.videoId" size="small" class="small_input" clearable placeholder="输入视频ID" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="replaceBackVideoLoading" round type="primary" @click="handleReplaceBackVideoSure">确 定
        </el-button>
        <el-button round @click="replaceBackVideoDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="替换回放地址" :visible.sync="replaceBackUrlDialog" width="600px">
      <el-form :model="replaceBackUrlForm" size="small" label-width="80px">
        <el-form-item label="视频Url">
          <el-input v-model="replaceBackUrlForm.backUrl" clearable placeholder="输入视频Url" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="replaceBackVideoLoading" round type="primary" @click="handleReplaceBackUrlSure">确 定
        </el-button>
        <el-button round @click="replaceBackUrlDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="回放视频下载" :visible.sync="showBackUrlDialog" width="600px">
      <el-link type="primary" :href="liveBackVideoUrl" target="_blank">{{ liveBackVideoUrl }}</el-link>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="showBackUrlDialog = false"> 关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  liveUserList,
  liveUserAll,
  liveUserBackRecord,
  liveDetailInfo,
  liveUserRecord_bjy,
  liveUserBackRecord_bjy,
  generatePureVideo_bjy,
  replaceBackVideo_bjy,
  replaceBackVideoTime_bjy,
  replaceBackUrl,
  updateClassHour,
  publishLiveExam,
  getPureVideo_bjy,
  liveExamInfo,
  deleteUnJoinLiveUsers
} from '@/api/live'
import { formatDateTime, formatSecond } from '@/utils'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { orgsData } from '@/api/user'
export default {
  name: 'LiveDetail',
  components: {
    Pagination
  },
  data() {
    return {
      liveId: this.$route.query.id,
      form: {
        title: '',
        description: '',
        startTime: '',
        endTime: '',
        timeLong: '',
        lecturer: '',
        userCount: '',
        classHour: 0,
        closeDate: '',
        passDuration: 0,
        canExamAfterLearn: false,
        passExamGetHour: false
      },
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        LiveStreamId: this.$route.query.id,
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: 'userNumber desc',
        page: 1,
        totalCount: 0
      },
      hasExam: false,
      detailDialog: false,
      detailList: [],
      detailListLoading: false,
      detailListQuery: {
        Filter: '',
        LiveStreamId: this.$route.query.id,
        UserId: '',
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: 'creationTime desc',
        page: 1,
        totalCount: 0
      },
      replaceBackVideoDialog: false,
      replaceBackVideoLoading: false,
      replaceForm: {
        liveId: this.$route.query.id,
        videoId: ''
      },
      replaceBackUrlDialog: false,
      replaceBackUrlForm: {
        liveId: this.$route.query.id,
        backUrl: ''
      },
      showBackUrlDialog: false,
      liveBackVideoUrl: '',
      importLoading: false,
      fileTemp: null,

      map: null,
      cacheDate: new Map()
    }
  },
  created() {
    this.loadClass()
    this.getLiveDetailInfo()
    this.getLiveUser()
    this.getLiveExam()
  },
  methods: {
    handleChange(file, fileList) {
      this.importLoading = true
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.submitProgress = 0
          this.importfxx(this.fileTemp)
        } else {
          this.importLoading = false
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.importLoading = false
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    async importfxx(file) {
      const _this = this
      var reader = new FileReader()
      reader.onload = function(e) {
        var binary = ''
        var wb // 读取完成的数据
        var bytes = new Uint8Array(reader.result)
        var length = bytes.byteLength
        var xlsx = require('xlsx')
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        wb = xlsx.read(binary, {
          type: 'binary'
        })
        _this.da = xlsx.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        /* excel数据合法性验证 前端 【 和数据库数据对比不在此范围内处理 放到后台代码中 】*/
        if (_this.da.length > 0) {
          var tmp = []
          _this.da.forEach(item => {
            var _time = _this.sencodChange(item['参与时长'])
            tmp.push({
              liveStreamId: _this.$route.query.id,
              userId: item['用户ID'],
              userName: item['用户名'],
              actualListenTime: _time
            })
          })
          _this.importTime(tmp)
        } else {
          this.$message.error('Excel未读取到数据')
        }
      }
      reader.readAsArrayBuffer(file)
    },
    async importTime(tmp) {
      for await (const tmpItem of tmp) {
        this.importLoading = true
        await replaceBackVideoTime_bjy(tmpItem).then(res => {

        }).catch(() => {
          if (this.$refs.fileUpload) {
            this.$refs.fileUpload.clearFiles()
          }

          this.$message.error('导入失败')
          this.importLoading = false
        })
      }
      if (this.$refs.fileUpload) {
        this.$refs.fileUpload.clearFiles()
      }
      this.$message.success('导入成功')
      this.importLoading = false
      this.getLiveUser()
    },
    sencodChange(val) {
      if (val && val !== '--') {
        const tmp = val.split(':')
        return parseInt(tmp[0]) * 3600 + parseInt(tmp[1]) * 60 + parseInt(tmp[2])
      } else {
        return 0
      }
    },
    handleExceed() {
      this.importLoading = false
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    handleRemove(file, fileList) {
      this.fileTemp = null
    },
    handleRefreshList(t) {
      this.listQuery.page = 1
      this.getLiveUser()
    },
    handleUserLiveDetail(row) {
      this.detailListQuery.UserId = row.userId
      this.getDetailUserLive()
      this.detailDialog = true
    },
    handleUserDetailExport() {
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['用户ID', '直播名称', '讲师', '开始时间', '结束时间', '姓名', '用户名', '一级部门', '二级部门', '三级部门', '角色', '课时', '学时', '测评成绩', '通过考核', '参与时长', '回放观看时长']
        const filterVal = [
          'userId',
          'title',
          'lecturer',
          'startTime',
          'endTime',
          'name',
          'userName',
          'className1',
          'className2',
          'className3',
          'userRole',
          'hour',
          'classHour',
          'examUserScore',
          'examPass',
          'liveViewDuration',
          'backPlayDuration'
        ]
        // 成绩列表数据
        var list = []
        const res = await liveUserAll({ liveStreamId: this.$route.query.id })
        res.items.forEach((item) => {
          list.push({
            ...item,
            userRole: item.userRole === 0 ? '学生' : '助教',
            examPass: this.hasExam ? (item.examPass ? '是' : '否') : '无',
            liveViewDuration: formatSecond(item.liveViewDuration),
            backPlayDuration: formatSecond(item.backPlayDuration),
            className1: this.cacheFindParent(item.classId).length ? this.cacheFindParent(item.classId)[0] : '',
            className2: this.cacheFindParent(item.classId).length > 1 ? this.cacheFindParent(item.classId)[1] : '',
            className3: this.cacheFindParent(item.classId).length > 2 ? this.cacheFindParent(item.classId)[2] : '',
            title: this.form.title,
            lecturer: this.form.lecturer,
            startTime: formatDateTime(this.form.startTime),
            endTime: formatDateTime(this.form.endTime),
            hour: this.form.classHour
          })
        })
        const data = this.formatJson(filterVal, list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename:
            this.$route.query.title + '_详情_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    handleUpdateTimeLong() {

    },
    // 替换回放
    replaceBackVideo() {
      this.replaceForm = {
        liveId: this.$route.query.id,
        videoId: ''
      }
      this.replaceBackVideoDialog = true
    },

    handleReplaceBackVideoSure() {
      if (this.replaceForm.videoId === '') {
        this.$message.warning('请输入视频ID')
        return
      }
      this.replaceBackVideoLoading = true
      replaceBackVideo_bjy(this.replaceForm).then(res => {
        this.replaceBackVideoLoading = false
        if (res.code === 0) {
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.msg)
        }
      }).catch(() => { this.replaceBackVideoLoading = false })
    },
    replaceBackUrl() {
      this.replaceBackUrlDialog = true
    },
    handleReplaceBackUrlSure() {
      this.replaceBackVideoLoading = true
      replaceBackUrl(this.replaceBackUrlForm).then(res => {
        this.replaceBackVideoLoading = false
        this.$message.success('操作成功')
      }).catch(() => { this.replaceBackVideoLoading = false })
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.handleRefreshList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleRefreshList()
    },
    detailSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getDetailUserLive()
        return
      }
      this.detailListQuery.Sorting = prop + ' ' + order
      this.getDetailUserLive()
    },
    getLiveUser() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      liveUserList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    getDetailUserLive() {
      this.detailListLoading = true
      this.detailListQuery.SkipCount =
        (this.detailListQuery.page - 1) * this.detailListQuery.MaxResultCount
      liveUserBackRecord(this.detailListQuery)
        .then((res) => {
          this.detailList = res.items
          this.detailListQuery.totalCount = res.totalCount
          this.detailListLoading = false
        })
        .catch(() => {
          this.detailListLoading = false
        })
    },
    getLiveDetailInfo() {
      liveDetailInfo(this.$route.query.id).then((res) => {
        this.form.title = res.title
        this.form.description = res.description
        this.form.startTime = res.startTime
        this.form.endTime = res.endTime
        this.form.timeLong = res.timeLong
        this.form.lecturer = res.lecturer
        this.form.userCount = res.userCount
        this.form.closeDate = res.closeDate
        this.form.classHour = res.classHour
        this.form.passDuration = res.passDuration
        this.form.canExamAfterLearn = res.canExamAfterLearn
        this.form.passExamGetHour = res.passExamGetHour

        this.replaceBackUrlForm.backUrl = res.backUrl
      })
    },
    loadLiveUserRecords() {
      liveUserRecord_bjy(this.liveId).then((res) => {
        this.$message.success('数据同步中，稍后刷新页面查看')
      })
    },
    updateClassHour() {
      updateClassHour(this.liveId).then((res) => {
        this.$message.success('数据同步中，稍后刷新页面查看')
      })
    },
    publicLiveExam() {
      publishLiveExam(this.liveId).then((res) => {
        this.$message.success('发布成功')
      })
    },
    loadLiveUserBackRecords() {
      // var start = moment(new Date()).subtract(1, 'months').format('YYYY-MM-DD HH:mm')
      // var start = '2023-2-24 00:00:00'
      // var end = '2023-2-24 23:59:59'
      var start = moment(new Date()).format('YYYY-MM-DD 00:00')
      var end = moment(new Date()).format('YYYY-MM-DD HH:mm')
      var data = {
        IsAuto: false,
        LiveStreamId: this.liveId,
        StartTime: start,
        EndTime: end
      }
      liveUserBackRecord_bjy(data).then((res) => {
        this.$message.success('数据同步中，稍后刷新页面查看')
      })
    },
    generatePureVideo() {
      generatePureVideo_bjy(this.liveId).then((res) => {
        if (res.code === 0) {
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getPureVideo() {
      getPureVideo_bjy(this.liveId).then((res) => {
        if (res.code === 0) {
          this.liveBackVideoUrl = res.data.url
          this.showBackUrlDialog = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    cacheFindParent(id) {
      if (this.cacheDate.get(id)) {
        return this.cacheDate.get(id)
      }
      return this.findParent(id)
    },
    async loadClass() {
      const res = await orgsData()
      // this.orgDatas = res.items
      this.map = new Map()
      res.items.forEach(item => {
        this.map.set(item.id, item)
      })
    },
    // findParent(id) {
    //   const result = []
    //   if (!this.map) {
    //     return []
    //   }
    //   const d = this.map.get(id)
    //   if (d) {
    //     result.unshift(d?.displayName)
    //   }
    //   if (d?.parentId) {
    //     let next = this.map.get(d.parentId)
    //     while (next) {
    //       result.unshift(next.displayName)
    //       next = this.map.get(next.parentId)
    //     }
    //   }
    //   this.cacheDate.set(id, result.join('/'))
    //   return result.join('/')
    // }
    findParent(id) {
      const result = []
      if (!this.map) {
        return []
      }
      const d = this.map.get(id)
      if (d) {
        result.unshift(d?.displayName)
      }
      if (d?.parentId) {
        let next = this.map.get(d.parentId)
        while (next) {
          result.unshift(next.displayName)
          next = this.map.get(next.parentId)
        }
      }
      this.cacheDate.set(id, result)
      return result
    },
    getLiveExam() {
      var data = {
        Filter: '',
        LiveId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10
      }
      liveExamInfo(data).then(res => {
        if (res.totalCount > 0) {
          this.hasExam = true
        }
      })
    },
    handerDeleteUnJoinLiveUsers() {
      this.$confirm('仅支持删除回放已禁用且直播结束超过15天的数据,是否确定删除? ', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUnJoinLiveUsers(this.liveId).then(res => {
          this.$message.success('删除成功')
          this.handleRefreshList()
        })
      })
    }
  }
}
</script>
<style scoped>
.el-descriptions ::v-deep .descriptions_label {
  width: 180px;
}
</style>

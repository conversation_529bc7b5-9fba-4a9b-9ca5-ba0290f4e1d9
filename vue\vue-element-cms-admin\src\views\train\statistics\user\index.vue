<template>
  <div>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <el-button v-loading="exportLoading" :disabled="exportLoading" round size="small" type="primary" icon="el-icon-download" @click="handleExport">导出</el-button>
    <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
      <el-table-column label="学员姓名" prop="name" sortable="name" width="140" />
      <el-table-column label="用户名" prop="userName" sortable="userName" width="140" />
      <!-- <el-table-column label="部门" prop="className" sortable="className" show-overflow-tooltip /> -->
      <el-table-column label="部门" prop="className" sortable="className" min-width="150" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ cacheFindParent(row.classId).join('/') }}
        </template>
      </el-table-column>
      <el-table-column label="学员类型" prop="trainUserType" width="100">
        <template slot-scope="{row}">
          <span> {{ row.trainUserType ? '临时' : '正式' }} </span>
        </template>
      </el-table-column>
      <el-table-column label="签到次数" prop="signCount" width="100">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewSignRecord(row)">{{ row.signCount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学时" prop="classHour" width="100">
        <template slot-scope="{row}">
          <span>{{ row.requiredCourseClassHour + row.electiveCourseClassHour +
            row.offLineCourseClassHour + row.liveCourseClassHour
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="必修课学时" prop="requiredCourseClassHour" width="120">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewCourseRecord(true, row)">{{ row.requiredCourseClassHour }}</span>
        </template>
      </el-table-column>
      <el-table-column label="选修课学时" prop="electiveCourseClassHour" width="120">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewCourseRecord(false, row)">{{ row.electiveCourseClassHour }}</span>
        </template>
      </el-table-column>
      <el-table-column label="直播课学时" prop="liveCourseClassHour" width="120">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewLiveRecord(row)">{{ row.liveCourseClassHour ?
            row.liveCourseClassHour : 0
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="线下课学时" prop="offLineCourseClassHour" width="120">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewOffLineCourse(row)">{{ row.offLineCourseClassHour }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训考核" prop="examPass" sortable="examPass" width="120">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleExamList(row)">{{ hasExam?(row.examPass ? '合格' : '不合格'):'无' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getTrainUser"
    />

    <!-- 必修课课程记录 -->
    <el-dialog v-if="courseRecordDialog" title="课程学习记录" :visible.sync="courseRecordDialog" top="5vh" width="1200px">
      <course-record :list-query="recordListQuery" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="courseRecordDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 签到记录 -->
    <el-dialog v-if="signRecordDialog" title="签到记录详情" :visible.sync="signRecordDialog" top="5vh" width="1200px">
      <train-sign :user-id="userId" :train-id="trainId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="signRecordDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 线下课程 记录 -->
    <el-dialog v-if="offLineCourseDialog" title="线下课程记录详情" :visible.sync="offLineCourseDialog" top="5vh" width="1200px">
      <train-offline :user-id="userId" :train-id="trainId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="offLineCourseDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 考核列表 -->
    <el-dialog v-if="examListDialog" title="考核列表" :visible.sync="examListDialog" top="5vh" width="1200px">
      <train-exam-list :train-id="trainId" :user-info="userInfo" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="examListDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 直播课程记录 -->
    <el-dialog v-if="liveDetailDialog" title="直播课程记录详情" :visible.sync="liveDetailDialog" top="5vh" width="1200px">
      <train-live :train-id="trainId" :user-id="userId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="liveDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsUserRecord, trainsUserLiveClassHour } from '@/api/train'
import Pagination from '@/components/Pagination'
import CourseRecord from './course.vue'
import TrainSign from './sign.vue'
import TrainOffline from './offline.vue'
import TrainExamList from './examList.vue'
import TrainLive from './live.vue'
import { orgsData } from '@/api/user'
export default {
  name: 'TrainUser',
  components: {
    Pagination,
    CourseRecord,
    TrainSign,
    TrainOffline,
    TrainExamList,
    TrainLive
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    },
    trainName: {
      required: false,
      type: String,
      default: '培训详情'
    },
    hasExam: {
      required: false,
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: '',
        TrainId: this.trainId,
        IsAll: false,
        page: 1,
        totalCount: 0
      },

      // 必修 选修课程Dialog
      courseRecordDialog: false,
      recordListQuery: {
        IsRequired: true,
        UserId: '',
        TrainId: this.trainId
      },

      userId: '',
      userInfo: {},

      signRecordDialog: false,

      liveDetailDialog: false,

      offLineCourseDialog: false,

      examListDialog: false,
      examData: null,

      exportLoading: false,

      map: null,
      cacheDate: new Map()
    }
  },
  created() {
    this.loadClass()
    this.getTrainUser()
  },
  methods: {
    handleViewSignRecord(row) {
      this.userId = row.userId
      this.signRecordDialog = true
    },
    handleViewCourseRecord(t, row) {
      this.recordListQuery.IsRequired = t
      this.recordListQuery.UserId = row.userId
      this.courseRecordDialog = true
    },
    handleViewLiveRecord(row) {
      this.userId = row.userId
      this.liveDetailDialog = true
    },
    handleViewOffLineCourse(row) {
      this.userId = row.userId
      this.offLineCourseDialog = true
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getTrainUser()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getTrainUser()
    },
    handleExamList(row) {
      this.userInfo.userId = row.userId
      this.userInfo.userName = row.userName
      this.userInfo.name = row.name
      this.userInfo.className = row.className
      this.userId = row.userId
      this.examListDialog = true
    },
    async handleExport() {
      this.exportLoading = true
      var allList = []
      const count = 100
      const len = this.listQuery.totalCount % count === 0 ? this.listQuery.totalCount / count : (Math.floor(this.listQuery.totalCount / count) + 1)
      for (let i = 0; i < len; i++) {
        var query = {
          Filter: '',
          SkipCount: i * count,
          MaxResultCount: count,
          Sorting: '',
          TrainId: this.trainId,
          IsAll: false,
          page: 1,
          totalCount: 0
        }
        var tmp = []
        const res = await trainsUserRecord(query)
        tmp = res.items
        var userIds = []
        res.items.forEach(item => {
          userIds.push(item.userId)
        })
        const classHourRes = await trainsUserLiveClassHour({ UserIds: userIds, TrainId: this.trainId })
        tmp.forEach((item, i) => {
          classHourRes.items.forEach(citem => {
            if (item.userId === citem.userId) {
              this.$set(tmp, i, { ...item, className1: this.cacheFindParent(item.classId).length ? this.cacheFindParent(item.classId)[0] : '',
                className2: this.cacheFindParent(item.classId).length > 1 ? this.cacheFindParent(item.classId)[1] : '',
                className3: this.cacheFindParent(item.classId).length > 2 ? this.cacheFindParent(item.classId)[2] : '',
                liveCourseClassHour: citem.classHours,
                classHour: item.requiredCourseClassHour + item.electiveCourseClassHour + citem.classHours + item.offLineCourseClassHour,
                examPass: this.hasExam ? (item.examPass ? '合格' : '不合格') : '无',
                trainUserType: item.trainUserType ? '临时' : '正式' })
            }
          })
        })
        allList = allList.concat(res.items)
      }
      this.exportLoading = false
      console.log(allList)
      import('@/vendor/Export2Excel').then((excel) => {
        const tHeader = ['姓名', '用户名', '一级部门', '二级部门', '三级部门', '学员类型', '签到次数', '学时', '必修课学时', '选修课学时', '直播课学时', '线下课学时', '培训考核']
        const filterVal = ['name', 'userName', 'className1', 'className2', 'className3', 'trainUserType', 'signCount', 'classHour', 'requiredCourseClassHour', 'electiveCourseClassHour', 'liveCourseClassHour', 'offLineCourseClassHour', 'examPass']
        // 成绩列表数据
        // const list = Response.items;
        const data = this.formatJson(filterVal, allList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.trainName + '_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    async getTrainUser() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      try {
        const res = await trainsUserRecord(this.listQuery)
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        var userIds = []
        res.items.forEach(item => {
          userIds.push(item.userId)
        })
        const classHourRes = await trainsUserLiveClassHour({ UserIds: userIds, TrainId: this.trainId })
        this.list.forEach((item, i) => {
          classHourRes.items.forEach(citem => {
            if (item.userId === citem.userId) {
              this.$set(this.list, i, { ...item, liveCourseClassHour: citem.classHours })
            }
          })
        })
        this.listLoading = false
      } catch {
        this.listLoading = false
      }
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getTrainUser()
    },
    cacheFindParent(id) {
      if (this.cacheDate.get(id)) {
        return this.cacheDate.get(id)
      }
      return this.findParent(id)
    },
    async loadClass() {
      const res = await orgsData()
      // this.orgDatas = res.items
      this.map = new Map()
      res.items.forEach(item => {
        this.map.set(item.id, item)
      })
    },
    // findParent(id) {
    //   const result = []
    //   if (!this.map) {
    //     return []
    //   }
    //   const d = this.map.get(id)
    //   if (d) {
    //     result.unshift(d?.displayName)
    //   }
    //   if (d?.parentId) {
    //     let next = this.map.get(d.parentId)
    //     while (next) {
    //       result.unshift(next.displayName)
    //       next = this.map.get(next.parentId)
    //     }
    //   }
    //   this.cacheDate.set(id, result.join('/'))
    //   return result.join('/')
    // }
    findParent(id) {
      const result = []
      if (!this.map) {
        return []
      }
      const d = this.map.get(id)
      if (d) {
        result.unshift(d?.displayName)
      }
      if (d?.parentId) {
        let next = this.map.get(d.parentId)
        while (next) {
          result.unshift(next.displayName)
          next = this.map.get(next.parentId)
        }
      }
      this.cacheDate.set(id, result)
      return result
    }
  }

}
</script>

<template>
  <div class="app-container">
    <div class="table">
      <el-card shadow="always" class="box-card el-card_header_border_0">
        <div slot="header">
          <!-- <span class="role-span">试卷管理</span> -->
          <!-- <el-select v-model="listQuery.categoryId" size="small" @change="handleRefreshList">
            <el-option v-for="item in categoryList" :key="item.id" :value="item.id">{{ item.title }}</el-option>
          </el-select> -->
          <el-cascader v-model="listQuery.ExamPaperCategoryId" filterable clearable :options="categoryList" :props="{'label': 'name','value': 'id','checkStrictly': true,'emitPath': false}" size="small" style="margin: 0 10px" placeholder="请选择试卷分类..." @change="handleRefreshList" />
          <el-select v-model="listQuery.Tag" filterable clearable size="small" @change="handleRefreshList" placeholder="请选择试卷标签...">
            <el-option v-for="item in tagsList" :key="item.id" :value="item.name">{{ item.name }}</el-option>
          </el-select>
          <el-input v-model="listQuery.Filter" clearable size="small" placeholder="搜索..." style="width: 200px"
            class="filter-item" @input="searchContentChange" @keyup.enter.native="searchHandle" />
          <el-button class="filter-item" size="small" round type="success" icon="el-icon-search" @click="searchHandle">
            搜索</el-button>
          <el-button v-permission="['Exam.ExamPapers.Create']" class="filter-item" size="small" round type="primary"
            icon="el-icon-plus" @click="handleGeneratingPaper">添加
          </el-button>
          <export-excel :header="['试卷编号', '试卷名称', '总分', '题数', '标签','创建日期']"
            :filter-val="['code', 'name', 'totalScore', 'questionNumber','tag', 'creationTime']"
            :field="{  5: [2] }" :apiFn="getExamPaperList" />
        </div>
        <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
          <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
          <el-table-column prop="code" label="试卷编号" sortable="code" width="200">
            <template slot-scope="{row}">
              <span class="link-type" @click="handleCheck(row)">{{
                  row.code
              }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="name" label="试卷名称" sortable="name"  /> -->
          <el-table-column prop="name" label="试卷名称" sortable="name" min-width="200">
            <template slot-scope="{row}">
              <span class="link-type" @click="handleCheck(row)">{{
                  row.name
              }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="totalScore" label="总分" sortable="totalScore" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.totalScore }} 分</span>
            </template>
          </el-table-column>
          <el-table-column prop="questionNumber" label="题数" sortable="questionNumber" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.questionNumber }} 题</span>
            </template>
          </el-table-column>
          <el-table-column prop="tag" label="标签" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.tag }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="creationTime" sortable="creationTime" label="创建日期" width="135px">
            <template slot-scope="scope">
              <span>{{ scope.row.creationTime | formatDatetime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300">
            <template slot-scope="{row}">
              <el-button v-permission="['Exam.ExamPapers.Update']" round type="primary" size="mini" icon="el-icon-edit"
                @click="handleEdit(row)">编辑</el-button>
              <el-button v-permission="['Exam.ExamPapers.Update']" round type="" size="mini" icon="el-icon-edit-outline"
                @click="handleRename(1, row)">重命名</el-button>
              <!-- <el-button
                round
                type=""
                size="mini"
                icon="el-icon-view"
                @click="handleCheck(row)"
              >预览</el-button> -->
              <el-button v-permission="['Exam.ExamPapers.Delete']" round type="danger" size="mini" icon="el-icon-delete"
                @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="totalCount > 0" :total="totalCount" :page.sync="page" :limit.sync="listQuery.MaxResultCount"
          @pagination="getList" />
      </el-card>
    </div>
    <el-dialog :close-on-click-modal="false" class="examPaperEditDialog" title="重命名试卷"
      :visible.sync="examPaperEditDialog" width="35%">
      <div class="tag-type">
        <el-form ref="examinationForm" :model="examinationForm" label-width="100px" :rules="examinationRules">
          <el-form-item label="试卷编号" prop="code">
            <el-input v-model="examinationForm.code" :disabled="true" />
          </el-form-item>
          <el-form-item label="试卷名称" prop="name">
            <el-input v-model="examinationForm.name" />
          </el-form-item>
          <el-form-item label="试卷分类" prop="examPaperCategoryId">
            <el-cascader v-model="examinationForm.examPaperCategoryId" filterable clearable :options="categoryList" :props="{'label': 'name','value': 'id','checkStrictly': true,'emitPath': false}" placeholder="请选择试卷分类..." />
          </el-form-item>
          <el-form-item label="标签">
              <el-select v-model="examinationForm.tag" filterable clearable>
                <el-option v-for="item in tagsList" :key="item.id" :value="item.name">{{ item.name }}</el-option>
              </el-select>
            </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button round size="medium" type="primary" :loading="formLoading" @click="sureBtn">确 定</el-button>
        <el-button round size="medium" @click="examPaperEditDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog v-if="examPaperPreviewDialog" class="examPaperPreviewDialog" title="试卷预览"
      :visible.sync="examPaperPreviewDialog" top="5vh" width="950px">
      <div class="title">
        <span>{{ examPaperDetail.title }}</span>
        <span class="totalscore"> ( 共 {{ examPaperDetail.count }} 题 ，{{ examPaperDetail.totalScore }}分)</span>
      </div>
      <div v-loading="examPaperPreviewLoading">
        <div v-for="(item, index) in examPaperDetail.questionList" :key="item.id">
          <exam-preview :order="index + 1" :data="item" />
        </div>
      </div>
      <!-- <div v-for="(item,index) in examPaperDetail.questionList" :key="item.id">
        <exam-preview :order="index + 1" :data="item" />
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button round size="medium" @click="examPaperPreviewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="alertDialog" width="450px">
      <el-result icon="warning" :sub-title="alertContent" style="padding: 0px">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="alertDialog = false">确 定</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { getExamPaperList, renameExamPaper, deleteExamPaper, examPaperQuestionlist, packageExamHasUse, trainsExamHasUse, examPaperCategoryAll, examPaperTagAll } from '@/api/examPaper'
import ExamPreview from '@/components/ExamPreview'
import permission from '@/directive/permission/index'
// const defaultForm = {
//   id: '',
//   code: '',
//   name: ''
// }
export default {
  name: 'ExamPaper',
  directives: { permission },
  components: {
    Pagination,
    ExamPreview
  },
  data() {
    return {
      categoryList: [],
      tagsList: [],
      list: [],
      listLoading: false,
      // 添加/重命名form
      examinationForm: {
        id: '',
        code: '',
        name: '',
        examPaperCategoryId: '',
        tag: ''
      },
      // 重命名dialog显示/不显示
      examPaperEditDialog: false,
      // 预览试卷
      examPaperPreviewDialog: false,
      // 试卷预览 loading
      examPaperPreviewLoading: false,
      // 试卷预览信息
      examPaperDetail: {
        title: '',
        totalScore: 0,
        count: 0,
        questionList: []
      },
      // 表单请求转圈
      formLoading: false,
      // 分页请求参数
      listQuery: {
        // Filter: '',
        // Sorting: 'creationTime desc',
        // MaxResultCount: 10,
        // SkipCount: 0,
        // totalCount: 0,
        // page: 1
        Filter: '',
        ExamPaperType: '',
        ExamPaperCategoryId: '',
        Tag: '',
        Sorting: 'creationTime desc',
        MaxResultCount: 10,
        SkipCount: 0
      },
      totalCount: 0,
      page: 1,
      // 表单验证
      examinationRules: {
        title: [{
          required: true,
          message: '请输入试卷标题',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }]
      },
      alertDialog: false,
      alertContent: '该试卷已被使用，无法删除'
    }
  },
  created() {
    this.getList()
    this.getCategoryList()
    this.getTagList()
  },
  methods: {
    getExamPaperList(args) {
      return getExamPaperList(args)
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.page - 1) * this.listQuery.MaxResultCount
      getExamPaperList(this.listQuery).then(res => {
        this.list = res.items
        this.listLoading = false
        this.totalCount = res.totalCount
      }).catch(() => {
        this.listLoading = false
      })
    },
    // 搜索
    searchHandle() {
      this.page = 1
      this.getList()
    },
    // 搜索框清空 请求列表
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    // 编辑试卷
    handleEdit(row) {
      this.$router.push({
        name: 'PaperEdit',
        query: { 'examPaperId': row.id }
      })
    },
    // 组卷
    handleGeneratingPaper(row) {
      this.$router.push({
        name: 'GeneratingPaper'
        // query: { 'code': this.getAssessmentCode() }
      })
    },
    // 重命名
    handleRename(t, row) {
      if (this.examinationForm) {
        this.restForm()
        // this.$refs.examinationForm.clearValidate()
      }
      this.examinationForm = JSON.parse(JSON.stringify(row))
      this.examPaperEditDialog = true
    },
    // 预览
    async handleCheck(row) {
      this.examPaperDetail.questionList = []
      this.examPaperPreviewLoading = true
      this.examPaperPreviewDialog = true
      this.examPaperDetail.title = row.name
      this.examPaperDetail.count = row.questionNumber
      this.examPaperDetail.totalScore = row.totalScore
      var form = {
        ExamPaperId: row.id,
        Filter: '',
        Sorting: '',
        MaxResultCount: 900,
        SkipCount: 0
      }
      const res = await examPaperQuestionlist(form)
      this.examPaperPreviewLoading = false
      this.examPaperDetail.questionList = res.items
    },
    // 删除
    handleDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const a = await packageExamHasUse(row.id)
        if (a) {
          this.alertContent = '该试卷已被课程使用，无法删除'
          this.alertDialog = true
          return
        }
        const b = await trainsExamHasUse(row.id)
        if (b) {
          this.alertContent = '该试卷已被培训包使用，无法删除'
          this.alertDialog = true
          return
        }
        deleteExamPaper(row.id).then(res => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        }).catch(() => {
          this.$notify({
            title: '失败',
            message: '删除失败',
            type: 'error',
            duration: 2000
          })
        })
      }).catch(() => { })
    },
    // 添加重命名表单提交
    sureBtn() {
      this.$refs.examinationForm.validate((valid) => {
        if (valid) {
          this.formLoading = true
          renameExamPaper(this.examinationForm).then(res => {
            this.formLoading = false
            this.$notify({
              title: '成功',
              message: '重命名成功',
              type: 'success',
              duration: 2000
            })
            this.examPaperEditDialog = false
            this.getList()
          })
        } else {
          return false
        }
      })
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.page = 1
      this.getList()
    },
    restForm() {
      this.examinationForm = {
        id: '',
        code: '',
        name: ''
      }
    },
    getTagList() {
      examPaperTagAll().then(res => {
        this.tagsList = res.items
      })
    },
    async getCategoryList() {
      const res = await examPaperCategoryAll()

      this.categoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    }
  }

}

</script>

<style scoped>
.examPaperPreviewDialog ::v-deep .el-dialog__body {
  padding: 30px 40px;
}

.examPaperPreviewDialog .title {
  text-align: center;
  height: 50px;
  font-size: 16px;
  font-weight: bold;
}

.examPaperPreviewDialog .totalscore {
  font-size: 14px;
  font-weight: initial;
}
</style>

<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 16:04:12
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-01 10:40:35
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="margin-top: 20px">

    <el-descriptions title="学习情况" />
    <el-tabs v-model="tabActiveName">
      <el-tab-pane label="培训学员" name="user">
        <train-user :train-id="trainId" :train-name="trainName" :has-exam="hasExam" />
      </el-tab-pane>
      <el-tab-pane label="培训考核" name="exam">
        <train-exam :train-id="trainId" @examResponse="getExamCount" />

      </el-tab-pane>
      <el-tab-pane label="培训课程" name="course">
        <train-course :train-id="trainId" @courseResponse="getCourseClassHour" />
      </el-tab-pane>
      <el-tab-pane label="直播课程" name="live">
        <train-live :train-id="trainId" @liveResponse="getLiveClassHour" />
      </el-tab-pane>
      <el-tab-pane label="线下课程" name="offline">
        <train-offline-course :train-id="trainId" />
      </el-tab-pane>
      <el-tab-pane label="线下考核" name="offlineExam">
        <train-offline-exam :train-id="trainId" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import TrainUser from './user'
import TrainExam from './exam'
import TrainCourse from './course'
import TrainLive from './live'
import TrainOfflineCourse from './offline'
import TrainOfflineExam from './offlineExam'
export default {
  name: 'TStatistics',
  components: {
    TrainUser,
    TrainExam,
    TrainCourse,
    TrainLive,
    TrainOfflineCourse,
    TrainOfflineExam
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    },
    trainName: {
      required: false,
      type: String,
      default: '培训详情'
    }
  },
  data() {
    return {
      tabActiveName: 'user',
      liveClassHour: 0,
      courseClassHour: 0,
      hasExam: false
    }
  },
  created() {

  },
  methods: {
    getLiveClassHour(val) {
      this.liveClassHour = val
      this.$emit('totalResponse', this.liveClassHour + this.courseClassHour)
    },
    getCourseClassHour(val) {
      this.courseClassHour = val
      this.$emit('totalResponse', this.liveClassHour + this.courseClassHour)
    },
    getExamCount(val) {
      if (val > 0) { this.hasExam = true }
    }

  }
}

</script>
<style lang="scss" scoped>
</style>

﻿html {
    height: 100%;
}

body {
    height: 100%;
    width: 100%;
    margin: 0;
    background: #f3f3f3;
    color: #484848;
    font-size: 14px;
    line-height: 1.5;
}

textarea {
    resize: none;
}

.head_nav {
    z-index: 2;
}

.editInfo .editCon .CodeMirror {
    height: 100%;
    font-family: -apple-system,BlinkMacSystemFont,Helvetica Neue,PingFang SC,Microsoft YaHei,Source Han Sans SC,Noto Sans CJK SC,WenQuanYi Micro Hei,sans-serif;
}

.CodeMirror .text-to-survey-editor-error {
    color: #ef6262;
    background: #fce0e0;
    text-indent: 20px;
}

.CodeMirror pre {
    padding: 0 20px;
    /* height: 21px; */
    /*overflow: hidden;*/
}

.CodeMirror-focused .CodeMirror-selected {
    background: #ddedfd;
}

.textProject {
    position: absolute;
    top: 0;
    right: 1%;
    left: 1%;
    bottom: 0;
}

.editInfo {
    position: absolute;
    background: #fff;
    width: 45%;
    top: 0;
    left: 0;
    bottom: 0;
}

    .editInfo .edit_head {
        position: absolute;
        right: 10px;
        top: 0;
        left: 10px;
        height: 52px;
        font-size: 16px;
        line-height: 52px;
    }

        .editInfo .edit_head .help_bt {
            color: #53a4f4;
            display: inline-block;
            height: 52px;
            line-height: 52px;
            font-size: 16px;
            float: right;
            margin-top:16px;
        }

    .editInfo .editCon {
        position: absolute;
        left: 10px;
        right: 10px;
        top: 52px;
        bottom: 60px;
        border: 1px solid #ccc;
    }

    .editInfo .edit_bottom {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 68px;
        text-align:center;
    }

        .editInfo .edit_bottom a {
            /*position: absolute;*/
            height: 36px;
            line-height: 36px;
            right: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            padding: 0 40px;
            display: inline-block;
            color: #fff;
            background: #53a4f4;
            margin-top:20px;
        }

            .editInfo .edit_bottom a:hover {
                background: #75b6f5;
            }

.previewProj {
    position: absolute;
    background: #fff;
    width: 54%;
    top: 0;
    left: 46%;
    bottom: 0;
}

    .previewProj .preview_head {
        position: absolute;
        right: 10px;
        top: 0;
        left: 0;
        height: 52px;
        line-height: 52px;
        background: url('image/create_yl.png') no-repeat left top;
    }

    .previewProj .previewCon {
        position: absolute;
        top: 52px;
        bottom: 0;
        left: 0;
        right: 0;
        overflow: auto;
        padding: 0 10% 5%;
    }

        .previewProj .previewCon h2 {
            font-size: 24px;
            text-align: center;
        }

        .previewProj .previewCon .question_model {
            margin-top: 32px;
        }

        .previewProj .previewCon .labelInfo {
            height: 28px;
            line-height: 32px;
            cursor: pointer;
        }

.QUESTION_TYPE_SINGLE i {
    display: inline-block;
    background: transparent url(image/radio.png) no-repeat center top;
    vertical-align: middle;
    height: 16px;
    width: 18px;
    margin: 4px 5px 3px 0;
}

.QUESTION_TYPE_JUDGE i {
    display: inline-block;
    background: transparent url(image/radio.png) no-repeat center top;
    vertical-align: middle;
    height: 16px;
    width: 18px;
    margin: 4px 5px 3px 0;
    cursor:pointer
}


.QUESTION_TYPE_MULTIPLE i {
    display: inline-block;
    background: transparent url(image/checkbox.png) no-repeat center top;
    vertical-align: middle;
    height: 16px;
    width: 18px;
    margin: 4px 5px 5px 0;
}

.QUESTION_TYPE_BLANK input {
    border-radius: 4px;
    border: 1px solid #ccc;
    width: 260px;
    height: 28px;
}

.QUESTION_TYPE_BLANK textarea {
    border-radius: 4px;
    border: 1px solid #ccc;
    width: 260px;
}

.previewCon .options {
    margin-top: 10px;
}

    .previewCon .options .option {
        text-indent: 32px;
    }

    .previewCon .options i.active {
        background-position: center bottom;
    }

/*---help---*/
.helpBox {
    display: none;
}

.helpCon {
    padding: 20px 32px;
    overflow: auto;
    height: 480px;
}

    .helpCon .part h2 {
        font-size: 18px;
        height: 22px;
        line-height: 22px;
        margin-bottom: 20px;
        color:blue
    }

    .helpCon .part1 p {
        padding: 0 20px;
    }

    .helpCon .tIndent {
        display: block;
        text-indent: 20px;
        line-height: 22px;
    }

    .helpCon .part2 {
        margin-top: 20px;
    }

table, th, td {
    border: 1px solid #ccc;
}

.helpCon .part2 table {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
}

    .helpCon .part2 table th {
        height: 50px;
        background: #ddedfd;
    }

.helpCon .part2 .tLeft {
    padding: 20px;
    text-align: left;
}

.helpCon .part2 .qTitle {
    margin-bottom: 10px;
}


 .helpContxt {
    display: block;
    line-height: 30px;
    font-weight: 600;
    font-size: 16px;
    margin-top: 10px;
    background: #e9e9e9;
    margin-left: 30px
 }

 .helpConimg{
     margin: 0 95px 10px;
 }

 
 .helpContxtline {
        display: block;
        margin-left: 30px;
        line-height: 22px;
        margin-top: 10px;
    }
.previewProj .previewCon .question_model .text{
    text-indent: 20px;
    word-break: break-all;
}
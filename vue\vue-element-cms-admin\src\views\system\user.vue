<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="8" :md="8" :lg="6" :xl="4">
        <el-card class="box-card" style="max-height: 800px;overflow: auto;">
          <!-- <div class="head-container">
          <el-input
            v-model="orgName"
            clearable
            size="small"
            placeholder="搜索..."
            prefix-icon="el-icon-search"
            @input="getOrgs"
          />
        </div> -->
          <el-button type="text" style="margin-left: 23px" @click="handleNodeClick()">
            全部
          </el-button>
          <el-tree
            :data="orgDatas"
            :props="defaultProps"
            highlight-current
            style="margin-top: 5px"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="16" :md="16" :lg="18" :xl="20">
        <el-card>
          <!--工具栏-->
          <div class="header_flex_box">
            <!-- 搜索 -->
            <el-input
              v-model="listQuery.Filter"
              clearable
              size="small"
              placeholder="搜索..."
              class="small_input"
              @keyup.enter.native="handleFilter"
            />
            <el-button round size="mini" type="success" icon="el-icon-search" @click="handleFilter">搜索</el-button>
            <el-button
              v-permission="['AbpIdentity.Users.Create']"
              round
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="handleCreate"
            >新增用户</el-button>
            <!-- <el-button
            round
            size="mini"
            type="success"
            icon="el-icon-edit"
            v-permission="['AbpIdentity.Users.Update']"
            @click="handleUpdate()"
            >修改</el-button
          > -->

            <el-button
              v-permission="['AbpIdentity.Users.Create']"
              round
              size="mini"
              icon="el-icon-top"
              type="primary"
              style="margin-left: 10px"
              @click="openImportdialog"
            >批量导入</el-button>
            <el-button
              v-permission="['AbpIdentity.Users.Create']"
              :loading="exportLoading"
              round
              size="mini"
              icon="el-icon-bottom"
              type="primary"
              style="margin-left: 10px"
              @click="exportAllUser"
            >导出用户</el-button>

            <div class="opts">
              <el-dialog
                :visible.sync="dialogFormVisibleUpload"
                :close-on-click-modal="false"
                title="导入用户"
                width="800px"
                @close="closeImportdialog()"
              >
                <el-steps :active="importUserStep">
                  <el-step title="下载模板" />
                  <el-step title="上传Excel" />
                  <el-step title="验证用户" />
                </el-steps>

                <div v-if="importUserStep === 1" class="step">
                  <el-button round size="mini" icon="el-icon-bottom" type="primary" style="margin-top: 20px"><a
                    type="primary"
                    href="/用户导入模板.xlsx"
                  >下载模板</a></el-button>

                  <div class="htitle">
                    提示：请先下载模板，录入用户信息后，再点击
                    “下一步”上传Excel
                  </div>
                </div>
                <div v-if="importUserStep === 2" class="step">
                  <el-upload
                    round
                    class="upload-demo"
                    action=""
                    :on-change="handleChange"
                    :on-remove="handleRemove"
                    :on-exceed="handleExceed"
                    :limit="limitUpload"
                    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                    :auto-upload="false"
                  >
                    <el-button round size="mini" icon="el-icon-top" type="primary" style="margin-top: 20px">上传Excel
                    </el-button>
                  </el-upload>
                  <div class="htitle">
                    提示：Excel用户信息录入时,请勿更改模板格式
                  </div>
                </div>
                <div v-if="importUserStep === 3" class="step">
                  <div class="htitle">
                    验证用户信息
                    <div v-if="!importVaildSuccess" style="float: right">
                      <el-upload
                        v-if="!vaildLoading"
                        round
                        class="upload-demo"
                        action=""
                        :on-change="handleChange"
                        :on-remove="handleRemove"
                        :on-exceed="handleExceed"
                        :limit="limitUpload"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                        :auto-upload="false"
                      >
                        <el-button round size="mini" icon="el-icon-top" type="primary">重新上传Excel</el-button>
                      </el-upload>
                    </div>
                  </div>
                  <!--v-if="!importVaildSuccess" v-else -->
                  <el-main v-if="!importVaildSuccess">
                    <el-table v-loading="vaildLoading" :data="errorUsers" style="color: red" height="400">
                      <el-table-column prop="rowNum" label="行号" />
                      <el-table-column prop="userName" label="用户" />
                      <el-table-column prop="phoneNumber" label="手机号码" />
                      <el-table-column prop="email" label="邮箱" />
                      <el-table-column prop="error" label="验证信息" />
                    </el-table>
                  </el-main>
                  <div v-else>
                    <div style="color: #4caf50; font-size: 16px; margin: 18px 0">
                      <i class="el-icon-circle-check" />验证用户信息通过
                      <div style="float: right">
                        <el-button round size="mini" type="primary" :loading="!importVaildSuccess" @click="submitUsers">
                          点击开始导入用户</el-button>
                      </div>
                    </div>
                    <div>
                      导入进度
                      <el-progress :percentage="submitProgress" />
                    </div>
                  </div>
                </div>
                <div slot="footer" class="dialog-footer">
                  <el-button
                    v-if="errorUsers.length || importUserStep === 2"
                    round
                    style="margin-top: 12px"
                    @click="prevStep"
                  >上一步</el-button>
                  <el-button v-if="importUserStep < 2" round style="margin-top: 12px" @click="nextStep">下一步</el-button>
                  <el-button round @click="closeImportdialog">关闭</el-button>
                </div>
              </el-dialog>
            </div>
          </div>

          <!--表格渲染-->
          <el-table
            ref="multipleTable"
            v-loading="listLoading"
            :data="list"
            style="width: 100%"
            stripe
            @sort-change="sortChange"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="44px" />
            <el-table-column label="用户名" prop="userName" sortable="custom" width="160px">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleUpdate(row)">{{
                  row.userName
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="姓名" prop="name" sortable="custom">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工号" prop="studentIDNumber" sortable="custom">
              <template slot-scope="scope">
                <span>{{ scope.row.studentIDNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" prop="extraProperties.OUName" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.extraProperties.OUName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位" prop="extraProperties.Position" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.extraProperties.Position }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="extraProperties.Remarks" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.extraProperties.Remarks }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              label="工号"
              prop="studentIDNumber"
              sortable="custom"

            >
              <template slot-scope="scope">
                <span>{{ scope.row.studentIDNumber }}</span>
              </template>
            </el-table-column> -->
            <!-- <el-table-column
            label="邮箱"
            prop="email"
            sortable="custom"

            width="200px"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.email }}</span>
            </template>
          </el-table-column> -->
            <el-table-column label="手机号码" prop="phoneNumber" sortable="custom" width="120px">
              <template slot-scope="scope">
                <span>{{ scope.row.phoneNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="角色" prop="role" width="120px">
              <template slot-scope="scope">
                <span>{{ roleTransformation(scope.row.extraProperties.Roles) }}</span>
                <!-- <span>{{ scope.row.extraProperties.Roles=='student'?'学生':(
                  scope.row.extraProperties.Roles=='teacher'?'老师':(
                    scope.row.extraProperties.Roles=='admin'?'管理员':scope.row.extraProperties.Roles
                  )
                ) }}</span> -->
              </template>
            </el-table-column>
            <el-table-column label="禁用状态" prop="isLockedOut" width="100px">
              <template slot-scope="scope">
                <span>{{ scope.row.isLockedOut ? "已禁用" : "未禁用" }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="creationTime" sortable="custom" label="创建日期" width="160px">
              <template slot-scope="scope">
                <span>{{ scope.row.creationTime | formatDateTime }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="lastModificationTime"
              sortable="custom"
              label="修改日期"
              width="160px"
            >
              <template slot-scope="scope">
                <span>{{
                  scope.row.lastModificationTime | formatDateTime
                }}</span>
              </template>
            </el-table-column> -->
            <el-table-column label="操作" width="300">
              <template slot-scope="{ row }">
                <el-button
                  v-if="!row.isLockedOut"
                  v-permission="['AbpIdentity.Users.Update']"
                  round
                  type="warning"
                  size="mini"
                  icon="el-icon-lock"
                  @click="lockUser(row.id)"
                >禁用</el-button>
                <el-button
                  v-if="row.isLockedOut"
                  v-permission="['AbpIdentity.Users.Update']"
                  round
                  type="success"
                  size="mini"
                  icon="el-icon-unlock"
                  @click="unLockUser(row.id)"
                >解禁</el-button>
                <el-button
                  v-permission="['AbpIdentity.Users.Update']"
                  round
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  @click="handleUpdate(row)"
                >编辑</el-button>
                <el-button
                  v-permission="['AbpIdentity.Users.Delete']"
                  round
                  type="danger"
                  size="mini"
                  :disabled="row.userName === 'admin'"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="totalCount > 0"
            :total="totalCount"
            :page.sync="page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog title="注册二维码" :visible.sync="registQRCodeDialog" width="500px">
      <qr-code-make v-if="registQRCodeDialog" :qr-url="registUrl" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="registQRCodeDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!--表单渲染-->
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :title="formTitle"
      width="700px"
      @close="cancel()"
    >
      <el-form ref="form" :inline="true" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" :disabled="isEdit" style="width: 225px" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input v-model.number="form.phoneNumber" style="width: 225px" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" style="width: 225px" />
        </el-form-item>
        <!-- <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" style="width: 225px" />
              </el-form-item> -->
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" autocomplete="new-password" style="width: 225px" />
        </el-form-item>
        <el-form-item label="身份证号" prop="indentityCode">
          <el-input v-model="form.indentityCode" style="width: 225px" />
        </el-form-item>
        <el-form-item label="性别" prop="surname">
          <el-select v-model="form.surname" placeholder="请选择性别" style="width: 225px">
            <el-option label="男" value="0" />
            <el-option label="女" value="1" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="学籍号" prop="studentNumber">
                <el-input v-model="form.studentNumber" style="width: 225px" />
              </el-form-item> -->
        <el-form-item label="工号" prop="studentIDNumber">
          <el-input v-model="form.studentIDNumber" style="width: 225px" />
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-select v-model="checkedRole" multiple placeholder="请选择" style="width: 225px">
            <el-option v-for="item in roleList" :key="item.name" :label="item.displayName" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="ouId">
          <!-- <treeselect
                  v-model="form.ouId"
                  :options="orgs"
                  style="width: 225px"
                  placeholder="选择班级"
                /> -->
          <el-cascader
            ref="classCascader"
            v-model="classValue"
            filterable
            style="width: 225px"
            :options="classData"
            :props="cascaderProps"
            :show-all-levels="false"
            clearable
            placeholder="请选择..."
            @change="handleClassChange"
          />
          <!-- <el-cascader
            ref="classCascader"
            v-model="classValue"
            filterable
            clearable
            style="width: 225px"
            :options="classData"
            :show-all-levels="false"
            @change="handleClassChange"
          /> -->
        </el-form-item>
        <!-- <el-form-item label="允许禁用">
          <el-radio-group v-model="form.lockoutEnabled" style="width: 225px">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="岗位">
          <el-input v-model="form.extraProperties.Position" style="width: 225px" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.extraProperties.Remarks" type="textarea" style="width: 225px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="formLoading" round type="primary" @click="save">确认</el-button>
        <el-button round @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect'
import { getAllStudents, getFilterUsers, orgsData } from '@/api/user'
import QrCodeMake from '@/components/QrCodeMake'

const defaultForm = {
  id: '',
  ouId: null,
  ouName: '',
  userName: '',
  phoneNumber: '',
  name: '',
  email: '',
  password: '',
  lockoutEnabled: true,
  roleNames: [],
  // jobs:[],
  orgIdToName: null,
  surname: '',
  remarks: '',
  position: '',
  extraProperties: {
    Remarks: '',
    Position: ''
  }
}

export default {
  name: 'User',
  components: {
    Pagination,
    QrCodeMake
  },
  directives: {
    permission
  },
  data() {
    const validateRoles = (rule, value, callback) => {
      if (this.checkedRole == null || this.checkedRole.length === 0 || this.checkedRole === undefined) {
        callback(new Error('请选择角色'))
      } else {
        callback()
      }
    }

    return {
      cascaderProps: {
        label: 'displayName',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      registQRCodeDialog: false,
      registUrl: '',
      limitUpload: 1,
      fileTemp: null,
      file: null,
      da: [],
      rules: {
        userName: [
          {
            required: true,
            message: '请输入用户名',
            trigger: 'blur'
          },
          {
            min: 4,
            max: 20,
            message: '长度在 4 到 20 个字符',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入用户姓名',
            trigger: 'blur'
          },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur'
          }
        ],
        // phoneNumber: [{
        //   required: true,
        //   validator: validatePhoneNumber,
        //   trigger: 'blur'
        // }],
        password: [
          {
            required: false,
            message: '请输入密码',
            trigger: 'blur'
          },
          {
            min: 6,
            max: 20,
            message: '长度在 6 到 20 个字符',
            trigger: 'blur'
          }
        ],
        // surname: [
        //   { required: true, message: '请选择性别', trigger: 'change' }
        // ],
        // ouId: [
        //   { required: true, message: '请选择班级', trigger: 'change' }
        // ],
        roles: [
          { required: true, validator: validateRoles, trigger: 'change' }
        ]
        // email: [
        //   {
        //     required: true,
        //     message: '请输入邮箱地址',
        //     trigger: 'blur'
        //   },
        //   {
        //     type: 'email',
        //     message: '请输入正确的邮箱地址',
        //     trigger: 'blur'
        //   }
        // ]
      },
      defaultProps: {
        children: 'children',
        label: 'label'
        // isLeaf: "leaf"
      },
      form: Object.assign({}, defaultForm),
      list: null,
      orgName: '',
      orgs: [],
      // jobData:[],
      orgDatas: [],
      roleList: [],
      checkedRole: [],
      totalCount: 0,
      listLoading: true,
      formLoading: false,
      listQuery: {
        OuId: null,
        GetChildren: true,
        Filter: '',
        Sorting: 'creationtime desc',
        SkipCount: 0,
        MaxResultCount: 10
      },
      page: 1,
      dialogFormVisible: false,
      multipleSelection: [],
      formTitle: '',
      isEdit: false,
      isAdmin: false,
      errorUsers: [],
      vaildLoading: false,
      importVaildSuccess: false,
      dialogFormVisibleUpload: false, // 导入用户弹框
      importUserStep: 1,
      treeData: [],
      successUsers: [],
      submitingUser: [],
      submitedUserCount: 0,
      submitProgress: 0,

      classData: [],
      classValue: [],

      exportLoading: false,

      map: null,
      cacheDate: new Map()
    }
  },
  created() {
    this.getList()
    this.getThreeData()
  },
  methods: {
    getOrgs(node) {
      if (node !== '' && node !== null) {
        const rootChildren = []
        this.orgs.forEach((element) => {
          if (element.label.indexOf(node) !== -1) {
            rootChildren.push({
              label: element.label,
              id: element.id
            })
          }
        })
        this.orgDatas = rootChildren
      } else {
        this.orgDatas = this.orgs
      }
    },
    getThreeData() {
      this.$axios
        .gets('/api/appuser/organization/loadNodes')
        .then((response) => {
          this.map = new Map()
          response.items.forEach(item => {
            this.map.set(item.id, item)
          })
          this.treeData = response
          this.loadTree(this.treeData)
          this.loadClassData(response.items)
        })
    },
    loadClassData(res) {
      this.classData = this.deleteChildren(res.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
      // res.forEach((item) => {
      //   if (item.parentId == null) {
      //     this.classData.push({
      //       value: item.id,
      //       label: item.displayName,
      //       children: []
      //     })
      //   }
      // })
      // res.forEach((item) => {
      //   this.classData.forEach((classItem) => {
      //     if (item.parentId != null && item.parentId === classItem.value) {
      //       classItem.children.push({
      //         value: item.id,
      //         label: item.displayName,
      //         children: undefined
      //       })
      //     }
      //   })
      // })
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      this.$axios
        .gets('/api/appuser/users', this.listQuery)
        .then((response) => {
          this.list = response.items
          this.totalCount = response.totalCount
          this.listLoading = false
        })
    },
    async fetchData(id) {
      // 编辑
      this.getAllRoles()
      // this.getJobs()
      this.form.ouId = null
      this.classValue = ''
      const response = await this.$axios.gets('/api/appuser/users/' + id)
      this.form = response
      // this.classValue = response.extraProperties.OUId
      this.$axios.gets('/api/appuser/users/' + id + '/ous').then((data) => {
        data.items.forEach((item) => {
          this.form.extraProperties.OUId = item.id
          this.form.ouId = item.id
          // this.classValue = [item.parentId, item.id]
          this.classValue = item.id
        })
      })

      this.$axios.gets('/api/appuser/users/' + id + '/roles').then((data) => {
        this.isAdmin = false
        this.checkedRole = []
        data.items.forEach((item) => {
          this.checkedRole.push(item.name)
          if (item.name === 'admin') {
            this.isAdmin = true
          }
        })
      })
    },
    loadOrgs({ action, parentNode, callback }) {
      if (action === LOAD_CHILDREN_OPTIONS) {
        this.$axios
          .gets('/api/appuser/organizattion/loadOrgs', {
            id: parentNode.id
          })
          .then((response) => {
            parentNode.children = response.items.map(function(obj) {
              if (obj !== null) {
                return {
                  id: obj.id,
                  label: obj.displayName
                  // children: null
                }
              }
            })
            setTimeout(() => {
              callback()
            }, 100)
          })
      }
    },
    getAllRoles() {
      this.$axios.gets('/api/appuser/roles/all').then((response) => {
        response.items.forEach((item, index) => {
          item.displayName = item.name
          if (item.name === 'student') {
            item.displayName = '学生'
          }
          if (item.name === 'teacher') {
            item.displayName = '老师'
          }
          if (item.name === 'admin') {
            item.displayName = '管理员'
          }
        })
        this.roleList = response.items
      })
    },
    handleClassChange(val) {
      if (val.length > 0) {
        this.form.ouId = val
        this.form.ouName = ''
      } else {
        this.form.ouName = ''
        this.form.ouId = ''
      }
      // if (
      //   this.$refs.classCascader &&
      //   this.$refs.classCascader.getCheckedNodes().length > 0
      // ) {
      //   this.form.ouName = this.$refs.classCascader.getCheckedNodes()[0].label
      //   this.form.ouId = val[1]
      // } else {
      //   this.form.ouName = ''
      //   this.form.ouId = ''
      // }
      this.classValue = val
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    handleNodeClick(data) {
      if (data !== undefined) {
        this.listQuery.OuId = data.id
      } else {
        this.listQuery.OuId = null
      }

      this.getList()
    },
    // 保存用户
    save() {
      this.formLoading = true
      if (this.$refs.classCascader) {
        if (this.$refs.classCascader.getCheckedNodes()[0]) {
          this.form.ouName =
            this.$refs.classCascader.getCheckedNodes()[0].label
        }
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.roleNames = this.checkedRole
          this.form.remarks = this.form.extraProperties.Remarks
          this.form.position = this.form.extraProperties.Position

          if (this.isEdit) {
            this.$axios
              .puts('/api/appuser/users/' + this.form.id, this.form)
              .then((response) => {
                this.formLoading = false
                this.$notify({
                  title: '成功',
                  message: '更新成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.getList()
              })
              .catch(() => {
                this.formLoading = false
              })
          } else {
            if (this.form.password.length === 0) {
              this.formLoading = false
              this.$message({
                message: '请输入密码',
                type: 'error',
                duration: 2 * 1000
              })
              return
            }
            this.form.email = this.form.userName + '@tempmail.com'
            this.$axios
              .posts('/api/appuser/users', this.form)
              .then((response) => {
                this.formLoading = false
                this.$notify({
                  title: '成功',
                  message: '新增成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.getList()
              })
              .catch(() => {
                this.formLoading = false
              })
          }
        } else {
          this.formLoading = false
        }
      })
    },
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields()
      }
    },
    handleCreate() {
      this.formTitle = '新增用户'
      this.isEdit = false
      this.classValue = []
      this.dialogFormVisible = true
      this.getAllRoles()
      this.isAdmin = false
      this.resetForm('form')
      // this.getJobs()
    },
    handleDelete(row) {
      if (row) {
        this.$axios
          .gets('/api/appuser/users/' + row.id + '/roles')
          .then((data) => {
            this.isAdmin = false
            this.checkedRole = []
            data.items.forEach((item) => {
              this.checkedRole.push(item.name)
              if (item.name === 'admin') {
                this.isAdmin = true
              }
            })

            this.$confirm('是否删除' + row.name + '?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                if (!this.isAdmin) {
                  this.$axios
                    .deletes('/api/appuser/users/' + row.id)
                    .then((response) => {
                      const index = this.list.indexOf(row)
                      this.list.splice(index, 1)
                      this.$notify({
                        title: '成功',
                        message: '删除成功',
                        type: 'success',
                        duration: 2000
                      })
                    })
                } else {
                  this.$notify({
                    title: '失败',
                    message: '管理员用户不能删除',
                    type: 'error',
                    duration: 2000
                  })
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除'
                })
              })
          })
      } else {
        this.$alert('暂时不支持用户批量删除', '提示', {
          confirmButtonText: '确定',
          callback: (action) => { }
        })
      }
    },
    handleUpdate(row) {
      this.formTitle = '修改用户'
      this.isEdit = true
      this.resetForm('form')
      if (row) {
        this.fetchData(row.id)
        this.dialogFormVisible = true
      } else {
        if (this.multipleSelection.length !== 1) {
          this.$message({
            message: '编辑必须选择单行',
            type: 'warning'
          })
          return
        } else {
          this.fetchData(this.multipleSelection[0].id)
          this.dialogFormVisible = true
        }
      }
    },

    lockUser(uid) {
      // 锁定用户
      this.$axios
        .puts('/api/appuser/users/' + uid + '/lock')
        .then((response) => {
          this.formLoading = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
        .catch(() => {
          this.formLoading = false
        })
    },
    unLockUser(uid) {
      // 解锁用户
      this.$axios
        .puts('/api/appuser/users/' + uid + '/unlock')
        .then((response) => {
          this.formLoading = false
          this.$notify({
            title: '成功',
            message: '更新成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
        .catch(() => {
          this.formLoading = false
        })
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.handleFilter()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleFilter()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRowClick(row, column, event) {
      this.$refs.multipleTable.clearSelection()
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    cancel() {
      this.form = Object.assign({}, defaultForm)
      this.checkedRole = []
      this.jobData = []
      this.dialogFormVisible = false
      this.$refs.form.clearValidate()
    },
    // TODO：引用公共方法
    loadTree(data) {
      if (data.items === undefined) {
        return
      }
      data.items.forEach((item) => {
        if (item.parentId === null) {
          var element = {
            id: item.id,
            label: item.displayName
            // children: null,
          }
          element.hasChildren = false
          // element.children = [];
          this.orgs.push(element)
        }
      })

      this.setChildren(this.orgs, data.items)
      this.orgDatas = this.orgs
    },
    setChildren(roots, items) {
      roots.forEach((element) => {
        items.forEach((item) => {
          if (item.parentId === element.id) {
            if (!element.children) element.children = []
            element.children.push({
              id: item.id,
              label: item.displayName
              // children: null,
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },
    openImportdialog() {
      this.dialogFormVisibleUpload = true
    },
    closeImportdialog() {
      this.dialogFormVisibleUpload = false
      this.importUserStep = 1
    },

    nextStep() {
      // 下一步
      this.importUserStep++
    },
    prevStep() {
      // 上一步
      this.importUserStep--
      this.errorUsers = []
      this.importVaildSuccess = false
      this.submitProgress = 0
    },
    handleChange(file, fileList) {
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.submitProgress = 0
          this.importfxx(this.fileTemp)
          this.importUserStep = 3
        } else {
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    handleRemove(file, fileList) {
      this.fileTemp = null
      this.errorUsers = null
      this.importVaildSuccess = false
      this.submitProgress = 0
    },
    importfxx(file) {
      const _this = this
      _this.vaildLoading = true
      _this.errorUsers = []
      _this.successUsers = []
      this.file = file
      var reader = new FileReader()
      reader.onload = function(e) {
        var binary = ''
        var wb // 读取完成的数据
        var bytes = new Uint8Array(reader.result)
        var length = bytes.byteLength
        var xlsx = require('xlsx')
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        wb = xlsx.read(binary, {
          type: 'binary'
        })
        _this.da = xlsx.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        /* excel数据合法性验证 前端 【 和数据库数据对比不在此范围内处理 放到后台代码中 】*/
        var db = _this.da
        var eusers = []
        for (var i = 0; i < _this.da.length; i++) {
          var errmsg = ''
          var iserror = false
          var iserror2 = false
          var errorUser = {}
          var successUser = {}
          var surname = null
          if (_this.da[i]['性别'] && _this.da[i]['性别'] === '男') {
            surname = '0'
          }
          if (_this.da[i]['性别'] && _this.da[i]['性别'] === '女') {
            surname = '1'
          }
          var user = {
            userName: _this.da[i]['用户名'],
            password: _this.da[i]['密码'],
            roleNames: _this.da[i]['角色'],
            name: _this.da[i]['姓名'],
            surname: surname,
            indentityCode: _this.da[i]['身份证号'],
            phoneNumber: _this.da[i]['手机号码'],
            email: '', // _this.da[i]['邮箱'],
            department1: _this.da[i]['一级部门'],
            department2: _this.da[i]['二级部门'],
            department3: _this.da[i]['三级部门'],
            studentIDNumber: _this.da[i]['工号'],
            remarks: _this.da[i]['备注'],
            position: _this.da[i]['岗位']

            // studentNumber: _this.da[i]['学籍号']
          }

          var reg_a = /^1[3456789]\d{9}$/
          var reg_b =
            /^[0-9a-zA-Z_\.-]+[@][0-9a-zA-Z_\.-]+([\.][a-zA-Z]+){1,2}$/
          /**/
          if (
            user.phoneNumber !== '' &&
            user.phoneNumber !== undefined &&
            !reg_a.test(user.phoneNumber)
          ) {
            errmsg += '手机号格式不正确'
            iserror = true
          }
          if (
            user.userName === null ||
            user.userName === undefined ||
            user.userName === ''
          ) {
            errmsg += '用户名不得为空'
            iserror = true
          }
          if (
            user.roleNames === null ||
            user.roleNames === undefined ||
            user.roleNames === ''
          ) {
            errmsg += '角色不得为空'
            iserror = true
          } else if (user.roleNames !== '老师' && user.roleNames !== '学生') {
            errmsg += '角色必须为老师或学生'
            iserror = true
          }
          if (
            user.email === null ||
            user.email === undefined ||
            user.email === ''
          ) {
            user.email = user.userName + '@tempmail.com'
          }
          if (!reg_b.test(user.email)) {
            errmsg += ' 邮箱格式不正确'
            iserror = true
          }

          errorUser['rowNum'] = i + 2
          errorUser['userName'] = user.userName
          errorUser['phoneNumber'] = user.phoneNumber
          errorUser['email'] = user.email
          errorUser['name'] = user.name
          if (iserror) {
            errorUser['error'] = errmsg
            eusers.push(errorUser)
            continue
          }
          /* excel内部去重*/
          for (var j = 0; j < db.length; j++) {
            // &&
            //       user.email === db[j]['邮箱']
            if (
              i !== j &&
              (user.userName === null ||
                user.userName === undefined ||
                user.userName === '' ||
                user.userName === db[j]['用户名'] ||
                (user.phoneNumber !== null &&
                  user.phoneNumber !== undefined &&
                  user.phoneNumber !== '' &&
                  user.phoneNumber === db[j]['手机号码'])
                // || (user.email !== null &&
                //   user.email !== undefined &&
                //   user.email !== '' &&
                //   user.email === db[j]['邮箱'])
              )
            ) {
              errmsg += ' 和第' + (j + 2).toString() + '行'
              if (
                user.userName === null ||
                user.userName === undefined ||
                user.userName === ''
              ) {
                // errmsg="用户名不得为空";
              } else if (user.userName === db[j]['用户名']) {
                errmsg += ' 用户名相同'
              }
              if (user.phoneNumber === db[j]['手机号码']) {
                errmsg += ' 手机号相同'
              }
              // if (user.email === db[j]['邮箱']) {
              //   errmsg += ' 邮箱相同'
              // }
              errorUser['error'] = errmsg
              eusers.push(errorUser)
              iserror2 = true
              break
            }
          }
          /* excel内部去重*/

          if (!iserror && !iserror2) {
            successUser['userName'] = user.userName
            successUser['phoneNumber'] = user.phoneNumber
            successUser['name'] = user.name
            successUser['surname'] = user.surname
            successUser['email'] = user.email
            successUser['password'] = user.password
            successUser['roleNames'] =
              user.roleNames === '老师'
                ? ['teacher']
                : user.roleNames === '学生'
                  ? ['student']
                  : ''
            successUser['department1'] = user.department1
            successUser['department2'] = user.department2
            successUser['department3'] = user.department3

            successUser['indentityCode'] = user.indentityCode
            successUser['studentIDNumber'] = user.studentIDNumber
            successUser['studentNumber'] = user.studentNumber
            successUser['remarks'] = user.remarks
            successUser['position'] = user.position
            _this.successUsers.push(successUser)
          }
        }
        if (eusers !== null && eusers.length > 0) {
          _this.errorUsers = eusers
          _this.vaildLoading = false
          return
        }
        _this.vaildLoading = false
        _this.importVaildSuccess = true
        // _this.$axios
        //   .posts(
        //     '/api/appuser/users/validateUsers',
        //     _this.successUsers,
        //     1000 * 60 * 1
        //   )
        //   .then((response) => {
        //     _this.errorUsers = response
        //     _this.vaildLoading = false
        //     _this.importVaildSuccess = _this.errorUsers.length === 0
        //   })
        //   .catch((error) => {
        //     _this.vaildLoading = false
        //   })
      }
      reader.readAsArrayBuffer(this.file)
    },
    submitUsers() {
      if (this.submitProgress === 0) {
        this.submitProgress = 2
      }
      this.submitingUser = []
      for (let i = 0; i < 5; i++) {
        if (this.successUsers[this.submitedUserCount + i] !== undefined) {
          this.submitingUser.push(
            this.successUsers[this.submitedUserCount + i]
          )
        }
      }
      if (this.submitingUser.length === 0) {
        return
      }
      this.$axios
        .posts(
          '/api/appuser/users/importExcel',
          this.submitingUser,
          1000 * 60 * 1 // 超时时间1min
        )
        .then((response) => {
          this.submitedUserCount += this.submitingUser.length
          this.submitProgress = Math.round(
            (this.submitedUserCount / this.successUsers.length) * 100
          )
          if (this.submitedUserCount === this.successUsers.length) {
            this.dialogFormVisibleUpload = false
            this.$notify({
              title: '成功',
              message: 'excel导入成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
            this.importVaildSuccess = false
          } else {
            this.submitUsers()
          }
        })
        .catch((error) => { })
    },
    // handleCreateQRCode() {
    //   const loading = this.$loading({
    //     lock: true,
    //     text: 'Loading',
    //     spinner: 'el-icon-loading',
    //     background: 'rgba(0, 0, 0, 0.7)'
    //   })
    //   userRegistQRCode()
    //     .then((res) => {
    //       loading.close()
    //       this.registUrl = res
    //       this.registQRCodeDialog = true
    //     })
    //     .catch(() => {
    //       loading.close()
    //       this.$message.error('获取二维码失败')
    //     })
    // },

    async exportAllUser() {
      this.exportLoading = true
      // var allList = [...this.list]
      var allList = []
      try {
        const maxCount = 500
        const totalRes = await getFilterUsers({ OUId: this.listQuery.OuId, Filter: this.listQuery.Filter, SkipCount: 0, MaxResultCount: maxCount, GetChildren: true })

        const times = totalRes.totalCount % maxCount === 0 ? totalRes.totalCount / maxCount : (Math.floor(totalRes.totalCount / maxCount) + 1)
        for (let i = 0; i < times; i++) {
          var data = {
            OUId: this.listQuery.OuId,
            GetChildren: true,
            Filter: this.listQuery.Filter,
            Sorting: 'creationtime desc',
            SkipCount: i * maxCount,
            MaxResultCount: maxCount
          }
          const res = await getFilterUsers(data)
          for (let i = 0; i < res.items.length; i++) {
            const item = res.items[i]
            if (item.surname === '0') {
              item.surname = '男'
            } else if (item.surname === '1') {
              item.surname = '女'
            } else {
              item.surname = ''
            }
            item.roles = item.extraProperties.Roles
            item.className1 = this.cacheFindParent(item.extraProperties.OUId).length ? this.cacheFindParent(item.extraProperties.OUId)[0] : ''
            item.className2 = this.cacheFindParent(item.extraProperties.OUId).length > 1 ? this.cacheFindParent(item.extraProperties.OUId)[1] : ''
            item.className3 = this.cacheFindParent(item.extraProperties.OUId).length > 2 ? this.cacheFindParent(item.extraProperties.OUId)[2] : ''
            // item.ouName = item.extraProperties.OUName
            item.remarks = item.extraProperties.Remarks
            item.position = item.extraProperties.Position
          }
          allList = allList.concat(res.items)
        }
        // if (this.listQuery.OuId?.length) {
        //   allList = allList.filter(item => { return item.extraProperties.OUId === this.listQuery.OuId })
        // }
        // if (this.listQuery.Filter?.length) {
        //   var reg = RegExp(this.listQuery.Filter)
        //   allList = allList.filter(item => { return item.userName?.match(reg) || item.name?.match(reg) })
        // }
        this.exportLoading = false
      } catch (e) {
        this.exportLoading = false
      }
      import('@/vendor/Export2Excel').then((excel) => {
        const tHeader = ['用户名', '角色', '姓名', '性别', '身份证号', '手机号码', '一级部门', '二级部门', '三级部门', '工号', '岗位', '备注', '禁用状态']
        const filterVal = ['userName', 'roles', 'name', 'surname', 'indentityCode', 'phoneNumber', 'className1', 'className2', 'className3', 'studentIDNumber', 'position', 'remarks', 'isLockedOut']
        // 成绩列表数据
        // const list = Response.items;
        const data = this.formatJson(filterVal, allList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '导出用户_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          if (j === 'roles') {
            return this.roleTransformation(v[j])
            // if (v[j] === 'student') {
            //   return '学生'
            // }
            // if (v[j] === 'teacher') {
            //   return '老师'
            // }
            // if (v[j] === 'admin') {
            //   return '管理员'
            // }
          } else if (j === 'isLockedOut') {
            if (v[j]) return '已禁用'
            else return '未禁用'
          } else {
            return v[j]
          }
        })
      )
    },
    roleTransformation(val) {
      if (Object.prototype.toString.call(val) === '[object String]') {
        var rolesList = val.split(',')
        var roleName = ''
        rolesList.forEach(item => {
          if (item === 'student') {
            roleName += '学生' + ','
          } else if (item === 'teacher') {
            roleName += '老师' + ','
          } else if (item === 'admin') {
            roleName += '管理员' + ','
          } else {
            roleName += item + ','
          }
        })
        return this.rtrim(roleName, ',')
      } else {
        return val
      }
    },
    rtrim(val, char, type) {
      if (char) {
        if (type === 'left') {
          return val.replace(new RegExp('^\\' + char + '+', 'g'), '')
        } else if (type === 'right') {
          return val.replace(new RegExp('\\' + char + '+$', 'g'), '')
        }
        return val.replace(new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'), '')
      }
      return val.replace(/^\s+|\s+$/g, '')
    },
    cacheFindParent(id) {
      if (this.cacheDate.get(id)) {
        return this.cacheDate.get(id)
      }
      return this.findParent(id)
    },
    async loadClass() {
      const res = await orgsData()
      // this.orgDatas = res.items
      this.map = new Map()
      res.items.forEach(item => {
        this.map.set(item.id, item)
      })
    },
    // findParent(id) {
    //   const result = []
    //   if (!this.map) {
    //     return []
    //   }
    //   const d = this.map.get(id)
    //   if (d) {
    //     result.unshift(d?.displayName)
    //   }
    //   if (d?.parentId) {
    //     let next = this.map.get(d.parentId)
    //     while (next) {
    //       result.unshift(next.displayName)
    //       next = this.map.get(next.parentId)
    //     }
    //   }
    //   this.cacheDate.set(id, result.join('/'))
    //   return result.join('/')
    // }
    findParent(id) {
      const result = []
      if (!this.map) {
        return []
      }
      const d = this.map.get(id)
      if (d) {
        result.unshift(d?.displayName)
      }
      if (d?.parentId) {
        let next = this.map.get(d.parentId)
        while (next) {
          result.unshift(next.displayName)
          next = this.map.get(next.parentId)
        }
      }
      this.cacheDate.set(id, result)
      return result
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.opts {
  padding: 6px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}

.htitle {
  margin-top: 20px;
  line-height: 32px;
}
</style>

<template>
  <div>
    <el-form :model="batchViewQuestionForm" label-width="80px">
      <el-form-item label="选择题目">
        <el-select v-model="selectQuestionId" placeholder="请选择题目..." style="width: 100%" @change="batchViewQuestionChange">
          <el-option v-for="item in subjectiveQuestionList" :key="item.id" :label="JSON.parse(item.questionStem).Title" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="题目解析">
        <el-input v-model="batchViewQuestionForm.analysis" :disabled="true" type="textarea" :rows="2" />
      </el-form-item>
      <el-form-item>
        <el-tabs v-model="currentScored" @tab-click="handleScoreTableClick">
          <el-tab-pane label="未阅" name="scored">
            <el-table v-loading="batchListLoading" :data="batchQuestionList" size="mini">
              <el-table-column label="用户名" prop="userName" width="140px" />
              <el-table-column label="答案" prop="answer">
                <template slot-scope="{row}">
                  <span v-if="batchViewQuestionForm.questionType === 6">{{ row.answer }}</span>
                  <span v-if="batchViewQuestionForm.questionType === 3 && row.answer.length" class="blank-item">
                    <span v-for="(str,i) in JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/)" :key="i">
                      {{ str }}<span v-if="i< JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/).length-1" class="answer">{{ row.answer ? JSON.parse(row.answer)[i] : '' }}</span>
                    </span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="分数" prop="score" width="120px">
                <template slot-scope="{row}">
                  <el-select v-model="row.score" size="mini" @change="batchUserScoreChange(row)">
                    <el-option v-for="i in batchViewQuestionForm.score*2" :key="i" :label="i*0.5" :value="i*0.5" />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已阅" name="noScored">
            <el-table v-loading="batchListLoading" :data="batchQuestionList" size="mini">
              <el-table-column label="用户名" prop="userName" width="140px" />
              <el-table-column label="答案" prop="answer">
                <template slot-scope="{row}">
                  <span v-if="batchViewQuestionForm.questionType === 6">{{ row.answer }}</span>
                  <span v-if="batchViewQuestionForm.questionType === 3 && row.answer" class="blank-item">
                    <span v-for="(str,i) in JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/)" :key="i">
                      {{ str }}<span v-if="i< JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/).length-1" class="answer">{{ row.answer ? JSON.parse(row.answer)[i] : '' }}</span>
                    </span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="分数" prop="score" width="120px">
                <template slot-scope="{row}">
                  <el-select v-model="row.score" size="mini" @change="batchUserScoreChange(row)">
                    <el-option v-for="i in batchViewQuestionForm.score*2" :key="i" :label="i*0.5" :value="i*0.5" />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <pagination
          v-show="subjectiveQuestionListQuery.totalCount > 0"
          :total="subjectiveQuestionListQuery.totalCount"
          :page.sync="subjectiveQuestionListQuery.page"
          :limit.sync="subjectiveQuestionListQuery.MaxResultCount"
          @pagination="getBatchQuestionList"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { subjectiveQuestionList, subjectiveQuestionScore, subjectiveQuestionAnswer } from '@/api/train'
import Pagination from '@/components/Pagination'
export default {
  name: 'TrainExamList',
  components: {
    Pagination
  },
  props: {
    examinationId: {
      reuqerd: true,
      type: String,
      default: ''
    }

  },
  data() {
    return {
      currentScored: 'scored',
      batchListLoading: false,
      batchQuestionList: [],
      // 批量阅卷题目
      subjectiveQuestionList: [],
      subjectiveQuestionListQuery: {
        ExaminationId: this.examinationId,
        QuestionId: null,
        IsScored: false,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 批量阅卷题目信息
      selectQuestionId: null,
      batchViewQuestionForm: {}
    }
  },
  created() {
    this.getSubjectiveQuestionList()
  },
  methods: {
    // 批量阅卷题目变化
    async batchViewQuestionChange(val) {
      // this.batchViewQuestionForm = {}
      this.subjectiveQuestionListQuery.QuestionId = val
      this.subjectiveQuestionListQuery.page = 1
      this.batchViewQuestionForm = this.subjectiveQuestionList.find(item => { return item.id === this.selectQuestionId })
      // 获取最新列表
      this.getBatchQuestionList()
    },
    // 学生分数变化
    batchUserScoreChange(row) {
      // 提交数据
      var data = {
        examinationId: this.subjectiveQuestionListQuery.ExaminationId,
        questionId: this.subjectiveQuestionListQuery.QuestionId,
        userId: row.userId,
        score: row.score
      }
      subjectiveQuestionScore(data).then(res => {

      })
    },
    async handleScoreTableClick(val, event) {
      this.subjectiveQuestionListQuery.page = 1
      if (val.name === 'scored') {
        this.subjectiveQuestionListQuery.IsScored = false
      } else {
        this.subjectiveQuestionListQuery.IsScored = true
      }
      this.getBatchQuestionList()
    },
    getSubjectiveQuestionList() {
      subjectiveQuestionList(this.examinationId).then(res => {
        this.subjectiveQuestionList = res
      }).catch(() => {
      })
    },
    getBatchQuestionList() {
      this.batchListLoading = true
      this.subjectiveQuestionListQuery.SkipCount = (this.subjectiveQuestionListQuery.page - 1) * this.subjectiveQuestionListQuery.MaxResultCount
      subjectiveQuestionAnswer(this.subjectiveQuestionListQuery).then(res => {
        this.batchQuestionList = res.items
        this.batchListLoading = false
      }).catch(() => {
        this.batchListLoading = false
      })
    }
  }
}
</script>

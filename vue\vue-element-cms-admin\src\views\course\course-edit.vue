<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ pageTitle }} （ 总时长：{{ totalDuration | formatSecond }} ）</span>
      </div>
      <el-tabs>
        <el-tab-pane label="基础信息">
          <el-row :gutter="40">
            <el-col :span="6">
              <el-form ref="form" :model="form" :rules="formRules">
                <el-form-item prop="coverUrl">
                  <lz-upload-images
                    ref="previewFile"
                    :limit="1"
                    :file-size="500"
                    :file-type="['jpg', 'png', 'jpeg']"
                    :source-list="previewFileList"
                    @response-fn="handleImageResponse"
                    @remove-upload="handleRemoveUploadImage"
                  />
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="18">
              <el-form ref="form" v-loading="formLoading" :model="form" :rules="formRules" label-width="100px">
                <el-form-item label="课程编号" prop="number">
                  <el-input v-model="form.number" />
                </el-form-item>
                <el-form-item label="课程名称" prop="name">
                  <el-input v-model="form.name" />
                </el-form-item>
                <el-form-item label="课程分类" prop="courseCategoryId">
                  <el-cascader v-model="form.courseCategoryIds" filterable clearable :options="courseCategoryList" :props="cascaderProps" style="width: 100%" placeholder="请选择课程分类..." />
                  <!-- <el-select v-model="form.courseCategoryId" clearable placeholder="选择课程分类" class="form-select">
                    <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select> -->
                </el-form-item>
                <el-form-item label="课程讲师" prop="lecturer">
                  <el-input v-model="form.lecturer" />
                </el-form-item>
                <el-form-item label="课程课时" prop="classHour">
                  <el-input-number v-model="form.classHour" :min="0" :step="0.1" />
                </el-form-item>
                <el-form-item label="课程介绍" prop="introduce">
                  <tinymce v-if="showContent" id="tinymce" v-model="form.introduce" :value="form.introduce" :height="400" :width="700" />
                </el-form-item>
                <el-form-item label="">
                  <el-button
                    v-permission="['CourseManagement.Courses.Update']"
                    type="primary"
                    round
                    icon="el-icon-check"
                    @click="handleSaveCourseEdit"
                  >保存</el-button>
                </el-form-item>

              </el-form>

            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="课程内容">
          <el-row :gutter="10">
            <el-col :xs="24" :sm="8" :md="6" :lg="6">
              <div class="">
                <el-card class="shadow_none card-padding-0" shadow="always">
                  <div slot="header" class="clearfix">
                    <span>课程目录</span>
                    <el-button style="float: right; padding: 3px 0" type="text" @click="handleAddCourse">添加目录</el-button>
                  </div>
                  <el-tree
                    ref="tree"
                    v-loading="treeDataLoading"
                    class="course-dir-tree"
                    style="height: 766px;overflow: auto"
                    accordion
                    :data="treeData"
                    :props="treeProps"
                    draggable
                    :highlight-current="true"
                    node-key="id"
                    :expand-on-click-node="false"
                    default-expand-all
                    :render-content="renderContent"
                    :allow-drop="handleAllowDrop"
                    @node-click="handleTreeNodeClick"
                    @node-drop="handleNodeDropResponse"
                  />
                </el-card>
              </div>
              <!-- <el-card class="box-card" shadow="always" style="margin: 0 5px; border: 1px solid lightgray">
                <div slot="header" class="flex_between_box">
                  <span>课程目录</span>
                  <el-button v-permission="['CourseManagement.CourseDirectorys.Create']" size="mini" type="text" @click="handleAddCourse">添加目录</el-button>
                </div>
                <el-tree ref="tree" style="height: 766px;overflow: auto" :data="treeData" :props="treeProps" :highlight-current="true" default-expand-all node-key="id" :expand-on-click-node="false" :render-content="renderContent" @node-click="handleTreeNodeClick" />
              </el-card> -->
            </el-col>
            <el-col :xs="24" :sm="16" :md="18" :lg="18">
              <el-card class="shadow_none" shadow="always">
                <div slot="header" class="">
                  <span>{{ currentNodeDataLabel }}</span>
                  <div style="float: right;">
                    <el-button round size="small" type="primary" style="margin-left: 15px;" icon="el-icon-plus" @click="handleCourseResourceCreate">添加资源
                    </el-button>
                    <el-button round size="small" type="primary" style="margin-left: 15px" icon="el-icon-upload2" @click="handleUploadResource">上传资源
                    </el-button>
                  </div>

                </div>
                <el-alert title="按住鼠标上下拖动资源可对资源进行排序" type="info" show-icon />
                <el-table ref="dragTable" v-loading="listLoading" row-key="id" :data="list" height="590px" highlight-current-row>
                  <el-table-column label="排序" width="60px">
                    <template>
                      <svg-icon icon-class="drag" class="meta-item__icon" />
                    </template>
                  </el-table-column>
                  <el-table-column label="资源名称" prop="name">
                    <template slot-scope="{row}">
                      <el-link type="primary" @click="handlePreviewResource(row)"> {{ row.name }} </el-link>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="宝之云" prop="tranStatus" width="100">
                    <template slot-scope="{row}">
                      <el-tag v-if="row.tranStatus===0" type="default" size="mini"> --  </el-tag>
                      <el-tag v-if="row.tranStatus===4" size="mini"> 待转码 </el-tag>
                      <el-tag v-if="row.tranStatus===1" type="warning" size="mini"> 上传中 </el-tag>
                      <el-tag v-if="row.tranStatus===2" type="success" size="mini"> 成功 </el-tag>
                      <el-tag v-if="row.tranStatus===3" type="danger" size="mini"> 失败 </el-tag>
                    </template>
                  </el-table-column> -->
                  <el-table-column label="格式" prop="fileType" width="80" />
                  <el-table-column label="大小" prop="duration" width="100">
                    <template slot-scope="{row}">
                      <span>{{ row.size | formatFileSize }} </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="时长" prop="duration" width="130">
                    <template slot-scope="{row}">
                      <span>{{ row.duration | formatSecond }} </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="200">
                    <template slot-scope="{row}">
                      <el-button round size="mini" type="primary" icon="el-icon-edit" @click="handleCourseResourceEdit(row)">
                        编辑</el-button>
                      <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleCourseResourceDelete(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="listQuery.totalCount > 0"
                  :total="listQuery.totalCount"
                  :page.sync="listQuery.page"
                  :limit.sync="listQuery.MaxResultCount"
                  @pagination="getCourseResourceList"
                />
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-dialog title="课程目录" :close-on-click-modal="false" :visible.sync="treeNodeDialog" width="500px">
      <el-form ref="courseDirectoryForm" :model="courseDirectoryForm" :rules="rules" label-position="right" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="courseDirectoryForm.name" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="courseDirectoryForm.sort" :min="0" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleTreeNodeDialogSure">确 定</el-button>
        <el-button round @click="treeNodeDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="上传资源" :close-on-click-modal="false" :visible.sync="uploadResourceDialog" width="600px">
      <!-- <resource-list v-if="uploadResourceDialog" @select-change="handleUploadResourceChange" /> -->
      <el-form label-width="80px">
        <el-form-item label="上传资源" prop="url">
          <!-- <local-file-upload class="uplaod_file" :multiple="true" :file-size="1024" :file-type="uploadFileType" :file-list="uploadResourceList" :btn-title="'上传资源'" @response-fn="handleFileResponse" @remove-upload="handleRemoveUploadFile" /> -->
          <lz-upload-file class="uplaod_file" :multiple="true" :file-size="500" :is-public="false" :is-show-tip="true" :file-type="uploadFileType" :file-list="uploadResourceList" :btn-title="'上传资源'" @response-fn="handleFileResponse" @remove-upload="handleRemoveUploadFile" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleUploadResourceSure">确 定</el-button>
        <el-button round @click="uploadResourceDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="添加/编辑资源" :close-on-click-modal="false" :visible.sync="courseResourceDialog" width="600px">
      <el-form ref="courseResourceForm" :model="courseResourceForm" :rules="rules" label-width="100px">
        <el-form-item label="资源名称" prop="name">
          <el-input v-model="courseResourceForm.name" />
        </el-form-item>
        <el-form-item label="资源类型" prop="resType">
          <el-select v-model="courseResourceForm.resType" placeholder="请选择类型" @change="resTypeChange">
            <el-option
              v-for="item in resTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isExternal" label="资源地址" prop="url">
          <el-input v-model="courseResourceForm.url" />
        </el-form-item>
        <el-form-item v-if="!isExternal" label="上传资源">
          <!-- <local-file-upload class="uplaod_file" :multiple="false" :file-size="1024" :file-type="uploadFileType" :file-list="uploadResourceList" :btn-title="'上传资源'" @response-fn="handleFileResponse" @remove-upload="handleRemoveUploadFile" /> -->
          <lz-upload-file class="uplaod_file" :multiple="false" :file-size="500" :is-public="false" :is-show-tip="true" :file-type="uploadFileType" :file-list="uploadResourceList" :btn-title="'上传资源'" @response-fn="handleFileResponse" @remove-upload="handleRemoveUploadFile" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="courseResourceForm.sort" :min="0" />
        </el-form-item>
        <el-form-item v-show="!checkedFileTypeIsVideo(courseResourceForm.fileType)||isExternal" label="时长" prop="duration">
          <el-input-number v-model="durationHour" :controls="false" :min="0" />
          <span>  时</span>
          <el-input-number v-model="durationMinute" :controls="false" :min="0" :max="59" />
          <span>  分</span>
          <el-input-number v-model="durationSecond" :controls="false" :min="0" :max="59" />
          <span>  秒</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleCourseResourceSure">确 定</el-button>
        <el-button round @click="courseResourceDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源预览" append-to-body :visible.sync="resourcePreviewDialog" width="1000px" top="5vh">
      <bos-resource-preview v-if="resourcePreviewDialog&&previewSource===0" :type="previewType" :url="previewUrl" :doc-id="previewDocId" />
      <preview-resource
        v-if="resourcePreviewDialog&&previewSource===1"
        ref="previewResource"
        :type="previewType"
        :url="previewUrl"
      />
    </el-dialog>
  </div>
</template>
<script>
import LzUploadImages from '@/components/LzUploadImages'
import LocalFileUpload from '@/components/LocalFileUpload'
import LzUploadFile from '@/components/LzUploadFile'

// import ResourceList from '@/components/ResourceList'
import tinymce from '@/components/Tinymce'
import Pagination from '@/components/Pagination'
import Sortable from 'sortablejs'
import BosResourcePreview from '@/components/BosResourcePreview'
import PreviewResource from '@/components/PreviewResource'
import permission from '@/directive/permission'
import {
  courseCategoryList,
  courseDetail,
  courseEdit,
  courseDirectoryAllList,
  courseDirectoryAdd,
  courseDirectoryEdit,
  courseDirectoryDelete,
  courseDirectorySort,
  courseResourceList,
  courseResourceEdit,
  courseResourceDelete,
  courseResourceSetOrder,
  courseDuration,
  courseResourceBatchAdd,
  getCourseResourceHtmlUrl
} from '@/api/course'
import { getFileContent, getFileDownloadInfo, getBaoCloudResourceUrl, resourcePath } from '@/api/upload'
export default {
  name: 'CourseEdit',
  directives: {
    permission
  },
  components: {
    tinymce,
    LzUploadImages,
    LocalFileUpload,
    LzUploadFile,
    Pagination,
    BosResourcePreview,
    PreviewResource
  },
  data() {
    var checkCoverUrl = (rule, value, callback) => {
      if (this.form.coverUrl === '') {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false,
        multiple: true
      },
      pageTitle: '',
      treeData: [],
      treeDataLoading: false,
      treeNodeDialog: false,
      treeProps: {
        id: 'id',
        label: 'name',
        parentId: 'parrentId',
        children: 'children'
      },
      // 节点form
      courseDirectoryForm: {
        courseId: this.$route.query.id,
        parentId: null,
        name: '',
        sort: 0,
        code: ''
      },
      currentNodeData: null,
      currentNodeDataLabel: '全部',
      isNodeEdit: 0,
      // 课程资源列表
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseId: this.$route.query.id,
        DirectoryId: null,
        Sorting: 'sort',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 上传资源Dialog
      uploadResourceDialog: false,
      uploadResourceList: [],
      uploadFileType: ['pdf', 'mp4'],

      // 上传图片列表
      previewFileList: [],
      formLoading: false,
      // form
      form: {
        coverUrl: '',
        // 编号
        number: '',
        name: '',
        courseCategoryId: '',
        lecturer: '',
        classHour: 1,
        introduce: '',
        courseCategoryIds: []
      },
      formRules: {
        coverUrl: [{
          required: true,
          validator: checkCoverUrl,
          trigger: 'blur'
        }],
        number: [{
          required: true,
          message: '请输入课程编号',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        name: [{
          required: true,
          message: '请输入课程名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 50,
          message: '长度在 1 到 50 个字符',
          trigger: 'blur'
        }
        ],
        courseCategoryIds: [{
          required: true,
          message: '请选择课程分类',
          trigger: 'change'
        }],
        lecturer: [{
          required: true,
          message: '请输入讲师名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ]
      },
      // 课程分类列表
      courseCategoryList: [],
      // 课程资源编辑DIalog
      courseResourceDialog: false,
      courseResourceForm: {
        name: '',
        sort: 0,
        duration: 0,
        fileType: '',
        url: '',
        resType: '',
        courseId: this.$route.query.id,
        courseDirectoryId: null,
        id: ''
      },
      isExternal: false,
      isAddRes: true,
      resTypeOptions: [
        {
          value: 'video',
          label: '视频'
        }, {
          value: 'pdf',
          label: 'PDF'
        }
        // , {
        //   value: 'html',
        //   label: 'H5网页'
        // },
        // {
        //   value: 'hundun-video',
        //   label: '混沌学院'
        // }, {
        //   value: 'geektime-video',
        //   label: '极客视频'
        // }, {
        //   value: 'geektime-html',
        //   label: '极客html'
        // }, {
        //   value: 'ximalaya',
        //   label: '喜马拉雅'
        // },
        // {
        //   value: 'pdfh5',
        //   label: 'H5_PDF'
        // },
        // {
        //   value: 'videoh5',
        //   label: 'H5_Video'
        // }
      ],
      rules: {
        name: [{
          required: true,
          message: '请输入名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 50,
          message: '长度在 1 到 50 个字符',
          trigger: 'blur'
        }
        ],
        url: [{
          required: true,
          message: '请输入URL',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 500,
          message: '长度在 1 到 500 个字符',
          trigger: 'blur'
        }
        ]
      },
      durationHour: 0,
      durationMinute: 0,
      durationSecond: 0,
      totalDuration: 0,

      // 列表拖拽排序
      sortable: null,

      // 资源预览
      resourcePreviewDialog: false,
      previewSource: 0, // 0：bce 1：local
      previewUrl: '',
      previewDocId: '',
      previewType: '',

      showContent: false
    }
  },
  mounted() {
    this.getCourseCategoryList()
    this.getCourseDetail()
    this.getCourseDirectoryAllList()
    // this.getCourseResourceList()
  },
  methods: {
    // 课程文件列表删除
    handleCourseResourceDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseResourceDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getCourseResourceList()
          this.getCourseDuration()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleCourseResourceCreate() {
      this.restCourseResourceForm()
      this.isExternal = true
      this.isAddRes = true
      this.courseResourceDialog = true
      this.uploadResourceList = []
    },
    resTypeChange() {
      this.isExternal = false
      switch (this.courseResourceForm.resType) {
        case 'hundun-video':
        case 'geektime-video':
        case 'geektime-html':
        case 'ximalaya':
        case 'pdfh5':
        case 'videoh5':
          this.isExternal = true
          break
      }
    },
    // 课程文件列表编辑
    handleCourseResourceEdit(row) {
      if (row.tranStatus === 1) {
        this.$message.warning('资源上传中，请上传成功后再编辑')
        return
      }

      this.isAddRes = false
      this.restCourseResourceForm()
      this.courseResourceForm.name = row.name
      this.courseResourceForm.fileName = row.fileName
      this.courseResourceForm.fileType = row.fileType
      this.courseResourceForm.duration = row.duration
      this.courseResourceForm.resType = row.resType
      this.courseResourceForm.url = row.url
      this.courseResourceForm.size = row.size
      this.courseResourceForm.hash = row.hash
      this.courseResourceForm.jobId = row.jobId
      this.courseResourceForm.sort = row.sort
      this.courseResourceForm.id = row.id
      this.courseResourceForm.courseDirectoryId = row.courseDirectoryId
      this.durationHour = Number.parseInt(row.duration / 3600)
      this.durationMinute = Number.parseInt((row.duration % 3600) / 60)
      this.durationSecond = Number.parseInt((row.duration % 3600) % 60)
      this.resTypeChange()
      this.uploadResourceList = []
      if (!this.isExternal) {
        this.uploadResourceList.push({
          url: this.courseResourceForm.url,
          localUrl: '',
          fileName: this.courseResourceForm.fileName,
          name: this.courseResourceForm.name,
          hash: this.courseResourceForm.hash,
          extend: this.courseResourceForm.fileType,
          size: this.courseResourceForm.size,
          fileType: this.courseResourceForm.fileType,
          resType: this.courseResourceForm.resType,
          jobId: this.courseResourceForm.jobId,
          durationInSecond: this.courseResourceForm.duration
        })
      }
      this.courseResourceDialog = true
    },
    handleCourseResourceSure() {
      this.$refs.courseResourceForm.validate((valid) => {
        if (valid) {
          this.courseResourceForm.duration = this.durationHour * 3600 + this.durationMinute * 60 + this.durationSecond
          if (this.isAddRes) {
            if (this.courseResourceForm.resType === '') {
              this.$message.error('请选择类型')
              return
            }
            var data_list = {
              items: []
            }
            if (this.isExternal) {
              data_list.items.push({
                courseId: this.$route.query.id,
                courseDirectoryId: this.currentNodeData ? this.currentNodeData.id : null,
                name: this.courseResourceForm.name,
                hash: '',
                fileName: '',
                fileType: '',
                url: this.courseResourceForm.url,
                localUrl: '',
                resType: this.courseResourceForm.resType,
                size: 0,
                duration: this.courseResourceForm.duration,
                thumbnailUrl: '',
                tranStatus: 0,
                documentId: '',
                jobId: this.courseResourceForm.resType,
                sort: this.courseResourceForm.sort
              })
            } else {
              data_list.items.push({
                courseId: this.$route.query.id,
                courseDirectoryId: this.currentNodeData ? this.currentNodeData.id : null,

                hash: this.uploadResourceList[0].hash,
                fileName: this.uploadResourceList[0].fileName,
                fileType: this.uploadResourceList[0].fileType,
                url: this.uploadResourceList[0].url,
                localUrl: this.uploadResourceList[0].localUrl,
                resType: this.uploadResourceList[0].resType,
                size: this.uploadResourceList[0].size,
                duration: this.uploadResourceList[0].durationInSecond,
                jobId: this.uploadResourceList[0].jobId,

                name: this.courseResourceForm.name,
                sort: this.courseResourceForm.sort,

                thumbnailUrl: '',
                tranStatus: 0,
                documentId: ''
              })
            }
            // console.log(data_list)
            courseResourceBatchAdd(data_list).then(res => {
              this.$message.success('添加成功')
              this.courseResourceDialog = false
              this.getCourseResourceList()
              // this.getCourseDuration()
            })
          } else {
            if (this.isExternal) {
              this.courseResourceForm.hash = ''
              this.courseResourceForm.fileName = ''
              this.courseResourceForm.fileType = ''
              this.courseResourceForm.localUrl = ''
              this.courseResourceForm.thumbnailUrl = ''
              this.courseResourceForm.documentId = ''
              this.courseResourceForm.size = 0
              this.courseResourceForm.jobId = this.courseResourceForm.resType
            } else {
              if (!this.uploadResourceList.length) {
                this.$message.warning('请上传资源')
                return
              }
              this.courseResourceForm.hash = this.uploadResourceList[0].hash
              this.courseResourceForm.fileName = this.uploadResourceList[0].fileName
              this.courseResourceForm.fileType = this.uploadResourceList[0].fileType
              // this.courseResourceForm.resType = this.uploadResourceList[0].resType
              this.courseResourceForm.localUrl = this.uploadResourceList[0].localUrl
              this.courseResourceForm.url = this.uploadResourceList[0].url
              this.courseResourceForm.size = this.uploadResourceList[0].size
              this.courseResourceForm.jobId = this.uploadResourceList[0].jobId
              if (this.checkedFileTypeIsVideo(this.courseResourceForm.fileType)) {
                this.courseResourceForm.duration = this.uploadResourceList[0].durationInSecond
              }
            }

            courseResourceEdit(this.courseResourceForm.id, this.courseResourceForm).then(res => {
              this.$message.success('编辑成功')
              this.courseResourceDialog = false
              this.getCourseResourceList()
              this.getCourseDuration()
            }).catch(() => {
              this.$message.error('编辑失败')
            })
          }
        } else {
          return false
        }
      })
    },
    handleSaveCourseEdit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // console.log(this.form)
          // return
          this.formLoading = true
          courseEdit(this.$route.query.id, this.form).then(res => {
            this.formLoading = false
            this.$message.success('保存成功')
            this.getCourseDetail()
          }).catch(() => {
            this.formLoading = false
            this.$message.error('保存失败')
          })
        } else {
          return false
        }
      })
    },
    // 添加根节点
    handleAddCourse() {
      this.isNodeEdit = 0
      this.resetForm()
      this.courseDirectoryForm.sort = this.treeData.length + 1
      this.treeNodeDialog = true
    },
    // 节点添加打开dialog
    handleAdd(node, data) {
      this.courseDirectoryForm.name = ''
      this.courseDirectoryForm.parentId = data.id
      this.courseDirectoryForm.sort = data.children.length + 1
      this.isNodeEdit = 0
      this.treeNodeDialog = true
    },
    // 节点编辑打开dialog
    handleEdit(node, data) {
      this.courseDirectoryForm.name = node.label
      this.courseDirectoryForm.parentId = data.parentId
      this.courseDirectoryForm.sort = data.sort
      this.currentNodeData = data
      this.isNodeEdit = 1
      this.treeNodeDialog = true
    },
    // 删除
    handleDelete(node, data) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseDirectoryDelete(data.id).then(res => {
          this.$message.success('删除成功')
          this.listQuery.DirectoryId = ''
          this.getCourseDirectoryAllList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 打开资源选择dialog
    handleUploadResource() {
      if (this.currentNodeData && this.currentNodeData.id) {
        this.uploadResourceList = []
        this.uploadResourceDialog = true
      } else {
        this.$message.warning('请选择课程目录')
      }
    },
    // 上传文件成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.form.coverUrl = url
    },
    // 上传文件删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.form.coverUrl = ''
    },
    handleFileResponse(url, fileForm) {
      this.uploadResourceList.push({
        url: url,
        localUrl: fileForm.localUrl,
        fileName: fileForm.fileName,
        name: fileForm.fileName,
        hash: fileForm.hash,
        extend: fileForm.fileType,
        size: fileForm.size,
        fileType: fileForm.fileType,
        resType: fileForm.resType,
        tranStatus: fileForm.tranStatus,
        documentId: fileForm.documentId,
        jobId: fileForm.jobId,
        durationInSecond: fileForm.durationInSecond
      })
    },
    handleRemoveUploadFile(file) {
      var index = this.uploadResourceList.indexOf(file)
      if (index > -1) {
        this.uploadResourceList.splice(index, 1)
      }
    },
    // 资源选择确定 进行上传
    handleUploadResourceSure() {
      if (!this.uploadResourceList.length) {
        this.$message.warning('请上传资源')
        return
      }
      var data_list = {
        items: []
      }
      this.uploadResourceList.forEach((item, index) => {
        var data = {
          courseId: this.$route.query.id,
          courseDirectoryId: this.currentNodeData ? this.currentNodeData.id : null,
          name: item.name,
          hash: item.hash,
          fileName: item.fileName,
          fileType: item.fileType,
          url: item.url,
          localUrl: item.localUrl,
          resType: item.resType,
          size: item.size,
          duration: item.durationInSecond,
          thumbnailUrl: item.thumbnailUrl,
          tranStatus: item.tranStatus,
          documentId: item.documentId,
          jobId: item.jobId,
          sort: this.listQuery.totalCount + index + 1
        }
        data_list.items.push(data)
      })
      courseResourceBatchAdd(data_list).then(res => {
        this.$message.success('添加成功')
        this.uploadResourceDialog = false
        this.getCourseResourceList()
        this.getCourseDuration()
      })
    },
    // 编辑添加树节点确定
    handleTreeNodeDialogSure() {
      this.$refs.courseDirectoryForm.validate((valid) => {
        if (valid) {
          if (this.isNodeEdit === 0) {
            // 添加节点
            courseDirectoryAdd(this.courseDirectoryForm).then(res => {
              this.$message.success('添加成功')
              this.listQuery.DirectoryId = res.id
              this.currentNodeData = res
              this.currentNodeDataLabel = res.name
              this.treeNodeDialog = false
              this.getCourseDirectoryAllList()
            }).catch(() => {

            })
          } else if (this.isNodeEdit === 1) {
            // 编辑节点
            courseDirectoryEdit(this.currentNodeData.id, this.courseDirectoryForm).then(res => {
              this.$message.success('编辑成功')
              this.listQuery.DirectoryId = res.id1
              this.currentNodeData = res
              this.currentNodeDataLabel = res.name
              this.treeNodeDialog = false
              this.getCourseDirectoryAllList()
            }).catch(() => {

            })
          }
        } else {
          return false
        }
      })
    },
    handleTreeNodeClick(data) {
      this.currentNodeData = data
      this.currentNodeDataLabel = data.name
      this.listQuery.DirectoryId = data.id
      this.getCourseResourceList()
    },
    // 节点是否能被放置
    handleAllowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level === dropNode.level) {
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next'
        } else {
          return false
        }
      } else {
        return false
      }
    },
    // 节点拖拽结束
    handleNodeDropResponse(draggingNode, dropNode, dropType, ev) {
      this.treeDataLoading = true
      var nodeParent = dropNode.parent.childNodes
      var param = []
      nodeParent.forEach((item, index) => {
        param.push({
          directoryId: item.data.id,
          sort: index + 1
        })
      })
      courseDirectorySort(this.$route.query.id, param).then(res => {
        this.treeDataLoading = false
      }).catch(() => {
        this.getCourseDirectoryAllList()
      })
    },
    async handlePreviewResource(row) {
      switch (row.fileType) {
        case '.mp4':
          this.previewType = 'video'
          break
        case '.pdf':
          this.previewType = 'pdf'
          break
        case '.ppt':
        case '.doc':
          this.previewType = 'doc'
          break
        case '.png':
        case '.jpeg':
        case '.jpg':
          this.previewType = 'image'
          break
      }
      switch (row.resType) {
        case 'hundun-video':
          this.previewType = 'hundun-video'
          break
        case 'geektime-video':
          this.previewType = 'geektime-video'
          break
        case 'geektime-html':
          this.previewType = 'geektime-html'
          break
        case 'ximalaya':
          this.previewType = 'ximalaya'
          break
        case 'pdfh5':
          this.previewType = 'pdfh5'
          break
        case 'videoh5':
          this.previewType = 'videoh5'
          break
      }
      if (row.jobId === 'local') { // 本地资源
        const downloadInfo = await getFileDownloadInfo(row.url)
        this.previewSource = 1
        if (row.resType === 'html') {
          this.previewType = 'h5'
          getCourseResourceHtmlUrl(downloadInfo.downloadUrl).then(data => {
            this.previewUrl = data
          })
        } else {
          this.previewUrl = downloadInfo.downloadUrl
        }
        this.resourcePreviewDialog = true
      } else if (row.jobId === 'AWS') {
        this.previewSource = 1
        getBaoCloudResourceUrl(row.url).then(res => {
          this.previewUrl = res.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://qcfile.ciep-pimp.com/pbktgf0006/')
          this.resourcePreviewDialog = true
        })
      } else { // 云端资源
        this.previewSource = 0
        this.previewUrl = encodeURIComponent(await resourcePath({ url: row.url }))
        // this.previewSource = 0
        // this.previewUrl = row.url
        // this.previewDocId = row.documentId
        this.resourcePreviewDialog = true
      }
    },
    resetForm() {
      this.courseDirectoryForm = {
        courseId: this.$route.query.id,
        parentId: null,
        name: '',
        sort: 0,
        code: ''
      }
    },
    renderContent(h, {
      node,
      data,
      store
    }) {
      // const directives = [{
      //   name: 'permission', value: ['CourseManagement.CourseDirectorys.Create']
      // }]
      if (node.level < 3) {
        return (
          <span slot-scope = '{ node, data }' class = 'custom-tree-node' >
            <span class='node-lable'>{ node.label }</span>
            <span>
              <el-button class='normal_button' type='text' icon='el-icon-plus' size='mini' circle on-click = {() => this.handleAdd(node, data)}/>
              <el-button class='normal_button' type='text' icon='el-icon-edit' size='mini' circle on-click = {() => this.handleEdit(node, data)}/>
              <el-button class='normal_button' type='text' icon='el-icon-delete' size='mini' circle on-click = {() => this.handleDelete(node, data)}/>
            </span>
          </span>
        )
      } else {
        return (
          <span slot-scope = '{ node, data }' class = 'custom-tree-node' >
            <span class='node-lable'>{ node.label }</span>
            <span>
              <el-button class='normal_button' type='text' icon='el-icon-edit' size='mini' circle on-click = {() => this.handleEdit(node, data)}/>
              <el-button class='normal_button' type='text' icon='el-icon-delete' size='mini' circle on-click = {() => this.handleDelete(node, data)}/>
            </span>
          </span>
        )
      }
    },
    checkedFileTypeIsVideo(fileType) {
      var type = fileType.toLocaleLowerCase()
      var typeList = ['.mp4', '.avi', '.mov', '.rmvb', '.mpeg', '.flv']
      for (var i = 0; i < typeList.length; i++) {
        if (typeList[i].search(type) !== -1) {
          return true
        }
      }
      return false
    },
    restCourseResourceForm() {
      this.courseResourceForm = {
        name: '',
        sort: 0,
        duration: 0,
        fileType: '',
        url: '',
        resType: '',
        courseId: this.$route.query.id,
        courseDirectoryId: null,
        id: ''
      }
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function(dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0]
          this.list.splice(evt.newIndex, 0, targetRow)
          // for show the changes, you can delete in you code
          var orderData = {
            items: []
          }
          this.list.forEach((item, index) => {
            orderData.items.push({
              id: item.id,
              sort: (this.listQuery.page - 1) * this.listQuery.MaxResultCount + index + 1
            })
          })
          courseResourceSetOrder(orderData).then(res => {
            this.getCourseResourceList()
          }).catch(() => {})
        }
      })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getCourseDetail() {
      courseDetail(this.$route.query.id).then(res => {
        this.form.coverUrl = res.coverUrl
        this.form.number = res.number
        this.form.name = res.name
        this.pageTitle = res.name
        this.form.lecturer = res.lecturer
        this.form.classHour = res.classHour
        this.form.introduce = res.introduce
        this.form.courseCategoryId = res.courseCategoryId
        this.form.courseCategoryIds = res.courseCategoryIds
        // this.form.courseCategoryIds = ['7f74bf5f-20a2-517b-961b-3a07d84830b0', 'e4b2bda5-1e33-0187-313a-3a07d382f04a']
        this.showContent = true
        this.previewFileList = []
        if (this.form.coverUrl) {
          this.previewFileList.push({
            url: this.form.coverUrl
          })
        }
        this.totalDuration = res.resourceDuration
      }).catch(() => {

      })
    },
    async getCourseDirectoryAllList() {
      this.treeDataLoading = true
      const res = await courseDirectoryAllList(this.$route.query.id)
      this.treeData = res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter(f => f.parentId === item.id)
          })
          // .map(ele => {
          //     return { id: ele.id, parentId: ele.parentId, label: ele.name}})
        } else {
          item.children = list.filter(f => f.parentId === item.id)
        }
        return total
      }, [])
      if (this.listQuery.DirectoryId) {
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(this.listQuery.DirectoryId)
          }
        })
      } else if (this.treeData.length) {
        this.currentNodeData = this.treeData[0]
        this.currentNodeDataLabel = this.treeData[0].name
        this.listQuery.DirectoryId = this.treeData[0].id
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(this.treeData[0].id)
          }
        })
      }
      this.treeDataLoading = false
      this.getCourseResourceList()
    },
    getCourseResourceList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseResourceList(this.listQuery).then(res => {
        this.list = res.items
        // var duration = 0
        // res.items.forEach(item => {
        //   duration += item.duration
        // })
        // this.totalDuration = this.timeFormat(duration)
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
        this.$nextTick(() => {
          this.setSort()
        })
      }).catch(() => {
        this.listLoading = false
      })
    },
    getCourseDuration() {
      courseDuration(this.$route.query.id).then(res => {
        this.totalDuration = res
      })
    }
  }
  /**
     if (node.level < 3) {
          return (
            <span slot-scope = '{ node, data }' class = 'custom-tree-node' style = 'display: flex;width: 100%;justify-content: space-between;align-items: center;'>
              <span style='max-width: 190px;overflow: hidden;'>{ node.label }</span>
              <span>
                <el-button class='normal_button' type='text' icon='el-icon-plus' size='mini' circle on-click = {() => this.handleAdd(node, data)}/>
                <el-button class='normal_button' type='text' icon='el-icon-edit' size='mini' circle on-click = {() => this.handleEdit(node, data)}/>
                <el-button class='normal_button' type='text' icon='el-icon-delete' size='mini' circle on-click = {() => this.handleDelete(node, data)}/>
              </span>
            </span>
          )
        } else {
          return (
            <span slot-scope = '{ node, data }' class = 'custom-tree-node' style = 'display: flex;width: 100%;justify-content: space-between;align-items: center;'>
              <span style='max-width: 190px;overflow: hidden;'>{ node.label }</span>
              <span>
                <el-button class='normal_button' type='text' icon='el-icon-edit' size='mini' circle on-click = {() => this.handleEdit(node, data)}/>
                <el-button class='normal_button' type='text' icon='el-icon-delete' size='mini' circle on-click = {() => this.handleDelete(node, data)}/>
              </span>
            </span>
          )
        }
     */
}

</script>
<style scoped>
  /* .tinymce-container {
    width: 600px !important;
  } */
  .is-without-controls {
    width: 80px;
  }
   /* .el-tree >>> .custom-tree-node{
display: flex;
width: 100%;
justify-content: space-between;
align-items: center;
}
.el-tree >>> .custom-tree-node .node-lable{
max-width: 190px;overflow: hidden;font-size: 14px;
}
.el-tree >>> .custom-tree-node .node-buttons{

}
.el-tree >>> .el-tree-node__content{
  border-bottom: 1px solid #eee;
  height: 35px;
}
.card-padding-0 >>> .el-card__body
{
  padding: 0;
} */
</style>

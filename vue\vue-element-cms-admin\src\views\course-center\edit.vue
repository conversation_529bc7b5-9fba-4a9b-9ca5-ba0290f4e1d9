<template>
  <div class="app-container">
    <el-card v-loading="cardLoading" class="box-card">
      <div slot="header">
        <span>添加/编辑课程</span>
      </div>
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="基础信息" name="courseInfo">
          <el-form ref="courseCenterForm" :model="courseCenterForm" :rules="courseCenterRules" label-width="120px">
            <el-form-item v-if="!isEdit" label="选择课程" prop="courseId">
              <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleChooseCourseClick">选择课程
              </el-button>
            </el-form-item>
            <el-form-item v-if="courseCenterForm.courseId != ''" label="课程名称">
              <span>{{ courseInfo.courseName }}</span>
            </el-form-item>
            <el-form-item v-if="courseCenterForm.courseId != ''" label="课程封面">
              <el-image
                v-if="courseInfo.courseCoverUrl != ''"
                style="width: 300px; height: 200px"
                fit="cover"
                :src="courseInfo.courseCoverUrl"
              />
            </el-form-item>
            <el-form-item label="排序" prop="order">
              <el-input-number v-model="courseCenterForm.order" :step="1" :min="1" />
            </el-form-item>
            <el-form-item label="有效期" prop="endTime">
              <el-date-picker
                v-model="courseCenterForm.endTime"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                placeholder="选择日期时间"
              />
            </el-form-item>
            <el-form-item v-if="courseCenterForm.courseId != ''" label="课程参考课时" prop="endTime">
              <span>{{ courseInfo.classHour }}</span>
            </el-form-item>
            <el-form-item label="课程学时" prop="classHour">
              <el-input-number v-model="courseCenterForm.classHour" :min="0" :step="0.1" step-strictly />
            </el-form-item>
            <el-form-item v-if="courseCenterForm.courseId != ''" label="资源总时长" prop="endTime">
              <span>{{ courseInfo.resourceDuration | formatSecond }}</span>
            </el-form-item>
            <el-form-item label="合格时长" prop="passDuration">
              <el-input-number v-model="passDuration" :min="0" :step="1" step-strictly />
              分钟
            </el-form-item>
            <!-- <el-form-item prop="trainUsers" label="选择学员">
              <el-button :loading="userLoading" :disabled="userLoading" round size="small" type="primary" icon="el-icon-plus" @click="handleSelectStudent">选择学员</el-button>
              <el-button v-permission="['AppUserManagement.Classes']" round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">选择班级</el-button>
              <span style="margin-left: 20px">已选择{{ selectUsers.length }}人</span>
            </el-form-item> -->
            <!-- <el-form-item label="课程分类">
          <span>{{courseInfo.courseCategory}}</span>
           <el-select disabled placeholder="课程分类" size="small" v-model="courseInfo.courseCategoryId">
            <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item> -->
            <!-- <el-form-item label="是否免费" prop="freeModel">
          <el-radio-group v-model="courseCenterForm.freeModel" @change="handleFreeModelChange">
            <el-radio :label="0">免费</el-radio>
            <el-radio :label="1">收费</el-radio>
          </el-radio-group>
        </el-form-item> -->
            <!-- <el-form-item label="学习期限" prop="learningPeriod">
              <el-input-number v-model="courseCenterForm.learningPeriod" :min="0" :max="365" /><span> 天</span>
            </el-form-item> -->
            <el-form-item v-if="courseCenterForm.freeModel" label="试看课件" prop="coursewareProveds">
              <el-button round type="primary" icon="el-icon-plus" @click="handleChooseCourseResource">选择课件
              </el-button>
              <el-table size="small" :data="resourceList" highlight-current-row>
                <el-table-column label="课件名称" prop="courseResourceName" />
                <el-table-column label="试看时长" width="200">
                  <template slot-scope="{ row }">
                    <el-input-number v-model="row.time" :min="1" :step="1" /><span> 分钟</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="{ row }">
                    <el-button
                      round
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      @click="handleRemoveCourseResource(row)"
                    >移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item>
              <el-button round :loading="saveLoading" type="primary" icon="el-icon-check" @click="handleSaveEdit">保存
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="用户管理" name="courseUser">
          <div class="header_flex_box">
            <el-input
              v-model="selectUsersListQuery.Filter"
              size="small"
              class="small_input"
              clearable
              placeholder="输入名称搜索"
            />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshSelectUserList">搜索
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectCourseStudent">选择学员
            </el-button>
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleCourseUserDelete">批量删除
            </el-button>
            <el-button :loading="exportUserLoading" :disabled="exportUserLoading" round size="mini" type="success" icon="el-icon-download" @click="handleExportUserJsonData">导出JSON
            </el-button>
            <export-excel
              :header="['姓名', '用户名', '部门', '允许学习']"
              :filter-val="['name', 'userName', 'className', 'cloudLearn']"
              :query="{'CourseCenterId': $route.query.id}"
              :field="{ 3: ['否', '是']}"
              :api-fn="courseCenterUserList"
            />

            <el-button round size="mini" type="primary" :disabled="registerUserLoading1" :loading="registerUserLoading1" icon="el-icon-user" @click="handleHundunUserRegister">批量注册-混沌
            </el-button>
            <el-button round size="mini" type="primary" :disabled="registerUserLoading2" :loading="registerUserLoading2" icon="el-icon-user" @click="handleGeekUserRegister">批量注册-极客
            </el-button>

          </div>
          <el-table
            v-loading="selectUsersListLoading"
            :data="selectUsersList"
            max-height="700px"
            size="small"
            @sort-change="handleSelectUserSortChange"
            @selection-change="handleDeleteUserChange"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column label="姓名" prop="name" sortable="name" />
            <el-table-column label="用户名" prop="userName" sortable="userName" />
            <el-table-column label="部门" prop="className" sortable="className" />
            <el-table-column label="允许学习" prop="cloudLearn" sortable="cloudLearn">
              <template slot-scope="{ row }">
                {{ row.cloudLearn ? "是" : "否" }}
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="selectUsersListQuery.totalCount > 0"
            :total="selectUsersListQuery.totalCount"
            :page.sync="selectUsersListQuery.page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :limit.sync="selectUsersListQuery.MaxResultCount"
            @pagination="getCourseCenterUserList"
          />
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="课程考评" name="courseExam">
          <el-form ref="courseExamForm" :model="form" :rules="formRules" label-width="160px">
            <el-form-item label="课程考评" prop="courseId">
              <el-switch v-model="courseExam" active-color="#13ce66" inactive-color="#ff4949" />
            </el-form-item>
            <div v-if="courseExam">
              <el-form-item label="选择试卷" prop="examPaperId">
                <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleChooseExamPaperClick">
                  选择试卷</el-button>
                <el-table ref="courseExamPaperTable" :data="courseExamPaperList" highlight-current-row>
                  <el-table-column label="试卷名称" prop="name" />
                  <el-table-column label="题数" prop="questionNumber" />
                  <el-table-column label="总分" prop="totalScore" />
                </el-table>
              </el-form-item>
              <el-form-item label="考评名称" prop="courseExam.examName">
                <el-input v-model="form.courseExam.examName" />
              </el-form-item>
              <el-form-item v-if="courseExamPaperList.length" label="合格分数" prop="order">
                <el-input-number
                  v-model="form.courseExam.passScore"
                  :min="0"
                  :step="0.1"
                  step-strictly
                  :max="courseExamPaperList[0].totalScore"
                />
                分
              </el-form-item>
              <!-- <el-form-item label="考评时长" prop="order">
                <el-input-number v-model="form.courseExam.examTimeLong" :min="5" :step="1" step-strictly />
              </el-form-item> -->
              <el-form-item label="考评通过才能获取学时" prop="passExamGetHour">
                <el-radio-group v-model="form.passExamGetHour">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否学习完成才能考评" prop="canExamAfterLearn">
                <el-radio-group v-model="form.canExamAfterLearn">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <el-form-item>
              <el-button round :loading="saveLoading" type="primary" icon="el-icon-check" @click="handleSaveCourseExam">
                保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-dialog title="选择课程" :close-on-click-modal="false" :visible.sync="chooseCourseDialog" width="1000px" top="5vh">
      <div class="header_flex_box">
        <!-- <el-select
          v-model="courseListQuery.CourseCategoryId"
          placeholder="选择课程分类"
          size="small"
          @change="handleRefreshList"
        >
          <el-option label="全部" :value="''" />
          <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
        <el-cascader v-model="courseListQuery.CourseCategoryId" filterable clearable :options="courseCategoryList" :props="{'label': 'name','value': 'id','children': 'children','checkStrictly': true,'emitPath': false}" size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
        <el-input v-model="courseListQuery.Filter" size="small" class="small_input" placeholder="输入名称搜索" />
        <el-button round type="success" size="small" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      </div>
      <el-table
        ref="courseTable"
        v-loading="courseListLoading"
        :row-key="getRowKeys"
        :data="courseList"
        size="small"
        highlight-current-row
        @selection-change="handleChooseCourseChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="课程封面" prop="coverUrl" width="140">
          <template slot-scope="{ row }">
            <el-image :src="row.coverUrl" style="width: 96px; height: 54px" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="name" />
        <el-table-column label="课程编号" prop="number" sortable="number" />
        <el-table-column label="课程讲师" prop="lecturer" sortable="lecturer" />
        <el-table-column label="课时" prop="classHour" width="80" sortable="classHour" />
        <el-table-column label="上架状态" prop="status" width="120">
          <template slot-scope="{ row }">
            <el-tag v-if="row.status === 0" size="mini" type="info">未上架</el-tag>
            <el-tag v-if="row.status === 1" size="mini" type="success">已上架</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="courseListQuery.totalCount > 0"
        :total="courseListQuery.totalCount"
        :page.sync="courseListQuery.page"
        :limit.sync="courseListQuery.MaxResultCount"
        @pagination="getUnCourseList"
      />

      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseCourseSure">确 定</el-button>
        <el-button round @click="chooseCourseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择课件" :close-on-click-modal="false" :visible.sync="courseResourceDialog" width="1200px" top="5vh">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-card class="box-card card-padding-0" shadow="never">
            <div slot="header">
              <span class="role-span">课程目录</span>
            </div>
            <el-tree
              ref="tree"
              class="course-dir-tree"
              style="height: 550px; overflow-x: hidden; overflow-y: auto"
              :data="treeData"
              :props="treeProps"
              :highlight-current="true"
              default-expand-all
              node-key="id"
              :expand-on-click-node="false"
              @node-click="handleTreeNodeClick"
            />
            <!-- <el-tree ref="tree" style="height: 500px; overflow: auto" :data="treeData" :highlight-current="true" node-key="id" :props="treeProps" :node-key="id" default-expand-all :expand-on-click-node="false" @node-click="handleTreeNodeClick" /> -->
          </el-card>
        </el-col>
        <el-col :span="18">
          <el-card class="box-card" shadow="never">
            <div slot="header">
              <span>{{ currentNodeDataLabel }}</span>
            </div>
            <el-table
              ref="resourceTable"
              v-loading="listLoading"
              :row-key="getRowKeys"
              row-key="id"
              :data="list"
              size="small"
              height="415px"
              highlight-current-row
              @selection-change="handleChooseCourseResourceChange"
            >
              <el-table-column type="selection" width="55" align="center" :selectable="checkFileType" />
              <el-table-column label="资源名称" prop="name" />
              <el-table-column label="格式" prop="fileType" width="80" />
              <el-table-column label="大小" prop="duration" width="100">
                <template slot-scope="{ row }">
                  <span>{{ row.size | formatFileSize }} </span>
                </template>
              </el-table-column>
              <el-table-column label="时长" prop="duration" width="160">
                <template slot-scope="{ row }">
                  <span>{{ row.duration | formatSecond }} </span>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="listQuery.totalCount > 0"
              :total="listQuery.totalCount"
              :page.sync="listQuery.page"
              :limit.sync="listQuery.MaxResultCount"
              @pagination="getCourseResourceList"
            />
          </el-card>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseCourseResourceSure">确 定</el-button>
        <el-button round @click="courseResourceDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择学生" :visible.sync="chooseUserDialog" :close-on-click-modal="false" width="1000px">
      <choose-user-permission
        v-if="chooseUserDialog"
        :all-org="allOrg"
        :all-student="allStudent"
        :current-select-student="selectUsers"
        @user-change="chooseStudent"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseUserSure">确 定</el-button>
        <el-button round @click="chooseUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择学生" :visible.sync="selectUserDialog" :close-on-click-modal="false" width="1000px">
      <select-user v-if="selectUserDialog" :all-org="allOrg" @response-fn="handleSelectUserResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="selectUserDialogLoading" round type="primary" @click="handleSelectUserSure">确 定</el-button>
        <el-button round @click="selectUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择试卷" :close-on-click-modal="false" :visible.sync="examPaperChooseDialog">
      <choose-exam-paper
        v-if="examPaperChooseDialog"
        :current-paper="currentPaperId"
        @response="handleCourseSelectExamPaper"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleCourseSelectExamPaperSure">确 定</el-button>
        <el-button round @click="examPaperChooseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  courseList,
  courseCategoryList,
  courseResourceList,
  courseDirectoryAllList,
  courseCenterEdit,
  courseCenterAdd,
  courseCenterDetail,
  uncourseCenterList,
  courseCenterAllUser,
  courseCenterUserReg,
  coursePermission,
  courseCenterUserList,
  courseCenterUserDelete,
  courseExamInfo,
  courseExamEdit,
  courseDetail
} from '@/api/course'
import { loadNodes, getAllStudents, findName, classesUsers } from '@/api/user'
import { examPaperDetailInfo } from '@/api/examPaper'
import ChooseUserPermission from '@/components/ChooseUserPermission/choose.vue'
import ChooseClass from '@/components/ChooseClass'
import SelectUser from '@/components/ChooseUserPermission/user.vue'
import Pagination from '@/components/Pagination'
import ChooseExamPaper from '@/components/ChooseExamPaper'
import permission from '@/directive/permission'
import { parseTimeDate } from '@/utils'
import { getEnterpriseToken, batchRegistGeek } from '@/api/geektimeapi'
import { batchRegistHundun } from '@/api/hundunapi'
export default {
  name: 'CenterEdit',
  directives: {
    permission
  },
  components: {
    Pagination,
    ChooseUserPermission,
    ChooseClass,
    SelectUser,
    ChooseExamPaper
  },
  // computed() {
  //   [this.courseCenterForm.passDuration]: {
  //     get(val) {
  //       return val * 60
  //     },
  //     set(val) {
  //       return val / 60
  //     }
  //   }
  // },
  data() {
    var courseValidator = (rule, value, callback) => {
      if (!this.courseCenterForm.courseId) {
        callback(new Error('请选择课程'))
      } else {
        callback()
      }
    }
    var courseResource = (rule, value, callback) => {
      if (this.courseCenterForm.coursewareProveds.length === 0) {
        callback(new Error('请选择课件'))
      } else {
        callback()
      }
    }
    var userValid = (rule, value, callback) => {
      if (this.selectUsers.length === 0) {
        callback(new Error('请选择用户'))
      } else {
        callback()
      }
    }
    var validateExamPaper = (rule, value, callback) => {
      if (this.form.courseExam.examPaperId.length === 0) {
        callback(new Error('请选择试卷'))
      } else {
        callback()
      }
    }

    return {
      // 获取详情LOADING
      cardLoading: false,
      saveLoading: false,
      previewFileList: [],
      courseCenterForm: {
        courseId: '',
        freeModel: 0,
        order: 1,
        learningPeriod: 0,
        coursewareProveds: [],
        courseCenterUsers: [],
        endTime: '',
        passDuration: 0,
        classHour: 0
      },
      passDuration: 0,

      courseInfo: {
        courseName: '',
        courseCoverUrl: '',
        classHour: 0,
        resourceDuration: 0
        // courseCategory: ''
      },
      // 选择课程dialog

      chooseCourseDialog: false,
      courseList: [],
      courseListLoading: false,
      courseListQuery: {
        Filter: '',
        CourseCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      selectedCourse: [],
      courseCategoryList: [],
      // 选择课件Dialog
      courseResourceDialog: false,
      treeData: [],
      treeProps: {
        id: 'id',
        label: 'name',
        parentId: 'parrentId',
        children: 'children'
      },
      currentNodeDataLabel: '全部',
      // 课程资源列表
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseId: '',
        DirectoryId: null,
        Sorting: 'sort',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      selectedResource: [],
      resourceList: [],

      // 班级数据
      allOrg: [],
      // 全部学生
      allStudent: [],
      userLoading: false,
      // 选择用户Dialog
      chooseUserDialog: false,
      selectUsers: [],

      courseCenterRules: {
        courseId: [
          {
            required: true,
            validator: courseValidator,
            trigger: 'blur'
          }
        ],
        coursewareProveds: [
          {
            required: true,
            validator: courseResource,
            trigger: 'blur'
          }
        ]
      },

      isEdit: !!this.$route.query.id,

      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],

      activeTabName: 'courseInfo',
      selectUserDialog: false,
      selectUserDialogLoading: false,
      currentSelectUsers: [],
      exportUserLoading: false,
      selectUsersList: [],
      selectUsersListLoading: false,
      selectUsersListQuery: {
        Filter: '',
        CourseCenterId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      currentDeleteUsers: [],

      courseExamPaperList: [],
      courseExam: false,
      form: {
        courseCenterId: this.$route.query.id,
        passExamGetHour: false,
        canExamAfterLearn: false,
        courseExam: {
          courseId: '',
          examName: '',
          examTimeLong: 5,
          examPaperId: '',
          passScore: 0
        }
      },
      formRules: {
        examPaperId: [
          { required: true, validator: validateExamPaper, trigger: 'blur' }
        ],
        courseExam: {
          examName: [
            { required: true, message: '请输入考核名称', trigger: 'blur' },
            {
              min: 1,
              max: 30,
              message: '长度在 1 到 30 个字符',
              trigger: 'blur'
            }
          ]
        }
      },
      examPaperChooseDialog: false,
      currentPaperId: '',
      currentSelectPaper: null,
      registerUserLoading1: false,
      registerUserLoading2: false
    }
  },
  created() {
    this.loadClassList()
    if (this.$route.query.id) {
      this.getCourseCenterDetail()
      this.getCourseCenterUserList()
    }
    this.getCourseCategoryList()
    // this.getUnCourseList()
  },
  methods: {
    courseCenterUserList(args) {
      return courseCenterUserList(args)
    },
    handleTabClick(tab, event) { },
    handleSelectCourseStudent() {
      this.currentSelectUsers = []
      this.selectUserDialog = true
    },
    handleSelectUserResponse(val) {
      this.currentSelectUsers = val
    },
    handleSelectUserSure() {
      if (this.currentSelectUsers.length) {
        this.selectUserDialogLoading = true
        var form = {
          courseCenterId: this.$route.query.id,
          courseCenterUsers: []
        }
        this.currentSelectUsers.forEach((item) => {
          form.courseCenterUsers.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            cloudLearn: item.cloudLearn
          })
        })
        var _this = this
        coursePermission(form)
          .then((res) => {
            setTimeout(function() {
              _this.selectUserDialog = false
              _this.selectUserDialogLoading = false
              _this.$message.success('操作成功，稍后刷新查看结果')
              _this.getCourseCenterUserList()
            }, 1000 * 5)
          })
          .catch(() => {
            this.selectUserDialogLoading = false
          })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleDeleteUserChange(val) {
      this.currentDeleteUsers = val
    },
    handleCourseUserDelete() {
      if (this.currentDeleteUsers.length) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            var form = {
              courseCenterId: this.$route.query.id,
              courseCenterUserIds: []
            }
            this.currentDeleteUsers.forEach((item) => {
              form.courseCenterUserIds.push(item.id)
            })
            courseCenterUserDelete(form)
              .then((res) => {
                this.$message.success('删除成功')
                this.currentDeleteUsers = []
                this.getCourseCenterUserList()
              })
              .catch(() => {
                this.$message.error('删除失败')
              })
          })
          .catch(() => {
            this.$message.info('已取消删除')
          })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    // 混沌 批量注册
    handleHundunUserRegister() {
      this.$prompt('请输入密钥', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: function(value) {
          if (value === 'qc.ciep-pimp.com') {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '密钥不正确'
      }).then(({ value }) => {
        this.registerUserLoading1 = true
        courseCenterUserReg({
          type: 1,
          id: this.$route.query.id
        }).then((res) => {
          this.$message.success('操作成功，后台正在注册中,稍后刷新查看结果')
          this.registerUserLoading1 = false
        })
      }).catch(() => {

      })

      // courseCenterAllUser(this.$route.query.id).then((res) => {
      //   // 点击下载
      //   const totalUserList = []
      //   res.items.forEach(element => {
      //     const arr = {
      //       user_no: element.userId,
      //       user_name: element.userName
      //     }
      //     totalUserList.push(arr)
      //   })
      //   console.log(totalUserList)
      //   batchRegistHundun(totalUserList)

      //   this.registerUserLoading1 = false
      // }).catch(() => {
      //   this.registerUserLoading1 = false
      // })
    },
    // 极客 批量注册
    handleGeekUserRegister() {
      this.$prompt('请输入密钥', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: function(value) {
          if (value === 'qc.ciep-pimp.com') {
            return true
          } else {
            return false
          }
        },
        inputErrorMessage: '密钥不正确'
      }).then(({ value }) => {
        this.registerUserLoading2 = true
        courseCenterUserReg({
          type: 2,
          id: this.$route.query.id
        }).then((res) => {
          this.$message.success('操作成功，后台正在注册中,稍后刷新查看结果')
          this.registerUserLoading2 = false
        })
      }).catch(() => {

      })
      // this.registerUserLoading2 = true
      // courseCenterAllUser(this.$route.query.id).then((res) => {
      //   // 点击下载
      //   const totalUserList = []
      //   res.items.forEach(element => {
      //     const arr = {
      //       user_no: element.userId,
      //       user_name: element.userName
      //     }
      //     totalUserList.push(arr)
      //   })
      //   getEnterpriseToken()
      //   batchRegistGeek(totalUserList)
      //   // batchRegistGeek()
      //   this.registerUserLoading2 = false
      // }).catch(() => {
      //   this.registerUserLoading2 = false
      // })
    },
    handleExportUserJsonData() {
      this.exportUserLoading = true
      courseCenterAllUser(this.$route.query.id).then((res) => {
        // 点击下载
        var data = JSON.stringify(res.items)
        // encodeURIComponent解决中文乱码
        const uri =
          'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(data)
        // 通过创建a标签实现
        const link = document.createElement('a')
        link.href = uri
        // 对下载的文件命名
        link.download = '学员数据.json'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.exportUserLoading = false
      }).catch(() => {
        this.exportUserLoading = false
      })
    },
    handleRefreshSelectUserList() {
      this.selectUsersListQuery.page = 1
      this.getCourseCenterUserList()
    },
    // 选择课程
    handleChooseCourseClick() {
      this.chooseCourseDialog = true
      this.getUnCourseList()
      this.$nextTick(() => {
        if (this.selectedCourse.length > 0) {
          this.$refs.courseTable.toggleRowSelection(
            this.selectedCourse[0],
            true
          )
        }
      })
    },

    handleChooseCourseChange(val) {
      if (val.length > 1) {
        this.$refs.courseTable.clearSelection()
        this.$nextTick(() => {
          this.$refs.courseTable.toggleRowSelection(val.pop())
        })
      } else {
        this.selectedCourse = val
      }
    },
    handleChooseCourseSure() {
      if (this.selectedCourse.length > 0) {
        this.courseInfo.courseName = this.selectedCourse[0].name
        this.courseInfo.courseCoverUrl = this.selectedCourse[0].coverUrl
        this.courseInfo.classHour = this.selectedCourse[0].classHour
        this.courseInfo.resourceDuration =
          this.selectedCourse[0].resourceDuration

        this.courseCenterForm.classHour = this.courseInfo.classHour
        this.passDuration = this.courseInfo.resourceDuration / 60
        // for(var i= 0; i < this.courseCategoryList.length; i ++) {
        //   if (this.courseCategoryList[i].id === this.selectedCourse[0].courseCategoryId) {
        //     this.courseInfo.courseCategory = this.courseCategoryList[i].name
        //     break
        //   }
        // }
        this.courseCenterForm.courseId = this.selectedCourse[0].id
        this.courseCenterForm.learningPeriod =
          this.selectedCourse[0].learningPeriod
        this.listQuery.CourseId = this.selectedCourse[0].id
        this.chooseCourseDialog = false

        this.getCourseDirectoryAllList(this.selectedCourse[0].id)
        this.getCourseResourceList()
      } else {
        this.$message.warning('请选择课程')
      }
    },
    // 收费模式变化
    handleFreeModelChange(val) { },
    // 选择课件
    handleChooseCourseResource() {
      this.selectedResource = []
      this.courseResourceDialog = true
      if (this.treeData.length) {
        this.currentNodeDataLabel = this.treeData[0].name
        this.listQuery.DirectoryId = this.treeData[0].id
        this.getCourseResourceList()
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(this.treeData[0].id)
          }
        })
      }
    },
    handleChooseCourseResourceChange(val) {
      this.selectedResource = val
    },
    handleChooseCourseResourceSure() {
      this.selectedResource.forEach((item) => {
        this.resourceList.push({
          courseCenterId: this.$route.query ? this.$route.query.id : null,
          courseId: item.courseId,
          courseResourceId: item.id,
          time: Number.parseInt(item.duration / 60),
          url: item.url,
          courseResourceName: item.name
        })
      })
      // 去重
      this.resourceList = this.resourceList.reduce((prev, cur, index, arr) => {
        const findIndex = prev.findIndex(
          (i) => i.courseResourceId === cur.courseResourceId
        )
        if (findIndex === -1) {
          prev.push(cur)
        }
        return prev
      }, [])
      this.courseCenterForm.coursewareProveds = this.resourceList
      this.courseResourceDialog = false
    },
    handleRemoveCourseResource(row) {
      for (var i = 0; i < this.resourceList.length; i++) {
        if (this.resourceList[i].courseResourceId === row.courseResourceId) {
          this.resourceList.splice(i, 1)
          break
        }
      }
      this.courseCenterForm.coursewareProveds = this.resourceList
    },
    handleTreeNodeClick(data) {
      this.currentNodeDataLabel = data.name
      this.listQuery.DirectoryId = data.id
      this.getCourseResourceList()
    },
    handleSaveEdit() {
      this.$refs.courseCenterForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true

          if (this.courseCenterForm.freeModel === 0) {
            this.courseCenterForm.coursewareProveds = []
          } else {
            this.courseCenterForm.coursewareProveds = this.resourceList
          }
          this.courseCenterForm.passDuration = this.passDuration * 60
          if (this.$route.query.id) {
            courseCenterEdit(this.$route.query.id, this.courseCenterForm)
              .then((res) => {
                this.$message.success('保存成功')
                this.saveLoading = false
              })
              .catch(() => {
                this.$message.error('保存失败')
                this.saveLoading = false
              })
          } else {
            courseCenterAdd(this.courseCenterForm)
              .then((res) => {
                this.$message.success('添加成功')
                this.saveLoading = false
                this.$router.go(-1)
              })
              .catch(() => {
                this.$message.error('添加失败')
                this.saveLoading = false
              })
          }
        } else {
          return false
        }
      })
    },
    // 选择学生
    handleSelectStudent() {
      this.chooseUserDialog = true
    },
    // 选择学生组件传值获取选择的学生
    chooseStudent(val) {
      this.selectUsers = val
    },
    // 选择用户确定
    handleChooseUserSure() {
      this.dialogSureLoading = true
      var tmp = []
      this.selectUsers.forEach((item) => {
        tmp.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties
            ? item.extraProperties.OUId
            : item.classId,
          className: item.extraProperties
            ? item.extraProperties.OUName
            : item.className,
          userName: item.userName,
          name: item.name,
          cloudLearn: item.cloudLearn
        })
      })
      this.courseCenterForm.courseCenterUsers = tmp
      this.dialogSureLoading = false
      this.chooseUserDialog = false
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var newArr = []
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        res.items.forEach((item) => {
          newArr.push({
            id: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            cloudLearn: true
          })
        })
      }
      this.selectUsers = this.selectUsers.concat(newArr)

      var setA = new Set()
      this.selectUsers = this.selectUsers.filter((item) => {
        const result = setA.has(item.id)
        setA.add(item.id)
        return !result
      })
      var tmp = []
      this.selectUsers.forEach((item) => {
        tmp.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties
            ? item.extraProperties.OUId
            : item.classId,
          className: item.extraProperties
            ? item.extraProperties.OUName
            : item.className,
          userName: item.userName,
          name: item.name,
          cloudLearn: true
        })
      })
      this.classLoading = false
      this.courseCenterForm.courseCenterUsers = newArr
      this.chooseClassDialog = false
    },
    handleChooseExamPaperClick() {
      this.examPaperChooseDialog = true
    },
    handleCourseSelectExamPaper(val) {
      this.currentSelectPaper = val
    },
    handleCourseSelectExamPaperSure() {
      this.courseExamPaperList = []
      if (this.currentSelectPaper) {
        this.courseExamPaperList.push(this.currentSelectPaper)
        this.form.courseExam.examPaperId = this.currentSelectPaper.id
        this.currentPaperId = this.currentSelectPaper.id
      }
      this.examPaperChooseDialog = false
    },
    handleSaveCourseExam() {
      if (this.courseExam === false) {
        this.form.courseExam = null
        this.courseExamPaperList = []
        this.currentPaperId = []
        this.currentSelectPaper = null
      }
      if (this.courseExam === true) {
        this.form.courseExam.courseId = this.courseCenterForm.courseId
      }
      this.$refs.courseExamForm.validate((valid) => {
        if (valid) {
          courseExamEdit(this.form).then((res) => {
            this.$message.success('保存成功')
            if (!this.form.courseExam) {
              this.form.courseExam = {
                courseId: this.courseCenterForm.courseId,
                examName: '',
                examTimeLong: 5,
                examPaperId: '',
                passScore: 0
              }
            }
          })
        } else {
          return false
        }
      })
    },
    handleRefreshList() {
      this.courseListQuery.page = 1
      this.getUnCourseList()
    },
    getRowKeys(row) {
      return row.id
    },
    checkFileType(row) {
      if (row.fileType !== '.mp4') {
        return false
      }
      return true
    },
    handleSelectUserSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getCourseCenterUserList()
        return
      }
      this.selectUsersListQuery.Sorting = prop + ' ' + order
      this.getCourseCenterUserList()
    },
    getUnCourseList() {
      this.courseListLoading = true
      this.courseListQuery.SkipCount =
        (this.courseListQuery.page - 1) * this.courseListQuery.MaxResultCount
      uncourseCenterList(this.courseListQuery).then((res) => {
        this.courseList = res.items
        this.courseListQuery.totalCount = res.totalCount
        this.courseListLoading = false
      })
    },
    async getCourseResourceList() {
      this.listLoading = true
      // this.listQuery.CourseId = id
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      await courseResourceList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getCourseDirectoryAllList(id) {
      courseDirectoryAllList(id).then((res) => {
        this.treeData = res.items.reduce((total, item, index, list) => {
          if (item.parentId === null) {
            total.push({
              ...item,
              children: list.filter((f) => f.parentId === item.id)
            })
            // .map(ele => {
            //     return { id: ele.id, parentId: ele.parentId, label: ele.name}})
          } else {
            item.children = list.filter((f) => f.parentId === item.id)
          }
          return total
        }, [])
      })
    },
    async getCourseCenterDetail() {
      this.cardLoading = true
      await courseCenterDetail(this.$route.query.id)
        .then((res) => {
          this.getCourseDetail(res.courseId)
          this.courseInfo.courseName = res.courseName
          this.courseInfo.courseCoverUrl = res.courseCoverUrl
          this.courseCenterForm.courseId = res.courseId
          this.courseCenterForm.order = res.order
          this.courseCenterForm.passDuration = res.passDuration
          this.passDuration = res.passDuration / 60
          this.courseCenterForm.classHour = res.classHour
          this.courseCenterForm.endTime = parseTimeDate(res.endTime)
          this.courseCenterForm.learningPeriod = res.learningPeriod
          this.form.canExamAfterLearn = res.canExamAfterLearn
          this.form.passExamGetHour = res.passExamGetHour
          // this.getCourseDirectoryAllList(res.courseId)

          this.courseCenterForm.coursewareProveds = res.coursewareProveds
          this.resourceList = res.coursewareProveds
          this.courseCenterForm.freeModel = res.freeModel
          this.cardLoading = false
        })
        .catch(() => {
          this.cardLoading = false
        })
      await this.getCourseExamDetail()
    },
    getCourseCenterUsers() {
      this.userLoading = true
      courseCenterAllUser(this.$route.query.id).then((res) => {
        res.items.forEach((item) => {
          this.selectUsers.push({
            id: item.userId,
            userId: item.userId,
            userName: item.userName,
            name: item.name,
            classId: item.classId,
            className: item.className,
            cloudLearn: item.cloudLearn
          })
        })
        this.userLoading = false
      })
    },
    async loadClassList() {
      // this.userLoading = true
      loadNodes().then((res) => {
        this.allOrg = res.items
      })

      // if (this.$route.query.id) {
      //   this.getCourseCenterUsers()
      // }
      // this.userLoading = false
    },
    getCourseCenterUserList() {
      this.selectUsersListLoading = true
      this.selectUsersListQuery.SkipCount =
        (this.selectUsersListQuery.page - 1) *
        this.selectUsersListQuery.MaxResultCount
      courseCenterUserList(this.selectUsersListQuery).then((res) => {
        this.selectUsersList = res.items
        this.selectUsersListQuery.totalCount = res.totalCount
        this.selectUsersListLoading = false
      })
    },
    getCourseExamDetail() {
      var data = {
        Filter: '',
        CourseId: this.courseCenterForm.courseId,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10
      }
      courseExamInfo(data).then((res) => {
        if (res.items.length === 0) {
          this.courseExam = false
        } else {
          this.courseExam = true
          this.courseExamPaperList = res.items
          this.form.courseExam.examName = res.items[0].examName
          this.form.courseExam.examTimeLong = res.items[0].examTimeLong
          this.form.courseExam.examPaperId = res.items[0].examPaperId
          this.currentPaperId = res.items[0].examPaperId
          this.form.courseExam.passScore = res.items[0].passScore
          this.form.courseExam.courseId = this.courseCenterForm.courseId
          this.getPaperInfoDetail(res.items[0].examPaperId)
        }
      })
    },
    // 获取试卷详情
    getPaperInfoDetail(id) {
      this.courseExamPaperList = []
      examPaperDetailInfo(id).then((res) => {
        this.courseExamPaperList.push(res)
      })
    },
    getCourseDetail(id) {
      courseDetail(id).then((res) => {
        this.courseInfo.courseName = res.name
        this.courseInfo.courseCoverUrl = res.coverUrl
        this.courseInfo.classHour = res.classHour
        this.courseInfo.resourceDuration = res.resourceDuration
      })
    }
  }
}
</script>

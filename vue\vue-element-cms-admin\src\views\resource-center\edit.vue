<template>
  <div class="app-container">
    <el-card v-loading="cardLoading" class="box-card">
      <div slot="header">
        <span>添加/编辑资源</span>
      </div>
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="基础信息" name="knowledgeInfo">
          <el-form ref="resourceCenterForm" :model="resourceCenterForm" :rules="resourceCenterRules" label-width="120px">
            <el-form-item v-if="!isEdit" label="选择资源" prop="knowledgeResourceId">
              <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleChooseResourceClick">选择资源</el-button>
            </el-form-item>
            <el-form-item v-if="resourceCenterForm.knowledgeResourceId!==''" label="资源名称">
              <span>{{ resourceInfo.resourceName }}</span>
            </el-form-item>
            <el-form-item v-if="resourceCenterForm.knowledgeResourceId!==''" label="资源封面">
              <el-image v-if="resourceInfo.resourceCoverUrl!=''" style="width: 200px;" fit="cover" :src="resourceInfo.resourceCoverUrl" />
            </el-form-item>
            <!-- <el-form-item prop="trainUsers" label="选择学员">
              <el-button :loading="userLoading" :disabled="userLoading" round size="small" type="primary" icon="el-icon-plus" @click="handleSelectStudent">选择学员</el-button>
              <el-button v-permission="['AppUserManagement.Classes']" round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">选择班级</el-button>
              <span style="margin-left: 20px">已选择{{ selectUsers.length }}人</span>
            </el-form-item> -->
            <!-- <el-form-item label="资源分类">
          <span>{{resourceInfo.resourceCategory}}</span>
           <el-select disabled placeholder="资源分类" size="small" v-model="resourceInfo.KnowledgeCategoryId">
            <el-option v-for="item in resourceCategoryList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item> -->
            <!-- <el-form-item label="是否免费" prop="freeModel">
          <el-radio-group v-model="resourceCenterForm.freeModel" @change="handleFreeModelChange">
            <el-radio :label="0">免费</el-radio>
            <el-radio :label="1">收费</el-radio>
          </el-radio-group>
        </el-form-item> -->
            <!-- <el-form-item label="学习期限" prop="learningPeriod">
          <el-input-number v-model="resourceCenterForm.learningPeriod" :min="0" :max="365" /><span> 天</span>
        </el-form-item> -->

            <el-form-item>
              <el-button round :loading="saveLoading" type="primary" icon="el-icon-check" @click="handleSaveEdit">保存</el-button>
            </el-form-item>

          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="用户管理" name="courseUser">
          <div class="header_flex_box">
            <el-input v-model="selectUsersListQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshSelectUserList">搜索</el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectCourseStudent">选择学员</el-button>
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleCourseUserDelete">批量删除</el-button>
            <export-excel :header="['姓名', '用户名', '部门', '允许学习']"
              :filter-val="['name', 'userName', 'className', 'cloudLearn']"
              :field="{ 3: ['否', '是'] }" :query="{'KnowledgeCenterId': $route.query.id}" :api-fn="resourceCenterUserList" />
          </div>
          <el-table v-loading="selectUsersListLoading" :data="selectUsersList" max-height="700px" size="small" @selection-change="handleDeleteUserChange">
            <el-table-column type="selection" align="center" />
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="用户名" prop="userName" />
            <el-table-column label="部门" prop="className" />
            <el-table-column label="允许学习" prop="cloudLearn">
              <template slot-scope="{row}">
                {{ row.cloudLearn?'是':'否' }}
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="selectUsersListQuery.totalCount > 0"
            :total="selectUsersListQuery.totalCount"
            :page.sync="selectUsersListQuery.page"
            :page-sizes="[10,20,50,100,500, 1000]"
            :limit.sync="selectUsersListQuery.MaxResultCount"
            @pagination="getResourceCenterUserList"
          />
        </el-tab-pane>
      </el-tabs>

    </el-card>
    <el-dialog title="选择资源" :close-on-click-modal="false" :visible.sync="chooseResourceDialog" width="1000px" top="5vh">
      <div class="header_flex_box">
        <el-select
          v-model="resourceListQuery.KnowledgeCategoryId"
          placeholder="选择资源分类"
          size="small"
          @change="handleRefreshList"
        >
          <el-option label="全部" :value="''" />
          <el-option v-for="item in resourceCategoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model="resourceListQuery.Filter" size="small" class="small_input" placeholder="输入名称搜索" />
        <el-button round type="success" size="small" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      </div>
      <el-table
        ref="resourceTable"
        v-loading="resourceListLoading"
        :row-key="getRowKeys"
        :data="resourceList"
        size="small"
        highlight-current-row
        @selection-change="handleChooseResourceChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="资源封面" prop="thumbnailUrl" width="140">
          <template slot-scope="{ row }">
            <el-image :src="row.thumbnailUrl" style="width: 96px; height: 54px" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="资源名称" prop="name" />
        <el-table-column label="资源编号" prop="number" sortable="number" />
        <el-table-column label="作者" prop="author" sortable="lecturer" />
        <el-table-column label="资源时长" prop="duration" sortable="duration" width="100">
          <template slot-scope="{ row }">
            {{ row.duration | formatSecond }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="课时" prop="classHour" width="80" sortable="classHour" />
        <el-table-column label="上架状态" prop="status" width="120">
          <template slot-scope="{ row }">
            <el-tag v-if="row.status === 0" size="mini" type="info">未上架</el-tag>
            <el-tag v-if="row.status === 1" size="mini" type="success">已上架</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="resourceListQuery.totalCount > 0"
        :total="resourceListQuery.totalCount"
        :page.sync="resourceListQuery.page"
        :limit.sync="resourceListQuery.MaxResultCount"
        @pagination="getUnResourceList"
      />

      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseResourceSure">确 定</el-button>
        <el-button round @click="chooseResourceDialog = false">取 消</el-button>

      </span>
    </el-dialog>

    <el-dialog title="选择学生" :visible.sync="chooseUserDialog" :close-on-click-modal="false" width="1000px">
      <choose-user-permission v-if="chooseUserDialog" :all-org="allOrg" :all-student="allStudent" :current-select-student="selectUsers" @user-change="chooseStudent" />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseUserSure">确 定</el-button>
        <el-button round @click="chooseUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择学生" :visible.sync="selectUserDialog" :close-on-click-modal="false" width="1000px">
      <select-user v-if="selectUserDialog" :all-org="allOrg" @response-fn="handleSelectUserResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="selectUserDialogLoading" round type="primary" @click="handleSelectUserSure">确 定</el-button>
        <el-button round @click="selectUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  resourceCategoryList,
  resourceResourceList,
  resourceCenterEdit,
  resourceCenterAdd,
  resourceCenterDetail,
  unresourceCenterList,
  resourceCenterAllUser,
  resourceCenterUserList,
  knowledgeCenterUserDelete,
  resourcePermission
} from '@/api/resource'
import {
  loadNodes,
  getAllStudents,
  findName,
  classesUsers
} from '@/api/user'
import ChooseUserPermission from '@/components/ChooseUserPermission/choose.vue'
import SelectUser from '@/components/ChooseUserPermission/user.vue'
import permission from '@/directive/permission'
import ChooseClass from '@/components/ChooseClass'
import Pagination from '@/components/Pagination'
export default {
  name: 'ResourceEdit',
  directives: {
    permission
  },
  components: {
    Pagination,
    ChooseUserPermission,
    ChooseClass,
    SelectUser
  },
  data() {
    var resourceValidator = (rule, value, callback) => {
      if (!this.resourceCenterForm.knowledgeResourceId) {
        callback(new Error('请选择资源'))
      } else {
        callback()
      }
    }
    var userValid = (rule, value, callback) => {
      if (this.selectUsers.length === 0) {
        callback(new Error('请选择用户'))
      } else {
        callback()
      }
    }
    return {
      // 获取详情LOADING
      cardLoading: false,
      saveLoading: false,
      previewFileList: [],
      resourceCenterForm: {
        knowledgeResourceId: '',
        knowledgeCenterUsers: []
      },
      resourceInfo: {
        resourceName: '',
        resourceCoverUrl: ''
        // resourceCategory: ''
      },
      // 选择资源dialog

      chooseResourceDialog: false,
      resourceList: [],
      resourceListLoading: false,
      resourceListQuery: {
        Filter: '',
        KnowledgeCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      selectedResource: [],
      resourceCategoryList: [],

      // selectedResource: [],
      // resourceList: [],

      // 班级数据
      allOrg: [],
      // 全部学生
      allStudent: [],
      userLoading: false,
      // 选择用户Dialog
      chooseUserDialog: false,
      selectUsers: [],

      resourceCenterRules: {
        knowledgeResourceId: [{
          required: true,
          validator: resourceValidator,
          trigger: 'blur'
        }]
      },

      isEdit: !!this.$route.query.id,
      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],

      activeTabName: 'knowledgeInfo',
      selectUserDialog: false,
      selectUserDialogLoading: false,
      currentSelectUsers: [],
      selectUsersList: [],
      selectUsersListLoading: false,
      selectUsersListQuery: {
        Filter: '',
        KnowledgeCenterId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      currentDeleteUsers: []
    }
  },
  created() {
    this.loadClassList()
    if (this.$route.query.id) {
      this.getResourceCenterDetail()
      this.getResourceCenterUserList()
    }
    this.getResourceCategoryList()
  },
  methods: {
    resourceCenterUserList(args) {
      return resourceCenterUserList(args)
    },
    handleTabClick(tab, event) {

    },
    handleSelectCourseStudent() {
      this.currentSelectUsers = []
      this.selectUserDialog = true
    },
    handleSelectUserResponse(val) {
      this.currentSelectUsers = val
    },
    handleSelectUserSure() {
      if (this.currentSelectUsers.length) {
        this.selectUserDialogLoading = true
        var form = {
          knowledgeCenterId: this.$route.query.id,
          knowledgeCenterUsers: []
        }
        this.currentSelectUsers.forEach(item => {
          form.knowledgeCenterUsers.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            cloudLearn: item.cloudLearn
          })
        })
        var _this = this
        resourcePermission(form).then(res => {
          setTimeout(function() {
            _this.selectUserDialog = false
            _this.selectUserDialogLoading = false
            _this.$message.success('操作成功，稍后刷新查看结果')
            _this.getResourceCenterUserList()
          }, 1000 * 5)
        }).catch(() => {
          this.selectUserDialogLoading = false
        })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleDeleteUserChange(val) {
      this.currentDeleteUsers = val
    },
    handleCourseUserDelete() {
      if (this.currentDeleteUsers.length) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var form = {
            knowledgeCenterId: this.$route.query.id,
            knowledgeCenterUserIds: []
          }
          this.currentDeleteUsers.forEach(item => {
            form.knowledgeCenterUserIds.push(item.id)
          })
          knowledgeCenterUserDelete(form).then(res => {
            this.$message.success('删除成功')
            this.currentDeleteUsers = []
            this.getResourceCenterUserList()
          }).catch(() => {
            this.$message.error('删除失败')
          })
        }).catch(() => {
          this.$message.info('已取消删除')
        })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleRefreshSelectUserList() {
      this.selectUsersListQuery.page = 1
      this.getResourceCenterUserList()
    },
    // 选择资源
    handleChooseResourceClick() {
      this.chooseResourceDialog = true
      this.getUnResourceList()
      this.$nextTick(() => {
        if (this.selectedResource.length > 0) {
          this.$refs.resourceTable.toggleRowSelection(this.selectedResource[0], true)
        }
      })
    },

    handleChooseResourceChange(val) {
      if (val.length > 1) {
        this.$refs.resourceTable.clearSelection()
        this.$nextTick(() => {
          this.$refs.resourceTable.toggleRowSelection(val.pop())
        })
      } else {
        this.selectedResource = val
      }
    },
    handleChooseResourceSure() {
      if (this.selectedResource.length > 0) {
        this.resourceInfo.resourceName = this.selectedResource[0].name
        this.resourceInfo.resourceCoverUrl = this.selectedResource[0].thumbnailUrl
        this.resourceCenterForm.knowledgeResourceId = this.selectedResource[0].id
        this.chooseResourceDialog = false
      } else {
        this.$message.warning('请选择资源')
      }
    },

    handleSaveEdit() {
      this.$refs.resourceCenterForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true

          if (this.$route.query.id) {
            resourceCenterEdit(this.$route.query.id, this.resourceCenterForm).then(res => {
              this.$message.success('保存成功')
              this.saveLoading = false
            }).catch(() => {
              this.$message.error('保存失败')
              this.saveLoading = false
            })
          } else {
            resourceCenterAdd(this.resourceCenterForm).then(res => {
              this.$message.success('添加成功')
              this.saveLoading = false
              this.$router.go(-1)
            }).catch(() => {
              this.$message.error('添加失败')
              this.saveLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    // 选择学生
    handleSelectStudent() {
      this.chooseUserDialog = true
    },
    // 选择学生组件传值获取选择的学生
    chooseStudent(val) {
      this.selectUsers = val
    },
    // 选择用户确定
    handleChooseUserSure() {
      this.dialogSureLoading = true
      var tmp = []
      this.selectUsers.forEach(item => {
        tmp.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : item.classId,
          className: item.extraProperties ? item.extraProperties.OUName : item.className,
          userName: item.userName,
          name: item.name,
          cloudLearn: item.cloudLearn
        })
      })
      this.resourceCenterForm.knowledgeCenterUsers = tmp
      this.dialogSureLoading = false
      this.chooseUserDialog = false
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    // 从班级导入
    async handleChooseClassSure() {
      this.classLoading = true
      var newArr = []
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        res.items.forEach(item => {
          newArr.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : '',
            name: item.name,
            cloudLearn: true })
        })
      }
      this.selectUsers = this.selectUsers.concat(newArr)
      var setA = new Set()
      this.selectUsers = this.selectUsers.filter(item => {
        const result = setA.has(item.userId)
        setA.add(item.id)
        return !result
      })
      var tmp = []
      this.selectUsers.forEach(item => {
        tmp.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : item.classId,
          className: item.extraProperties ? item.extraProperties.OUName : item.className,
          userName: item.userName,
          name: item.name,
          cloudLearn: true
        })
      })
      this.classLoading = false
      this.resourceCenterForm.knowledgeCenterUsers = tmp
      this.chooseClassDialog = false
    },
    handleRefreshList() {
      this.resourceListQuery.page = 1
      this.getUnResourceList()
    },
    getRowKeys(row) {
      return row.id
    },
    checkFileType(row) {
      if (row.fileType !== '.mp4') {
        return false
      }
      return true
    },
    getUnResourceList() {
      this.resourceListLoading = true
      this.resourceListQuery.SkipCount = (this.resourceListQuery.page - 1) * this.resourceListQuery.MaxResultCount
      unresourceCenterList(this.resourceListQuery).then(res => {
        this.resourceList = res.items
        this.resourceListQuery.totalCount = res.totalCount
        this.resourceListLoading = false
      })
    },

    getResourceCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      resourceCategoryList(data).then(res => {
        this.resourceCategoryList = res.items
      })
    },

    getResourceCenterDetail() {
      this.cardLoading = true
      resourceCenterDetail(this.$route.query.id).then(async res => {
        this.resourceInfo.resourceName = res.knowledgeName
        this.resourceInfo.resourceCoverUrl = res.knowledgeThumbnailUrl
        // this.resourceInfo.resourceCategory = res.resourceCategory
        this.resourceCenterForm.knowledgeResourceId = res.knowledgeResourceId

        this.cardLoading = false
      }).catch(() => {
        this.cardLoading = false
      })
    },
    getResourceCenterUsers() {
      this.userLoading = true
      resourceCenterAllUser(this.$route.query.id).then(res => {
        res.items.forEach(item => {
          this.selectUsers.push({
            id: item.userId,
            userId: item.userId,
            userName: item.userName,
            name: item.name,
            classId: item.classId,
            className: item.className,
            cloudLearn: item.cloudLearn
          })
        })
        this.userLoading = false
      })
    },
    async loadClassList() {
      loadNodes().then(res => {
        this.allOrg = res.items
      })
      // if (this.$route.query.id) {
      //   this.getResourceCenterUsers()
      // }
    },
    getResourceCenterUserList() {
      this.selectUsersListLoading = true
      this.selectUsersListQuery.SkipCount = (this.selectUsersListQuery.page - 1) * this.selectUsersListQuery.MaxResultCount
      resourceCenterUserList(this.selectUsersListQuery).then(res => {
        this.selectUsersList = res.items
        this.selectUsersListQuery.totalCount = res.totalCount
        this.selectUsersListLoading = false
      })
    }
  }
}

</script>

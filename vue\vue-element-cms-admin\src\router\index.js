import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */
// import componentsRouter from './modules/components'
// import vmsadminRouter from './modules/vms'
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
  path: '/redirect',
  component: Layout,
  hidden: true,
  children: [{
    path: '/redirect/:path*',
    component: () => import('@/views/redirect/index')
  }]
},
{
  path: '/auth-redirect',
  component: () => import('@/views/login/redirect'),
  hidden: true
},
{
  path: '/login',
  component: () => import('@/views/login/index'),
  hidden: true
},
{
  path: '/404',
  component: () => import('@/views/error-page/404'),
  hidden: true
},
{
  path: '/401',
  component: () => import('@/views/error-page/401'),
  hidden: true
},
{
  path: '/uploadtest',
  component: () => import('@/views/test'),
  hidden: true
},
{
  path: '/profile',
  component: Layout,
  redirect: '/profile/index',
  hidden: true,
  children: [{
    path: 'index',
    component: () => import('@/views/profile/index'),
    name: 'Profile',
    meta: {
      title: '个人中心',
      icon: 'user',
      noCache: true
    }
  }]
},
// {
//   path: '/',
//   component: Layout,
//   redirect: '/train',
//   hidden: true,
//   children: [{
//     path: 'train/index',
//     component: () => import('@/views/train'),
//     name: 'train',
//     meta: {
//       title: '培训管理',
//       icon: 'train',
//       affix: true
//     }
//   }]
// }
{
  path: '/',
  component: Layout,
  redirect: '/dashboard',
  hidden: true,
  children: [{
    path: 'dashboard',
    component: () => import('@/views/dashboard'),
    name: 'Dashboard',
    meta: {
      title: '首页',
      icon: 'dashboard',
      affix: true
    }
  }]
}
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [{
  path: '/train',
  component: Layout,
  redirect: '/train/index',
  meta: {
    title: '培训管理',
    icon: 'train',
    roles: ['CourseManagement.Trains']
  },
  children: [{
    path: '/train/index',
    component: () => import('@/views/train'),
    name: 'Train',
    meta: {
      title: '培训管理',
      icon: 'train',
      breadcrumb: false,
      roles: ['CourseManagement.Trains']
    }
  },
  {
    path: '/train/train-live-statistics',
    component: () => import('@/views/train/live-statistics'),
    name: 'TrainLiveStatistics',
    meta: {
      title: '直播培训记录',
      activeMenu: '/train/index'
    },
    hidden: true
  },
  {
    path: '/train/train-statistics',
    component: () => import('@/views/train/train-statistics'),
    name: 'TrainStatistics',
    meta: {
      title: '培训学习统计',
      activeMenu: '/train/index',
      roles: ['CourseManagement.Trains']
    },
    hidden: true
  },
  {
    path: '/train/select',
    component: () => import('@/views/train/select/index'),
    redirect: '/train/select/train',
    name: 'TrainNewEdit',
    meta: {
      title: '培训编辑',
      activeMenu: '/train/index',
      roles: ['CourseManagement.Trains.Update']
    },
    hidden: true,
    children: [{
      path: '/train/select/train',
      component: () => import('@/views/train/select/train'),
      name: 'TrainNewEdits',
      meta: {
        title: '培训编辑',
        breadcrumb: false,
        activeMenu: '/train/index',
        roles: ['CourseManagement.Trains.Update']
      }
    },
    {
      path: '/train/select/live-select',
      component: () => import('@/views/train/select/live-select'),
      name: 'TrainLiveSelect',
      meta: {
        title: '直播选择',
        activeMenu: '/train/index'
      },
      hidden: true
    }
    ]

  }

  ]
},
// {
//   path: '/train-pack',
//   component: Layout,
//   redirect: '/train-pack/index',
//   meta: {
//     title: '培训包管理',
//     icon: 'trainPack',
//     roles: ['CourseManagement.TrainPackages']
//   },
//   children: [{
//     path: '/train-pack/index',
//     component: () => import('@/views/train-pack'),
//     name: 'TrainPack',
//     meta: {
//       title: '培训包管理',
//       icon: 'package',
//       breadcrumb: false,
//       roles: ['CourseManagement.TrainPackages']
//     }
//   },
//   {
//     path: '/train-pack/select',
//     component: () => import('@/views/train-pack/select/index'),
//     redirect: '/train-pack/select/pack-edit',
//     name: 'PackEdits',
//     meta: {
//       title: '培训包编辑',
//       activeMenu: '/train-pack/index',
//       roles: ['CourseManagement.TrainPackages.Update']
//     },
//     hidden: true,
//     children: [{
//       path: '/train-pack/select/pack-edit',
//       component: () => import('@/views/train-pack/select/pack-edit'),
//       name: 'PackEdit',
//       meta: {
//         title: '培训包编辑',
//         breadcrumb: false,
//         activeMenu: '/train-pack/index',
//         roles: ['CourseManagement.TrainPackages.Update']
//       }
//     },
//     {
//       path: '/train-pack/select/course-select',
//       component: () => import('@/views/train-pack/select/course-select'),
//       name: 'CourseSelect',
//       meta: {
//         title: '课程选择',
//         activeMenu: '/train-pack/index',
//         roles: ['CourseManagement.Courses']
//       },
//       hidden: true
//     }

//     ]
//   }
//   ]
// },
{
  path: '/resource',
  component: Layout,
  redirect: '/resource/resource-list',
  name: 'Resource',
  meta: {
    title: '知识锦囊管理',
    icon: 'resources',
    roles: ['CourseManagement.KnowledgeResources']
  },
  children: [
    {
      path: '/resource/resource-list',
      component: () => import('@/views/resource/resource-list'),
      name: 'ResourceList',
      meta: {
        title: '资源管理',
        icon: 'resources',
        activeMenu: '/resource/resource-list',
        roles: ['CourseManagement.KnowledgeResources']
      }
    },
    {
      path: '/resource/resource-classify',
      component: () => import('@/views/resource/resource-classify'),
      redirect: '/resource/resource-classify',
      meta: {
        title: '资源分类',
        icon: 'resource',
        roles: ['CourseManagement.KnowledgeCategorys']
      },
      children: [{
        path: '/resource/resource-classify',
        component: () => import('@/views/resource/resource-classify'),
        name: 'ResourceClassify',
        meta: {
          title: '资源分类',
          icon: 'category',
          breadcrumb: false,
          roles: ['CourseManagement.KnowledgeCategorys']
        }
      }]
    },
    {
      path: '/resource-center/index',
      component: () => import('@/views/resource-center/index'),
      redirect: '/resource-center/list',
      name: 'ResourceCenters',
      meta: { title: '资源中心', icon: 'resourceCenter', roles: ['CourseManagement.KnowledgeCenters'] },
      children: [
        {
          path: '/resource-center/list',
          component: () => import('@/views/resource-center/list'),
          name: 'ResourceCenter',
          meta: { title: '资源中心', breadcrumb: false, icon: 'resourceCenter', roles: ['CourseManagement.KnowledgeCenters'] }
        },
        {
          path: '/resource-center/edit',
          component: () => import('@/views/resource-center/edit'),
          name: 'ResourceEdit',
          meta: { title: '资源中心添加/编辑', activeMenu: '/resource-center/list', roles: ['CourseManagement.KnowledgeCenters.Update'] },
          hidden: true
        },
        {
          path: '/resource-center/permission',
          component: () => import('@/views/resource-center/permission'),
          name: 'ResourcePermission',
          meta: {
            title: '资源权限',
            activeMenu: '/resource-center/list',
            roles: ['CourseManagement.KnowledgeCenters.Update']
          },
          hidden: true
        },
        {
          path: '/resource-center/classpermission',
          component: () => import('@/views/resource-center/classpermission'),
          name: 'ClassResourcePermission',
          meta: {
            title: '资源权限',
            activeMenu: '/resource-center/list',
            roles: ['AppUserManagement.Classes']
          },
          hidden: true
        }
      ]
    },
    {
      path: '/resource/resource-comment',
      component: () => import('@/views/resource/resource-comment'),
      redirect: '/resource/resource-comment',
      meta: {
        title: '资源评论',
        icon: 'comment',
        roles: ['CourseManagement.KnowledgeComments']
      },
      children: [{
        path: '/resource/resource-comment/index',
        component: () => import('@/views/resource/resource-comment'),
        name: 'ResourceComment',
        meta: {
          title: '资源评论',
          icon: 'comment',
          roles: ['CourseManagement.KnowledgeComments'],
          breadcrumb: false
        }
      }]
    },
    {
      path: '/resource/resource-author',
      component: () => import('@/views/resource/resource-author'),
      redirect: '/resource/resource-author',
      meta: {
        title: '作者管理',
        icon: 'author',
        roles: ['CourseManagement.KnowledgeResources']
      },
      children: [{
        path: '/resource/resource-author/index',
        component: () => import('@/views/resource/resource-author'),
        name: 'ResourceAuthor',
        meta: {
          title: '作者管理',
          icon: 'author',
          roles: ['CourseManagement.KnowledgeResources'],
          breadcrumb: false
        }
      }]
    }
    // {
    //   path: '/resource/resource-likes',
    //   component: () => import('@/views/resource/resource-likes'),
    //   redirect: '/resource/resource-likes',
    //   meta: {
    //     title: '资源点赞',
    //     icon: 'score',
    //     roles: ['CourseManagement.CourseScores']
    //   },
    //   children: [{
    //     path: '/resource-score/index',
    //     component: () => import('@/views/resource/resource-likes'),
    //     name: 'ResourceLikes',
    //     meta: {
    //       title: '资源点赞',
    //       icon: 'score',
    //       roles: ['CourseManagement.CourseScores'],
    //       breadcrumb: false
    //     }
    //   }]
    // }
  ]
},
{
  path: '/course',
  component: Layout,
  redirect: '/course/course-list',
  name: 'Course',
  meta: {
    title: '课程管理',
    icon: 'course',
    roles: ['CourseManagement.Courses']
  },
  children: [
    {
      path: '/course/course-list',
      component: () => import('@/views/course/course-list'),
      name: 'CourseList',
      meta: {
        title: '课程管理',
        icon: 'course',
        activeMenu: '/course/course-list',
        roles: ['CourseManagement.Courses']
      }
    },
    {
      path: '/course/course-edit',
      component: () => import('@/views/course/course-edit'),
      name: 'CourseEdit',
      meta: {
        title: '课程编辑',
        activeMenu: '/course/course-list',
        roles: ['CourseManagement.Courses.Update']
      },
      hidden: true
    },
    {
      path: '/course/course-view',
      component: () => import('@/views/course/course-view'),
      name: 'CourseView',
      meta: {
        title: '课程查看',
        activeMenu: '/course/course-list',
        roles: ['CourseManagement.Courses']
      },
      hidden: true
    },

    {
      path: '/course/course-classify',
      component: () => import('@/views/course/course-classify'),
      redirect: '/course/course-classify',
      meta: {
        title: '课程分类',
        icon: 'category',
        roles: ['CourseManagement.CourseCategorys']
      },
      children: [{
        path: '/course/course-classify',
        component: () => import('@/views/course/course-classify'),
        name: 'CourseClassify',
        meta: {
          title: '课程分类',
          icon: 'category',
          breadcrumb: false,
          roles: ['CourseManagement.CourseCategorys']
        }
      }]
    },
    {
      path: '/course-center/index',
      component: () => import('@/views/course-center/index'),
      redirect: '/course-center/list',
      name: 'CourseCenters',
      meta: { title: '课程中心', icon: 'courseCenter', roles: ['CourseManagement.CourseCenters'] },
      children: [
        {
          path: '/course-center/list',
          component: () => import('@/views/course-center/list'),
          name: 'CourseCenter',
          meta: { title: '课程中心', breadcrumb: false, icon: 'courseCenter', roles: ['CourseManagement.CourseCenters'] }
        },
        {
          path: '/course-center/edit',
          component: () => import('@/views/course-center/edit'),
          name: 'CenterEdit',
          meta: { title: '课程中心添加/编辑', activeMenu: '/course-center/list', roles: ['CourseManagement.CourseCenters.Update'] },
          hidden: true
        },
        {
          path: '/course-center/record',
          component: () => import('@/views/course-center/record'),
          name: 'CourseCenterRecord',
          meta: { title: '课程学习情况', activeMenu: '/course-center/list', roles: ['CourseManagement.CourseCenters.Update'] },
          hidden: true
        },
        {
          path: '/course-center/permission',
          component: () => import('@/views/course-center/permission'),
          name: 'CoursePermission',
          meta: {
            title: '课程权限',
            activeMenu: '/course-center/list',
            roles: ['CourseManagement.CourseCenters.Update']
          },
          hidden: true
        },
        {
          path: '/course-center/classcoursepermission',
          component: () => import('@/views/course-center/classcoursepermission'),
          name: 'ClassCoursePermission',
          meta: {
            title: '课程权限',
            activeMenu: '/course-center/list',
            roles: ['AppUserManagement.Classes']
          },
          hidden: true
        }
      ]
    },
    {
      path: '/comment/index',
      component: () => import('@/views/comment/index'),
      redirect: '/comment/index',
      meta: {
        title: '课程评论',
        icon: 'comment',
        roles: ['CourseManagement.CourseComments']
      },
      children: [{
        path: '/comment/index',
        component: () => import('@/views/comment/index'),
        name: 'Comment',
        meta: {
          title: '课程评论',
          icon: 'comment',
          roles: ['CourseManagement.CourseComments'],
          breadcrumb: false
        }
      }]
    },
    {
      path: '/score/index',
      component: () => import('@/views/score/index'),
      redirect: '/score/index',
      meta: {
        title: '课程评分',
        icon: 'score',
        roles: ['CourseManagement.CourseScores']
      },
      children: [{
        path: '/score/index',
        component: () => import('@/views/score/index'),
        name: 'Score',
        meta: {
          title: '课程评分',
          icon: 'score',
          roles: ['CourseManagement.CourseScores'],
          breadcrumb: false
        }
      }]
    }
  ]
},

{
  path: '/live/index',
  component: Layout,
  redirect: '/live/list',
  name: 'Live',
  meta: { title: '直播管理', icon: 'live', roles: ['LiveManagement.LiveStreams'] },
  children: [
    {
      path: '/live/list',
      component: () => import('@/views/live/list'),
      name: 'LiveList',
      meta: { title: '直播管理', breadcrumb: false, icon: 'live', roles: ['LiveManagement.LiveStreams'] }
    },
    {
      path: '/live/edit',
      component: () => import('@/views/live/edit'),
      name: 'LiveEdit',
      meta: { title: '直播添加/编辑', activeMenu: '/live/list', breadcrumb: false, icon: 'live', roles: ['LiveManagement.LiveStreams'] },
      hidden: true
    },
    {
      path: '/live/detail',
      component: () => import('@/views/live/detail'),
      name: 'LiveDetail',
      meta: { title: '直播详情', activeMenu: '/live/list', roles: ['LiveManagement.LiveStreams'] },
      hidden: true
    },
    {
      path: '/live/category',
      component: () => import('@/views/live/category'),
      name: 'LiveCategory',
      meta: { title: '直播分类', icon: 'category', roles: ['LiveManagement.LiveStreams'] }
    }
  ]
},
{
  path: '/agents',
  component: Layout,
  redirect: '/agents/index',
  meta: {
    title: 'AI智能体管理',
    icon: 'rotationChart'
  },
  children: [{
    path: '/agents/index',
    component: () => import('@/views/agents/list'),
    name: 'AI智能体管理',
    meta: {
      title: 'AI智能体管理',
      icon: 'rotationChart',
      breadcrumb: false
    }
  }]
},
{
  path: '/report',
  component: Layout,
  redirect: '/report/live',
  name: 'Report',
  meta: { title: '报表管理', icon: 'report' },
  children: [
    {
      path: '/report/live',
      component: () => import('@/views/report/live'),
      name: 'LiveReport',
      meta: { title: '直播报表', icon: 'live', roles: ['LiveManagement.LiveStreams'] }
    },
    {
      path: '/report/course',
      component: () => import('@/views/report/course'),
      name: 'CourseReport',
      meta: { title: '课程报表', icon: 'course', roles: ['CourseManagement.Courses'] }
    }
  ]
},
{
  path: '/period',
  component: Layout,
  redirect: '/period/index',
  meta: {
    title: '学时管理',
    icon: 'hour'
  },
  children: [{
    path: '/period/index',
    component: () => import('@/views/period/index'),
    name: 'Period',
    meta: {
      title: '学时管理',
      icon: 'hour',
      breadcrumb: false
    }
  },
  {
    path: '/period/entry',
    component: () => import('@/views/period/entry'),
    name: 'PeriodEntry',
    meta: {
      title: '学时录入',
      icon: 'entry',
      breadcrumb: false
    }
  },
  {
    path: '/period/edit',
    component: () => import('@/views/period/edit'),
    name: 'PeriodEdit',
    meta: {
      title: '课程录入',
      icon: 'rotationChart',
      breadcrumb: false,
      activeMenu: '/period/entry'
    },
    hidden: true
  }]
},
{
  path: '/exercise',
  component: Layout,
  redirect: '/exercise/index',
  meta: {
    title: '练习管理',
    icon: 'exercise'
  },
  children: [{
    path: '/exercise/index',
    component: () => import('@/views/exercise/index'),
    name: 'exercise',
    meta: {
      title: '练习题库管理',
      icon: 'exercise',
      breadcrumb: false,
      roles: ['Exam.ExerciseBanks']
    }
  },
  {
    path: '/exercise/detail',
    component: () => import('@/views/exercise/detail'),
    name: 'ExerciseDetial',
    meta: {
      title: '练习题库',
      icon: 'achievement',
      activeMenu: '/exercise/index',
      roles: ['Exam.ExerciseBanks']
    },
    hidden: true
  },
  {
    path: '/exercise-achivement/index',
    component: () => import('@/views/exercise-achivement/index'),
    name: 'ExerciseAchivement',
    meta: {
      title: '练习记录',
      icon: 'achievement',
      roles: ['Exam.ExerciseRecords']
    }
    // hidden: true
  }
  // {
  //   path: '/exercise-achivement/detail',
  //   component: () => import('@/views/exercise-achivement/detail'),
  //   name: 'ExerciseAchivementDetail',
  //   meta: {
  //     title: '练习记录',
  //     icon: 'achievement',
  //     activeMenu: '/exercise-achivement/index',
  //     roles: ['Exam.Examinations']
  //   },
  //   hidden: true
  // }
  ]
},
{
  path: '/examination',
  component: Layout,
  redirect: '/examination/index',
  meta: {
    title: '考核管理',
    icon: 'exam',
    roles: ['Exam.Examinations']
  },
  children: [{
    path: '/examination/index',
    component: () => import('@/views/examination'),
    name: 'Examination',
    meta: {
      title: '考核管理',
      icon: 'exam',
      breadcrumb: false,
      roles: ['Exam.Examinations']
    }
  },
  {
    path: '/achievement/index',
    component: () => import('@/views/achievement/index'),
    name: 'Achievement',
    meta: {
      title: '成绩管理',
      icon: 'achievement',
      roles: ['Exam.ExaminationRecords']
    }
    // hidden: true
  },
  {
    path: '/achievement/detail',
    component: () => import('@/views/achievement/detail'),
    name: 'AchievementDetail',
    meta: {
      title: '成绩详情',
      icon: 'achievement',
      activeMenu: '/achievement/index',
      roles: ['Exam.ExaminationRecords']
    },
    hidden: true
  }]
},
// {
//   path: '/achievement',
//   component: Layout,
//   redirect: '/achievement/index',
//   name: 'Achievements',
//   meta: {
//     title: '成绩管理',
//     icon: 'achievement',
//     roles: ['Exam.Examinations']
//   },
//   children: [
//     {
//       path: '/achievement/index',
//       component: () => import('@/views/achievement/index'),
//       name: 'Achievement',
//       meta: {
//         title: '成绩管理',
//         icon: 'achievement',
//         activeMenu: '/achievement',
//         roles: ['Exam.Examinations']
//       },
//       hidden: true
//     },
//     {
//       path: '/achievement/detail',
//       component: () => import('@/views/achievement/detail'),
//       name: 'AchievementDetail',
//       meta: {
//         title: '成绩详情',
//         icon: 'achievement',
//         activeMenu: '/achievement',
//         roles: ['Exam.Examinations']
//       },
//       hidden: true
//     }]
// },
{
  path: '/exam-paper',
  component: Layout,
  redirect: '/exam-paper/index',
  meta: {
    title: '试卷管理',
    icon: 'exam',
    roles: ['Exam.ExamPapers']
  },
  children: [{
    path: '/exam-paper/index',
    component: () => import('@/views/exam-paper'),
    name: 'ExamPaper',
    meta: {
      title: '试卷管理',
      icon: 'exam',
      breadcrumb: false,
      roles: ['Exam.ExamPapers']
    }
  },
  {
    path: '/exam-paper/paper-tags',
    component: () => import('@/views/exam-paper/paper-tags'),
    name: 'ExamPaperTags',
    meta: {
      title: '标签管理',
      icon: 'exam',
      breadcrumb: false,
      roles: ['Exam.ExamPapers']
    }
  },
  {
    path: '/exam-paper/paper-class',
    component: () => import('@/views/exam-paper/paper-class'),
    name: 'ExamPaperClass',
    meta: {
      title: '分类管理',
      icon: 'exam',
      breadcrumb: false,
      roles: ['Exam.ExamPapers']
    }
  },
  {
    path: '/exam-paper/paper-edit',
    component: () => import('@/views/exam-paper/paper-edit'),
    name: 'PaperEdit',
    meta: {
      title: '试卷编辑',
      icon: 'exam',
      activeMenu: '/exam-paper/index',
      roles: ['Exam.ExamPapers.Update']
    },
    hidden: true
  },
  {
    path: '/exam-paper/generating-paper',
    component: () => import('@/views/exam-paper/generating-paper'),
    name: 'GeneratingPaper',
    meta: {
      title: '组卷管理',
      icon: 'exam',
      activeMenu: '/exam-paper/index',
      roles: ['Exam.ExamPapers.Update']
    },
    hidden: true
  }
  ]
},
{
  path: '/question-bank',
  component: Layout,
  redirect: '/question-bank/index',
  meta: {
    title: '题库管理',
    icon: 'questionBank',
    roles: ['Exam.QuestionBanks']
  },
  children: [{
    path: '/question-bank/index',
    component: () => import('@/views/question-bank'),
    name: 'QuestionBank',
    meta: {
      title: '题库管理',
      icon: 'questionBank',
      breadcrumb: false,
      roles: ['Exam.QuestionBanks']
    }
  },
  {
    path: '/question-bank/tag-type',
    component: () => import('@/views/question-bank/tagType'),
    name: 'QuestionBankTagType',
    meta: { title: '题库标签分类', icon: 'questionBank', roles: ['Exam.QuestionBankTags'] }
  },
  {
    path: '/question-bank/tag',
    component: () => import('@/views/question-bank/tag'),
    name: 'QuestionBankTag',
    meta: { title: '题库标签', icon: 'questionBank', roles: ['Exam.QuestionBankTags'] }
  }
  ]
},
{
  path: '/notice/index',
  component: Layout,
  redirect: '/notice/list',
  name: 'Notice',
  meta: { title: '资讯中心管理', icon: 'notice', roles: ['NoticeManagement.Announcements'] },
  children: [
    {
      path: '/notice/list',
      component: () => import('@/views/notice/list'),
      name: 'NoticeList',
      meta: { title: '资讯中心管理', breadcrumb: false, roles: ['NoticeManagement.Announcements'] }
    },
    {
      path: '/notice/notice-edit',
      component: () => import('@/views/notice/notice-edit'),
      name: 'NoticeEdit',
      meta: { title: '资讯中心添加/编辑', activeMenu: '/notice/list', breadcrumb: false, roles: ['NoticeManagement.Announcements'] },
      hidden: true
    }
  ]
},

{
  path: '/technology-centers/index',
  component: Layout,
  redirect: '/technology-centers/list',
  name: 'TechnologyCenters',
  meta: { title: '技术中心管理', icon: 'notice', roles: ['NoticeManagement.TechnologyCenters'] },
  children: [
    {
      path: '/technology-centers/list',
      component: () => import('@/views/technology-centers/list'),
      name: 'TechnologyCentersList',
      meta: { title: '技术中心管理', breadcrumb: false, roles: ['NoticeManagement.TechnologyCenters'] }
    },
    {
      path: '/technology-centers/technology-centers-edit',
      component: () => import('@/views/technology-centers/technology-centers-edit'),
      name: 'TechnologyCentersEdit',
      meta: { title: '技术中心添加/编辑', activeMenu: '/technology-centers/list', breadcrumb: false, roles: ['NoticeManagement.TechnologyCenters'] },
      hidden: true
    }
  ]
},
{
  path: '/link',
  component: Layout,
  redirect: '/link/index',
  name: 'Links',
  meta: { title: '链接管理', icon: 'link' },
  children: [
    {
      path: '/link/index',
      component: () => import('@/views/link/index'),
      name: 'Link',
      meta: { title: '链接管理', breadcrumb: false }
    }
  ]
},
{
  path: '/web-manage/rotation-chart',
  component: Layout,
  redirect: '/web-manage/rotation-chart',
  meta: {
    title: '轮播图管理',
    icon: 'rotationChart',
    roles: ['CourseManagement.RotationCharts']
  },
  children: [{
    path: '/web-manage/rotation-chart',
    component: () => import('@/views/web-manage/rotation-chart'),
    name: 'WebRotationChart',
    meta: {
      title: '轮播图管理',
      icon: 'rotationChart',
      roles: ['CourseManagement.RotationCharts'],
      breadcrumb: false
    }
  }]
},
{
  path: '/recommend/index',
  component: Layout,
  redirect: '/recommend/category',
  name: 'WebRecommendCategorys',
  meta: { title: '推荐课程管理', icon: 'courseRecommend', roles: ['CourseManagement.RecommendCategorys'] },
  children: [
    {
      path: '/recommend/category',
      component: () => import('@/views/recommend/category'),
      name: 'WebRecommendCategory',
      meta: { title: '推荐课程管理', breadcrumb: false, icon: 'courseRecommend', roles: ['CourseManagement.RecommendCategorys'] }
    },
    {
      path: '/recommend/course',
      component: () => import('@/views/recommend/course'),
      name: 'WebRecommendCourse',
      meta: { title: '推荐课程管理', activeMenu: '/recommend/category', roles: ['CourseManagement.RecommendCourses'] },
      hidden: true
    }
  ]
},

{
  path: '/system',
  component: Layout,
  redirect: '/system/user',
  name: 'system',
  meta: {
    title: '系统管理',
    icon: 'system'
  },
  children: [{
    path: '/system/user',
    component: () => import('@/views/system/user'),
    name: 'User',
    meta: {
      title: '用户管理',
      icon: 'user',
      roles: ['AbpIdentity.Users.Create']
    }
  },
  // {
  //   path: '/system/user-audit',
  //   component: () => import('@/views/system/user-audit'),
  //   name: 'UserAudit',
  //   meta: { title: '用户审核', icon: 'user', noCache: true }
  // },
  {
    path: '/system/role',
    component: () => import('@/views/system/role'),
    name: 'Role',
    meta: {
      title: '角色管理',
      icon: 'role',
      roles: ['AbpIdentity.Roles']
    }
  },
  {
    path: '/system/orgs',
    component: () => import('@/views/system/orgs'),
    name: 'Orgs',
    meta: {
      title: '部门管理',
      icon: 'org',
      roles: ['AppUserManagement.OrganizationUnits']
    }
  },
  {
    path: '/system/class',
    component: () => import('@/views/system/class'),
    name: 'Class',
    meta: {
      title: '班级管理',
      icon: 'org',
      roles: ['AppUserManagement.Classes']
    }
  },
  {
    path: '/system/classUser',
    component: () => import('@/views/system/classUser'),
    name: 'ClassUser',
    meta: {
      title: '班级用户管理',
      icon: 'org',
      roles: ['AppUserManagement.Classes']
    }
  },
  {
    path: '/logs/index',
    component: () => import('@/views/logs/index'),
    name: 'Logs',
    meta: { title: '日志数据', icon: 'log', roles: ['AuditLogging.AuditLogs'], noCache: true }
  },
  {
    path: '/system/set',
    component: () => import('@/views/system/set'),
    name: 'Set',
    meta: {
      title: '系统设置',
      icon: 'system',
      roles: ['AppUserManagement.SettingManagement']
    }
  }
  ]
},
{
  path: '*',
  redirect: '/404',
  hidden: true
}
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router

<template>
  <div>
    <export-excel
      :header="['考试名称', '开始时间', '结束时间', '考核时长', '合格分数', '合格人数']"
      :filter-val="['examName', 'startTime', 'endTime', 'examTimeLong', 'passScore', 'passCount',]"
      :field="{ 1: [2], 2: [2] }"
      :paging="false"
      :api-fn="trainsExamList"
    />
    <el-table :data="list" highlight-current-row>
      <el-table-column label="考试名称" prop="examName" min-width="200">
        <template slot-scope="{row}">
          <span v-if="row.examinationId !== null" class="link-type" @click="handleViewExamWithUser(row)">{{ row.examName
          }}</span>
          <span v-else>{{ row.examName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" prop="startTime" width="180">
        <template slot-scope="{row}">
          <span>{{ row.startTime | formatDateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" prop="endTime" width="180">
        <template slot-scope="{row}">
          <span>{{ row.endTime | formatDateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考核时长" prop="examTimeLong" width="150">
        <template slot-scope="{row}">
          <span>{{ row.examTimeLong }} 分钟</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="总分" prop="totalScore" width="100" /> -->
      <!-- <el-table-column label="参与人数" prop="examName" width="100" /> -->
      <el-table-column label="合格分数" prop="passScore" width="100" />
      <el-table-column label="合格人数" prop="passCount" width="100" />
      <el-table-column label="操作" width="240">
        <template slot-scope="{row}">
          <el-button
            v-if="row.examinationId !== null"
            round
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleBatchViewExam(row)"
          >批量阅卷</el-button>
          <el-button round type="warning" size="mini" icon="el-icon-document" @click="handlePublic(row)">
            公布成绩</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-if="examUserDialog"
      class="examUserDialog"
      title="培训考核学生详情"
      :visible.sync="examUserDialog"
      top="5vh"
      width="1200px"
    >
      <train-exam-user :examination-id="examinationId" :train-exam-name="trainExamName" :pass-score="passScore" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="examUserDialog = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 批量阅卷 -->
    <el-dialog title="批量阅卷" :visible.sync="batchViewDialog" width="1000px" top="10vh">

      <batch-view-exam :examination-id="examinationId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="batchViewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsExamList } from '@/api/train'
import { publishScore } from '@/api/examPaper'
import TrainExamUser from './examUser.vue'
import BatchViewExam from './batch'
import ExportExcel from '@/components/ExportExcel/index.vue'
export default {
  name: 'TrainCourse',
  components: {
    TrainExamUser,
    BatchViewExam,
    ExportExcel
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,

      examUserDialog: false,
      trainExamName: '',
      examinationId: '',
      passScore: 0,
      batchViewDialog: false

    }
  },
  created() {
    this.getTrainExamList()
  },
  methods: {
    trainsExamList() {
      return trainsExamList(this.$route.query.id)
    },
    handleViewExamWithUser(row) {
      this.trainExamName = row.examName
      this.examinationId = row.examinationId
      this.passScore = row.passScore
      this.examUserDialog = true
    },
    // 批量阅卷
    handleBatchViewExam(row) {
      this.examinationId = row.examinationId
      this.batchViewDialog = true
    },
    // 公布成绩
    handlePublic(row) {
      this.$confirm('公布成绩后无法取消, 是否确认公布?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        publishScore(row.examinationId).then(res => {
          this.$notify({
            title: '成功',
            message: '公布成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {

      })
    },
    // 获取培训下所有考核
    getTrainExamList() {
      trainsExamList(this.$route.query.id).then(res => {
        this.list = res.items
        if (this.list.length > 0) {
          this.$emit('examResponse', this.list.length)
        }
      })
    }
  }
}
</script>

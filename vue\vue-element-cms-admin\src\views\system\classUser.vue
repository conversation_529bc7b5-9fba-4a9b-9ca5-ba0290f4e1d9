<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="8" :md="8" :lg="6" :xl="4">
        <el-card class="box-card" style="max-height: 800px;overflow: auto;">
          <el-button type="text" style="margin-left: 23px" @click="handleNodeClick()">
            全部
          </el-button>
          <el-tree :data="orgDatas" :props="defaultProps" highlight-current style="margin-top: 5px"
            @node-click="handleNodeClick" />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="16" :md="16" :lg="18" :xl="20">
        <el-card>
          <!--工具栏-->
          <div class="header_flex_box">
            <!-- 搜索 -->
            <el-input v-model="listQuery.Filter" clearable size="small" placeholder="搜索..." class="small_input"
              @keyup.enter.native="handleFilter" />
            <el-button round size="mini" type="success" icon="el-icon-search" @click="handleFilter">搜索</el-button>
            <el-button v-permission="['AppUserManagement.Classes.Update']" :loading="importUserLoading" :disabled="importUserDisabled" round size="mini" type="primary" icon="el-icon-plus" @click="showUploadDialog = true">导入
            </el-button>
            <!-- <el-upload ref="fileUpload" round class="upload-demo" action="" :on-change="handleChange"
              :on-remove="handleRemove" :on-exceed="handleExceed" :show-file-list="false"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
              :auto-upload="false">
              <el-button v-permission="['AppUserManagement.Classes.Update']" :loading="importUserLoading"
                :disabled="importUserDisabled" round size="small" icon="el-icon-top" style="margin-left: 10px"
                type="primary">批量导入</el-button>
            </el-upload> -->
            <el-button v-permission="['AppUserManagement.Classes.Update']" :disabled="importUserDisabled"
              :loading="exportLoading" round size="mini" icon="el-icon-plus" type="primary" style="margin-left: 10px"
              @click="handleUpdateUser">添加</el-button>
            <el-button v-permission="['AppUserManagement.Classes']" :loading="exportLoading" round size="mini"
              icon="el-icon-bottom" type="primary" @click="exportAllUser">导出用户</el-button>

          </div>

          <!--表格渲染-->
          <el-table ref="multipleTable" v-loading="listLoading" :data="list" style="width: 100%" stripe
            @sort-change="sortChange">
            <el-table-column type="selection" width="44px" />
            <el-table-column label="用户名" prop="userName" sortable="custom" width="160px">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleUpdate(row)">{{
                    row.userName
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="姓名" prop="name" sortable="custom">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" prop="extraProperties.OUName" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.extraProperties.OUName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="岗位" prop="extraProperties.Position" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.extraProperties.Position }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="extraProperties.Remarks" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.extraProperties.Remarks }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工号" prop="studentIDNumber" sortable="custom">
              <template slot-scope="scope">
                <span>{{ scope.row.studentIDNumber }}</span>
              </template>
            </el-table-column>

            <el-table-column label="手机号码" prop="phoneNumber" sortable="custom" width="120px">
              <template slot-scope="scope">
                <span>{{ scope.row.phoneNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="角色" prop="role" width="120px">
              <template slot-scope="scope">
                <span>{{ roleTransformation(scope.row.extraProperties.Roles) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="creationTime" sortable="custom" label="创建日期" width="160px">
              <template slot-scope="scope">
                <span>{{ scope.row.creationTime | formatDateTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="{ row }">
                <el-button v-permission="['AppUserManagement.Classes.Delete']" round type="danger" size="mini"
                  :disabled="row.userName === 'admin'" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
            :limit.sync="listQuery.MaxResultCount" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="importNoFoundUserDialog" width="600px" title="下列用户不存在当前系统，请核对后再导入！">
      <el-table :data="importNoFoundUserList" size="small">
        <el-table-column label="用户名">
          <template slot-scope="{row}">
            {{ row }}
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="importNoFoundUserDialog = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="userImportDialog" width="600px" title="添加用户">
      <el-form label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="userName" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="userImportDialog = false">取消</el-button>
        <el-button round type="primary" @click="handleImportUserSure">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="showUploadDialog" width="600px" title="导入用户">
    <div class="header_flex_box">
      <el-upload ref="fileUpload" round class="upload-demo" action="" :on-change="handleChange"
        :on-remove="handleRemove" :on-exceed="handleExceed" :show-file-list="false"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        :auto-upload="false">
        <el-button v-permission="['AppUserManagement.Classes.Update']" :loading="importUserLoading"
          :disabled="importUserDisabled" round size="small" icon="el-icon-top" style="margin-right: 10px" type="primary">
          批量导入</el-button>
      </el-upload>
      <el-button round size="mini" icon="el-icon-bottom" type="primary"><a type="primary"
          href="/班级用户导入模板.xlsx">下载模板</a></el-button>
          </div>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="showUploadDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { deleteClassesUsers, getAllStudents } from '@/api/user'
import { classesData, classesUsers, importClassesUsers } from '@/api/user'

export default {
  name: 'User',
  components: {
    Pagination
  },
  directives: {
    permission
  },
  data() {
    return {
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'children',
        emitPath: false
      },
      limitUpload: 1,
      fileTemp: null,
      importUserLoading: false,
      importUserDisabled: true,
      defaultProps: {
        children: 'children',
        label: 'name'
        // isLeaf: "leaf"
      },
      list: null,

      orgDatas: [],
      roleList: [],
      checkedRole: [],

      listLoading: true,
      listQuery: {
        ClassId: null,
        Filter: '',
        Sorting: 'creationtime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        IsAll: false,
        page: 1,
        totalCount: 0
      },

      treeData: [],
      errorUsers: [],
      successUsers: [],
      submitingUser: [],
      submitedUserCount: 0,
      submitProgress: 0,

      classData: [],
      classValue: [],

      exportLoading: false,
      importNoFoundUserDialog: false,
      importNoFoundUserList: [],

      userName: '',
      userImportDialog: false,

      showUploadDialog: false
    }
  },
  created() {
    this.getList()
    this.getTreeDatas()
  },
  methods: {
    getTreeDatas() {
      classesData().then((response) => {
        this.orgDatas = response.items.reduce((total, item, index, list) => {
          if (item.parentId === null) {
            total.push({
              ...item,
              children: list.filter((f) => f.parentId === item.id)
            })
          } else {
            item.children = list.filter((f) => f.parentId === item.id)
          }
          return total
        }, [])
      })
    },

    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      classesUsers(this.listQuery).then((response) => {
        this.list = response.items
        this.listQuery.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    handleNodeClick(data) {
      if (data !== undefined) {
        if (data.parentId === null) {
          this.importUserDisabled = true
        } else {
          this.importUserDisabled = false
        }

        this.listQuery.ClassId = data.id
      } else {
        this.listQuery.ClassId = null
      }

      this.getList()
    },

    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields()
      }
    },

    handleDelete(row) {
      this.$confirm('是否删除' + row.name + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteClassesUsers({ classId: this.listQuery.ClassId, userIds: [row.id] })
          .then((response) => {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      })
    },

    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.handleFilter()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleFilter()
    },
    handleUpdateUser() {
      this.userName = ''
      this.userImportDialog = true
    },
    handleImportUserSure() {
      if (this.userName.length === 0) {
        this.$message.warning('请输入用户名')
        return
      }
      importClassesUsers({ classId: this.listQuery.ClassId, userNames: [this.userName] }).then(res => {
        if (!res.notFoundUsers.length) {
          this.$message.success('导入成功')
          this.userImportDialog = false
          this.getList()
        } else {
          this.importNoFoundUserList = res.notFoundUsers
          this.importNoFoundUserDialog = true
        }
      }).catch(() => {
        this.$message.error('导入失败')
      })
    },
    handleChange(file, fileList) {
      this.importUserLoading = true
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.submitProgress = 0
          this.importfxx(this.fileTemp)
        } else {
          this.importUserLoading = false
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.importUserLoading = false
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    handleExceed() {
      this.importUserLoading = false
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    handleRemove(file, fileList) {
      this.fileTemp = null
    },
    importfxx(file) {
      const _this = this
      this.file = file
      var reader = new FileReader()
      reader.onload = function (e) {
        var binary = ''
        var wb // 读取完成的数据
        var bytes = new Uint8Array(reader.result)
        var length = bytes.byteLength
        var xlsx = require('xlsx')
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        wb = xlsx.read(binary, {
          type: 'binary'
        })
        _this.da = xlsx.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        /* excel数据合法性验证 前端 【 和数据库数据对比不在此范围内处理 放到后台代码中 】*/
        if (_this.da.length > 0) {
          if (_this.da.length > 2000) {
            _this.importUserLoading = false
            _this.$message.error('Excel数据大于2000条')
            return
          }

          var tmp = []
          _this.da.forEach(item => {
            var _name = item['用户名'] + ''
            tmp.push(_name)
          })
          if (_this.isRepeat(tmp)) {
            _this.importUserLoading = false
            _this.$message.error('Excel存在重复数据, 请检查后重新导入')
            return
          }
          importClassesUsers({ classId: _this.listQuery.ClassId, userNames: tmp }).then(res => {
            if (!res.notFoundUsers.length) {
              _this.$message.success('导入成功')
              _this.importUserLoading = false
              _this.getList()
            } else {
              _this.importUserLoading = false
              _this.importNoFoundUserList = res.notFoundUsers
              _this.importNoFoundUserDialog = true
            }
          }).catch(() => {
            _this.$message.error('导入失败')
            _this.importUserLoading = false
          })
        } else {
          this.$message.error('Excel未读取到数据')
        }
      }
      reader.readAsArrayBuffer(file)
    },
    isRepeat(arr) {
      var hash = {}
      for (var i in arr) {
        if (hash[arr[i]]) {
          return true
        }
        // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
        hash[arr[i]] = true
      }
      return false
    },
    async exportAllUser() {
      this.exportLoading = true
      // var allList = [...this.list]
      var allList = []
      try {
        const res = await classesUsers({ Filter: this.listQuery.Filter, ClassId: this.listQuery.ClassId, IsAll: true })
        // const res = await getFilterUsers(query)
        allList = res.items
        for (let i = 0; i < allList.length; i++) {
          const item = allList[i]
          if (item.surname === '0') {
            item.surname = '男'
          } else if (item.surname === '1') {
            item.surname = '女'
          } else {
            item.surname = ''
          }
          item.roles = item.extraProperties.Roles
          item.ouName = item.extraProperties.OUName
          item.remarks = item.extraProperties.Remarks
          item.position = item.extraProperties.Position
        }
        // if (this.listQuery.ClassId?.length) {
        //   allList = allList.filter(item => { return item.extraProperties.OUId === this.listQuery.ClassId })
        // }
        // if (this.listQuery.Filter?.length) {
        //   var reg = RegExp(this.listQuery.Filter)
        //   allList = allList.filter(item => { return item.userName?.match(reg) || item.name?.match(reg) })
        // }
        this.exportLoading = false
      } catch (e) {
        this.exportLoading = false
      }
      import('@/vendor/Export2Excel').then((excel) => {
        const tHeader = ['用户名', '角色', '姓名', '性别', '身份证号', '手机号码', '部门', '工号', '岗位', '备注']
        const filterVal = ['userName', 'roles', 'name', 'surname', 'indentityCode', 'phoneNumber', 'ouName', 'studentIDNumber', 'position', 'remarks']
        // 成绩列表数据
        // const list = Response.items;
        const data = this.formatJson(filterVal, allList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '导出用户_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          if (j === 'roles') {
            return this.roleTransformation(v[j])
            // if (v[j] === 'student') {
            //   return '学生'
            // }
            // if (v[j] === 'teacher') {
            //   return '老师'
            // }
            // if (v[j] === 'admin') {
            //   return '管理员'
            // }
          } else {
            return v[j]
          }
        })
      )
    },
    roleTransformation(val) {
      if (Object.prototype.toString.call(val) === '[object String]') {
        var rolesList = val.split(',')
        var roleName = ''
        rolesList.forEach(item => {
          if (item === 'student') {
            roleName += '学生' + ','
          } else if (item === 'teacher') {
            roleName += '老师' + ','
          } else if (item === 'admin') {
            roleName += '管理员' + ','
          } else {
            roleName += item + ','
          }
        })
        return this.rtrim(roleName, ',')
      } else {
        return val
      }
    },
    rtrim(val, char, type) {
      if (char) {
        if (type === 'left') {
          return val.replace(new RegExp('^\\' + char + '+', 'g'), '')
        } else if (type === 'right') {
          return val.replace(new RegExp('\\' + char + '+$', 'g'), '')
        }
        return val.replace(new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'), '')
      }
      return val.replace(/^\s+|\s+$/g, '')
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.opts {
  padding: 6px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}

.htitle {
  margin-top: 20px;
  line-height: 32px;
}
</style>

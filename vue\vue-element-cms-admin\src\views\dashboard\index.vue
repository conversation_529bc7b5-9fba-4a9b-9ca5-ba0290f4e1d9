<template>
  <div class="dashboard-container">
    <!-- <component :is="currentRole" /> -->
    <div class="block">
    <!-- <el-carousel height="360px" :autoplay="true" >
      <el-carousel-item v-for="(item,index) in imglist" :key="index">
          <el-image
                style="width: 100%; height: 100%"
                :src="item.url"
                :fit="fit"></el-image>
        </el-carousel-item>
    </el-carousel> -->
    </div>
  </div>
</template>

<script>
// import { mapGetters } from 'vuex'
// import adminDashboard from './admin'
// import editorDashboard from './editor'

export default {
  name: 'Dashboard',
  components: { },
  data() {
    return {
      fit: 'cover',
      imglist: [] // 轮播图列表
      // currentRole: 'adminDashboard'
    }
  },
  computed: {
    // ...mapGetters([
    //   'roles'
    // ])
  },
  created() {
    // this.getCarousel();
  },
  methods: {

  }

}
</script>

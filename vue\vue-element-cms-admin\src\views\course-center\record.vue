<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>{{ pageTitle }}</span>
      </div>
      <div class="header_flex_box">
        <el-radio-group v-model="recordListQuery.HasClassHour" size="small" @change="handleRefreshList">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="true">已获学时</el-radio-button>
          <el-radio-button :label="false">未获学时</el-radio-button>
        </el-radio-group>
        <el-input v-model="recordListQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button :loading="exportLoading" size="small" round type="primary" icon="el-icon-download" @click="handleUserRecordExport">导出
        </el-button>
        <el-button size="small" round type="primary" @click="handleUpdateCourseRecords">更新记录
        </el-button>
      </div>

      <el-table
        v-loading="recordListLoading"
        :data="recordList"
        size="small"
        highlight-current-row
        @sort-change="recordSortChange"
      >
        <el-table-column label="用户名" prop="userName" min-width="120">
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleRecordDetail(row)">{{ row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="userTrueName" min-width="120" />
        <el-table-column label="部门" prop="classId" sortable="classId" min-width="150" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ cacheFindParent(row.classId).join('/') }}
          </template>
        </el-table-column>
        <el-table-column label="资源总时长" prop="resourceDuration" width="140">
          <template slot-scope="{ row }">
            {{ row.resourceDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学习时长" prop="courseLearnDuration" sortable="courseLearnDuration" width="140">
          <template slot-scope="{ row }">
            {{ row.courseLearnDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学习进度" prop="courseLearnProgress" sortable="courseLearnProgress" width="120">
          <template slot-scope="{ row }">
            {{ row.courseLearnProgress.toFixed(2) }} %
          </template>
        </el-table-column>
        <el-table-column label="学习状态" prop="learnPass" sortable="learnPass" width="120">
          <template slot-scope="{ row }">
            <el-tag v-if="row.learnPass" size="small" type="success">已完成</el-tag>
            <el-tag v-else size="small" type="info">未完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="考评状态" prop="examPass" sortable="examPass" width="120">
          <template slot-scope="{ row }">
            <el-tag v-if="row.examPass" size="small" type="success">已通过</el-tag>
            <el-tag v-else-if="!hasExam" size="small" type="info">无</el-tag>
            <el-tag v-else size="small" type="info">未通过</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="学时状态" prop="classHour" sortable="classHour" width="120">
          <template slot-scope="{ row }">
            <el-tag v-if="row.classHour > 0" size="small" type="success">已获学时</el-tag>
            <el-tag v-else size="small" type="info">未获学时</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="课程学时" sortable="classHour" width="120">
          <template slot-scope="{ row }">
            {{ tmpData.classHour }}
          </template>
        </el-table-column>
        <el-table-column label="获得学时" prop="classHour" sortable="classHour" width="120">
          <template slot-scope="{ row }">
            {{ row.classHour }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="视频时长" prop="courseVideoLearnDuration" sortable="courseVideoLearnDuration">
          <template slot-scope="{row}">
            {{row.courseVideoLearnDuration | formatSecond}}
          </template>
        </el-table-column> -->
        <el-table-column label="开始学习时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="最后学习时间" prop="lastLearnTime" sortable="lastLearnTime" width="160">
          <template slot-scope="{ row }">
            {{ row.lastLearnTime | formatDateTime }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" width="100">
          <template slot-scope="{ row }">
            <el-button round type="primary" size="mini" @click="handleUpdateCourseRecord(row)"> 更新
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <pagination
        v-show="recordListQuery.totalCount > 0"
        :total="recordListQuery.totalCount"
        :page.sync="recordListQuery.page"
        :limit.sync="recordListQuery.MaxResultCount"
        @pagination="getRecordList"
      />
    </el-card>
    <el-dialog :title="recordDetailDialogTitle" :visible.sync="recordDetailDialog" :close-on-click-modal="true" width="1000px">
      <el-table
        v-loading="recordDetailListLoading"
        :data="recordDetailList"
        size="small"
        @sort-change="recordDetailSortChange"
      >
        <el-table-column label="资源名称" prop="courseResourceName" sortable="courseResourceName" show-overflow-tooltip />
        <el-table-column label="开始学习时间" prop="creationTime" sortable="creationTime" width="140">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="结束学习时间" prop="lastLearnTime" sortable="lastLearnTime" width="140">
          <template slot-scope="{ row }">
            {{ row.lastLearnTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="资源学习时长" prop="resLearnDuration" sortable="resLearnDuration" width="140">
          <template slot-scope="{ row }">
            {{ row.resLearnDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学习进度" prop="lastLearnProgress" sortable="lastLearnProgress" width="120">
          <template slot-scope="{ row }">
            {{ row.lastLearnProgress }} %
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="recordDetailListQuery.totalCount > 0"
        :total="recordDetailListQuery.totalCount"
        :page.sync="recordDetailListQuery.page"
        :limit.sync="recordDetailListQuery.MaxResultCount"
        @pagination="getRecordDetailList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="recordDetailDialog = false">关  闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { courseRecordList, updateCourseRecords, updateCourseRecord, courseCenterDetail, userDetailRecordList, courseExamInfo } from '@/api/course'
import Pagination from '@/components/Pagination'
import { formatSecond, formatDateTime } from '@/utils'
import { orgsData } from '@/api/user'
export default {
  name: 'CourseCenterRecord',
  components: {
    Pagination
  },
  data() {
    return {
      pageTitle: this.$route.query.name,
      recordList: [],
      recordListLoading: false,
      recordListQuery: {
        CourseId: this.$route.query.courseId,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: '',
        MaxResultCount: 10,
        page: 1,
        HasClassHour: null,
        totalCount: 0,
        IsAll: false
      },
      tmpData: {
        classHour: 0,
        passDuration: 0
      },
      exportLoading: false,

      hasExam: false,

      recordDetailDialogTitle: '',
      recordDetailDialog: false,
      recordDetailList: [],
      recordDetailListQuery: {
        UserId: '',
        CourseId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      recordDetailListLoading: false,

      allOrgs: null,
      cacheDate: new Map()
    }
  },
  created() {
    this.loadClass()
  },
  mounted() {
    this.getRecordList()
    this.getCourseCenterDetail()
    this.getCourseExam()
  },
  methods: {
    handleRecordDetail(row) {
      this.recordDetailDialogTitle = row.userName + '_详情'
      this.recordDetailListQuery.page = 1
      this.recordDetailListQuery.UserId = row.userId
      this.recordDetailListQuery.CourseId = row.courseId
      this.recordDetailListQuery.Sorting = 'creationTime desc'
      this.getRecordDetailList()
      this.recordDetailDialog = true
    },
    handleUserRecordExport() {
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['用户名', '姓名', '一级部门', '二级部门', '三级部门', '资源总时长', '学习时长', '学习进度', '学习状态', '考评状态', '学时状态', '课程学时', '获得学时', '开始学习时间', '最后学习时间']
        const filterVal = [
          'userName',
          'userTrueName',
          'className1',
          'className2',
          'className3',
          'resourceDuration',
          'courseLearnDuration',
          'courseLearnProgress',
          'courseLearnStatus',
          'examPass',
          'classStatus',
          'courseHour',
          'classHour',
          'creationTime',
          'lastLearnTime'

        ]
        // 成绩列表数据
        var list = []
        this.exportLoading = true
        const res = await courseRecordList({ IsAll: true, CourseId: this.$route.query.courseId })
        res.items.forEach((item) => {
          var orgs = this.cacheFindParent(item.classId)
          item.className1 = orgs.length ? orgs[0] : ''
          item.className2 = orgs.length > 1 ? orgs[1] : ''
          item.className3 = orgs.length > 2 ? orgs[2] : ''
          list.push({
            ...item,
            classStatus: item.classHour > 0 ? '已获学时' : '未获学时',
            courseLearnStatus: item.learnPass ? '已完成' : '未完成',
            resourceDuration: formatSecond(item.resourceDuration),
            courseLearnDuration: formatSecond(item.courseLearnDuration),
            courseLearnProgress: item.courseLearnProgress.toFixed(2) + '%',
            creationTime: formatDateTime(item.creationTime),
            lastLearnTime: formatDateTime(item.lastLearnTime),
            courseHour: this.tmpData.classHour,
            examPass: this.hasExam ? (item.examPass ? '已通过' : '未通过') : '无'
          })
        })
        const data = this.formatJson(filterVal, list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename:
            this.$route.query.name + '_详情_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })

        this.exportLoading = false
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    handleRefreshList() {
      this.recordListQuery.page = 1
      this.getRecordList()
    },
    recordSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getRecordList()
        return
      }
      this.recordListQuery.Sorting = prop + ' ' + order
      this.getRecordList()
    },
    recordDetailSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getRecordDetailList()
        return
      }
      this.recordDetailListQuery.Sorting = prop + ' ' + order
      this.getRecordDetailList()
    },
    getRecordList() {
      this.recordListLoading = true
      this.recordListQuery.SkipCount =
        (this.recordListQuery.page - 1) * this.recordListQuery.MaxResultCount
      courseRecordList(this.recordListQuery)
        .then((res) => {
          this.recordList = res.items
          this.recordListQuery.totalCount = res.totalCount
          this.recordListLoading = false
        })
        .catch(() => {
          this.recordListLoading = false
        })
    },
    getCourseCenterDetail() {
      courseCenterDetail(this.$route.query.id).then(res => {
        this.tmpData.passDuration = res.passDuration
        this.tmpData.classHour = res.classHour
      }).catch(() => {
      })
    },
    getRecordDetailList() {
      this.recordDetailListLoading = true
      this.recordDetailListQuery.SkipCount = (this.recordDetailListQuery.page - 1) * this.recordDetailListQuery.MaxResultCount
      userDetailRecordList(this.recordDetailListQuery).then(res => {
        this.recordDetailList = res.items
        this.recordDetailListQuery.totalCount = res.totalCount
        this.recordDetailListLoading = false
      }).catch(() => {
        this.recordDetailListLoading = false
      })
    },
    handleUpdateCourseRecords() {
      var data = {
        courseId: this.recordListQuery.CourseId
      }
      updateCourseRecords(data).then(res => {
        this.$message.success('操作成功，稍后刷新查看结果')
      })
    },
    handleUpdateCourseRecord(row) {
      var data = {
        courseId: this.recordListQuery.CourseId,
        userId: row.userId
      }
      updateCourseRecord(data).then(res => {
        this.$message.success('操作成功，稍后刷新查看结果')
      })
    },

    getCourseExam() {
      var data = {
        Filter: '',
        CourseId: this.$route.query.courseId,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10
      }
      courseExamInfo(data).then((res) => {
        if (res.totalCount > 0) {
          this.hasExam = true
        }
      })
    },

    // 三级部门展示
    async loadClass() {
      await orgsData().then(res => {
        // this.orgDatas = res.items
        this.allOrgs = new Map()
        res.items.forEach(item => {
          this.allOrgs.set(item.id, item)
        })
      })
    },
    cacheFindParent(id) {
      if (this.cacheDate.get(id)) {
        return this.cacheDate.get(id)
      }
      return this.findParent(id)
    },
    findParent(id) {
      const result = []
      if (!this.allOrgs) {
        return []
      }
      const d = this.allOrgs.get(id)
      if (d) {
        result.unshift(d?.displayName)
      }
      if (d?.parentId) {
        let next = this.allOrgs.get(d.parentId)
        while (next) {
          result.unshift(next.displayName)
          next = this.allOrgs.get(next.parentId)
        }
      }
      this.cacheDate.set(id, result)
      return result
    }

  }
}
</script>

<template>
  <div :data="data" :order="order" class="question">
    <div class="question_title">
      <div v-if="type !== 3">
        <span v-if="order" class="q_order">{{ order }}、</span><span class="q_title">{{ item.Title }}</span>
      </div>
      <div v-else class="blank-item">
        <span v-if="order" class="q_order">{{ order }}、</span>
        <span v-for="(str,i) in item.Title.split(/_+/)" :key="i">
          {{ str }}
          <span v-if="i< item.Title.split(/_+/).length-1" class="blank-item-answer"> {{ userAnswer[i] }}</span>
          <!-- <input v-if="i< item.Title.split('__').length-1" v-model="userAnswer[i]" type="text"> -->
        </span>
      </div>

    </div>
    <div class="question_img">
      <el-image
        v-for="img in item.imgs"
        :key="img"
        :src="img"
        fit="cover"
        :preview-src-list="item.imgs"
        @click.stop="handleClickItem"
      />
    </div>
    <div v-if="type === 0 || type === 1 || type === 2" class="question_options">
      <el-radio-group v-if="type === 0" v-model="data.answerModel">
        <el-radio v-for="(option, index) in item.Options" :key="option.Title" :label="index + 1">
          <span>{{ numSwitchChar(index + 1) }} 、</span>{{ option.Title }}
          <div v-if="option.Images" class="option_images">
            <el-image
              v-for="img in option.imgs"
              v-show="img"
              :key="img"
              class="option_image"
              :src="img"
              :preview-src-list="option.imgs"
              @click.stop="handleClickItem"
            />
          </div>
        </el-radio>
      </el-radio-group>
      <el-checkbox-group v-if="type === 1" v-model="data.answerModel">
        <el-checkbox v-for="(option, index) in item.Options" :key="option.Title" :label="index + 1">
          <span>{{ numSwitchChar(index + 1) }} 、</span>{{ option.Title }}
          <div v-if="option.Images" class="option_images">
            <el-image
              v-for="img in option.imgs"
              v-show="img"
              :key="img"
              class="option_image"
              :src="img"
              :preview-src-list="option.imgs"
              @click.stop="handleClickItem"
            />
          </div>
        </el-checkbox>
      </el-checkbox-group>
      <!-- 判断 -->
      <el-radio-group v-if="type === 2" v-model="data.answerModel">
        <el-radio :label="1">正确</el-radio>
        <el-radio :label="0">错误</el-radio>
      </el-radio-group>
    </div>
    <div v-if="type === 0 || type === 1 || type === 2" class="answer_detail">
      <p>
        <i v-if="data.IsRight" class="el-icon-success" style="color: #67c23a">
          答对了</i>
        <i v-if="!data.IsRight" class="el-icon-error" style="color: #f56c6c">
          答错了</i>
      </p>
      <p>学员答案 : {{ userAnswer }}</p>
      <p>
        正确答案 :<span> {{ rightAnswer }}</span>
      </p>
      <p v-show="data.analysis && data.analysis.length > 0">
        答案解析 : {{ data.analysis ? data.analysis : "无" }}
      </p>
    </div>
    <div v-if="type === 3 || type === 6">
      <div v-if="type === 6">
        <p class="user-answer">学生答案：{{ userAnswer }}</p>
        <!-- <el-input v-model="userAnswer" type="textarea" :rows="3" /> -->
      </div>
      <p v-show="data.analysis && data.analysis.length > 0" class="answer_detail">
        答案解析 : {{ data.analysis ? data.analysis : "无" }}
      </p>
      <div class="score-set">
        <span>评分：</span>
        <el-select v-model="scoreModel" size="small" style="margin-top:10px" @change="scoreChange">
          <el-option v-for="score in totalScore*2" :key="score" :label="score*0.5" :value="score*0.5" />
        </el-select>
      </div>

    </div>
  </div>
</template>
<script>

export default {
  name: 'ExamResultPreview',
  props: {
    order: {
      required: false,
      type: Number,
      default: null
    },
    data: {
      required: true,
      type: Object,
      default: null
    }
  },
  data() {
    return {
      rightAnswer: null,
      userAnswer: null,
      item: null,
      type: null,
      scoreModel: 0,
      totalScore: 0,
      tmpVal: 0
    }
  },
  computed: {},
  created() {
    this.changeData()
    this.getUserAnswer()
    this.getRightAnswer()
  },
  methods: {
    changeData() {
      this.item = JSON.parse(this.data.questionStem)

      this.type = this.data.questionType
      if (this.item.Title_Imgs !== null && this.item.Title_Imgs.length > 0) {
        const imgs = this.item.Title_Imgs.split(',')
        this.item.imgs = imgs
      }
      if (this.type === 0 || this.type === 1) {
        this.item.Options.forEach((op, i) => {
          if (op.Images != null && op.Images.length > 0) {
            const imgs = op.Images.split(',')
            op.imgs = imgs
          }
        })
      }
      if (this.type === 3 || this.type === 6) {
        this.scoreModel = this.data.finalScore
        this.tmpVal = this.data.finalScore
      }
    },
    getUserAnswer() {
      if (this.data.questionType === 0) {
        this.userAnswer = this.numSwitchChar(this.data.answerModel)
      } else if (this.data.questionType === 1) {
        this.data.answerModel.forEach((item, index) => {
          if (index === 0) {
            this.userAnswer = this.numSwitchChar(item)
          } else {
            this.userAnswer += ',' + this.numSwitchChar(item)
          }
        })
      } else if (this.data.questionType === 2) {
        if (this.data.answerModel === 1) {
          this.userAnswer = '正确'
        } else if (this.data.answerModel === 0) {
          this.userAnswer = '错误'
        } else if (this.userAnswer === null) {
          this.userAnswer = ''
        }
      } else if (this.data.questionType === 3) {
        this.totalScore = this.data.score
        this.userAnswer = this.data.answerModel
        // this.data.answerModel.forEach((item, index) => {
        //   if (index === 0) {
        //     this.userAnswer = item
        //   } else {
        //     this.userAnswer += ',' + item
        //   }
        // })
      } else if (this.data.questionType === 6) {
        this.totalScore = this.data.score
        this.userAnswer = this.data.answerModel
      }
    },
    getRightAnswer() {
      var answer = this.data.answerModels
      if (this.data.questionType === 0) {
        if (answer.optionAnswerOrders && answer.optionAnswerOrders.length > 0) {
          this.rightAnswer = this.numSwitchChar(answer.optionAnswerOrders[0])
        } else {
          this.rightAnswer = ''
        }
      } else if (this.data.questionType === 1) {
        answer.optionAnswerOrders.forEach((answerItem, index) => {
          if (index === 0) {
            this.rightAnswer = this.numSwitchChar(answerItem)
          } else {
            this.rightAnswer += ',' + this.numSwitchChar(answerItem)
          }
        })
      } else if (this.data.questionType === 2) {
        if (answer.judgeAnswer === 1) {
          this.rightAnswer = '正确'
        } else if (answer.judgeAnswer === 0) {
          this.rightAnswer = '错误'
        }
      }
    },

    scoreChange(val) {
      this.$emit('score-change', this.data.id, val, this.tmpVal, this.data.questionType)
      this.tmpVal = val
    },
    // getRightAnswer() {
    //   this.data.answerModels.optionAnswerOrders.forEach((item, index) => {
    //     if (index === 0) {
    //       this.rightAnswer = this.numSwitchChar(item)
    //     } else {
    //       this.rightAnswer += ',' + this.numSwitchChar(item)
    //     }
    //   })
    // },
    handleClickItem() {
      this.$nextTick(() => {
        // 获取遮罩层dom
        const domImageMask = document.querySelector('.el-image-viewer__mask')
        if (!domImageMask) {
          return
        }
        domImageMask.addEventListener('click', () => {
          // 点击遮罩层时调用关闭按钮的 click 事件
          document.querySelector('.el-image-viewer__close').click()
        })
      })
    },
    numSwitchChar(index) {
      switch (index) {
        case 1:
          return 'A'
        case 2:
          return 'B'
        case 3:
          return 'C'
        case 4:
          return 'D'
        case 5:
          return 'E'
        case 6:
          return 'F'
        case 7:
          return 'G'
        case 8:
          return 'H'
        case 9:
          return 'I'
        case 10:
          return 'J'
        case 11:
          return 'K'
        case 12:
          return 'L'
        case 13:
          return 'M'
        case 14:
          return 'N'
        default:
          break
      }
    }
  }
}

</script>
<style lang="scss" scoped>
  .el-radio,
  .el-checkbox {
    display: block;
  }

  .el-radio-group ::v-deep .el-radio,
  .el-checkbox-group ::v-deep .el-checkbox {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .el-radio ::v-deep .el-radio__inner {
    margin-bottom: -2px;
  }

  .question ::v-deep .el-radio__label {
    text-overflow: ellipsis;
    white-space: normal;
    line-height: 18px;
    vertical-align: top;
    display: inline-block;
  }

  .question ::v-deep .el-checkbox__label {
    text-overflow: ellipsis;
    white-space: normal;
    vertical-align: top;
  }

  .option_images {
    display: block;
  }

  .option_image {
    display: inline-block;
    max-width: 120px;
    margin: 10px 10px 0 0;
    border: 1px solid #eee;
  }

  .answer_detail {
    width: 100%;
    background-color: rgb(242 245 250);
    padding: 15px 10px;
    color: #909399;
  }

  .answer_detail p {
    margin: 0 0 10px 0;
    line-height: 25px;
  }

  .answer_detail span {
    color: #606266;
  }

  .question .question_title {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    padding-bottom: 10px;
  }

  .question .question_title .q_title {
    margin-left: 0px;
  }

  .question {
    margin-bottom: 30px;
  }

  .question_options {
    margin-left: 8px;
  }

  .question_img ::v-deep .el-image {
    max-height: 120px;
    border: 1px solid #eee;
    margin-right: 10px;
    display: inline-block;
  }

  .question_img ::v-deep .el-image .el-image__inner {
    max-height: 120px;
    width: auto;
  }

  .question_img ::v-deep .el-icon-circle-close {
    color: white;
  }

  .option_image ::v-deep .el-icon-circle-close {
    color: white;
  }
  .blank-item{
    .blank-item-answer{
      // line-height: 30px;
      // border: none;
      // border-bottom: 1px solid #ccc;
      // padding: 0 10px;
      // outline: none;
      // min-width: 50px;
      // display: inline-block;
      // vertical-align: bottom;
      text-decoration: underline #ccc;
        text-underline-offset: 5px;
        margin: 0 5px;
    }
    .blank_answer{
        height: 36px;
        line-height: 36px;
        display: inline-block;
        padding: 0 10px;
        border-bottom: 1px solid #aaa;
        margin: 0 10px;
    }
  }
  .user-answer{
    font-size: 14px;
  }
  .score-set{
    text-align: right;
  }
</style>

<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-24 10:05:29
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-12 11:26:00
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/offline/detail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <export-excel
      :header="['用户名','姓名','成绩']"
      :filter-val="['userName','userTrueName','score']"
      :query="{'OfflineExamId': examId}"
      :api-fn="offLineExamRecord"
    />
    <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
      <el-table-column label="用户名" prop="userName" sortable="userName" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span>{{ row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="userTrueName" sortable="userTrueName" show-overflow-tooltip />

      <!-- <el-table-column label="部门" prop="className" sortable="className" show-overflow-tooltip /> -->

      <el-table-column label="成绩" prop="score" sortable="score">
        <template slot-scope="{ row }">
          {{ row.score }}
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getOfflineExamRecord"
    />
  </div>
</template>
<script>
// import { courseRecordList } from '@/api/course'
import { offLineExamRecord } from '@/api/train'

import Pagination from '@/components/Pagination'
export default {
  name: 'TOfflineExam',
  components: {
    Pagination
  },
  props: {
    examId: {
      reuqerd: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: '',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1,
        OfflineExamId: this.examId
      }

    }
  },
  created() {
    this.getOfflineExamRecord()
  },
  methods: {
    offLineExamRecord(args) {
      return offLineExamRecord(args)
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getOfflineExamRecord()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getOfflineExamRecord()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getOfflineExamRecord()
    },
    // 获取课程下所有学生
    getOfflineExamRecord() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      // courseRecordList(this.listQuery).then(res => {
      offLineExamRecord(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>


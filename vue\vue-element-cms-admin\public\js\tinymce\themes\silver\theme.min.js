/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.0 (2019-10-17)
 */
!function(H){"use strict";function Z(){}function i(e,o){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e(o.apply(null,n))}}function l(n){return n}var nn=function(n){return function(){return n}};function d(o){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=r.concat(n);return o.apply(null,e)}}function v(e){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return!e.apply(null,n)}}function r(n){return function(){throw new Error(n)}}var u=nn(!1),a=nn(!0),n=tinymce.util.Tools.resolve("tinymce.ThemeManager"),P=function(){return(P=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)};function c(n,t){var e={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&t.indexOf(o)<0&&(e[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(n);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(e[o[r]]=n[o[r]])}return e}function g(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;var o=Array(n),r=0;for(t=0;t<e;t++)for(var i=arguments[t],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}function t(){return s}var e,s=(e={fold:function(n,t){return n()},is:u,isSome:u,isNone:a,getOr:m,getOrThunk:f,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:nn(null),getOrUndefined:nn(undefined),or:m,orThunk:f,map:t,each:Z,bind:t,exists:u,forall:a,filter:t,equals:o,equals_:o,toArray:function(){return[]},toString:nn("none()")},Object.freeze&&Object.freeze(e),e);function o(n){return n.isNone()}function f(n){return n()}function m(n){return n}function p(t){return function(n){return function(n){if(null===n)return"null";var t=typeof n;return"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t}(n)===t}}function h(n,t){if(fn(n)){for(var e=0,o=n.length;e<o;++e)if(!0!==t(n[e]))return!1;return!0}return!1}function b(n,t){return pn.call(n,t)}function y(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}function x(n,t){for(var e=[],o=0;o<n.length;o+=t){var r=gn.call(n,o,o+t);e.push(r)}return e}function w(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}function S(n,t){for(var e=[],o=0,r=n.length;o<r;o++){var i=n[o];t(i,o)&&e.push(i)}return e}function C(n,t,e){return function(n,t){for(var e=n.length-1;0<=e;e--){t(n[e],e)}}(n,function(n){e=t(e,n)}),e}function k(n,t,e){return bn(n,function(n){e=t(e,n)}),e}function O(n,t){for(var e=0,o=n.length;e<o;e++){var r=n[e];if(t(r,e))return on.some(r)}return on.none()}function T(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return on.some(e)}return on.none()}function z(n){for(var t=[],e=0,o=n.length;e<o;++e){if(!fn(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);hn.apply(t,n[e])}return t}function E(n,t){var e=w(n,t);return z(e)}function B(n,t){for(var e=0,o=n.length;e<o;++e){if(!0!==t(n[e],e))return!1}return!0}function D(n){var t=gn.call(n,0);return t.reverse(),t}function _(n,t){return S(n,function(n){return!vn(t,n)})}function A(n){return[n]}function M(n){return 0===n.length?on.none():on.some(n[n.length-1])}function L(n,e){return kn(n,function(n,t){return{k:t,v:e(n,t)}})}function F(n,t){for(var e=wn(n),o=0,r=e.length;o<r;o++){var i=e[o],u=n[i];if(t(u,i,n))return on.some(u)}return on.none()}function I(n){return On(n,function(n){return n})}function R(n,t){return Tn(n,t)?on.from(n[t]):on.none()}function V(u){return function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)Bn.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}}function N(e){var o,r=!1;return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return r||(r=!0,o=e.apply(null,n)),o}}function j(n){return An.defaultedThunk(nn(n))}function U(t){return function(n){return Tn(n,t)?on.from(n[t]):on.none()}}function W(n,t){return U(t)(n)}function G(n,t){var e={};return e[n]=t,e}function X(n,t){return function(n,e){var o={};return Cn(n,function(n,t){vn(e,t)||(o[t]=n)}),o}(n,t)}function Y(n,t){return function(t,e){return function(n){return Tn(n,t)?n[t]:e}}(n,t)}function q(n,t){return G(n,t)}function K(n){return function(n){var t={};return bn(n,function(n){t[n.key]=n.value}),t}(n)}function J(n,t){var e=function(n){var t=[],e=[];return bn(n,function(n){n.fold(function(n){t.push(n)},function(n){e.push(n)})}),{errors:t,values:e}}(n);return 0<e.errors.length?function(n){return an.error(z(n))}(e.errors):function(n,t){return 0===n.length?an.value(t):an.value(Dn(t,_n.apply(undefined,n)))}(e.values,t)}function $(n,t){return function(n,t){return Tn(n,t)&&n[t]!==undefined&&null!==n[t]}(n,t)}var Q,tn,en=function(e){function n(){return r}function t(n){return n(e)}var o=nn(e),r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:a,isNone:u,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return en(n(e))},each:function(n){n(e)},bind:t,exists:t,forall:t,filter:function(n){return n(e)?r:s},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(u,function(n){return t(e,n)})}};return r},on={some:en,none:t,from:function(n){return null===n||n===undefined?s:en(n)}},rn=function(e){return{is:function(n){return e===n},isValue:a,isError:u,getOr:nn(e),getOrThunk:nn(e),getOrDie:nn(e),or:function(n){return rn(e)},orThunk:function(n){return rn(e)},fold:function(n,t){return t(e)},map:function(n){return rn(n(e))},mapError:function(n){return rn(e)},each:function(n){n(e)},bind:function(n){return n(e)},exists:function(n){return n(e)},forall:function(n){return n(e)},toOption:function(){return on.some(e)}}},un=function(e){return{is:u,isValue:u,isError:a,getOr:l,getOrThunk:function(n){return n()},getOrDie:function(){return r(String(e))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,t){return n(e)},map:function(n){return un(e)},mapError:function(n){return un(n(e))},each:Z,bind:function(n){return un(e)},exists:u,forall:a,toOption:on.none}},an={value:rn,error:un,fromOption:function(n,t){return n.fold(function(){return un(t)},rn)}},cn=p("string"),sn=p("object"),fn=p("array"),ln=p("boolean"),dn=p("function"),mn=p("number"),gn=Array.prototype.slice,pn=Array.prototype.indexOf,hn=Array.prototype.push,vn=function(n,t){return-1<b(n,t)},bn=function(n,t){for(var e=0,o=n.length;e<o;e++){t(n[e],e)}},yn=function(n){return 0===n.length?on.none():on.some(n[0])},xn=dn(Array.from)?Array.from:function(n){return gn.call(n)},wn=Object.keys,Sn=Object.hasOwnProperty,Cn=function(n,t){for(var e=wn(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},kn=function(n,o){var r={};return Cn(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},On=function(n,e){var o=[];return Cn(n,function(n,t){o.push(e(n,t))}),o},Tn=function(n,t){return Sn.call(n,t)},En=function(u){if(!fn(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return bn(u,function(n,o){var t=wn(n);if(1!==t.length)throw new Error("one and only one name per case");var r=t[0],i=n[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!fn(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);for(var e=new Array(n),t=0;t<e.length;t++)e[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(n){var t=wn(n);if(a.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+t.join(","));if(!B(a,function(n){return vn(t,n)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+a.join(", "));return n[r].apply(null,e)},log:function(n){H.console.log(n,{constructors:a,constructor:r,params:e})}}}}),e},Bn=Object.prototype.hasOwnProperty,Dn=V(function(n,t){return sn(n)&&sn(t)?Dn(n,t):t}),_n=V(function(n,t){return t}),An=En([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Mn=An.strict,Fn=An.asOption,In=An.defaultedThunk,Rn=An.mergeWithThunk,Vn=(En([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return U(n)}),Nn=function(n,t){return W(n,t)};(tn=Q=Q||{})[tn.Error=0]="Error",tn[tn.Value=1]="Value";function Hn(n,t,e){return n.stype===Q.Error?t(n.serror):e(n.svalue)}function Pn(n){return{stype:Q.Value,svalue:n}}function zn(n){return{stype:Q.Error,serror:n}}function Ln(n){return i(te,z)(n)}function jn(n){return sn(n)&&100<wn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Un(n,t){return te([{path:n,getErrorInfo:t}])}function Wn(n,t,e){return W(t,e).fold(function(){return function(n,t,e){return Un(n,function(){return'Could not find valid *strict* value for "'+t+'" in '+jn(e)})}(n,e,t)},Zt)}function Gn(n,t,e){var o=W(n,t).fold(function(){return e(n)},l);return Zt(o)}function Xn(u,a,n,c){return n.fold(function(o,e,n,r){function i(n){var t=r.extract(u.concat([o]),c,n);return re(t,function(n){return G(e,c(n))})}function t(n){return n.fold(function(){var n=G(e,c(on.none()));return Zt(n)},function(n){var t=r.extract(u.concat([o]),c,n);return re(t,function(n){return G(e,c(on.some(n)))})})}return n.fold(function(){return ee(Wn(u,a,o),i)},function(n){return ee(Gn(a,o,n),i)},function(){return ee(function(n,t){return Zt(W(n,t))}(a,o),t)},function(n){return ee(function(t,n,e){var o=W(t,n).map(function(n){return!0===n?e(t):n});return Zt(o)}(a,o,n),t)},function(n){var t=n(a),e=re(Gn(a,o,nn({})),function(n){return Dn(t,n)});return ee(e,i)})},function(n,t){var e=t(a);return Zt(G(n,c(e)))})}function Yn(o){return{extract:function(t,n,e){return oe(o(e,n),function(n){return function(n,t){return Un(n,function(){return t})}(t,n)})},toString:function(){return"val"},toDsl:function(){return ce.itemOf(o)}}}function qn(n){var i=le(n),u=C(n,function(t,n){return n.fold(function(n){return Dn(t,q(n,!0))},nn(t))},{});return{extract:function(n,t,e){var o=ln(e)?[]:function(t){var n=wn(t);return S(n,function(n){return $(t,n)})}(e),r=S(o,function(n){return!$(u,n)});return 0===r.length?i.extract(n,t,e):function(n,t){return Un(n,function(){return"There are unsupported fields: ["+t.join(", ")+"] specified"})}(n,r)},toString:i.toString,toDsl:i.toDsl}}function Kn(r){return{extract:function(e,o,n){var t=w(n,function(n,t){return r.extract(e.concat(["["+t+"]"]),o,n)});return ae(t)},toString:function(){return"array("+r.toString()+")"},toDsl:function(){return ce.arrOf(r)}}}function Jn(i,u){return{extract:function(e,o,r){var n=wn(r),t=function(n,t){return Kn(Yn(i)).extract(n,l,t)}(e,n);return ee(t,function(n){var t=w(n,function(n){return fe.field(n,n,Mn(),u)});return le(t).extract(e,o,r)})},toString:function(){return"setOf("+u.toString()+")"},toDsl:function(){return ce.setOf(i,u)}}}function $n(t,e,o,n,r){return Nn(n,r).fold(function(){return function(n,t,e){return Un(n,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+jn(t)})}(t,n,r)},function(n){return n.extract(t.concat(["branch: "+r]),e,o)})}function Qn(n,r){return{extract:function(t,e,o){return Nn(o,n).fold(function(){return function(n,t){return Un(n,function(){return'Choice schema did not contain choice key: "'+t+'"'})}(t,n)},function(n){return $n(t,e,o,r,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+wn(r)},toDsl:function(){return ce.choiceOf(n,r)}}}function Zn(t){return Yn(function(n){return t(n).fold(te,Zt)})}function nt(t,n){return Jn(function(n){return $t(t(n))},n)}function tt(n,t,e){return Qt(function(n,t,e,o){var r=t.extract([n],e,o);return ie(r,function(n){return{input:o,errors:n}})}(n,t,l,e))}function et(n){return n.fold(function(n){throw new Error(be(n))},l)}function ot(n,t,e){return et(tt(n,t,e))}function rt(n,t){return Qn(n,t)}function it(n,t){return Qn(n,L(t,le))}function ut(e,o){return Yn(function(n){var t=typeof n;return e(n)?Zt(n):te("Expected type: "+o+" but got: "+t)})}function at(t){return Zn(function(n){return vn(t,n)?an.value(n):an.error('Unsupported value: "'+n+'", choose one of "'+t.join(", ")+'".')})}function ct(n){return pe(n,n,Mn(),de())}function st(n,t){return pe(n,n,Mn(),t)}function ft(n){return st(n,we)}function lt(n,t){return pe(n,n,Mn(),at(t))}function dt(n){return st(n,Ce)}function mt(n,t){return pe(n,n,Mn(),le(t))}function gt(n,t){return pe(n,n,Mn(),me(t))}function pt(n,t){return pe(n,n,Mn(),Kn(t))}function ht(n){return pe(n,n,Fn(),de())}function vt(n,t){return pe(n,n,Fn(),t)}function bt(n){return vt(n,xe)}function yt(n){return vt(n,we)}function xt(n){return vt(n,Ce)}function wt(n,t){return vt(n,le(t))}function St(n,t){return pe(n,n,j(t),de())}function Ct(n,t,e){return pe(n,n,j(t),e)}function kt(n,t){return Ct(n,t,xe)}function Ot(n,t){return Ct(n,t,we)}function Tt(n,t,e){return Ct(n,t,at(e))}function Et(n,t){return Ct(n,t,Se)}function Bt(n,t){return Ct(n,t,Ce)}function Dt(n,t,e){return Ct(n,t,le(e))}function _t(n,t){return ge(n,t)}function At(n,t,e){return 0!=(n.compareDocumentPosition(t)&e)}function Mt(n,t){var e=function(n,t){for(var e=0;e<n.length;e++){var o=n[e];if(o.test(t))return o}return undefined}(n,t);if(!e)return{major:0,minor:0};function o(n){return Number(t.replace(e,"$"+n))}return Ae(o(1),o(2))}function Ft(n,t){return function(){return t===n}}function It(n,t){return function(){return t===n}}function Rt(n,t){var e=String(t).toLowerCase();return O(n,function(n){return n.search(e)})}function Vt(n,t){return-1!==n.indexOf(t)}function Nt(t){return function(n){return Vt(n,t)}}function Ht(){return qe.get()}function Pt(n,t){var e=n.dom();if(e.nodeType!==Qe)return!1;var o=e;if(o.matches!==undefined)return o.matches(t);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(t);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(t);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function zt(n){return n.nodeType!==Qe&&n.nodeType!==Ze||0===n.childElementCount}function Lt(n,t){var e=t===undefined?H.document:t.dom();return zt(e)?[]:w(e.querySelectorAll(n),Be.fromDom)}function jt(n,t){return n.dom()===t.dom()}function Ut(n,t){return jt(n.element(),t.event().target())}function Wt(n){if(!$(n,"can")&&!$(n,"abort")&&!$(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return ot("Extracting event.handler",qn([St("can",nn(!0)),St("abort",nn(!1)),St("run",Z)]),n)}function Gt(e){var n=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return k(t,function(n,t){return n&&o(t).apply(undefined,e)},!0)}}(e,function(n){return n.can}),t=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return k(t,function(n,t){return n||o(t).apply(undefined,e)},!1)}}(e,function(n){return n.abort});return Wt({can:n,abort:t,run:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];bn(e,function(n){n.run.apply(undefined,t)})}})}function Xt(n,t){Xo(n,n.element(),t,{})}function Yt(n,t,e){Xo(n,n.element(),t,e)}function qt(n){Xt(n,ko())}function Kt(n,t,e){Xo(n,t,e,{})}function Jt(n,t,e,o){n.getSystem().triggerEvent(e,t,o.event())}var $t=function(n){return n.fold(zn,Pn)},Qt=function(n){return Hn(n,an.error,an.value)},Zt=Pn,ne=function(n){var t=[],e=[];return bn(n,function(n){Hn(n,function(n){return e.push(n)},function(n){return t.push(n)})}),{values:t,errors:e}},te=zn,ee=function(n,t){return n.stype===Q.Value?t(n.svalue):n},oe=function(n,t){return n.stype===Q.Error?t(n.serror):n},re=function(n,t){return n.stype===Q.Value?{stype:Q.Value,svalue:t(n.svalue)}:n},ie=function(n,t){return n.stype===Q.Error?{stype:Q.Error,serror:t(n.serror)}:n},ue=function(n,t){var e=ne(n);return 0<e.errors.length?Ln(e.errors):function(n,t){return 0<n.length?Zt(Dn(t,_n.apply(undefined,n))):Zt(t)}(e.values,t)},ae=function(n){var t=ne(n);return 0<t.errors.length?Ln(t.errors):Zt(t.values)},ce=En([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),se=En([{field:["name","presence","type"]},{state:["name"]}]),fe=En([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),le=function(o){return{extract:function(n,t,e){return function(t,e,n,o){var r=w(n,function(n){return Xn(t,e,n,o)});return ue(r,{})}(n,e,o,t)},toString:function(){return"obj{\n"+w(o,function(n){return n.fold(function(n,t,e,o){return n+" -> "+o.toString()},function(n,t){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return ce.objOf(w(o,function(n){return n.fold(function(n,t,e,o){return se.field(n,e,o)},function(n,t){return se.state(n)})}))}}},de=nn(Yn(Zt)),me=i(Kn,le),ge=fe.state,pe=fe.field,he=Yn(Zt),ve=function(o){return{extract:function(n,t,e){return o().extract(n,t,e)},toString:function(){return o().toString()},toDsl:function(){return o().toDsl()}}},be=function(n){return"Errors: \n"+function(n){var t=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return w(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors)+"\n\nInput object: "+jn(n.input)},ye=nn(he),xe=ut(mn,"number"),we=ut(cn,"string"),Se=ut(ln,"boolean"),Ce=ut(dn,"function"),ke=function(t){function n(n,t){for(var e=n.next();!e.done;){if(!t(e.value))return!1;e=n.next()}return!0}if(Object(t)!==t)return!0;switch({}.toString.call(t).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(t).every(function(n){return ke(t[n])});case"Map":return n(t.keys(),ke)&&n(t.values(),ke);case"Set":return n(t.keys(),ke);default:return!1}},Oe=Yn(function(n){return ke(n)?Zt(n):te("Expected value to be acceptable for sending via postMessage")}),Te=function(n){function t(){return e}var e=n;return{get:t,set:function(n){e=n},clone:function(){return Te(t())}}},Ee=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:nn(n)}},Be={fromHtml:function(n,t){var e=(t||H.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw H.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Ee(e.childNodes[0])},fromTag:function(n,t){var e=(t||H.document).createElement(n);return Ee(e)},fromText:function(n,t){var e=(t||H.document).createTextNode(n);return Ee(e)},fromDom:Ee,fromPoint:function(n,t,e){var o=n.dom();return on.from(o.elementFromPoint(t,e)).map(Ee)}},De=function(n,t){return At(n,t,H.Node.DOCUMENT_POSITION_CONTAINED_BY)},_e=function(){return Ae(0,0)},Ae=function(n,t){return{major:n,minor:t}},Me={nu:Ae,detect:function(n,t){var e=String(t).toLowerCase();return 0===n.length?_e():Mt(n,e)},unknown:_e},Fe="Firefox",Ie=function(n){var t=n.current;return{current:t,version:n.version,isEdge:Ft("Edge",t),isChrome:Ft("Chrome",t),isIE:Ft("IE",t),isOpera:Ft("Opera",t),isFirefox:Ft(Fe,t),isSafari:Ft("Safari",t)}},Re={unknown:function(){return Ie({current:undefined,version:Me.unknown()})},nu:Ie,edge:nn("Edge"),chrome:nn("Chrome"),ie:nn("IE"),opera:nn("Opera"),firefox:nn(Fe),safari:nn("Safari")},Ve="Windows",Ne="Android",He="Solaris",Pe="FreeBSD",ze=function(n){var t=n.current;return{current:t,version:n.version,isWindows:It(Ve,t),isiOS:It("iOS",t),isAndroid:It(Ne,t),isOSX:It("OSX",t),isLinux:It("Linux",t),isSolaris:It(He,t),isFreeBSD:It(Pe,t)}},Le={unknown:function(){return ze({current:undefined,version:Me.unknown()})},nu:ze,windows:nn(Ve),ios:nn("iOS"),android:nn(Ne),linux:nn("Linux"),osx:nn("OSX"),solaris:nn(He),freebsd:nn(Pe)},je=function(n,e){return Rt(n,e).map(function(n){var t=Me.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Ue=function(n,e){return Rt(n,e).map(function(n){var t=Me.detect(n.versionRegexes,e);return{current:n.name,version:t}})},We=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Ge=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Vt(n,"edge/")&&Vt(n,"chrome")&&Vt(n,"safari")&&Vt(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,We],search:function(n){return Vt(n,"chrome")&&!Vt(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Vt(n,"msie")||Vt(n,"trident")}},{name:"Opera",versionRegexes:[We,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Nt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Nt("firefox")},{name:"Safari",versionRegexes:[We,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Vt(n,"safari")||Vt(n,"mobile/"))&&Vt(n,"applewebkit")}}],Xe=[{name:"Windows",search:Nt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Vt(n,"iphone")||Vt(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Nt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Nt("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Nt("linux"),versionRegexes:[]},{name:"Solaris",search:Nt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Nt("freebsd"),versionRegexes:[]}],Ye={browsers:nn(Ge),oses:nn(Xe)},qe=Te(function(n,t){var e=Ye.browsers(),o=Ye.oses(),r=je(e,n).fold(Re.unknown,Re.nu),i=Ue(o,n).fold(Le.unknown,Le.nu);return{browser:r,os:i,deviceType:function(n,t,e,o){var r=n.isiOS()&&!0===/ipad/i.test(e),i=n.isiOS()&&!r,u=n.isiOS()||n.isAndroid(),a=u||o("(pointer:coarse)"),c=r||!i&&u&&o("(min-device-width:768px)"),s=i||u&&!c,f=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(e),l=!s&&!c&&!f;return{isiPad:nn(r),isiPhone:nn(i),isTablet:nn(c),isPhone:nn(s),isTouch:nn(a),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:nn(f),isDesktop:nn(l)}}(i,r,n,t)}}(H.navigator.userAgent,function(n){return H.window.matchMedia(n).matches})),Ke=(H.Node.ATTRIBUTE_NODE,H.Node.CDATA_SECTION_NODE,H.Node.COMMENT_NODE,H.Node.DOCUMENT_NODE),Je=(H.Node.DOCUMENT_TYPE_NODE,H.Node.DOCUMENT_FRAGMENT_NODE,H.Node.ELEMENT_NODE),$e=H.Node.TEXT_NODE,Qe=(H.Node.PROCESSING_INSTRUCTION_NODE,H.Node.ENTITY_REFERENCE_NODE,H.Node.ENTITY_NODE,H.Node.NOTATION_NODE,Je),Ze=Ke,no=Ht().browser.isIE()?function(n,t){return De(n.dom(),t.dom())}:function(n,t){var e=n.dom(),o=t.dom();return e!==o&&e.contains(o)},to=nn("touchstart"),eo=nn("touchmove"),oo=nn("touchend"),ro=nn("mousedown"),io=nn("mousemove"),uo=nn("mouseout"),ao=nn("mouseup"),co=nn("mouseover"),so=nn("focusin"),fo=nn("focusout"),lo=nn("keydown"),mo=nn("keyup"),go=nn("input"),po=nn("change"),ho=nn("click"),vo=nn("transitionend"),bo=nn("selectstart"),yo={tap:nn("alloy.tap")},xo=nn("alloy.focus"),wo=nn("alloy.blur.post"),So=nn("alloy.paste.post"),Co=nn("alloy.receive"),ko=nn("alloy.execute"),Oo=nn("alloy.focus.item"),To=yo.tap,Eo=Ht().deviceType.isTouch()?yo.tap:ho,Bo=nn("alloy.longpress"),Do=nn("alloy.sandbox.close"),_o=nn("alloy.typeahead.cancel"),Ao=nn("alloy.system.init"),Mo=nn("alloy.system.touchmove"),Fo=nn("alloy.system.touchend"),Io=nn("alloy.system.scroll"),Ro=nn("alloy.system.resize"),Vo=nn("alloy.system.attached"),No=nn("alloy.system.detached"),Ho=nn("alloy.system.dismissRequested"),Po=nn("alloy.system.repositionRequested"),zo=nn("alloy.focusmanager.shifted"),Lo=nn("alloy.slotcontainer.visibility"),jo=nn("alloy.change.tab"),Uo=nn("alloy.dismiss.tab"),Wo=nn("alloy.highlight"),Go=nn("alloy.dehighlight"),Xo=function(n,t,e,o){var r=P({target:t},o);n.getSystem().triggerEvent(e,t,L(r,nn))};function Yo(n,t,e,o,r){return n(e,o)?on.some(e):dn(r)&&r(e)?on.none():t(e,o,r)}function qo(n){return n.dom().nodeName.toLowerCase()}function Ko(t){return function(n){return function(n){return n.dom().nodeType}(n)===t}}"undefined"!=typeof H.window?H.window:Function("return this;")();function Jo(n){var t=Di(n)?n.dom().parentNode:n.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}function $o(n,t,e){for(var o=n.dom(),r=dn(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=Be.fromDom(o);if(t(i))return on.some(i);if(r(i))break}return on.none()}function Qo(n,t,e){return Yo(function(n,t){return t(n)},$o,n,t,e)}function Zo(n,r){var i=function(n){for(var t=0;t<n.childNodes.length;t++){var e=Be.fromDom(n.childNodes[t]);if(r(e))return on.some(e);var o=i(n.childNodes[t]);if(o.isSome())return o}return on.none()};return i(n.dom())}function nr(n){return K(n)}function tr(n,t){return{key:n,value:Wt({abort:t})}}function er(n){return{key:n,value:Wt({run:function(n,t){t.event().prevent()}})}}function or(n,t){return{key:n,value:Wt({run:t})}}function rr(n,t,e){return{key:n,value:Wt({run:function(n){t.apply(undefined,[n].concat(e))}})}}function ir(n){return function(e){return{key:n,value:Wt({run:function(n,t){Ut(n,t)&&e(n,t)}})}}}function ur(n,t,e){return function(e,o){return or(e,function(n,t){n.getSystem().getByUid(o).each(function(n){Jt(n,n.element(),e,t)})})}(n,t.partUids[e])}function ar(n,r){return or(n,function(t,n){var e=n.event(),o=t.getSystem().getByDom(e.target()).fold(function(){return Mi(e.target(),function(n){return t.getSystem().getByDom(n).toOption()},nn(!1)).getOr(t)},function(n){return n});r(t,o,n)})}function cr(n){return or(n,function(n,t){t.cut()})}function sr(n,t){return ir(n)(t)}function fr(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+e.length+" arguments");var o={};return bn(t,function(n,t){o[n]=nn(e[t])}),o}}function lr(n){return n.slice(0).sort()}function dr(t,n){if(!fn(n))throw new Error("The "+t+" fields must be an array. Was: "+n+".");bn(n,function(n){if(!cn(n))throw new Error("The value "+n+" in the "+t+" fields was not a string.")})}function mr(r,i){var u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return dr("required",r),dr("optional",i),function(n){var e=lr(n);O(e,function(n,t){return t<e.length-1&&n===e[t+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}(u),function(t){var e=wn(t);B(r,function(n){return vn(e,n)})||function(n,t){throw new Error("All required keys ("+lr(n).join(", ")+") were not specified. Specified keys were: "+lr(t).join(", ")+".")}(r,e);var n=S(e,function(n){return!vn(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+lr(n).join(", "))}(n);var o={};return bn(r,function(n){o[n]=nn(t[n])}),bn(i,function(n){o[n]=nn(Object.prototype.hasOwnProperty.call(t,n)?on.some(t[n]):on.none())}),o}}function gr(n){return Be.fromDom(n.dom().ownerDocument)}function pr(n){return Be.fromDom(n.dom().ownerDocument.defaultView)}function hr(n){return on.from(n.dom().parentNode).map(Be.fromDom)}function vr(n){return on.from(n.dom().offsetParent).map(Be.fromDom)}function br(n){return w(n.dom().childNodes,Be.fromDom)}function yr(n,t){var e=n.dom().childNodes;return on.from(e[t]).map(Be.fromDom)}function xr(t,e){hr(t).each(function(n){n.dom().insertBefore(e.dom(),t.dom())})}function wr(n,t){(function(n){return on.from(n.dom().nextSibling).map(Be.fromDom)})(n).fold(function(){hr(n).each(function(n){Ni(n,t)})},function(n){xr(n,t)})}function Sr(t,e){(function(n){return yr(n,0)})(t).fold(function(){Ni(t,e)},function(n){t.dom().insertBefore(e.dom(),n.dom())})}function Cr(t,n){bn(n,function(n){Ni(t,n)})}function kr(n){n.dom().textContent="",bn(br(n),function(n){Hi(n)})}function Or(n){var t=br(n);0<t.length&&function(t,n){bn(n,function(n){xr(t,n)})}(n,t),Hi(n)}function Tr(n){return n.dom().innerHTML}function Er(n,t){var e=gr(n).dom(),o=Be.fromDom(e.createDocumentFragment()),r=function(n,t){var e=(t||H.document).createElement("div");return e.innerHTML=n,br(Be.fromDom(e))}(t,e);Cr(o,r),kr(n),Ni(n,o)}function Br(n,t,e){if(!(cn(e)||ln(e)||mn(e)))throw H.console.error("Invalid call to Attr.set. Key ",t,":: Value ",e,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(t,e+"")}function Dr(n,t,e){Br(n.dom(),t,e)}function _r(n,t){var e=n.dom().getAttribute(t);return null===e?undefined:e}function Ar(n,t){var e=n.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(t)}function Mr(n,t){n.dom().removeAttribute(t)}function Fr(n){return function(n,t){return Be.fromDom(n.dom().cloneNode(t))}(n,!1)}function Ir(n){return function(n){var t=Be.fromTag("div"),e=Be.fromDom(n.dom().cloneNode(!0));return Ni(t,e),Tr(t)}(Fr(n))}function Rr(n){return Ir(n)}function Vr(n){var t=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Li+String(t)}function Nr(n){return Vr(n)}function Hr(t){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+Rr(t().element())+" is not in context.")}}return{debugInfo:nn("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:nn(!1)}}function Pr(n,t){var e=n.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Ji(i)}},n}function zr(n){return q($i,n)}function Lr(o){return function(n,t){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Ji(i.slice(1))}},n}(function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return o.apply(undefined,[n.getApis()].concat([n].concat(t)))},o)}function jr(n,r){var i={};return Cn(n,function(n,o){Cn(n,function(n,t){var e=Y(t,[])(i);i[t]=e.concat([r(o,n)])})}),i}function Ur(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function Wr(n){return n.cHandler}function Gr(n,t){return{name:nn(n),handler:nn(t)}}function Xr(n,t,e){var o=P(P({},e),function(n,t){var e={};return bn(n,function(n){e[n.name()]=n.handlers(t)}),e}(t,n));return jr(o,Gr)}function Yr(n){var i=function(n){return dn(n)?{can:nn(!0),abort:nn(!1),run:n}:n}(n);return function(n,t){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[n,t].concat(e);i.abort.apply(undefined,r)?t.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}}function qr(n,t,e){var o=t[e];return o?function(u,a,n,c){var t=n.slice(0);try{var e=t.sort(function(n,t){var e=n[a](),o=t[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return an.value(e)}catch(o){return an.error([o])}}("Event: "+e,"name",n,o).map(function(n){var t=w(n,function(n){return n.handler()});return Gt(t)}):function(n,t){return an.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(w(t,function(n){return n.name()}),null,2)])}(e,n)}function Kr(n){return tt("custom.definition",le([pe("dom","dom",Mn(),le([ct("tag"),St("styles",{}),St("classes",[]),St("attributes",{}),ht("value"),ht("innerHtml")])),ct("components"),ct("uid"),St("events",{}),St("apis",{}),pe("eventOrder","eventOrder",function(n){return An.mergeWithThunk(nn(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),ye()),ht("domModification")]),n)}function Jr(n,t){var e=_r(n,t);return e===undefined||""===e?[]:e.split(" ")}function $r(n){return n.dom().classList!==undefined}function Qr(n,t){return function(n,t,e){var o=Jr(n,t).concat([e]);return Dr(n,t,o.join(" ")),!0}(n,"class",t)}function Zr(n,t){return function(n,t,e){var o=S(Jr(n,t),function(n){return n!==e});return 0<o.length?Dr(n,t,o.join(" ")):Mr(n,t),!1}(n,"class",t)}function ni(n,t){$r(n)?n.dom().classList.add(t):Qr(n,t)}function ti(n){0===($r(n)?n.dom().classList:function(n){return Jr(n,"class")}(n)).length&&Mr(n,"class")}function ei(n,t){$r(n)?n.dom().classList.remove(t):Zr(n,t),ti(n)}function oi(n,t){return $r(n)&&n.dom().classList.contains(t)}function ri(t,n){bn(n,function(n){ni(t,n)})}function ii(t,n){bn(n,function(n){ei(t,n)})}function ui(n){return n.style!==undefined&&dn(n.style.getPropertyValue)}function ai(n,t,e){if(!cn(e))throw H.console.error("Invalid call to CSS.set. Property ",t,":: Value ",e,":: Element ",n),new Error("CSS value must be a string: "+e);ui(n)&&n.style.setProperty(t,e)}function ci(n,t){ui(n)&&n.style.removeProperty(t)}function si(n,t,e){var o=n.dom();ai(o,t,e)}function fi(n,t){var e=n.dom();Cn(t,function(n,t){ai(e,t,n)})}function li(n,t){var e=n.dom(),o=H.window.getComputedStyle(e).getPropertyValue(t),r=""!==o||Jo(n)?o:eu(e,t);return null===r?undefined:r}function di(n,t){var e=n.dom(),o=eu(e,t);return on.from(o).filter(function(n){return 0<n.length})}function mi(n,t,e){var o=Be.fromTag(n);return si(o,t,e),di(o,t).isSome()}function gi(n,t){var e=n.dom();ci(e,t),Ar(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(_r(n,"style"))&&Mr(n,"style")}function pi(n){return n.dom().offsetWidth}function hi(n){return n.dom().value}function vi(n,t){if(t===undefined)throw new Error("Value.set was undefined");n.dom().value=t}function bi(n){var t=Be.fromTag(n.tag);!function(n,t){var e=n.dom();Cn(t,function(n,t){Br(e,t,n)})}(t,n.attributes),ri(t,n.classes),fi(t,n.styles),n.innerHtml.each(function(n){return Er(t,n)});var e=n.domChildren;return Cr(t,e),n.value.each(function(n){vi(t,n)}),n.uid,Xi(t,n.uid),t}function yi(n,t){return function(t,n){var e=w(n,function(n){return wt(n.name(),[ct("config"),St("state",Qi)])}),o=tt("component.behaviours",le(e),t.behaviours).fold(function(n){throw new Error(be(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n});return{list:n,data:L(o,function(n){var t=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return t}})}}(n,t)}function xi(n){var t=function(n){var t=Y("behaviours",{})(n),e=S(wn(t),function(n){return t[n]!==undefined});return w(e,function(n){return t[n].me})}(n);return yi(n,t)}function wi(n,t,e){var o=function(n){return P(P({},n.dom),{uid:n.uid,domChildren:w(n.components,function(n){return n.element()})})}(n),r=function(n){return n.domModification.fold(function(){return Ur({})},Ur)}(n),i={"alloy.base.modification":r};return function(n,t){return P(P({},n),{attributes:P(P({},n.attributes),t.attributes),styles:P(P({},n.styles),t.styles),classes:n.classes.concat(t.classes)})}(o,0<t.length?function(t,n,e,o){var r=P({},n);bn(e,function(n){r[n.name()]=n.exhibit(t,o)});function i(n){return C(n,function(n,t){return P(P({},t.modification),n)},{})}var u=jr(r,function(n,t){return{name:n,modification:t}}),a=C(u.classes,function(n,t){return t.modification.concat(n)},[]),c=i(u.attributes),s=i(u.styles);return Ur({classes:a,attributes:c,styles:s})}(e,i,t,o):r)}function Si(n,t,e){var o={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,t,e,o){var r=Xr(n,e,o);return tu(r,t)}(e,n.eventOrder,t,o).getOrDie()}function Ci(n){var t=qi(n),e=t.events,o=c(t,["events"]),r=function(n){var t=Y("components",[])(n);return w(t,iu)}(o),i=P(P({},o),{events:P(P({},zi),e),components:r});return an.value(function(e){function n(){return l}var o=Te(Ki),t=et(Kr(e)),r=xi(e),i=function(n){return n.list}(r),u=function(n){return n.data}(r),a=wi(t,i,u),c=bi(a),s=Si(t,i,u),f=Te(t.components),l={getSystem:o.get,config:function(n){var t=u;return(dn(t[n.name()])?t[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(n){return dn(u[n.name()])},spec:nn(e),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return t.apis},connect:function(n){o.set(n)},disconnect:function(){o.set(Hr(n))},element:nn(c),syncComponents:function(){var n=br(c),t=E(n,function(n){return o.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(t)},components:f.get,events:nn(s)};return l}(i))}function ki(n){var t=Be.fromText(n);return ou({element:t})}var Oi,Ti,Ei,Bi=Ko(Je),Di=Ko($e),_i=N(function(){return Ai(Be.fromDom(H.document))}),Ai=function(n){var t=n.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return Be.fromDom(t)},Mi=function(n,t,e){return Qo(n,function(n){return t(n).isSome()},e).bind(t)},Fi=ir(Vo()),Ii=ir(No()),Ri=ir(Ao()),Vi=(Oi=ko(),function(n){return or(Oi,n)}),Ni=(fr("element","offset"),function(n,t){n.dom().appendChild(t.dom())}),Hi=function(n){var t=n.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Pi=nr([(Ti=xo(),Ei=function(n,t){var e=t.event().originator(),o=t.event().target();return!function(n,t,e){return jt(t,n.element())&&!jt(t,e)}(n,e,o)||(H.console.warn(xo()+" did not get interpreted by the desired target. \nOriginator: "+Rr(e)+"\nTarget: "+Rr(o)+"\nCheck the "+xo()+" event handlers"),!1)},{key:Ti,value:Wt({can:Ei})})]),zi=/* */Object.freeze({events:Pi}),Li=0,ji=nn("alloy-id-"),Ui=nn("data-alloy-id"),Wi=ji(),Gi=Ui(),Xi=function(n,t){Object.defineProperty(n.dom(),Gi,{value:t,writable:!0})},Yi=function(n){var t=Bi(n)?n.dom()[Gi]:null;return on.from(t)},qi=l,Ki=Hr(),Ji=function(n){return w(n,function(n){return function(n,t){return function(n,t,e){return""===t||!(n.length<t.length)&&n.substr(e,e+t.length)===t}(n,t,n.length-t.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},$i=Vr("alloy-premade"),Qi={init:function(){return Zi({readState:function(){return"No State required"}})}},Zi=function(n){return n},nu=function(n,t){return function(n,t){return{cHandler:n,purpose:nn(t)}}(d.apply(undefined,[n.handler].concat(t)),n.purpose())},tu=function(n,i){var t=On(n,function(o,r){return(1===o.length?an.value(o[0].handler()):qr(o,i,r)).map(function(n){var t=Yr(n),e=1<o.length?S(i[r],function(t){return y(o,function(n){return n.name()===t})}).join(" > "):o[0].name();return q(r,function(n,t){return{handler:n,purpose:nn(t)}}(t,e))})});return J(t,{})},eu=function(n,t){return ui(n)?n.style.getPropertyValue(t):""},ou=function(n){var t=ot("external.component",qn([ct("element"),ht("uid")]),n),e=Te(Hr());t.uid.each(function(n){Xi(t.element,n)});var o={getSystem:e.get,config:on.none,hasConfigured:nn(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(Hr(function(){return o}))},getApis:function(){return{}},element:nn(t.element),spec:nn(n),readState:nn("No state"),syncComponents:Z,components:nn([]),events:nn({})};return zr(o)},ru=Nr,iu=function(t){return function(n){return Nn(n,$i)}(t).fold(function(){var n=t.hasOwnProperty("uid")?t:P({uid:ru("")},t);return Ci(n).getOrDie()},function(n){return n})},uu=zr;function au(o,r){function n(n){var t=r(n);if(t<=0||null===t){var e=li(n,o);return parseFloat(e)||0}return t}function i(r,n){return k(n,function(n,t){var e=li(r,t),o=e===undefined?0:parseInt(e,10);return isNaN(o)?n:n+o},0)}return{set:function(n,t){if(!mn(t)&&!t.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+t);var e=n.dom();ui(e)&&(e.style[o]=t+"px")},get:n,getOuter:n,aggregate:i,max:function(n,t,e){var o=i(n,e);return o<t?t-o:0}}}function cu(n){return Au.get(n)}function su(n){return Au.getOuter(n)}function fu(n,t){return n!==undefined?n:t!==undefined?t:0}function lu(n){var t=n.dom().ownerDocument,e=t.body,o=t.defaultView,r=t.documentElement;if(e===n.dom())return Fu(e.offsetLeft,e.offsetTop);var i=fu(o.pageYOffset,r.scrollTop),u=fu(o.pageXOffset,r.scrollLeft),a=fu(r.clientTop,e.clientTop),c=fu(r.clientLeft,e.clientLeft);return Iu(n).translate(u-c,i-a)}function du(n){return Ru.get(n)}function mu(n){return Ru.getOuter(n)}function gu(n){var t=n!==undefined?n.dom():H.document,e=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return Fu(e,o)}function pu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function hu(n){var t=n===undefined?H.window:n,e=t.visualViewport;if(e!==undefined)return pu(e.pageLeft,e.pageTop,e.width,e.height);var o=Be.fromDom(t.document),r=t.document.documentElement,i=gu(o),u=r.clientWidth,a=r.clientHeight;return pu(i.left(),i.top(),u,a)}function vu(o){var n=Be.fromDom(H.document),r=gu(n);return function(n,t){var e=t.owner(n),o=Vu(t,e);return on.some(o)}(o,Nu).fold(d(lu,o),function(n){var t=Iu(o),e=C(n,function(n,t){var e=Iu(t);return{left:n.left+e.left(),top:n.top+e.top()}},{left:0,top:0});return Fu(e.left+t.left()+r.left(),e.top+t.top()+r.top())})}function bu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function yu(n){var t=lu(n),e=mu(n),o=su(n);return bu(t.left(),t.top(),e,o)}function xu(n){var t=vu(n),e=mu(n),o=su(n);return bu(t.left(),t.top(),e,o)}function wu(){return hu(H.window)}function Su(n,t,e){return $o(n,function(n){return Pt(n,t)},e)}function Cu(n,t){return function(n,t){var e=t===undefined?H.document:t.dom();return zt(e)?on.none():on.from(e.querySelector(n)).map(Be.fromDom)}(t,n)}function ku(n,t,e){return Yo(Pt,Su,n,t,e)}function Ou(){var t=Vr("aria-owns");return{id:nn(t),link:function(n){Dr(n,"aria-owns",t)},unlink:function(n){Mr(n,"aria-owns")}}}function Tu(t,n){return function(n){return Qo(n,function(n){if(!Bi(n))return!1;var t=_r(n,"id");return t!==undefined&&-1<t.indexOf("aria-owns")}).bind(function(n){var t=_r(n,"id"),e=gr(n);return Cu(e,'[aria-owns="'+t+'"]')})}(n).exists(function(n){return zu(t,n)})}function Eu(n){for(var t=[],e=function(n){t.push(n)},o=0;o<n.length;o++)n[o].each(e);return t}function Bu(n,t){for(var e=0;e<n.length;e++){var o=t(n[e],e);if(o.isSome())return o}return on.none()}var Du,_u,Au=au("height",function(n){var t=n.dom();return Jo(n)?t.getBoundingClientRect().height:t.offsetHeight}),Mu=function(e,o){return{left:nn(e),top:nn(o),translate:function(n,t){return Mu(e+n,o+t)}}},Fu=Mu,Iu=function(n){var t=n.dom(),e=t.ownerDocument.body;return e===t?Fu(e.offsetLeft,e.offsetTop):Jo(n)?function(n){var t=n.getBoundingClientRect();return Fu(t.left,t.top)}(t):Fu(0,0)},Ru=au("width",function(n){return n.dom().offsetWidth}),Vu=(Ht().browser.isSafari(),function(o,n){return o.view(n).fold(nn([]),function(n){var t=o.owner(n),e=Vu(o,t);return[n].concat(e)})}),Nu=/* */Object.freeze({view:function(n){return(n.dom()===H.document?on.none():on.from(n.dom().defaultView.frameElement)).map(Be.fromDom)},owner:function(n){return gr(n)}}),Hu=fr("point","width","height"),Pu=fr("x","y","width","height"),zu=function(t,n){return function(n,t,e){return Qo(n,t,e).isSome()}(n,function(n){return jt(n,t.element())},nn(!1))||Tu(t,n)},Lu="unknown";(_u=Du=Du||{})[_u.STOP=0]="STOP",_u[_u.NORMAL=1]="NORMAL",_u[_u.LOGGING=2]="LOGGING";function ju(t,n,e){switch(Nn(Ha.get(),t).orThunk(function(){var n=wn(Ha.get());return Bu(n,function(n){return-1<t.indexOf(n)?on.some(Ha.get()[n]):on.none()})}).getOr(Du.NORMAL)){case Du.NORMAL:return e(za());case Du.LOGGING:var o=function(t,e){var o=[],r=(new Date).getTime();return{logEventCut:function(n,t,e){o.push({outcome:"cut",target:t,purpose:e})},logEventStopped:function(n,t,e){o.push({outcome:"stopped",target:t,purpose:e})},logNoParent:function(n,t,e){o.push({outcome:"no-parent",target:t,purpose:e})},logEventNoHandlers:function(n,t){o.push({outcome:"no-handlers-left",target:t})},logEventResponse:function(n,t,e){o.push({outcome:"response",purpose:e,target:t})},write:function(){var n=(new Date).getTime();vn(["mousemove","mouseover","mouseout",Ao()],t)||H.console.log(t,{event:t,time:n-r,target:e.dom(),sequence:w(o,function(n){return vn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Rr(n.target)+")":n.outcome})})}}}(t,n),r=e(o);return o.write(),r;case Du.STOP:return!0}}function Uu(n,t,e){return ju(n,t,e)}function Wu(){return mt("markers",[ct("backgroundMenu")].concat(La()).concat(ja()))}function Gu(n){return mt("markers",w(n,ct))}function Xu(n,t,e){return function(){var n=new Error;if(n.stack===undefined)return;var t=n.stack.split("\n");O(t,function(t){return 0<t.indexOf("alloy")&&!y(Pa,function(n){return-1<t.indexOf(n)})}).getOr(Lu)}(),pe(t,t,e,Zn(function(e){return an.value(function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.apply(undefined,n)})}))}function Yu(n){return Xu(0,n,j(Z))}function qu(n){return Xu(0,n,j(on.none))}function Ku(n){return Xu(0,n,Mn())}function Ju(n){return Xu(0,n,Mn())}function $u(n,t){return _t(n,nn(t))}function Qu(n){return _t(n,l)}function Zu(n){return n.x()}function na(n,t){return n.x()+n.width()/2-t.width()/2}function ta(n,t){return n.x()+n.width()-t.width()}function ea(n,t){return n.y()-t.height()}function oa(n){return n.y()+n.height()}function ra(n,t){return n.y()+n.height()/2-t.height()/2}function ia(n,t,e){return Xa(Zu(n),oa(n),e.southeast(),qa(),"layout-se")}function ua(n,t,e){return Xa(ta(n,t),oa(n),e.southwest(),Ka(),"layout-sw")}function aa(n,t,e){return Xa(Zu(n),ea(n,t),e.northeast(),Ja(),"layout-ne")}function ca(n,t,e){return Xa(ta(n,t),ea(n,t),e.northwest(),$a(),"layout-nw")}function sa(n,t,e){return Xa(function(n){return n.x()+n.width()}(n),ra(n,t),e.east(),nc(),"layout-e")}function fa(n,t,e){return Xa(function(n,t){return n.x()-t.width()}(n,t),ra(n,t),e.west(),tc(),"layout-w")}function la(){return[ia,ua,aa,ca,oc,ec]}function da(){return[ua,ia,ca,aa,oc,ec]}function ma(e,o,r){return Ri(function(n,t){r(n,e,o)})}function ga(n,t,e,o,r,i){var u=qn(n),a=wt(t,[function(n,t){return vt(n,qn(t))}("config",n)]);return rc(u,a,t,e,o,r,i)}function pa(r,i,u){return function(n,t,e){var o=e.toString(),r=o.indexOf(")")+1,i=o.indexOf("("),u=o.substring(i+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Ji(u.slice(0,1).concat(u.slice(3)))}},n}(function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];var o=[e].concat(n);return e.config({name:nn(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var t=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,n.config,n.state].concat(t))})},u,i)}function ha(n){return{key:n,value:undefined}}function va(n){return K(n)}function ba(n){var t=ot("Creating behaviour: "+n.name,ic,n);return ga(t.fields,t.name,t.active,t.apis,t.extra,t.state)}function ya(n){var t=ot("Creating behaviour: "+n.name,uc,n);return function(n,t,e,o,r,i){var u=n,a=wt(t,[vt("config",n)]);return rc(u,a,t,e,o,r,i)}(it(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)}function xa(n){n.dom().focus()}function wa(n){var t=n!==undefined?n.dom():H.document;return on.from(t.activeElement).map(Be.fromDom)}function Sa(t){return wa(gr(t)).filter(function(n){return t.dom().contains(n.dom())})}function Ca(n,e){var o=gr(e),t=wa(o).bind(function(t){function n(n){return jt(t,n)}return n(e)?on.some(e):Zo(e,n)}),r=n(e);return t.each(function(t){wa(o).filter(function(n){return jt(n,t)}).fold(function(){xa(t)},Z)}),r}function ka(n,t,e){function r(n){return Nn(e,n).getOr([])}function o(n,t,e){var o=_(dc,e);return{offset:function(){return Fu(n,t)},classesOn:function(){return E(e,r)},classesOff:function(){return E(o,r)}}}return{southeast:function(){return o(-n,t,["top","alignLeft"])},southwest:function(){return o(n,t,["top","alignRight"])},south:function(){return o(-n/2,t,["top","alignCentre"])},northeast:function(){return o(-n,-t,["bottom","alignLeft"])},northwest:function(){return o(n,-t,["bottom","alignRight"])},north:function(){return o(-n/2,-t,["bottom","alignCentre"])},east:function(){return o(n,-t/2,["valignCentre","left"])},west:function(){return o(-n,-t/2,["valignCentre","right"])},innerNorthwest:function(){return o(-n,t,["top","alignRight"])},innerNortheast:function(){return o(n,t,["top","alignLeft"])},innerNorth:function(){return o(-n/2,t,["top","alignCentre"])},innerSouthwest:function(){return o(-n,-t,["bottom","alignRight"])},innerSoutheast:function(){return o(n,-t,["bottom","alignLeft"])},innerSouth:function(){return o(-n/2,-t,["bottom","alignCentre"])},innerWest:function(){return o(n,-t/2,["valignCentre","right"])},innerEast:function(){return o(-n,-t/2,["valignCentre","left"])}}}function Oa(){return ka(0,0,{})}function Ta(n,t,e,o,r,i){var u=t.x()-e,a=t.y()-o,c=r-(u+t.width()),s=i-(a+t.height()),f=on.some(u),l=on.some(a),d=on.some(c),m=on.some(s),g=on.none();return function(n,t,e,o,r,i,u,a,c){return n.fold(t,e,o,r,i,u,a,c)}(t.direction(),function(){return gc(n,f,l,g,g)},function(){return gc(n,g,l,d,g)},function(){return gc(n,f,g,g,m)},function(){return gc(n,g,g,d,m)},function(){return gc(n,f,l,g,g)},function(){return gc(n,f,g,g,m)},function(){return gc(n,f,l,g,g)},function(){return gc(n,g,l,d,g)})}function Ea(n,t){var e=d(vu,t),o=n.fold(e,e,function(){var n=gu();return vu(t).translate(-n.left(),-n.top())}),r=mu(t),i=su(t);return bu(o.left(),o.top(),r,i)}function Ba(n){return n}function Da(t,e){return function(n){return"rtl"===yc(n)?e:t}}function _a(){return wt("layouts",[ct("onLtr"),ct("onRtl")])}function Aa(t,n,e,o){var r=n.layouts.map(function(n){return n.onLtr(t)}).getOr(e),i=n.layouts.map(function(n){return n.onRtl(t)}).getOr(o);return Da(r,i)(t)}function Ma(n,t,e){var o=n.document.createRange();return function(e,n){n.fold(function(n){e.setStartBefore(n.dom())},function(n,t){e.setStart(n.dom(),t)},function(n){e.setStartAfter(n.dom())})}(o,t),function(e,n){n.fold(function(n){e.setEndBefore(n.dom())},function(n,t){e.setEnd(n.dom(),t)},function(n){e.setEndAfter(n.dom())})}(o,e),o}function Fa(n,t,e,o,r){var i=n.document.createRange();return i.setStart(t.dom(),e),i.setEnd(o.dom(),r),i}function Ia(n){return{left:nn(n.left),top:nn(n.top),right:nn(n.right),bottom:nn(n.bottom),width:nn(n.width),height:nn(n.height)}}function Ra(n,t,e){return t(Be.fromDom(e.startContainer),e.startOffset,Be.fromDom(e.endContainer),e.endOffset)}function Va(n,t){return function(n,t){var e=t.ltr();return e.collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Ec.rtl(Be.fromDom(n.endContainer),n.endOffset,Be.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Ra(0,Ec.ltr,e)}):Ra(0,Ec.ltr,e)}(0,function(r,n){return n.match({domRange:function(n){return{ltr:nn(n),rtl:on.none}},relative:function(n,t){return{ltr:N(function(){return Ma(r,n,t)}),rtl:N(function(){return on.some(Ma(r,t,n))})}},exact:function(n,t,e,o){return{ltr:N(function(){return Fa(r,n,t,e,o)}),rtl:N(function(){return on.some(Fa(r,e,o,n,t))})}}})}(n,t))}function Na(n,t,e){return t>=n.left&&t<=n.right&&e>=n.top&&e<=n.bottom}var Ha=Te({}),Pa=["alloy/data/Fields","alloy/debugging/Debugging"],za=nn({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),La=nn([ct("menu"),ct("selectedMenu")]),ja=nn([ct("item"),ct("selectedItem")]),Ua=(nn(le(ja().concat(La()))),nn(le(ja()))),Wa=mt("initSize",[ct("numColumns"),ct("numRows")]),Ga=nn(Wa),Xa=fr("x","y","bubble","direction","label"),Ya=En([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),qa=Ya.southeast,Ka=Ya.southwest,Ja=Ya.northeast,$a=Ya.northwest,Qa=Ya.south,Za=Ya.north,nc=Ya.east,tc=Ya.west,ec=function(n,t,e){return Xa(na(n,t),ea(n,t),e.north(),Za(),"layout-n")},oc=function(n,t,e){return Xa(na(n,t),oa(n),e.south(),Qa(),"layout-s")},rc=function(e,n,o,r,t,i,u){function a(n){return $(n,o)?n[o]():on.none()}var c=L(t,function(n,t){return pa(o,n,t)}),s=L(i,function(n,t){return Pr(n,t)}),f=P(P(P({},s),c),{revoke:d(ha,o),config:function(n){var t=ot(o+"-config",e,n);return{key:o,value:{config:t,me:f,configAsRaw:N(function(){return ot(o+"-config",e,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,e){return a(n).bind(function(t){return Nn(r,"exhibit").map(function(n){return n(e,t.config,t.state)})}).getOr(Ur({}))},name:function(){return o},handlers:function(n){return a(n).map(function(n){return Y("events",function(n,t){return{}})(r)(n.config,n.state)}).getOr({})}});return f},ic=qn([ct("fields"),ct("name"),St("active",{}),St("apis",{}),St("state",Qi),St("extra",{})]),uc=qn([ct("branchKey"),ct("branches"),ct("name"),St("active",{}),St("apis",{}),St("state",Qi),St("extra",{})]),ac=nn(undefined),cc=/* */Object.freeze({events:function(t){return nr([or(Co(),function(r,i){var u=t.channels,n=function(n,t){return t.universal()?n:S(n,function(n){return vn(t.channels(),n)})}(wn(u),i);bn(n,function(n){var t=u[n],e=t.schema,o=ot("channel["+n+"] data\nReceiver: "+Rr(r.element()),e,i.data());t.onReceive(r,o)})})])}}),sc=[st("channels",nt(an.value,qn([Ku("onReceive"),St("schema",ye())])))],fc=ba({fields:sc,name:"receiving",active:cc}),lc=/* */Object.freeze({exhibit:function(n,t){return Ur({classes:[],styles:t.useFixed()?{}:{position:"relative"}})}}),dc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],mc=mr(["x","y","width","height","maxHeight","maxWidth","direction","classes","label","candidateYforTest"],[]),gc=fr("position","left","top","right","bottom"),pc=En([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),hc=function(n,t,e){var o=Fu(t,e);return n.fold(nn(o),nn(o),function(){var n=gu();return o.translate(-n.left(),-n.top())})},vc=pc.relative,bc=pc.fixed,yc=function(n){return"rtl"===li(n,"direction")?"rtl":"ltr"},xc=[ct("hotspot"),ht("bubble"),St("overrides",{}),_a(),$u("placement",function(n,t,e){var o=t.hotspot,r=Ea(e,o.element()),i=Aa(n.element(),t,la(),da());return on.some(Ba({anchorBox:r,bubble:t.bubble.getOr(Oa()),overrides:t.overrides,layouts:i,placer:on.none()}))})],wc=[ct("x"),ct("y"),St("height",0),St("width",0),St("bubble",Oa()),St("overrides",{}),_a(),$u("placement",function(n,t,e){var o=hc(e,t.x,t.y),r=bu(o.left(),o.top(),t.width,t.height),i=Aa(n.element(),t,[ia,ua,aa,ca,oc,ec,sa,fa],[ua,ia,ca,aa,oc,ec,sa,fa]);return on.some(Ba({anchorBox:r,bubble:t.bubble,overrides:t.overrides,layouts:i,placer:on.none()}))})],Sc={create:fr("start","soffset","finish","foffset")},Cc=En([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),kc=(Cc.before,Cc.on,Cc.after,function(n){return n.fold(l,l,l)}),Oc=En([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Tc={domRange:Oc.domRange,relative:Oc.relative,exact:Oc.exact,exactFromRange:function(n){return Oc.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var t=function(n){return n.match({domRange:function(n){return Be.fromDom(n.startContainer)},relative:function(n,t){return kc(n)},exact:function(n,t,e,o){return n}})}(n);return pr(t)},range:Sc.create},Ec=En([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function Bc(n){return of.get(n)}function Dc(n){return of.getOption(n)}function _c(e,o,n,t,r){function i(n){var t=e.dom().createRange();return t.setStart(o.dom(),n),t.collapse(!0),t}var u=Bc(o).length,a=function(n,t,e,o,r){if(0===r)return 0;if(t===o)return r-1;for(var i=o,u=1;u<r;u++){var a=n(u),c=Math.abs(t-a.left);if(e<=a.bottom){if(e<a.top||i<c)return u-1;i=c}}return 0}(function(n){return i(n).getBoundingClientRect()},n,t,r.right,u);return i(a)}function Ac(n){return function(n){return Dc(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||vn(uf,qo(n))}function Mc(n){return Zo(n,Ac)}function Fc(n){return af(n,Ac)}function Ic(n,t){return t-n.left<n.right-t}function Rc(n,t,e){var o=n.dom().createRange();return o.selectNode(t.dom()),o.collapse(e),o}function Vc(t,n,e){var o=t.dom().createRange();o.selectNode(n.dom());var r=o.getBoundingClientRect(),i=Ic(r,e);return(!0===i?Mc:Fc)(n).map(function(n){return Rc(t,n,i)})}function Nc(n,t,e){var o=t.dom().getBoundingClientRect(),r=Ic(o,e);return on.some(Rc(n,t,r))}function Hc(n,t,e,o){var r=n.dom().createRange();r.selectNode(t.dom());var i=r.getBoundingClientRect();return function(n,t,e,o){var r=n.dom().createRange();r.selectNode(t.dom());var i=r.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,e)),a=Math.max(i.top,Math.min(i.bottom,o));return rf(n,t,u,a)}(n,t,Math.max(i.left,Math.min(i.right,e)),Math.max(i.top,Math.min(i.bottom,o)))}function Pc(n,t){return Lt(t,n)}function zc(n,t,e,o){var r=function(n,t,e,o){var r=gr(n).dom().createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r}(n,t,e,o),i=jt(n,e)&&t===o;return r.collapsed&&!i}function Lc(n){var t=Be.fromDom(n.anchorNode),e=Be.fromDom(n.focusNode);return zc(t,n.anchorOffset,e,n.focusOffset)?on.some(Sc.create(t,n.anchorOffset,e,n.focusOffset)):function(n){if(0<n.rangeCount){var t=n.getRangeAt(0),e=n.getRangeAt(n.rangeCount-1);return on.some(Sc.create(Be.fromDom(t.startContainer),t.startOffset,Be.fromDom(e.endContainer),e.endOffset))}return on.none()}(n)}function jc(n,t){return function(n){var t=n.getClientRects(),e=0<t.length?t[0]:n.getBoundingClientRect();return 0<e.width||0<e.height?on.some(e).map(Ia):on.none()}(function(i,n){return Va(i,n).match({ltr:function(n,t,e,o){var r=i.document.createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r},rtl:function(n,t,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(n.dom(),t),r}})}(n,t))}function Uc(n){return n.fold(function(n){return n},function(n,t,e){return n.translate(-t,-e)})}function Wc(n){return n.fold(function(n){return n},function(n,t,e){return n})}function Gc(n){return k(n,function(n,t){return n.translate(t.left(),t.top())},Fu(0,0))}function Xc(n){var t=w(n,Wc);return Gc(t)}function Yc(n,t,e){var o=gr(n.element()),r=gu(o),i=function(o,n,t){var e=pr(t.root).dom();return on.from(e.frameElement).map(Be.fromDom).filter(function(n){var t=gr(n),e=gr(o.element());return jt(t,e)}).map(lu)}(n,0,e).getOr(r);return lf(i,r.left(),r.top())}function qc(n,t){return Di(n)?gf(n,t):function(n,t){var e=br(n);if(0===e.length)return cf(n,t);if(t<e.length)return cf(e[t],0);var o=e[e.length-1],r=Di(o)?Bc(o).length:br(o).length;return cf(o,r)}(n,t)}function Kc(n,t){return t.getSelection.getOrThunk(function(){return function(){return function(n){return on.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(Lc)}(n)}})().map(function(n){var t=qc(n.start(),n.soffset()),e=qc(n.finish(),n.foffset());return Tc.range(t.element(),t.offset(),e.element(),e.offset())})}function Jc(n){return n.x()+n.width()}function $c(n,t){return n.x()-t.width()}function Qc(n,t){return n.y()-t.height()+n.height()}function Zc(n){return n.y()}function ns(n,t,e){return Xa(Jc(n),Zc(n),e.southeast(),qa(),"link-layout-se")}function ts(n,t,e){return Xa($c(n,t),Zc(n),e.southwest(),Ka(),"link-layout-sw")}function es(n,t,e){return Xa(Jc(n),Qc(n,t),e.northeast(),Ja(),"link-layout-ne")}function os(n,t,e){return Xa($c(n,t),Qc(n,t),e.northwest(),$a(),"link-layout-nw")}function rs(n,t,e,o){var r=n+t;return o<r?e:r<e?o:r}function is(n,t,e){return n<=t?t:e<=n?e:n}function us(n,t,e,o){var r=n.x(),i=n.y(),u=n.bubble().offset().left(),a=n.bubble().offset().top(),c=o.y(),s=o.bottom(),f=o.x(),l=o.right(),d=i+a,m=function(n,t,e,o,r){var i=r.x(),u=r.y(),a=r.width(),c=r.height(),s=i<=n,f=u<=t,l=s&&f,d=n+e<=i+a&&t+o<=u+c,m=Math.abs(Math.min(e,s?i+a-n:i-(n+e))),g=Math.abs(Math.min(o,f?u+c-t:u-(t+o)));return{originInBounds:l,sizeInBounds:d,limitX:is(n,r.x(),r.right()),limitY:is(t,r.y(),r.bottom()),deltaW:m,deltaH:g}}(r+u,d,t,e,o),g=m.originInBounds,p=m.sizeInBounds,h=m.limitX,v=m.limitY,b=m.deltaW,y=m.deltaH,x=nn(v+y-c),w=nn(s-v),S=function(n,t,e,o){return n.fold(t,t,o,o,t,o,e,e)}(n.direction(),w,w,x),C=nn(h+b-f),k=nn(l-h),O=function(n,t,e,o){return n.fold(t,o,t,o,e,e,t,o)}(n.direction(),k,k,C),T=mc({x:h,y:v,width:b,height:y,maxHeight:S,maxWidth:O,direction:n.direction(),classes:{on:n.bubble().classesOn(),off:n.bubble().classesOff()},label:n.label(),candidateYforTest:d});return g&&p?xf.fit(T):xf.nofit(T,b,y)}function as(n,t,e,o){gi(t,"max-height"),gi(t,"max-width");var r=function(n){return{width:nn(mu(n)),height:nn(su(n))}}(t);return function(n,e,u,a,c){function o(n,o,r,i){var t=n(e,u,a);return us(t,s,f,c).fold(xf.fit,function(n,t,e){return i<e||r<t?xf.nofit(n,t,e):xf.nofit(o,r,i)})}var s=u.width(),f=u.height();return k(n,function(n,t){var e=d(o,t);return n.fold(xf.fit,e)},xf.nofit(mc({x:e.x(),y:e.y(),width:u.width(),height:u.height(),maxHeight:u.height(),maxWidth:u.width(),direction:qa(),classes:{on:[],off:[]},label:"none",candidateYforTest:e.y()}),-1,-1)).fold(l,l)}(o.preference(),n,r,e,o.bounds())}function cs(n,t,e){function o(n){return n+"px"}var r=function(n,r){return n.fold(function(){return gc("absolute",on.some(r.x()),on.some(r.y()),on.none(),on.none())},function(n,t,e,o){return Ta("absolute",r,n,t,e,o)},function(n,t,e,o){return Ta("fixed",r,n,t,e,o)})}(e.origin(),t);!function(n,t){var e=n.dom();Cn(t,function(n,t){n.fold(function(){ci(e,t)},function(n){ai(e,t,n)})})}(n,{position:on.some(r.position()),left:r.left().map(o),top:r.top().map(o),right:r.right().map(o),bottom:r.bottom().map(o)})}function ss(n,t){!function(n,t){var e=Au.max(n,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);si(n,"max-height",e+"px")}(n,Math.floor(t))}function fs(n,t,e){return n[t]===undefined?e:n[t]}function ls(n,t,e,o,r,i){var u=fs(i,"maxHeightFunction",wf()),a=fs(i,"maxWidthFunction",Z),c=n.anchorBox(),s=n.origin(),f=Cf({bounds:function(o,n){return n.fold(function(){return o.fold(wu,wu,bu)},function(e){return o.fold(e,e,function(){var n=e(),t=hc(o,n.x(),n.y());return bu(t.left(),t.top(),n.width(),n.height())})})}(s,r),origin:s,preference:o,maxHeightFunction:u,maxWidthFunction:a});kf(c,t,e,f)}function ds(n,t,e,o,r){var i=function(n,t){return yf(n,t)}(e.anchorBox,t);ls(i,r.element(),e.bubble,e.layouts,o,e.overrides)}function ms(n,t){Ni(n.element(),t.element())}function gs(t,n){var e=t.components();!function(n){bn(n.components(),function(n){return Hi(n.element())}),kr(n.element()),n.syncComponents()}(t);var o=_(e,n);bn(o,function(n){_f(n),t.getSystem().removeFromWorld(n)}),bn(n,function(n){n.getSystem().isConnected()?ms(t,n):(t.getSystem().addToWorld(n),ms(t,n),Jo(t.element())&&Af(n)),t.syncComponents()})}function ps(n,t){Mf(n,t,Ni)}function hs(n){_f(n),Hi(n.element()),n.getSystem().removeFromWorld(n)}function vs(t){var n=hr(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()});hs(t),n.each(function(n){n.syncComponents()})}function bs(n){var t=n.components();bn(t,hs),kr(n.element()),n.syncComponents()}function ys(n,t){Ff(n,t,Ni)}function xs(t){var n=br(t.element());bn(n,function(n){t.getByDom(n).each(_f)}),Hi(t.element())}function ws(t,n,e,o){e.get().each(function(n){bs(t)});var r=n.getAttachPoint(t);ps(r,t);var i=t.getSystem().build(o);return ps(t,i),e.set(i),i}function Ss(n,t,e,o){var r=ws(n,t,e,o);return t.onOpen(n,r),r}function Cs(t,e,o){o.get().each(function(n){bs(t),vs(t),e.onClose(t,n),o.clear()})}function ks(n,t,e){return e.isOpen()}function Os(n){var t,e=ot("Dismissal",Uf,n);return(t={})[zf()]={schema:qn([ct("target")]),onReceive:function(t,n){Pf.isOpen(t)&&(Pf.isPartOf(t,n.target)||e.isExtraPart(t,n.target)||e.fireEventInstead.fold(function(){return Pf.close(t)},function(n){return Xt(t,n.event)}))}},t}function Ts(n){var t,e=ot("Reposition",Wf,n);return(t={})[Lf()]={onReceive:function(t){Pf.isOpen(t)&&e.fireEventInstead.fold(function(){return e.doReposition(t)},function(n){return Xt(t,n.event)})}},t}function Es(n,t,e){t.store.manager.onLoad(n,t,e)}function Bs(n,t,e){t.store.manager.onUnload(n,t,e)}function Ds(){var n=Te(null);return Zi({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function _s(){var i=Te({}),u=Te({});return Zi({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Nn(i.get(),n).orThunk(function(){return Nn(u.get(),n)})},update:function(n){var t=i.get(),e=u.get(),o={},r={};bn(n,function(t){o[t.value]=t,Nn(t,"meta").each(function(n){Nn(n,"text").each(function(n){r[n]=t})})}),i.set(P(P({},t),o)),u.set(P(P({},e),r))},clear:function(){i.set({}),u.set({})}})}function As(n,t,e,o){var r=t.store;e.update([o]),r.setValue(n,o),t.onSetValue(n,o)}function Ms(t,n){return Dt(t,{},w(n,function(n){return function(t,e){return pe(t,t,Fn(),Yn(function(n){return te("The field: "+t+" is forbidden. "+e)}))}(n.name(),"Cannot configure "+n.name()+" for "+t)}).concat([_t("dump",l)]))}function Fs(n){return n.dump}function Is(n,t){return P(P({},n.dump),va(t))}function Rs(n,t,e,o){return e.uiType===tl?function(n,t,e,o){return n.exists(function(n){return n!==e.owner})?el.single(!0,nn(e)):Nn(o,e.name).fold(function(){throw new Error("Unknown placeholder component: "+e.name+"\nKnown: ["+wn(o)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(e,null,2))},function(n){return n.replace()})}(n,0,e,o):el.single(!1,nn(e))}function Vs(t,e,n,o){var r=L(o,function(n,t){return function(n,t){var e=!1;return{name:nn(n),required:function(){return t.fold(function(n,t){return n},function(n,t){return n})},used:function(){return e},replace:function(){if(!0===e)throw new Error("Trying to use the same placeholder more than once: "+n);return e=!0,t}}}(t,n)}),i=function(t,e,n,o){return E(n,function(n){return ol(t,e,n,o)})}(t,e,n,r);return Cn(r,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+t.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),i}function Ns(n){return n.fold(on.some,on.none,on.some,on.some)}function Hs(n){function t(n){return n.name}return n.fold(t,t,t,t)}function Ps(e,o){return function(n){var t=ot("Converting part type",o,n);return e(t)}}function zs(n,t,e,o){return Dn(t.defaults(n,e,o),e,{uid:n.partUids[t.name]},t.overrides(n,e,o))}function Ls(r,n){var t={};return bn(n,function(n){Ns(n).each(function(e){var o=Ol(r,e.pname);t[e.name]=function(n){var t=ot("Part: "+e.name+" in "+r,le(e.schema),n);return P(P({},o),{config:n,validated:t})}})}),t}function js(n,t,e){return{uiType:ul(),owner:n,name:t,config:e,validated:{}}}function Us(n){return E(n,function(n){return n.fold(on.none,on.some,on.none,on.none).map(function(n){return mt(n.name,n.schema.concat([Qu(Cl())]))}).toArray()})}function Ws(n){return w(n,Hs)}function Gs(n,t,e){return function(n,e,t){var i={},o={};return bn(t,function(n){n.fold(function(o){i[o.pname]=rl(!0,function(n,t,e){return o.factory.sketch(zs(n,o,t,e))})},function(n){var t=e.parts[n.name];o[n.name]=nn(n.factory.sketch(zs(e,n,t[Cl()]),t))},function(o){i[o.pname]=rl(!1,function(n,t,e){return o.factory.sketch(zs(n,o,t,e))})},function(r){i[r.pname]=il(!0,function(t,n,e){var o=t[r.name];return w(o,function(n){return r.factory.sketch(Dn(r.defaults(t,n,e),n,r.overrides(t,n)))})})})}),{internals:nn(i),externals:nn(o)}}(0,t,e)}function Xs(n,t,e){return Vs(on.some(n),t,t.components,e)}function Ys(n,t,e){var o=t.partUids[e];return n.getSystem().getByUid(o).toOption()}function qs(n,t,e){return Ys(n,t,e).getOrDie("Could not find part: "+e)}function Ks(n,t,e){var o={},r=t.partUids,i=n.getSystem();return bn(e,function(n){o[n]=nn(i.getByUid(r[n]))}),o}function Js(n,t){var e=n.getSystem();return L(t.partUids,function(n,t){return nn(e.getByUid(n))})}function $s(n){return wn(n.partUids)}function Qs(n,t,e){var o={},r=t.partUids,i=n.getSystem();return bn(e,function(n){o[n]=nn(i.getByUid(r[n]).getOrDie())}),o}function Zs(t,n){var e=Ws(n);return K(w(e,function(n){return{key:n,value:t+"-"+n}}))}function nf(t){return pe("partUids","partUids",Rn(function(n){return Zs(n.uid,t)}),ye())}function tf(n,t,e,o,r){var i=function(n,t){return(0<n.length?[mt("parts",n)]:[]).concat([ct("uid"),St("dom",{}),St("components",[]),Qu("originalSpec"),St("debug.sketcher",{})]).concat(t)}(o,r);return ot(n+" [SpecSchema]",qn(i.concat(t)),e)}function ef(n,t,e,o,r){var i=El(r),u=Us(e),a=nf(e),c=tf(n,t,i,u,[a]),s=Gs(0,c,e);return o(c,Xs(n,c,s.internals()),i,s.externals())}var of=function WF(e,o){var t=function(n){return e(n)?on.from(n.dom().nodeValue):on.none()};return{get:function(n){if(!e(n))throw new Error("Can only get "+o+" value of a "+o+" node");return t(n).getOr("")},getOption:t,set:function(n,t){if(!e(n))throw new Error("Can only set raw "+o+" value of a "+o+" node");n.dom().nodeValue=t}}}(Di,"text"),rf=function(n,t,e,o){return Di(t)?function(t,e,o,r){var n=t.dom().createRange();n.selectNode(e.dom());var i=n.getClientRects();return Bu(i,function(n){return Na(n,o,r)?on.some(n):on.none()}).map(function(n){return _c(t,e,o,r,n)})}(n,t,e,o):function(t,n,e,o){var r=t.dom().createRange(),i=br(n);return Bu(i,function(n){return r.selectNode(n.dom()),Na(r.getBoundingClientRect(),e,o)?rf(t,n,e,o):on.none()})}(n,t,e,o)},uf=["img","br"],af=function(n,i){var u=function(n){for(var t=br(n),e=t.length-1;0<=e;e--){var o=t[e];if(i(o))return on.some(o);var r=u(o);if(r.isSome())return r}return on.none()};return u(n)},cf=(document.caretPositionFromPoint||document.caretRangeFromPoint,fr("element","offset")),sf=En([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),ff=sf.screen,lf=sf.absolute,df=function(n,t,e,o){var r=n,i=t,u=e,a=o;n<0&&(r=0,u=e+n),t<0&&(i=0,a=o+t);var c=ff(Fu(r,i));return on.some(Hu(c,u,a))},mf=function(n,i,u,a,c){return n.map(function(n){var t=[i,n.point()],e=function(n,t,e,o){return n.fold(t,e,o)}(a,function(){return Xc(t)},function(){return Xc(t)},function(){return function(n){var t=w(n,Uc);return Gc(t)}(t)}),o=Pu(e.left(),e.top(),n.width(),n.height()),r=Aa(c,u,u.showAbove?[aa,ca,ia,ua,ec,oc]:[ia,ua,aa,ca,oc,oc],u.showAbove?[ca,aa,ua,ia,ec,oc]:[ua,ia,ca,aa,oc,ec]);return Ba({anchorBox:o,bubble:u.bubble.getOr(Oa()),overrides:u.overrides,layouts:r,placer:on.none()})})},gf=fr("element","offset"),pf=[ht("getSelection"),ct("root"),ht("bubble"),_a(),St("overrides",{}),St("showAbove",!1),$u("placement",function(n,t,e){var o=pr(t.root).dom(),r=Yc(n,0,t),i=Kc(o,t).bind(function(n){return jc(o,Tc.exactFromRange(n)).orThunk(function(){var t=Be.fromText("\ufeff");return xr(n.start(),t),jc(o,Tc.exact(t,0,t,1)).map(function(n){return Hi(t),n})}).bind(function(n){return df(n.left(),n.top(),n.width(),n.height())})}),u=Kc(o,t).bind(function(n){return Bi(n.start())?on.some(n.start()):hr(n.start())}).getOr(n.element());return mf(i,r,t,e,u)})],hf=[ct("node"),ct("root"),ht("bubble"),_a(),St("overrides",{}),St("showAbove",!1),$u("placement",function(r,i,u){var a=Yc(r,0,i);return i.node.bind(function(n){var t=n.dom().getBoundingClientRect(),e=df(t.left,t.top,t.width,t.height),o=i.node.getOr(r.element());return mf(e,a,i,u,o)})})],vf=[ct("item"),_a(),St("overrides",{}),$u("placement",function(n,t,e){var o=Ea(e,t.item.element()),r=Aa(n.element(),t,[ns,ts,es,os],[ts,ns,os,es]);return on.some(Ba({anchorBox:o,bubble:Oa(),overrides:t.overrides,layouts:r,placer:on.none()}))})],bf=it("anchor",{selection:pf,node:hf,hotspot:xc,submenu:vf,makeshift:wc}),yf=fr("anchorBox","origin"),xf=En([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),wf=nn(function(n,t){ss(n,t),fi(n,{"overflow-x":"hidden","overflow-y":"auto"})}),Sf=nn(function(n,t){ss(n,t)}),Cf=mr(["bounds","origin","preference","maxHeightFunction","maxWidthFunction"],[]),kf=function(n,t,e,o){var r=as(n,t,e,o);cs(t,r,o),function(n,t){var e=t.classes();ii(n,e.off),ri(n,e.on)}(t,r),function(n,t,e){e.maxHeightFunction()(n,t.maxHeight())}(t,r,o),function(n,t,e){e.maxWidthFunction()(n,t.maxWidth())}(t,r,o)},Of=function(n,t,e,o,r,i){var u=i.map(yu);return Tf(n,t,e,o,r,u)},Tf=function(r,i,n,t,u,a){var c=ot("positioning anchor.info",bf,t);Ca(function(){si(u.element(),"position","fixed");var n=di(u.element(),"visibility");si(u.element(),"visibility","hidden");var t=i.useFixed()?function(){var n=H.document.documentElement;return bc(0,0,n.clientWidth,n.clientHeight)}():function(n){var t=lu(n.element()),e=n.element().dom().getBoundingClientRect();return vc(t.left(),t.top(),e.width,e.height)}(r),e=c.placement,o=a.map(nn).or(i.getBounds);e(r,c,t).each(function(n){n.placer.getOr(ds)(r,t,n,o,u)}),n.fold(function(){gi(u.element(),"visibility")},function(n){si(u.element(),"visibility",n)}),di(u.element(),"left").isNone()&&di(u.element(),"top").isNone()&&di(u.element(),"right").isNone()&&di(u.element(),"bottom").isNone()&&di(u.element(),"position").is("fixed")&&gi(u.element(),"position")},u.element())},Ef=/* */Object.freeze({position:function(n,t,e,o,r){Of(n,t,e,o,r,on.none())},positionWithin:Of,positionWithinBounds:Tf,getMode:function(n,t,e){return t.useFixed()?"fixed":"absolute"}}),Bf=[St("useFixed",u),ht("getBounds")],Df=ba({fields:Bf,name:"positioning",active:lc,apis:Ef}),_f=function(n){Xt(n,No());var t=n.components();bn(t,_f)},Af=function(n){var t=n.components();bn(t,Af),Xt(n,Vo())},Mf=function(n,t,e){n.getSystem().addToWorld(t),e(n.element(),t.element()),Jo(n.element())&&Af(t),n.syncComponents()},Ff=function(n,t,e){e(n,t.element());var o=br(t.element());bn(o,function(n){t.getByDom(n).each(Af)})},If=function(n,t,e){var o=t.getAttachPoint(n);si(n.element(),"position",Df.getMode(o)),function(t,n,e,o){di(t.element(),n).fold(function(){Mr(t.element(),e)},function(n){Dr(t.element(),e,n)}),si(t.element(),n,o)}(n,"visibility",t.cloakVisibilityAttr,"hidden")},Rf=function(n,t,e){!function(t){return y(["top","left","right","bottom"],function(n){return di(t,n).isSome()})}(n.element())&&gi(n.element(),"position"),function(n,t,e){if(Ar(n.element(),e)){var o=_r(n.element(),e);si(n.element(),t,o)}else gi(n.element(),t)}(n,"visibility",t.cloakVisibilityAttr)},Vf=/* */Object.freeze({cloak:If,decloak:Rf,open:Ss,openWhileCloaked:function(n,t,e,o,r){If(n,t),Ss(n,t,e,o),r(),Rf(n,t)},close:Cs,isOpen:ks,isPartOf:function(t,e,n,o){return ks(0,0,n)&&n.get().exists(function(n){return e.isPartOf(t,n,o)})},getState:function(n,t,e){return e.get()},setContent:function(n,t,e,o){return e.get().map(function(){return ws(n,t,e,o)})}}),Nf=/* */Object.freeze({events:function(e,o){return nr([or(Do(),function(n,t){Cs(n,e,o)})])}}),Hf=[Yu("onOpen"),Yu("onClose"),ct("isPartOf"),ct("getAttachPoint"),St("cloakVisibilityAttr","data-precloak-visibility")],Pf=ba({fields:Hf,name:"sandboxing",active:Nf,apis:Vf,state:/* */Object.freeze({init:function(){var t=Te(on.none()),n=nn("not-implemented");return Zi({readState:n,isOpen:function(){return t.get().isSome()},clear:function(){t.set(on.none())},set:function(n){t.set(on.some(n))},get:function(n){return t.get()}})}})}),zf=nn("dismiss.popups"),Lf=nn("reposition.popups"),jf=nn("mouse.released"),Uf=qn([St("isExtraPart",nn(!1)),wt("fireEventInstead",[St("event",Ho())])]),Wf=qn([St("isExtraPart",nn(!1)),wt("fireEventInstead",[St("event",Po())]),dt("doReposition")]),Gf=/* */Object.freeze({onLoad:Es,onUnload:Bs,setValue:function(n,t,e,o){t.store.manager.setValue(n,t,e,o)},getValue:function(n,t,e){return t.store.manager.getValue(n,t,e)},getState:function(n,t,e){return e}}),Xf=/* */Object.freeze({events:function(e,o){var n=e.resetOnDom?[Fi(function(n,t){Es(n,e,o)}),Ii(function(n,t){Bs(n,e,o)})]:[ma(e,o,Es)];return nr(n)}}),Yf=/* */Object.freeze({memory:Ds,dataset:_s,manual:function(){return Zi({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),qf=[ht("initialValue"),ct("getFallbackEntry"),ct("getDataKey"),ct("setValue"),$u("manager",{setValue:As,getValue:function(n,t,e){var o=t.store,r=o.getDataKey(n);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(n){return n})},onLoad:function(t,e,o){e.store.initialValue.each(function(n){As(t,e,o,n)})},onUnload:function(n,t,e){e.clear()},state:_s})],Kf=[ct("getValue"),St("setValue",Z),ht("initialValue"),$u("manager",{setValue:function(n,t,e,o){t.store.setValue(n,o),t.onSetValue(n,o)},getValue:function(n,t,e){return t.store.getValue(n)},onLoad:function(t,e,n){e.store.initialValue.each(function(n){e.store.setValue(t,n)})},onUnload:Z,state:Qi.init})],Jf=[ht("initialValue"),$u("manager",{setValue:function(n,t,e,o){e.set(o),t.onSetValue(n,o)},getValue:function(n,t,e){return e.get()},onLoad:function(n,t,e){t.store.initialValue.each(function(n){e.isNotSet()&&e.set(n)})},onUnload:function(n,t,e){e.clear()},state:Ds})],$f=[Ct("store",{mode:"memory"},it("mode",{memory:Jf,manual:Kf,dataset:qf})),Yu("onSetValue"),St("resetOnDom",!1)],Qf=ba({fields:$f,name:"representing",active:Xf,apis:Gf,extra:{setValueFrom:function(n,t){var e=Qf.getValue(t);Qf.setValue(n,e)}},state:Yf}),Zf=Ms,nl=Is,tl="placeholder",el=En([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),ol=function(i,u,a,c){return Rs(i,0,a,c).fold(function(n,t){var e=t(u,a.config,a.validated),o=Nn(e,"components").getOr([]),r=E(o,function(n){return ol(i,u,n,c)});return[P(P({},e),{components:r})]},function(n,t){var e=t(u,a.config,a.validated);return a.validated.preprocess.getOr(l)(e)})},rl=el.single,il=el.multiple,ul=nn(tl),al=En([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),cl=St("factory",{sketch:l}),sl=St("schema",[]),fl=ct("name"),ll=pe("pname","pname",In(function(n){return"<alloy."+Vr(n.name)+">"}),ye()),dl=_t("schema",function(){return[ht("preprocess")]}),ml=St("defaults",nn({})),gl=St("overrides",nn({})),pl=le([cl,sl,fl,ll,ml,gl]),hl=le([cl,sl,fl,ml,gl]),vl=le([cl,sl,fl,ll,ml,gl]),bl=le([cl,dl,fl,ct("unit"),ll,ml,gl]),yl=Ps(al.required,pl),xl=Ps(al.external,hl),wl=Ps(al.optional,vl),Sl=Ps(al.group,bl),Cl=nn("entirety"),kl=/* */Object.freeze({required:yl,external:xl,optional:wl,group:Sl,asNamedPart:Ns,name:Hs,asCommon:function(n){return n.fold(l,l,l,l)},original:Cl}),Ol=function(n,t){return{uiType:ul(),owner:n,name:t}},Tl=/* */Object.freeze({generate:Ls,generateOne:js,schemas:Us,names:Ws,substitutes:Gs,components:Xs,defaultUids:Zs,defaultUidsSchema:nf,getAllParts:Js,getAllPartNames:$s,getPart:Ys,getPartOrDie:qs,getParts:Ks,getPartsOrDie:Qs}),El=function(n){return n.hasOwnProperty("uid")?n:P(P({},n),{uid:Nr("uid")})};function Bl(n){var t=ot("Sketcher for "+n.name,ql,n),e=L(t.apis,Lr),o=L(t.extraApis,function(n,t){return Pr(n,t)});return P(P({name:nn(t.name),partFields:nn([]),configFields:nn(t.configFields),sketch:function(n){return function(n,t,e,o){var r=El(o);return e(tf(n,t,r,[],[]),r)}(t.name,t.configFields,t.factory,n)}},e),o)}function Dl(n){var t=ot("Sketcher for "+n.name,Kl,n),e=Ls(t.name,t.partFields),o=L(t.apis,Lr),r=L(t.extraApis,function(n,t){return Pr(n,t)});return P(P({name:nn(t.name),partFields:nn(t.partFields),configFields:nn(t.configFields),sketch:function(n){return ef(t.name,t.configFields,t.partFields,t.factory,n)},parts:nn(e)},o),r)}function _l(n){return"input"===qo(n)&&"radio"!==_r(n,"type")||"textarea"===qo(n)}function Al(e,o,n,r){var t=Pc(e.element(),"."+o.highlightClass);bn(t,function(t){y(r,function(n){return n.element()===t})||(ei(t,o.highlightClass),e.getSystem().getByDom(t).each(function(n){o.onDehighlight(e,n),Xt(n,Go())}))})}function Ml(n,t,e,o){Al(n,t,0,[o]),Zl(n,t,e,o)||(ni(o.element(),t.highlightClass),t.onHighlight(n,o),Xt(o,Wo()))}function Fl(e,t,n,o){var r=Pc(e.element(),"."+t.itemClass);return T(r,function(n){return oi(n,t.highlightClass)}).bind(function(n){var t=rs(n,o,0,r.length-1);return e.getSystem().getByDom(r[t]).toOption()})}function Il(n,t,e){var o=D(n.slice(0,t)),r=D(n.slice(t+1));return O(o.concat(r),e)}function Rl(n,t,e){var o=D(n.slice(0,t));return O(o,e)}function Vl(n,t,e){var o=n.slice(0,t),r=n.slice(t+1);return O(r.concat(o),e)}function Nl(n,t,e){var o=n.slice(t+1);return O(o,e)}function Hl(e){return function(n){var t=n.raw();return vn(e,t.which)}}function Pl(n){return function(t){return B(n,function(n){return n(t)})}}function zl(n){return!0===n.raw().shiftKey}function Ll(n){return!0===n.raw().ctrlKey}function jl(n,t){return{matches:n,classification:t}}function Ul(n,t,e){t.exists(function(t){return e.exists(function(n){return jt(n,t)})})||Yt(n,zo(),{prevFocus:t,newFocus:e})}function Wl(){function r(n){return Sa(n.element())}return{get:r,set:function(n,t){var e=r(n);n.getSystem().triggerFocus(t,n.element());var o=r(n);Ul(n,e,o)}}}function Gl(){function r(n){return ud.getHighlighted(n).map(function(n){return n.element()})}return{get:r,set:function(t,n){var e=r(t);t.getSystem().getByDom(n).fold(Z,function(n){ud.highlight(t,n)});var o=r(t);Ul(t,e,o)}}}var Xl,Yl,ql=qn([ct("name"),ct("factory"),ct("configFields"),St("apis",{}),St("extraApis",{})]),Kl=qn([ct("name"),ct("factory"),ct("configFields"),ct("partFields"),St("apis",{}),St("extraApis",{})]),Jl=/* */Object.freeze({getCurrent:function(n,t,e){return t.find(n)}}),$l=[ct("find")],Ql=ba({fields:$l,name:"composing",apis:Jl}),Zl=function(n,t,e,o){return oi(o.element(),t.highlightClass)},nd=function(n,t,e,o){var r=Pc(n.element(),"."+t.itemClass);return on.from(r[o]).fold(function(){return an.error("No element found with index "+o)},n.getSystem().getByDom)},td=function(t,n,e){return Cu(t.element(),"."+n.itemClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},ed=function(t,n,e){var o=Pc(t.element(),"."+n.itemClass);return(0<o.length?on.some(o[o.length-1]):on.none()).bind(function(n){return t.getSystem().getByDom(n).toOption()})},od=function(t,n,e){var o=Pc(t.element(),"."+n.itemClass);return Eu(w(o,function(n){return t.getSystem().getByDom(n).toOption()}))},rd=/* */Object.freeze({dehighlightAll:function(n,t,e){return Al(n,t,0,[])},dehighlight:function(n,t,e,o){Zl(n,t,e,o)&&(ei(o.element(),t.highlightClass),t.onDehighlight(n,o),Xt(o,Go()))},highlight:Ml,highlightFirst:function(t,e,o){td(t,e).each(function(n){Ml(t,e,o,n)})},highlightLast:function(t,e,o){ed(t,e).each(function(n){Ml(t,e,o,n)})},highlightAt:function(t,e,o,n){nd(t,e,o,n).fold(function(n){throw new Error(n)},function(n){Ml(t,e,o,n)})},highlightBy:function(t,e,o,n){var r=od(t,e);O(r,n).each(function(n){Ml(t,e,o,n)})},isHighlighted:Zl,getHighlighted:function(t,n,e){return Cu(t.element(),"."+n.highlightClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},getFirst:td,getLast:ed,getPrevious:function(n,t,e){return Fl(n,t,0,-1)},getNext:function(n,t,e){return Fl(n,t,0,1)},getCandidates:od}),id=[ct("highlightClass"),ct("itemClass"),Yu("onHighlight"),Yu("onDehighlight")],ud=ba({fields:id,name:"highlighting",apis:rd}),ad=v(zl);(Yl=Xl=Xl||{}).OnFocusMode="onFocus",Yl.OnEnterOrSpaceMode="onEnterOrSpace",Yl.OnApiMode="onApi";function cd(n,t,e,i,u){function a(t,e,n,o,r){return function(n,t){return O(n,function(n){return n.matches(t)}).map(function(n){return n.classification})}(n(t,e,o,r),e.event()).bind(function(n){return n(t,e,o,r)})}var o={schema:function(){return n.concat([St("focusManager",Wl()),Ct("focusInside","onFocus",Zn(function(n){return vn(["onFocus","onEnterOrSpace","onApi"],n)?an.value(n):an.error("Invalid value for focusInside")})),$u("handler",o),$u("state",t),$u("sendFocusIn",u)])},processKey:a,toEvents:function(o,r){var n=o.focusInside!==Xl.OnFocusMode?on.none():u(o).map(function(e){return or(xo(),function(n,t){e(n,o,r),t.stop()})});return nr(n.toArray().concat([or(lo(),function(n,t){a(n,t,e,o,r).fold(function(){!function(t,e){var n=Hl([32].concat([13]))(e.event());o.focusInside===Xl.OnEnterOrSpaceMode&&n&&Ut(t,e)&&u(o).each(function(n){n(t,o,r),e.stop()})}(n,t)},function(n){t.stop()})}),or(mo(),function(n,t){a(n,t,i,o,r).each(function(n){t.stop()})})]))}};return o}function sd(n){function i(n,t){var e=n.visibilitySelector.bind(function(n){return ku(t,n)}).getOr(t);return 0<cu(e)}function t(t,e){(function(n,t){var e=Pc(n.element(),t.selector),o=S(e,function(n){return i(t,n)});return on.from(o[t.firstTabstop])})(t,e).each(function(n){e.focusManager.set(t,n)})}function u(t,n,e,o,r){return r(n,e,function(n){return function(n,t){return i(n,t)&&n.useTabstopAt(t)}(o,n)}).fold(function(){return o.cyclic?on.some(!0):on.none()},function(n){return o.focusManager.set(t,n),on.some(!0)})}function a(t,n,e,o){var r=Pc(t.element(),e.selector);return function(n,t){return t.focusManager.get(n).bind(function(n){return ku(n,t.selector)})}(t,e).bind(function(n){return T(r,d(jt,n)).bind(function(n){return u(t,r,n,e,o)})})}var e=[ht("onEscape"),ht("onEnter"),St("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),St("firstTabstop",0),St("useTabstopAt",nn(!0)),ht("visibilitySelector")].concat([n]),o=nn([jl(Pl([zl,Hl([9])]),function(n,t,e,o){var r=e.cyclic?Il:Rl;return a(n,0,e,r)}),jl(Hl([9]),function(n,t,e,o){var r=e.cyclic?Vl:Nl;return a(n,0,e,r)}),jl(Hl([27]),function(t,e,n,o){return n.onEscape.bind(function(n){return n(t,e)})}),jl(Pl([ad,Hl([13])]),function(t,e,n,o){return n.onEnter.bind(function(n){return n(t,e)})})]),r=nn([]);return cd(e,Qi.init,o,r,function(){return on.some(t)})}function fd(n,t,e){return _l(e)&&Hl([32])(t.event())?on.none():function(n,t,e){return Kt(n,e,ko()),on.some(!0)}(n,0,e)}function ld(n,t){return on.some(!0)}function dd(n,t,e){return e.execute(n,t,n.element())}function md(n){var e=Te(on.none());return Zi({readState:function(){return e.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,t){e.set(on.some({numRows:nn(n),numColumns:nn(t)}))},getNumRows:function(){return e.get().map(function(n){return n.numRows()})},getNumColumns:function(){return e.get().map(function(n){return n.numColumns()})}})}function gd(i){return function(n,t,e,o){var r=i(n.element());return km(r,n,t,e,o)}}function pd(n,t){var e=Da(n,t);return gd(e)}function hd(n,t){var e=Da(t,n);return gd(e)}function vd(r){return function(n,t,e,o){return km(r,n,t,e,o)}}function bd(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function yd(n,t,e){var o=d(jt,t),r=Pc(n,e);return function(t,n){return T(t,n).map(function(n){return Bm({index:n,candidates:t})})}(S(r,bd),o)}function xd(n,t){return T(n,function(n){return jt(t,n)})}function wd(e,n,o,t){return t(Math.floor(n/o),n%o).bind(function(n){var t=n.row()*o+n.column();return 0<=t&&t<e.length?on.some(e[t]):on.none()})}function Sd(r,n,i,u,a){return wd(r,n,u,function(n,t){var e=n===i-1?r.length-n*u:u,o=rs(t,a,0,e-1);return on.some({row:nn(n),column:nn(o)})})}function Cd(i,n,u,a,c){return wd(i,n,a,function(n,t){var e=rs(n,c,0,u-1),o=e===u-1?i.length-e*a:a,r=is(t,0,o-1);return on.some({row:nn(e),column:nn(r)})})}function kd(t,e,n){Cu(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Od(r){return function(n,t,e,o){return yd(n,t,e.selector).bind(function(n){return r(n.candidates(),n.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Td(n,t,e,o){return e.captureTab?on.some(!0):on.none()}function Ed(n,t,e,r){var i=function(n,t,e){var o=rs(t,r,0,e.length-1);return o===n?on.none():function(n){return"button"===qo(n)&&"disabled"===_r(n,"disabled")}(e[o])?i(n,o,e):on.from(e[o])};return yd(n,e,t).bind(function(n){var t=n.index(),e=n.candidates();return i(t,t,e)})}function Bd(t,e,o){return function(n,t){return t.focusManager.get(n).bind(function(n){return ku(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})}function Dd(t,e){e.getInitial(t).orThunk(function(){return Cu(t.element(),e.selector)}).each(function(n){e.focusManager.set(t,n)})}function _d(n,t,e){return Ed(n,e.selector,t,-1)}function Ad(n,t,e){return Ed(n,e.selector,t,1)}function Md(o){return function(n,t,e){return o(n,t,e).bind(function(){return e.executeOnMove?Bd(n,t,e):on.some(!0)})}}function Fd(n,t,e,o){return e.onEscape(n,t)}function Id(n,t,e){return on.from(n[t]).bind(function(n){return on.from(n[e]).map(function(n){return zm({rowIndex:t,columnIndex:e,cell:n})})})}function Rd(n,t,e,o){var r=n[t].length,i=rs(e,o,0,r-1);return Id(n,t,i)}function Vd(n,t,e,o){var r=rs(e,o,0,n.length-1),i=n[r].length,u=is(t,0,i-1);return Id(n,r,u)}function Nd(n,t,e,o){var r=n[t].length,i=is(e+o,0,r-1);return Id(n,t,i)}function Hd(n,t,e,o){var r=is(e+o,0,n.length-1),i=n[r].length,u=is(t,0,i-1);return Id(n,r,u)}function Pd(t,e){e.previousSelector(t).orThunk(function(){var n=e.selectors;return Cu(t.element(),n.cell)}).each(function(n){e.focusManager.set(t,n)})}function zd(n,t){return function(r,e,i){var u=i.cycles?n:t;return ku(e,i.selectors.row).bind(function(n){var t=Pc(n,i.selectors.cell);return xd(t,e).bind(function(e){var o=Pc(r,i.selectors.row);return xd(o,n).bind(function(n){var t=function(n,t){return w(n,function(n){return Pc(n,t.selectors.cell)})}(o,i);return u(t,n,e).map(function(n){return n.cell()})})})})}}function Ld(t,e,o){return o.focusManager.get(t).bind(function(n){return o.execute(t,e,n)})}function jd(t,e){Cu(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Ud(n,t,e){return Ed(n,e.selector,t,-1)}function Wd(n,t,e){return Ed(n,e.selector,t,1)}function Gd(n,t,e,o){var r=n.getSystem().build(o);Mf(n,r,e)}function Xd(n,t,e,o){var r=fg(n);O(r,function(n){return jt(o.element(),n.element())}).each(vs)}function Yd(t,n,e,o,r){var i=fg(t);return on.from(i[o]).map(function(n){return Xd(t,0,0,n),r.each(function(n){Gd(t,0,function(n,t){!function(n,t,e){yr(n,e).fold(function(){Ni(n,t)},function(n){xr(n,t)})}(n,t,o)},n)}),n})}function qd(n,t){return{key:n,value:{config:{},me:function(n,t){var e=nr(t);return ba({fields:[ct("enabled")],name:n,active:{events:nn(e)}})}(n,t),configAsRaw:nn({}),initialConfig:{},state:Qi}}}function Kd(n,t){t.ignore||(xa(n.element()),t.onFocus(n))}function Jd(n,t,e){var o=t.aria;o.update(n,o,e.get())}function $d(t,n,e){n.toggleClass.each(function(n){e.get()?ni(t.element(),n):ei(t.element(),n)})}function Qd(n,t,e){hg(n,t,e,!e.get())}function Zd(n,t,e){e.set(!0),$d(n,t,e),Jd(n,t,e)}function nm(n,t,e){e.set(!1),$d(n,t,e),Jd(n,t,e)}function tm(n,t,e){hg(n,t,e,t.selected)}function em(n){(Sa(n.element()).isNone()||pg.isFocused(n))&&(pg.isFocused(n)||pg.focus(n),Yt(n,Sg,{item:n}))}function om(n){Yt(n,Cg,{item:n})}function rm(n,t){var e={};Cn(n,function(n,t){bn(n,function(n){e[n]=t})});var o=t,r=function(n){return kn(n,function(n,t){return{k:n,v:t}})}(t),i=L(r,function(n,t){return[t].concat(Rg(e,o,r,t))});return L(e,function(n){return Nn(i,n).getOr([n])})}function im(n){return n.x()}function um(n,t){return n.x()+n.width()/2-t.width()/2}function am(n,t){return n.x()+n.width()-t.width()}function cm(n){return n.y()}function sm(n,t){return n.y()+n.height()-t.height()}function fm(n,t,e){return Xa(im(n),sm(n,t),e.innerSoutheast(),qa(),"layout-se")}function lm(n,t,e){return Xa(am(n,t),sm(n,t),e.innerSouthwest(),Ka(),"layout-sw")}function dm(n,t,e){return Xa(im(n),cm(n),e.innerNortheast(),Ja(),"layout-ne")}function mm(n,t,e){return Xa(am(n,t),cm(n),e.innerNorthwest(),$a(),"layout-nw")}function gm(n){function t(n,t){t.stop(),qt(n)}var e=Ht().deviceType.isTouch()?[or(To(),t)]:[or(ho(),t),or(ro(),function(n,t){t.cut()})];return nr(z([n.map(function(e){return or(ko(),function(n,t){e(n),t.stop()})}).toArray(),e]))}function pm(n){var t=function e(n){return n.uid!==undefined}(n)&&$(n,"uid")?n.uid:Nr("memento");return{get:function(n){return n.getSystem().getByUid(t).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(t).toOption()},asSpec:function(){return P(P({},n),{uid:t})}}}function hm(n){return on.from(n()["temporary-placeholder"]).getOr("!not found!")}function vm(n,t){return on.from(t()[n]).getOrThunk(function(){return hm(t)})}var bm,ym=sd(_t("cyclic",nn(!1))),xm=sd(_t("cyclic",nn(!0))),wm=[St("execute",fd),St("useSpace",!1),St("useEnter",!0),St("useControlEnter",!1),St("useDown",!1)],Sm=cd(wm,Qi.init,function(n,t,e,o){var r=e.useSpace&&!_l(n.element())?[32]:[],i=e.useEnter?[13]:[],u=e.useDown?[40]:[],a=r.concat(i).concat(u);return[jl(Hl(a),dd)].concat(e.useControlEnter?[jl(Pl([Ll,Hl([13])]),dd)]:[])},function(n,t,e,o){return e.useSpace&&!_l(n.element())?[jl(Hl([32]),ld)]:[]},function(){return on.none()}),Cm=/* */Object.freeze({flatgrid:md,init:function(n){return n.state(n)}}),km=function(t,e,n,o,r){return o.focusManager.get(e).bind(function(n){return t(e.element(),n,o,r)}).map(function(n){return o.focusManager.set(e,n),!0})},Om=vd,Tm=vd,Em=vd,Bm=mr(["index","candidates"],[]),Dm=[ct("selector"),St("execute",fd),qu("onEscape"),St("captureTab",!1),Ga()],_m=Od(function(n,t,e,o){return Sd(n,t,e,o,-1)}),Am=Od(function(n,t,e,o){return Sd(n,t,e,o,1)}),Mm=Od(function(n,t,e,o){return Cd(n,t,e,o,-1)}),Fm=Od(function(n,t,e,o){return Cd(n,t,e,o,1)}),Im=nn([jl(Hl([37]),pd(_m,Am)),jl(Hl([39]),hd(_m,Am)),jl(Hl([38]),Om(Mm)),jl(Hl([40]),Tm(Fm)),jl(Pl([zl,Hl([9])]),Td),jl(Pl([ad,Hl([9])]),Td),jl(Hl([27]),function(n,t,e,o){return e.onEscape(n,t)}),jl(Hl([32].concat([13])),function(t,e,o,n){return function(n,t){return t.focusManager.get(n).bind(function(n){return ku(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})})]),Rm=nn([jl(Hl([32]),ld)]),Vm=cd(Dm,md,Im,Rm,function(){return on.some(kd)}),Nm=[ct("selector"),St("getInitial",on.none),St("execute",fd),qu("onEscape"),St("executeOnMove",!1),St("allowVertical",!0)],Hm=nn([jl(Hl([32]),ld)]),Pm=cd(Nm,Qi.init,function(n,t,e,o){var r=[37].concat(e.allowVertical?[38]:[]),i=[39].concat(e.allowVertical?[40]:[]);return[jl(Hl(r),Md(pd(_d,Ad))),jl(Hl(i),Md(hd(_d,Ad))),jl(Hl([13]),Bd),jl(Hl([32]),Bd),jl(Hl([27]),Fd)]},Hm,function(){return on.some(Dd)}),zm=mr(["rowIndex","columnIndex","cell"],[]),Lm=[mt("selectors",[ct("row"),ct("cell")]),St("cycles",!0),St("previousSelector",on.none),St("execute",fd)],jm=zd(function(n,t,e){return Rd(n,t,e,-1)},function(n,t,e){return Nd(n,t,e,-1)}),Um=zd(function(n,t,e){return Rd(n,t,e,1)},function(n,t,e){return Nd(n,t,e,1)}),Wm=zd(function(n,t,e){return Vd(n,e,t,-1)},function(n,t,e){return Hd(n,e,t,-1)}),Gm=zd(function(n,t,e){return Vd(n,e,t,1)},function(n,t,e){return Hd(n,e,t,1)}),Xm=nn([jl(Hl([37]),pd(jm,Um)),jl(Hl([39]),hd(jm,Um)),jl(Hl([38]),Om(Wm)),jl(Hl([40]),Tm(Gm)),jl(Hl([32].concat([13])),function(t,e,o){return Sa(t.element()).bind(function(n){return o.execute(t,e,n)})})]),Ym=nn([jl(Hl([32]),ld)]),qm=cd(Lm,Qi.init,Xm,Ym,function(){return on.some(Pd)}),Km=[ct("selector"),St("execute",fd),St("moveOnTab",!1)],Jm=nn([jl(Hl([38]),Em(Ud)),jl(Hl([40]),Em(Wd)),jl(Pl([zl,Hl([9])]),function(n,t,e){return e.moveOnTab?Em(Ud)(n,t,e):on.none()}),jl(Pl([ad,Hl([9])]),function(n,t,e){return e.moveOnTab?Em(Wd)(n,t,e):on.none()}),jl(Hl([13]),Ld),jl(Hl([32]),Ld)]),$m=nn([jl(Hl([32]),ld)]),Qm=cd(Km,Qi.init,Jm,$m,function(){return on.some(jd)}),Zm=[qu("onSpace"),qu("onEnter"),qu("onShiftEnter"),qu("onLeft"),qu("onRight"),qu("onTab"),qu("onShiftTab"),qu("onUp"),qu("onDown"),qu("onEscape"),St("stopSpaceKeyup",!1),ht("focusIn")],ng=cd(Zm,Qi.init,function(n,t,e){return[jl(Hl([32]),e.onSpace),jl(Pl([ad,Hl([13])]),e.onEnter),jl(Pl([zl,Hl([13])]),e.onShiftEnter),jl(Pl([zl,Hl([9])]),e.onShiftTab),jl(Pl([ad,Hl([9])]),e.onTab),jl(Hl([38]),e.onUp),jl(Hl([40]),e.onDown),jl(Hl([37]),e.onLeft),jl(Hl([39]),e.onRight),jl(Hl([32]),e.onSpace),jl(Hl([27]),e.onEscape)]},function(n,t,e){return e.stopSpaceKeyup?[jl(Hl([32]),ld)]:[]},function(n){return n.focusIn}),tg=ym.schema(),eg=xm.schema(),og=Pm.schema(),rg=Vm.schema(),ig=qm.schema(),ug=Sm.schema(),ag=Qm.schema(),cg=ng.schema(),sg=ya({branchKey:"mode",branches:/* */Object.freeze({acyclic:tg,cyclic:eg,flow:og,flatgrid:rg,matrix:ig,execution:ug,menu:ag,special:cg}),name:"keying",active:{events:function(n,t){return n.handler.toEvents(n,t)}},apis:{focusIn:function(t,e,o){e.sendFocusIn(e).fold(function(){t.getSystem().triggerFocus(t.element(),t.element())},function(n){n(t,e,o)})},setGridSize:function(n,t,e,o,r){$(e,"setGridSize")?e.setGridSize(o,r):H.console.error("Layout does not support setGridSize")}},state:Cm}),fg=function(n,t){return n.components()},lg=ba({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,t,e,o){Gd(n,0,Ni,o)},prepend:function(n,t,e,o){Gd(n,0,Sr,o)},remove:Xd,replaceAt:Yd,replaceBy:function(t,n,e,o,r){var i=fg(t);return T(i,o).bind(function(n){return Yd(t,0,0,n,r)})},set:function(t,n,e,o){Ca(function(){var n=w(o,t.getSystem().build);gs(t,n)},t.element())},contents:fg})}),dg=/* */Object.freeze({focus:Kd,blur:function(n,t){t.ignore||function(n){n.dom().blur()}(n.element())},isFocused:function(n){return function(n){var t=gr(n).dom();return n.dom()===t.activeElement}(n.element())}}),mg=/* */Object.freeze({exhibit:function(n,t){var e=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ur(e)},events:function(e){return nr([or(xo(),function(n,t){Kd(n,e),t.stop()})].concat(e.stopMousedown?[or(ro(),function(n,t){t.event().prevent()})]:[]))}}),gg=[Yu("onFocus"),St("stopMousedown",!1),St("ignore",!1)],pg=ba({fields:gg,name:"focusing",active:mg,apis:dg}),hg=function(n,t,e,o){(o?Zd:nm)(n,t,e)},vg=/* */Object.freeze({onLoad:tm,toggle:Qd,isOn:function(n,t,e){return e.get()},on:Zd,off:nm,set:hg}),bg=/* */Object.freeze({exhibit:function(n,t,e){return Ur({})},events:function(n,t){var e=function(t,e,o){return Vi(function(n){o(n,t,e)})}(n,t,Qd),o=ma(n,t,tm);return nr(z([n.toggleOnExecute?[e]:[],[o]]))}}),yg=function(n,t,e){Dr(n.element(),"aria-expanded",e)},xg=[St("selected",!1),ht("toggleClass"),St("toggleOnExecute",!0),Ct("aria",{mode:"none"},it("mode",{pressed:[St("syncWithExpanded",!1),$u("update",function(n,t,e){Dr(n.element(),"aria-pressed",e),t.syncWithExpanded&&yg(n,t,e)})],checked:[$u("update",function(n,t,e){Dr(n.element(),"aria-checked",e)})],expanded:[$u("update",yg)],selected:[$u("update",function(n,t,e){Dr(n.element(),"aria-selected",e)})],none:[$u("update",Z)]}))],wg=ba({fields:xg,name:"toggling",active:bg,apis:vg,state:(bm=!1,{init:function(){var t=Te(bm);return{get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(bm)},readState:function(){return t.get()}}}})}),Sg="alloy.item-hover",Cg="alloy.item-focus",kg=nn(Sg),Og=nn(Cg),Tg=[ct("data"),ct("components"),ct("dom"),St("hasSubmenu",!1),ht("toggling"),Zf("itemBehaviours",[wg,pg,sg,Qf]),St("ignoreFocus",!1),St("domModification",{}),$u("builder",function(n){return{dom:n.dom,domModification:P(P({},n.domModification),{attributes:P(P(P({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:nl(n.itemBehaviours,[n.toggling.fold(wg.revoke,function(n){return wg.config(P({aria:{mode:"checked"}},n))}),pg.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){om(n)}}),sg.config({mode:"execution"}),Qf.config({store:{mode:"memory",initialValue:n.data}}),qd("item-type-events",[or(Eo(),qt),cr(ro()),or(co(),em),or(Oo(),pg.focus)])]),components:n.components,eventOrder:n.eventOrder}}),St("eventOrder",{})],Eg=[ct("dom"),ct("components"),$u("builder",function(n){return{dom:n.dom,components:n.components,events:nr([function(n){return or(n,function(n,t){t.stop()})}(Oo())])}})],Bg=nn([yl({name:"widget",overrides:function(t){return{behaviours:va([Qf.config({store:{mode:"manual",getValue:function(n){return t.data},setValue:function(){}}})])}}})]),Dg=[ct("uid"),ct("data"),ct("components"),ct("dom"),St("autofocus",!1),St("ignoreFocus",!1),Zf("widgetBehaviours",[Qf,pg,sg]),St("domModification",{}),nf(Bg()),$u("builder",function(e){function o(n){return Ys(n,e,"widget").map(function(n){return sg.focusIn(n),n})}function n(n,t){return _l(t.event().target())||e.autofocus&&t.setSource(n.element()),on.none()}var t=Gs(0,e,Bg()),r=Xs("item-widget",e,t.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:nr([Vi(function(n,t){o(n).each(function(n){t.stop()})}),or(co(),em),or(Oo(),function(n,t){e.autofocus?o(n):pg.focus(n)})]),behaviours:nl(e.widgetBehaviours,[Qf.config({store:{mode:"memory",initialValue:e.data}}),pg.config({ignore:e.ignoreFocus,onFocus:function(n){om(n)}}),sg.config({mode:"special",focusIn:e.autofocus?function(n){o(n)}:ac(),onLeft:n,onRight:n,onEscape:function(n,t){return pg.isFocused(n)||e.autofocus?(e.autofocus&&t.setSource(n.element()),on.none()):(pg.focus(n),on.some(!0))}})])}})],_g=it("type",{widget:Dg,item:Tg,separator:Eg}),Ag=nn([Sl({factory:{sketch:function(n){var t=ot("menu.spec item",_g,n);return t.builder(t)}},name:"items",unit:"item",defaults:function(n,t){return t.hasOwnProperty("uid")?t:P(P({},t),{uid:Nr("item")})},overrides:function(n,t){return{type:t.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Mg=nn([ct("value"),ct("items"),ct("dom"),ct("components"),St("eventOrder",{}),Ms("menuBehaviours",[ud,Qf,Ql,sg]),Ct("movement",{mode:"menu",moveOnTab:!0},it("mode",{grid:[Ga(),$u("config",function(n,t){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:n.focusManager}})],matrix:[$u("config",function(n,t){return{mode:"matrix",selectors:{row:t.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),ct("rowSelector")],menu:[St("moveOnTab",!0),$u("config",function(n,t){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:t.moveOnTab,focusManager:n.focusManager}})]})),st("markers",Ua()),St("fakeFocus",!1),St("focusManager",Wl()),Yu("onHighlight")]),Fg=nn("alloy.menu-focus"),Ig=Dl({name:"Menu",configFields:Mg(),partFields:Ag(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Is(n.menuBehaviours,[ud.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),Qf.config({store:{mode:"memory",initialValue:n.value}}),Ql.config({find:on.some}),sg.config(n.movement.config(n,n.movement))]),events:nr([or(Og(),function(t,e){var n=e.event();t.getSystem().getByDom(n.target()).each(function(n){ud.highlight(t,n),e.stop(),Yt(t,Fg(),{menu:t,item:n})})}),or(kg(),function(n,t){var e=t.event().item();ud.highlight(n,e)})]),components:t,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Rg=function(e,o,r,n){return Nn(r,n).bind(function(n){return Nn(e,n).bind(function(n){var t=Rg(e,o,r,n);return on.some([n].concat(t))})}).getOr([])},Vg=function(n){return"prepared"===n.type?on.some(n.menu):on.none()},Ng={init:function(){function r(n,e,o){return f(n).bind(function(t){return function(e){return F(i.get(),function(n,t){return n===e})}(n).bind(function(n){return e(n).map(function(n){return{triggeredMenu:t,triggeringItem:n,triggeringPath:o}})})})}var i=Te({}),u=Te({}),a=Te({}),c=Te(on.none()),s=Te({}),f=function(n){return t(n).bind(Vg)},t=function(n){return Nn(u.get(),n)},e=function(n){return Nn(i.get(),n)};return{setMenuBuilt:function(n,t){var e;u.set(P(P({},u.get()),((e={})[n]={type:"prepared",menu:t},e)))},setContents:function(n,t,e,o){c.set(on.some(n)),i.set(e),u.set(t),s.set(o);var r=rm(o,e);a.set(r)},expand:function(e){return Nn(i.get(),e).map(function(n){var t=Nn(a.get(),e).getOr([]);return[n].concat(t)})},refresh:function(n){return Nn(a.get(),n)},collapse:function(n){return Nn(a.get(),n).bind(function(n){return 1<n.length?on.some(n.slice(1)):on.none()})},lookupMenu:t,lookupItem:e,otherMenus:function(n){var t=s.get();return _(wn(t),n)},getPrimary:function(){return c.get().bind(f)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(on.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(n,o){var t=S(e(n).toArray(),function(n){return f(n).isSome()});return Nn(a.get(),n).bind(function(n){var e=D(t.concat(n));return function(n){for(var t=[],e=0;e<n.length;e++){var o=n[e];if(!o.isSome())return on.none();t.push(o.getOrDie())}return on.some(t)}(E(e,function(n,t){return r(n,o,e.slice(0,t+1)).fold(function(){return c.get().is(n)?[]:[on.none()]},function(n){return[on.some(n)]})}))})}}},extractPreparedMenu:Vg},Hg=nn("collapse-item"),Pg=Bl({name:"TieredMenu",configFields:[Ju("onExecute"),Ju("onEscape"),Ku("onOpenMenu"),Ku("onOpenSubmenu"),Ku("onRepositionMenu"),Yu("onCollapseMenu"),St("highlightImmediately",!0),mt("data",[ct("primary"),ct("menus"),ct("expansions")]),St("fakeFocus",!1),Yu("onHighlight"),Yu("onHover"),Wu(),ct("dom"),St("navigateOnHover",!0),St("stayInDom",!1),Ms("tmenuBehaviours",[sg,ud,Ql,lg]),St("eventOrder",{})],apis:{collapseMenu:function(n,t){n.collapseMenu(t)},highlightPrimary:function(n,t){n.highlightPrimary(t)},repositionMenus:function(n,t){n.repositionMenus(t)}},factory:function(a,n){function e(n){var t=function(o,r,n){return L(n,function(n,t){function e(){return Ig.sketch(P(P({dom:n.dom},n),{value:t,items:n.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?Gl():Wl()}))}return t===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})}(n,a.data.primary,a.data.menus),e=o();return g.setContents(a.data.primary,t,a.data.expansions,e),g.getPrimary()}function c(n){return Qf.getValue(n).value}function u(t,n){ud.highlight(t,n),ud.getHighlighted(n).orThunk(function(){return ud.getFirst(n)}).each(function(n){Kt(t,n.element(),Oo())})}function s(t,n){return Eu(w(n,function(n){return t.lookupMenu(n).bind(function(n){return"prepared"===n.type?on.some(n.menu):on.none()})}))}function f(t,n,e){var o=s(n,n.otherMenus(e));bn(o,function(n){ii(n.element(),[a.markers.backgroundMenu]),a.stayInDom||lg.remove(t,n)})}function l(n,o){var t=function(o){return r.get().getOrThunk(function(){var e={},n=Pc(o.element(),"."+a.markers.item),t=S(n,function(n){return"true"===_r(n,"aria-haspopup")});return bn(t,function(n){o.getSystem().getByDom(n).each(function(n){var t=c(n);e[t]=n})}),r.set(on.some(e)),e})}(n);Cn(t,function(n,t){var e=vn(o,t);Dr(n.element(),"aria-expanded",e)})}function d(o,r,i){return on.from(i[0]).bind(function(n){return r.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return on.none();var t=n.menu,e=s(r,i.slice(1));return bn(e,function(n){ni(n.element(),a.markers.backgroundMenu)}),Jo(t.element())||lg.append(o,uu(t)),ii(t.element(),[a.markers.backgroundMenu]),u(o,t),f(o,r,i),on.some(t)})})}var m,t,r=Te(on.none()),g=Ng.init(),o=function(n){return L(a.data.menus,function(n,t){return E(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(t=m=m||{})[t.HighlightSubmenu=0]="HighlightSubmenu",t[t.HighlightParent=1]="HighlightParent";function i(r,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=c(i);return g.expand(n).bind(function(o){return l(r,o),on.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(n){var t=function(n,t,e){if("notbuilt"!==e.type)return e.menu;var o=n.getSystem().build(e.nbMenu());return g.setMenuBuilt(t,o),o}(r,e,n);return Jo(t.element())||lg.append(r,uu(t)),a.onOpenSubmenu(r,i,t,D(o)),u===m.HighlightSubmenu?(ud.highlightFirst(t),d(r,g,o)):(ud.dehighlightAll(t),on.some(i))})})})}function p(t,e){var n=c(e);return g.collapse(n).bind(function(n){return l(t,n),d(t,g,n).map(function(n){return a.onCollapseMenu(t,e,n),n})})}function h(e){return function(t,n){return ku(n.getSource(),"."+a.markers.item).bind(function(n){return t.getSystem().getByDom(n).toOption().bind(function(n){return e(t,n).map(function(){return!0})})})}}function v(n){return ud.getHighlighted(n).bind(ud.getHighlighted)}var b=nr([or(Fg(),function(e,o){var n=o.event().item();g.lookupItem(c(n)).each(function(){var n=o.event().menu();ud.highlight(e,n);var t=c(o.event().item());g.refresh(t).each(function(n){return f(e,g,n)})})}),Vi(function(t,n){var e=n.event().target();t.getSystem().getByDom(e).each(function(n){0===c(n).indexOf("collapse-item")&&p(t,n),i(t,n,m.HighlightSubmenu).fold(function(){a.onExecute(t,n)},function(){})})}),Fi(function(t,n){e(t).each(function(n){lg.append(t,uu(n)),a.onOpenMenu(t,n),a.highlightImmediately&&u(t,n)})})].concat(a.navigateOnHover?[or(kg(),function(n,t){var e=t.event().item();!function(t,n){var e=c(n);g.refresh(e).bind(function(n){return l(t,n),d(t,g,n)})}(n,e),i(n,e,m.HighlightParent),a.onHover(n,e)})]:[])),y={collapseMenu:function(t){v(t).each(function(n){p(t,n)})},highlightPrimary:function(t){g.getPrimary().each(function(n){u(t,n)})},repositionMenus:function(o){g.getPrimary().bind(function(t){return v(o).bind(function(n){var t=c(n),e=I(g.getMenus()),o=Eu(w(e,Ng.extractPreparedMenu));return g.getTriggeringPath(t,function(n){return function(n,t,e){return Bu(t,function(n){if(!n.getSystem().isConnected())return on.none();var t=ud.getCandidates(n);return O(t,function(n){return c(n)===e})})}(0,o,n)})}).map(function(n){return{primary:t,triggeringPath:n}})}).fold(function(){(function(n){return on.from(n.components()[0]).filter(function(n){return"menu"===_r(n.element(),"role")})})(o).each(function(n){a.onRepositionMenu(o,n,[])})},function(n){var t=n.primary,e=n.triggeringPath;a.onRepositionMenu(o,t,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Is(a.tmenuBehaviours,[sg.config({mode:"special",onRight:h(function(n,t){return _l(t.element())?on.none():i(n,t,m.HighlightSubmenu)}),onLeft:h(function(n,t){return _l(t.element())?on.none():p(n,t)}),onEscape:h(function(n,t){return p(n,t).orThunk(function(){return a.onEscape(n,t).map(function(){return n})})}),focusIn:function(t,n){g.getPrimary().each(function(n){Kt(t,n.element(),Oo())})}}),ud.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Ql.config({find:function(n){return ud.getHighlighted(n)}}),lg.config({})]),eventOrder:a.eventOrder,apis:y,events:b}},extraApis:{tieredData:function(n,t,e){return{primary:n,menus:t,expansions:e}},singleData:function(n,t){return{primary:n,menus:q(n,t),expansions:{}}},collapseItem:function(n){return{value:Vr(Hg()),meta:{text:n}}}}}),zg=Bl({name:"InlineView",configFields:[ct("lazySink"),Yu("onShow"),Yu("onHide"),xt("onEscape"),Ms("inlineBehaviours",[Pf,Qf,fc]),wt("fireDismissalEventInstead",[St("event",Ho())]),wt("fireRepositionEventInstead",[St("event",Po())]),St("getRelated",on.none),St("eventOrder",on.none)],factory:function(i,n){function t(e){Pf.isOpen(e)&&Qf.getValue(e).each(function(n){switch(n.mode){case"menu":Pf.getState(e).each(function(n){Pg.repositionMenus(n)});break;case"position":var t=i.lazySink(e).getOrDie();Df.positionWithinBounds(t,n.anchor,e,n.getBounds())}})}var o=function(n,t,e,o){r(n,t,e,function(){return o.map(function(n){return yu(n)})})},r=function(n,t,e,o){var r=i.lazySink(n).getOrDie();Pf.openWhileCloaked(n,e,function(){return Df.positionWithinBounds(r,t,n,o())}),Qf.setValue(n,on.some({mode:"position",anchor:t,getBounds:o}))},u=function(n,t,e,o){var r=function(n,t,r,e,i){function u(){return n.lazySink(t)}function a(n){return function(n){return 2===n.length}(n)?o:{}}var o="horizontal"===e.type?{layouts:{onLtr:function(){return la()},onRtl:function(){return da()}}}:{};return Pg.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,onEscape:function(){return Pf.close(t),n.onEscape.map(function(n){return n(t)}),on.some(!0)},onExecute:function(){return on.some(!0)},onOpenMenu:function(n,t){Df.positionWithinBounds(u().getOrDie(),r,t,i())},onOpenSubmenu:function(n,t,e,o){var r=u().getOrDie();Df.position(r,P({anchor:"submenu",item:t},a(o)),e)},onRepositionMenu:function(n,t,e){var o=u().getOrDie();Df.positionWithinBounds(o,r,t,i()),bn(e,function(n){var t=a(n.triggeringPath);Df.position(o,P({anchor:"submenu",item:n.triggeringItem},t),n.triggeredMenu)})}})}(i,n,t,e,o);Pf.open(n,r),Qf.setValue(n,on.some({mode:"menu",menu:r}))},e={setContent:function(n,t){Pf.setContent(n,t)},showAt:function(n,t,e){o(n,t,e,on.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(n,t,e){u(n,t,e,function(){return on.none()})},showMenuWithinBounds:u,hide:function(n){Qf.setValue(n,on.none()),Pf.close(n)},getContent:function(n){return Pf.getState(n)},reposition:t,isOpen:Pf.isOpen};return{uid:i.uid,dom:i.dom,behaviours:Is(i.inlineBehaviours,[Pf.config({isPartOf:function(n,t,e){return zu(t,e)||function(n,t){return i.getRelated(n).exists(function(n){return zu(n,t)})}(n,e)},getAttachPoint:function(n){return i.lazySink(n).getOrDie()},onOpen:function(n){i.onShow(n)},onClose:function(n){i.onHide(n)}}),Qf.config({store:{mode:"memory",initialValue:on.none()}}),fc.config({channels:P(P({},Os(P({isExtraPart:nn(!1)},i.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Ts(P(P({isExtraPart:nn(!1)},i.fireRepositionEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})),{doReposition:t})))})]),eventOrder:i.eventOrder,apis:e}},apis:{showAt:function(n,t,e,o){n.showAt(t,e,o)},showWithin:function(n,t,e,o,r){n.showWithin(t,e,o,r)},showWithinBounds:function(n,t,e,o,r){n.showWithinBounds(t,e,o,r)},showMenuAt:function(n,t,e,o){n.showMenuAt(t,e,o)},showMenuWithinBounds:function(n,t,e,o,r){n.showMenuWithinBounds(t,e,o,r)},hide:function(n,t){n.hide(t)},isOpen:function(n,t){return n.isOpen(t)},getContent:function(n,t){return n.getContent(t)},setContent:function(n,t,e){n.setContent(t,e)},reposition:function(n,t){n.reposition(t)}}}),Lg=function(n,t,e){return Xa(um(n,t),cm(n),e.innerNorth(),Za(),"layout-n")},jg=function(n,t,e){return Xa(um(n,t),sm(n,t),e.innerSouth(),Qa(),"layout-s")},Ug=Bl({name:"Button",factory:function(n){function e(t){return Nn(n.dom,"attributes").bind(function(n){return Nn(n,t)})}var t=gm(n.action),o=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:nl(n.buttonBehaviours,[pg.config({}),sg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var n=e("type").getOr("button"),t=e("role").map(function(n){return{role:n}}).getOr({});return P({type:n},t)}()},eventOrder:n.eventOrder}},configFields:[St("uid",undefined),ct("dom"),St("components",[]),Zf("buttonBehaviours",[pg,sg]),ht("action"),ht("role"),St("eventOrder",{})]}),Wg={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Gg=Bl({name:"Notification",factory:function(t){function e(n){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+n+"%"}}}}function o(n){return{dom:{tag:"div",classes:["tox-text"],innerHtml:n+"%"}}}var r=pm({dom:{tag:"p",innerHtml:t.translationProvider(t.text)},behaviours:va([lg.config({})])}),i=pm({dom:{tag:"div",classes:t.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:va([lg.config({})])}),n={updateProgress:function(n,t){n.getSystem().isConnected()&&i.getOpt(n).each(function(n){lg.set(n,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(t)]},o(t)])})},updateText:function(n,t){if(n.getSystem().isConnected()){var e=r.get(n);lg.set(e,[ki(t)])}}},u=z([t.icon.toArray(),t.level.toArray(),t.level.bind(function(n){return on.from(Wg[n])}).toArray()]);return{uid:t.uid,dom:{tag:"div",attributes:{role:"alert"},classes:t.level.map(function(n){return["tox-notification","tox-notification--in","tox-notification--"+n]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:function(n,t){return Bu(n,function(n){return on.from(t()[n])}).getOrThunk(function(){return hm(t)})}(u,t.iconProvider)}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[r.asSpec()],behaviours:va([lg.config({})])}].concat(t.progress?[i.asSpec()]:[]).concat(Ug.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:vm("close",t.iconProvider),attributes:{"aria-label":t.translationProvider("Close")}}}],action:function(n){t.onAction(n)}})),apis:n}},configFields:[ht("level"),ct("progress"),ct("icon"),ct("onAction"),ct("text"),ct("iconProvider"),ct("translationProvider")],apis:{updateProgress:function(n,t,e){n.updateProgress(t,e)},updateText:function(n,t,e){n.updateText(t,e)}}}),Xg=tinymce.util.Tools.resolve("tinymce.util.Delay");function Yg(n,u,o){var a=u.backstage;return{open:function(n,t){function e(){t(),zg.hide(i)}var r=iu(Gg.sketch({text:n.text,level:vn(["success","error","warning","warn","info"],n.type)?n.type:undefined,progress:!0===n.progressBar,icon:on.from(n.icon),onAction:e,iconProvider:a.shared.providers.icons,translationProvider:a.shared.providers.translate})),i=iu(zg.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}}));return o.add(i),0<n.timeout&&Xg.setTimeout(function(){e()},n.timeout),{close:e,moveTo:function(n,t){zg.showAt(i,{anchor:"makeshift",x:n,y:t},uu(r))},moveRel:function(n,t){if("banner"!==t){var e=function(n){switch(n){case"bc-bc":return jg;case"tc-tc":return Lg;case"tc-bc":return ec;case"bc-tc":default:return oc}}(t),o={anchor:"node",root:_i(),node:on.some(Be.fromDom(n)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};zg.showAt(i,o,uu(r))}else zg.showAt(i,u.backstage.shared.anchors.banner(),uu(r))},text:function(n){Gg.updateText(r,n)},settings:n,getEl:function(){return r.element().dom()},progressBar:{value:function(n){Gg.updateProgress(r,n)}}}},close:function(n){n.close()},reposition:function(n){!function(n){bn(n,function(n){return n.moveTo(0,0)})}(n),function(e){0<e.length&&(yn(e).each(function(n){return n.moveRel(null,"banner")}),bn(e,function(n,t){0<t&&n.moveRel(e[t-1].getEl(),"bc-tc")}))}(n)},getArgs:function(n){return n.settings}}}function qg(e,o){var r=null;return{cancel:function(){null!==r&&(H.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==r&&H.clearTimeout(r),r=H.setTimeout(function(){e.apply(null,n),r=null},o)}}}function Kg(n,t,e,o,r){var i=new Bp(t,r||n.getRoot());return Ap(n,t,on.some(e),o,i.prev,on.none())}function Jg(t,e){return Mp(Be.fromDom(t.selection.getNode())).getOrThunk(function(){var n=Be.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());return Ni(n,Be.fromDom(e.extractContents())),e.insertNode(n.dom()),hr(n).each(function(n){return n.dom().normalize()}),Fc(n).map(function(n){t.selection.setCursorLocation(n.dom(),function(n){return"img"===qo(n)?1:Dc(n).fold(function(){return br(n).length},function(n){return n.length})}(n))}),n})}function $g(n,t){return n.toString().substring(t.length).replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function Qg(n,u,a,c){return void 0===c&&(c=0),function(n){return n.collapsed&&3===n.startContainer.nodeType}(u)?Kg(n,u.startContainer,u.startOffset,function(e,o,r,n){var i=n.getOr(r.length);return function(n,t,e,o){var r;for(r=t-1;0<=r;r--){var i=n.charAt(r);if(Fp.test(i))return on.none();if(i===e)break}return-1===r||t-r<o?on.none():on.some(n.substring(r+1,t))}(r,i,a,1).fold(function(){return r.match(Fp)?e.abort():e.kontinue()},function(n){var t=u.cloneRange();return t.setStart(o,i-n.length-1),t.setEnd(u.endContainer,u.endOffset),r.length<c?e.abort():e.finish({text:$g(t,a),range:t,triggerChar:a})})}).fold(on.none,on.none,on.some):on.none()}function Zg(e,n,o,t){return void 0===t&&(t=0),Mp(Be.fromDom(n.startContainer)).fold(function(){return Qg(e,n,o,t)},function(n){var t=e.createRng();return t.selectNode(n.dom()),on.some({range:t,text:$g(t,o),triggerChar:o})})}function np(n,t){return{element:n,offset:t}}function tp(t,e){var n=e(),o=t.selection.getRng();return function(t,e,n){return Bu(n.triggerChars,function(n){return Zg(t,e,n)})}(t.dom,o,n).bind(function(n){return Hp(t,e,n)})}function ep(n){var t=n.ui.registry.getAll().popups,e=L(t,function(n){return function(n){return tt("Autocompleter",Lp,n)}(n).fold(function(n){throw new Error(be(n))},function(n){return n})}),o=function(n){var t={};return bn(n,function(n){t[n]={}}),wn(t)}(On(e,function(n){return n.ch})),r=I(e);return{dataset:e,triggerChars:o,lookupByChar:function(t){return S(r,function(n){return n.ch===t})}}}function op(n,o,t){var r=Pc(n.element(),"."+t);if(0<r.length){var e=T(r,function(n){var t=n.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(t-e)>o}).getOr(r.length);return on.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return on.none()}function rp(n,t){return va([qd(n,t)])}function ip(n,t,e){n.getSystem().broadcastOn([$p],{})}function up(n){var t=Be.fromHtml(n),e=br(t),o=function(n){var t=n.dom().attributes!==undefined?n.dom().attributes:[];return k(t,function(n,t){var e;return"class"===t.name?n:P(P({},n),((e={})[t.name]=t.value,e))},{})}(t),r=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(t),i=0===e.length?{}:{innerHtml:Tr(t)};return P({tag:qo(t),classes:r,attributes:o},i)}function ap(n){return Nn(uh,n).getOr(oh)}function cp(n){return{dom:{tag:"div",classes:[ch],innerHtml:n}}}function sp(n){return{dom:{tag:"div",classes:[sh]},components:[ki(eh.translate(n))]}}function fp(n,t){return{dom:{tag:"div",classes:[sh]},components:[{dom:{tag:n.tag,attributes:{style:n.styleAttr}},components:[ki(eh.translate(t))]}]}}function lp(n){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:mh(n)}}}function dp(n){return{dom:{tag:"div",classes:[ch,"tox-collection__item-checkmark"],innerHtml:vm("checkmark",n)}}}function mp(n,t,e,o,r){var i=e?n.checkMark.orThunk(function(){return t.or(on.some("")).map(cp)}):on.none(),u=n.ariaLabel.map(function(n){return{attributes:{title:eh.translate(n)}}}).getOr({});return{dom:_n({tag:"div",classes:[oh,rh].concat(r?["tox-collection__item-icon-rtl"]:[])},u),optComponents:[i,n.htmlContent.fold(function(){return n.textContent.map(o)},function(n){return on.some(function(n){return{dom:{tag:"div",classes:[sh],innerHtml:n}}}(n))}),n.shortcutContent.map(lp),n.caret]}}function gp(n,t,e,o){void 0===o&&(o=on.none());var r=eh.isRtl()&&n.iconContent.exists(function(n){return vn(ph,n)}),i=n.iconContent.map(function(n){return eh.isRtl()&&vn(gh,n)?n+"-rtl":n}).map(function(n){return function(n,t,e){return on.from(t()[n]).or(e).getOrThunk(function(){return hm(t)})}(n,t.icons,o)}),u=on.from(n.meta).fold(function(){return sp},function(n){return Tn(n,"style")?d(fp,n.style):sp});return"color"===n.presets?function(n,t,e,o){var r,i,u;return{dom:(r=ih,i=e.getOr(""),u=n.map(function(n){return' title="'+o.translate(n)+'"'}).getOr(""),up("custom"===t?'<button class="'+r+' tox-swatches__picker-btn"'+u+">"+i+"</button>":"remove"===t?'<div class="'+r+' tox-swatch--remove"'+u+">"+i+"</div>":'<div class="'+r+'" style="background-color: '+t+'" data-mce-color="'+t+'"'+u+"></div>")),optComponents:[]}}(n.ariaLabel,n.value,i,t):mp(n,i,e,u,r)}function pp(n,t,e){t.disabled&&vh(n,t)}function hp(n,t){return!0===t.useNative&&vn(hh,qo(n.element()))}function vp(n){Dr(n.element(),"disabled","disabled")}function bp(n){Mr(n.element(),"disabled")}function yp(n){Dr(n.element(),"aria-disabled","true")}function xp(n){Dr(n.element(),"aria-disabled","false")}function wp(t,n,e){n.disableClass.each(function(n){ei(t.element(),n)}),(hp(t,n)?bp:xp)(t),n.onEnabled(t)}function Sp(n,t){return hp(n,t)?function(n){return Ar(n.element(),"disabled")}(n):function(n){return"true"===_r(n.element(),"aria-disabled")}(n)}function Cp(n,t){var e=n.getApi(t);return function(n){n(e)}}function kp(e,o){return Fi(function(n){Cp(e,n)(function(n){var t=e.onSetup(n);null!==t&&t!==undefined&&o.set(t)})})}function Op(t,e){return Ii(function(n){return Cp(t,n)(e.get())})}var Tp,Ep,Bp=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),Dp=En([{aborted:[]},{edge:["element"]},{success:["info"]}]),_p=En([{abort:[]},{kontinue:[]},{finish:["info"]}]),Ap=function(t,e,n,o,r,i){function u(){return i.fold(Dp.aborted,Dp.edge)}function a(){var n=r();return n?Ap(t,n,on.none(),o,r,on.some(e)):u()}if(function(n,t){return n.isBlock(t)||vn(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===n.getContentEditable(t)}(t,e))return u();if(function(n){return n.nodeType===H.Node.TEXT_NODE}(e)){var c=e.textContent;return o(_p,e,c,n).fold(Dp.aborted,function(){return a()},Dp.success)}return a()},Mp=function(n){return ku(n,"[data-mce-autocompleter]")},Fp=/[\u00a0 \t\r\n]/,Ip=function(e,n){n.on("keypress compositionend",e.onKeypress.throttle),n.on("remove",e.onKeypress.cancel);function o(n,t){Yt(n,lo(),{raw:t})}n.on("keydown",function(t){function n(){return e.getView().bind(ud.getHighlighted)}8===t.which&&e.onKeypress.throttle(t),e.isActive()&&(27===t.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===t.which?(n().each(qt),t.preventDefault()):40===t.which?(n().fold(function(){e.getView().each(ud.highlightFirst)},function(n){o(n,t)}),t.preventDefault(),t.stopImmediatePropagation()):37!==t.which&&38!==t.which&&39!==t.which||n().each(function(n){o(n,t),t.preventDefault(),t.stopImmediatePropagation()}):13!==t.which&&38!==t.which&&40!==t.which||e.cancelIfNecessary())}),n.on("NodeChange",function(n){e.isActive()&&!e.isProcessingAction()&&Mp(Be.fromDom(n.element)).isNone()&&e.cancelIfNecessary()})},Rp=tinymce.util.Tools.resolve("tinymce.util.Promise"),Vp=function(n){if(function(n){return n.nodeType===H.Node.TEXT_NODE}(n))return np(n,n.data.length);var t=n.childNodes;return 0<t.length?Vp(t[t.length-1]):np(n,t.length)},Np=function(n,t){var e=n.childNodes;return 0<e.length&&t<e.length?Np(e[t],0):0<e.length&&function(n){return n.nodeType===H.Node.ELEMENT_NODE}(n)&&e.length===t?Vp(e[e.length-1]):np(n,t)},Hp=function(t,n,e,o){void 0===o&&(o={});var r=n(),i=t.selection.getRng().startContainer.nodeValue,u=S(r.lookupByChar(e.triggerChar),function(n){return e.text.length>=n.minChars&&n.matches.getOrThunk(function(){return function(e){function o(n,t,e,o){var r=o.getOr(e.length);return 0===r?n.kontinue():n.finish(/\s/.test(e.charAt(r-1)))}return function(n){var t=Np(n.startContainer,n.startOffset);return Kg(e,t.element,t.offset,o).fold(nn(!0),nn(!0),l)}}(t.dom)})(e.range,i,e.text)});if(0===u.length)return on.none();var a=Rp.all(w(u,function(t){return t.fetch(e.text,t.maxResults,o).then(function(n){return{matchText:e.text,items:n,columns:t.columns,onAction:t.onAction}})}));return on.some({lookupData:a,context:e})},Pp=le([ft("type"),yt("text")]),zp=le([_t("type",function(){return"autocompleteitem"}),_t("active",function(){return!1}),_t("disabled",function(){return!1}),St("meta",{}),ft("value"),yt("text"),yt("icon")]),Lp=le([ft("type"),ft("ch"),kt("minChars",1),St("columns",1),kt("maxResults",10),xt("matches"),dt("fetch"),dt("onAction")]),jp=[Et("disabled",!1),yt("text"),yt("shortcut"),pe("value","value",In(function(){return Vr("menuitem-value")}),ye()),St("meta",{})],Up=le([ft("type"),Bt("onSetup",function(){return Z}),Bt("onAction",Z),yt("icon")].concat(jp)),Wp=le([ft("type"),dt("getSubmenuItems"),Bt("onSetup",function(){return Z}),yt("icon")].concat(jp)),Gp=le([ft("type"),Et("active",!1),Bt("onSetup",function(){return Z}),dt("onAction")].concat(jp)),Xp=le([ft("type"),Et("active",!1),yt("icon")].concat(jp)),Yp=le([ft("type"),lt("fancytype",["inserttable","colorswatch"]),Bt("onAction",Z)]),qp=function(n){return rp(Vr("unnamed-events"),n)},Kp=[ct("lazySink"),ct("tooltipDom"),St("exclusive",!0),St("tooltipComponents",[]),St("delay",300),Tt("mode","normal",["normal","follow-highlight"]),St("anchor",function(n){return{anchor:"hotspot",hotspot:n,layouts:{onLtr:nn([oc,ec,ia,aa,ua,ca]),onRtl:nn([oc,ec,ia,aa,ua,ca])}}}),Yu("onHide"),Yu("onShow")],Jp=/* */Object.freeze({init:function(){function e(){o.get().each(function(n){H.clearTimeout(n)})}var o=Te(on.none()),t=Te(on.none()),n=nn("not-implemented");return Zi({getTooltip:function(){return t.get()},isShowing:function(){return t.get().isSome()},setTooltip:function(n){t.set(on.some(n))},clearTooltip:function(){t.set(on.none())},clearTimer:e,resetTimer:function(n,t){e(),o.set(on.some(H.setTimeout(function(){n()},t)))},readState:n})}}),$p=Vr("tooltip.exclusive"),Qp=Vr("tooltip.show"),Zp=Vr("tooltip.hide"),nh=/* */Object.freeze({hideAllExclusive:ip,setComponents:function(n,t,e,o){e.getTooltip().each(function(n){n.getSystem().isConnected()&&lg.set(n,o)})}}),th=ba({fields:Kp,name:"tooltipping",active:/* */Object.freeze({events:function(o,r){function e(t){r.getTooltip().each(function(n){vs(n),o.onHide(t,n),r.clearTooltip()}),r.clearTimer()}return nr(z([[or(Qp,function(n){r.resetTimer(function(){!function(t){if(!r.isShowing()){ip(t);var n=o.lazySink(t).getOrDie(),e=t.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:nr("normal"===o.mode?[or(co(),function(n){Xt(t,Qp)}),or(uo(),function(n){Xt(t,Zp)})]:[]),behaviours:va([lg.config({})])});r.setTooltip(e),ps(n,e),o.onShow(t,e),Df.position(n,o.anchor(t),e)}}(n)},o.delay)}),or(Zp,function(n){r.resetTimer(function(){e(n)},o.delay)}),or(Co(),function(n,t){vn(t.channels(),$p)&&e(n)}),Ii(function(n){e(n)})],"normal"===o.mode?[or(so(),function(n){Xt(n,Qp)}),or(wo(),function(n){Xt(n,Zp)}),or(co(),function(n){Xt(n,Qp)}),or(uo(),function(n){Xt(n,Zp)})]:[or(Wo(),function(n,t){Xt(n,Qp)}),or(Go(),function(n){Xt(n,Zp)})]]))}}),state:Jp,apis:nh}),eh=tinymce.util.Tools.resolve("tinymce.util.I18n"),oh="tox-menu-nav__js",rh="tox-collection__item",ih="tox-swatch",uh={normal:oh,color:ih},ah="tox-collection__item--enabled",ch="tox-collection__item-icon",sh="tox-collection__item-label",fh="tox-collection__item-caret",lh="tox-collection__item--active",dh=tinymce.util.Tools.resolve("tinymce.Env"),mh=function(n){var e=dh.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},t=n.split("+"),o=w(t,function(n){var t=n.toLowerCase().trim();return Tn(e,t)?e[t]:n});return dh.mac?o.join(""):o.join("+")},gh=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],ph=["list-bull-circle","list-bull-default","list-bull-square"],hh=["input","button","textarea","select"],vh=function(t,n,e){n.disableClass.each(function(n){ni(t.element(),n)}),(hp(t,n)?vp:yp)(t),n.onDisabled(t)},bh=/* */Object.freeze({enable:wp,disable:vh,isDisabled:Sp,onLoad:pp,set:function(n,t,e,o){(o?vh:wp)(n,t,e)}}),yh=/* */Object.freeze({exhibit:function(n,t,e){return Ur({classes:t.disabled?t.disableClass.map(A).getOr([]):[]})},events:function(e,n){return nr([tr(ko(),function(n,t){return Sp(n,e)}),ma(e,n,pp)])}}),xh=[St("disabled",!1),St("useNative",!0),ht("disableClass"),Yu("onDisabled"),Yu("onEnabled")],wh=ba({fields:xh,name:"disabling",active:yh,apis:bh}),Sh=function(n){return wh.config({disabled:n,disableClass:"tox-collection__item--state-disabled"})},Ch=function(n){return wh.config({disabled:n})},kh=function(n){return wh.config({disabled:n,disableClass:"tox-tbtn--disabled"})},Oh=function(n){return wh.config({disabled:n,disableClass:"tox-tbtn--disabled",useNative:!1})};(Ep=Tp=Tp||{})[Ep.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Ep[Ep.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";function Th(n){return E(n,function(n){return n.toArray()})}function Eh(n,t,e){var o=Te(Z);return{type:"item",dom:t.dom,components:Th(t.optComponents),data:n.data,eventOrder:Mh,hasSubmenu:n.triggersSubmenu,itemBehaviours:va([qd("item-events",[function(e,o){return Vi(function(n,t){Cp(e,n)(e.onAction),e.triggersSubmenu||o!==Ah.CLOSE_ON_EXECUTE||(Xt(n,Do()),t.stop())})}(n,e),kp(n,o),Op(n,o)]),Sh(n.disabled),lg.config({})].concat(n.itemBehaviours))}}function Bh(n){return{value:n.value,meta:_n({text:n.text.getOr("")},n.meta)}}function Dh(n,t){var e=function(n){return Fh.DOM.encode(n)}(eh.translate(n));if(0<t.length){var o=new RegExp(function(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(t),"gi");return e.replace(o,function(n){return'<span class="tox-autocompleter-highlight">'+n+"</span>"})}return e}function _h(t,e,n){function o(n){return Yt(n,Vh,{row:t,col:e})}var r;return iu({dom:{tag:"div",attributes:(r={role:"button"},r["aria-labelledby"]=n,r)},behaviours:va([qd("insert-table-picker-cell",[or(co(),pg.focus),or(ko(),o),or(Eo(),o)]),wg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),pg.config({onFocus:function(n){return Yt(n,Rh,{row:t,col:e})}})])})}var Ah=Tp,Mh={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},Fh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Ih=nn(Ls("item-widget",Bg())),Rh=Vr("cell-over"),Vh=Vr("cell-execute");function Nh(n){return{value:nn(n)}}function Hh(n){return qh.test(n)||Kh.test(n)}function Ph(n){var t=function(n){var t=n.value().replace(qh,function(n,t,e,o){return t+t+e+e+o+o});return{value:nn(t)}}(n),e=Kh.exec(t.value());return null===e?["FFFFFF","FF","FF","FF"]:e}function zh(n){var t=n.toString(16);return 1===t.length?"0"+t:t}function Lh(n){var t=zh(n.red())+zh(n.green())+zh(n.blue());return Nh(t)}function jh(n,t,e,o){return{red:nn(n),green:nn(t),blue:nn(e),alpha:nn(o)}}function Uh(n){var t=parseInt(n,10);return t.toString()===n&&0<=t&&t<=255}function Wh(n){var t,e,o,r=(n.hue()||0)%360,i=n.saturation()/100,u=n.value()/100;if(i=$h(0,Jh(i,1)),u=$h(0,Jh(u,1)),0===i)return t=e=o=Qh(255*u),jh(t,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),f=u-c;switch(Math.floor(a)){case 0:t=c,e=s,o=0;break;case 1:t=s,e=c,o=0;break;case 2:t=0,e=c,o=s;break;case 3:t=0,e=s,o=c;break;case 4:t=s,e=0,o=c;break;case 5:t=c,e=0,o=s;break;default:t=e=o=0}return t=Qh(255*(t+f)),e=Qh(255*(e+f)),o=Qh(255*(o+f)),jh(t,e,o,1)}function Gh(n){var t=Ph(n),e=parseInt(t[1],16),o=parseInt(t[2],16),r=parseInt(t[3],16);return jh(e,o,r,1)}function Xh(n,t,e,o){var r=parseInt(n,10),i=parseInt(t,10),u=parseInt(e,10),a=parseFloat(o);return jh(r,i,u,a)}function Yh(n){return"rgba("+n.red()+","+n.green()+","+n.blue()+","+n.alpha()+")"}var qh=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Kh=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Jh=Math.min,$h=Math.max,Qh=Math.round,Zh=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,nv=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,tv=nn(jh(255,0,0,1)),ev=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),ov="tinymce-custom-colors";function rv(n){var t=[],u=H.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(n,t){var e=t/255;return("0"+Math.round(n*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(n){if(/^[0-9A-Fa-f]{6}$/.test(n))return"#"+n.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=n,a.fillRect(0,0,1,1);var t=a.getImageData(0,0,1,1).data,e=t[0],o=t[1],r=t[2],i=t[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<n.length;o+=2)t.push({text:n[o+1],value:e(n[o]),type:"choiceitem"});return t}function iv(n){return n.getParam("color_map")}function uv(n,e){var o;return n.dom.getParents(n.selection.getStart(),function(n){var t;(t=n.style["forecolor"===e?"color":"background-color"])&&(o=o||t)}),o}function av(n){return Math.max(5,Math.ceil(Math.sqrt(n)))}function cv(n){var t=Mv(n),e=av(t.length);return _v(n,e)}function sv(t,e,n,o){"custom"===n?zv(t)(function(n){n.each(function(n){Iv(n),t.execCommand("mceApplyTextcolor",e,n),o(n)})},"#000000"):"remove"===n?(o(""),t.execCommand("mceRemoveTextcolor",e)):(o(n),t.execCommand("mceApplyTextcolor",e,n))}function fv(n,t){return n.concat(Fv().concat(function(n){var t="choiceitem",e={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return n?[e,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}(t)))}function lv(t,e){return function(n){n(fv(t,e))}}function dv(n,t,e){var o,r;o="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,n.setIconFill(o,r),n.setIconStroke(o,r)}function mv(o,e,r,n,i){o.ui.registry.addSplitButton(e,{tooltip:n,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){return on.from(uv(o,r)).bind(function(n){return function(n){if("transparent"===n)return on.some(jh(0,0,0,0));var t=Zh.exec(n);if(null!==t)return on.some(Xh(t[1],t[2],t[3],"1"));var e=nv.exec(n);return null!==e?on.some(Xh(e[1],e[2],e[3],e[4])):on.none()}(n).map(function(n){var t=Lh(n).value();return Vt(e.toLowerCase(),t)})}).getOr(!1)},columns:cv(o),fetch:lv(Mv(o),Av(o)),onAction:function(n){null!==i.get()&&sv(o,r,i.get(),function(){})},onItemAction:function(n,t){sv(o,r,t,function(n){i.set(n),Pv(o,{name:e,color:n})})},onSetup:function(t){null!==i.get()&&dv(t,e,i.get());function n(n){n.name===e&&dv(t,n.name,n.color)}return o.on("TextColorChange",n),function(){o.off("TextColorChange",n)}}})}function gv(t,n,e,o){t.ui.registry.addNestedMenuItem(n,{text:o,icon:"forecolor"===n?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(n){sv(t,e,n.value,Z)}}]}})}function pv(e,o){return function(n){var t=x(n,o);return w(t,function(n){return{dom:e,components:n}})}}function hv(n,e){var o=[],r=[];return bn(n,function(n,t){e(n,t)?(0<r.length&&o.push(r),r=[],Tn(n.dom,"innerHtml")&&r.push(n)):r.push(n)}),0<r.length&&o.push(r),w(o,function(n){return{dom:{tag:"div",classes:["tox-collection__group"]},components:n}})}function vv(t,e,n){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===t?["tox-collection--list"]:["tox-collection--grid"])},components:[Ig.parts().items({preprocess:function(n){return"auto"!==t&&1<t?pv({tag:"div",classes:["tox-collection__group"]},t)(n):hv(n,function(n,t){return"separator"===e[t].type})}})]}}function bv(n){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:function(n){return"color"===n?"tox-swatches":"tox-menu"}(n),tieredMenu:"tox-tiered-menu"}}function yv(n){var t=bv(n);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:ap(n)}}function xv(n,t,e){var o=bv(e);return{dom:{tag:"div",classes:z([[o.tieredMenu]])},markers:yv(e)}}function wv(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function Sv(n){return H.console.error(be(n)),H.console.log(n),on.none()}function Cv(n,t,e,o,r){var i=function(e){return{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Ig.parts().items({preprocess:function(n){return hv(n,function(n,t){return"separator"===e[t].type})}})]}}(e);return{value:n,dom:i.dom,components:i.components,items:e}}function kv(n,t,e,o,r){var i;return"color"===r?{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Ig.parts().items({preprocess:"auto"!==n?pv({tag:"div",classes:["tox-swatches__row"]},n):l})]}]}}(o)).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:n,dom:(i=vv(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:n,dom:(i=vv(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:n,dom:(i=vv(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:n,dom:function(n,t,e){var o=bv(e);return{tag:"div",classes:z([[o.menu,"tox-menu-"+t+"-column"],n?[o.hasIcons]:[]])}}(t,o,r),components:jv,items:e}:{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Ig.parts().items({preprocess:pv({tag:"div",classes:["tox-collection__group"]},n)})]}}(o)).dom,components:i.components,items:e}}function Ov(n,t,e,o,r,i,u,a){var c=function(n){return y(n,wv)}(t),s=Uv(t,e,o,"color"!==r?"normal":"color",i,u,a);return kv(n,c,s,o,r)}function Tv(n,t){var e=yv(t);return 1===n?{mode:"menu",moveOnTab:!0}:"auto"===n?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}}var Ev="choiceitem",Bv=[{type:Ev,text:"Light Green",value:"#BFEDD2"},{type:Ev,text:"Light Yellow",value:"#FBEEB8"},{type:Ev,text:"Light Red",value:"#F8CAC6"},{type:Ev,text:"Light Purple",value:"#ECCAFA"},{type:Ev,text:"Light Blue",value:"#C2E0F4"},{type:Ev,text:"Green",value:"#2DC26B"},{type:Ev,text:"Yellow",value:"#F1C40F"},{type:Ev,text:"Red",value:"#E03E2D"},{type:Ev,text:"Purple",value:"#B96AD9"},{type:Ev,text:"Blue",value:"#3598DB"},{type:Ev,text:"Dark Turquoise",value:"#169179"},{type:Ev,text:"Orange",value:"#E67E23"},{type:Ev,text:"Dark Red",value:"#BA372A"},{type:Ev,text:"Dark Purple",value:"#843FA1"},{type:Ev,text:"Dark Blue",value:"#236FA1"},{type:Ev,text:"Light Gray",value:"#ECF0F1"},{type:Ev,text:"Medium Gray",value:"#CED4D9"},{type:Ev,text:"Gray",value:"#95A5A6"},{type:Ev,text:"Dark Gray",value:"#7E8C8D"},{type:Ev,text:"Navy Blue",value:"#34495E"},{type:Ev,text:"Black",value:"#000000"},{type:Ev,text:"White",value:"#ffffff"}],Dv=function GF(t){void 0===t&&(t=10);var n,e=ev.getItem(ov),o=cn(e)?JSON.parse(e):[],r=t-(n=o).length<0?n.slice(0,t):n,i=function(n){r.splice(n,1)};return{add:function(n){(function(n,t){var e=b(n,t);return-1===e?on.none():on.some(e)})(r,n).each(i),r.unshift(n),r.length>t&&r.pop(),ev.setItem(ov,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),_v=function(n,t){return n.getParam("color_cols",t,"number")},Av=function(n){return!1!==n.getParam("custom_colors")},Mv=function(n){var t=iv(n);return t!==undefined?rv(t):Bv},Fv=function(){return w(Dv.state(),function(n){return{type:Ev,text:n,value:n}})},Iv=function(n){Dv.add(n)},Rv=function(n){return n.fire("SkinLoaded")},Vv=function(n){return n.fire("ResizeEditor")},Nv=function(n,t){return n.fire("ScrollContent",t)},Hv=function(n,t){return n.fire("ResizeContent",t)},Pv=function(n,t){return n.fire("TextColorChange",t)},zv=function(i){return function(n,t){var e,o={colorpicker:t},r=(e=n,function(n){var t=n.getData();e(on.from(t.colorpicker)),n.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(n,t){"hex-valid"===t.name&&(t.value?n.enable("ok"):n.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){n(on.none())}})}},Lv={register:function(n){!function(e){e.addCommand("mceApplyTextcolor",function(n,t){!function(n,t,e){n.undoManager.transact(function(){n.focus(),n.formatter.apply(t,{value:e}),n.nodeChanged()})}(e,n,t)}),e.addCommand("mceRemoveTextcolor",function(n){!function(n,t){n.undoManager.transact(function(){n.focus(),n.formatter.remove(t,{value:null},null,!0),n.nodeChanged()})}(e,n)})}(n);var t=Te(null),e=Te(null);mv(n,"forecolor","forecolor","Text color",t),mv(n,"backcolor","hilitecolor","Background color",e),gv(n,"forecolor","forecolor","Text color"),gv(n,"backcolor","hilitecolor","Background color")},getColors:fv,getFetch:lv,colorPickerDialog:zv,getCurrentColor:uv,getColorCols:cv,calcCols:av},jv=[Ig.parts().items({})],Uv=function(n,e,o,r,i,u,a){return Eu(w(n,function(t){return"choiceitem"===t.type?function(n){return tt("choicemenuitem",Xp,n)}(t).fold(Sv,function(n){return on.some(function(t,n,e,o,r,i,u){var a=gp({presets:e,textContent:n?t.text:on.none(),htmlContent:on.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:n?t.shortcut:on.none(),checkMark:n?on.some(dp(u.icons)):on.none(),caret:on.none(),value:t.value},u,!0);return Dn(Eh({data:Bh(t),disabled:t.disabled,getApi:function(t){return{setActive:function(n){wg.set(t,n)},isActive:function(){return wg.isOn(t)},isDisabled:function(){return wh.isDisabled(t)},setDisabled:function(n){return wh.set(t,n)}}},onAction:function(n){return o(t.value)},onSetup:function(n){return n.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},a,i),{toggling:{toggleClass:ah,toggleOnExecute:!1,selected:t.active}})}(n,1===o,r,e,u(t.value),i,a))}):on.none()}))};var Wv,Gv,Xv={inserttable:function XF(o){var n=Vr("size-label"),i=function(n,t,e){for(var o=[],r=0;r<t;r++){for(var i=[],u=0;u<e;u++)i.push(_h(r,u,n));o.push(i)}return o}(n,10,10),u=pm({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:n}},components:[ki("0x0")],behaviours:va([lg.config({})])});return{type:"widget",data:{value:Vr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Ih().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:function(n){return E(n,function(n){return w(n,uu)})}(i).concat(u.asSpec()),behaviours:va([qd("insert-table-picker",[ar(Rh,function(n,t,e){var o=e.event().row(),r=e.event().col();!function(n,t,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)wg.set(n[i][u],i<=t&&u<=e)}(i,o,r,10,10),lg.set(u.get(n),[function(n,t){return ki(t+1+"x"+(n+1))}(o,r)])}),ar(Vh,function(n,t,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),Xt(n,Do())})]),sg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function YF(t,n){var e=Lv.getColors(n.colorinput.getColors(),n.colorinput.hasCustomColors()),o=n.colorinput.getColorCols(),r=Ov(Vr("menu-value"),e,function(n){t.onAction({value:n})},o,"color",Ah.CLOSE_ON_EXECUTE,function(){return!1},n.shared.providers),i=Dn(P(P({},r),{markers:yv("color"),movement:Tv(o,"color")}));return{type:"widget",data:{value:Vr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Ih().widget(Ig.sketch(i))]}}},Yv=function(t,e,n,o,r,i,u,a){void 0===a&&(a=!0);var c=gp({presets:o,textContent:on.none(),htmlContent:n?t.text.map(function(n){return Dh(n,e)}):on.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:on.none(),checkMark:on.none(),caret:on.none(),value:t.value},u.providers,a,t.icon);return Eh({data:Bh(t),disabled:t.disabled,getApi:function(){return{}},onAction:function(n){return r(t.value,t.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:function(n,t){return R(n,"tooltipWorker").map(function(e){return[th.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(n){return{anchor:"submenu",item:n,overrides:{maxHeightFunction:Sf}}},mode:"follow-highlight",onShow:function(t,n){e(function(n){th.setComponents(t,[ou({element:Be.fromDom(n)})])})}})]}).getOr([])}(t.meta,u)},c,i)},qv=function(n){var t=n.text.fold(function(){return{}},function(n){return{innerHtml:n}});return{type:"separator",dom:P({tag:"div",classes:[rh,"tox-collection__group-heading"]},t),components:[]}},Kv=function(n,t,e,o){void 0===o&&(o=!0);var r=gp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,caret:on.none(),checkMark:on.none(),shortcutContent:n.shortcut},e,o);return Eh({data:Bh(n),getApi:function(t){return{isDisabled:function(){return wh.isDisabled(t)},setDisabled:function(n){return wh.set(t,n)}}},disabled:n.disabled,onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t)},Jv=function(n,t,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i=r?function(n){return{dom:{tag:"div",classes:[fh],innerHtml:vm("chevron-down",n)}}}(e.icons):function(n){return{dom:{tag:"div",classes:[fh],innerHtml:vm("chevron-right",n)}}}(e.icons),u=gp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,caret:on.some(i),checkMark:on.none(),shortcutContent:n.shortcut},e,o);return Eh({data:Bh(n),getApi:function(t){return{isDisabled:function(){return wh.isDisabled(t)},setDisabled:function(n){return wh.set(t,n)}}},disabled:n.disabled,onAction:Z,onSetup:n.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,t)},$v=function(n,t,e){var o=gp({iconContent:on.none(),textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,checkMark:on.some(dp(e.icons)),caret:on.none(),shortcutContent:n.shortcut,presets:"normal",meta:n.meta},e,!0);return Dn(Eh({data:Bh(n),disabled:n.disabled,getApi:function(t){return{setActive:function(n){wg.set(t,n)},isActive:function(){return wg.isOn(t)},isDisabled:function(){return wh.isDisabled(t)},setDisabled:function(n){return wh.set(t,n)}}},onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},o,t),{toggling:{toggleClass:ah,toggleOnExecute:!1,selected:n.active}})},Qv=function(t,e){return function(n,t){return Object.prototype.hasOwnProperty.call(n,t)?on.some(n[t]):on.none()}(Xv,t.fancytype).map(function(n){return n(t,e)})};(Gv=Wv=Wv||{})[Gv.ContentFocus=0]="ContentFocus",Gv[Gv.UiFocus=1]="UiFocus";function Zv(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function nb(n){return y(n,Zv)}function tb(n,t,e,o,r){function i(n){return r?P(P({},n),{shortcut:on.none(),icon:n.text.isSome()?on.none():n.icon}):n}var u=e.shared.providers;switch(n.type){case"menuitem":return function(n){return tt("menuitem",Up,n)}(n).fold(Sv,function(n){return on.some(Kv(i(n),t,u,o))});case"nestedmenuitem":return function(n){return tt("nestedmenuitem",Wp,n)}(n).fold(Sv,function(n){return on.some(Jv(i(n),t,u,o,r))});case"togglemenuitem":return function(n){return tt("togglemenuitem",Gp,n)}(n).fold(Sv,function(n){return on.some($v(i(n),t,u))});case"separator":return function(n){return tt("separatormenuitem",Pp,n)}(n).fold(Sv,function(n){return on.some(qv(n))});case"fancymenuitem":return function(n){return tt("fancymenuitem",Yp,n)}(n).fold(Sv,function(n){return Qv(i(n),e)});default:return H.console.error("Unknown item in general menu",n),on.none()}}function eb(n,t,e,o,r,i){var u=1===o,a=!u||nb(n);return Eu(w(n,function(n){return"separator"===n.type?function(n){return tt("Autocompleter.Separator",Pp,n)}(n).fold(Sv,function(n){return on.some(qv(n))}):function(n){return tt("Autocompleter.Item",zp,n)}(n).fold(Sv,function(n){return on.some(Yv(n,t,u,"normal",e,r,i,a))})}))}function ob(n,t,e,o,r){var i=nb(t),u=Eu(w(t,function(n){function t(n){return tb(n,e,o,function(n){return r?!n.hasOwnProperty("text"):i}(n),r)}return"nestedmenuitem"===n.type&&n.getSubmenuItems().length<=0?t(_n(n,{disabled:!0})):t(n)}));return(r?Cv:kv)(n,i,u,1,"normal")}function rb(n){return Pg.singleData(n.value,n)}function ib(n){function t(){n.stopPropagation()}function e(){n.preventDefault()}var o=Be.fromDom(n.target),r=i(e,t);return function(n,t,e,o,r,i,u){return{target:nn(n),x:nn(t),y:nn(e),stop:o,prevent:r,kill:i,raw:nn(u)}}(o,n.clientX,n.clientY,t,e,r,n)}function ub(n,t,e,o,r){var i=function(t,e){return function(n){t(n)&&e(ib(n))}}(e,o);return n.dom().addEventListener(t,i,r),{unbind:d(lb,n,t,i,r)}}function ab(n,t,e){return function(n,t,e,o){return ub(n,t,e,o,!1)}(n,t,db,e)}function cb(n,t,e){return function(n,t,e,o){return ub(n,t,e,o,!0)}(n,t,db,e)}function sb(n,t,e){return ku(n,t,e).isSome()}var fb=function(u,a){function e(){return s.get().isSome()}function c(){e()&&zg.hide(l)}function i(n,t,e,o){n.matchLength=t.text.length;var r=Bu(e,function(n){return on.from(n.columns)}).getOr(1);zg.showAt(l,{anchor:"node",root:Be.fromDom(u.getBody()),node:on.from(n.element)},Ig.sketch(function(n,t,e,o){var r=e===Wv.ContentFocus?Gl():Wl(),i=Tv(t,o),u=yv(o);return{dom:n.dom,components:n.components,items:n.items,value:n.value,markers:{selectedItem:u.selectedItem,item:u.item},movement:i,fakeFocus:e===Wv.ContentFocus,focusManager:r,menuBehaviours:qp("auto"!==t?[]:[Fi(function(o,n){op(o,4,u.item).each(function(n){var t=n.numColumns,e=n.numRows;sg.setGridSize(o,e,t)})})])}}(kv("autocompleter-value",!0,o,r,"normal"),r,Wv.ContentFocus,"normal"))),zg.getContent(l).each(ud.highlightFirst)}var s=Te(on.none()),f=Te(!1),l=iu(zg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:va([qd("dismissAutocompleter",[or(Ho(),function(){return d()})])]),lazySink:a.getSink})),d=function(){if(e()){var n=s.get().map(function(n){return n.element});Mp(n.getOr(Be.fromDom(u.selection.getNode()))).each(Or),c(),s.set(on.none()),f.set(!1)}},o=N(function(){return ep(u)}),m=function(n){(function(t){return s.get().map(function(n){return Zg(u.dom,u.selection.getRng(),n.triggerChar).bind(function(n){return Hp(u,o,n,t)})}).getOrThunk(function(){return tp(u,o)})})(n).fold(d,function(r){!function(n){if(!e()){var t=Jg(u,n.range);s.set(on.some({triggerChar:n.triggerChar,element:t,matchLength:n.text.length})),f.set(!1)}}(r.context),r.lookupData.then(function(o){s.get().map(function(n){var t=r.context;if(n.triggerChar===t.triggerChar){var e=function(t,n){var e=Bu(n,function(n){return on.from(n.columns)}).getOr(1);return E(n,function(i){var n=i.items;return eb(n,i.matchText,function(o,r){var n=u.selection.getRng();Zg(u.dom,n,t).fold(function(){return H.console.error("Lost context. Cursor probably moved")},function(n){var t=n.range,e={hide:function(){d()},reload:function(n){c(),m(n)}};f.set(!0),i.onAction(e,t,o,r),f.set(!1)})},e,Ah.BUBBLE_TO_SANDBOX,a)})}(t.triggerChar,o);0<e.length?i(n,t,o,e):10<=t.text.length-n.matchLength?d():c()}})})})},n={onKeypress:qg(function(n){27!==n.which&&m()},50),cancelIfNecessary:d,isMenuOpen:function(){return zg.isOpen(l)},isActive:e,isProcessingAction:f.get,getView:function(){return zg.getContent(l)}};Ip(n,u)},lb=function(n,t,e,o){n.dom().removeEventListener(t,e,o)},db=nn(!0),mb=ib;function gb(e,o){var r=null;return{cancel:function(){null!==r&&(H.clearTimeout(r),r=null)},schedule:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];r=H.setTimeout(function(){e.apply(null,n),r=null},o)}}}function pb(n){var t=n.raw();return t.touches===undefined||1!==t.touches.length?on.none():on.some(t.touches[0])}function hb(e){var o=Te(on.none()),r=Te(!1),i=gb(function(n){e.triggerEvent(Bo(),n),r.set(!0)},400),u=K([{key:to(),value:function(e){return pb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:e.target};i.schedule(e),r.set(!1),o.set(on.some(t))}),on.none()}},{key:eo(),value:function(n){return i.cancel(),pb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||o.set(on.none())})}),on.none()}},{key:oo(),value:function(t){i.cancel();return o.get().filter(function(n){return jt(n.target(),t.target())}).map(function(n){return r.get()?(t.prevent(),!1):e.triggerEvent(To(),t)})}}]);return{fireIfReady:function(t,n){return Nn(u,n).bind(function(n){return n(t)})}}}function vb(t,n){var e=ot("Getting GUI events settings",Sb,n),o=Ht().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],r=hb(e),i=w(o.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return ab(t,n,function(t){r.fireIfReady(t,n).each(function(n){n&&t.kill()}),e.triggerEvent(n,t)&&t.kill()})}),u=Te(on.none()),a=ab(t,"paste",function(t){r.fireIfReady(t,"paste").each(function(n){n&&t.kill()}),e.triggerEvent("paste",t)&&t.kill(),u.set(on.some(H.setTimeout(function(){e.triggerEvent(So(),t)},0)))}),c=ab(t,"keydown",function(n){e.triggerEvent("keydown",n)?n.kill():!0===e.stopBackspace&&function(n){return 8===n.raw().which&&!vn(["input","textarea"],qo(n.target()))&&!sb(n.target(),'[contenteditable="true"]')}(n)&&n.prevent()}),s=function(n,t){return wb?cb(n,"focus",t):ab(n,"focusin",t)}(t,function(n){e.triggerEvent("focusin",n)&&n.kill()}),f=Te(on.none()),l=function(n,t){return wb?cb(n,"blur",t):ab(n,"focusout",t)}(t,function(n){e.triggerEvent("focusout",n)&&n.kill(),f.set(on.some(H.setTimeout(function(){e.triggerEvent(wo(),n)},0)))});return{unbind:function(){bn(i,function(n){n.unbind()}),c.unbind(),s.unbind(),l.unbind(),a.unbind(),u.get().each(H.clearTimeout),f.get().each(H.clearTimeout)}}}function bb(n,t){var e=Nn(n,"target").map(function(n){return n()}).getOr(t);return Te(e)}function yb(n,o,t,e,r,i){var u=n(o,e),a=function(n,t){var e=Te(!1),o=Te(!1);return{stop:function(){e.set(!0)},cut:function(){o.set(!0)},isStopped:e.get,isCut:o.get,event:nn(n),setSource:t.set,getSource:t.get}}(t,r);return u.fold(function(){return i.logEventNoHandlers(o,e),Cb.complete()},function(t){var e=t.descHandler();return Wr(e)(a),a.isStopped()?(i.logEventStopped(o,t.element(),e.purpose()),Cb.stopped()):a.isCut()?(i.logEventCut(o,t.element(),e.purpose()),Cb.complete()):hr(t.element()).fold(function(){return i.logNoParent(o,t.element(),e.purpose()),Cb.complete()},function(n){return i.logEventResponse(o,t.element(),e.purpose()),Cb.resume(n)})})}function xb(n,t,e){var o=function(n){var t=Te(!1);return{stop:function(){t.set(!0)},cut:Z,isStopped:t.get,isCut:nn(!1),event:nn(n),setSource:r("Cannot set source of a broadcasted event"),getSource:r("Cannot get source of a broadcasted event")}}(t);return bn(n,function(n){var t=n.descHandler();Wr(t)(o)}),o.isStopped()}var wb=Ht().browser.isFirefox(),Sb=qn([dt("triggerEvent"),St("stopBackspace",!0)]),Cb=En([{stopped:[]},{resume:["element"]},{complete:[]}]),kb=function(t,e,o,n,r,i){return yb(t,e,o,n,r,i).fold(function(){return!0},function(n){return kb(t,e,o,n,r,i)},function(){return!1})},Ob=function(n,t,e,o,r){var i=bb(e,o);return kb(n,t,e,o,i,r)},Tb=fr("element","descHandler"),Eb=function(n,t){return{id:nn(n),descHandler:nn(t)}};function Bb(){var i={};return{registerId:function(o,r,n){Cn(n,function(n,t){var e=i[t]!==undefined?i[t]:{};e[r]=nu(n,o),i[t]=e})},unregisterId:function(e){Cn(i,function(n,t){n.hasOwnProperty(e)&&delete n[e]})},filterByType:function(n){return Nn(i,n).map(function(n){return On(n,function(n,t){return Eb(t,n)})}).getOr([])},find:function(n,t,e){var o=Vn(t)(i);return Mi(e,function(n){return function(e,o){return Yi(o).fold(function(){return on.none()},function(n){var t=Vn(n);return e.bind(t).map(function(n){return Tb(o,n)})})}(o,n)},n)}}}function Db(){function o(n){var t=n.element();return Yi(t).fold(function(){return function(n,t){var e=Vr(Wi+n);return Xi(t,e),e}("uid-",n.element())},function(n){return n})}var r=Bb(),i={},u=function(n){Yi(n.element()).each(function(n){delete i[n],r.unregisterId(n)})};return{find:function(n,t,e){return r.find(n,t,e)},filter:function(n){return r.filterByType(n)},register:function(n){var t=o(n);$(i,t)&&function(n,t){var e=i[t];if(e!==n)throw new Error('The tagId "'+t+'" is already used by: '+Rr(e.element())+"\nCannot use it for: "+Rr(n.element())+"\nThe conflicting element is"+(Jo(e.element())?" ":" not ")+"already in the DOM");u(n)}(n,t);var e=[n];r.registerId(e,t,n.events()),i[t]=n},unregister:u,getById:function(n){return Vn(n)(i)}}}function _b(e){function o(t){return hr(e.element()).fold(function(){return!0},function(n){return jt(t,n)})}function r(n,t){return u.find(o,n,t)}function i(e){var n=u.filter(Co());bn(n,function(n){var t=n.descHandler();Wr(t)(e)})}var u=Db(),n=vb(e.element(),{triggerEvent:function(t,e){return Uu(t,e.target(),function(n){return function(n,t,e,o){var r=e.target();return Ob(n,t,e,r,o)}(r,t,e,n)})}}),a={debugInfo:nn("real"),triggerEvent:function(t,e,o){Uu(t,e,function(n){Ob(r,t,o,e,n)})},triggerFocus:function(t,e){Yi(t).fold(function(){xa(t)},function(n){Uu(xo(),t,function(n){!function(n,t,e,o,r){var i=bb(e,o);yb(n,t,e,o,i,r)}(r,xo(),{originator:nn(e),kill:Z,prevent:Z,target:nn(t)},t,n)})})},triggerEscape:function(n,t){a.triggerEvent("keydown",n.element(),t.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:iu,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){t(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,t){d(n,t)},broadcastEvent:function(n,t){m(n,t)},isConnected:nn(!0)},t=function(n){n.connect(a),Di(n.element())||(u.register(n),bn(n.components(),t),a.triggerEvent(Ao(),n.element(),{target:nn(n.element())}))},c=function(n){Di(n.element())||(bn(n.components(),c),u.unregister(n)),n.disconnect()},s=function(n){ps(e,n)},f=function(n){vs(n)},l=function(n){i({universal:nn(!0),data:nn(n)})},d=function(n,t){i({universal:nn(!1),channels:nn(n),data:nn(t)})},m=function(n,t){var e=u.filter(n);return xb(e,t)},g=function(n){return u.getById(n).fold(function(){return an.error(new Error('Could not find component with uid: "'+n+'" in system.'))},an.value)},p=function(n){var t=Yi(n).getOr("not found");return g(t)};return t(e),{root:nn(e),element:e.element,destroy:function(){n.unbind(),Hi(e.element())},add:s,remove:f,getByUid:g,getByDom:p,addToWorld:t,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}}function Ab(n){return on.from(n.settings.min_width).filter(mn)}function Mb(n){return on.from(n.settings.min_height).filter(mn)}function Fb(n){return!1!==n.getParam("menubar",!0,"boolean")}function Ib(n){var t=n.getParam("toolbar",!0),e=!0===t,o=cn(t),r=fn(t)&&0<t.length;return!zb(n)&&(r||o||e)}function Rb(t){var n=wn(t.settings),e=S(n,function(n){return/^toolbar([1-9])$/.test(n)}),o=w(e,function(n){return t.getParam(n,!1,"string")}),r=S(o,function(n){return"string"==typeof n});return 0<r.length?on.some(r):on.none()}var Vb,Nb,Hb=Bl({name:"Container",factory:function(n){var t=n.dom,e=t.attributes,o=c(t,["attributes"]);return{uid:n.uid,dom:P({tag:"div",attributes:P({role:"presentation"},e)},o),components:n.components,behaviours:Fs(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[St("components",[]),Ms("containerBehaviours",[]),St("events",{}),St("domModification",{}),St("eventOrder",{})]}),Pb=tinymce.util.Tools.resolve("tinymce.EditorManager"),zb=function(n){return Rb(n).fold(function(){return 0<n.getParam("toolbar",[],"string[]").length},function(){return!0})};(Nb=Vb=Vb||{})["default"]="",Nb.floating="floating",Nb.sliding="sliding";function Lb(n){return n.getParam("toolbar_drawer","","string")}function jb(n){var t=function(n){return n.getParam("fixed_toolbar_container","","string")}(n);return 0<t.length&&n.inline?Cu(_i(),t):on.none()}function Ub(n){return n.inline&&jb(n).isSome()}function Wb(n){return n.inline&&!Fb(n)&&!Ib(n)&&!zb(n)}function Gb(n){return(n.getParam("toolbar_sticky",!1,"boolean")||n.inline)&&!Ub(n)&&!Wb(n)}function Xb(n){return va([pg.config({onFocus:!1===n.selectOnFocus?Z:function(n){var t=n.element(),e=hi(t);t.dom().setSelectionRange(0,e.length)}})])}function Yb(n){return{tag:n.tag,attributes:P({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}var qb,Kb,Jb,$b,Qb=Vr("form-component-change"),Zb=Vr("form-close"),ny=Vr("form-cancel"),ty=Vr("form-action"),ey=Vr("form-submit"),oy=Vr("form-block"),ry=Vr("form-unblock"),iy=Vr("form-tabchange"),uy=Vr("form-resize"),ay=nn([St("prefix","form-field"),Ms("fieldBehaviours",[Ql,Qf])]),cy=nn([wl({schema:[ct("dom")],name:"label"}),wl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[ct("text")],name:"aria-descriptor"}),yl({factory:{sketch:function(n){var t=X(n,["factory"]);return n.factory.sketch(t)}},schema:[ct("factory")],name:"field"})]),sy=Dl({name:"FormField",configFields:ay(),partFields:cy(),factory:function(r,n,t,e){var o=Is(r.fieldBehaviours,[Ql.config({find:function(n){return Ys(n,r,"field")}}),Qf.config({store:{mode:"manual",getValue:function(n){return Ql.getCurrent(n).bind(Qf.getValue)},setValue:function(n,t){Ql.getCurrent(n).each(function(n){Qf.setValue(n,t)})}}})]),i=nr([Fi(function(n,t){var o=Ks(n,r,["label","field","aria-descriptor"]);o.field().each(function(e){var t=Vr(r.prefix);o.label().each(function(n){Dr(n.element(),"for",t),Dr(e.element(),"id",t)}),o["aria-descriptor"]().each(function(n){var t=Vr(r.prefix);Dr(n.element(),"id",t),Dr(e.element(),"aria-describedby",t)})})})]),u={getField:function(n){return Ys(n,r,"field")},getLabel:function(n){return Ys(n,r,"label")}};return{uid:r.uid,dom:r.dom,components:n,behaviours:o,events:i,apis:u}},apis:{getField:function(n,t){return n.getField(t)},getLabel:function(n,t){return n.getLabel(t)}}}),fy=nn([ht("data"),St("inputAttributes",{}),St("inputStyles",{}),St("tag","input"),St("inputClasses",[]),Yu("onSetValue"),St("styles",{}),St("eventOrder",{}),Ms("inputBehaviours",[Qf,pg]),St("selectOnFocus",!0)]),ly=Bl({name:"Input",configFields:fy(),factory:function(n,t){return{uid:n.uid,dom:Yb(n),components:[],behaviours:function(n){return P(P({},Xb(n)),Is(n.inputBehaviours,[Qf.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return hi(n.element())},setValue:function(n,t){hi(n.element())!==t&&vi(n.element(),t)}},onSetValue:n.onSetValue})]))}(n),eventOrder:n.eventOrder}}}),dy={},my={exports:dy};qb=undefined,Kb=dy,Jb=my,$b=undefined,function(n){"object"==typeof Kb&&void 0!==Jb?Jb.exports=n():"function"==typeof qb&&qb.amd?qb([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function f(i,u,a){function c(t,n){if(!u[t]){if(!i[t]){var e="function"==typeof $b&&$b;if(!n&&e)return e(t,!0);if(s)return s(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[t]={exports:{}};i[t][0].call(r.exports,function(n){return c(i[t][1][n]||n)},r,r.exports,f,i,u,a)}return u[t].exports}for(var s="function"==typeof $b&&$b,n=0;n<a.length;n++)c(a[n]);return c}({1:[function(n,t,e){var o,r,i=t.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(n){if(o===setTimeout)return setTimeout(n,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(n,0);try{return o(n,0)}catch(t){try{return o.call(null,n,0)}catch(t){return o.call(this,n,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(n){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(n){r=a}}();var s,f=[],l=!1,d=-1;function m(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&g())}function g(){if(!l){var n=c(m);l=!0;for(var t=f.length;t;){for(s=f,f=[];++d<t;)s&&s[d].run();d=-1,t=f.length}s=null,l=!1,function e(n){if(r===clearTimeout)return clearTimeout(n);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(n);try{return r(n)}catch(t){try{return r.call(null,n)}catch(t){return r.call(this,n)}}}(n)}}function p(n,t){this.fun=n,this.array=t}function h(){}i.nextTick=function(n){var t=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)t[e-1]=arguments[e];f.push(new p(n,t)),1!==f.length||l||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,t){(function(t){function o(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],f(n,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){var t;try{t=n(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,t)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void c(n);if("function"==typeof e)return void f(function o(n,t){return function(){n.apply(t,arguments)}}(e,t),n)}n._state=1,n._value=t,c(n)}catch(r){a(n,r)}}function a(n,t){n._state=2,n._value=t,c(n)}function c(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var t=0,e=n._deferreds.length;t<e;t++)r(n,n._deferreds[t]);n._deferreds=null}function s(n,t,e){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.promise=e}function f(n,t){var e=!1;try{n(function(n){e||(e=!0,u(t,n))},function(n){e||(e=!0,a(t,n))})}catch(o){if(e)return;e=!0,a(t,o)}}var n,e;n=this,e=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,t){var e=new this.constructor(o);return r(this,new s(n,t,e)),e},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(n){n(t)})},i.reject=function(e){return new i(function(n,t){t(e)})},i.race=function(r){return new i(function(n,t){for(var e=0,o=r.length;e<o;e++)r[e].then(n,t)})},i._immediateFn="function"==typeof t?function(n){t(n)}:function(n){e(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(c,n,s){(function(n,t){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(n,t){this._id=n,this._clearFn=t}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,t){clearTimeout(n._idleTimeoutId),n._idleTimeout=t},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var t=n._idleTimeout;0<=t&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},t))},s.setImmediate="function"==typeof n?n:function(n){var t=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[t]=!0,o(function(){i[t]&&(e?n.apply(null,e):n.call(null),s.clearImmediate(t))}),t},s.clearImmediate="function"==typeof t?t:function(n){delete i[n]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,t,e){var o=n("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});function gy(n){H.setTimeout(function(){throw n},0)}function py(n){var t=qo(n);return vn(_y,t)}function hy(n,t){var e=t.getRoot(n).getOr(n.element());ei(e,t.invalidClass),t.notify.each(function(t){py(n.element())&&Dr(n.element(),"aria-invalid",!1),t.getContainer(n).each(function(n){Er(n,t.validHtml)}),t.onValid(n)})}function vy(t,n,e,o){var r=n.getRoot(t).getOr(t.element());ni(r,n.invalidClass),n.notify.each(function(n){py(t.element())&&Dr(t.element(),"aria-invalid",!0),n.getContainer(t).each(function(n){Er(n,o)}),n.onInvalid(t,o)})}function by(t,n,e){return n.validator.fold(function(){return Dy(an.value(!0))},function(n){return n.validate(t)})}function yy(t,e,n){return e.notify.each(function(n){n.onValidate(t)}),by(t,e).map(function(n){return t.getSystem().isConnected()?n.fold(function(n){return vy(t,e,0,n),an.error(n)},function(n){return hy(t,e),an.value(n)}):an.error("No longer in system")})}function xy(n,t,e,o){var r=Hy(n,t,e,o);return sy.sketch(r)}function wy(n,t){return sy.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}})}var Sy,Cy,ky=my.exports.boltExport,Oy=function(n){var e=on.none(),t=[],o=function(n){r()?u(n):t.push(n)},r=function(){return e.isSome()},i=function(n){bn(n,u)},u=function(t){e.each(function(n){H.setTimeout(function(){t(n)},0)})};return n(function(n){e=on.some(n),i(t),t=[]}),{get:o,map:function(e){return Oy(function(t){o(function(n){t(e(n))})})},isReady:r}},Ty={nu:Oy,pure:function(t){return Oy(function(n){n(t)})}},Ey=function(e){function n(n){e().then(n,gy)}return{map:function(n){return Ey(function(){return e().then(n)})},bind:function(t){return Ey(function(){return e().then(function(n){return t(n).toPromise()})})},anonBind:function(n){return Ey(function(){return e().then(function(){return n.toPromise()})})},toLazy:function(){return Ty.nu(n)},toCached:function(){var n=null;return Ey(function(){return null===n&&(n=e()),n})},toPromise:e,get:n}},By=function(n){return Ey(function(){return new ky(n)})},Dy=function(n){return Ey(function(){return ky.resolve(n)})},_y=["input","textarea"],Ay=/* */Object.freeze({markValid:hy,markInvalid:vy,query:by,run:yy,isInvalid:function(n,t){var e=t.getRoot(n).getOr(n.element());return oi(e,t.invalidClass)}}),My=/* */Object.freeze({events:function(t,n){return t.validator.map(function(n){return nr([or(n.onEvent,function(n){yy(n,t).get(l)})].concat(n.validateOnLoad?[Fi(function(n){yy(n,t).get(Z)})]:[]))}).getOr({})}}),Fy=[ct("invalidClass"),St("getRoot",on.none),wt("notify",[St("aria","alert"),St("getContainer",on.none),St("validHtml",""),Yu("onValid"),Yu("onInvalid"),Yu("onValidate")]),wt("validator",[ct("validate"),St("onEvent","input"),St("validateOnLoad",!0)])],Iy=ba({fields:Fy,name:"invalidating",active:My,apis:Ay,extra:{validation:function(e){return function(n){var t=Qf.getValue(n);return Dy(e(t))}}}}),Ry=/* */Object.freeze({exhibit:function(n,t){return Ur({attributes:K([{key:t.tabAttr,value:"true"}])})}}),Vy=[St("tabAttr","data-alloy-tabstop")],Ny=ba({fields:Vy,name:"tabstopping",active:Ry}),Hy=function(n,t,e,o){return{dom:Py(e),components:n.toArray().concat([t]),fieldBehaviours:va(o)}},Py=function(n){return{tag:"div",classes:["tox-form__group"].concat(n)}},zy=/* */Object.freeze({getCoupled:function(n,t,e,o){return e.getOrCreate(n,t,o)}}),Ly=[st("others",nt(an.value,ye()))],jy=ba({fields:Ly,name:"coupling",apis:zy,state:/* */Object.freeze({init:function(n){var i={},t=nn({});return Zi({readState:t,getOrCreate:function(e,o,r){var n=wn(o.others);if(n)return Nn(i,r).getOrThunk(function(){var n=Nn(o.others,r).getOrDie("No information found for coupled component: "+r)(e),t=e.getSystem().build(n);return i[r]=t});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(n,null,2))}})}})}),Uy=nn("sink"),Wy=nn(wl({name:Uy(),overrides:nn({dom:{tag:"div"},behaviours:va([Df.config({useFixed:a})]),events:nr([cr(lo()),cr(ro()),cr(ho())])})}));(Cy=Sy=Sy||{})[Cy.HighlightFirst=0]="HighlightFirst",Cy[Cy.HighlightNone=1]="HighlightNone";function Gy(n,t){var e=n.getHotspot(t).getOr(t),o=n.getAnchorOverrides();return n.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(n){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:n}})}function Xy(n,t,e,o,r,i,u){return function(n,t,r,e,i,o,u){var a=function(n,t,e){return(0,n.fetch)(e).map(t)}(n,t,e),c=gw(e,n);return a.map(function(n){return n.bind(function(n){return on.from(Pg.sketch(P(P({},o.menu()),{uid:Nr(""),data:n,highlightImmediately:u===Sy.HighlightFirst,onOpenMenu:function(n,t){var e=c().getOrDie();Df.position(e,r,t),Pf.decloak(i)},onOpenSubmenu:function(n,t,e){var o=c().getOrDie();Df.position(o,{anchor:"submenu",item:t},e),Pf.decloak(i)},onRepositionMenu:function(n,t,e){var o=c().getOrDie();Df.position(o,r,t),bn(e,function(n){Df.position(o,{anchor:"submenu",item:n.triggeringItem},n.triggeredMenu)})},onEscape:function(){return pg.focus(e),Pf.close(i),on.some(!0)}})))})})}(n,t,Gy(n,e),e,o,r,u).map(function(n){return n.fold(function(){Pf.isOpen(o)&&Pf.close(o)},function(n){Pf.cloak(o),Pf.open(o,n),i(o)}),o})}function Yy(n,t,e,o,r,i,u){return Pf.close(o),Dy(o)}function qy(n,t,e,o,r,i){var u=jy.getCoupled(e,"sandbox");return(Pf.isOpen(u)?Yy:Xy)(n,t,e,u,o,r,i)}function Ky(n,t,e){var o=Ql.getCurrent(t).getOr(t),r=du(n.element());e?si(o.element(),"min-width",r+"px"):function(n,t){Ru.set(n,t)}(o.element(),r)}function Jy(n){Pf.getState(n).each(function(n){Pg.repositionMenus(n)})}function $y(o,r,i){var u=Ou(),n=gw(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id(),role:"listbox"}},behaviours:nl(o.sandboxBehaviours,[Qf.config({store:{mode:"memory",initialValue:r}}),Pf.config({onOpen:function(n,t){var e=Gy(o,r);u.link(r.element()),o.matchWidth&&Ky(e.hotspot,t,o.useMinWidth),o.onOpen(e,n,t),i!==undefined&&i.onOpen!==undefined&&i.onOpen(n,t)},onClose:function(n,t){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(n,t)},isPartOf:function(n,t,e){return zu(t,e)||zu(r,e)},getAttachPoint:function(){return n().getOrDie()}}),Ql.config({find:function(n){return Pf.getState(n).bind(function(n){return Ql.getCurrent(n)})}}),fc.config({channels:P(P({},Os({isExtraPart:nn(!1)})),Ts({isExtraPart:nn(!1),doReposition:Jy}))})])}}function Qy(n){var t=jy.getCoupled(n,"sandbox");Jy(t)}function Zy(){return[St("sandboxClasses",[]),Zf("sandboxBehaviours",[Ql,fc,Pf,Qf])]}function nx(e,t,o){function r(n,t){Yt(n,xw,{value:t})}var n=sy.parts().field({factory:ly,inputClasses:["tox-textfield"],onSetValue:function(n){return Iy.run(n).get(function(){})},inputBehaviours:va([Ny.config({}),Iy.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(n){return hr(n.element())},notify:{onValid:function(n){var t=Qf.getValue(n);Yt(n,yw,{color:t})}},validator:{validateOnLoad:!1,validate:function(n){var t=Qf.getValue(n);if(0===t.length)return Dy(an.value(!0));var e=Be.fromTag("span");si(e,"background-color",t);var o=di(e,"background-color").fold(function(){return an.error("blah")},function(n){return an.value(t)});return Dy(o)}}})]),selectOnFocus:!1}),i=e.label.map(function(n){return wy(n,t.providers)}),u=pm(function(e,o){return vw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:va([bw.config({}),Ny.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:o.getSink,fetch:function(t){return By(function(n){return e.fetch(n)}).map(function(n){return on.from(rb(Dn(Ov(Vr("menu-value"),n,function(n){e.onItemAction(t,n)},e.columns,e.presets,Ah.CLOSE_ON_EXECUTE,function(){return!1},o.providers),{movement:Tv(e.columns,e.presets)})))})},parts:{menu:xv(0,0,e.presets)}})}({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:on.some({onRtl:function(){return[ia]},onLtr:function(){return[ua]}}),components:[],fetch:Lv.getFetch(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(n,e){u.getOpt(n).each(function(t){"custom"===e?o.colorPicker(function(n){n.fold(function(){return Xt(t,ww)},function(n){r(t,n),Iv(n)})},"#ffffff"):r(t,"remove"===e?"":e)})}},t));return sy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:i.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[n,u.asSpec()]}]),fieldBehaviours:va([qd("form-field-events",[or(yw,function(n,t){u.getOpt(n).each(function(n){si(n.element(),"background-color",t.event().color())}),Yt(n,Qb,{name:e.name})}),or(xw,function(t,e){sy.getField(t).each(function(n){Qf.setValue(n,e.event().value()),Ql.getCurrent(t).each(pg.focus)})}),or(ww,function(t,n){sy.getField(t).each(function(n){Ql.getCurrent(t).each(pg.focus)})})])])})}function tx(n,t,e){return{hue:nn(n),saturation:nn(t),value:nn(e)}}function ex(n){return wl({name:n+"-edge",overrides:function(o){return o.model.manager.edgeActions[n].fold(function(){return{}},function(e){var n=nr([rr(to(),e,[o])]),t=nr([rr(ro(),e,[o]),rr(io(),function(n,t){t.mouseIsDown.get()&&e(n,t)},[o])]);return{events:Ow?n:t}})}})}function ox(n){var t=n.event().raw();if(Rw){var e=t;return e.touches!==undefined&&1===e.touches.length?on.some(e.touches[0]).map(function(n){return Fu(n.clientX,n.clientY)}):on.none()}var o=t;return o.clientX!==undefined?on.some(o).map(function(n){return Fu(n.clientX,n.clientY)}):on.none()}function rx(n){return n.model.minX}function ix(n){return n.model.minY}function ux(n){return n.model.minX-1}function ax(n){return n.model.minY-1}function cx(n){return n.model.maxX}function sx(n){return n.model.maxY}function fx(n){return n.model.maxX+1}function lx(n){return n.model.maxY+1}function dx(n,t,e){return t(n)-e(n)}function mx(n){return dx(n,cx,rx)}function gx(n){return dx(n,sx,ix)}function px(n){return mx(n)/2}function hx(n){return gx(n)/2}function vx(n){return n.stepSize}function bx(n){return n.snapToGrid}function yx(n){return n.snapStart}function xx(n){return n.rounded}function wx(n,t){return n[t+"-edge"]!==undefined}function Sx(n){return wx(n,"left")}function Cx(n){return wx(n,"right")}function kx(n){return wx(n,"top")}function Ox(n){return wx(n,"bottom")}function Tx(n){return n.model.value.get()}function Ex(n){return{x:nn(n)}}function Bx(n){return{y:nn(n)}}function Dx(n,t){return{x:nn(n),y:nn(t)}}function _x(n,t){Yt(n,Vw(),{value:t})}function Ax(n,t,e,o){return n<t?n:e<n?e:n===t?t-1:Math.max(t,n-o)}function Mx(n,t,e,o){return e<n?n:n<t?t:n===e?e+1:Math.min(e,n+o)}function Fx(n,t,e){return Math.max(t,Math.min(e,n))}function Ix(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.step,u=n.snap,a=n.snapStart,c=n.rounded,s=n.hasMinEdge,f=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=s?t-1:t,p=f?e+1:e;if(r<l)return g;if(d<r)return p;var h=function(n,t,e){return Math.min(e,Math.max(n,t))-t}(r,l,d),v=Fx(h/m*o+t,g,p);return u&&t<=v&&v<=e?function(u,e,a,c,n){return n.fold(function(){var n=u-e,t=Math.round(n/c)*c;return Fx(e+t,e-1,a+1)},function(n){var t=(u-n)%c,e=Math.round(t/c),o=Math.floor((u-n)/c),r=Math.floor((a-n)/c),i=n+Math.min(r,o+e)*c;return Math.max(n,i)})}(v,t,e,i,a):c?Math.round(v):v}function Rx(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,a=n.maxBound,c=n.maxOffset,s=n.centerMinEdge,f=n.centerMaxEdge;return r<t?i?0:s:e<r?u?a:f:(r-t)/o*c}function Vx(n){return n.element().dom().getBoundingClientRect()}function Nx(n,t){return n[t]}function Hx(n){var t=Vx(n);return Nx(t,Nw)}function Px(n){var t=Vx(n);return Nx(t,"right")}function zx(n){var t=Vx(n);return Nx(t,"top")}function Lx(n){var t=Vx(n);return Nx(t,"bottom")}function jx(n){var t=Vx(n);return Nx(t,"width")}function Ux(n){var t=Vx(n);return Nx(t,"height")}function Wx(n,t,e){return(n+t)/2-e}function Gx(n,t){var e=Vx(n),o=Vx(t),r=Nx(e,Nw),i=Nx(e,"right"),u=Nx(o,Nw);return Wx(r,i,u)}function Xx(n,t){var e=Vx(n),o=Vx(t),r=Nx(e,"top"),i=Nx(e,"bottom"),u=Nx(o,"top");return Wx(r,i,u)}function Yx(n,t){Yt(n,Vw(),{value:t})}function qx(n){return{x:nn(n)}}function Kx(n,t,e){var o={min:rx(t),max:cx(t),range:mx(t),value:e,step:vx(t),snap:bx(t),snapStart:yx(t),rounded:xx(t),hasMinEdge:Sx(t),hasMaxEdge:Cx(t),minBound:Hx(n),maxBound:Px(n),screenRange:jx(n)};return Ix(o)}function Jx(e){return function(n,t){return function(n,t,e){var o=(0<n?Mx:Ax)(Tx(e).x(),rx(e),cx(e),vx(e));return Yx(t,qx(o)),on.some(o)}(e,n,t).map(function(){return!0})}}function $x(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=jx(t),u=o.bind(function(n){return on.some(Gx(n,t))}).getOr(0),a=r.bind(function(n){return on.some(Gx(n,t))}).getOr(i),c={min:rx(n),max:cx(n),range:mx(n),value:e,hasMinEdge:Sx(n),hasMaxEdge:Cx(n),minBound:Hx(t),minOffset:0,maxBound:Px(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Rx(c)}(t,i,e,o,r);return Hx(t)-Hx(n)+u}function Qx(n,t){Yt(n,Vw(),{value:t})}function Zx(n){return{y:nn(n)}}function nw(n,t,e){var o={min:ix(t),max:sx(t),range:gx(t),value:e,step:vx(t),snap:bx(t),snapStart:yx(t),rounded:xx(t),hasMinEdge:kx(t),hasMaxEdge:Ox(t),minBound:zx(n),maxBound:Lx(n),screenRange:Ux(n)};return Ix(o)}function tw(e){return function(n,t){return function(n,t,e){var o=(0<n?Mx:Ax)(Tx(e).y(),ix(e),sx(e),vx(e));return Qx(t,Zx(o)),on.some(o)}(e,n,t).map(function(){return!0})}}function ew(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=Ux(t),u=o.bind(function(n){return on.some(Xx(n,t))}).getOr(0),a=r.bind(function(n){return on.some(Xx(n,t))}).getOr(i),c={min:ix(n),max:sx(n),range:gx(n),value:e,hasMinEdge:kx(n),hasMaxEdge:Ox(n),minBound:zx(t),minOffset:0,maxBound:Lx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Rx(c)}(t,i,e,o,r);return zx(t)-zx(n)+u}function ow(n,t){Yt(n,Vw(),{value:t})}function rw(n,t){return{x:nn(n),y:nn(t)}}function iw(e,o){return function(n,t){return function(n,t,e,o){var r=0<n?Mx:Ax,i=t?Tx(o).x():r(Tx(o).x(),rx(o),cx(o),vx(o)),u=t?r(Tx(o).y(),ix(o),sx(o),vx(o)):Tx(o).y();return ow(e,rw(i,u)),on.some(i)}(e,o,n,t).map(function(){return!0})}}function uw(n){return"<alloy.field."+n+">"}function aw(n){return function(n){return xS[n]}(n)}function cw(n,t,e){return Qf.config(Dn({store:{mode:"manual",getValue:t,setValue:e}},n.map(function(n){return{store:{initialValue:n}}}).getOr({})))}function sw(n,t,e){return cw(n,function(n){return t(n.element())},function(n,t){return e(n.element(),t)})}function fw(e,t){function o(n,t){t.stop()}function r(n){return function(t,e){bn(n,function(n){n(t,e)})}}function i(n,t){if(!wh.isDisabled(n)){var e=t.event().raw();a(n,e.dataTransfer.files)}}function u(n,t){var e=t.event().raw().target.files;a(n,e)}var a=function(n,t){Qf.setValue(n,function(n){var t=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i");return S(xn(n),function(n){return t.test(n.name)})}(t)),Yt(n,Qb,{name:e.name})},c=pm({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:va([qd("input-file-events",[cr(Eo())])])}),n=e.label.map(function(n){return wy(n,t)}),s=sy.parts().field({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:va([TS([]),vS(),wh.config({}),wg.config({toggleClass:"dragenter",toggleOnExecute:!1}),qd("dropzone-events",[or("dragenter",r([o,wg.toggle])),or("dragleave",r([o,wg.toggle])),or("dragover",o),or("drop",r([o,i])),or(po(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:t.translate("Drop an image here")}},Ug.sketch({dom:{tag:"button",innerHtml:t.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(n){c.get(n).element().dom().click()},buttonBehaviours:va([Ny.config({})])})]}]}}}});return xy(n,s,["tox-form__group--stretched"],[])}function lw(n){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:n},behaviours:va([pg.config({ignore:!0}),Ny.config({})])}}function dw(n,t){Yt(n,lo(),{raw:{which:9,shiftKey:t}})}function mw(n,t){var e=MS&&n.sandboxed,o=P(P({},n.label.map(function(n){return{title:n}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),r=function(o){var r=Te("");return{getValue:function(n){return r.get()},setValue:function(n,t){if(o)Dr(n.element(),"srcdoc",t);else{Dr(n.element(),"src","javascript:''");var e=n.element().dom().contentWindow.document;e.open(),e.write(t),e.close()}r.set(t)}}}(e),i=n.label.map(function(n){return wy(n,t)}),u=sy.parts().field({factory:{sketch:function(n){return AS({uid:n.uid,dom:{tag:"iframe",attributes:o},behaviours:va([Ny.config({}),pg.config({}),kS(on.none(),r.getValue,r.setValue)])})}}});return xy(i,u,["tox-form__group--stretched"],[])}var gw=function(t,n){return t.getSystem().getByUid(n.uid+"-"+Uy()).map(function(n){return function(){return an.value(n)}}).getOrThunk(function(){return n.lazySink.fold(function(){return function(){return an.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(n){return function(){return n(t)}})})},pw=nn([ct("dom"),ct("fetch"),Yu("onOpen"),qu("onExecute"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),Ms("dropdownBehaviours",[wg,jy,sg,pg]),ct("toggleClass"),St("eventOrder",{}),ht("lazySink"),St("matchWidth",!1),St("useMinWidth",!1),ht("role")].concat(Zy())),hw=nn([xl({schema:[Wu()],name:"menu",defaults:function(n){return{onExecute:n.onExecute}}}),Wy()]),vw=Dl({name:"Dropdown",configFields:pw(),partFields:hw(),factory:function(t,n,e,o){function r(n){Pf.getState(n).each(function(n){Pg.highlightPrimary(n)})}function i(n,t){return qt(n),on.some(!0)}var u,a,c={expand:function(n){wg.isOn(n)||qy(t,function(n){return n},n,o,Z,Sy.HighlightNone).get(Z)},open:function(n){wg.isOn(n)||qy(t,function(n){return n},n,o,Z,Sy.HighlightFirst).get(Z)},isOpen:wg.isOn,close:function(n){wg.isOn(n)&&qy(t,function(n){return n},n,o,Z,Sy.HighlightFirst).get(Z)},repositionMenus:function(n){wg.isOn(n)&&Qy(n)}};return{uid:t.uid,dom:t.dom,components:n,behaviours:Is(t.dropdownBehaviours,[wg.config({toggleClass:t.toggleClass,aria:{mode:"expanded"}}),jy.config({others:{sandbox:function(n){return $y(t,n,{onOpen:function(){wg.on(n)},onClose:function(){wg.off(n)}})}}}),sg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(n,t){if(vw.isOpen(n)){var e=jy.getCoupled(n,"sandbox");r(e)}else vw.open(n);return on.some(!0)},onEscape:function(n,t){return vw.isOpen(n)?(vw.close(n),on.some(!0)):on.none()}}),pg.config({})]),events:gm(on.some(function(n){qy(t,function(n){return n},n,o,r,Sy.HighlightFirst).get(Z)})),eventOrder:P(P({},t.eventOrder),(u={},u[ko()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:c,domModification:{attributes:P(P({"aria-haspopup":"true"},t.role.fold(function(){return{}},function(n){return{role:n}})),"button"===t.dom.tag?{type:(a="type",Nn(t.dom,"attributes").bind(function(n){return Nn(n,a)})).getOr("button")}:{})}}},apis:{open:function(n,t){return n.open(t)},expand:function(n,t){return n.expand(t)},close:function(n,t){return n.close(t)},isOpen:function(n,t){return n.isOpen(t)},repositionMenus:function(n,t){return n.repositionMenus(t)}}}),bw=ba({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return nr([tr(bo(),nn(!0))])},exhibit:function(n,t){return Ur({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),yw=Vr("color-input-change"),xw=Vr("color-swatch-change"),ww=Vr("color-picker-cancel"),Sw=nn(Vr("rgb-hex-update")),Cw=nn(Vr("slider-update")),kw=nn(Vr("palette-update")),Ow=Ht().deviceType.isTouch(),Tw=wl({schema:[ct("dom")],name:"label"}),Ew=ex("top-left"),Bw=ex("top"),Dw=ex("top-right"),_w=ex("right"),Aw=ex("bottom-right"),Mw=ex("bottom"),Fw=ex("bottom-left"),Iw=[Tw,ex("left"),_w,Bw,Mw,Ew,Dw,Fw,Aw,yl({name:"thumb",defaults:nn({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:nr([ur(to(),n,"spectrum"),ur(eo(),n,"spectrum"),ur(oo(),n,"spectrum"),ur(ro(),n,"spectrum"),ur(io(),n,"spectrum"),ur(ao(),n,"spectrum")])}}}),yl({schema:[_t("mouseIsDown",function(){return Te(!1)})],name:"spectrum",overrides:function(e){function o(t,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(t,e,n)})}var r=e.model.manager,n=nr([or(to(),o),or(eo(),o)]),t=nr([or(ro(),o),or(io(),function(n,t){e.mouseIsDown.get()&&o(n,t)})]);return{behaviours:va(Ow?[]:[sg.config({mode:"special",onLeft:function(n){return r.onLeft(n,e)},onRight:function(n){return r.onRight(n,e)},onUp:function(n){return r.onUp(n,e)},onDown:function(n){return r.onDown(n,e)}}),pg.config({})]),events:Ow?n:t}}})],Rw=Ht().deviceType.isTouch(),Vw=nn("slider.change.value"),Nw="left",Hw=Jx(-1),Pw=Jx(1),zw=on.none,Lw=on.none,jw={"top-left":on.none(),top:on.none(),"top-right":on.none(),right:on.some(function(n,t){_x(n,Ex(fx(t)))}),"bottom-right":on.none(),bottom:on.none(),"bottom-left":on.none(),left:on.some(function(n,t){_x(n,Ex(ux(t)))})},Uw=/* */Object.freeze({setValueFrom:function(n,t,e){var o=Kx(n,t,e),r=qx(o);return Yx(n,r),o},setToMin:function(n,t){var e=rx(t);Yx(n,qx(e))},setToMax:function(n,t){var e=cx(t);Yx(n,qx(e))},findValueOfOffset:Kx,getValueFromEvent:function(n){return ox(n).map(function(n){return n.left()})},findPositionOfValue:$x,setPositionFromValue:function(n,t,e,o){var r=Tx(e),i=$x(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=du(t.element())/2;si(t.element(),"left",i-u+"px")},onLeft:Hw,onRight:Pw,onUp:zw,onDown:Lw,edgeActions:jw}),Ww=on.none,Gw=on.none,Xw=tw(-1),Yw=tw(1),qw={"top-left":on.none(),top:on.some(function(n,t){_x(n,Bx(ax(t)))}),"top-right":on.none(),right:on.none(),"bottom-right":on.none(),bottom:on.some(function(n,t){_x(n,Bx(lx(t)))}),"bottom-left":on.none(),left:on.none()},Kw=/* */Object.freeze({setValueFrom:function(n,t,e){var o=nw(n,t,e),r=Zx(o);return Qx(n,r),o},setToMin:function(n,t){var e=ix(t);Qx(n,Zx(e))},setToMax:function(n,t){var e=sx(t);Qx(n,Zx(e))},findValueOfOffset:nw,getValueFromEvent:function(n){return ox(n).map(function(n){return n.top()})},findPositionOfValue:ew,setPositionFromValue:function(n,t,e,o){var r=Tx(e),i=ew(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),u=cu(t.element())/2;si(t.element(),"top",i-u+"px")},onLeft:Ww,onRight:Gw,onUp:Xw,onDown:Yw,edgeActions:qw}),Jw=iw(-1,!1),$w=iw(1,!1),Qw=iw(-1,!0),Zw=iw(1,!0),nS={"top-left":on.some(function(n,t){_x(n,Dx(ux(t),ax(t)))}),top:on.some(function(n,t){_x(n,Dx(px(t),ax(t)))}),"top-right":on.some(function(n,t){_x(n,Dx(fx(t),ax(t)))}),right:on.some(function(n,t){_x(n,Dx(fx(t),hx(t)))}),"bottom-right":on.some(function(n,t){_x(n,Dx(fx(t),lx(t)))}),bottom:on.some(function(n,t){_x(n,Dx(px(t),lx(t)))}),"bottom-left":on.some(function(n,t){_x(n,Dx(ux(t),lx(t)))}),left:on.some(function(n,t){_x(n,Dx(ux(t),hx(t)))})},tS=/* */Object.freeze({setValueFrom:function(n,t,e){var o=Kx(n,t,e.left()),r=nw(n,t,e.top()),i=rw(o,r);return ow(n,i),i},setToMin:function(n,t){var e=rx(t),o=ix(t);ow(n,rw(e,o))},setToMax:function(n,t){var e=cx(t),o=sx(t);ow(n,rw(e,o))},getValueFromEvent:function(n){return ox(n)},setPositionFromValue:function(n,t,e,o){var r=Tx(e),i=$x(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=ew(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),a=du(t.element())/2,c=cu(t.element())/2;si(t.element(),"left",i-a+"px"),si(t.element(),"top",u-c+"px")},onLeft:Jw,onRight:$w,onUp:Qw,onDown:Zw,edgeActions:nS}),eS=Ht().deviceType.isTouch(),oS=[St("stepSize",1),St("onChange",Z),St("onChoose",Z),St("onInit",Z),St("onDragStart",Z),St("onDragEnd",Z),St("snapToGrid",!1),St("rounded",!0),ht("snapStart"),st("model",it("mode",{x:[St("minX",0),St("maxX",100),_t("value",function(n){return Te(n.mode.minX)}),ct("getInitialValue"),$u("manager",Uw)],y:[St("minY",0),St("maxY",100),_t("value",function(n){return Te(n.mode.minY)}),ct("getInitialValue"),$u("manager",Kw)],xy:[St("minX",0),St("maxX",100),St("minY",0),St("maxY",100),_t("value",function(n){return Te({x:nn(n.mode.minX),y:nn(n.mode.minY)})}),ct("getInitialValue"),$u("manager",tS)]})),Ms("sliderBehaviours",[sg,Qf])].concat(eS?[]:[_t("mouseIsDown",function(){return Te(!1)})]),rS=Ht().deviceType.isTouch(),iS=Dl({name:"Slider",configFields:oS,partFields:Iw,factory:function(i,n,t,e){function u(n){return qs(n,i,"thumb")}function a(n){return qs(n,i,"spectrum")}function o(n){return Ys(n,i,"left-edge")}function r(n){return Ys(n,i,"right-edge")}function c(n){return Ys(n,i,"top-edge")}function s(n){return Ys(n,i,"bottom-edge")}function f(n,t){m.setPositionFromValue(n,t,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function l(n,t){d.value.set(t);var e=u(n);return f(n,e),i.onChange(n,e,t),on.some(!0)}var d=i.model,m=d.manager,g=[or(to(),function(n,t){i.onDragStart(n,u(n))}),or(oo(),function(n,t){i.onDragEnd(n,u(n))})],p=[or(ro(),function(n,t){t.stop(),i.onDragStart(n,u(n)),i.mouseIsDown.set(!0)}),or(ao(),function(n,t){i.onDragEnd(n,u(n))})],h=rS?g:p;return{uid:i.uid,dom:i.dom,components:n,behaviours:Is(i.sliderBehaviours,z([rS?[]:[sg.config({mode:"special",focusIn:function(n){return Ys(n,i,"spectrum").map(sg.focusIn).map(nn(!0))}})],[Qf.config({store:{mode:"manual",getValue:function(n){return d.value.get()}}}),fc.config({channels:{"mouse.released":{onReceive:function(e,n){function t(){Ys(e,i,"thumb").each(function(n){var t=d.value.get();i.onChoose(e,n,t)})}if(rS)t();else{var o=i.mouseIsDown.get();i.mouseIsDown.set(!1),o&&t()}}}}})]])),events:nr([or(Vw(),function(n,t){l(n,t.event().value())}),Fi(function(n,t){var e=d.getInitialValue();d.value.set(e);var o=u(n);f(n,o);var r=a(n);i.onInit(n,o,r,d.value.get())})].concat(h)),apis:{resetToMin:function(n){m.setToMin(n,i)},resetToMax:function(n){m.setToMax(n,i)},changeValue:l,refresh:f},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,t){n.resetToMin(t)},resetToMax:function(n,t){n.resetToMax(t)},refresh:function(n,t){n.refresh(t)}}}),uS=function(n,t){var e=iS.parts().spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=iS.parts().thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return iS.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:nn({y:nn(0)})},components:[e,o],sliderBehaviours:va([pg.config({})]),onChange:function(n,t,e){Yt(n,Cw(),{value:e})}})},aS=[Ms("formBehaviours",[Qf])],cS=function(o,n,t){return{uid:o.uid,dom:o.dom,components:n,behaviours:Is(o.formBehaviours,[Qf.config({store:{mode:"manual",getValue:function(n){var t=Js(n,o);return L(t,function(n,t){return n().bind(function(n){return function(n,t){return n.fold(function(){return an.error(t)},an.value)}(Ql.getCurrent(n),"missing current")}).map(Qf.getValue)})},setValue:function(e,n){Cn(n,function(t,n){Ys(e,o,n).each(function(n){Ql.getCurrent(n).each(function(n){Qf.setValue(n,t)})})})}}})]),apis:{getField:function(n,t){return Ys(n,o,t).bind(Ql.getCurrent)}}}},sS={getField:Lr(function(n,t,e){return n.getField(t,e)}),sketch:function(n){var e,t=(e=[],{field:function(n,t){return e.push(n),js("form",uw(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=w(r,function(n){return yl({name:n,pname:uw(n)})});return ef("form",aS,i,cS,o)}},fS=Vr("valid-input"),lS=Vr("invalid-input"),dS=Vr("validating-input"),mS="colorcustom.rgb.",gS=function(d,m,g,p){function h(n,t,e,o,r){var i=d(mS+"range"),u=[sy.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),sy.parts().field({data:r,factory:ly,inputAttributes:P({type:"text"},"hex"===t?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:va([function(t,o){return Iy.config({invalidClass:m("invalid"),notify:{onValidate:function(n){Yt(n,dS,{type:t})},onValid:function(n){Yt(n,fS,{type:t,value:Qf.getValue(n)})},onInvalid:function(n){Yt(n,lS,{type:t,value:Qf.getValue(n)})}},validator:{validate:function(n){var t=Qf.getValue(n),e=o(t)?an.value(!0):an.error(d("aria.input.invalid"));return Dy(e)},validateOnLoad:!1}})}(t,n),Ny.config({})]),onSetValue:function(n){Iy.isInvalid(n)&&Iy.run(n).get(Z)}})],a="hex"!==t?[sy.parts()["aria-descriptor"]({text:i})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(a)}}function v(n,t){var e=t.red(),o=t.green(),r=t.blue();Qf.setValue(n,{red:e,green:o,blue:r})}function b(n,t){y.getOpt(n).each(function(n){si(n.element(),"background-color","#"+t.value())})}var y=pm({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return Bl({factory:function(){function r(n){return u[n]().get()}function i(n,t){u[n]().set(t)}function t(n,t){var e=t.event();"hex"!==e.type()?i(e.type(),on.none()):p(n)}function o(e,n,t){var o=parseInt(t,10);i(n,on.some(o)),r("red").bind(function(e){return r("green").bind(function(t){return r("blue").map(function(n){return jh(e,t,n,1)})})}).each(function(n){var t=function(t,n){var e=Lh(n);return sS.getField(t,"hex").each(function(n){pg.isFocused(n)||Qf.setValue(t,{hex:e.value()})}),e}(e,n);b(e,t)})}function e(n,t){var e=t.event();!function(n){return"hex"===n.type()}(e)?o(n,e.type(),e.value()):function(n,t){g(n);var e=Nh(t);i("hex",on.some(t));var o=Gh(e);v(n,o),a(o),Yt(n,Sw(),{hex:e}),b(n,e)}(n,e.value())}function n(n){return{label:d(mS+n+".label"),description:d(mS+n+".description")}}var u={red:nn(Te(on.some(255))),green:nn(Te(on.some(255))),blue:nn(Te(on.some(255))),hex:nn(Te(on.some("ffffff")))},a=function(n){var t=n.red(),e=n.green(),o=n.blue();i("red",on.some(t)),i("green",on.some(e)),i("blue",on.some(o))},c=n("red"),s=n("green"),f=n("blue"),l=n("hex");return Dn(sS.sketch(function(n){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[n.field("red",sy.sketch(h(Uh,"red",c.label,c.description,255))),n.field("green",sy.sketch(h(Uh,"green",s.label,s.description,255))),n.field("blue",sy.sketch(h(Uh,"blue",f.label,f.description,255))),n.field("hex",sy.sketch(h(Hh,"hex",l.label,l.description,"ffffff"))),y.asSpec()],formBehaviours:va([Iy.config({invalidClass:m("form-invalid")}),qd("rgb-form-events",[or(fS,e),or(lS,t),or(dS,t)])])}}),{apis:{updateHex:function(n,t){Qf.setValue(n,{hex:t.value()}),function(n,t){var e=Gh(t);v(n,e),a(e)}(n,t),b(n,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(n,t,e){n.updateHex(t,e)}},extraApis:{}})},pS=function(n,o){function r(n,t){var e=n.width,o=n.height,r=n.getContext("2d");if(null!==r){r.fillStyle=t,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}}var i=iS.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),u=iS.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}});return Bl({factory:function(n){var t=nn({x:nn(0),y:nn(0)}),e=va([Ql.config({find:on.some}),pg.config({})]);return iS.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:t},rounded:!1,components:[i,u],onChange:function(n,t,e){Yt(n,kw(),{value:e})},onInit:function(n,t,e,o){r(e.element().dom(),Yh(tv()))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(n,t,e){!function(n,t){var e=n.components()[0].element().dom();r(e,Yh(t))}(t,e)}},extraApis:{}})},hS=function(l,d){return Bl({name:"ColourPicker",configFields:[ct("dom"),St("onValidHex",Z),St("onInvalidHex",Z)],factory:function(n){function t(n,e){u.getOpt(n).each(function(n){var t=Gh(e);s.paletteRgba().set(t),i.setRgba(n,t)})}function e(n,t){f.getOpt(n).each(function(n){r.updateHex(n,t)})}function a(t,e,n){bn(n,function(n){n(t,e)})}var o,c,r=gS(l,d,n.onValidHex,n.onInvalidHex),i=pS(l,d),s={paletteRgba:nn(Te(tv()))},u=pm(i.sketch({})),f=pm(r.sketch({}));return{uid:n.uid,dom:n.dom,components:[u.asSpec(),uS(l,d),f.asSpec()],behaviours:va([qd("colour-picker-events",[or(kw(),(c=[e],function(n,t){var e=t.event().value(),o=function(n){var t,e=0,o=0,r=n.red()/255,i=n.green()/255,u=n.blue()/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?tx(0,0,100*(o=a)):(e=60*((e=r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),t=(c-a)/c,o=c,tx(Math.round(e),Math.round(100*t),Math.round(100*o)))}(s.paletteRgba().get()),r=tx(o.hue(),e.x(),100-e.y()),i=Wh(r),u=Lh(i);a(n,u,c)})),or(Cw(),(o=[t,e],function(n,t){var e=function(n){var t=tx((100-n)/100*360,100,100),e=Wh(t);return Lh(e)}(t.event().value().y());a(n,e,o)}))]),Ql.config({find:function(n){return f.getOpt(n)}}),sg.config({mode:"acyclic"})])}}})},vS=function(){return Ql.config({find:on.some})},bS=function(n){return Ql.config({find:n.getOpt})},yS=function(n){return Ql.config({find:function(t){return yr(t.element(),n).bind(function(n){return t.getSystem().getByDom(n).toOption()})}})},xS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},wS=tinymce.util.Tools.resolve("tinymce.Resource"),SS=le([St("preprocess",l),St("postprocess",l)]),CS=function(r,n){var i=ot("RepresentingConfigs.memento processors",SS,n);return Qf.config({store:{mode:"manual",getValue:function(n){var t=r.get(n),e=Qf.getValue(t);return i.postprocess(e)},setValue:function(n,t){var e=i.preprocess(t),o=r.get(n);Qf.setValue(o,e)}}})},kS=cw,OS=function(n){return sw(n,Tr,Er)},TS=function(n){return Qf.config({store:{mode:"memory",initialValue:n}})},ES=Vr("alloy-fake-before-tabstop"),BS=Vr("alloy-fake-after-tabstop"),DS=function(n){return sb(n,["."+ES,"."+BS].join(","),nn(!1))},_S=function(n,t){var e=t.element();oi(e,ES)?dw(n,!0):oi(e,BS)&&dw(n,!1)},AS=function(n){return{dom:{tag:"div",classes:["tox-navobj"]},components:[lw([ES]),n,lw([BS])],behaviours:va([yS(1)])}},MS=!(Ht().browser.isIE()||Ht().browser.isEdge());function FS(n,t){return VS(H.document.createElement("canvas"),n,t)}function IS(n){var t=FS(n.width,n.height);return RS(t).drawImage(n,0,0),t}function RS(n){return n.getContext("2d")}function VS(n,t,e){return n.width=t,n.height=e,n}function NS(n){return n.naturalWidth||n.width}function HS(n){return n.naturalHeight||n.height}var PS,zS,LS=window.Promise?window.Promise:(PS=jS.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){H.setTimeout(n,1)},zS=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},jS.prototype["catch"]=function(n){return this.then(null,n)},jS.prototype.then=function(e,o){var r=this;return new jS(function(n,t){WS.call(r,new qS(e,o,n,t))})},jS.all=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var c=Array.prototype.slice.call(1===n.length&&zS(n[0])?n[0]:n);return new jS(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},jS.resolve=function(t){return t&&"object"==typeof t&&t.constructor===jS?t:new jS(function(n){n(t)})},jS.reject=function(e){return new jS(function(n,t){t(e)})},jS.race=function(r){return new jS(function(n,t){for(var e=0,o=r;e<o.length;e++)o[e].then(n,t)})},jS);function jS(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],KS(n,US(GS,this),US(XS,this))}function US(n,t){return function(){return n.apply(t,arguments)}}function WS(o){var r=this;null!==this._state?PS(function(){var n=r._state?o.onFulfilled:o.onRejected;if(null!==n){var t;try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function GS(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void KS(US(t,n),US(GS,this),US(XS,this))}this._state=!0,this._value=n,YS.call(this)}catch(e){XS.call(this,e)}}function XS(n){this._state=!1,this._value=n,YS.call(this)}function YS(){for(var n=0,t=this._deferreds;n<t.length;n++){var e=t[n];WS.call(this,e)}this._deferreds=[]}function qS(n,t,e,o){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.resolve=e,this.reject=o}function KS(n,t,e){var o=!1;try{n(function(n){o||(o=!0,t(n))},function(n){o||(o=!0,e(n))})}catch(r){if(o)return;o=!0,e(r)}}function JS(e){return new LS(function(n,t){(function p(n){var t=n.split(","),e=/data:([^;]+)/.exec(t[0]);if(!e)return on.none();for(var o=e[1],r=t[1],i=H.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,g=0;m<l;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return on.some(new H.Blob(c,{type:o}))})(e).fold(function(){t("uri is not base64: "+e)},n)})}function $S(n,o,r){return o=o||"image/png",H.HTMLCanvasElement.prototype.toBlob?new LS(function(t,e){n.toBlob(function(n){n?t(n):e()},o,r)}):JS(n.toDataURL(o,r))}function QS(n){return function t(a){return new LS(function(n,t){var e=H.URL.createObjectURL(a),o=new H.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),n(o)}function u(){r(),t("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(n).then(function(n){!function e(n){H.URL.revokeObjectURL(n.src)}(n);var t=FS(NS(n),HS(n));return RS(t).drawImage(n,0,0),t})}function ZS(n,t,e){var o=t.type;function r(t,e){return n.then(function(n){return function o(n,t,e){return t=t||"image/png",n.toDataURL(t,e)}(n,t,e)})}return{getType:nn(o),toBlob:function i(){return LS.resolve(t)},toDataURL:function u(){return e},toBase64:function a(){return e.split(",")[1]},toAdjustedBlob:function c(t,e){return n.then(function(n){return $S(n,t,e)})},toAdjustedDataURL:r,toAdjustedBase64:function s(n,t){return r(n,t).then(function(n){return n.split(",")[1]})},toCanvas:function f(){return n.then(IS)}}}function nC(t){return function n(e){return new LS(function(n){var t=new H.FileReader;t.onloadend=function(){n(t.result)},t.readAsDataURL(e)})}(t).then(function(n){return ZS(QS(t),t,n)})}function tC(t,n){return $S(t,n).then(function(n){return ZS(LS.resolve(t),n,t.toDataURL())})}function eC(n,t,e){var o="string"==typeof n?parseFloat(n):n;return e<o?o=e:o<t&&(o=t),o}var oC=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function rC(n,t){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=t[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=n[u+5*a]*o[a];r[u+5*i]=e}}return r}function iC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=RS(n);var r=function B(n,t){for(var e,o,r,i,u=n.data,a=t[0],c=t[1],s=t[2],f=t[3],l=t[4],d=t[5],m=t[6],g=t[7],p=t[8],h=t[9],v=t[10],b=t[11],y=t[12],x=t[13],w=t[14],S=t[15],C=t[16],k=t[17],O=t[18],T=t[19],E=0;E<u.length;E+=4)e=u[E],o=u[E+1],r=u[E+2],i=u[E+3],u[E]=e*a+o*c+r*s+i*f+l,u[E+1]=e*d+o*m+r*g+i*p+h,u[E+2]=e*v+o*b+r*y+i*x+w,u[E+3]=e*S+o*C+r*k+i*O+T;return n}(o.getImageData(0,0,n.width,n.height),e);return o.putImageData(r,0,0),tC(n,t)}(n,t.getType(),e)})}function uC(t,e){return t.toCanvas().then(function(n){return function u(n,t,e){var o=RS(n);var r=o.getImageData(0,0,n.width,n.height),i=o.getImageData(0,0,n.width,n.height);return i=function w(n,t,e){function o(n,t,e){return e<n?n=e:n<t&&(n=t),n}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=n.data,a=t.data,c=n.width,s=n.height,f=0;f<s;f++)for(var l=0;l<c;l++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(l+h-i,0,c-1),b=4*(o(f+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(f*c+l);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return t}(r,i,e),o.putImageData(i,0,0),tC(n,t)}(n,t.getType(),e)})}function aC(e){return function(n,t){return iC(n,e([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t))}}function cC(n,t,e,o){return iC(n,function r(n,t,e,o){return rC(n,[t=eC(t,0,2),0,0,0,0,0,e=eC(e,0,2),0,0,0,0,0,o=eC(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t,e,o))}var sC=function qF(t){return function(n){return iC(n,t)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),fC=aC(function KF(n,t){return rC(n,[1,0,0,0,t=eC(255*t,-255,255),0,1,0,0,t,0,0,1,0,t,0,0,0,1,0,0,0,0,0,1])}),lC=aC(function JF(n,t){var e;return t=eC(t,-1,1),rC(n,[(e=(t*=100)<0?127+t/100*127:127*(e=0===(e=t%1)?oC[t]:oC[Math.floor(t)]*(1-e)+oC[Math.floor(t)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),dC=function $F(t){return function(n){return uC(n,t)}}([0,-1,0,-1,5,-1,0,-1,0]),mC=function QF(c){return function(t,e){return t.toCanvas().then(function(n){return function(n,t,e){var o=RS(n),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(n,t){for(var e=n.data,o=0;o<e.length;o+=4)e[o]=t[e[o]],e[o+1]=t[e[o+1]],e[o+2]=t[e[o+2]];return n}(o.getImageData(0,0,n.width,n.height),r);return o.putImageData(u,0,0),tC(n,t)}(n,t.getType(),e)})}}(function(n,t){return 255*Math.pow(n/255,1-t)});function gC(n,t,e){var o=NS(n),r=HS(n),i=t/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new LS(function(n){var t=NS(u),e=HS(u),o=Math.floor(t*a),r=Math.floor(e*c),i=FS(o,r);RS(i).drawImage(u,0,0,t,e,0,0,o,r),n(i)})}(n,i,u);return a?c.then(function(n){return gC(n,t,e)}):c}function pC(t,e){return t.toCanvas().then(function(n){return function a(n,t,e){var o=FS(n.width,n.height),r=RS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||VS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(n,0,0),tC(o,t)}(n,t.getType(),e)})}function hC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=FS(n.width,n.height),r=RS(o);"v"===e?(r.scale(1,-1),r.drawImage(n,0,-o.height)):(r.scale(-1,1),r.drawImage(n,-o.width,0));return tC(o,t)}(n,t.getType(),e)})}function vC(t,e,o,r,i){return t.toCanvas().then(function(n){return function a(n,t,e,o,r,i){var u=FS(r,i);return RS(u).drawImage(n,-e,-o),tC(u,t)}(n,t.getType(),e,o,r,i)})}function bC(n){return sC(n)}function yC(n){return dC(n)}function xC(n,t){return mC(n,t)}function wC(n,t){return fC(n,t)}function SC(n,t){return lC(n,t)}function CC(n,t){return hC(n,t)}function kC(n,t,e){return function r(t,e,o){return t.toCanvas().then(function(n){return gC(n,e,o).then(function(n){return tC(n,t.getType())})})}(n,t,e)}function OC(n,t){return pC(n,t)}function TC(n,t){return P({dom:{tag:"span",innerHtml:n,classes:["tox-icon","tox-tbtn__icon-wrap"]}},t)}function EC(n,t){return TC(vm(n,t),{})}function BC(n,t){return TC(vm(n,t),{behaviours:va([lg.config({})])})}function DC(n,t,e){return{dom:{tag:"span",innerHtml:e.translate(n),classes:[t+"__select-label"]},behaviours:va([lg.config({})])}}function _C(n,t,o){function e(n,t){var e=Qf.getValue(n);return pg.focus(e),Yt(e,"keydown",{raw:t.event().raw()}),vw.close(e),on.some(!0)}var r=Te(Z),i=n.text.map(function(n){return pm(DC(n,t,o.providers))}),u=n.icon.map(function(n){return pm(BC(n,o.providers.icons))}),a=n.role.fold(function(){return{}},function(n){return{role:n}}),c=n.tooltip.fold(function(){return{}},function(n){var t=o.providers.translate(n);return{title:t,"aria-label":t}});return pm(vw.sketch(P(P({},a),{dom:{tag:"button",classes:[t,t+"--select"].concat(w(n.classes,function(n){return t+"--"+n})),attributes:P({},c)},components:Th([u.map(function(n){return n.asSpec()}),i.map(function(n){return n.asSpec()}),on.some({dom:{tag:"div",classes:[t+"__select-chevron"],innerHtml:vm("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:va(g(n.dropdownBehaviours,[Ch(n.disabled),bw.config({}),lg.config({}),qd("dropdown-events",[kp(n,r),Op(n,r)]),qd("menubutton-update-display-text",[or(tk,function(t,e){i.bind(function(n){return n.getOpt(t)}).each(function(n){lg.set(n,[ki(o.providers.translate(e.event().text()))])})}),or(ek,function(t,e){u.bind(function(n){return n.getOpt(t)}).each(function(n){lg.set(n,[BC(e.event().icon(),o.providers.icons)])})})])])),eventOrder:Dn(nk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:va([sg.config({mode:"special",onLeft:e,onRight:e})]),lazySink:o.getSink,toggleClass:t+"--active",parts:{menu:xv(0,n.columns,n.presets)},fetch:function(){return By(n.fetch)}}))).asSpec()}function AC(n){return"separator"===n.type}function MC(n,e){var t=k(n,function(n,t){return function(n){return cn(n)}(t)?""===t?n:"|"===t?0<n.length&&!AC(n[n.length-1])?n.concat([ok]):n:Tn(e,t.toLowerCase())?n.concat([e[t.toLowerCase()]]):n:n.concat([t])},[]);return 0<t.length&&AC(t[t.length-1])&&t.pop(),t}function FC(n,t){return function(n){return Tn(n,"getSubmenuItems")}(n)?function(n,t){var e=n.getSubmenuItems(),o=rk(e,t);return{item:n,menus:Dn(o.menus,q(n.value,o.items)),expansions:Dn(o.expansions,q(n.value,n.value))}}(n,t):{item:n,menus:{},expansions:{}}}function IC(n,e,o,t){var r=Vr("primary-menu"),i=rk(n,o.shared.providers.menuItems());if(0===i.items.length)return on.none();var u=ob(r,i.items,e,o,t),a=L(i.menus,function(n,t){return ob(t,n,e,o,!1)}),c=Dn(a,q(r,u));return on.from(Pg.tieredData(r,c,i.expansions))}function RC(e){return{isDisabled:function(){return wh.isDisabled(e)},setDisabled:function(n){return wh.set(e,n)},setActive:function(n){var t=e.element();n?(ni(t,"tox-tbtn--enabled"),Dr(t,"aria-pressed",!0)):(ei(t,"tox-tbtn--enabled"),Mr(t,"aria-pressed"))},isActive:function(){return oi(e.element(),"tox-tbtn--enabled")}}}function VC(n,t,e,o){return _C({text:n.text,icon:n.icon,tooltip:n.tooltip,role:o,fetch:function(t){n.fetch(function(n){t(IC(n,Ah.CLOSE_ON_EXECUTE,e,!1))})},onSetup:n.onSetup,getApi:RC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Ny.config({})]},t,e.shared)}function NC(t,o,r){return function(n){n(w(t,function(n){var t=n.text.fold(function(){return{}},function(n){return{text:n}});return P(P({type:n.type},t),{onAction:function(e){return function(n){r.shared.getSink().each(function(n){o().getOpt(n).each(function(n){xa(n.element()),Yt(n,ty,{name:e.name,value:e.storage.get()})})});var t=!n.isActive();n.setActive(t),e.storage.set(t)}}(n),onSetup:function(t){return function(n){n.setActive(t.storage.get())}}(n)})}))}}function HC(n,t,e,o,r){void 0===e&&(e=[]);var i=t.fold(function(){return{}},function(n){return{action:n}}),u=P({buttonBehaviours:va([Ch(n.disabled),Ny.config({}),qd("button press",[er("click"),er("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},i),a=Dn(u,{dom:o});return Dn(a,{components:r})}function PC(n,t,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:n.tooltip.map(function(n){return{"aria-label":e.translate(n),title:e.translate(n)}}).getOr({})},i=n.icon.map(function(n){return EC(n,e.icons)}),u=Th([i]);return HC(n,t,o,r,u)}function zC(n,t,e,o){void 0===o&&(o=[]);var r=PC(n,on.some(t),e,o);return Ug.sketch(r)}function LC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(n.text),u=n.icon?n.icon.map(function(n){return EC(n,e.icons)}):on.none(),a=u.isSome()?Th([u]):[],c=u.isSome()?{}:{innerHtml:i},s=g(n.primary||n.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],n.borderless?["tox-button--naked"]:[],r),f=P(P({tag:"button",classes:s},c),{attributes:{title:i}});return HC(n,t,o,f,a)}function jC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=LC(n,on.some(t),e,o,r);return Ug.sketch(i)}function UC(t,e){return function(n){"custom"===e?Yt(n,ty,{name:t,value:{}}):"submit"===e?Xt(n,ey):"cancel"===e?Xt(n,ny):H.console.error("Unknown button type: ",e)}}function WC(n,t,e){if(function(n,t){return"menu"===t}(0,t)){var o=n,r=P(P({},n),{fetch:NC(o.items,function(){return i},e)}),i=pm(VC(r,"tox-tbtn",e,on.none()));return i.asSpec()}if(function(n,t){return"custom"===t||"cancel"===t||"submit"===t}(0,t)){var u=UC(n.name,t),a=P(P({},n),{borderless:!1});return jC(a,u,e.shared.providers,[])}H.console.error("Unknown footer button type: ",t)}function GC(n,t){var e=UC(n.name,"custom");return function(n,t){return xy(n,t,[],[])}(on.none(),sy.parts().field(P({factory:Ug},LC(n,on.some(e),t,[TS(""),vS()]))))}function XC(n,t){return yl({factory:sy,name:n,overrides:function(o){return{fieldBehaviours:va([qd("coupled-input-behaviour",[or(go(),function(e){(function(n,t,e){return Ys(n,t,e).bind(Ql.getCurrent)})(e,o,t).each(function(t){Ys(e,o,"lock").each(function(n){wg.isOn(n)&&o.onLockedChange(e,t,n)})})})])])}}})}function YC(n){var t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(n);if(null===t)return an.error(n);var e=parseFloat(t[1]),o=t[2];return an.value({value:e,unit:o})}function qC(n,t){function e(n){return Object.prototype.hasOwnProperty.call(o,n)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1};return n.unit===t?on.some(n.value):e(n.unit)&&e(t)?o[n.unit]===o[t]?on.some(n.value):on.some(n.value/o[n.unit]*o[t]):on.none()}function KC(n){return on.none()}function JC(n,t){return function(n,t,e){return n.isSome()&&t.isSome()?on.some(e(n.getOrDie(),t.getOrDie())):on.none()}(YC(n).toOption(),YC(t).toOption(),function(n,t){return qC(n,t.unit).map(function(n){return t.value/n}).map(function(n){return function(t,e){return function(n){return qC(n,e).map(function(n){return{value:n*t,unit:e}})}}(n,t.unit)}).getOr(KC)}).getOr(KC)}function $C(o,t){function n(n){return{dom:{tag:"div",classes:["tox-form__group"]},components:n}}function e(e){return sy.parts().field({factory:ly,inputClasses:["tox-textfield"],inputBehaviours:va([wh.config({disabled:o.disabled}),Ny.config({}),qd("size-input-events",[or(so(),function(n,t){Yt(n,i,{isField1:e})}),or(po(),function(n,t){Yt(n,Qb,{name:o.name})})])]),selectOnFocus:!1})}function r(n){return{dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}}}var a=KC,i=Vr("ratio-event"),u=ak.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:vm("lock",t.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:vm("unlock",t.icons)}}],buttonBehaviours:va([Ch(o.disabled),Ny.config({})])}),c=ak.parts().field1(n([sy.parts().label(r("Width")),e(!0)])),s=ak.parts().field2(n([sy.parts().label(r("Height")),e(!1)]));return ak.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,n([r("&nbsp;"),u])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(n,t,e){YC(Qf.getValue(n)).each(function(n){a(n).each(function(n){Qf.setValue(t,function(n){var t,e={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},o=n.value.toFixed((t=n.unit)in e?e[t]:1);return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+n.unit}(n))})})},coupledFieldBehaviours:va([wh.config({disabled:o.disabled,onDisabled:function(n){ak.getField1(n).bind(sy.getField).each(wh.disable),ak.getField2(n).bind(sy.getField).each(wh.disable),ak.getLock(n).each(wh.disable)},onEnabled:function(n){ak.getField1(n).bind(sy.getField).each(wh.enable),ak.getField2(n).bind(sy.getField).each(wh.enable),ak.getLock(n).each(wh.enable)}}),qd("size-input-events2",[or(i,function(n,t){var e=t.event().isField1(),o=e?ak.getField1(n):ak.getField2(n),r=e?ak.getField2(n):ak.getField1(n),i=o.map(Qf.getValue).getOr(""),u=r.map(Qf.getValue).getOr("");a=JC(i,u)})])])})}function QC(r,c){function n(n,t,e,o){return pm(jC({name:n,text:n,disabled:e,primary:o,icon:on.none(),borderless:!1},t,c))}function t(n,t,e,o){return pm(zC({name:n,icon:on.some(n),tooltip:on.some(t),disabled:o,primary:!1,borderless:!1},e,c))}function u(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(wh)&&wh.disable(t)})}function a(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(wh)&&wh.enable(t)})}function i(n,t,e){Yt(n,t,e)}function e(n){return Xt(n,dk.disable())}function o(n){return Xt(n,dk.enable())}function s(n,t){e(n),i(n,ck.transform(),{transform:t}),o(n)}function f(n){return function(){Q.getOpt(n).each(function(n){lg.set(n,[J])})}}function l(n,t){e(n),i(n,ck.transformApply(),{transform:t,swap:f(n)}),o(n)}function d(){return n("Back",function(n){return i(n,ck.back(),{swap:f(n)})},!1,!1)}function m(){return pm({dom:{tag:"div",classes:["tox-spacer"]},behaviours:va([wh.config({})])})}function g(){return n("Apply",function(n){return i(n,ck.apply(),{swap:f(n)})},!0,!0)}function p(){return function(n){var t=r.getRect();return function(n,t,e,o,r){return vC(n,t,e,o,r)}(n,t.x,t.y,t.w,t.h)}}function h(t,e){return function(n){return t(n,e)}}function v(n,t){!function(n,t){e(n),i(n,ck.tempTransform(),{transform:t}),o(n)}(n,t)}function b(n,t,e,o,r){var i=iS.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(n)}}),u=iS.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=iS.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return pm(iS.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:nn({x:nn(o)})},components:[i,u,a],sliderBehaviours:va([pg.config({})]),onChoose:t}))}function y(n,t,e,o,r){return[d(),function(n,r,t,e,o){return b(n,function(n,t,e){var o=h(r,e.x()/100);s(n,o)},t,e,o)}(n,t,e,o,r),g()]}function x(n,t,e,o,r){var i=y(n,t,e,o,r);return Hb.sketch({dom:k,components:i.map(function(n){return n.asSpec()}),containerBehaviours:va([qd("image-tools-filter-panel-buttons-events",[or(dk.disable(),function(n,t){u(i,n)}),or(dk.enable(),function(n,t){a(i,n)})])])})}function w(t,e,o){return function(n){return function(n,t,e,o){return cC(n,t,e,o)}(n,t,e,o)}}function S(n){return b(n,function(a,n,t){var e=j.getOpt(a),o=W.getOpt(a),r=U.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(n){var t=Qf.getValue(u).x()/100,e=Qf.getValue(n).x()/100,o=Qf.getValue(i).x()/100,r=w(t,e,o);s(a,r)})})})},0,100,200)}function C(t,e,o){return function(n){i(n,ck.swap(),{transform:e,swap:function(){Q.getOpt(n).each(function(n){lg.set(n,[t]),o(n)})}})}}var k={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},O=Z,T=[d(),m(),n("Apply",function(n){var t=p();l(n,t),r.hideCrop()},!1,!0)],E=Hb.sketch({dom:k,components:T.map(function(n){return n.asSpec()}),containerBehaviours:va([qd("image-tools-crop-buttons-events",[or(dk.disable(),function(n,t){u(T,n)}),or(dk.enable(),function(n,t){a(T,n)})])])}),B=pm($C({name:"size",label:on.none(),constrain:!0,disabled:!1},c)),D=[d(),m(),B,m(),n("Apply",function(o){B.getOpt(o).each(function(n){var t=Qf.getValue(n),e=function(t,e){return function(n){return kC(n,t,e)}}(parseInt(t.width,10),parseInt(t.height,10));l(o,e)})},!1,!0)],_=Hb.sketch({dom:k,components:D.map(function(n){return n.asSpec()}),containerBehaviours:va([qd("image-tools-resize-buttons-events",[or(dk.disable(),function(n,t){u(D,n)}),or(dk.enable(),function(n,t){a(D,n)})])])}),A=h(CC,"h"),M=h(CC,"v"),F=h(OC,-90),I=h(OC,90),R=[d(),m(),t("flip-horizontally","Flip horizontally",function(n){v(n,A)},!1),t("flip-vertically","Flip vertically",function(n){v(n,M)},!1),t("rotate-left","Rotate counterclockwise",function(n){v(n,F)},!1),t("rotate-right","Rotate clockwise",function(n){v(n,I)},!1),m(),g()],V=Hb.sketch({dom:k,components:R.map(function(n){return n.asSpec()}),containerBehaviours:va([qd("image-tools-fliprotate-buttons-events",[or(dk.disable(),function(n,t){u(R,n)}),or(dk.enable(),function(n,t){a(R,n)})])])}),N=[d(),m(),g()],H=Hb.sketch({dom:k,components:N.map(function(n){return n.asSpec()})}),P=x("Brightness",wC,-100,0,100),z=x("Contrast",SC,-100,0,100),L=x("Gamma",xC,-100,0,100),j=S("R"),U=S("G"),W=S("B"),G=[d(),j,U,W,g()],X=Hb.sketch({dom:k,components:G.map(function(n){return n.asSpec()})}),Y=on.some(yC),q=on.some(bC),K=[t("crop","Crop",C(E,on.none(),function(n){r.showCrop()}),!1),t("resize","Resize",C(_,on.none(),function(n){B.getOpt(n).each(function(n){var t=r.getMeasurements(),e=t.width,o=t.height;Qf.setValue(n,{width:e,height:o})})}),!1),t("orientation","Orientation",C(V,on.none(),O),!1),t("brightness","Brightness",C(P,on.none(),O),!1),t("sharpen","Sharpen",C(H,Y,O),!1),t("contrast","Contrast",C(z,on.none(),O),!1),t("color-levels","Color levels",C(X,on.none(),O),!1),t("gamma","Gamma",C(L,on.none(),O),!1),t("invert","Invert",C(H,q,O),!1)],J=Hb.sketch({dom:k,components:K.map(function(n){return n.asSpec()})}),$=Hb.sketch({dom:{tag:"div"},components:[J],containerBehaviours:va([lg.config({})])}),Q=pm($);return{memContainer:Q,getApplyButton:function(n){return Q.getOpt(n).map(function(n){var t=n.components()[0];return t.components()[t.components().length-1]})}}}var ZC=Vr("toolbar.button.execute"),nk={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},tk=Vr("update-menu-text"),ek=Vr("update-menu-icon"),ok={type:"separator"},rk=function(n,r){var t=MC(cn(n)?n.split(" "):n,r);return C(t,function(n,t){var e=function(n){if(AC(n))return n;var t=Nn(n,"value").getOrThunk(function(){return Vr("generated-menu-item")});return Dn({value:t},n)}(t),o=FC(e,r);return{menus:Dn(n.menus,o.menus),items:[o.item].concat(n.items),expansions:Dn(n.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},ik=nn([St("field1Name","field1"),St("field2Name","field2"),Ku("onLockedChange"),Gu(["lockClass"]),St("locked",!1),Zf("coupledFieldBehaviours",[Ql,Qf])]),uk=nn([XC("field1","field2"),XC("field2","field1"),yl({factory:Ug,schema:[ct("dom")],name:"lock",overrides:function(n){return{buttonBehaviours:va([wg.config({selected:n.locked,toggleClass:n.markers.lockClass,aria:{mode:"pressed"}})])}}})]),ak=Dl({name:"FormCoupledInputs",configFields:ik(),partFields:uk(),factory:function(o,n,t,e){return{uid:o.uid,dom:o.dom,components:n,behaviours:nl(o.coupledFieldBehaviours,[Ql.config({find:on.some}),Qf.config({store:{mode:"manual",getValue:function(n){var t,e=Qs(n,o,["field1","field2"]);return(t={})[o.field1Name]=Qf.getValue(e.field1()),t[o.field2Name]=Qf.getValue(e.field2()),t},setValue:function(n,t){var e=Qs(n,o,["field1","field2"]);$(t,o.field1Name)&&Qf.setValue(e.field1(),t[o.field1Name]),$(t,o.field2Name)&&Qf.setValue(e.field2(),t[o.field2Name])}}})]),apis:{getField1:function(n){return Ys(n,o,"field1")},getField2:function(n){return Ys(n,o,"field2")},getLock:function(n){return Ys(n,o,"lock")}}}},apis:{getField1:function(n,t){return n.getField1(t)},getField2:function(n,t){return n.getField2(t)},getLock:function(n,t){return n.getLock(t)}}}),ck={undo:nn(Vr("undo")),redo:nn(Vr("redo")),zoom:nn(Vr("zoom")),back:nn(Vr("back")),apply:nn(Vr("apply")),swap:nn(Vr("swap")),transform:nn(Vr("transform")),tempTransform:nn(Vr("temp-transform")),transformApply:nn(Vr("transform-apply"))},sk=nn("save-state"),fk=nn("disable"),lk=nn("enable"),dk={formActionEvent:ty,saveState:sk,disable:fk,enable:lk},mk=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),gk=tinymce.util.Tools.resolve("tinymce.geom.Rect"),pk=tinymce.util.Tools.resolve("tinymce.util.Observable"),hk=tinymce.util.Tools.resolve("tinymce.util.Tools"),vk=tinymce.util.Tools.resolve("tinymce.util.VK");function bk(n){var t,e;if(n.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<t.length;e++)n[t[e]]=n.changedTouches[0][t[e]]}function yk(n,r){var i,u,t,a,c,f,l,d=r.document||H.document;r=r||{};var m=d.getElementById(r.handle||n);t=function(n){var t,e,o=function s(n){var t,e,o,r,i,u,a,c=Math.max;return t=n.documentElement,e=n.body,o=c(t.scrollWidth,e.scrollWidth),r=c(t.clientWidth,e.clientWidth),i=c(t.offsetWidth,e.offsetWidth),u=c(t.scrollHeight,e.scrollHeight),a=c(t.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(t.offsetHeight,e.offsetHeight)?a:u}}(d);bk(n),n.preventDefault(),u=n.button,t=m,f=n.screenX,l=n.screenY,e=H.window.getComputedStyle?H.window.getComputedStyle(t,null).getPropertyValue("cursor"):t.runtimeStyle.cursor,i=mk("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),mk(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(n)},c=function(n){if(bk(n),n.button!==u)return a(n);n.deltaX=n.screenX-f,n.deltaY=n.screenY-l,n.preventDefault(),r.drag(n)},a=function(n){bk(n),mk(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(n)},this.destroy=function(){mk(m).off()},mk(m).on("mousedown touchstart",t)}function xk(t){function u(n,s){c.getOpt(n).each(function(n){var e=l.get(),o=du(n.element()),r=cu(n.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),t={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};fi(s,t),f.getOpt(n).each(function(n){fi(n.element(),t)}),d.get().each(function(n){var t=m.get();n.setRect({x:t.x*e+a,y:t.y*e+c,w:t.w*e,h:t.h*e}),n.setClampRect({x:a,y:c,w:i,h:u}),n.setViewPortRect({x:0,y:0,w:o,h:r})})})}function e(n,t){var i=Be.fromTag("img");return Dr(i,"src",t),function(e){return new Rp(function(n){var t=function(){e.removeEventListener("load",t),n(e)};e.complete?n(e):e.addEventListener("load",t)})}(i.dom()).then(function(){return c.getOpt(n).map(function(n){var t=ou({element:i});lg.replaceAt(n,1,on.some(t));var e=a.get(),o={x:0,y:0,w:i.dom().naturalWidth,h:i.dom().naturalHeight};a.set(o);var r=gk.inflate(o,-20,-20);return m.set(r),e.w===o.w&&e.h===o.h||function(n,u){c.getOpt(n).each(function(n){var t=du(n.element()),e=cu(n.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(t/o,e/r);1<=i?l.set(1):l.set(i)})}(n,i),u(n,i),i})})}var f=pm({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),l=Te(1),d=Te(on.none()),m=Te({x:0,y:0,w:1,h:1}),a=Te({x:0,y:0,w:1,h:1}),n=Hb.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[f.asSpec(),{dom:{tag:"img",attributes:{src:t}}},{dom:{tag:"div"},behaviours:va([qd("image-panel-crop-events",[Fi(function(n){c.getOpt(n).each(function(n){var t=n.element().dom(),e=kk({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},t,function(){});e.toggleVisibility(!1),e.on("updateRect",function(n){var t=n.rect,e=l.get(),o={x:Math.round(t.x/e),y:Math.round(t.y/e),w:Math.round(t.w/e),h:Math.round(t.h/e)};m.set(o)}),d.set(on.some(e))})})])])}],containerBehaviours:va([lg.config({}),qd("image-panel-events",[Fi(function(n){e(n,t)})])])}),c=pm(n);return{memContainer:c,updateSrc:e,zoom:function(n,t){var e=l.get(),o=0<t?Math.min(2,e+.1):Math.max(.1,e-.1);l.set(o),c.getOpt(n).each(function(n){var t=n.components()[1].element();u(n,t)})},showCrop:function(){d.get().each(function(n){n.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(n){n.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var n=a.get();return{width:n.w,height:n.h}}}}function wk(n,t,e,o,r){return zC({name:n,icon:on.some(t),disabled:e,tooltip:on.some(n),primary:!1,borderless:!1},o,r)}function Sk(n,t){t?wh.enable(n):wh.disable(n)}var Ck=0,kk=function(s,e,f,o,r){var l,t,i,u="tox-",a="tox-crid-"+Ck++,c=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}];i=["top","right","bottom","left"];var d=function(n,t){return{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}},m=function(n,t){return{x:t.x-n.x,y:t.y-n.y,w:t.w,h:t.h}};function g(n,t,e,o){var r,i,u,a,c;r=t.x,i=t.y,u=t.w,a=t.h,r+=e*n.deltaX,i+=o*n.deltaY,(u+=e*n.deltaW)<20&&(u=20),(a+=o*n.deltaH)<20&&(a=20),c=s=gk.clamp({x:r,y:i,w:u,h:a},f,"move"===n.name),c=m(f,c),l.fire("updateRect",{rect:c}),v(c)}function p(t){function n(n,t){t.h<0&&(t.h=0),t.w<0&&(t.w=0),mk("#"+a+"-"+n,o).css({left:t.x,top:t.y,width:t.w,height:t.h})}hk.each(c,function(n){mk("#"+a+"-"+n.name,o).css({left:t.w*n.xMul+t.x,top:t.h*n.yMul+t.y})}),n("top",{x:e.x,y:e.y,w:e.w,h:t.y-e.y}),n("right",{x:t.x+t.w,y:t.y,w:e.w-t.x-t.w+e.x,h:t.h}),n("bottom",{x:e.x,y:t.y+t.h,w:e.w,h:e.h-t.y-t.h+e.y}),n("left",{x:e.x,y:t.y,w:t.x-e.x,h:t.h}),n("move",t)}function h(n){p(s=n)}function v(n){h(d(f,n))}return function b(){mk('<div id="'+a+'" class="'+u+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),hk.each(i,function(n){mk("#"+a,o).append('<div id="'+a+"-"+n+'"class="'+u+'croprect-block" style="display: none" data-mce-bogus="all">')}),hk.each(c,function(n){mk("#"+a,o).append('<div id="'+a+"-"+n.name+'" class="'+u+"croprect-handle "+u+"croprect-handle-"+n.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+n.label+'" aria-grabbed="false" title="'+n.label+'">')}),t=hk.map(c,function n(t){var e;return new yk(a,{document:o.ownerDocument,handle:a+"-"+t.name,start:function(){e=s},drag:function(n){g(t,e,n.deltaX,n.deltaY)}})}),p(s),mk(o).on("focusin focusout",function(n){mk(n.target).attr("aria-grabbed","focus"===n.type?"true":"false")}),mk(o).on("keydown",function(t){var i;function n(n,t,e,o,r){n.stopPropagation(),n.preventDefault(),g(i,e,o,r)}switch(hk.each(c,function(n){if(t.target.id===a+"-"+n.name)return i=n,!1}),t.keyCode){case vk.LEFT:n(t,0,s,-10,0);break;case vk.RIGHT:n(t,0,s,10,0);break;case vk.UP:n(t,0,s,0,-10);break;case vk.DOWN:n(t,0,s,0,10);break;case vk.ENTER:case vk.SPACEBAR:t.preventDefault(),r()}})}(),l=hk.extend({toggleVisibility:function y(n){var t;t=hk.map(c,function(n){return"#"+a+"-"+n.name}).concat(hk.map(i,function(n){return"#"+a+"-"+n})).join(","),n?mk(t,o).show():mk(t,o).hide()},setClampRect:function x(n){f=n,p(s)},setRect:h,getInnerRect:function(){return m(f,s)},setInnerRect:v,setViewPortRect:function w(n){e=n,p(s)},destroy:function n(){hk.each(t,function(n){n.destroy()}),t=[]}},pk)};function Ok(n){var t=Te(n),e=Te(on.none()),o=function s(){var e=[],o=-1;function n(){return 0<o}function t(){return-1!==o&&o<e.length-1}return{data:e,add:function r(n){var t;return t=e.splice(++o),e.push(n),{state:n,removed:t}},undo:function i(){if(n())return e[--o]},redo:function u(){if(t())return e[++o]},canUndo:n,canRedo:t}}();function r(n){t.set(n)}function i(n){H.URL.revokeObjectURL(n.url)}function u(n){var t=a(n);return r(t),function(n){hk.each(n,i)}(o.add(t).removed),t.url}o.add(n);var a=function(n){return{blob:n,url:H.URL.createObjectURL(n)}},c=function(){e.get().each(i),e.set(on.none())};return{getBlobState:function(){return t.get()},setBlobState:r,addBlobState:u,getTempState:function(){return e.get().fold(function(){return t.get()},function(n){return n})},updateTempState:function(n){var t=a(n);return c(),e.set(on.some(t)),t.url},addTempState:function(n){var t=a(n);return e.set(on.some(t)),t.url},applyTempState:function(t){return e.get().fold(function(){},function(n){u(n.blob),t()})},destroyTempState:c,undo:function(){var n=o.undo();return r(n),n.url},redo:function(){var n=o.redo();return r(n),n.url},getHistoryStates:function(){return{undoEnabled:o.canUndo(),redoEnabled:o.canRedo()}}}}function Tk(n,t){function i(n){var t=s.getHistoryStates();m.updateButtonUndoStates(n,t.undoEnabled,t.redoEnabled),Yt(n,dk.formActionEvent,{name:dk.saveState(),value:t.undoEnabled})}function u(n){return n.toBlob()}function a(n){Yt(n,dk.formActionEvent,{name:dk.disable(),value:{}})}function r(t,n,e,o,r){return a(t),function(n){return nC(n)}(n).then(e).then(u).then(o).then(function(n){return l(t,n).then(function(n){return i(t),r(),f(t),n})})["catch"](function(n){return H.console.log(n),f(t),n})}function c(n,t,e){var o=s.getBlobState().blob;r(n,o,t,function(n){return s.updateTempState(n)},e)}var s=Ok(n.currentState),f=function(n){e.getApplyButton(n).each(function(n){wh.enable(n)}),Yt(n,dk.formActionEvent,{name:dk.enable(),value:{}})},l=function(n,t){return a(n),o.updateSrc(n,t)},d=function(n){var t=s.getBlobState().url;return s.destroyTempState(),i(n),t},o=xk(n.currentState.url),m=function(n){var o=pm(wk("Undo","undo",!0,function(n){Yt(n,ck.undo(),{direction:1})},n)),r=pm(wk("Redo","redo",!0,function(n){Yt(n,ck.redo(),{direction:1})},n));return{container:Hb.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),wk("Zoom in","zoom-in",!1,function(n){Yt(n,ck.zoom(),{direction:1})},n),wk("Zoom out","zoom-out",!1,function(n){Yt(n,ck.zoom(),{direction:-1})},n)]}),updateButtonUndoStates:function(n,t,e){o.getOpt(n).each(function(n){Sk(n,t)}),r.getOpt(n).each(function(n){Sk(n,e)})}}}(t),e=QC(o,t);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[e.memContainer.asSpec(),o.memContainer.asSpec(),m.container],behaviours:va([Qf.config({store:{mode:"manual",getValue:function(){return s.getBlobState()}}}),qd("image-tools-events",[or(ck.undo(),function(t,n){var e=s.undo();l(t,e).then(function(n){f(t),i(t)})}),or(ck.redo(),function(t,n){var e=s.redo();l(t,e).then(function(n){f(t),i(t)})}),or(ck.zoom(),function(n,t){var e=t.event().direction();o.zoom(n,e)}),or(ck.back(),function(n,t){!function(t){var n=d(t);l(t,n).then(function(n){f(t)})}(n),t.event().swap()(),o.hideCrop()}),or(ck.apply(),function(n,t){s.applyTempState(function(){d(n),t.event().swap()()})}),or(ck.transform(),function(n,t){return c(n,t.event().transform(),Z)}),or(ck.tempTransform(),function(n,t){return function(n,t){var e=s.getTempState().blob;r(n,e,t,function(n){return s.addTempState(n)},Z)}(n,t.event().transform())}),or(ck.transformApply(),function(n,t){return function(e,n,t){var o=s.getBlobState().blob;r(e,o,n,function(n){var t=s.addBlobState(n);return d(e),t},t)}(n,t.event().transform(),t.event().swap())}),or(ck.swap(),function(t,n){!function(n){m.updateButtonUndoStates(n,!1,!1)}(t);var e=n.event().transform(),o=n.event().swap();e.fold(function(){o()},function(n){c(t,n,o)})})]),vS()])}}function Ek(e,t){var n=e.label.map(function(n){return wy(n,t)}),o=[wh.config({disabled:e.disabled}),sg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(n){return Xt(n,ey),on.some(!0)}}),qd("textfield-change",[or(go(),function(n,t){Yt(n,Qb,{name:e.name})}),or(So(),function(n,t){Yt(n,Qb,{name:e.name})})]),Ny.config({})],r=e.validation.map(function(o){return Iy.config({getRoot:function(n){return hr(n.element())},invalidClass:"tox-invalid",validator:{validate:function(n){var t=Qf.getValue(n),e=o.validator(t);return Dy(!0===e?an.value(t):an.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(nn({}),function(n){return{placeholder:t.translate(n)}}),u=e.inputMode.fold(nn({}),function(n){return{inputmode:n}}),a=P(P({},i),u),c=sy.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:va(z([o,r])),selectOnFocus:!1,factory:ly}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),f=[wh.config({disabled:e.disabled,onDisabled:function(n){sy.getField(n).each(wh.disable)},onEnabled:function(n){sy.getField(n).each(wh.enable)}})];return xy(n,c,s,f)}function Bk(n){var t=Te(null);return Zi({readState:function(){return{timer:null!==t.get()?"set":"unset"}},setTimer:function(n){t.set(n)},cancel:function(){var n=t.get();null!==n&&n.cancel()}})}function Dk(n,t,e){var o=Qf.getValue(e);Qf.setValue(t,o),xT(t)}function _k(n,t){var e=n.element(),o=hi(e),r=e.dom();"number"!==_r(e,"type")&&t(r,o)}function Ak(n,t,e){if(n.selectsOver){var o=Qf.getValue(t),r=n.getDisplayText(o),i=Qf.getValue(e);return 0===n.getDisplayText(i).indexOf(r)?on.some(function(){Dk(0,t,e),function(n,e){_k(n,function(n,t){return n.setSelectionRange(e,t.length)})}(t,r.length)}):on.none()}return on.none()}function Mk(n){return OT(By(n))}function Fk(n){return{type:"menuitem",value:n.url,text:n.title,meta:{attach:n.attach},onAction:function(){}}}function Ik(n,t){return{type:"menuitem",value:t,text:n,meta:{attach:undefined},onAction:function(){}}}function Rk(n,t){return function(n){return w(n,Fk)}(function(t,n){return S(n,function(n){return n.type===t})}(n,t))}function Vk(n,t){var e=n.toLowerCase();return S(t,function(n){var t=n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.text;return Vt(t.toLowerCase(),e)||Vt(n.value.toLowerCase(),e)})}function Nk(e,n,o){var t=Qf.getValue(n),r=t.meta.text!==undefined?t.meta.text:t.value;return o.getLinkInformation().fold(function(){return[]},function(n){var t=Vk(r,function(n){return w(n,function(n){return Ik(n,n)})}(o.getHistory(e)));return"file"===e?function(n){return k(n,function(n,t){return 0===n.length||0===t.length?n.concat(t):n.concat(ET,t)},[])}([t,Vk(r,function(n){return Rk("header",n.targets)}(n)),Vk(r,z([function(n){return on.from(n.anchorTop).map(function(n){return Ik("<top>",n)}).toArray()}(n),function(n){return Rk("anchor",n.targets)}(n),function(n){return on.from(n.anchorBottom).map(function(n){return Ik("<bottom>",n)}).toArray()}(n)]))]):t})}function Hk(r,o,i){function u(n){var t=Qf.getValue(n);i.addToHistory(t.value,r.filetype)}var n,t,e,a,c,s=o.shared.providers,f=sy.parts().field({factory:kT,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":BT,type:"url"},minChars:0,responseTime:0,fetch:function(n){var t=Nk(r.filetype,n,i),e=IC(t,Ah.BUBBLE_TO_SANDBOX,o,!1);return Dy(e)},getHotspot:function(n){return h.getOpt(n)},onSetValue:function(n,t){n.hasConfigured(Iy)&&Iy.run(n).get(Z)},typeaheadBehaviours:va(z([i.getValidationHandler().map(function(e){return Iy.config({getRoot:function(n){return hr(n.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(n,t){d.getOpt(n).each(function(n){Dr(n.element(),"title",s.translate(t))})}},validator:{validate:function(n){var t=Qf.getValue(n);return TT(function(o){e({type:r.filetype,url:t.value},function(n){if("invalid"===n.status){var t=an.error(n.message);o(t)}else{var e=an.value(n.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[wh.config({disabled:r.disabled}),Ny.config({}),qd("urlinput-events",z(["file"===r.filetype?[or(go(),function(n){Yt(n,Qb,{name:r.name})})]:[],[or(po(),function(n){Yt(n,Qb,{name:r.name}),u(n)}),or(So(),function(n){Yt(n,Qb,{name:r.name}),u(n)})]]))]])),eventOrder:(n={},n[go()]=["streaming","urlinput-events","invalidating"],n),model:{getDisplayText:function(n){return n.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:xv(0,0,"normal")},onExecute:function(n,t,e){Yt(t,ey,{})},onItemExecute:function(n,t,e,o){u(n),Yt(n,Qb,{name:r.name})}}),l=r.label.map(function(n){return wy(n,s)}),d=pm((t="invalid",e=on.some(BT),void 0===(a="warning")&&(a=t),void 0===c&&(c=t),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],innerHtml:vm(a,s.icons),attributes:P({title:s.translate(c),"aria-live":"polite"},e.fold(function(){return{}},function(n){return{id:n}}))}})),m=pm({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Vr("browser.url.event"),h=pm({dom:{tag:"div",classes:["tox-control-wrap"]},components:[f,m.asSpec()],behaviours:va([wh.config({disabled:r.disabled})])}),v=pm(jC({name:r.name,icon:on.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(n){return Xt(n,p)},s,[],["tox-browse-url"]));return sy.sketch({dom:Py([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:z([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:va([wh.config({disabled:r.disabled,onDisabled:function(n){sy.getField(n).each(wh.disable),v.getOpt(n).each(wh.disable)},onEnabled:function(n){sy.getField(n).each(wh.enable),v.getOpt(n).each(wh.enable)}}),qd("url-input-events",[or(p,function(o){Ql.getCurrent(o).each(function(t){var e=Qf.getValue(t);g.each(function(n){n(e).get(function(n){Qf.setValue(t,n),Yt(o,Qb,{name:r.name})})})})})])])})}function Pk(u,t){function n(e){return function(t,n){ku(n.event().target(),"[data-collection-item-value]").each(function(n){e(t,n,_r(n,"data-collection-item-value"))})}}var e=u.label.map(function(n){return wy(n,t)}),o=[or(co(),n(function(n,t){xa(t)})),or(Eo(),n(function(n,t,e){Yt(n,ty,{name:u.name,value:e})})),or(so(),n(function(n,t,e){Cu(n.element(),"."+lh).each(function(n){ei(n,lh)}),ni(t,lh)})),or(fo(),n(function(n,t,e){Cu(n.element(),"."+lh).each(function(n){ei(n,lh)})})),Vi(n(function(n,t,e){Yt(n,ty,{name:u.name,value:e})}))],r=sy.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:l},behaviours:va([lg.config({}),Qf.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,n){!function(n,t){var e=w(t,function(n){var t=eh.translate(n.text),e=1===u.columns?'<div class="tox-collection__item-label">'+t+"</div>":"",o='<div class="tox-collection__item-icon">'+n.icon+"</div>",r={_:" "," - ":" ","-":" "},i=t.replace(/\_| \- |\-/g,function(n){return r[n]});return'<div class="tox-collection__item" tabindex="-1" data-collection-item-value="'+function(n){return'"'===n?"&quot;":n}(n.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),o=1<u.columns&&"auto"!==u.columns?x(e,u.columns):[e],r=w(o,function(n){return'<div class="tox-collection__group">'+n.join("")+"</div>"});Er(n.element(),r.join(""))}(o,n),"auto"===u.columns&&op(o,5,"tox-collection__item").each(function(n){var t=n.numRows,e=n.numColumns;sg.setGridSize(o,t,e)}),Xt(o,uy)}}),Ny.config({}),sg.config(function(n,t){return 1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===t?".tox-swatches__row":".tox-collection__group",cell:"color"===t?"."+ih:"."+rh}}}(u.columns,"normal")),qd("collection-events",o)])});return xy(e,r,["tox-form__group--collection"],[])}function zk(r){return function(t,e,o){return Nn(e,"name").fold(function(){return r(e,o)},function(n){return t.field(n,r(e,o))})}}function Lk(t,n,e){var o=Dn(e,{shared:{interpreter:function(n){return AT(t,n,o)}}});return AT(t,n,o)}function jk(n){return{colorPicker:function(e){return function(n,t){Lv.colorPickerDialog(e)(n,t)}}(n),hasCustomColors:function(n){return function(){return Av(n)}}(n),getColors:function(n){return function(){return Mv(n)}}(n),getColorCols:function(n){return function(){return Lv.getColorCols(n)}}(n)}}function Uk(e){return function(n){return on.from(n.getParam("style_formats")).filter(fn)}(e).map(function(n){var t=function(t,n){function e(n){bn(n,function(n){t.formatter.has(n.name)||t.formatter.register(n.name,n.format)})}var o=VT(n);return t.formatter?e(o.customFormats):t.on("init",function(){e(o.customFormats)}),o.formats}(e,n);return function(n){return n.getParam("style_formats_merge",!1,"boolean")}(e)?RT.concat(t):t}).getOr(RT)}function Wk(n,t,e){var o={type:"formatter",isSelected:t(n.format),getStylePreview:e(n.format)};return Dn(n,o)}function Gk(r,n,i,u){var o=function(n){return w(n,function(n){var t=wn(n);if($(n,"items")){var e=o(n.items);return Dn(function(n){var t={type:"submenu",isSelected:nn(!1),getStylePreview:function(){return on.none()}};return Dn(n,t)}(n),{getStyleItems:function(){return e}})}return $(n,"format")?function(n){return Wk(n,i,u)}(n):1===t.length&&vn(t,"title")?Dn(n,{type:"separator"}):function(n){var t=Vr(n.title),e={type:"formatter",format:t,isSelected:i(t),getStylePreview:u(t)},o=Dn(n,e);return r.formatter.register(t,o),o}(n)})};return o(n)}function Xk(t){return function(n){if(n&&1===n.nodeType){if(n.contentEditable===t)return!0;if(n.getAttribute("data-mce-contenteditable")===t)return!0}return!1}}function Yk(n,t,e,o,r){return{type:n,title:t,url:e,level:o,attach:r}}function qk(n){return n.innerText||n.textContent}function Kk(n){return function(n){return n&&"A"===n.nodeName&&(n.id||n.name)!==undefined}(n)&&zT(n)}function Jk(n){return n&&/^(H[1-6])$/.test(n.nodeName)}function $k(n){return Jk(n)&&zT(n)}function Qk(n){var t=function(n){return n.id?n.id:Vr("h")}(n);return Yk("header",qk(n),"#"+t,function(n){return Jk(n)?parseInt(n.nodeName.substr(1),10):0}(n),function(){n.id=t})}function Zk(n){var t=n.id||n.name,e=qk(n);return Yk("anchor",e||"#"+t,"#"+t,0,Z)}function nO(n){return function(n,t){return w(Pc(Be.fromDom(t),n),function(n){return n.dom()})}("h1,h2,h3,h4,h5,h6,a:not([href])",n)}function tO(n){return 0<NT(n.title).length}function eO(n){return cn(n)&&/^https?/.test(n)}function oO(n){return sn(n)&&F(n,function(n){return!function(n){return fn(n)&&n.length<=5&&B(n,eO)}(n)}).isNone()}function rO(){var n,t=H.localStorage.getItem(jT);if(null===t)return{};try{n=JSON.parse(t)}catch(e){if(e instanceof SyntaxError)return H.console.log("Local storage "+jT+" was not valid JSON",e),{};throw e}return oO(n)?n:(H.console.log("Local storage "+jT+" was not valid format",n),{})}function iO(n){var t=rO();return Object.prototype.hasOwnProperty.call(t,n)?t[n]:[]}function uO(t,n){if(eO(t)){var e=rO(),o=Object.prototype.hasOwnProperty.call(e,n)?e[n]:[],r=S(o,function(n){return n!==t});e[n]=[t].concat(r).slice(0,5),function(n){if(!oO(n))throw new Error("Bad format for history:\n"+JSON.stringify(n));H.localStorage.setItem(jT,JSON.stringify(n))}(e)}}function aO(n){return!!n}function cO(n){return L(hk.makeMap(n,/[, ]/),aO)}function sO(n,t,e){var o=function(n,t){return UT.call(n,t)?on.some(n[t]):on.none()}(n,t).getOr(e);return cn(o)?on.some(o):on.none()}function fO(n){return on.some(n.file_picker_callback).filter(dn)}function lO(n,t){var e=function(n){var t=on.some(n.file_picker_types).filter(aO),e=on.some(n.file_browser_callback_types).filter(aO),o=t.or(e).map(cO);return fO(n).fold(function(){return!1},function(n){return o.fold(function(){return!0},function(n){return 0<wn(n).length&&n})})}(n);return ln(e)?e?fO(n):on.none():e[t]?fO(n):on.none()}function dO(t){return{getHistory:iO,addToHistory:uO,getLinkInformation:function(){return function(n){return!1===n.settings.typeahead_urls?on.none():on.some({targets:LT(n.getBody()),anchorTop:sO(n.settings,"anchor_top","#top").getOrUndefined(),anchorBottom:sO(n.settings,"anchor_bottom","#bottom").getOrUndefined()})}(t)},getValidationHandler:function(){return function(n){return on.from(n.settings.file_picker_validator_handler).filter(dn).orThunk(function(){return on.from(n.settings.filepicker_validator_handler).filter(dn)})}(t)},getUrlPicker:function(n){return function(r,i){return lO(r.settings,i).map(function(o){return function(t){return By(function(e){var n=hk.extend({filetype:i},on.from(t.meta).getOr({}));o.call(r,function(n,t){if(!cn(n))throw new Error("Expected value to be string");if(t!==undefined&&!sn(t))throw new Error("Expected meta to be a object");e({value:n,meta:t})},t.value,n)})}})}(t,n)}}}function mO(n,t,e,o){var r=Te(!1),i={shared:{providers:{icons:function(){return t.ui.registry.getAll().icons},menuItems:function(){return t.ui.registry.getAll().menuItems},translate:eh.translate},interpreter:function(n){return function(n,t){return AT(_T,n,t)}(n,i)},anchors:IT(t,e,o),getSink:function(){return an.value(n)}},urlinput:dO(t),styleselect:function(e){function o(n){return function(){return e.formatter.match(n)}}function r(t){return function(){var n=e.formatter.get(t);return n!==undefined?on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:e.formatter.getCssText(t)}):on.none()}}var i=function(n){var t=n.items;return t!==undefined&&0<t.length?E(t,i):[n.format]},u=Te([]),a=Te([]),c=Te([]),s=Te([]),f=Te(!1);e.on("init",function(){var n=Uk(e),t=Gk(e,n,o,r);u.set(t),a.set(E(t,i))}),e.on("addStyleModifications",function(n){var t=Gk(e,n.items,o,r);c.set(t),f.set(n.replace),s.set(E(t,i))});return{getData:function(){var n=f.get()?[]:u.get(),t=c.get();return n.concat(t)},getFlattenedKeys:function(){var n=f.get()?[]:a.get(),t=s.get();return n.concat(t)}}}(t),colorinput:jk(t),dialog:function(n){return{isDraggableModal:function(n){return function(){return function(n){return n.getParam("draggable_modal",!1,"boolean")}(n)}}(n)}}(t),isContextMenuOpen:function(){return r.get()},setContextMenuState:function(n){return r.set(n)}};return i}function gO(n,t,o){var e=function(n,e){return k(n,function(t,n){return e(n,t.len).fold(nn(t),function(n){return{len:n.finish(),list:t.list.concat([n])}})},{len:0,list:[]}).list}(n,function(n,t){var e=o(n);return on.some({element:nn(n),start:nn(t),finish:nn(t+e),width:nn(e)})}),r=S(e,function(n){return n.finish()<=t}),i=C(r,function(n,t){return n+t.width()},0),u=e.slice(r.length);return{within:nn(r),extra:nn(u),withinWidth:nn(i)}}function pO(n){return w(n,function(n){return n.element()})}function hO(n,t,e,o){var r=function(n,t,e){var o=gO(t,n,e);return 0===o.extra().length?on.some(o):on.none()}(n,t,e).getOrThunk(function(){return gO(t,n-e(o),e)}),i=r.within(),u=r.extra(),a=r.withinWidth();return 1===u.length&&u[0].width()<=e(o)?function(n,t,e){var o=pO(n.concat(t));return qT(o,[],e)}(i,u,a):1<=u.length?function(n,t,e,o){var r=pO(n).concat([e]);return qT(r,pO(t),o)}(i,u,o,a):function(n,t,e){return qT(pO(n),[],e)}(i,0,a)}function vO(n,t){var e=w(t,function(n){return uu(n)});YT.setGroups(n,e)}function bO(n,t,e,o){var r=qs(n,t,"primary"),i=Ys(n,t,"overflow-button"),u=jy.getCoupled(n,"overflowGroup");si(r.element(),"visibility","hidden");var a=function(n,t){return n.bind(function(t){return Sa(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()})}).orThunk(function(){return t.filter(pg.isFocused)})}(e,i);e.each(function(n){YT.setGroups(n,[])});var c=t.builtGroups.get();vO(r,c.concat([u]));var s=du(r.element()),f=hO(s,c,function(n){return du(n.element())},u);0===f.extra().length?(lg.remove(r,u),e.each(function(n){YT.setGroups(n,[])})):(vO(r,f.within()),e.each(function(n){vO(n,f.extra())})),gi(r.element(),"visibility"),pi(r.element()),e.each(function(t){i.each(function(n){return wg.set(n,o(t))}),a.each(pg.focus)})}function yO(o,n,t,e,r){var i="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:n,behaviours:Is(o.splitToolbarBehaviours,[jy.config({others:P(P({},r.coupling),{overflowGroup:function(t){return $T.sketch(P(P({},e["overflow-group"]()),{items:[Ug.sketch(P(P({},e["overflow-button"]()),{action:function(n){Xt(t,i)}}))]}))}})}),qd("toolbar-toggle-events",[or(i,function(n){r.apis.toggle(n)})])]),apis:P({setGroups:function(n,t){!function(n,t){var e=w(t,n.getSystem().build);o.builtGroups.set(e)}(n,t),r.apis.refresh(n)},getMoreButton:function(n){return function(n){return Ys(n,o,"overflow-button")}(n)}},r.apis),domModification:{attributes:{role:"group"}}}}function xO(n){return n.getSystem().isConnected()}function wO(n,t,e){var o=t.lazySink(n).getOrDie(),r=t.getAnchor(n),i=t.getOverflowBounds.map(function(n){return n()});Df.positionWithinBounds(o,r,e,i)}function SO(t,e){var n=Pf.getState(jy.getCoupled(t,"sandbox"));bO(t,e,n,xO),n.each(function(n){return wO(t,e,n)})}function CO(t,e){Pf.getState(jy.getCoupled(t,"sandbox")).each(function(n){return wO(t,e,n)})}function kO(t,n){return n.getAnimationRoot.fold(function(){return t.element()},function(n){return n(t)})}function OO(n){return n.dimension.property}function TO(n,t){return n.dimension.getDimension(t)}function EO(n,t){var e=kO(n,t);ii(e,[t.shrinkingClass,t.growingClass])}function BO(n,t){ei(n.element(),t.openClass),ni(n.element(),t.closedClass),si(n.element(),OO(t),"0px"),pi(n.element())}function DO(n,t){ei(n.element(),t.closedClass),ni(n.element(),t.openClass),gi(n.element(),OO(t))}function _O(n,t,e,o){e.setCollapsed(),si(n.element(),OO(t),TO(t,n.element())),pi(n.element()),EO(n,t),BO(n,t),t.onStartShrink(n),t.onShrunk(n)}function AO(n,t,e,o){var r=o.getOrThunk(function(){return TO(t,n.element())});e.setCollapsed(),si(n.element(),OO(t),r),pi(n.element());var i=kO(n,t);ei(i,t.growingClass),ni(i,t.shrinkingClass),BO(n,t),t.onStartShrink(n)}function MO(n,t,e){var o=TO(t,n.element());("0px"===o?_O:AO)(n,t,e,on.some(o))}function FO(n,t,e){var o=kO(n,t),r=oi(o,t.shrinkingClass),i=TO(t,n.element());DO(n,t);var u=TO(t,n.element());(r?function(){si(n.element(),OO(t),i),pi(n.element())}:function(){BO(n,t)})(),ei(o,t.shrinkingClass),ni(o,t.growingClass),DO(n,t),si(n.element(),OO(t),u),e.setExpanded(),t.onStartGrow(n)}function IO(n,t,e){var o=kO(n,t);return!0===oi(o,t.growingClass)}function RO(n,t,e){var o=kO(n,t);return!0===oi(o,t.shrinkingClass)}function VO(n){return iE.hasGrown(n)}function NO(n,t){var e=n.outerContainer;!function(n,t){var e=n.outerContainer.element();t&&(n.mothership.broadcastOn([zf()],{target:e}),n.uiMothership.broadcastOn([zf()],{target:e})),n.mothership.broadcastOn([fE],{readonly:t}),n.uiMothership.broadcastOn([fE],{readonly:t})}(n,t),Lt("*",e.element()).forEach(function(n){e.getSystem().getByDom(n).each(function(n){n.hasConfigured(wh)&&wh.set(n,t)})})}function HO(n,t){n.on("init",function(){n.readonly&&NO(t,!0)}),n.on("SwitchMode",function(){return NO(t,n.readonly)}),function(n){return n.getParam("readonly",!1,"boolean")}(n)&&n.setMode("readonly")}function PO(e){var n;return fc.config({channels:(n={},n[fE]={schema:lE,onReceive:function(n,t){e(n).each(function(n){!function(t,e){Lt("*",t.element()).forEach(function(n){t.getSystem().getByDom(n).each(function(n){n.hasConfigured(wh)&&wh.set(n,e)})})}(n,t.readonly)})}},n)})}function zO(n){var t=n.title.fold(function(){return{}},function(n){return{attributes:{title:n}}});return{dom:P({tag:"div",classes:["tox-toolbar__group"]},t),components:[$T.parts().items({})],items:n.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:va([Ny.config({}),pg.config({})])}}function LO(n){return $T.sketch(zO(n))}function jO(e,n,t){var o=Fi(function(n){var t=w(e.initGroups,LO);YT.setGroups(n,t)});return va([sg.config({mode:n,onEscape:e.onEscape,selector:".tox-toolbar__group"}),qd("toolbar-events",[o]),PO(t)])}function UO(n,t){var e=n.cyclicKeying?"cyclic":"acyclic";return{uid:n.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":zO({title:on.none(),items:[]}),"overflow-button":PC({name:"more",icon:on.some("more-drawer"),disabled:!1,tooltip:on.some("More..."),primary:!1,borderless:!1},on.none(),n.backstage.shared.providers)},splitToolbarBehaviours:jO(n,e,t)}}function WO(r){var n=UO(r,tE.getOverflow),t=tE.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return tE.sketch(P(P({},n),{lazySink:r.getSink,getAnchor:function(){return r.backstage.shared.anchors.toolbarOverflow()},getOverflowBounds:function(){var n=r.moreDrawerData.lazyHeader().element(),t=xu(n),e=function(n){return Be.fromDom(n.dom().ownerDocument.documentElement)}(n),o=xu(e);return bu(t.x()+4,o.y(),t.width()-8,o.height())},parts:P(P({},n.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"]}}}),components:[t],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}function GO(n){var t=sE.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=sE.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=UO(n,sE.getOverflow);return sE.sketch(P(P({},o),{components:[t,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(n){n.getSystem().broadcastOn([dE()],{type:"opened"})},onClosed:function(n){n.getSystem().broadcastOn([dE()],{type:"closed"})}}))}function XO(n){var t=n.cyclicKeying?"cyclic":"acyclic";return YT.sketch({uid:n.uid,dom:{tag:"div",classes:["tox-toolbar"]},components:[YT.parts().groups({})],toolbarBehaviours:jO(n,t,nn(on.none()))})}function YO(n){return tt("toolbarbutton",gE,n)}function qO(n){return tt("menubutton",hE,n)}function KO(n){return tt("ToggleButton",yE,n)}function JO(t){return{isDisabled:function(){return wh.isDisabled(t)},setDisabled:function(n){return wh.set(t,n)}}}function $O(t){return{setActive:function(n){wg.set(t,n)},isActive:function(){return wg.isOn(t)},isDisabled:function(){return wh.isDisabled(t)},setDisabled:function(n){return wh.set(t,n)}}}function QO(n,t){return n.map(function(n){return{"aria-label":t.translate(n),title:t.translate(n)}}).getOr({})}function ZO(t,e,n,o,r,i){function u(n){return eh.isRtl()&&vn(PE,n)?n+"-rtl":n}var a,c=eh.isRtl()&&t.exists(function(n){return vn(zE,n)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:QO(n,i)},components:Th([t.map(function(n){return EC(u(n),i.icons)}),e.map(function(n){return DC(n,"tox-tbtn",i)})]),eventOrder:(a={},a[ro()]=["focusing","alloy.base.behaviour","common-button-display-events"],a),buttonBehaviours:va([qd("common-button-display-events",[or(ro(),function(n,t){t.event().prevent(),Xt(n,HE)})])].concat(o.map(function(n){return ME.config({channel:n,initialData:{icon:t,text:e},renderComponents:function(n,t){return Th([n.icon.map(function(n){return EC(u(n),i.icons)}),n.text.map(function(n){return DC(n,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function nT(n,t,e){var o=Te(Z),r=ZO(n.icon,n.text,n.tooltip,on.none(),on.none(),e);return Ug.sketch({dom:r.dom,components:r.components,eventOrder:nk,buttonBehaviours:va([qd("toolbar-button-events",[function(e){return Vi(function(t,n){Cp(e,t)(function(n){Yt(t,ZC,{buttonApi:n}),e.onAction(n)})})}({onAction:n.onAction,getApi:t.getApi}),kp(t,o),Op(t,o)]),Oh(n.disabled)].concat(t.toolbarButtonBehaviours))})}function tT(t,n){function e(e){return{isDisabled:function(){return wh.isDisabled(e)},setDisabled:function(n){return wh.set(e,n)},setIconFill:function(n,t){Cu(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){Dr(n,"fill",t)})},setIconStroke:function(n,t){Cu(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){Dr(n,"stroke",t)})},setActive:function(t){Dr(e.element(),"aria-pressed",t),Cu(e.element(),"span").each(function(n){e.getSystem().getByDom(n).each(function(n){return wg.set(n,t)})})},isActive:function(){return Cu(e.element(),"span").exists(function(n){return e.getSystem().getByDom(n).exists(wg.isOn)})}}}var o,r=Vr("channel-update-split-dropdown-display"),i=Te(Z),u={getApi:e,onSetup:t.onSetup};return NE.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:_n({"aria-pressed":!1},QO(t.tooltip,n.providers))},onExecute:function(n){t.onAction(e(n))},onItemExecute:function(n,t,e){},splitDropdownBehaviours:va([kh(!1),qd("split-dropdown-events",[or(HE,pg.focus),kp(u,i),Op(u,i)]),bw.config({})]),eventOrder:(o={},o[Vo()]=["alloy.base.behaviour","split-dropdown-events"],o),toggleClass:"tox-tbtn--enabled",lazySink:n.getSink,fetch:function(e,r,o){return function(t){return By(function(n){return r.fetch(n)}).map(function(n){return on.from(rb(Dn(Ov(Vr("menu-value"),n,function(n){r.onItemAction(e(t),n)},r.columns,r.presets,Ah.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),o),{movement:Tv(r.columns,r.presets),menuBehaviours:qp("auto"!==r.columns?[]:[Fi(function(o,n){op(o,4,ap(r.presets)).each(function(n){var t=n.numRows,e=n.numColumns;sg.setGridSize(o,t,e)})})])})))})}}(e,t,n.providers),parts:{menu:xv(0,t.columns,t.presets)},components:[NE.parts().button(ZO(t.icon,t.text,on.none(),on.some(r),on.some([wg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),n.providers)),NE.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:vm("chevron-down",n.providers.icons)}}),NE.parts()["aria-descriptor"]({text:n.providers.translate("To open the popup, press Shift+Enter")})]})}function eT(o,r){return or(ZC,function(n,t){var e=function(n){return{hide:function(){return Xt(n,Do())},getValue:function(){return Qf.getValue(n)}}}(o.get(n));r.onAction(e,t.event().buttonApi())})}function oT(n,t,e){var o={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===t.type?function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=et(KO(P(P({},r),{type:"togglebutton",onAction:function(){}})));return jE(i,e.backstage.shared.providers,[eT(n,t)])}(n,t,o):function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=et(YO(P(P({},r),{type:"button",onAction:function(){}})));return LE(i,e.backstage.shared.providers,[eT(n,t)])}(n,t,o)}function rT(n,t){var e=Math.max(t.x(),n.x()),o=n.right()-e,r=t.width()-(e-t.x());return{x:e,width:Math.min(o,r)}}function iT(n,t){var e=hu(H.window),o=yu(Be.fromDom(n.getContentAreaContainer()));return n.inline&&!t?on.some(function(n,t,e){var o=rT(t,e),r=o.x,i=o.width;return bu(r,e.y(),i,e.height())}(0,o,e)):n.inline?on.some(function(n,t,e){var o=rT(t,e),r=o.x,i=o.width,u=Be.fromDom(n.getContainer()),a=Cu(u,".tox-editor-header").getOr(u),c=yu(a),s=e.height(),f=e.y();if(c.y()>=t.bottom()){var l=Math.min(s+f,c.y());return bu(r,f,i,l-f)}var d=Math.max(f,c.bottom());return bu(r,d,i,s-(d-f))}(n,o,e)):on.some(function(n,t,e){var o=rT(t,e),r=o.x,i=o.width,u=Be.fromDom(n.getContainer()),a=Cu(u,".tox-editor-header").getOr(u),c=yu(u),s=yu(a),f=Math.max(e.y(),t.y(),s.bottom()),l=c.bottom()-f,d=e.height()-(f-e.y()),m=Math.min(l,d);return bu(r,f,i,m)}(n,o,e))}function uT(t,n){return Bu(n,function(n){return n.predicate(t.dom())?on.some({toolbarApi:n,elem:t}):on.none()})}function aT(o,r){return function(t){function n(){t.setActive(o.formatter.match(r));var n=o.formatter.formatChanged(r,t.setActive).unbind;e.set(on.some(n))}var e=Te(on.none());return o.initialized?n():o.on("init",n),function(){return e.get().each(function(n){return n()})}}}function cT(t){return function(n){return function(){t.undoManager.transact(function(){t.focus(),t.execCommand("mceToggleFormat",!1,n.format)})}}}function sT(n,t,e){var o=e.dataset,r="basic"===o.type?function(){return w(o.data,function(n){return Wk(n,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:function(n,u,a){function r(n,t,e,o){var r=u.shared.providers.translate(n.title);if("separator"===n.type)return on.some({type:"separator",text:r});if("submenu"!==n.type)return on.some(P({type:"togglemenuitem",text:r,active:n.isSelected(o),disabled:e,onAction:a.onAction(n)},n.getStylePreview().fold(function(){return{}},function(n){return{meta:{style:n}}})));var i=E(n.getStyleItems(),function(n){return c(n,t,o)});return 0===t&&i.length<=0?on.none():on.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return E(n.getStyleItems(),function(n){return c(n,t,o)})}})}function i(n){var t=a.getCurrentValue(),e=a.shouldHide?0:1;return E(n,function(n){return c(n,e,t)})}var c=function(n,t,e){var o="formatter"===n.type&&a.isInvalid(n);return 0===t?o?[]:r(n,t,!1,e).toArray():r(n,t,o,e).toArray()};return{validateItems:i,getFetch:function(o,r){return function(n){var t=r(),e=i(t);n(IC(e,Ah.CLOSE_ON_EXECUTE,o,!1))}}}}(0,t,e),getStyleItems:r}}function fT(o,n,t){var e=sT(0,n,t),r=e.items,i=e.getStyleItems;return _C({text:t.icon.isSome()?on.none():on.some(""),icon:t.icon,tooltip:on.from(t.tooltip),role:on.none(),fetch:r.getFetch(n,i),onSetup:function(e){return t.setInitialValue.each(function(n){return n(e.getComponent())}),t.nodeChangeHandler.map(function(n){var t=n(e.getComponent());return o.on("NodeChange",t),function(){o.off("NodeChange",t)}}).getOr(Z)},getApi:function(n){return{getComponent:function(){return n}}},columns:1,presets:"normal",classes:t.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}var lT,dT,mT,gT,pT=Bl({name:"HtmlSelect",configFields:[ct("options"),Ms("selectBehaviours",[pg,Qf]),St("selectClasses",[]),St("selectAttributes",{}),ht("data")],factory:function(e,n){var t=w(e.options,function(n){return{dom:{tag:"option",value:n.value,innerHtml:n.text}}}),o=e.data.map(function(n){return q("initialValue",n)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:t,behaviours:Is(e.selectBehaviours,[pg.config({}),Qf.config({store:P({mode:"manual",getValue:function(n){return hi(n.element())},setValue:function(n,t){O(e.options,function(n){return n.value===t}).isSome()&&vi(n.element(),t)}},o)})])}}}),hT=/* */Object.freeze({events:function(n,t){var e=n.stream.streams.setup(n,t);return nr([or(n.event,e),Ii(function(){return t.cancel()})].concat(n.cancelEvent.map(function(n){return[or(n,function(){return t.cancel()})]}).getOr([])))}}),vT=/* */Object.freeze({throttle:Bk,init:function(n){return n.stream.streams.state(n)}}),bT=[st("stream",it("mode",{throttle:[ct("delay"),St("stopEvent",!0),$u("streams",{setup:function(n,t){var e=n.stream,o=qg(n.onStream,e.delay);return t.setTimer(o),function(n,t){o.throttle(n,t),e.stopEvent&&t.stop()}},state:Bk})]})),St("event","input"),ht("cancelEvent"),Ku("onStream")],yT=ba({fields:bT,name:"streaming",active:hT,state:vT}),xT=function(n){_k(n,function(n,t){return n.setSelectionRange(t.length,t.length)})},wT=nn("alloy.typeahead.itemexecute"),ST=nn([ht("lazySink"),ct("fetch"),St("minChars",5),St("responseTime",1e3),Yu("onOpen"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),St("eventOrder",{}),Dt("model",{},[St("getDisplayText",function(n){return n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.value}),St("selectsOver",!0),St("populateFromBrowse",!0)]),Yu("onSetValue"),qu("onExecute"),Yu("onItemExecute"),St("inputClasses",[]),St("inputAttributes",{}),St("inputStyles",{}),St("matchWidth",!0),St("useMinWidth",!1),St("dismissOnBlur",!0),Gu(["openClass"]),ht("initialData"),Ms("typeaheadBehaviours",[pg,Qf,yT,sg,wg,jy]),_t("previewing",function(){return Te(!0)})].concat(fy()).concat(Zy())),CT=nn([xl({schema:[Wu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(t,e){o.previewing.get()?t.getSystem().getByUid(o.uid).each(function(n){Ak(o.model,n,e).fold(function(){return ud.dehighlight(t,e)},function(n){return n()})}):t.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Dk(o.model,n,e)}),o.previewing.set(!1)},onExecute:function(n,t){return n.getSystem().getByUid(o.uid).toOption().map(function(n){return Yt(n,wT(),{item:t}),!0})},onHover:function(n,t){o.previewing.set(!1),n.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Dk(o.model,n,t)})}}}})]),kT=Dl({name:"Typeahead",configFields:ST(),partFields:CT(),factory:function(r,n,t,i){function e(n,t,e){r.previewing.set(!1);var o=jy.getCoupled(n,"sandbox");if(Pf.isOpen(o))Ql.getCurrent(o).each(function(n){ud.getHighlighted(n).fold(function(){e(n)},function(){Jt(o,n.element(),"keydown",t)})});else{Xy(r,u(n),n,o,i,function(n){Ql.getCurrent(n).each(e)},Sy.HighlightFirst).get(Z)}}var o=Xb(r),u=function(o){return function(n){return n.map(function(n){var t=I(n.menus),e=E(t,function(n){return S(n.items,function(n){return"item"===n.type})});return Qf.getState(o).update(w(e,function(n){return n.data})),n})}},a=[pg.config({}),Qf.config({onSetValue:r.onSetValue,store:P({mode:"dataset",getDataKey:function(n){return hi(n.element())},getFallbackEntry:function(n){return{value:n,meta:{}}},setValue:function(n,t){vi(n.element(),r.model.getDisplayText(t))}},r.initialData.map(function(n){return q("initialValue",n)}).getOr({}))}),yT.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(n,t){var e=jy.getCoupled(n,"sandbox");if(pg.isFocused(n)&&hi(n.element()).length>=r.minChars){var o=Ql.getCurrent(e).bind(function(n){return ud.getHighlighted(n).map(Qf.getValue)});r.previewing.set(!0);Xy(r,u(n),n,e,i,function(n){Ql.getCurrent(e).each(function(n){o.fold(function(){r.model.selectsOver&&ud.highlightFirst(n)},function(t){ud.highlightBy(n,function(n){return Qf.getValue(n).value===t.value}),ud.getHighlighted(n).orThunk(function(){return ud.highlightFirst(n),on.none()})})})},Sy.HighlightFirst).get(Z)}},cancelEvent:_o()}),sg.config({mode:"special",onDown:function(n,t){return e(n,t,ud.highlightFirst),on.some(!0)},onEscape:function(n){var t=jy.getCoupled(n,"sandbox");return Pf.isOpen(t)?(Pf.close(t),on.some(!0)):on.none()},onUp:function(n,t){return e(n,t,ud.highlightLast),on.some(!0)},onEnter:function(t){var n=jy.getCoupled(t,"sandbox"),e=Pf.isOpen(n);if(e&&!r.previewing.get())return Ql.getCurrent(n).bind(function(n){return ud.getHighlighted(n)}).map(function(n){return Yt(t,wT(),{item:n}),!0});var o=Qf.getValue(t);return Xt(t,_o()),r.onExecute(n,t,o),e&&Pf.close(n),on.some(!0)}}),wg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),jy.config({others:{sandbox:function(n){return $y(r,n,{onOpen:function(){return wg.on(n)},onClose:function(){return wg.off(n)}})}}}),qd("typeaheadevents",[Vi(function(n){var t=Z;qy(r,u(n),n,i,t,Sy.HighlightFirst).get(Z)}),or(wT(),function(n,t){var e=jy.getCoupled(n,"sandbox");Dk(r.model,n,t.event().item()),Xt(n,_o()),r.onItemExecute(n,e,t.event().item(),Qf.getValue(n)),Pf.close(e),xT(n)})].concat(r.dismissOnBlur?[or(wo(),function(n){var t=jy.getCoupled(n,"sandbox");Sa(t.element()).isNone()&&Pf.close(t)})]:[]))];return{uid:r.uid,dom:Yb(Dn(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:P(P({},o),Is(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),OT=function(i){return P(P({},i),{toCached:function(){return OT(i.toCached())},bindFuture:function(t){return OT(i.bind(function(n){return n.fold(function(n){return Dy(an.error(n))},function(n){return t(n)})}))},bindResult:function(t){return OT(i.map(function(n){return n.bind(t)}))},mapResult:function(t){return OT(i.map(function(n){return n.map(t)}))},mapError:function(t){return OT(i.map(function(n){return n.mapError(t)}))},foldResult:function(t,e){return i.map(function(n){return n.fold(t,e)})},withTimeout:function(n,r){return OT(By(function(t){var e=!1,o=H.setTimeout(function(){e=!0,t(an.error(r()))},n);i.get(function(n){e||(H.clearTimeout(o),t(n))})}))}})},TT=Mk,ET={type:"separator"},BT=Vr("aria-invalid"),DT={bar:zk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:w(n.items,t.interpreter)}}(n,t.shared)}),collection:zk(function(n,t){return Pk(n,t.shared.providers)}),alertbanner:zk(function(n,t){return function(t,n){return Hb.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+t.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Ug.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:vm(t.icon,n.icons),attributes:{title:n.translate(t.iconTooltip)}},action:function(n){Yt(n,ty,{name:"alert-banner",value:t.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:n.translate(t.text)}}]})}(n,t.shared.providers)}),input:zk(function(n,t){return function(n,t){return Ek({name:n.name,multiline:!1,label:n.label,inputMode:n.inputMode,placeholder:n.placeholder,flex:!1,disabled:n.disabled,classname:"tox-textfield",validation:on.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),textarea:zk(function(n,t){return function(n,t){return Ek({name:n.name,multiline:!0,label:n.label,inputMode:on.none(),placeholder:n.placeholder,flex:!0,disabled:n.disabled,classname:"tox-textarea",validation:on.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),label:zk(function(n,t){return function(n,t){var e={dom:{tag:"label",innerHtml:t.providers.translate(n.label),classes:["tox-label"]}},o=w(n.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[e].concat(o),behaviours:va([vS(),lg.config({}),OS(on.none()),sg.config({mode:"acyclic"})])}}(n,t.shared)}),iframe:(lT=function(n,t){return mw(n,t.shared.providers)},function(n,t,e){var o=Dn(t,{source:"dynamic"});return zk(lT)(n,o,e)}),button:zk(function(n,t){return GC(n,t.shared.providers)}),checkbox:zk(function(n,t){return function(e,t){function n(n){return n.element().dom().click(),on.some(!0)}function o(n){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+n],innerHtml:vm("checked"===n?"selected":"unselected",t.icons)}}}var r=Qf.config({store:{mode:"manual",getValue:function(n){return n.element().dom().checked},setValue:function(n,t){n.element().dom().checked=t}}}),i=sy.parts().field({factory:{sketch:l},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:va([vS(),wh.config({disabled:e.disabled}),Ny.config({}),pg.config({}),r,sg.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),qd("checkbox-events",[or(po(),function(n,t){Yt(n,Qb,{name:e.name})})])])}),u=sy.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:t.translate(e.label)},behaviours:va([bw.config({})])}),a=pm({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]});return sy.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[i,a.asSpec(),u],fieldBehaviours:va([wh.config({disabled:e.disabled,disableClass:"tox-checkbox--disabled",onDisabled:function(n){sy.getField(n).each(wh.disable)},onEnabled:function(n){sy.getField(n).each(wh.enable)}})])})}(n,t.shared.providers)}),colorinput:zk(function(n,t){return nx(n,t.shared,t.colorinput)}),colorpicker:zk(function(n){function t(n){return"tox-"+n}var e=hS(aw,t),r=pm(e.sketch({dom:{tag:"div",classes:[t("color-picker-container")],attributes:{role:"presentation"}},onValidHex:function(n){Yt(n,ty,{name:"hex-valid",value:!0})},onInvalidHex:function(n){Yt(n,ty,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:va([Qf.config({store:{mode:"manual",getValue:function(n){var t=r.get(n);return Ql.getCurrent(t).bind(function(n){return Qf.getValue(n).hex}).map(function(n){return"#"+n}).getOr("")},setValue:function(n,t){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),o=r.get(n);Ql.getCurrent(o).fold(function(){H.console.log("Can not find form")},function(n){Qf.setValue(n,{hex:on.from(e[1]).getOr("")}),sS.getField(n,"hex").each(function(n){Xt(n,go())})})}}}),vS()])}}),dropzone:zk(function(n,t){return fw(n,t.shared.providers)}),grid:zk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+n.columns+"col"]},components:w(n.items,t.interpreter)}}(n,t.shared)}),selectbox:zk(function(n,t){return function(e,t){var n=w(e.items,function(n){return{text:t.translate(n.text),value:n.value}}),o=e.label.map(function(n){return wy(n,t)}),r=sy.parts().field({dom:{},selectAttributes:{size:e.size},options:n,factory:pT,selectBehaviours:va([wh.config({disabled:e.disabled}),Ny.config({}),qd("selectbox-change",[or(po(),function(n,t){Yt(n,Qb,{name:e.name})})])])}),i=1<e.size?on.none():on.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:vm("chevron-down",t.icons)}}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:z([[r],i.toArray()])};return sy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:z([o.toArray(),[u]]),fieldBehaviours:va([wh.config({disabled:e.disabled,onDisabled:function(n){sy.getField(n).each(wh.disable)},onEnabled:function(n){sy.getField(n).each(wh.enable)}})])})}(n,t.shared.providers)}),sizeinput:zk(function(n,t){return $C(n,t.shared.providers)}),urlinput:zk(function(n,t){return Hk(n,t,t.urlinput)}),customeditor:zk(function(e){var o=Te(on.none()),t=pm({dom:{tag:e.tag}}),r=Te(on.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:va([qd("editor-foo-events",[Fi(function(n){t.getOpt(n).each(function(t){(!function(n){return Object.prototype.hasOwnProperty.call(n,"init")}(e)?wS.load(e.scriptId,e.scriptUrl).then(function(n){return n(t.element().dom(),e.settings)}):e.init(t.element().dom())).then(function(t){r.get().each(function(n){t.setValue(n)}),r.set(on.none()),o.set(on.some(t))})})})]),Qf.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(n){return n.getValue()})},setValue:function(n,t){o.get().fold(function(){r.set(on.some(t))},function(n){return n.setValue(t)})}}}),vS()]),components:[t.asSpec()]}}),htmlpanel:zk(function(n){return"presentation"===n.presets?Hb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html}}):Hb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html,attributes:{role:"document"}},containerBehaviours:va([Ny.config({}),pg.config({})])})}),imagetools:zk(function(n,t){return Tk(n,t.shared.providers)}),table:zk(function(n,t){return function(n,t){function e(n){return{dom:{tag:"th",innerHtml:t.translate(n)}}}function o(n){return{dom:{tag:"td",innerHtml:t.translate(n)}}}function r(n){return{dom:{tag:"tr"},components:w(n,o)}}var i,u;return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(u=n.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:w(u,e)}]}),(i=n.cells,{dom:{tag:"tbody"},components:w(i,r)})],behaviours:va([Ny.config({}),pg.config({})])}}(n,t.shared.providers)}),panel:zk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:n.classes},components:w(n.items,t.shared.interpreter)}}(n,t)})},_T={field:function(n,t){return t}},AT=function(t,e,o){return Nn(DT,e.type).fold(function(){return H.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(n){return n(t,e,o)})},MT=nn(function(n,t){!function(n,t){var e=Ru.max(n,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);si(n,"max-width",e+"px")}(n,Math.floor(t))}),FT={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},IT=function(n,t,e){function o(){return Be.fromDom(n.getBody())}var r=Ub(n);return{toolbar:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:on.from(n()),bubble:ka(-12,-12,FT),layouts:{onRtl:function(){return[dm]},onLtr:function(){return[mm]}},overrides:{maxHeightFunction:Sf()}}}:function(){return{anchor:"hotspot",hotspot:t(),bubble:ka(-12,12,FT),layouts:{onRtl:function(){return[ia]},onLtr:function(){return[ua]}},overrides:{maxHeightFunction:Sf()}}}}(o,t,r),toolbarOverflow:function(n){return function(){return{anchor:"hotspot",hotspot:n(),overrides:{maxWidthFunction:MT()},layouts:{onRtl:function(){return[ia,ua]},onLtr:function(){return[ua,ia]}}}}}(e),banner:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:on.from(n()),layouts:{onRtl:function(){return[Lg]},onLtr:function(){return[Lg]}}}}:function(){return{anchor:"hotspot",hotspot:t(),layouts:{onRtl:function(){return[oc]},onLtr:function(){return[oc]}}}}}(o,t,r),cursor:function(t,n){return function(){return{anchor:"selection",root:n(),getSelection:function(){var n=t.selection.getRng();return on.some(Tc.range(Be.fromDom(n.startContainer),n.startOffset,Be.fromDom(n.endContainer),n.endOffset))}}}}(n,o),node:function(t){return function(n){return{anchor:"node",root:t(),node:n}}}(o)}},RT=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strike-through",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}]}],VT=function(n){return k(n,function(n,t){if(function(n){return Tn(n,"items")}(t)){var e=VT(t.items);return{customFormats:n.customFormats.concat(e.customFormats),formats:n.formats.concat([{title:t.title,items:e.formats}])}}if(function(n){return Tn(n,"inline")}(t)||function(n){return Tn(n,"block")}(t)||function(n){return Tn(n,"selector")}(t)){var o="custom-"+t.title.toLowerCase();return{customFormats:n.customFormats.concat([{name:o,format:t}]),formats:n.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return P(P({},n),{formats:n.formats.concat(t)})},{customFormats:[],formats:[]})},NT=hk.trim,HT=Xk("true"),PT=Xk("false"),zT=function(n){return function(n){for(;n=n.parentNode;){var t=n.contentEditable;if(t&&"inherit"!==t)return HT(n)}return!1}(n)&&!PT(n)},LT=function(n){var t=nO(n);return S(function(n){return w(S(n,$k),Qk)}(t).concat(function(n){return w(S(n,Kk),Zk)}(t)),tO)},jT="tinymce-url-history",UT=Object.prototype.hasOwnProperty,WT="contexttoolbar-hide",GT=nn([ct("dom"),St("shell",!0),Ms("toolbarBehaviours",[lg])]),XT=nn([wl({name:"groups",overrides:function(n){return{behaviours:va([lg.config({})])}}})]),YT=Dl({name:"Toolbar",configFields:GT(),partFields:XT(),factory:function(t,n,e,o){var r=function(n){return t.shell?on.some(n):Ys(n,t,"groups")},i=t.shell?{behaviours:[lg.config({})],components:[]}:{behaviours:[],components:n};return{uid:t.uid,dom:t.dom,components:i.components,behaviours:Is(t.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,t){r(n).fold(function(){throw H.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){lg.set(n,t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)}}}),qT=fr("within","extra","withinWidth"),KT=nn([ct("items"),Gu(["itemSelector"]),Ms("tgroupBehaviours",[sg])]),JT=nn([Sl({name:"items",unit:"item"})]),$T=Dl({name:"ToolbarGroup",configFields:KT(),partFields:JT(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,behaviours:Is(n.tgroupBehaviours,[sg.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),QT=nn([Ms("splitToolbarBehaviours",[jy]),_t("builtGroups",function(){return Te([])})]),ZT=nn([Gu(["overflowToggledClass"]),ct("getAnchor"),xt("getOverflowBounds"),ct("lazySink")].concat(QT())),nE=nn([yl({factory:YT,schema:GT(),name:"primary"}),xl({factory:YT,schema:GT(),name:"overflow",overrides:function(t){return{toolbarBehaviours:va([sg.config({mode:"cyclic",onEscape:function(n){return Ys(n,t,"overflow-button").each(pg.focus),on.none()}})])}}}),xl({name:"overflow-button",overrides:function(n){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:va([wg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),xl({name:"overflow-group"})]),tE=Dl({name:"SplitFloatingToolbar",configFields:ZT(),partFields:nE(),factory:function(t,n,e,o){return yO(t,n,0,o,{coupling:{sandbox:function(n){return function(o,e){var r=Ou();return{dom:{tag:"div",attributes:{id:r.id()}},behaviours:va([sg.config({mode:"special",onEscape:function(n){return Pf.close(n),on.some(!0)}}),Pf.config({onOpen:function(n,t){SO(o,e),Ys(o,e,"overflow-button").each(function(n){wg.on(n),r.link(n.element())}),sg.focusIn(t)},onClose:function(){Ys(o,e,"overflow-button").each(function(n){wg.off(n),pg.focus(n),r.unlink(n.element())})},isPartOf:function(n,t,e){return zu(t,e)||zu(o,e)},getAttachPoint:function(){return e.lazySink(o).getOrDie()}}),fc.config({channels:P({},Ts({isExtraPart:nn(!1),doReposition:function(){return CO(o,e)}}))})])}}(n,t)}},apis:{refresh:function(n){return SO(n,t)},toggle:function(n){return function(n,t,e){var o=jy.getCoupled(n,"sandbox");Pf.isOpen(o)?Pf.close(o):Pf.open(o,e.overflow())}(n,0,o)},getOverflow:function(n){return Pf.getState(jy.getCoupled(n,"sandbox"))},reposition:function(n){return CO(n,t)}}})},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},reposition:function(n,t){n.reposition(t)},getMoreButton:function(n,t){return n.getMoreButton(t)},getOverflow:function(n,t){return n.getOverflow(t)},toggle:function(n,t){n.toggle(t)}}}),eE=/* */Object.freeze({refresh:function(n,t,e){if(e.isExpanded()){gi(n.element(),OO(t));var o=TO(t,n.element());si(n.element(),OO(t),o)}},grow:function(n,t,e){e.isExpanded()||FO(n,t,e)},shrink:function(n,t,e){e.isExpanded()&&MO(n,t,e)},immediateShrink:function(n,t,e){e.isExpanded()&&_O(n,t,e)},hasGrown:function(n,t,e){return e.isExpanded()},hasShrunk:function(n,t,e){return e.isCollapsed()},isGrowing:IO,isShrinking:RO,isTransitioning:function(n,t,e){return!0===IO(n,t)||!0===RO(n,t)},toggleGrow:function(n,t,e){(e.isExpanded()?MO:FO)(n,t,e)},disableTransitions:EO}),oE=/* */Object.freeze({exhibit:function(n,t){var e=t.expanded;return Ur(e?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:q(t.dimension.property,"0px")})},events:function(e,o){return nr([sr(vo(),function(n,t){t.event().raw().propertyName===e.dimension.property&&(EO(n,e),o.isExpanded()&&gi(n.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(n))})])}}),rE=[ct("closedClass"),ct("openClass"),ct("shrinkingClass"),ct("growingClass"),ht("getAnimationRoot"),Yu("onShrunk"),Yu("onStartShrink"),Yu("onGrown"),Yu("onStartGrow"),St("expanded",!1),st("dimension",it("property",{width:[$u("property","width"),$u("getDimension",function(n){return du(n)+"px"})],height:[$u("property","height"),$u("getDimension",function(n){return cu(n)+"px"})]}))],iE=ba({fields:rE,name:"sliding",active:oE,apis:eE,state:/* */Object.freeze({init:function(n){var t=Te(n.expanded);return Zi({isExpanded:function(){return!0===t.get()},isCollapsed:function(){return!1===t.get()},setCollapsed:d(t.set,!1),setExpanded:d(t.set,!0),readState:function(){return"expanded: "+t.get()}})}})}),uE=nn([Gu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Yu("onOpened"),Yu("onClosed")].concat(QT())),aE=nn([yl({factory:YT,schema:GT(),name:"primary"}),yl({factory:YT,schema:GT(),name:"overflow",overrides:function(t){return{toolbarBehaviours:va([iE.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass,onShrunk:function(n){Ys(n,t,"overflow-button").each(function(n){wg.off(n),pg.focus(n)}),t.onClosed(n)},onGrown:function(n){sg.focusIn(n),t.onOpened(n)},onStartGrow:function(n){Ys(n,t,"overflow-button").each(wg.on)}}),sg.config({mode:"acyclic",onEscape:function(n){return Ys(n,t,"overflow-button").each(pg.focus),on.some(!0)}})])}}}),xl({name:"overflow-button",overrides:function(n){return{buttonBehaviours:va([wg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),xl({name:"overflow-group"})]),cE=function(n,t){var e=Ys(n,t,"overflow");bO(n,t,e,VO),e.each(iE.refresh)},sE=Dl({name:"SplitSlidingToolbar",configFields:uE(),partFields:aE(),factory:function(t,n,e,o){return yO(t,n,0,o,{coupling:{},apis:{refresh:function(n){return cE(n,t)},toggle:function(n){return function(t,e){Ys(t,e,"overflow").each(function(n){cE(t,e),iE.toggleGrow(n)})}(n,t)},getOverflow:function(n){return Ys(n,t,"overflow")}}})},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},getMoreButton:function(n,t){return n.getMoreButton(t)},getOverflow:function(n,t){return n.getOverflow(t)},toggle:function(n,t){n.toggle(t)}}}),fE="silver.readonly",lE=le([(dT="readonly",st(dT,Se))]),dE=nn(Vr("toolbar-height-change")),mE=[Et("disabled",!1),yt("tooltip"),yt("icon"),yt("text"),Bt("onSetup",function(){return Z})],gE=le([ft("type"),dt("onAction")].concat(mE)),pE=[yt("text"),yt("tooltip"),yt("icon"),dt("fetch"),Bt("onSetup",function(){return Z})],hE=le(g([ft("type")],pE)),vE=le([ft("type"),yt("tooltip"),yt("icon"),yt("text"),xt("select"),dt("fetch"),Bt("onSetup",function(){return Z}),Tt("presets","normal",["normal","color","listpreview"]),St("columns",1),dt("onAction"),dt("onItemAction")]),bE=[Et("active",!1)].concat(mE),yE=le(bE.concat([ft("type"),dt("onAction")])),xE=[Bt("predicate",function(){return!1}),Tt("scope","node",["node","editor"]),Tt("position","selection",["node","selection","line"])],wE=mE.concat([St("type","contextformbutton"),St("primary",!1),dt("onAction"),_t("original",l)]),SE=bE.concat([St("type","contextformbutton"),St("primary",!1),dt("onAction"),_t("original",l)]),CE=mE.concat([St("type","contextformbutton")]),kE=bE.concat([St("type","contextformtogglebutton")]),OE=it("type",{contextformbutton:wE,contextformtogglebutton:SE}),TE=le([St("type","contextform"),Bt("initValue",function(){return""}),yt("label"),pt("commands",OE),vt("launch",it("type",{contextformbutton:CE,contextformtogglebutton:kE}))].concat(xE)),EE=le([St("type","contexttoolbar"),ft("items")].concat(xE)),BE=/* */Object.freeze({getState:function(n,t,e){return e}}),DE=/* */Object.freeze({events:function(i,u){function o(o,r){i.updateState.each(function(n){var t=n(o,r);u.set(t)}),i.renderComponents.each(function(n){var t=n(r,u.get()),e=w(t,o.getSystem().build);gs(o,e)})}return nr([or(Co(),function(n,t){var e=i.channel;vn(t.channels(),e)&&o(n,t.data())}),Fi(function(t,n){i.initialData.each(function(n){o(t,n)})})])}}),_E=/* */Object.freeze({init:function(n){var t=Te(on.none());return{readState:function(){return t.get().fold(function(){return"none"},function(n){return n})},get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(on.none())}}}}),AE=[ct("channel"),ht("renderComponents"),ht("updateState"),ht("initialData")],ME=ba({fields:AE,name:"reflecting",active:DE,apis:BE,state:_E}),FE=nn([ct("toggleClass"),ct("fetch"),Ku("onExecute"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),Ku("onItemExecute"),ht("lazySink"),ct("dom"),Yu("onOpen"),Ms("splitDropdownBehaviours",[jy,sg,pg]),St("matchWidth",!1),St("useMinWidth",!1),St("eventOrder",{}),ht("role")].concat(Zy())),IE=yl({factory:Ug,schema:[ct("dom")],name:"arrow",defaults:function(n){return{buttonBehaviours:va([pg.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each(qt)},buttonBehaviours:va([wg.config({toggleOnExecute:!1,toggleClass:t.toggleClass})])}}}),RE=yl({factory:Ug,schema:[ct("dom")],name:"button",defaults:function(n){return{buttonBehaviours:va([pg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(e.uid).each(function(n){e.onExecute(n,t)})}}}}),VE=nn([IE,RE,wl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[ct("text")],name:"aria-descriptor"}),xl({schema:[Wu()],name:"menu",defaults:function(o){return{onExecute:function(t,e){t.getSystem().getByUid(o.uid).each(function(n){o.onItemExecute(n,t,e)})}}}}),Wy()]),NE=Dl({name:"SplitDropdown",configFields:FE(),partFields:VE(),factory:function(o,n,t,e){function r(n){Ql.getCurrent(n).each(function(n){ud.highlightFirst(n),sg.focusIn(n)})}function i(n){qy(o,function(n){return n},n,e,r,Sy.HighlightFirst).get(Z)}function u(n){var t=qs(n,o,"button");return qt(t),on.some(!0)}var a=_n(nr([Fi(function(e,n){Ys(e,o,"aria-descriptor").each(function(n){var t=Vr("aria");Dr(n.element(),"id",t),Dr(e.element(),"aria-describedby",t)})})]),gm(on.some(i))),c={repositionMenus:function(n){wg.isOn(n)&&Qy(n)}};return{uid:o.uid,dom:o.dom,components:n,apis:c,eventOrder:P(P({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Is(o.splitDropdownBehaviours,[jy.config({others:{sandbox:function(n){var t=qs(n,o,"arrow");return $y(o,n,{onOpen:function(){wg.on(t),wg.on(n)},onClose:function(){wg.off(t),wg.off(n)}})}}}),sg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(n){return i(n),on.some(!0)}}),pg.config({}),wg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(n,t){return n.repositionMenus(t)}}}),HE=Vr("focus-button"),PE=["checklist","ordered-list"],zE=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],LE=function(n,t,e){return nT(n,{toolbarButtonBehaviours:[].concat(0<e.length?[qd("toolbarButtonWith",e)]:[]),getApi:JO,onSetup:n.onSetup},t)},jE=function(n,t,e){return Dn(nT(n,{toolbarButtonBehaviours:[lg.config({}),wg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[qd("toolbarToggleButtonWith",e)]:[]),getApi:$O,onSetup:n.onSetup},t))},UE=function(n,t){var e=n.label.fold(function(){return{}},function(n){return{"aria-label":n}}),o=pm(ly.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:n.initValue(),inputAttributes:e,selectOnFocus:!0,inputBehaviours:va([sg.config({mode:"special",onEnter:function(n){return r.findPrimary(n).map(function(n){return qt(n),!0})},onLeft:function(n,t){return t.cut(),on.none()},onRight:function(n,t){return t.cut(),on.none()}})])})),r=function(t,n,e){var o=w(n,function(n){return pm(oT(t,n,e))});return{asSpecs:function(){return w(o,function(n){return n.asSpec()})},findPrimary:function(e){return Bu(n,function(n,t){return n.primary?on.from(o[t]).bind(function(n){return n.getOpt(e)}).filter(v(wh.isDisabled)):on.none()})}}}(o,n.commands,t.shared.providers);return XO({uid:Vr("context-toolbar"),initGroups:[{title:on.none(),items:[o.asSpec()]},{title:on.none(),items:r.asSpecs()}],onEscape:on.none,cyclicKeying:!0,backstage:t,getSink:function(){return an.error("")}})},WE=function(t,e){function n(n){return n.dom()===e.getBody()}var o=Be.fromDom(e.selection.getNode());return uT(o,t.inNodeScope).orThunk(function(){return uT(o,t.inEditorScope).orThunk(function(){return function(n,t,e){for(var o=n.dom(),r=dn(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=Be.fromDom(o),u=t(i);if(u.isSome())return u;if(r(i))break}return on.none()}(o,function(n){return uT(n,t.inNodeScope)},n)})})},GE=function(e,r){function o(t,e){var o=et(function(n){return tt("ContextForm",TE,n)}(e));(n[t]=o).launch.map(function(n){c["form:"+t]=P(P({},e.launch),{type:"contextformtogglebutton"===n.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?a.push(o):u.push(o),s[t]=o}function i(t,e){(function(n){return tt("ContextToolbar",EE,n)})(e).each(function(n){"editor"===e.scope?a.push(n):u.push(n),s[t]=n})}var n={},u=[],a=[],c={},s={},t=wn(e);return bn(t,function(n){var t=e[n];"contextform"===t.type?o(n,t):"contexttoolbar"===t.type&&i(n,t)}),{forms:n,inNodeScope:u,inEditorScope:a,lookupTable:s,formNavigators:c}},XE=Vr("forward-slide"),YE=Vr("backward-slide"),qE=Vr("change-slide-event"),KE="tox-pop--resizing";(gT=mT=mT||{})[gT.SemiColon=0]="SemiColon",gT[gT.Space=1]="Space";function JE(n,t,e,o){return{type:"basic",data:function(n){return w(n,function(n){var t=n,e=n,o=n.split("=");return 1<o.length&&(t=o[0],e=o[1]),{title:t,format:e}})}(function(n,t){return t===mT.SemiColon?n.replace(/;$/,"").split(";"):n.split(" ")}(Nn(n.settings,t).getOr(e),o))}}function $E(e){function t(n){var t=O(wD,function(n){return e.formatter.match(n.format)}).fold(function(){return"left"},function(n){return n.title.toLowerCase()});Yt(n,ek,{icon:"align-"+t})}var n=on.some(function(n){return function(){return t(n)}}),o=on.some(function(n){return t(n)}),r=function(n){return{type:"basic",data:n}}(wD);return{tooltip:"Align",icon:on.some("align-left"),isSelectedFor:function(n){return function(){return e.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(n){return function(){return on.none()}},onAction:cT(e),setInitialValue:o,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!e.formatter.canApply(n.format)}}}function QE(n){var t=n.split(/\s*,\s*/);return w(t,function(n){return n.replace(/^['"]+|['"]+$/g,"")})}function ZE(r){function i(){function e(n){return n?QE(n)[0]:""}var n=r.queryCommandValue("FontName"),t=u.data,o=n?n.toLowerCase():"";return{matchOpt:O(t,function(n){var t=n.format;return t.toLowerCase()===o||e(t).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return function(n){var t;return 0===n.indexOf("-apple-system")&&(t=QE(n.toLowerCase()),B(SD,function(n){return-1<t.indexOf(n.toLowerCase())}))}(o)?on.from({title:"System Font",format:o}):on.none()}),font:n}}function t(n){var t=i(),e=t.matchOpt,o=t.font,r=e.fold(function(){return o},function(n){return n.title});Yt(n,tk,{text:r})}var n=on.some(function(n){return function(){return t(n)}}),e=on.some(function(n){return t(n)}),u=JE(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",mT.SemiColon);return{tooltip:"Fonts",icon:on.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(n){return function(){return on.some({tag:"div",styleAttr:-1===n.indexOf("dings")?"font-family:"+n:""})}},onAction:function(n){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,n.format)})}},setInitialValue:e,nodeChangeHandler:n,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function nB(n,t){return/[0-9.]+px$/.test(n)?function(n,t){var e=Math.pow(10,t);return Math.round(n*e)/e}(72*parseInt(n,10)/96,t||0)+"pt":n}function tB(e){function i(){var o=on.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var n=function(n){var t=nB(i,n),e=function(n){return R(CD,n).getOr("")}(t);o=O(r,function(n){return n.format===i||n.format===t||n.format===e})},t=3;o.isNone()&&0<=t;t--)n(t);return{matchOpt:o,px:i}}function t(n){var t=i(),e=t.matchOpt,o=t.px,r=e.fold(function(){return o},function(n){return n.title});Yt(n,tk,{text:r})}var n=nn(nn(on.none())),o=on.some(function(n){return function(){return t(n)}}),r=on.some(function(n){return t(n)}),u=JE(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",mT.Space);return{tooltip:"Font sizes",icon:on.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getPreviewFor:n,getCurrentValue:function(){return i().matchOpt},onAction:function(n){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,n.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function eB(e,n,t){var o=n();return Bu(t,function(t){return O(o,function(n){return e.formatter.matchNode(t,n.format)})}).orThunk(function(){return e.formatter.match("p")?on.some({title:"Paragraph",format:"p"}):on.none()})}function oB(n){var t=n.selection.getStart(!0)||n.getBody();return n.dom.getParents(t,function(){return!0},n.getBody())}function rB(o){function e(n,t){var e=function(n){return eB(o,function(){return r.data},n)}(n).fold(function(){return"Paragraph"},function(n){return n.title});Yt(t,tk,{text:e})}var n=on.some(function(t){return function(n){return e(n.parents,t)}}),t=on.some(function(n){var t=oB(o);e(t,n)}),r=JE(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",mT.SemiColon);return{tooltip:"Blocks",icon:on.none(),isSelectedFor:function(n){return function(){return o.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(t){return function(){var n=o.formatter.get(t);return on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:o.formatter.getCssText(t)})}},onAction:cT(o),setInitialValue:t,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!o.formatter.canApply(n.format)}}}function iB(i,n){function e(n,t){var e=function(n){var t=n.items;return t!==undefined&&0<t.length?E(t,e):[{title:n.title,format:n.format}]},o=E(Uk(i),e),r=eB(i,function(){return o},n).fold(function(){return"Paragraph"},function(n){return n.title});Yt(t,tk,{text:r})}var t=on.some(function(t){return function(n){return e(n.parents,t)}}),o=on.some(function(n){var t=oB(i);e(t,n)});return{tooltip:"Formats",icon:on.none(),isSelectedFor:function(n){return function(){return i.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(t){return function(){var n=i.formatter.get(t);return n!==undefined?on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:i.formatter.getCssText(t)}):on.none()}},onAction:cT(i),setInitialValue:o,nodeChangeHandler:t,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(n){return!i.formatter.canApply(n.format)},dataset:n}}function uB(o,r){return function(n,t){var e=o(n).mapError(function(n){return be(n)}).getOrDie();return r(e,t)}}function aB(n){var t=n.toolbar,e=n.buttons;return!1===t?[]:t===undefined||!0===t?function(e){var n=w(kD,function(n){var t=S(n.items,function(n){return Tn(e,n)||Tn(TD,n)});return{name:n.name,items:t}});return S(n,function(n){return 0<n.items.length})}(e):cn(t)?function(n){var t=n.split("|");return w(t,function(n){return{items:n.trim().split(" ")}})}(t):function(n){return h(n,function(n){return Tn(n,"name")&&Tn(n,"items")})}(t)?t:(H.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])}function cB(t,e,o,r,n){return R(e,o.toLowerCase()).orThunk(function(){return n.bind(function(n){return Bu(n,function(n){return R(e,n+o.toLowerCase())})})}).fold(function(){return R(TD,o.toLowerCase()).map(function(n){return n(t,r)}).orThunk(function(){return on.none()})},function(n){return function(t,e){return R(OD,t.type).fold(function(){return H.console.error("skipping button defined by",t),on.none()},function(n){return on.some(n(t,e))})}(n,r)})}function sB(e,o,r,i){var n=aB(o),t=w(n,function(n){var t=E(n.items,function(n){return 0===n.trim().length?[]:cB(e,o.buttons,n,r,i).toArray()});return{title:on.from(e.translate(n.name)),items:t}});return S(t,function(n){return 0<n.items.length})}function fB(e){return(di(e,"position").is("fixed")?on.none():vr(e)).orThunk(function(){var n=Be.fromTag("span");xr(e,n);var t=vr(n);return Hi(n),t}).map(lu).getOrThunk(function(){return Fu(0,0)})}function lB(t){return function(n){return n.translate(-t.left(),-t.top())}}function dB(t){return function(n){return n.translate(t.left(),t.top())}}function mB(e){return function(n,t){return k(e,function(n,t){return t(n)},Fu(n,t))}}function gB(n,t,e){return n.fold(mB([dB(e),lB(t)]),mB([lB(t)]),mB([]))}function pB(n,t,e){return n.fold(mB([dB(e)]),mB([]),mB([dB(t)]))}function hB(n,t,e){return n.fold(mB([]),mB([lB(e)]),mB([dB(t),lB(e)]))}function vB(n,t,e){return n.fold(function(n,t){return{position:"absolute",left:n+"px",top:t+"px"}},function(n,t){return{position:"absolute",left:n-e.left()+"px",top:t-e.top()+"px"}},function(n,t){return{position:"fixed",left:n+"px",top:t+"px"}})}function bB(n,i,u,a){function t(o,r){return function(n,t){var e=o(i,u,a);return r(n.getOr(e.left()),t.getOr(e.top()))}}return n.fold(t(hB,PD.offset),t(pB,PD.absolute),t(gB,PD.fixed))}function yB(n,t){var e=n.element();ni(e,t.transitionClass),ei(e,t.fadeOutClass),ni(e,t.fadeInClass),t.onShow(n)}function xB(n,t){var e=n.element();ni(e,t.transitionClass),ei(e,t.fadeInClass),ni(e,t.fadeOutClass),t.onHide(n)}function wB(n,t,e){return B(n,function(n){switch(n){case"bottom":return function(n,t){return n.bottom()<=t.bottom()}(t,e);case"top":return function(n,t){return n.y()>=t.y()}(t,e)}})}function SB(n,t){return Ar(n,t)?on.some(parseInt(_r(n,t),10)):on.none()}function CB(r,n){return SB(r,n.leftAttr).bind(function(o){return SB(r,n.topAttr).map(function(n){var t=du(r),e=cu(r);return bu(o,n,t,e)})})}function kB(n,t,e){var o=_r(n,t.positionAttr);switch(function(n,t){Mr(n,t.leftAttr),Mr(n,t.topAttr),Mr(n,t.positionAttr)}(n,t),o){case"static":return on.some(UD["static"]());case"absolute":return on.some(UD.absolute(e.x(),e.y()));default:return on.none()}}function OB(n,t,e,o,r){var i=yu(n);if(wB(t.modes,i,e))return on.none();var u=li(n,"position");!function(n,t,e,o,r){Dr(n,t.leftAttr,e),Dr(n,t.topAttr,o),Dr(n,t.positionAttr,r)}(n,t,i.x(),i.y(),u);var a=LD(i.x(),i.y()),c=gB(a,o,r),s=LD(e.x(),e.y()),f=gB(s,o,r),l=i.y()<=e.y()?f.top():f.top()+e.height()-i.height();return on.some(UD.fixed(c.left(),l))}function TB(n,t,e,o,r){var i=n.element();return di(i,"position").is("fixed")?function(t,e,o){return CB(t,e).filter(function(n){return wB(e.modes,n,o)}).bind(function(n){return kB(t,e,n)})}(i,t,e):OB(i,t,e,o,r)}function EB(t,n){bn(["left","top","position"],function(n){return gi(t.element(),n)}),n.onUndocked(t)}function BB(n,t,e,o,r){var i=vB(r,0,o);fi(n.element(),i),("fixed"===i.position?t.onDocked:t.onUndocked)(n)}function DB(o,n,r,i,u){void 0===u&&(u=!1),n.contextual.each(function(e){e.lazyContext(o).each(function(n){var t=function(n,t){return n.y()<t.bottom()&&n.bottom()>t.y()}(n,i);t!==r.isVisible()&&(r.setVisible(t),u&&!t?(ri(o.element(),[e.fadeOutClass]),e.onHide(o)):(t?yB:xB)(o,e))})})}function _B(r,i,n){var u=r.element();n.setDocked(!1),function(n,t){var e=n.element();return CB(e,t).bind(function(n){return kB(e,t,n)})}(r,i).each(function(n){n.fold(function(){return EB(r,i)},function(n,t){var e=gr(u),o=(gu(e),fB(u));BB(r,i,0,o,LD(n,t))},Z)}),n.setVisible(!0),i.contextual.each(function(n){ii(u,[n.fadeInClass,n.fadeOutClass,n.transitionClass]),n.onShow(r)}),WD(r,i,n)}function AB(n,t,e){e.isDocked()&&_B(n,t,e)}function MB(o){var r=o.element();hr(r).each(function(n){if(qD.isDocked(o)){var t=du(n);si(r,"width",t+"px");var e=su(r);si(n,"padding-top",e+"px")}else gi(r,"width"),gi(n,"padding-top")})}function FB(n,t){t?(ei(n,KD.fadeOutClass),ri(n,[KD.transitionClass,KD.fadeInClass])):(ei(n,KD.fadeInClass),ri(n,[KD.fadeOutClass,KD.transitionClass]))}function IB(n,t){var e=Be.fromDom(n.getContainer());t?(ni(e,JD),ei(e,$D)):(ni(e,$D),ei(e,JD))}function RB(i,e){function o(t){e().each(function(n){return t(n.element())})}function n(n){i.inline||MB(n),IB(i,qD.isDocked(n)),n.getSystem().broadcastOn([Lf()],{}),e().each(function(n){return n.getSystem().broadcastOn([Lf()],{})})}var t,r=Te(on.none());return[qD.config({leftAttr:"data-dock-left",topAttr:"data-dock-top",positionAttr:"data-dock-pos",contextual:P({lazyContext:function(n){var t=su(n.element()),e=i.inline?i.getContentAreaContainer():i.getContainer(),o=yu(Be.fromDom(e)),r=o.height()-t;return on.some(bu(o.x(),o.y(),o.width(),r))},onShow:function(){o(function(n){return FB(n,!0)})},onShown:function(t){o(function(n){return ii(n,[KD.transitionClass,KD.fadeInClass])}),r.get().each(function(n){!function(t,e){var o=gr(e);wa(o).filter(function(n){return!jt(e,n)}).filter(function(n){return jt(n,Be.fromDom(o.dom().body))||no(t,n)}).each(function(){return xa(e)})}(t.element(),n),r.set(on.none())})},onHide:function(n){r.set(function(n,t){return Sa(n).orThunk(function(){return t().toOption().bind(function(n){return Sa(n.element())})})}(n.element(),e)),o(function(n){return FB(n,!1)})},onHidden:function(){o(function(n){return ii(n,[KD.transitionClass])})}},KD),modes:["top"],onDocked:n,onUndocked:n}),pg.config({}),fc.config({channels:(t={},t[dE()]={onReceive:function(n){MB(n)}},t)})]}function VB(n){return"<alloy.field."+n+">"}function NB(n){return{element:function(){return n.element().dom()}}}function HB(e,o){var r=w(wn(o),function(n){var t=o[n],e=et(function(n){return tt("sidebar",s_,n)}(t));return{name:n,getApi:NB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return w(r,function(n){var t=Te(Z);return e.slot(n.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:qp([kp(n,t),Op(n,t),or(Lo(),function(t,n){var e=n.event();O(r,function(n){return n.name===e.name()}).each(function(n){(e.visible()?n.onShow:n.onHide)(n.getApi(t))})})])})})}function PB(n,t){Ql.getCurrent(n).each(function(n){return lg.set(n,[function(t){return c_.sketch(function(n){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:HB(n,t),slotBehaviours:qp([Fi(function(n){return c_.hideAllSlots(n)})])}})}(t)])})}function zB(n){return Ql.getCurrent(n).bind(function(n){return iE.isGrowing(n)||iE.hasGrown(n)?Ql.getCurrent(n).bind(function(t){return O(c_.getSlotNames(t),function(n){return c_.isShowing(t,n)})}):on.none()})}function LB(n,t,e){var o=n.element();!0===t?(lg.set(n,[function(n){return{dom:{tag:"div",attributes:{"aria-label":n.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:up('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:va([sg.config({mode:"special",onTab:function(){return on.some(!0)},onShiftTab:function(){return on.some(!0)}}),pg.config({})])}}(e)]),gi(o,"display"),Mr(o,"aria-hidden")):(lg.set(n,[]),si(o,"display","none"),Dr(o,"aria-hidden","true"))}function jB(n){return"string"==typeof n?n.split(" "):n}function UB(e,o){var r=_n(x_,o.menus),t=0<wn(o.menus).length,n=o.menubar===undefined||!0===o.menubar?jB("file edit view insert format tools table help"):jB(!1===o.menubar?"":o.menubar),i=S(n,function(n){return t&&o.menus.hasOwnProperty(n)&&o.menus[n].hasOwnProperty("items")||x_.hasOwnProperty(n)}),u=w(i,function(n){var t=r[n];return function(n,e,t){var o=function(n){return n.getParam("removed_menuitems","")}(t).split(/[ ,]/);return{text:n.title,getItems:function(){return E(n.items,function(n){var t=n.toLowerCase();return 0===t.trim().length?[]:y(o,function(n){return n===t})?[]:"separator"===t||"|"===t?[{type:"separator"}]:e.menuItems[t]?[e.menuItems[t]]:[]})}}}({title:t.title,items:jB(t.items)},o,e)});return S(u,function(n){return 0<n.getItems().length&&y(n.getItems(),function(n){return"separator"!==n.type})})}function WB(n,t){var e,o=function(n){var t=n.settings,e=t.skin,o=t.skin_url;if(!1!==e){var r=e||"oxide";o=o?n.documentBaseURI.toAbsolute(o):Pb.baseURL+"/skins/ui/"+r}return o}(t);o&&(e=o+"/skin.min.css",t.contentCSS.push(o+(n?"/content.inline":"/content")+".min.css")),!1===function(n){return!1===n.getParam("skin")}(t)&&e?Fh.DOM.styleSheetLoader.load(e,w_(t)):w_(t)()}function GB(t,n,e,o){var r=n.outerContainer,i=e.toolbar,u=e.buttons;if(h(i,cn)){var a=i.map(function(n){return sB(t,{toolbar:n,buttons:u},{backstage:o},on.none())});y_.setToolbars(r,a)}else y_.setToolbar(r,sB(t,e,{backstage:o},on.none()))}function XB(n,t){return function(){n.execCommand("mceToggleFormat",!1,t)}}function YB(n){!function(e){hk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(n,t){e.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onSetup:aT(e,n.name),onAction:XB(e,n.name)})});for(var n=1;n<=6;n++){var t="h"+n;e.ui.registry.addToggleButton(t,{text:t.toUpperCase(),tooltip:"Heading "+n,onSetup:aT(e,t),onAction:XB(e,t)})}}(n),function(t){hk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(n){t.ui.registry.addButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)}})})}(n),function(t){hk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)},onSetup:aT(t,n.name)})})}(n)}function qB(n,t,e){function o(){return!!t.undoManager&&t.undoManager[e]()}function r(){n.setDisabled(t.readonly||!o())}return n.setDisabled(!o()),t.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return t.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}}function KB(n,t){return{anchor:"makeshift",x:n,y:t}}function JB(n){return"longpress"===n.type||0===n.type.indexOf("touch")}function $B(n,t){var e=Fh.DOM.getPos(n);return function(n,t,e){return KB(n.x+t,n.y+e)}(t,e.x,e.y)}function QB(n,t){return"contextmenu"===t.type?n.inline?function(n){if(JB(n)){var t=n.touches[0];return KB(t.pageX,t.pageY)}return KB(n.pageX,n.pageY)}(t):$B(n.getContentAreaContainer(),function(n){if(JB(n)){var t=n.touches[0];return KB(t.clientX,t.clientY)}return KB(n.clientX,n.clientY)}(t)):V_(n)}function ZB(n){return{anchor:"node",node:on.some(Be.fromDom(n.selection.getNode())),root:Be.fromDom(n.getBody())}}function nD(n,t,e,o,r,i){var u=e(),a=function(n,t,e){return e?ZB(n):QB(n,t)}(n,t,i);IC(u,Ah.CLOSE_ON_EXECUTE,o,!1).map(function(n){t.preventDefault(),zg.showMenuAt(r,a,{menu:{markers:yv("normal")},data:n})})}function tD(t,e,n,o,r,i){var u=Fb(t)||Ib(t)||zb(t),a=function(n,t){var e=t?ZB(n):V_(n);return P({bubble:ka(0,12,H_),layouts:N_,overrides:{maxWidthFunction:MT(),maxHeightFunction:Sf()}},e)}(t,i);IC(n,Ah.CLOSE_ON_EXECUTE,o,!0).map(function(n){e.preventDefault(),zg.showMenuWithinBounds(r,a,{menu:{markers:yv("normal")},data:n,type:"horizontal"},function(){return iT(t,u)}),t.fire(WT)})}function eD(t,e,o,r,i,u){function n(){var n=o();tD(t,e,n,r,i,u)}var a=Ht(),c=a.os.isiOS(),s=a.os.isOSX(),f=a.os.isAndroid();if(!s&&!c||u)f&&!u&&t.selection.setCursorLocation(e.target,0),n();else{var l=function(){!function(n){function t(){Xg.setEditorTimeout(n,function(){n.selection.setRng(e)},10),i()}var e=n.selection.getRng();n.once("touchend",t);function o(n){n.preventDefault(),n.stopImmediatePropagation()}n.on("mousedown",o,!0);function r(){return i()}n.once("longpresscancel",r);var i=function(){n.off("touchend",t),n.off("longpresscancel",r),n.off("mousedown",o)}}(t),n()};!function(n,t){var e=n.selection;if(e.isCollapsed()||t.touches.length<1)return!1;var o=t.touches[0],r=e.getRng();return jc(n.getWin(),Tc.domRange(r)).exists(function(n){return n.left()<=o.clientX&&n.right()>=o.clientX&&n.top()<=o.clientY&&n.bottom()>=o.clientY})}(t,e)?(t.once("selectionchange",l),t.once("touchend",function(){return t.off("selectionchange",l)})):l()}}function oD(n){return"string"==typeof n?n.split(/[ ,]/):n}function rD(n){return cn(n)?"|"===n:"separator"===n.type}function iD(n,t){if(0===t.length)return n;var e=M(n).filter(function(n){return!rD(n)}).fold(function(){return[]},function(n){return[j_]});return n.concat(e).concat(t).concat([j_])}function uD(i,n,t){function e(n){return zg.hide(a)}function o(o){var n="longpress"===o.type;if(P_(i)&&o.preventDefault(),!function(n,t){return t.ctrlKey&&!P_(n)}(i,o)&&!L_(i)){var r=!n&&(2!==o.button||o.target===i.getBody());(u()?eD:nD)(i,o,function(){var n=r?i.selection.getStart(!0):o.target,t=i.ui.registry.getAll(),e=z_(i);return function(r,n,i){var t=k(n,function(n,t){if(Tn(r,t)){var e=r[t].update(i);if(cn(e))return iD(n,e.split(" "));if(0<e.length){var o=w(e,U_);return iD(n,o)}return n}return n.concat([t])},[]);return 0<t.length&&rD(t[t.length-1])&&t.pop(),t}(t.contextMenus,e,n)},t,a,r)}}var u=Ht().deviceType.isTouch,a=iu(zg.sketch({dom:{tag:"div"},lazySink:n,onEscape:function(){return i.focus()},onShow:function(){return t.setContextMenuState(!0)},onHide:function(){return t.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:va([qd("dismissContextMenu",[or(Ho(),function(n,t){Pf.close(n),i.focus()})])])}));i.on("init",function(){var n="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(u()?"":"ResizeWindow");i.on(n,e),i.on(u()?"longpress":"longpress contextmenu",o)})}function aD(n,t){n.getSystem().addToGui(t),function(n){hr(n.element()).filter(Bi).each(function(t){di(t,"z-index").each(function(n){Dr(t,X_,n)}),si(t,"z-index",li(n.element(),"z-index"))})}(t)}function cD(n){!function(n){hr(n.element()).filter(Bi).each(function(n){var t=_r(n,X_);Ar(n,X_)?si(n,"z-index",t):gi(n,"z-index"),Mr(n,X_)})}(n),n.getSystem().removeFromGui(n)}function sD(n,t,e,o){return function(n,t){var e=n.element(),o=parseInt(_r(e,t.leftAttr),10),r=parseInt(_r(e,t.topAttr),10);return isNaN(o)||isNaN(r)?on.none():on.some(Fu(o,r))}(n,t).fold(function(){return e},function(n){return jD(n.left()+o.left(),n.top()+o.top())})}function fD(n,t,e,o,r,i){var u=sD(n,t,e,o),a=t.mustSnap?Y_(n,t,u,r,i):q_(n,t,u,r,i),c=gB(u,r,i);return function(n,t,e){var o=n.element();Dr(o,t.leftAttr,e.left()+"px"),Dr(o,t.topAttr,e.top()+"px")}(n,t,c),a.fold(function(){return{coord:jD(c.left(),c.top()),extra:on.none()}},function(n){return{coord:n.output(),extra:n.extra()}})}function lD(n,t){!function(n,t){var e=n.element();Mr(e,t.leftAttr),Mr(e,t.topAttr)}(n,t)}function dD(n,e,o,r){return Bu(n,function(n){var t=n.sensor();return function(n,t,e,o,r,i){var u=pB(n,r,i),a=pB(t,r,i);return Math.abs(u.left()-a.left())<=e&&Math.abs(u.top()-a.top())<=o}(e,t,n.range().left(),n.range().top(),o,r)?on.some({output:nn(bB(n.output(),e,o,r)),extra:n.extra}):on.none()})}function mD(n,t){return{bounds:n.getBounds(),height:su(t.element()),width:mu(t.element()),comp:t}}function gD(e,n,o,r,i,u,t){return function(n,t,e,o,r){var i=r.bounds,u=pB(t,e,o),a=is(u.left(),i.x(),i.x()+i.width()-r.width),c=is(u.top(),i.y(),i.y()+i.height()-r.height),s=LD(a,c);return t.fold(function(){var n=hB(s,e,o);return zD(n.left(),n.top())},function(){return s},function(){var n=gB(s,e,o);return jD(n.left(),n.top())})}(0,n.fold(function(){var n=function(n,e,o){return n.fold(function(n,t){return PD.offset(n+e,t+o)},function(n,t){return PD.absolute(n+e,t+o)},function(n,t){return PD.fixed(n+e,t+o)})}(o,u.left(),u.top()),t=gB(n,r,i);return jD(t.left(),t.top())},function(t){var n=fD(e,t,o,u,r,i);return n.extra.each(function(n){t.onSensor(e,n)}),n.coord}),r,i,t)}function pD(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=gr(n.element()),u=gu(i),a=fB(r),c=function(o){return di(o,"left").bind(function(e){return di(o,"top").bind(function(t){return di(o,"position").map(function(n){return("fixed"===n?jD:zD)(parseInt(e,10),parseInt(t,10))})})}).getOrThunk(function(){var n=lu(o);return LD(n.left(),n.top())})}(r),s=gD(n,t.snaps,c,u,a,o,e),f=vB(s,0,a);fi(r,f)}t.onDrag(n,r,o)}var hD,vD,bD,yD,xD,wD=[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}],SD=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],CD={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},kD=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],OD={button:uB(YO,function(n,t){return function(n,t){return LE(n,t,[])}(n,t.backstage.shared.providers)}),togglebutton:uB(KO,function(n,t){return function(n,t){return jE(n,t,[])}(n,t.backstage.shared.providers)}),menubutton:uB(qO,function(n,t){return VC(n,"tox-tbtn",t.backstage,on.none())}),splitbutton:uB(function(n){return tt("SplitButton",vE,n)},function(n,t){return tT(n,t.backstage.shared)}),styleSelectButton:function(n,t){return function(n,t){var e=P({type:"advanced"},t.styleselect);return fT(n,t,iB(n,e))}(n,t.backstage)},fontsizeSelectButton:function(n,t){return function(n,t){return fT(n,t,tB(n))}(n,t.backstage)},fontSelectButton:function(n,t){return function(n,t){return fT(n,t,ZE(n))}(n,t.backstage)},formatButton:function(n,t){return function(n,t){return fT(n,t,rB(n))}(n,t.backstage)},alignMenuButton:function(n,t){return function(n,t){return fT(n,t,$E(n))}(n,t.backstage)}},TD={styleselect:OD.styleSelectButton,fontsizeselect:OD.fontsizeSelectButton,fontselect:OD.fontSelectButton,formatselect:OD.formatButton,align:OD.alignMenuButton},ED={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},BD={maxHeightFunction:Sf(),maxWidthFunction:MT()},DD={onLtr:function(){return[ec,oc,aa,ia,ca,ua,Lg,jg,dm,fm,mm,lm]},onRtl:function(){return[ec,oc,ca,ua,aa,ia,Lg,jg,mm,lm,dm,fm]}},_D={onLtr:function(){return[oc,ia,ua,aa,ca,ec,Lg,jg,dm,fm,mm,lm]},onRtl:function(){return[oc,ua,ia,ca,aa,ec,Lg,jg,mm,lm,dm,fm]}},AD=function(u,n,e,a){function c(){return iT(u,m)}function s(){if(l()&&a.backstage.isContextMenuOpen())return!0;var n=p.get().map(function(n){return n.getBoundingClientRect()}).getOrThunk(function(){return u.selection.getRng().getBoundingClientRect()}),t=pr(Be.fromDom(u.getBody())).dom().innerHeight,e=n.bottom<0,o=n.top>t;return e||o||function(n){var t=Cu(Be.fromDom(u.getContainer()),".tox-editor-header").getOrDie(),e="fixed"===li(t,"position");if(m&&e){var o=t.dom().getBoundingClientRect();if(u.inline)return n.bottom<o.bottom;var r=gu(),i=xu(Be.fromDom(u.getBody()));return n.bottom+(i.y()-r.top())<o.bottom}return!1}(n)}function t(){zg.hide(d)}function o(){g.get().each(function(n){var t=d.element();gi(t,"display"),s()?si(t,"display","none"):Df.positionWithinBounds(e,n,d,c())})}function f(n){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[n],behaviours:va([sg.config({mode:"acyclic"}),qd("pop-dialog-wrap-events",[Fi(function(n){u.shortcuts.add("ctrl+F9","focus statusbar",function(){return sg.focusIn(n)})}),Ii(function(n){u.shortcuts.remove("ctrl+F9")})])])}}var l=Ht().deviceType.isTouch,d=iu(function(n){var e=Te([]);return zg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(n){e.set([]),zg.getContent(n).each(function(n){gi(n.element(),"visibility")}),ei(n.element(),KE),gi(n.element(),"width")},inlineBehaviours:va([qd("context-toolbar-events",[sr(vo(),function(n,t){zg.getContent(n).each(function(n){}),ei(n.element(),KE),gi(n.element(),"width")}),or(qE,function(t,e){gi(t.element(),"width");var n=du(t.element());zg.setContent(t,e.event().contents()),ni(t.element(),KE);var o=du(t.element());si(t.element(),"width",n+"px"),zg.getContent(t).each(function(n){e.event().focus().bind(function(n){return xa(n),Sa(t.element())}).orThunk(function(){return sg.focusIn(n),wa()})}),Xg.setTimeout(function(){si(t.element(),"width",o+"px")},0)}),or(XE,function(n,t){zg.getContent(n).each(function(n){e.set(e.get().concat([{bar:n,focus:wa()}]))}),Yt(n,qE,{contents:t.event().forwardContents(),focus:on.none()})}),or(YE,function(t,n){M(e.get()).each(function(n){e.set(e.get().slice(0,e.get().length-1)),Yt(t,qE,{contents:uu(n.bar),focus:n.focus})})})]),sg.config({mode:"special",onEscape:function(t){return M(e.get()).fold(function(){return n.onEscape()},function(n){return Xt(t,YE),on.some(!0)})}})]),lazySink:function(){return an.value(n.sink)}})}({sink:e,onEscape:function(){return u.focus(),on.some(!0)}})),m=Fb(u)||Ib(u)||zb(u),g=Te(on.none()),p=Te(on.none()),r=Te(null),i=N(function(){return GE(n,function(n){var t=h(n);Yt(d,XE,{forwardContents:f(t)})})}),h=function(n){var t,e,o=u.ui.registry.getAll().buttons,r=i();return"contexttoolbar"===n.type?(t=_n(o,r.formNavigators),e=sB(u,{buttons:t,toolbar:n.items},a,on.some(["form:"])),XO({uid:Vr("context-toolbar"),initGroups:e,onEscape:on.none,cyclicKeying:!0,backstage:a.backstage,getSink:function(){return an.error("")}})):UE(n,a.backstage)};u.on("contexttoolbar-show",function(t){var n=i();Nn(n.lookupTable,t.toolbarKey).each(function(n){x(n,t.target===u?on.none():on.some(t)),zg.getContent(d).each(sg.focusIn)})});function v(n,t){var e="node"===n?a.backstage.shared.anchors.node(t):a.backstage.shared.anchors.cursor();return Dn(e,function(n,t){return"line"===n?{bubble:ka(12,0,ED),layouts:{onLtr:function(){return[sa]},onRtl:function(){return[fa]}},overrides:BD}:{bubble:ka(0,12,ED),layouts:t?_D:DD,overrides:BD}}(n,l()))}function b(){var n=i();WE(n,u).fold(function(){g.set(on.none()),zg.hide(d)},function(n){x(n.toolbarApi,on.some(n.elem.dom()))})}function y(n){w(),r.set(n)}var x=function(n,t){if(w(),!l()||!a.backstage.isContextMenuOpen()){var e=h(n),o=t.map(Be.fromDom),r=v(n.position,o);g.set(on.some(r)),p.set(t);var i=d.element();gi(i,"display"),zg.showWithinBounds(d,r,f(e),c),s()&&si(i,"display","none")}},w=function(){var n=r.get();null!==n&&(Xg.clearTimeout(n),r.set(null))};u.on("init",function(){u.on(WT,t),u.on("ScrollContent ScrollWindow longpress",o),u.on("click keyup SetContent ObjectResized ResizeEditor",function(n){y(Xg.setEditorTimeout(u,b,0))}),u.on("focusout",function(n){Xg.setEditorTimeout(u,function(){Sa(e.element()).isNone()&&Sa(d.element()).isNone()&&(g.set(on.none()),zg.hide(d))},0)}),u.on("SwitchMode",function(){u.readonly&&(g.set(on.none()),zg.hide(d))}),u.on("NodeChange",function(n){Sa(d.element()).fold(function(){y(Xg.setEditorTimeout(u,b,0))},function(n){})})})},MD=function(n,e,o){function t(t){bn([e,o],function(n){n.broadcastOn([zf()],{target:Be.fromDom(t.target)})})}function r(t){0===t.button&&bn([e,o],function(n){n.broadcastOn([jf()],{target:Be.fromDom(t.target)})})}function i(n){var t=mb(n);bn([e,o],function(n){n.broadcastEvent(Io(),t)})}function u(n){var t=mb(n);bn([e,o],function(n){n.broadcastOn([Lf()],{}),n.broadcastEvent(Ro(),t)})}function a(){bn([e,o],function(n){n.broadcastOn([Lf()],{})})}var c=ab(Be.fromDom(H.document),"mousedown",function(t){bn([e,o],function(n){n.broadcastOn([zf()],{target:t.target()})})}),s=ab(Be.fromDom(H.document),"touchstart",function(t){bn([e,o],function(n){n.broadcastOn([zf()],{target:t.target()})})}),f=ab(Be.fromDom(H.document),"touchmove",function(t){bn([e,o],function(n){n.broadcastEvent(Mo(),t)})}),l=ab(Be.fromDom(H.document),"touchend",function(t){bn([e,o],function(n){n.broadcastEvent(Fo(),t)})}),d=ab(Be.fromDom(H.document),"mouseup",function(t){0===t.raw().button&&bn([e,o],function(n){n.broadcastOn([jf()],{target:t.target()})})});n.on("PostRender",function(){n.on("mousedown",t),n.on("touchstart",t),n.on("mouseup",r),n.on("ScrollWindow",i),n.on("ResizeWindow",u),n.on("ResizeEditor",a)}),n.on("remove",function(){n.off("mousedown",t),n.off("touchstart",t),n.off("mouseup",r),n.off("ScrollWindow",i),n.off("ResizeWindow",u),n.off("ResizeEditor",a),c.unbind(),s.unbind(),f.unbind(),l.unbind(),d.unbind()}),n.on("detach",function(){xs(e),xs(o),e.destroy(),o.destroy()})},FD=nn([St("shell",!1),ct("makeItem"),St("setupItem",Z),Zf("listBehaviours",[lg])]),ID=wl({name:"items",overrides:function(n){return{behaviours:va([lg.config({})])}}}),RD=nn([ID]),VD=Dl({name:nn("CustomList")(),configFields:FD(),partFields:RD(),factory:function(s,n,t,e){var o=s.shell?{behaviours:[lg.config({})],components:[]}:{behaviours:[],components:n},r=function(n){return s.shell?on.some(n):Ys(n,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Is(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw H.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(t){var n=lg.contents(t),e=c.length,o=e-n.length,r=0<o?function(n,t){for(var e=[],o=0;o<n;o++)e.push(t(o));return e}(o,function(){return s.makeItem()}):[],i=n.slice(e);bn(i,function(n){return lg.remove(t,n)}),bn(r,function(n){return lg.append(t,n)});var u=lg.contents(t);bn(u,function(n,t){s.setupItem(a,n,c[t],t)})})}}}},apis:{setItems:function(n,t,e){n.setItems(t,e)}}}),ND=Tl,HD=kl,PD=En([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),zD=PD.offset,LD=PD.absolute,jD=PD.fixed,UD=En([{"static":[]},{absolute:["x","y"]},{fixed:["x","y"]}]),WD=function(n,t,e){n.getSystem().isConnected()&&function(e,o,r){var i=o.lazyViewport(e),n=e.element(),t=gr(n),u=gu(t),a=fB(n),c=r.isDocked();c&&DB(e,o,r,i),TB(e,o,i,u,a).each(function(n){r.setDocked(!c),n.fold(function(){return EB(e,o)},function(n,t){return BB(e,o,0,a,LD(n,t))},function(n,t){DB(e,o,r,i,!0),BB(e,o,0,a,jD(n,t))})})}(n,t,e)},GD=/* */Object.freeze({refresh:WD,reset:AB,isDocked:function(n,t,e){return e.isDocked()}}),XD=/* */Object.freeze({events:function(o,r){return nr([sr(vo(),function(t,e){o.contextual.each(function(n){oi(t.element(),n.transitionClass)&&(ii(t.element(),[n.transitionClass,n.fadeInClass]),(r.isVisible()?n.onShown:n.onHidden)(t));e.stop()})}),or(Io(),function(n,t){WD(n,o,r)}),or(Ro(),function(n,t){AB(n,o,r)})])}}),YD=[wt("contextual",[ft("fadeInClass"),ft("fadeOutClass"),ft("transitionClass"),dt("lazyContext"),Yu("onShow"),Yu("onShown"),Yu("onHide"),Yu("onHidden")]),Bt("lazyViewport",wu),ft("leftAttr"),ft("topAttr"),ft("positionAttr"),(hD="modes",vD=["top","bottom"],bD=we,Ct(hD,vD,Kn(bD))),Yu("onDocked"),Yu("onUndocked")],qD=ba({fields:YD,name:"docking",active:XD,apis:GD,state:/* */Object.freeze({init:function(){var t=Te(!1),e=Te(!0);return Zi({isDocked:function(){return t.get()},setDocked:function(n){return t.set(n)},isVisible:function(){return e.get()},setVisible:function(n){return e.set(n)},readState:function(){return"docked:  "+t.get()+", visible: "+e.get()}})}})}),KD={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},JD="tox-tinymce--toolbar-sticky-on",$D="tox-tinymce--toolbar-sticky-off",QD=/* */Object.freeze({setup:function(n,t){n.inline||(n.on("ResizeWindow ResizeEditor ResizeContent",function(){t().each(MB)}),n.on("SkinLoaded",function(){t().each(qD.reset)})),n.on("PostRender",function(){IB(n,!1)})},isDocked:function(n){return n().map(qD.isDocked).getOr(!1)},getBehaviours:RB}),ZD=Z,n_=u,t_=nn([]),e_=/* */Object.freeze({setup:ZD,isDocked:n_,getBehaviours:t_}),o_=Bl({factory:function(t,o){var n={focus:sg.focusIn,setMenus:function(n,t){var e=w(t,function(t){var n={type:"menubutton",text:t.text,fetch:function(n){n(t.getItems())}},e=qO(n).mapError(function(n){return be(n)}).getOrDie();return VC(e,"tox-mbtn",o.backstage,on.some("menuitem"))});lg.set(n,e)}};return{uid:t.uid,dom:t.dom,components:[],behaviours:va([lg.config({}),qd("menubar-events",[Fi(function(n){t.onSetup(n)}),or(co(),function(e,n){Cu(e.element(),".tox-mbtn--active").each(function(t){ku(n.event().target(),".tox-mbtn").each(function(n){jt(t,n)||e.getSystem().getByDom(t).each(function(t){e.getSystem().getByDom(n).each(function(n){vw.expand(n),vw.close(t),pg.focus(n)})})})})}),or(zo(),function(e,n){n.event().prevFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(t){n.event().newFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(n){vw.isOpen(t)&&(vw.expand(n),vw.close(t))})})})]),sg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(n){return t.onEscape(n),on.some(!0)}}),Ny.config({})]),apis:n,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ct("dom"),ct("uid"),ct("onEscape"),ct("backstage"),St("onSetup",Z)],apis:{focus:function(n,t){n.focus(t)},setMenus:function(n,t,e){n.setMenus(t,e)}}}),r_="container",i_=[Ms("slotBehaviours",[])],u_=function(r,n,t){function e(n){return $s(r)}function o(e,o){return void 0===o&&(o=undefined),function(n,t){return Ys(n,r,t).map(function(n){return e(n,t)}).getOr(o)}}function i(n,t){return"true"!==_r(n.element(),"aria-hidden")}var u,a=o(i,!1),c=o(function(n,t){if(i(n)){var e=n.element();si(e,"display","none"),Dr(e,"aria-hidden","true"),Yt(n,Lo(),{name:t,visible:!1})}}),s=(u=c,function(t,n){bn(n,function(n){return u(t,n)})}),f=o(function(n,t){if(!i(n)){var e=n.element();gi(e,"display"),Mr(e,"aria-hidden"),Yt(n,Lo(),{name:t,visible:!0})}}),l={getSlotNames:e,getSlot:function(n,t){return Ys(n,r,t)},isShowing:a,hideSlot:c,hideAllSlots:function(n){return s(n,e())},showSlot:f};return{uid:r.uid,dom:r.dom,components:n,behaviours:Fs(r.slotBehaviours),apis:l}},a_=L({getSlotNames:function(n,t){return n.getSlotNames(t)},getSlot:function(n,t,e){return n.getSlot(t,e)},isShowing:function(n,t,e){return n.isShowing(t,e)},hideSlot:function(n,t,e){return n.hideSlot(t,e)},hideAllSlots:function(n,t){return n.hideAllSlots(t)},showSlot:function(n,t,e){return n.showSlot(t,e)}},Lr),c_=P(P({},a_),{sketch:function(n){var e,t=(e=[],{slot:function(n,t){return e.push(n),js(r_,VB(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=w(r,function(n){return yl({name:n,pname:VB(n)})});return ef(r_,i_,i,u_,o)}}),s_=le([yt("icon"),yt("tooltip"),Bt("onShow",Z),Bt("onHide",Z),Bt("onSetup",function(){return Z})]),f_=Vr("FixSizeEvent"),l_=Vr("AutoSizeEvent"),d_=HD.optional({factory:o_,name:"menubar",schema:[ct("backstage")]}),m_=HD.optional({factory:{sketch:function(n){return VD.sketch({uid:n.uid,dom:n.dom,listBehaviours:va([sg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return XO({uid:Vr("multiple-toolbar-item"),backstage:n.backstage,cyclicKeying:!1,getSink:n.getSink,initGroups:[],onEscape:function(){return on.none()}})},setupItem:function(n,t,e,o){YT.setGroups(t,e)},shell:!0})}},name:"multiple-toolbar",schema:[ct("dom"),ct("onEscape")]}),g_=HD.optional({factory:{sketch:function(n){return function(n){return n.split===Vb.sliding?GO:n.split===Vb.floating?WO:XO}(n)({uid:n.uid,onEscape:function(){return n.onEscape(),on.some(!0)},cyclicKeying:!1,initGroups:[],getSink:n.getSink,backstage:n.backstage,moreDrawerData:{lazyToolbar:n.lazyToolbar,lazyMoreButton:n.lazyMoreButton,lazyHeader:n.lazyHeader}})}},name:"toolbar",schema:[ct("dom"),ct("onEscape"),ct("getSink")]}),p_=HD.optional({factory:{sketch:function(n){var t=n.editor,e=n.sticky?RB:t_;return{uid:n.uid,dom:n.dom,components:n.components,behaviours:va(e(t,n.getSink))}}},name:"header",schema:[ct("dom")]}),h_=HD.optional({name:"socket",schema:[ct("dom")]}),v_=HD.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:va([Ny.config({}),pg.config({}),iE.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(n){Ql.getCurrent(n).each(c_.hideAllSlots),Xt(n,l_)},onGrown:function(n){Xt(n,l_)},onStartGrow:function(n){Yt(n,f_,{width:di(n.element(),"width").getOr("")})},onStartShrink:function(n){Yt(n,f_,{width:du(n.element())+"px"})}}),lg.config({}),Ql.config({find:function(n){var t=lg.contents(n);return yn(t)}})])}],behaviours:va([yS(0),qd("sidebar-sliding-events",[or(f_,function(n,t){si(n.element(),"width",t.event().width())}),or(l_,function(n,t){gi(n.element(),"width")})])])}}},name:"sidebar",schema:[ct("dom")]}),b_=HD.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:va([lg.config({})]),components:[]}}},name:"throbber",schema:[ct("dom")]}),y_=Dl({name:"OuterContainer",factory:function(e,n,t){var o={getSocket:function(n){return ND.getPart(n,e,"socket")},setSidebar:function(n,t){ND.getPart(n,e,"sidebar").each(function(n){return PB(n,t)})},toggleSidebar:function(n,t){ND.getPart(n,e,"sidebar").each(function(n){return function(n,e){Ql.getCurrent(n).each(function(t){Ql.getCurrent(t).each(function(n){iE.hasGrown(t)?c_.isShowing(n,e)?iE.shrink(t):(c_.hideAllSlots(n),c_.showSlot(n,e)):(c_.hideAllSlots(n),c_.showSlot(n,e),iE.grow(t))})})}(n,t)})},whichSidebar:function(n){return ND.getPart(n,e,"sidebar").bind(zB).getOrNull()},getHeader:function(n){return ND.getPart(n,e,"header")},getToolbar:function(n){return ND.getPart(n,e,"toolbar")},setToolbar:function(n,t){ND.getPart(n,e,"toolbar").each(function(n){n.getApis().setGroups(n,t)})},setToolbars:function(n,t){ND.getPart(n,e,"multiple-toolbar").each(function(n){VD.setItems(n,t)})},refreshToolbar:function(n){ND.getPart(n,e,"toolbar").each(function(n){return n.getApis().refresh(n)})},getMoreButton:function(n){return ND.getPart(n,e,"toolbar").bind(function(n){return n.getApis().getMoreButton(n)})},getThrobber:function(n){return ND.getPart(n,e,"throbber")},focusToolbar:function(n){ND.getPart(n,e,"toolbar").orThunk(function(){return ND.getPart(n,e,"multiple-toolbar")}).each(function(n){sg.focusIn(n)})},setMenubar:function(n,t){ND.getPart(n,e,"menubar").each(function(n){o_.setMenus(n,t)})},focusMenubar:function(n){ND.getPart(n,e,"menubar").each(function(n){o_.focus(n)})}};return{uid:e.uid,dom:e.dom,components:n,apis:o,behaviours:e.behaviours}},configFields:[ct("dom"),ct("behaviours")],partFields:[p_,d_,g_,m_,h_,v_,b_],apis:{getSocket:function(n,t){return n.getSocket(t)},setSidebar:function(n,t,e){n.setSidebar(t,e)},toggleSidebar:function(n,t,e){n.toggleSidebar(t,e)},whichSidebar:function(n,t){return n.whichSidebar(t)},getHeader:function(n,t){return n.getHeader(t)},getToolbar:function(n,t){return n.getToolbar(t)},setToolbar:function(n,t,e){var o=w(e,function(n){return LO(n)});n.setToolbar(t,o)},setToolbars:function(n,t,e){var o=w(e,function(n){return w(n,LO)});n.setToolbars(t,o)},getMoreButton:function(n,t){return n.getMoreButton(t)},refreshToolbar:function(n,t){return n.refreshToolbar(t)},getThrobber:function(n,t){return n.getThrobber(t)},setMenubar:function(n,t,e){n.setMenubar(t,e)},focusMenubar:function(n,t){n.focusMenubar(t)},focusToolbar:function(n,t){n.focusToolbar(t)}}}),x_={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable tableprops deletetable row column cell"},help:{title:"Help",items:"help"}},w_=function(n){function t(){n._skinLoaded=!0,Rv(n)}return function(){n.initialized?t():n.on("init",t)}},S_=d(WB,!1),C_=d(WB,!0),k_=Fh.DOM,O_=Ht(),T_=O_.os.isiOS()&&O_.os.version.major<=12,E_={render:function(e,o,n,t,r){var i=Te(0);S_(e),function(n,t){Ff(n,t,wr)}(Be.fromDom(r.targetNode),o.mothership),ys(_i(),o.uiMothership),e.on("PostRender",function(){GB(e,o,n,t),i.set(e.getWin().innerWidth),y_.setMenubar(o.outerContainer,UB(e,n)),y_.setSidebar(o.outerContainer,n.sidebar),function(r){function n(n){var t=r.getDoc().documentElement,e=u.get(),o=a.get();e.left()!==i.innerWidth||e.top()!==i.innerHeight?(u.set(Fu(i.innerWidth,i.innerHeight)),Hv(r,n)):o.left()===t.offsetWidth&&o.top()===t.offsetHeight||(a.set(Fu(t.offsetWidth,t.offsetHeight)),Hv(r,n))}function t(n){return Nv(r,n)}var i=r.getWin(),e=r.getDoc().documentElement,u=Te(Fu(i.innerWidth,i.innerHeight)),a=Te(Fu(e.offsetWidth,e.offsetHeight));k_.bind(i,"resize",n),k_.bind(i,"scroll",t);var o=cb(Be.fromDom(r.getBody()),"load",n);r.on("remove",function(){o.unbind(),k_.unbind(i,"resize",n),k_.unbind(i,"scroll",t)})}(e)});var u=y_.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===T_){fi(u.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var a=function(e,o){var r=null;return{cancel:function(){null!==r&&(H.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null===r&&(r=H.setTimeout(function(){e.apply(null,n),r=null},o))}}}(function(){e.fire("ScrollContent")},20);ab(u.element(),"scroll",a.throttle)}HO(e,o),e.addCommand("ToggleSidebar",function(n,t){y_.toggleSidebar(o.outerContainer,t),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return y_.whichSidebar(o.outerContainer)});var c=Lb(e);return c!==Vb.sliding&&c!==Vb.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var n=e.getWin().innerWidth;n!==i.get()&&(y_.refreshToolbar(o.outerContainer),i.set(n))}),{iframeContainer:u.element().dom(),editorContainer:o.outerContainer.element().dom()}}},B_={render:function(t,r,e,o,n){var i,u=Fh.DOM,a=Ub(t),c=Gb(t),s=Be.fromDom(n.targetNode),f=Lb(t),l=f===Vb.sliding||f===Vb.floating;C_(t);function d(n){void 0===n&&(n=!1),l&&y_.refreshToolbar(r.outerContainer),a||function(n){var t=l?n.fold(function(){return 0},function(n){return 1<n.components().length?cu(n.components()[1].element()):0}):0,e=lu(s),o=e.top()-cu(i.element())+t;fi(r.outerContainer.element(),{position:"absolute",top:Math.round(o)+"px",left:Math.round(e.left())+"px"})}(y_.getToolbar(r.outerContainer)),c&&(n?qD.reset(i):qD.refresh(i))}function m(){si(r.outerContainer.element(),"display","flex"),u.addClass(t.getBody(),"mce-edit-focus"),gi(r.uiMothership.element(),"display"),d()}function g(){r.outerContainer&&(si(r.outerContainer.element(),"display","none"),u.removeClass(t.getBody(),"mce-edit-focus")),si(r.uiMothership.element(),"display","none")}function p(){if(i)m();else{i=y_.getHeader(r.outerContainer).getOrDie();var n=function(n){return jb(n).getOr(_i())}(t);ys(n,r.mothership),ys(n,r.uiMothership),GB(t,r,e,o),y_.setMenubar(r.outerContainer,UB(t,e)),m(),t.on("activate",m),t.on("deactivate",g),t.on("NodeChange SkinLoaded ResizeWindow",function(){t.hidden||d(!0)}),t.nodeChanged()}}return t.on("focus",p),t.on("blur hide",g),t.on("init",function(){t.hasFocus()&&p()}),HO(t,r),{editorContainer:r.outerContainer.element().dom()}}},D_=function(t){hk.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,onAction:function(){return t.execCommand(n.cmd)},icon:n.icon,onSetup:aT(t,n.name)})});var n="alignnone",e="No alignment",o="JustifyNone",r="align-none";t.ui.registry.addButton(n,{tooltip:e,onAction:function(){return t.execCommand(o)},icon:r})},__=function(n){YB(n),function(t){hk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(n){t.ui.registry.addMenuItem(n.name,{text:n.text,icon:n.icon,shortcut:n.shortcut,onAction:function(){return t.execCommand(n.action)}})}),t.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:XB(t,"code")})}(n)},A_=function(n){!function(t){t.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(n){return qB(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(n){return qB(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n),function(t){t.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(n){return qB(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(n){return qB(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n)},M_=function(n){!function(n){n.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return n.execCommand("mceToggleVisualAid")}})}(n),function(t){t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(n){return function(t,n){t.setActive(n.hasVisual);function e(n){t.setActive(n.hasVisual)}return n.on("VisualAid",e),function(){return n.off("VisualAid",e)}}(n,t)},onAction:function(){t.execCommand("mceToggleVisualAid")}})}(n)},F_=function(n){!function(t){t.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(n){return function(n,t){n.setDisabled(!t.queryCommandState("outdent"));function e(){n.setDisabled(!t.queryCommandState("outdent"))}return t.on("NodeChange",e),function(){return t.off("NodeChange",e)}}(n,t)},onAction:function(){return t.execCommand("outdent")}}),t.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return t.execCommand("indent")}})}(n)},I_=function(n,t){!function(n,t){var e=sT(0,t,$E(n));n.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=sT(0,t,ZE(n));n.ui.registry.addNestedMenuItem("fontformats",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=P({type:"advanced"},t.styleselect),o=sT(0,t,iB(n,e));n.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return o.items.validateItems(o.getStyleItems())}})}(n,t),function(n,t){var e=sT(0,t,rB(n));n.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=sT(0,t,tB(n));n.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t)},R_=function(n,t){D_(n),__(n),I_(n,t),A_(n),Lv.register(n),M_(n),F_(n)},V_=function(n){return{anchor:"selection",root:Be.fromDom(n.selection.getNode())}},N_={onLtr:function(){return[oc,ia,ua,aa,ca,ec,Lg,jg,dm,fm,mm,lm]},onRtl:function(){return[oc,ua,ia,ca,aa,ec,Lg,jg,mm,lm,dm,fm]}},H_={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},P_=function(n){return n.settings.contextmenu_never_use_native||!1},z_=function(n){return function(n,t,e){var o=n.ui.registry.getAll().contextMenus;return R(n.settings,t).map(oD).getOrThunk(function(){return S(oD(e),function(n){return Tn(o,n)})})}(n,"contextmenu","link linkchecker image imagetools table spellchecker configurepermanentpen")},L_=function(n){return!1===n.getParam("contextmenu")},j_={type:"separator"},U_=function(t){if(cn(t))return t;switch(t.type){case"separator":return j_;case"submenu":return{type:"nestedmenuitem",text:t.text,icon:t.icon,getSubmenuItems:function(){var n=t.getSubmenuItems();return cn(n)?n:w(n,U_)}};default:return{type:"menuitem",text:t.text,icon:t.icon,onAction:function(n){return function(){return n()}}(t.onAction)}}},W_=function(n){return/^[0-9\.]+(|px)$/i.test(""+n)?on.some(parseInt(n,10)):on.none()},G_=function(n){return mn(n)?n+"px":n},X_="data-initial-z-index",Y_=function(n,t,r,i,u){var e=t.getSnapPoints(n);return dD(e,r,i,u).orThunk(function(){return k(e,function(t,e){var n=e.sensor(),o=function(n,t,e,o,r,i){var u=pB(n,r,i),a=pB(t,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top());return Fu(c,s)}(r,n,e.range().left(),e.range().top(),i,u);return t.deltas.fold(function(){return{deltas:on.some(o),snap:on.some(e)}},function(n){return(o.left()+o.top())/2<=(n.left()+n.top())/2?{deltas:on.some(o),snap:on.some(e)}:t})},{deltas:on.none(),snap:on.none()}).snap.map(function(n){return{output:nn(bB(n.output(),r,i,u)),extra:n.extra}})})},q_=function(n,t,e,o,r){var i=t.getSnapPoints(n);return dD(i,e,o,r)},K_=wt("snaps",[ct("getSnapPoints"),Yu("onSensor"),ct("leftAttr"),ct("topAttr"),St("lazyViewport",wu),St("mustSnap",!1)]),J_=/* */Object.freeze({getData:function(n){return on.from(Fu(n.x(),n.y()))},getDelta:function(n,t){return Fu(t.left()-n.left(),t.top()-n.top())}}),$_=[St("useFixed",u),ct("blockerClass"),St("getTarget",l),St("onDrag",Z),St("repositionTarget",!0),Yu("onDrop"),Bt("getBounds",wu),K_,$u("dragger",{handlers:function(u,a){function c(n){a.setStartData(mD(u,n))}return nr([or(Io(),c),or(ro(),function(o,n){if(0===n.event().raw().button){n.stop();var t={drop:function(){r()},delayDrop:function(){i.schedule()},forceDrop:function(){r()},move:function(n){i.cancel();var t=a.update(J_,n),e=a.getStartData().getOrThunk(function(){return mD(u,o)});t.each(function(n){pD(o,u,e,n)})}},e=o.getSystem().build(Hb.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[u.blockerClass]},events:function(e){return nr([or(ro(),e.forceDrop),or(ao(),e.drop),or(io(),function(n,t){e.move(t.event())}),or(uo(),e.delayDrop)])}(t)})),r=function(){cD(e),u.snaps.each(function(n){lD(o,n)});var n=u.getTarget(o.element());a.reset(),u.onDrop(o,n)},i=gb(r,200);c(o),aD(o,e)}})])}})],Q_=/* */Object.freeze({getData:function(n){var t=n.raw().touches;return 1===t.length?function(n){var t=n[0];return on.some(Fu(t.clientX,t.clientY))}(t):on.none()},getDelta:function(n,t){return Fu(t.left()-n.left(),t.top()-n.top())}}),Z_=$_,nA=[St("useFixed",u),St("getTarget",l),St("onDrag",Z),St("repositionTarget",!0),St("onDrop",Z),Bt("getBounds",wu),K_,$u("dragger",{handlers:function(i,u){function e(n){u.setStartData(mD(i,n))}return nr([or(Io(),e),or(to(),function(n,t){e(n),t.stop()}),or(Mo(),function(o,r){u.getStartData().each(function(n){if(jt(n.comp.element(),o.element())){r.stop();var t=u.update(Q_,r.event()),e=u.getStartData().getOrThunk(function(){return mD(i,o)});t.each(function(n){pD(o,i,e,n)})}})}),or(Fo(),function(e,n){u.getStartData().each(function(n){if(jt(n.comp.element(),e.element())){i.snaps.each(function(n){lD(e,n)});var t=i.getTarget(e.element());u.reset(),i.onDrop(e,t)}})})])}})],tA=/* */Object.freeze({mouse:Z_,touch:nA}),eA=/* */Object.freeze({init:function(){var o=on.none(),t=on.none(),n=nn({});return Zi({readState:n,reset:function(){o=on.none(),t=on.none()},update:function(t,n){return t.getData(n).bind(function(n){return function(t,e){var n=o.map(function(n){return t.getDelta(n,e)});return o=on.some(e),n}(t,n)})},getStartData:function(){return t},setStartData:function(n){t=on.some(n)}})}}),oA=/* */Object.freeze({snapTo:function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=gr(n.element()),u=gu(i),a=fB(r),c=function(n,t,e){return{coord:bB(n.output(),n.output(),t,e),extra:n.extra()}}(o,u,a),s=vB(c.coord,0,a);fi(r,s)}}}),rA=ya({branchKey:"mode",branches:tA,name:"dragging",active:{events:function(n,t){return n.dragger.handlers(n,t)}},extra:{snap:mr(["sensor","range","output"],["extra"])},state:eA,apis:oA});(xD=yD=yD||{})[xD.None=0]="None",xD[xD.Both=1]="Both",xD[xD.Vertical=2]="Vertical";function iA(n,t,e,o){var r=n+t,i=e.filter(function(n){return r<n}),u=o.filter(function(n){return n<r});return i.or(u).getOr(r)}function uA(n,t,e,o,r){var i={};return i.height=iA(o,t.top(),Mb(n),function(n){return on.from(n.getParam("max_height")).filter(mn)}(n)),e===yD.Both&&(i.width=iA(r,t.left(),Ab(n),function(n){return on.from(n.getParam("max_width")).filter(mn)}(n))),i}function aA(n){if(1===n.nodeType){if("BR"===n.nodeName||n.getAttribute("data-mce-bogus"))return!0;if("bookmark"===n.getAttribute("data-mce-type"))return!0}return!1}function cA(r,t){function n(o){return{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},innerHtml:vm("resize-handle",t.icons)},behaviours:va([rA.config({mode:"mouse",repositionTarget:!1,onDrag:function(n,t,e){!function(n,t,e){var o=Be.fromDom(n.getContainer()),r=uA(n,t,e,cu(o),du(o));Cn(r,function(n,t){return si(o,t,G_(n))}),Vv(n)}(r,e,o)},blockerClass:"tox-blocker"})])}}var e,o;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(e=function(){var n=[];return r.getParam("elementpath",!0,"boolean")&&n.push(KA(r,{})),Vt(r.settings.plugins,"wordcount")&&n.push(function(n,o){function r(n,t,e){return lg.set(n,[ki(o.translate(["{0} "+e,t[e]]))])}var t;return Ug.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:va([Ny.config({}),lg.config({}),Qf.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),qd("wordcount-events",[or(Eo(),function(n){var t=Qf.getValue(n),e="words"===t.mode?"characters":"words";Qf.setValue(n,{mode:e,count:t.count}),r(n,t.count,e)}),Fi(function(e){n.on("wordCountUpdate",function(n){var t=Qf.getValue(e).mode;Qf.setValue(e,{mode:t,count:n.wordCount}),r(e,n.wordCount,t)})})])]),eventOrder:(t={},t[Eo()]=["wordcount-events","alloy.base.behaviour"],t)})}(r,t)),r.getParam("branding",!0,"boolean")&&n.push(function(){var n=eh.translate(["Powered by {0}","Tiny"]);return{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+n+'">'+n+"</a>"}}}()),0<n.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:n}]:[]}(),o=function(n){var t=!Vt(n.settings.plugins,"autoresize"),e=n.getParam("resize",t);return!1===e?yD.None:"both"===e?yD.Both:yD.Vertical}(r),o!==yD.None&&e.push(n(o)),e)}}function sA(n){return n.touches===undefined||1!==n.touches.length?on.none():on.some(n.touches[0])}function fA(n){return[ft("type"),function(n){return st(n,xe)}("columns"),n]}function lA(t){return pe("items","items",Mn(),Kn(Zn(function(n){return tt("Checking item of "+t,$M,n).fold(function(n){return an.error(be(n))},function(n){return an.value(n)})})))}function dA(n){return cn(n.type)&&cn(n.name)}function mA(n){var t=function(n){return S(dF(n),dA)}(n),e=E(t,function(t){return function(n){return on.from(mF[n.type])}(t).fold(function(){return[]},function(n){return[st(t.name,n)]})});return le(e)}function gA(n){return{internalDialog:et(function(n){return tt("dialog",lF,n)}(n)),dataValidator:mA(n),initialData:n.initialData}}function pA(n){var e=[],o={};return Cn(n,function(n,t){n.fold(function(){e.push(t)},function(n){o[t]=n})}),0<e.length?an.error(e):an.value(o)}function hA(n){return yn(function(n,t){var e=gn.call(n,0);return e.sort(t),e}(n,function(n,t){return t<n?-1:n<t?1:0}))}function vA(e,o,n){Su(e,'[role="dialog"]').each(function(t){n.get().map(function(n){return si(o,"height","0"),Math.min(n,function(n,t){var e=Su(n,".tox-dialog-wrap").getOr(n);return("fixed"===li(e,"position")?Math.max(H.document.documentElement.clientHeight,H.window.innerHeight):Math.max(H.document.documentElement.offsetHeight,H.document.documentElement.scrollHeight))-(n.dom().getBoundingClientRect().height-t.dom().getBoundingClientRect().height)}(t,e))}).each(function(n){si(o,"height",n+"px")})})}function bA(r){var i;return{smartTabHeight:(i=Te(on.none()),{extraEvents:[Fi(function(n){Cu(n.element(),'[role="tabpanel"]').each(function(o){si(o,"visibility","hidden"),n.getSystem().getByDom(o).toOption().each(function(n){var t=function(o,r,i){return w(o,function(n,t){lg.set(i,o[t].view());var e=r.dom().getBoundingClientRect();return lg.set(i,[]),e.height})}(r,o,n),e=hA(t);i.set(e)}),vA(n.element(),o,i),gi(o,"visibility"),function(n,t){yn(n).each(function(n){return EF.showTab(t,n.value)})}(r,n),Xg.requestAnimationFrame(function(){vA(n.element(),o,i)})})}),or(Ro(),function(t){Cu(t.element(),'[role="tabpanel"]').each(function(n){vA(t.element(),n,i)})}),or(uy,function(r,n){Cu(r.element(),'[role="tabpanel"]').each(function(t){var n=wa();si(t,"visibility","hidden");var e=di(t,"height").map(function(n){return parseInt(n,10)});gi(t,"height");var o=t.dom().getBoundingClientRect().height;e.forall(function(n){return n<o})?(i.set(on.from(o)),vA(r.element(),t,i)):e.each(function(n){si(t,"height",n+"px")}),gi(t,"visibility"),n.each(xa)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}}function yA(n,t,e,o){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:P(P({},t.map(function(n){return{id:n}}).getOr({})),o?{"aria-live":"polite"}:{})},components:[],behaviours:va([yS(0),ME.config({channel:MF,updateState:function(n,t){return on.some({isTabPanel:function(){return"tabpanel"===t.body.type}})},renderComponents:function(n){switch(n.body.type){case"tabpanel":return[function(n,e){function o(n){var t=Qf.getValue(n),e=pA(t).getOr({}),o=i.get(),r=Dn(o,e);i.set(r)}function r(n){var t=i.get();Qf.setValue(n,t)}var i=Te({}),u=Te(null),t=w(n.tabs,function(n){return{value:n.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(n.title)},view:function(){return[sS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"]},components:w(n.items,function(n){return Lk(t,n,e)}),formBehaviours:va([sg.config({mode:"acyclic",useTabstopAt:v(DS)}),qd("TabView.form.events",[Fi(r),Ii(o)]),fc.config({channels:K([{key:BF,value:{onReceive:o}},{key:DF,value:{onReceive:r}}])})])}})]}}}),a=bA(t).smartTabHeight;return EF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(n,t,e){var o=Qf.getValue(t);Yt(n,iy,{name:o,oldName:u.get()}),u.set(o)},tabs:t,components:[EF.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[wF.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:va([Ny.config({})])}),EF.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:a.selectFirst,tabSectionBehaviours:va([qd("tabpanel",a.extraEvents),sg.config({mode:"acyclic"}),Ql.config({find:function(n){return yn(EF.getViewItems(n))}}),Qf.config({store:{mode:"manual",getValue:function(n){return n.getSystem().broadcastOn([BF],{}),i.get()},setValue:function(n,t){i.set(t),n.getSystem().broadcastOn([DF],{})}}})])})}(n.body,e)];default:return[function(n,e){var t=pm(sS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"].concat(n.classes)},components:w(n.items,function(n){return Lk(t,n,e)})}}));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[t.asSpec()]}],behaviours:va([sg.config({mode:"acyclic",useTabstopAt:v(DS)}),bS(t),CS(t,{postprocess:function(n){return pA(n).fold(function(n){return H.console.error(n),{}},function(n){return n})}})])}}(n.body,e)]}},initialData:n})])}}function xA(n,e){return[ar(so(),_S),n(Zb,function(n,t){e.onClose(),t.onClose()}),n(ny,function(n,t,e,o){t.onCancel(n),Xt(o,Zb)}),or(ry,function(n,t){return e.onUnblock()}),or(oy,function(n,t){return e.onBlock(t.event())})]}function wA(n,t){function e(n,t){return Hb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+n]},components:w(t,function(n){return n.memento.asSpec()})})}var o=function(n,t){for(var e=[],o=[],r=0,i=n.length;r<i;r++){var u=n[r];(t(u,r)?e:o).push(u)}return{pass:e,fail:o}}(t.map(function(n){return n.footerButtons}).getOr([]),function(n){return"start"===n.align});return[e("start",o.pass),e("end",o.fail)]}function SA(n,o){return{dom:up('<div class="tox-dialog__footer"></div>'),components:[],behaviours:va([ME.config({channel:FF,initialData:n,updateState:function(n,t){var e=w(t.buttons,function(n){var t=pm(function(n,t){return WC(n,n.type,t)}(n,o));return{name:n.name,align:n.align,memento:t}});return on.some({lookupByName:function(n,t){return function(t,n,e){return O(n,function(n){return n.name===e}).bind(function(n){return n.memento.getOpt(t)})}(n,e,t)},footerButtons:e})},renderComponents:wA})])}}function CA(n,t){return rM.parts().footer(SA(n,t))}function kA(t,e){if(t.getRoot().getSystem().isConnected()){var o=Ql.getCurrent(t.getFormWrapper()).getOr(t.getFormWrapper());return sS.getField(o,e).fold(function(){var n=t.getFooter();return ME.getState(n).get().bind(function(n){return n.lookupByName(o,e)})},function(n){return on.some(n)})}return on.none()}function OA(u,o,a){function n(n){var t=u.getRoot();t.getSystem().isConnected()&&n(t)}var c={getData:function(){var n=u.getRoot(),t=n.getSystem().isConnected()?u.getFormWrapper():n,e=Qf.getValue(t),o=L(a,function(n){return n.get()});return P(P({},e),o)},setData:function(i){n(function(n){var t=c.getData(),e=_n(t,i),o=function(n,t){var e=n.getRoot();return ME.getState(e).get().map(function(n){return et(tt("data",n.dataValidator,t))}).getOr(t)}(u,e),r=u.getFormWrapper();Qf.setValue(r,o),Cn(a,function(n,t){Tn(e,t)&&n.set(e[t])})})},disable:function(n){kA(u,n).each(wh.disable)},enable:function(n){kA(u,n).each(wh.enable)},focus:function(n){kA(u,n).each(pg.focus)},block:function(t){if(!cn(t))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){Yt(n,oy,{message:t})})},unblock:function(){n(function(n){Xt(n,ry)})},showTab:function(e){n(function(n){var t=u.getBody();ME.getState(t).get().exists(function(n){return n.isTabPanel()})&&Ql.getCurrent(t).each(function(n){EF.showTab(n,e)})})},redial:function(e){n(function(n){var t=o(e);n.getSystem().broadcastOn([_F],t),n.getSystem().broadcastOn([AF],t.internalDialog),n.getSystem().broadcastOn([MF],t.internalDialog),n.getSystem().broadcastOn([FF],t.internalDialog),c.setData(t.initialData)})},close:function(){n(function(n){Xt(n,Zb)})}};return c}function TA(n){return Ug.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close"),title:n.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(n){Xt(n,ny)}})}function EA(n,t,e){function o(n){return[ki(e.translate(n.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:P({},t.map(function(n){return{id:n}}).getOr({}))},components:o(n),behaviours:va([ME.config({channel:AF,renderComponents:o})])}}function BA(){return{dom:up('<div class="tox-dialog__draghandle"></div>')}}function DA(n,t){return function(n,t){var e=rM.parts().title(EA(n,on.none(),t)),o=rM.parts().draghandle(BA()),r=rM.parts().close(TA(t)),i=[e].concat(n.draggable?[o]:[]).concat([r]);return Hb.sketch({dom:up('<div class="tox-dialog__header"></div>'),components:i})}({title:t.shared.providers.translate(n),draggable:t.dialog.isDraggableModal()},t.shared.providers)}function _A(n,t){return{onClose:function(){return t.closeWindow()},onBlock:function(e){rM.setBusy(n(),function(n,t){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:up('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){rM.setIdle(n())}}}function AA(n,t,e,o){var r;return iu(rM.sketch({lazySink:o.shared.getSink,onEscape:function(n){return Xt(n,ny),on.some(!0)},useTabstopAt:function(n){return!DS(n)},modalBehaviours:va(g([ME.config({channel:_F,updateState:function(n,t){return on.some(t)},initialData:t}),TS({}),pg.config({}),qd("execute-on-form",e.concat([sr(so(),function(n,t){sg.focusIn(n)})])),qd("scroll-lock",[Fi(function(){ni(_i(),"tox-dialog__disable-scroll")}),Ii(function(){ei(_i(),"tox-dialog__disable-scroll")})])],n.extraBehaviours)),eventOrder:(r={},r[ko()]=["execute-on-form"],r[Co()]=["reflecting","receiving"],r[Vo()]=["scroll-lock","reflecting","messages","execute-on-form","alloy.base.behaviour"],r[No()]=["alloy.base.behaviour","execute-on-form","messages","reflecting","scroll-lock"],r),dom:{tag:"div",classes:["tox-dialog"].concat(n.extraClasses),styles:P({position:"relative"},n.extraStyles)},components:g([n.header,n.body],n.footer.toArray()),dragBlockClass:"tox-dialog-wrap",parts:{blocker:{dom:up('<div class="tox-dialog-wrap"></div>'),components:[{dom:{tag:"div",classes:NF?["tox-dialog-wrap__backdrop","tox-dialog-wrap__backdrop--opaque"]:["tox-dialog-wrap__backdrop"]}}]}}}))}function MA(n){return w(n,function(n){return"menu"===n.type?function(n){var t=w(n.items,function(n){var t=Te(!1);return P(P({},n),{storage:t})});return P(P({},n),{items:t})}(n):n})}function FA(n){return k(n,function(n,t){return"menu"!==t.type?n:k(t.items,function(n,t){return n[t.name]=t.storage,n},n)},{})}function IA(n,t,e){var o=DA(n.internalDialog.title,e),r=function(n,t){var e=yA(n,on.none(),t,!1);return rM.parts().body(e)}({body:n.internalDialog.body},e),i=MA(n.internalDialog.buttons),u=FA(i),a=CA({buttons:i},e),c=VF(function(){return d},_A(function(){return l},t)),s="normal"!==n.internalDialog.size?"large"===n.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],f={header:o,body:r,footer:on.some(a),extraClasses:s,extraBehaviours:[],extraStyles:{}},l=AA(f,n,c,e),d=OA({getRoot:function(){return l},getBody:function(){return rM.getBody(l)},getFooter:function(){return rM.getFooter(l)},getFormWrapper:function(){var n=rM.getBody(l);return Ql.getCurrent(n).getOr(n)}},t.redial,u);return{dialog:l,instanceApi:d}}function RA(n){return sn(n)&&-1!==PF.indexOf(n.mceAction)}function VA(e,n,o,t){var r,i=DA(e.title,t),u=function(n){var t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[AS({dom:{tag:"iframe",attributes:{src:n.url}},behaviours:va([Ny.config({}),pg.config({})])})]}],behaviours:va([sg.config({mode:"acyclic",useTabstopAt:v(DS)})])};return rM.parts().body(t)}(e),a=e.buttons.bind(function(n){return 0===n.length?on.none():on.some(CA({buttons:n},t))}),c=RF(function(){return h},_A(function(){return p},n)),s=P(P({},e.height.fold(function(){return{}},function(n){return{height:n+"px","max-height":n+"px"}})),e.width.fold(function(){return{}},function(n){return{width:n+"px","max-width":n+"px"}})),f=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],l=new HF(e.url,{base_uri:new HF(H.window.location.href)}),d=l.protocol+"://"+l.host+(l.port?":"+l.port:""),m=Te(on.none()),g=[qd("messages",[Fi(function(){var n=ab(Be.fromDom(H.window),"message",function(n){if(l.isSameOrigin(new HF(n.raw().origin))){var t=n.raw().data;RA(t)?function(n,t,e){switch(e.mceAction){case"insertContent":n.insertContent(e.content);break;case"setContent":n.setContent(e.content);break;case"execCommand":var o=!!ln(e.ui)&&e.ui;n.execCommand(e.cmd,o,e.value);break;case"close":t.close();break;case"block":t.block(e.message);break;case"unblock":t.unblock()}}(o,h,t):function(n){return!RA(n)&&sn(n)&&Tn(n,"mceAction")}(t)&&e.onMessage(h,t)}});m.set(on.some(n))}),Ii(function(){m.get().each(function(n){return n.unbind()})})]),fc.config({channels:(r={},r[IF]={onReceive:function(n,t){Cu(n.element(),"iframe").each(function(n){n.dom().contentWindow.postMessage(t,d)})}},r)})],p=AA({header:i,body:u,footer:a,extraClasses:f,extraBehaviours:g,extraStyles:s},e,c,t),h=function(t){function n(n){t.getSystem().isConnected()&&n(t)}return{block:function(t){if(!cn(t))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){Yt(n,oy,{message:t})})},unblock:function(){n(function(n){Xt(n,ry)})},close:function(){n(function(n){Xt(n,Zb)})},sendMessage:function(t){n(function(n){n.getSystem().broadcastOn([IF],t)})}}}(p);return{dialog:p,instanceApi:h}}function NA(n,t,e,o){var r,i,u=Vr("dialog-label"),a=Vr("dialog-content"),c=pm(function(n,t,e){return Hb.sketch({dom:up('<div class="tox-dialog__header"></div>'),components:[EA(n,on.some(t),e),BA(),TA(e)],containerBehaviours:va([rA.config({mode:"mouse",blockerClass:"blocker",getTarget:function(n){return ku(n,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])})}({title:n.internalDialog.title,draggable:!0},u,e.shared.providers)),s=pm(function(n,t,e,o){return yA(n,on.some(t),e,o)}({body:n.internalDialog.body},a,e,o)),f=MA(n.internalDialog.buttons),l=FA(f),d=pm(function(n,t){return SA(n,t)}({buttons:f},e)),m=VF(function(){return p},{onBlock:function(){},onUnblock:function(){},onClose:function(){return t.closeWindow()}}),g=iu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:(r={role:"dialog"},r["aria-labelledby"]=u,r["aria-describedby"]=""+a,r)},eventOrder:(i={},i[Co()]=[ME.name(),fc.name()],i[ko()]=["execute-on-form"],i[Vo()]=["reflecting","execute-on-form"],i),behaviours:va([sg.config({mode:"cyclic",onEscape:function(n){return Xt(n,Zb),on.some(!0)},useTabstopAt:function(n){return!DS(n)&&("button"!==qo(n)||"disabled"!==_r(n,"disabled"))}}),ME.config({channel:_F,updateState:function(n,t){return on.some(t)},initialData:n}),pg.config({}),qd("execute-on-form",m.concat([sr(so(),function(n,t){sg.focusIn(n)})])),TS({})]),components:[c.asSpec(),s.asSpec(),d.asSpec()]}),p=OA({getRoot:function(){return g},getFooter:function(){return d.get(g)},getBody:function(){return s.get(g)},getFormWrapper:function(){var n=s.get(g);return Ql.getCurrent(n).getOr(n)}},t.redial,l);return{dialog:g,instanceApi:p}}function HA(n,t){return rM.parts().close(Ug.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:n,buttonBehaviours:va([Ny.config({})])}))}function PA(){return rM.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function zA(n,t){return rM.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:up("<p>"+t.translate(n)+"</p>")}]}]})}function LA(n){return rM.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:n})}function jA(n,t){return[Hb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:n}),Hb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})]}function UA(e){return rM.sketch({lazySink:e.lazySink,onEscape:function(){return e.onCancel(),on.some(!0)},dom:{tag:"div",classes:["tox-dialog"].concat(e.extraClasses)},components:[Dn(e.headerOverride.getOr(jF),{components:[e.partSpecs.title,e.partSpecs.close]}),e.partSpecs.body,e.partSpecs.footer],parts:{blocker:{dom:up('<div class="tox-dialog-wrap"></div>'),components:[{dom:{tag:"div",classes:zF?["tox-dialog-wrap__backdrop","tox-dialog-wrap__backdrop--opaque"]:["tox-dialog-wrap__backdrop"]}}]}},modalBehaviours:va([qd("basic-dialog-events",[or(ny,function(n,t){e.onCancel()}),or(ey,function(n,t){e.onSubmit()})])])})}var WA,GA,XA,YA,qA,KA=function(i,r){r.delimiter||(r.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:va([sg.config({mode:"flow",selector:"div[role=button]"}),Ny.config({}),lg.config({}),qd("elementPathEvents",[Fi(function(e,n){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return sg.focusIn(e)}),i.on("NodeChange",function(n){var t=function(n){for(var t=[],e=n.length;0<e--;){var o=n[e];if(1===o.nodeType&&!aA(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||t.push({name:r.name,element:o}),r.isPropagationStopped())break}}return t}(n.parents);0<t.length&&lg.set(e,function(n){var t=w(n||[],function(t,n){return Ug.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":n,"tab-index":-1,"aria-level":n+1},innerHtml:t.name},action:function(n){i.focus(),i.selection.select(t.element),i.nodeChanged()}})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+r.delimiter+" "}};return k(t.slice(1),function(n,t){var e=n;return e.push(o),e.push(t),e},[t[0]])}(t))})})])]),components:[]}},JA=Ht(),$A=function(c,n){function e(r){var i=xu(r);return s.getOpt(n).fold(function(){return rA.snap({sensor:LD(i.x()-20,i.y()-20),range:Fu(i.width(),i.height()),output:LD(on.some(i.x()),on.some(i.y())),extra:{td:r}})},function(n){var t=i.x()-20,e=i.y()-20,o=n.element().dom().getBoundingClientRect();return rA.snap({sensor:LD(t,e),range:Fu(40,40),output:LD(on.some(i.x()-o.width/2),on.some(i.y()-o.height/2)),extra:{td:r}})})}function o(r){var i=xu(r);return f.getOpt(n).fold(function(){return rA.snap({sensor:LD(i.x()-20,i.y()-20),range:Fu(i.width(),i.height()),output:LD(on.some(i.right()),on.some(i.bottom())),extra:{td:r}})},function(n){var t=i.right()-20,e=i.bottom()-20,o=n.element().dom().getBoundingClientRect();return rA.snap({sensor:LD(t,e),range:Fu(40,40),output:LD(on.some(i.right()-o.width/2),on.some(i.bottom()-o.height/2)),extra:{td:r}})})}function r(n,t,e,o){var r=t.dom().getBoundingClientRect();gi(n.element(),"display");var i=pr(Be.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&si(n.element(),"display","none")}var i=Te([]),u=Te([]),t={getSnapPoints:function(){return w(i.get(),function(n){return e(n)})},leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(n,t){g.set(t.td),c.fire("tableselectorchange",{start:g.get(),finish:p.get()})},mustSnap:!0},a={getSnapPoints:function(){return w(u.get(),function(n){return o(n)})},leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(n,t){p.set(t.td),c.fire("tableselectorchange",{start:g.get(),finish:p.get()})},mustSnap:!0},s=pm(Ug.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:va([rA.config(JA.deviceType.isTouch()?{mode:"touch",snaps:t}:{mode:"mouse",blockerClass:"blocker",snaps:t}),bw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"]}})),f=pm(Ug.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:va([rA.config(JA.deviceType.isTouch()?{mode:"touch",snaps:a}:{mode:"mouse",blockerClass:"blocker",snaps:a}),bw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"]}})),l=iu(s.asSpec()),d=iu(f.asSpec()),m=Te(!1),g=Te(null),p=Te(null),h=function(n){var t=e(n);rA.snapTo(l,t);r(l,n,function(n){return n.top<0},function(n,t){return n.top>t})},v=function(n){var t=o(n);rA.snapTo(d,t);r(d,n,function(n){return n.bottom<0},function(n,t){return n.bottom>t})};JA.deviceType.isTouch()&&(c.on("tableselectionchange",function(t){m.get()||(ps(n,l),ps(n,d),m.set(!0)),g.set(t.start),p.set(t.finish),t.otherCells.each(function(n){i.set(n.upOrLeftCells),u.set(n.downOrRightCells),h(t.start),v(t.finish)})}),c.on("resize ScrollContent",function(){!function(){var n=g.get();h(n)}(),function(){var n=p.get();v(n)}()}),c.on("tableselectionclear",function(){m.get()&&(vs(l),vs(d),m.set(!1))}))},QA=function(e){var o=Te(on.none()),r=Te(!1),i=qg(function(n){e.fire("longpress",P(P({},n),{type:"longpress"})),r.set(!0)},400);e.on("touchstart",function(e){sA(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:nn(e.target)};i.throttle(e),r.set(!1),o.set(on.some(t))})},!0),e.on("touchmove",function(n){i.cancel(),sA(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||(o.set(on.none()),r.set(!1),e.fire("longpresscancel"))})})},!0),e.on("touchend touchcancel",function(t){i.cancel(),"touchend"===t.type&&r.get()&&o.get().filter(function(n){return n.target().isEqualNode(t.target)}).map(function(){t.preventDefault()})},!0)},ZA=function(l){function d(){return o.bind(y_.getHeader)}function m(){return an.value(v)}function n(){return o.bind(function(n){return y_.getMoreButton(n)}).getOrDie("Could not find more button element")}function g(){return o.bind(function(n){return y_.getThrobber(n)}).getOrDie("Could not find throbber element")}function t(n){return Lb(n)}var e=l.inline,p=e?B_:E_,h=Gb(l)?QD:e_,o=on.none(),r=Ht(),i=r.browser.isIE()?["tox-platform-ie"]:[],u=r.deviceType.isTouch()?["tox-platform-touch"]:[],a=eh.isRtl()?{attributes:{dir:"rtl"}}:{},v=iu({dom:P({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(i).concat(u)},a),behaviours:va([Df.config({useFixed:function(){return h.isDocked(d)}})])}),c=pm({dom:{tag:"div",classes:["tox-anchorbar"]}}),b=mO(v,l,function(){return o.bind(function(n){return c.getOpt(n)}).getOrDie("Could not find a anchor bar element")},n),s=y_.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:b,onEscape:function(){l.focus()}}),f=y_.parts().toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:m,backstage:b,onEscape:function(){l.focus()},split:t(l),lazyToolbar:function(){return o.bind(function(n){return y_.getToolbar(n)}).getOrDie("Could not find more toolbar element")},lazyMoreButton:n,lazyHeader:function(){return d().getOrDie("Could not find header element")}}),y=y_.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},onEscape:function(){}}),x=y_.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),w=y_.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),S=y_.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:b}),C=l.getParam("statusbar",!0,"boolean")&&!e?on.some(cA(l,b.shared.providers)):on.none(),k={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[x,w]},O=zb(l),T=Ib(l),E=Fb(l),B=t(l)!==Vb["default"],D=y_.parts().header({dom:{tag:"div",classes:["tox-editor-header"]},components:z([E?[s]:[],O?(B&&H.console.warn("Toolbar drawer cannot be applied when multiple toolbars are active"),[y]):T?[f]:[],Ub(l)?[]:[c.asSpec()]]),sticky:Gb(l),editor:l,getSink:m}),_=z([[D],e?[]:[k]]),A=z([[{dom:{tag:"div",classes:["tox-editor-container"]},components:_}],e?[]:C.toArray(),[S]]),M=Wb(l),F=P(P({role:"application"},eh.isRtl()?{dir:"rtl"}:{}),M?{"aria-hidden":"true"}:{}),I=iu(y_.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(e?["tox-tinymce-inline"]:[]).concat(u).concat(i),styles:P({visibility:"hidden"},M?{opacity:"0",border:"0"}:{}),attributes:F},components:A,behaviours:va([sg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));o=on.some(I),l.shortcuts.add("alt+F9","focus menubar",function(){y_.focusMenubar(I)}),l.shortcuts.add("alt+F10","focus toolbar",function(){y_.focusToolbar(I)});var R=_b(I),V=_b(v);MD(l,R,V),QA(l);function N(n){var t=Fh.DOM,e=l.getParam("width",t.getStyle(n,"width")),o=function(n){return n.getParam("height",Math.max(n.getElement().offsetHeight,200))}(l),r=Ab(l),i=Mb(l),u=W_(e).bind(function(t){return G_(r.map(function(n){return Math.max(t,n)}))}).getOr(G_(e)),a=W_(o).bind(function(t){return i.map(function(n){return Math.max(t,n)})}).getOr(o),c=G_(u),s=l.inline?"max-width":"width";if(mi("div",s,c)&&si(I.element(),s,c),!l.inline){var f=G_(a);mi("div","height",f)?si(I.element(),"height",f):si(I.element(),"height","200px")}return a}return{mothership:R,uiMothership:V,backstage:b,renderUI:function(){h.setup(l,d),R_(l,b),uD(l,m,b),function(o){var r=o.ui.registry.getAll().sidebars;bn(wn(r),function(t){function e(){return on.from(o.queryCommandValue("ToggleSidebar")).is(t)}var n=r[t];o.ui.registry.addToggleButton(t,{icon:n.icon,tooltip:n.tooltip,onAction:function(n){o.execCommand("ToggleSidebar",!1,t),n.setActive(e())},onSetup:function(n){function t(){return n.setActive(e())}return o.on("ToggleSidebar",t),function(){o.off("ToggleSidebar",t)}}})})}(l),function(e,t,o){function r(n){n!==i.get()&&(LB(t(),n,o.providers),i.set(n))}var i=Te(!1),u=Te(on.none());e.on("ProgressState",function(n){if(u.get().each(Xg.clearTimeout),mn(n.time)){var t=Xg.setEditorTimeout(e,function(){return r(n.state)},n.time);u.set(on.some(t))}else r(n.state),u.set(on.none())})}(l,g,b.shared);var n=l.ui.registry.getAll(),t=n.buttons,e=n.menuItems,o=n.contextToolbars,r=n.sidebars,i=Rb(l),u={menuItems:e,menus:l.settings.menu?L(l.settings.menu,function(n){return _n(n,{items:n.items})}):{},menubar:l.settings.menubar,toolbar:i.getOrThunk(function(){return l.getParam("toolbar",!0)}),buttons:t,sidebar:r};AD(l,o,v,{backstage:b}),$A(l,v);var a=l.getElement(),c=N(a),s={mothership:R,uiMothership:V,outerContainer:I},f={targetNode:a,height:c};return p.render(l,s,u,b,f)},getUi:function(){return{channels:{broadcastAll:V.broadcast,broadcastOn:V.broadcastOn,register:function(){}}}}}},nM=function(n,t){var e=on.from(_r(n,"id")).fold(function(){var n=Vr("dialog-label");return Dr(t,"id",n),n},l);Dr(n,"aria-labelledby",e)},tM=nn([ct("lazySink"),ht("dragBlockClass"),Bt("getBounds",wu),St("useTabstopAt",nn(!0)),St("eventOrder",{}),Ms("modalBehaviours",[sg]),qu("onExecute"),Ju("onEscape")]),eM={sketch:l},oM=nn([wl({name:"draghandle",overrides:function(n,t){return{behaviours:va([rA.config({mode:"mouse",getTarget:function(n){return Su(n,'[role="dialog"]').getOr(n)},blockerClass:n.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:n.getDragBounds})])}}}),yl({schema:[ct("dom")],name:"title"}),yl({factory:eM,schema:[ct("dom")],name:"close"}),yl({factory:eM,schema:[ct("dom")],name:"body"}),wl({factory:eM,schema:[ct("dom")],name:"footer"}),xl({factory:{sketch:function(n,t){return P(P({},n),{dom:t.dom,components:t.components})}},schema:[St("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),St("components",[])],name:"blocker"})]),rM=Dl({name:"ModalDialog",configFields:tM(),partFields:oM(),factory:function(o,n,t,r){var a=Vr("alloy.dialog.busy"),c=Vr("alloy.dialog.idle"),s=va([sg.config({mode:"special",onTab:function(){return on.some(!0)},onShiftTab:function(){return on.some(!0)}}),pg.config({})]),e=Vr("modal-events"),i=P(P({},o.eventOrder),{"alloy.system.attached":[e].concat(o.eventOrder["alloy.system.attached"]||[])});return{uid:o.uid,dom:o.dom,components:n,apis:{show:function(i){var n=o.lazySink(i).getOrDie(),u=Te(on.none()),t=r.blocker(),e=n.getSystem().build(P(P({},t),{components:t.components.concat([uu(i)]),behaviours:va([qd("dialog-blocker-events",[or(c,function(n,t){Ar(i.element(),"aria-busy")&&(Mr(i.element(),"aria-busy"),u.get().each(function(n){return lg.remove(i,n)}))}),or(a,function(n,t){Dr(i.element(),"aria-busy","true");var e=t.event().getBusySpec();u.get().each(function(n){lg.remove(i,n)});var o=e(i,s),r=n.getSystem().build(o);u.set(on.some(r)),lg.append(i,uu(r)),r.hasConfigured(sg)&&sg.focusIn(r)})])])}));ps(n,e),sg.focusIn(i)},hide:function(t){hr(t.element()).each(function(n){t.getSystem().getByDom(n).each(function(n){vs(n)})})},getBody:function(n){return qs(n,o,"body")},getFooter:function(n){return qs(n,o,"footer")},setIdle:function(n){Xt(n,c)},setBusy:function(n,t){Yt(n,a,{getBusySpec:t})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Is(o.modalBehaviours,[lg.config({}),sg.config({mode:"cyclic",onEnter:o.onExecute,onEscape:o.onEscape,useTabstopAt:o.useTabstopAt}),qd(e,[Fi(function(n){nM(n.element(),qs(n,o,"title").element()),function(n,t){var e=on.from(_r(n,"id")).fold(function(){var n=Vr("dialog-describe");return Dr(t,"id",n),n},l);Dr(n,"aria-describedby",e)}(n.element(),qs(n,o,"body").element())})])])}},apis:{show:function(n,t){n.show(t)},hide:function(n,t){n.hide(t)},getBody:function(n,t){return n.getBody(t)},getFooter:function(n,t){return n.getFooter(t)},setBusy:function(n,t,e){n.setBusy(t,e)},setIdle:function(n,t){n.setIdle(t)}}}),iM=[ft("type"),ft("text"),lt("level",["info","warn","error","success"]),ft("icon"),St("url","")],uM=le(iM),aM=[ft("type"),ft("text"),Et("disabled",!1),Et("primary",!1),pe("name","name",In(function(){return Vr("button-name")}),we),yt("icon"),Et("borderless",!1)],cM=le(aM),sM=[ft("type"),ft("name"),ft("label"),Et("disabled",!1)],fM=le(sM),lM=Se,dM=[ft("type"),ft("name")],mM=dM.concat([yt("label")]),gM=le(mM),pM=we,hM=le(mM),vM=we,bM=le(mM),yM=Kn(he),xM=mM.concat([Et("sandboxed",!0)]),wM=le(xM),SM=we,CM=mM.concat([yt("inputMode"),yt("placeholder"),Et("maximized",!1),Et("disabled",!1)]),kM=le(CM),OM=we,TM=mM.concat([gt("items",[ft("text"),ft("value")]),kt("size",1),Et("disabled",!1)]),EM=le(TM),BM=we,DM=mM.concat([Et("constrain",!0),Et("disabled",!1)]),_M=le(DM),AM=le([ft("width"),ft("height")]),MM=mM.concat([yt("placeholder"),Et("maximized",!1),Et("disabled",!1)]),FM=le(MM),IM=we,RM=mM.concat([Tt("filetype","file",["image","media","file"]),St("disabled",!1)]),VM=le(RM),NM=le([ft("value"),St("meta",{})]),HM=dM.concat([Ot("tag","textarea"),ft("scriptId"),ft("scriptUrl"),(WA="settings",GA=undefined,Ct(WA,GA,Oe))]),PM=dM.concat([Ot("tag","textarea"),dt("init")]),zM=Zn(function(n){return tt("customeditor.old",qn(PM),n).orThunk(function(){return tt("customeditor.new",qn(HM),n)})}),LM=we,jM=[ft("type"),ft("html"),Tt("presets","presentation",["presentation","document"])],UM=le(jM),WM=mM.concat([st("currentState",le([ct("blob"),ft("url")]))]),GM=le(WM),XM=mM.concat([St("columns","auto")]),YM=le(XM),qM=(XA=[ft("value"),ft("text"),ft("icon")],me(XA)),KM=[ft("type"),pt("header",we),pt("cells",Kn(we))],JM=le(KM),$M=ve(function(){return rt("type",{alertbanner:uM,bar:le(function(n){return[ft("type"),n]}(lA("bar"))),button:cM,checkbox:fM,colorinput:gM,colorpicker:hM,dropzone:bM,grid:le(fA(lA("grid"))),iframe:wM,input:kM,selectbox:EM,sizeinput:_M,textarea:FM,urlinput:VM,customeditor:zM,htmlpanel:UM,imagetools:GM,collection:YM,label:le(function(n){return[ft("type"),ft("label"),n]}(lA("label"))),table:JM,panel:ZM})}),QM=[ft("type"),St("classes",[]),pt("items",$M)],ZM=le(QM),nF=[pe("name","name",In(function(){return Vr("tab-name")}),we),ft("title"),pt("items",$M)],tF=[ft("type"),gt("tabs",nF)],eF=le(tF),oF=le([ft("type"),ft("name"),Et("active",!1)].concat(jp)),rF=Se,iF=[pe("name","name",In(function(){return Vr("button-name")}),we),yt("icon"),Tt("align","end",["start","end"]),Et("primary",!1),Et("disabled",!1)],uF=g(iF,[ft("text")]),aF=g([lt("type",["submit","cancel","custom"])],uF),cF=g([lt("type",["menu"]),yt("text"),yt("tooltip"),yt("icon"),pt("items",oF),Bt("onSetup",function(){return Z})],iF),sF=uF,fF=it("type",{submit:aF,cancel:aF,custom:aF,menu:cF}),lF=le([ft("title"),st("body",rt("type",{panel:ZM,tabpanel:eF})),Ot("size","normal"),pt("buttons",fF),St("initialData",{}),Bt("onAction",Z),Bt("onChange",Z),Bt("onSubmit",Z),Bt("onClose",Z),Bt("onCancel",Z),St("onTabChange",Z)]),dF=function(n){return sn(n)?[n].concat(E(I(n),dF)):fn(n)?E(n,dF):[]},mF={checkbox:lM,colorinput:pM,colorpicker:vM,dropzone:yM,input:OM,iframe:SM,sizeinput:AM,selectbox:BM,size:AM,textarea:IM,urlinput:NM,customeditor:LM,collection:qM,togglemenuitem:rF},gF=le(g([lt("type",["cancel","custom"])],sF)),pF=le([ft("title"),ft("url"),bt("height"),bt("width"),(YA="buttons",qA=gF,vt(YA,Kn(qA))),Bt("onAction",Z),Bt("onCancel",Z),Bt("onClose",Z),Bt("onMessage",Z)]),hF={open:function(n,t){var e=gA(t);return n(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(n,t){return n(et(function(n){return tt("dialog",pF,n)}(t)))},redial:function(n){return gA(n)}},vF=Bl({name:"TabButton",configFields:[St("uid",undefined),ct("value"),pe("dom","dom",Rn(function(n){return{attributes:{role:"tab",id:Vr("aria"),"aria-selected":"false"}}}),ye()),ht("action"),St("domModification",{}),Ms("tabButtonBehaviours",[pg,sg,Qf]),ct("view")],factory:function(n,t){return{uid:n.uid,dom:n.dom,components:n.components,events:gm(n.action),behaviours:Is(n.tabButtonBehaviours,[pg.config({}),sg.config({mode:"execution",useSpace:!0,useEnter:!0}),Qf.config({store:{mode:"memory",initialValue:n.value}})]),domModification:n.domModification}}}),bF=nn([ct("tabs"),ct("dom"),St("clickToDismiss",!1),Ms("tabbarBehaviours",[ud,sg]),Gu(["tabClass","selectedClass"])]),yF=Sl({factory:vF,name:"tabs",unit:"tab",overrides:function(o,n){function r(n,t){ud.dehighlight(n,t),Yt(n,Uo(),{tabbar:n,button:t})}function i(n,t){ud.highlight(n,t),Yt(n,jo(),{tabbar:n,button:t})}return{action:function(n){var t=n.getSystem().getByUid(o.uid).getOrDie(),e=ud.isHighlighted(t,n);(e&&o.clickToDismiss?r:e?Z:i)(t,n)},domModification:{classes:[o.markers.tabClass]}}}}),xF=nn([yF]),wF=Dl({name:"Tabbar",configFields:bF(),partFields:xF(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Is(n.tabbarBehaviours,[ud.config({highlightClass:n.markers.selectedClass,itemClass:n.markers.tabClass,onHighlight:function(n,t){Dr(t.element(),"aria-selected","true")},onDehighlight:function(n,t){Dr(t.element(),"aria-selected","false")}}),sg.config({mode:"flow",getInitial:function(n){return ud.getHighlighted(n).map(function(n){return n.element()})},selector:"."+n.markers.tabClass,executeOnMove:!0})])}}}),SF=Bl({name:"Tabview",configFields:[Ms("tabviewBehaviours",[lg])],factory:function(n,t){return{uid:n.uid,dom:n.dom,behaviours:Is(n.tabviewBehaviours,[lg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),CF=nn([St("selectFirst",!0),Yu("onChangeTab"),Yu("onDismissTab"),St("tabs",[]),Ms("tabSectionBehaviours",[])]),kF=yl({factory:wF,schema:[ct("dom"),mt("markers",[ct("tabClass"),ct("selectedClass")])],name:"tabbar",defaults:function(n){return{tabs:n.tabs}}}),OF=yl({factory:SF,name:"tabview"}),TF=nn([kF,OF]),EF=Dl({name:"TabSection",configFields:CF(),partFields:TF(),factory:function(r,n,t,e){function o(n,t){Ys(n,r,"tabbar").each(function(n){t(n).each(qt)})}return{uid:r.uid,dom:r.dom,components:n,behaviours:Fs(r.tabSectionBehaviours),events:nr(z([r.selectFirst?[Fi(function(n,t){o(n,ud.getFirst)})]:[],[or(jo(),function(n,t){!function(o){var t=Qf.getValue(o);Ys(o,r,"tabview").each(function(e){O(r.tabs,function(n){return n.value===t}).each(function(n){var t=n.view();Dr(e.element(),"aria-labelledby",_r(o.element(),"id")),lg.set(e,t),r.onChangeTab(e,o,t)})})}(t.event().button())}),or(Uo(),function(n,t){var e=t.event().button();r.onDismissTab(n,e)})]])),apis:{getViewItems:function(n){return Ys(n,r,"tabview").map(function(n){return lg.contents(n)}).getOr([])},showTab:function(n,e){o(n,function(t){var n=ud.getCandidates(t);return O(n,function(n){return Qf.getValue(n)===e}).filter(function(n){return!ud.isHighlighted(t,n)})})}}}},apis:{getViewItems:function(n,t){return n.getViewItems(t)},showTab:function(n,t,e){n.showTab(t,e)}}}),BF="send-data-to-section",DF="send-data-to-view",_F=Vr("update-dialog"),AF=Vr("update-title"),MF=Vr("update-body"),FF=Vr("update-footer"),IF=Vr("body-send-message"),RF=function(i,n){function t(n,r){return or(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){ME.getState(t).get().each(function(n){e(n,t)})};return g(xA(t,n),[t(ty,function(n,t,e){t.onAction(n,{name:e.name()})})])},VF=function(i,n){function t(n,r){return or(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){ME.getState(t).get().each(function(n){e(n.internalDialog,t)})};return g(xA(t,n),[t(ey,function(n,t){return t.onSubmit(n)}),t(Qb,function(n,t,e){t.onChange(n,{name:e.name()})}),t(ty,function(n,t,e,o){function r(){return sg.focusIn(o)}var i=wa();t.onAction(n,{name:e.name(),value:e.value()}),wa().fold(function(){r()},function(n){!no(o.element(),n)||Ar(n,"disabled")?r():no(n,i.getOrNull())&&Ar(i.getOrDie(),"disabled")&&r()})}),t(iy,function(n,t,e){t.onTabChange(n,{newTabName:e.name(),oldTabName:e.oldName()})}),Ii(function(n){var t=i();Qf.setValue(n,t.getData())})])},NF=dh.deviceType.isTouch(),HF=tinymce.util.Tools.resolve("tinymce.util.URI"),PF=["insertContent","setContent","execCommand","close","block","unblock"],zF=dh.deviceType.isTouch(),LF={dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]}},jF={dom:{tag:"div",classes:["tox-dialog__header"]}},UF=function(n){var l=n.backstage,d=n.editor,m=Gb(d),e=function(u){var a=u.backstage.shared;return{open:function(n,t){function e(){rM.hide(r),t()}var o=pm(WC({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:on.none()},"cancel",u.backstage)),r=iu(UA({lazySink:function(){return a.getSink()},headerOverride:on.some(LF),partSpecs:{title:PA(),close:HA(function(){e()},a.providers),body:zA(n,a.providers),footer:LA(jA([],[o.asSpec()]))},onCancel:function(){return e()},onSubmit:Z,extraClasses:["tox-alert-dialog"]}));rM.show(r);var i=o.get(r);pg.focus(i)}}}(n),o=function(a){var c=a.backstage.shared;return{open:function(n,t){function e(n){rM.hide(i),t(n)}var o=pm(WC({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:on.none()},"submit",a.backstage)),r=WC({name:"no",text:"No",primary:!0,align:"end",disabled:!1,icon:on.none()},"cancel",a.backstage),i=iu(UA({lazySink:function(){return c.getSink()},headerOverride:on.some(LF),partSpecs:{title:PA(),close:HA(function(){e(!1)},c.providers),body:zA(n,c.providers),footer:LA(jA([],[r,o.asSpec()]))},onCancel:function(){return e(!1)},onSubmit:function(){return e(!0)},extraClasses:["tox-confirm-dialog"]}));rM.show(i);var u=o.get(i);pg.focus(u)}}}(n),r=function(n,e){return hF.openUrl(function(n){var t=VA(n,{closeWindow:function(){rM.hide(t.dialog),e(t.instanceApi)}},d,l);return rM.show(t.dialog),t.instanceApi},n)},i=function(n,i){return hF.open(function(n,t,e){var o=t,r=IA({dataValidator:e,initialData:o,internalDialog:n},{redial:hF.redial,closeWindow:function(){rM.hide(r.dialog),i(r.instanceApi)}},l);return rM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},n)},u=function(n,c,s,f){return hF.open(function(n,t,e){function o(){return i.on(function(n){qD.refresh(n)})}var r=function(n,t){return et(tt("data",t,n))}(t,e),i=function(){var t=Te(on.none());return{clear:function(){t.set(on.none())},set:function(n){t.set(on.some(n))},isSet:function(){return t.get().isSome()},on:function(n){t.get().each(n)}}}(),u=NA({dataValidator:e,initialData:r,internalDialog:n},{redial:hF.redial,closeWindow:function(){i.on(zg.hide),d.off("ResizeEditor",o),i.clear(),s(u.instanceApi)}},l,f),a=iu(zg.sketch({lazySink:l.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},inlineBehaviours:va(g([qd("window-manager-inline-events",[or(Ho(),function(n,t){Xt(u.dialog,ny)})])],function(r,n){return n?[]:[qD.config({contextual:{lazyContext:function(){return on.some(yu(Be.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},leftAttr:"data-dock-left",topAttr:"data-dock-top",positionAttr:"data-dock-pos",modes:["top"],lazyViewport:function(){var n=wu(),t=Cu(Be.fromDom(r.getContainer()),".tox-editor-header").getOrDie(),e=xu(t),o=Math.max(n.y(),e.bottom());return bu(n.x(),o,n.width(),n.bottom()-o)}})]}(d,m)))}));return i.set(a),zg.showWithin(a,c,uu(u.dialog),on.some(_i())),m||(qD.refresh(a),d.on("ResizeEditor",o)),u.instanceApi.setData(r),sg.focusIn(u.dialog),u.instanceApi},n)};return{open:function(n,t,e){return t!==undefined&&"toolbar"===t.inline?u(n,l.shared.anchors.toolbar(),e,t.ariaAttrs):t!==undefined&&"cursor"===t.inline?u(n,l.shared.anchors.cursor(),e,t.ariaAttrs):i(n,e)},openUrl:function(n,t){return r(n,t)},alert:function(n,t){e.open(n,function(){t()})},close:function(n){n.close()},confirm:function(n,t){o.open(n,function(n){t(n)})}}};!function ZF(){n.add("silver",function(n){var t=ZA(n),e=t.uiMothership,o=t.backstage,r=t.renderUI,i=t.getUi;fb(n,o.shared);var u=UF({editor:n,backstage:o});return{renderUI:r,getWindowManagerImpl:nn(u),getNotificationManagerImpl:function(){return Yg(0,{backstage:o},e)},ui:i()}})}()}(window);
<template>
  <div>
    <!-- <el-select v-model="listQuery.CourseCategoryId" size="small" placeholder="选择课程分类" @change="handleRefreshList">
      <el-option label="全部" :value="''" />
      <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id" />
    </el-select> -->
    <el-cascader v-model="listQuery.CourseCategoryId" filterable clearable :options="categoryList" :props="{'label': 'name','value': 'id','children': 'children','checkStrictly': true,'emitPath': false}" style="margin: 0 10px" size="small" placeholder="请选择课程分类..." />
    <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <el-table
      v-loading="listLoading"
      :data="list"
      @sort-change="sortChange"
      @selection-change="handleChooseCourseCenterChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课程封面" prop="courseCoverUrl" sortable="courseCoverUrl" width="160">
        <template slot-scope="{ row }">
          <el-image :src="row.courseCoverUrl" class="course-cover" fit="cover">
            <div slot="error">
              <div class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="课程名称" prop="courseName" sortable="courseName" min-width="200" />
      <el-table-column label="学时" prop="classHour" sortable="classHour" width="100" />
      <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
        <template slot-scope="{row}">
          {{ row.creationTime | formatDateTime }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="有效期" prop="endTime" sortable="dueTime" width="200">
        <template slot-scope="{row}">
          {{ row.endTime | formatDateTime }}
        </template>
      </el-table-column> -->
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getList"
    />

  </div>
</template>
<script>
import { courseCenterList, courseCategoryList, courseList } from '@/api/course'
import Pagination from '@/components/Pagination'
export default {
  name: 'ChooseCourseCenter',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],

      listLoading: false,
      listQuery: {
        Filter: '',
        CourseCategoryId: '',
        FreeModel: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      categoryList: []
    }
  },
  mounted() {
    this.getList()
    this.getCategoryList()
  },
  methods: {
    handleChooseCourseCenterChange(val) {
      this.$emit('response', val)
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseCenterList(this.listQuery).then(res => {
        this.list = res.items
        this.listLoading = false
        this.listQuery.totalCount = res.totalCount
      }).catch(() => {
        this.listLoading = false
      })
    },
    async getCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.categoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
      console.log(this.courseCategoryList)
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    }
  }
}
</script>

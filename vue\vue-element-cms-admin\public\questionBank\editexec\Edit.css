﻿
.shape {
    border-style: solid;
    border-width: 0 40px 40px 0;
    float: right;
    height: 0px;
    width: 0px;
    -ms-transform: rotate(360deg); /* IE 9 */
    -o-transform: rotate(360deg); /* Opera 10.5 */
    -webkit-transform: rotate(360deg); /* <PERSON>fari and Chrome */
    transform: rotate(360deg);
}

.speical {
    background: #fff;
    /*border: 1px solid #e2e2e2;*/
    /*box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);*/
    margin: 10px 0;
    /*overflow: hidden;*/
    margin-left: 20px;
}

    /*.speical:hover {
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
        -o-transform: scale(1.1);
        transform: rotate scale(1.1);
        -webkit-transition: all 0.4s ease-in-out;
        -moz-transition: all 0.4s ease-in-out;
        -o-transition: all 0.4s ease-in-out;
        transition: all 0.4s ease-in-out;
    }*/


.speicalStyle {
    background: #fff;
    border: 1px solid #ddd;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
    margin: 10px 0;
    overflow: hidden;
}

.speicalStyle:hover {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: rotate scale(1.1);
    -webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    -o-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}

.shape {
    border-color: rgba(255,255,255,0) #d9534f rgba(255,255,255,0) rgba(255,255,255,0);
}

.shapeTop {
    border-color: rgba(255,255,255,0) #ffffff rgba(255,255,255,0) rgba(255,255,255,0);
    border-style: solid;
    border-width: 0 40px 40px 0;
    float: right;
    height: 0px;
    width: 0px;
    -ms-transform: rotate(360deg); /* IE 9 */
    -o-transform: rotate(360deg); /* Opera 10.5 */
    -webkit-transform: rotate(360deg); /* Safari and Chrome */
    transform: rotate(360deg);
}


.speical-default {
    border: 1px solid #00bfff;
}

.speical-radius {
    border-radius: 4px;
}

.speicalStyle-radius {
    border-radius: 4px;
}


.shape-text {
    color: #fff;
    font-size: 12px;
    position: relative;
    right: -20px;
    top: 0px;
    white-space: nowrap;
    -ms-transform: rotate(45deg); /* IE 9 */
    -o-transform: rotate(45deg); /* Opera 10.5 */
    -webkit-transform: rotate(45deg); /* Safari and Chrome */
    transform: rotate(45deg);
}

.text-special-default {
    color: #d9534f;
}

.speical-content {
    padding: 0 20px 10px;
}

.martop {
    margin-top: -20px;
}
.t_score_sp{
   color:#0f89fd;
}
.drag_line1{
    width:80%;
    display:inline-block;
}
.drag_line2{
    width:17%;
    display:inline-block;
     vertical-align: middle;
}
.sg_score{
    float: right;
    font-size:16px;
    margin-top:3px;

}
.sv_btn{
    height: 30px; 
    width: 100px; 
    font-size: 14px;
    bottom:30px;
    background-color:#0f89fd;
    margin-left:60px;
}
.sv_btn:hover{
   background-color:#30a1f7
}
.sv_btn_line{
   height: 40px;
   margin: 0px 0; 
   text-align: center;
   background: #f6fafe;
    position: fixed;
    bottom: 0;
    width: 100%;
}
.sg_sg{
    vertical-align: middle;
    margin-right: 5px;
}
.sg_add{
    margin-left:10px;
}
.col-md-3{
    width:30%;
    display:inline-block
}
.icn_img{
    width:20px !important;
    height:20px !important;
}
.bac_over{
    opacity:0.6
}

#imgTemplate{ text-align: center;}
#imgbox-loading {position: absolute;top: 0;left: 0;	cursor: pointer;display: none;z-index: 90;}
#imgbox-loading div {background: #FFF;width: 100%;height : 100%;}
.imgbox-overlay {position: absolute;top: 0;	left: 0;width: 100%;height: 100%;background: #000;display: none;z-index: 80;opacity:0.6}
.imgbox-wrap {position: absolute;top: 0;left: 0;background: #FFF;display: none;	z-index: 90;}
.imgbox-img {padding: 0;margin: 0;border: none;width: 100%;	height: 100%;vertical-align: top;}
.imgbox-title {	padding-top: 10px;font-size: 11px;text-align: center;font-family: Arial;color: #333;display: none;}
.imgbox-bg-wrap {position: absolute;padding: 0;margin: 0;display: none;}
.imgbox-bg {position: absolute;width: 20px;	height: 20px;}
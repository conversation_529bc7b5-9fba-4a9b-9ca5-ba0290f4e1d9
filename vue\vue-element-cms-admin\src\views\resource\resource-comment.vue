<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-select v-model="listQuery.KnowledgeResourceId" size="small" clearable
          @change="handleKnowledgeResourceChange">
          <el-option v-for="item in resourceList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button v-permission="['CourseManagement.KnowledgeComments']" size="small" round type="success"
          icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <!-- <el-button v-permission="['CourseManagement.KnowledgeComments.Create']" size="small" round type="primary" icon="el-icon-plus" @click="handleResourceCommentEdit(0, 0)">添加</el-button> -->
        <export-excel :header="['资源名称', '内容', '提交人', '提交时间']"
          :filter-val="['knowledgeResource.name', 'content', 'author', 'creationTime']" :field="{ 3: [2] }"
          :api-fn="resourceCommentList" />
      </div>
      <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
        <el-table-column label="资源名称" prop="knowledgeResource.name" sortable="knowledgeResource.name"
          show-overflow-tooltip width="180" />
        <!-- <el-table-column label="类型" prop="commentType" sortable="commentType" width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.commentType === 0" type="info" size="mini">评论</el-tag>
            <el-tag v-else-if="row.commentType === 1" type="success" size="mini">提问</el-tag>
            <el-tag v-else type="primary" size="mini">回答</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="内容" prop="content" sortable="content" show-overflow-tooltip />
        <el-table-column label="提交人" prop="author" sortable="author" width="160" />
        <el-table-column label="提交时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="{ row }">
            <!-- <el-button size="mini" round type="primary" icon="el-icon-chat-line-square" @click="handleReplayDetail(row)">回复</el-button> -->
            <!-- <el-button v-permission="['ResourceManagement.ResourceComments.Update']" size="mini" round type="primary" icon="el-icon-edit" @click="handleResourceCommentEdit(1, row)">编辑</el-button> -->
            <el-button v-permission="['CourseManagement.KnowledgeComments.Delete']" size="mini" round type="danger"
              icon="el-icon-delete" @click="handleResourceCommentDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getResourceCommentList" />
    </el-card>
    <el-dialog :title="isEdit ? '编辑' : '新增'" :close-on-click-modal="false" :visible.sync="commentDialog" width="450px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="资源" prop="knowledgeResourceId">
          <el-select v-model="form.knowledgeResourceId" @change="resourceChange">
            <el-option v-for="item in resourceList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item> -->
        <el-form-item label="内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="2" />
        </el-form-item>
        <!-- <el-form-item label="用户" prop="author">
          <el-input v-model="form.author" />
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round :loading="dialogLoading" size="small" type="primary" @click="handleCommentSure">确 定</el-button>
        <el-button round size="small" @click="commentDialog = false">取 消</el-button>

      </span>
    </el-dialog>
    <el-dialog title="回复列表" :close-on-click-modal="false" :visible.sync="replayDialog" width="900px">
      <el-table v-loading="replayListLoading" :data="replayList" size="small" highlight-current-row
        @sort-change="sortChange">
        <el-table-column label="回复人" prop="author" sortable="author" width="150" />
        <el-table-column label="回复内容" prop="content" sortable="content" show-overflow-tooltip />
        <el-table-column label="回复时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{ row }">
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleReplayDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="replayListQuery.totalCount > 0" :total="replayListQuery.totalCount"
        :page.sync="replayListQuery.page" :limit.sync="replayListQuery.MaxResultCount" @pagination="getReplayList" />
    </el-dialog>
  </div>
</template>
<script>
import {
  resourceList,
  resourceCommentList,
  addResourceComment,
  editResourceComment,
  deletesResourceComment
} from '@/api/resource'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'ResourceComment',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        KnowledgeResourceId: '',
        ParentId: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      resourceList: [],

      replayList: [],
      replayListQuery: {
        Filter: '',
        KnowledgeResourceId: '',
        ParentId: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      replayListLoading: false,
      replayDialog: false,

      isEdit: false,
      commentDialog: false,
      dialogTitle: '新增',
      dialogLoading: false,
      form: {
        knowledgeResourceId: '819c2906-8007-be59-9a1b-3a03aa372c37',
        // title: '',
        content: '新评论',
        // author: '',
        author: '测试资源',
        agreeCount: 0,
        parentId: null
        // commentType: 0
      },
      rules: {
        title: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 50 个字符',
          trigger: 'blur'
        }],
        content: [{
          required: true,
          message: '请输入评论',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 100 个字符',
          trigger: 'blur'
        }
        ],
        KnowledgeResourceId: [{
          required: true,
          message: '请选择资源',
          trigger: 'change'
        }],
        author: [{
          required: true,
          message: '请输入用户',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ]
      }
    }
  },
  created() {
    this.getResourceCommentList()
    this.getResourceList()
  },
  methods: {
    resourceCommentList(args) {
      return resourceCommentList(args)
    },
    resourceChange(v) {
      this.resourceList.map((item) => {
        if (item.id === v) {
          this.form.resourceName = item.name
          return
        }
      })
    },
    handleKnowledgeResourceChange(val) {
      this.getResourceCommentList()
    },
    handleResourceCommentEdit(t, row) {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      // this.resetForm()
      this.isEdit = !!t
      if (this.isEdit) {
        this.form.resourceId = row.resourceId
        this.form.resourceName = row.resourceName
        this.form.parentId = row.parentId
        this.form.content = row.content
        // this.form.commentType = row.commentType
        this.form.id = row.id
      }
      this.commentDialog = true
    },
    handleCommentSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.isEdit) {
            editResourceComment(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.commentDialog = false
              this.dialogLoading = false
              this.getResourceCommentList()
            }).catch(() => {
              this.$message.error('编辑失败')
              this.dialogLoading = false
            })
          } else {
            addResourceComment(this.form).then(res => {
              this.$message.success('添加成功')
              this.commentDialog = false
              this.dialogLoading = false
              this.getResourceCommentList()
            }).catch(() => {
              this.$message.error('添加失败')
              this.dialogLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleResourceCommentDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesResourceComment(row.id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getResourceCommentList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    handleReplayDetail(row) {
      this.replayListQuery.ParentId = row.id
      this.replayListQuery.ResourceId = row.resourceId
      this.getReplayList()
      this.replayDialog = true
    },
    handleReplayDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesResourceComment(row.id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getReplayList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getResourceCommentList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceCommentList()
    },
    resetForm() {
      this.form = {
        resourceId: '',
        // title: '',
        content: '',
        // author: '',
        resourceName: '',
        parentId: null
        // commentType: '0'
      }
    },
    getResourceCommentList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceCommentList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    getReplayList() {
      this.replayListLoading = true
      this.replayListQuery.SkipCount =
        (this.replayListQuery.page - 1) * this.replayListQuery.MaxResultCount
      resourceCommentList(this.replayListQuery)
        .then((res) => {
          this.replayList = res.items
          this.replayListQuery.totalCount = res.totalCount
          this.replayListLoading = false
        })
        .catch(() => {
          this.replayListLoading = false
        })
    },
    getResourceList() {
      var data = {
        Filter: '',
        Status: '',
        ResourceCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      resourceList(data)
        .then((res) => {
          this.resourceList = res.items
        })
        .catch(() => {
          this.$message.error('获取资源列表失败')
        })
    }
  }
}
</script>

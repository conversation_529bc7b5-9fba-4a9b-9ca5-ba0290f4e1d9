<template>
  <div>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <export-excel
      :header="['用户名', '姓名', '资源总时长', '学习时长','学习进度','学习状态','考评状态','学时状态','获得学时','开始学习时间','最后学习时间']"
      :filter-val="['userName', 'name', 'resourceDuration', 'courseLearnDuration','courseLearnProgress', 'learnPass', 'examPass', 'classHour','classHour', 'creationTime', 'lastLearnTime']"
      :query="{'TrainId': trainId, 'CourseId': courseId}"
      :field="{2: [3], 3: [3], 4: ['%'],5:['未完成','已完成'], 6: ['未通过','已通过'], 7: ['未获得', '已获得'],9: [2], 10: [2]}"
      :api-fn="trainCourseAllUser"
    />
    <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
      <el-table-column label="用户名" prop="userName" width="160" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span>{{ row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="name" width="160" show-overflow-tooltip />
      <el-table-column label="资源总时长" prop="resourceDuration" width="140">
        <template slot-scope="{ row }">
          {{ row.resourceDuration | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column label="学习时长" prop="courseLearnDuration" sortable="courseLearnDuration" width="140">
        <template slot-scope="{ row }">
          {{ row.courseLearnDuration | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column label="学习进度" prop="courseLearnProgress" sortable="courseLearnProgress" width="120">
        <template slot-scope="{ row }">
          {{ row.courseLearnProgress.toFixed(2) }} %
        </template>
      </el-table-column>
      <el-table-column label="学习状态" prop="learnPass" sortable="learnPass" width="120">
        <template slot-scope="{ row }">
          <el-tag v-if="row.learnPass" size="small" type="success">已完成</el-tag>
          <el-tag v-else size="small" type="info">未完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="考评状态" prop="examPass" sortable="examPass" width="120">
        <template slot-scope="{ row }">
          <el-tag v-if="row.examPass" size="small" type="success">已通过</el-tag>
          <el-tag v-else size="small" type="info">未通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="学时状态" prop="classHour" sortable="classHour" width="120">
        <template slot-scope="{ row }">
          <el-tag v-if="row.classHour > 0" size="small" type="success">已获学时</el-tag>
          <el-tag v-else size="small" type="info">未获学时</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="课程学时" sortable="classHour" width="120">
          <template slot-scope="{ row }">
            {{ tmpData.classHour }}
          </template>
        </el-table-column> -->
      <el-table-column label="获得学时" prop="classHour" sortable="classHour" width="120">
        <template slot-scope="{ row }">
          {{ row.classHour }}
        </template>
      </el-table-column>
      <el-table-column label="开始学习时间" prop="creationTime" sortable="creationTime" width="160">
        <template slot-scope="{ row }">
          {{ row.creationTime | formatDateTime }}
        </template>
      </el-table-column>
      <el-table-column label="最后学习时间" prop="lastLearnTime" sortable="lastLearnTime" width="160">
        <template slot-scope="{ row }">
          {{ row.lastLearnTime | formatDateTime }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getCourseUserDetail"
    />
  </div>
</template>
<script>
// import { courseRecordList } from '@/api/course'
import { trainCourseAllUser } from '@/api/train'

import Pagination from '@/components/Pagination'
export default {
  name: 'TCourseDetail',
  components: {
    Pagination
  },
  props: {
    courseId: {
      reuqerd: true,
      type: String,
      default: ''
    },
    trainId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'lastLearnTime desc',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1,
        TrainId: this.trainId,
        CourseId: this.courseId
      }

    }
  },
  created() {
    this.getCourseUserDetail()
  },
  methods: {
    trainCourseAllUser(args) {
      return trainCourseAllUser(args)
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getCourseUserDetail()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getCourseUserDetail()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseUserDetail()
    },
    // 获取课程下所有学生
    getCourseUserDetail() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      // courseRecordList(this.listQuery).then(res => {
      trainCourseAllUser(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>


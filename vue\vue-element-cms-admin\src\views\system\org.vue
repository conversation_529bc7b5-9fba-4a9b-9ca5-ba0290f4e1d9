<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="6" :md="5" :lg="4" :xl="4">
        <!-- <div class="head-container">
          <el-input
            v-model="orgName"
            clearable
            size="small"
            placeholder="搜索..."
            prefix-icon="el-icon-search"
            @input="getOrgs"
          />
        </div> -->
        <el-card class="box-card">
          <!-- <el-tree
            :data="orgDatas"
            :load="getOrgs"
            :props="defaultProps"
            :expand-on-click-node="false"
            lazy
            style="margin-top: 15px"
            @node-click="handleNodeClick"
          /> -->
          <el-tree
            :data="orgDatas"
            :props="defaultProps"
            highlight-current
            style="margin-top: 5px"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="18" :md="19" :lg="20" :xl="20">
        <el-card class="box-card">
          <div class="header_flex_box">
            <el-input
              v-model="listQuery.Filter"
              clearable
              size="small"
              placeholder="搜索..."
              class="small_input"
              @keyup.enter.native="handleFilter"
            />
            <el-button
              round
              size="mini"
              type="success"
              icon="el-icon-search"
              @click="handleFilter"
            >搜索</el-button>
            <el-button
              v-permission="['AppUserManagement.OrganizationUnits.Create']"
              round
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="handleCreate"
            >新增</el-button>
            <!-- <el-button round
              size="mini"
              type="success"
              icon="el-icon-edit"
              v-permission="['AppUserManagement.OrganizationUnits.Update']"
              @click="handleUpdate()"
            >修改</el-button> -->

            <div style="" />
          </div>
          <el-dialog
            :close-on-click-modal="false"
            :visible.sync="dialogFormVisible"
            :title="formTitle"
            width="520px"
            @close="cancel()"
          >
            <el-form
              ref="form"
              :inline="true"
              :model="form"
              :rules="rules"
              size="small"
              label-width="80px"
            >
              <el-form-item label="名称" prop="displayName">
                <el-input v-model="form.displayName" style="width: 380px" />
              </el-form-item>

              <el-form-item label="上级">
                <el-radio-group v-model="form.isTop" style="width: 140px">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="form.isTop === false"
                style="margin-bottom: 0"
                label="上级"
                prop="parentId"
              >
                <!-- <treeselect
                  v-model="form.parentId"
                  :load-options="loadorgs"
                  :options="organization"
                  style="width: 370px"
                  placeholder="选择上级"
                /> -->
                <el-select v-model="form.parentId">
                  <el-option v-for="item in nodesOrgs" :key="item.id" :label="item.displayName" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button round type="" @click="cancel">取消</el-button>
              <el-button
                round
                :loading="formLoading"
                type="primary"
                @click="save"
              >确认</el-button>
            </div>
          </el-dialog>
          <el-table
            ref="multipleTable"
            v-loading="listLoading"
            row-key="id"
            :data="list"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="44px" />
            <el-table-column label="名称" prop="displayName">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleUpdate(row)">{{
                  row.displayName
                }}</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="200">
              <template slot-scope="{ row }">
                <el-button
                  v-permission="['AppUserManagement.OrganizationUnits.Update']"
                  round
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  @click="handleUpdate(row)"
                >编辑</el-button>
                <el-button
                  v-permission="['AppUserManagement.OrganizationUnits.Delete']"
                  round
                  type="danger"
                  size="mini"
                  :disabled="row.name === 'admin'"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="totalCount > 0"
            :total="totalCount"
            :page.sync="page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect'

const defaultForm = {
  id: null,
  displayName: null,
  parentId: null,
  isTop: true
}
export default {
  name: 'Organization',
  components: { Pagination, Treeselect },
  directives: { permission },
  filters: {},

  data() {
    return {
      rules: {
        displayName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      defaultProps: {
        children: 'children',
        label: 'label'
        // isLeaf: "leaf"
      },
      form: Object.assign({}, defaultForm),
      orgName: '',
      list: [],
      orgs: [],
      orgDatas: [],
      organization: [],
      totalCount: 0,
      listLoading: true,
      formLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10
      },
      page: 1,
      dialogFormVisible: false,
      multipleSelection: [],
      formTitle: '',
      isEdit: false,
      isTop: true,
      nodesOrgs: []
    }
  },
  created() {
    this.getList()
    this.getThreeDatas()
    this.getNodes()
  },
  methods: {
    getThreeDatas() {
      this.$axios
        .gets('/api/appuser/organization/loadNodes')
        .then((response) => {
          this.treeData = response
          this.loadTree(this.treeData)
        })
    },
    getNodes() {
      this.$axios
        .gets('/api/appuser/organization/loadNodes')
        .then((response) => {
          this.nodesOrgs = response.items.filter(item => {
            return item.parentId === null
          })
        })
    },
    loadTree(data) {
      if (data.items === undefined) {
        return
      }
      data.items.forEach((item) => {
        if (item.parentId === null) {
          var element = {
            id: item.id,
            label: item.displayName
            // children: null,
          }
          element.hasChildren = false
          // element.children = [];
          this.orgs.push(element)
        }
      })

      this.setChildren(this.orgs, data.items)
      this.orgDatas = this.orgs

    },
    setChildren(roots, items) {
      roots.forEach((element) => {
        items.forEach((item) => {
          if (item.parentId === element.id) {
            if (!element.children) element.children = []
            element.children.push({
              id: item.id,
              label: item.displayName
              // children: null,
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },
    getOrgNodes(id) {
      this.$axios
        .gets('/api/appuser/organization/loadNodes')
        .then((response) => {
          this.loadTree(response)
        })
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      this.list = []
      this.$axios
        .gets('/api/appuser/organization/all', this.listQuery)
        .then((response) => {
          this.list = response.items
          this.totalCount = response.totalCount
          this.listLoading = false
        })
    },
    fetchData(id) {
      this.getOrgNodes(id)
      this.$axios.gets('/api/appuser/organization/' + id).then((response) => {
        this.form = response
        this.isTop = !response.parentId
      })
    },
    loadorgs({ action, parentNode, callback }) {
      if (action === LOAD_CHILDREN_OPTIONS) {
        this.$axios
          .gets('/api/appuser/organization/loadorgs', { id: parentNode.id })
          .then((response) => {
            parentNode.children = response.items.map(function(obj) {
              // if (!obj.leaf) {
              //   obj.children = null;
              // }
              if (obj != null) {
                return { id: obj.id, label: obj.displayName, children: null }
              }
            })
            setTimeout(() => {
              callback()
            }, 100)
          })
      }
    },
    handleFilter() {
      this.page = 1
      this.listQuery.id = null
      this.getList()
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {

            this.$axios
              .puts('/api/appuser/organization/' + this.form.id, this.form)
              .then((response) => {
                this.formLoading = false
                this.$notify({
                  title: '成功',
                  message: '更新成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.listQuery.id = null
                this.getList()
              })
              .catch(() => {
                this.formLoading = false
              })
          } else {
            this.$axios
              .posts('/api/appuser/organization', this.form)
              .then((response) => {
                this.formLoading = false
                this.$notify({
                  title: '成功',
                  message: '新增成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialogFormVisible = false
                this.listQuery.id = null
                this.getList()
              })
              .catch(() => {
                this.formLoading = false
              })
          }
        }
      })
    },
    handleCreate() {
      this.formTitle = '新增'
      this.isEdit = false
      this.dialogFormVisible = true
      this.$axios
        .gets('/api/appuser/organization/loadorgs')
        .then((response) => {
          this.organization = response.items.map(function(obj) {
            if (obj.parentId == null) {
              return { id: obj.id, label: obj.displayName, children: null }
            }
          })
        })
    },
    handleDelete(row) {
      this.$confirm('是否删除 ' + row.displayName + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios
          .deletes('/api/appuser/organization/' + row.id)
          .then((response) => {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.listQuery.id = null
            this.getList()
          })
      })
    },
    handleUpdate(row) {
      this.formTitle = '修改'
      this.isEdit = true

      if (row) {
        this.dialogFormVisible = true
        this.fetchData(row.id)
      } else {
        if (this.multipleSelection.length !== 1) {
          this.$message({
            message: '编辑必须选择单行',
            type: 'warning'
          })
          return
        } else {
          this.fetchData(this.multipleSelection[0].id)
          this.dialogFormVisible = true
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRowClick(row, column, event) {
      this.$refs.multipleTable.clearSelection()
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    cancel() {
      this.dialogFormVisible = false
      this.$refs.form.clearValidate()
      this.form = Object.assign({}, defaultForm)
      this.organization = []
    },
    handleNodeClick(data) {
      this.listQuery.id = data.id
      this.getList()
    },
    changeEnabled(data, val) {
      data.active = val ? '启用' : '停用'
      this.$confirm('是否' + data.active + data.displayName + '？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$axios
            .puts('/api/appuser/organization/' + data.id, data)
            .then((response) => {
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
            })
            .catch(() => {
              data.enabled = !data.enabled
            })
        })
        .catch(() => {
          data.enabled = !data.enabled
        })
    }
  }
}
</script>

<template>
  <div :data="data" class="question">
    <el-form ref="questionForm" size="medium" :model="questionForm" label-width="100px" :rules="rules">
      <el-form-item class="question_filter" label="题型" prop="questionType">
        <el-radio-group v-model="questionForm.questionType" :disabled="isEdit" @change="radioChange">
          <el-radio :label="0">单选题</el-radio>
          <el-radio :label="1">多选题</el-radio>
          <el-radio :label="2">判断题</el-radio>
          <el-radio :label="3">填空题</el-radio>
          <el-radio :label="6">问答题</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="question_filter" label="难度" prop="difficulty">
        <el-radio-group v-model="questionForm.difficulty">
          <el-radio :label="1">易</el-radio>
          <el-radio :label="2">偏易</el-radio>
          <el-radio :label="3">适中</el-radio>
          <el-radio :label="4">偏难</el-radio>
          <el-radio :label="5">难</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-for="tagType in tagTypeList" class="question_filter" :label="tagType.tagTypeName" prop="Tags">
        <el-checkbox-group v-model="tagType.tagIds" @change="tagCheckboxChange(tagType)">
          <el-checkbox v-for="tag in tagType.tags" :label="tag.id">{{ tag.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item v-show="!isEdit" label="分类" prop="questionBankCategoryId">
        <el-cascader
          v-model="categoryOptionVules"
          :options="categoryOptions"
          clearable
          style="width:100%"
          @change="categoryHandleChange"
        />
      </el-form-item>
      <el-form-item label="题干" prop="questionStem.Title">
        <div class="header_flex_box">
          <el-input v-model="questionForm.questionStem.Title" />
          <local-file-upload style="margin-left:10px" :file-list="questionForm.questionStem.Title_Imgs" :multiple="true" :show-file-list="false" :file-size="500" :is-round="false" :btn-title="'上传题干图片'" @response-fn="handleResponse" @remove-upload="handleRemoveUploadFile" />
        </div>
        <div v-if="questionForm.questionStem.Title_Imgs&&questionForm.questionStem.Title_Imgs!=''" class="option_images">
          <ele-gallery
            :lazy="true"
            :width="200"
            :height="120"
            :source="questionForm.questionStem.Title_Imgs"
          >
            <template v-slot:action="{index}">
              <span>
                <i class="el-icon-delete" @click="handleRemoveTitleUploadImage(index,questionForm.questionStem.Title_Imgs)" />
              </span>
            </template>
          </ele-gallery>
        </div></el-form-item>
      <!-- <div class="header_flex_box"> -->
      <el-row v-if="questionForm.questionType !==3 && questionForm.questionType !==6" :gutter="10">
        <el-col :span="4" style="width: 93px">
          <span class="option_title">选项</span>
        </el-col>
        <el-col :span="20">
          <div class="question_options">
            <el-radio-group v-if="questionForm.questionType === 0" v-model="singleAnswerModel" @change="handleSingleAnswerChange">
              <el-radio v-for="(item,i) in questionForm.questionStem.Options" :key="item.Order" :label="item.Order">
                <span>{{ numSwitchChar(i + 1) }} 、 </span>
                <el-form-item
                  :prop="'questionStem.Options.' + i + '.Title'"
                  :rules="{
                    required: true, message: '不能为空', trigger: 'blur'
                  }"
                  class="option_form_item"
                >
                  <el-input v-model="item.Title" style="width:500px" />
                </el-form-item>
                <el-button type="text" icon="el-icon-delete" size="mini" style="margin-left: 10px;color: #F56C6C" circle @click="handleDeleteOption(i, item.Order)" />
                <local-file-upload style="display:inline-block;margin-left:10px" :file-list="item.Images" :current-index="i" :show-file-list="false" :btn-type="true" :multiple="true" :file-size="500" :is-round="false" @response-fn="handleOptionResponse" @remove-upload="handleRemoveUploadImage" />
                <div v-if="item.Images&&item.Images!=''" class="option_images">
                  <ele-gallery
                    :lazy="true"
                    :width="200"
                    :height="120"
                    :source="item.Images"
                  >
                    <template v-slot:action="{index}">
                      <span>
                        <i class="el-icon-delete" @click.prevent="handleRemoveUploadImage(i,index,item.Images) " />
                      </span>
                    </template>
                  </ele-gallery>
                </div>

              </el-radio>
            </el-radio-group>
            <el-checkbox-group v-if="questionForm.questionType === 1" v-model="multiAnswerModel" @change="handleMultiAnswerChange">
              <el-checkbox v-for="(item,i) in questionForm.questionStem.Options" :key="item.Order" :label="item.Order">
                <span>{{ numSwitchChar(i + 1) }} 、 </span>
                <el-form-item
                  :prop="'questionStem.Options.' + i + '.Title'"
                  :rules="{
                    required: true, message: '不能为空', trigger: 'blur'
                  }"
                  class="option_form_item"
                >
                  <el-input v-model="item.Title" style="width:500px" />
                </el-form-item>
                <el-button type="text" icon="el-icon-delete" size="mini" style="margin-left: 10px;color: #F56C6C" circle @click="handleDeleteOption(i, item.Order)" />
                <local-file-upload style="display:inline-block;margin-left:10px" :file-list="item.Images" :current-index="i" :show-file-list="false" :btn-type="true" :multiple="true" :file-size="500" :is-round="false" @response-fn="handleOptionResponse" @remove-upload="handleRemoveUploadImage" />
                <div v-if="item.Images" class="option_images">
                  <ele-gallery
                    :lazy="true"
                    :width="200"
                    :height="120"
                    :source="item.Images"
                  >
                    <template v-slot:action="{index}">
                      <span>
                        <i class="el-icon-delete" @click.prevent="handleRemoveUploadImage(i,index,item.Images) " />
                      </span>
                    </template>
                  </ele-gallery>
                </div>
              </el-checkbox>
            </el-checkbox-group>
            <el-radio-group v-if="questionForm.questionType === 2" v-model="judgeAnswerModel" @change="handleJudgeAnswerChange">
              <el-radio :label="1">正确</el-radio>
              <el-radio :label="0">错误</el-radio>
            </el-radio-group>
          </div>
          <!-- </div> -->
        </el-col>
      </el-row>
      <el-form-item>
        <el-button v-show="questionForm.questionType === 0 || questionForm.questionType === 1" style="width:120px" type="primary" icon="el-icon-plus" @click="handleAddOption">添加选项</el-button>
      </el-form-item>
      <el-form-item label="注解" prop="analysis">
        <el-input v-model="questionForm.analysis" type="textarea" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import LocalFileUpload from '@/components/LocalFileUpload'
import apiQuesBankCategory from '@/api/questionBankCategory'
import apiQuestionBank from '@/api/questionBank'
import EleGallery from 'vue-ele-gallery'
export default {
  name: 'QuestionAddOrEdit',
  components: {
    LocalFileUpload,
    EleGallery
  },
  props: {
    data: {
      type: Object,
      required: true,
      default: function() {
        return {
          questionStem: '',
          answer: '',
          analysis: '',
          questionType: 0,
          difficulty: 1
        }
      }
    },
    isEdit: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    // var optionCheck = (rule, value, callback) => {
    //   if (this.questionForm.questionStem.Options && this.questionForm.questionStem.Options.length !== 0) {
    //     var check = true
    //     for (var i = 0; i < this.questionForm.questionStem.Options.length; i++) {
    //       if (this.questionForm.questionStem.Options[i].Title.length === 0) {
    //         if (i >= 4) {
    //           this.$refs.questionForm.fields[4 + i + 1].validateState = 'error'
    //           this.$refs.questionForm.fields[4 + i + 1].validateMessage = '选项不能为空'
    //         } else {
    //           this.$refs.questionForm.fields[4 + i].validateState = 'error'
    //           this.$refs.questionForm.fields[4 + i].validateMessage = '选项不能为空'
    //         }
    //         check = false
    //         // break
    //       }
    //     }
    //     if (check) {
    //       callback()
    //     }
    //   } else {
    //     callback(new Error('请添加选项'))
    //   }
    // }
    var categoryValidate = (rule, value, callback) => {
      if (!this.isEdit) {
        if (this.categoryOptionVules.length === 0) {
          callback(new Error('请选择分类'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      rules: {
        questionStem: {
          Title: [{
            required: true,
            message: '请输入题干',
            trigger: 'blur'
          }]

        //   , Options: [{
        //     required: true, validator: optionCheck
        //   }]
        },
        questionBankCategoryId: [{ required: !this.isEdit, validator: categoryValidate }]
      },
      questionForm: {
        questionType: null,
        questionStem: {},
        answer: {},
        analysis: '',
        difficulty: null,
        questionBankCategoryId: null,
        questionBankTags: []
      },
      singleAnswerModel: null,
      multiAnswerModel: [],
      judgeAnswerModel: 1,
      categoryOptions: [],
      categoryOptionVules: [],
      tempCategoryList: [],
      quesCategoryIds: [], // 当前题目所属多级分类
      tagTypeList: [],
      quesTagIds: []
    }
  },
  watch: {
    data() {
      this.initFormData()
    }
  },
  mounted() {
    this.$on('form-data', () => {
      this.$refs.questionForm.validate((valid) => {
        if (valid) {
          var repeat = this.isRepeat(this.questionForm.questionStem.Options)
          if (repeat === 1) {
            this.$message.warning('有重复的选项')
            return
          } else if (repeat === 2) {
            this.$message.warning('请完善新选项')
            return
          }
          const newOptionAnswers = []
          if (this.questionForm.questionType === 0) {
            this.questionForm.questionStem.Options.forEach((oItem, index) => {
              if (oItem.Order === this.singleAnswerModel) {
                newOptionAnswers.push({
                  Title: oItem.Title,
                  Order: this.singleAnswerModel
                })
              }
            })
            this.questionForm.answer.OptionAnswers = newOptionAnswers
          } else if (this.questionForm.questionType === 1) {
            this.multiAnswerModel.forEach(item => {
              this.questionForm.questionStem.Options.forEach((oItem, index) => {
                if (oItem.Order === item) {
                  newOptionAnswers.push({
                    Title: oItem.Title,
                    Order: item
                  })
                }
              })
            })
            this.questionForm.answer.OptionAnswers = newOptionAnswers
          } else if (this.questionForm.questionType === 2) {
            this.questionForm.answer.JudgeAnswer = this.judgeAnswerModel
          } else if (this.questionForm.questionType === 3 || this.questionForm.questionType === 6) {
            this.questionForm.questionStem.Options = []
          }
          // 检测是否有答案
          if (this.questionForm.questionType === 1 || this.questionForm.questionType === 0) {
            if (newOptionAnswers.length < 1) {
              this.$message.warning('请设置正确答案')
              return
            }
          }
          if (this.questionForm.questionType === 3) {
            if (this.questionForm.questionStem.Title.indexOf('__') === -1) {
              this.$message.warning('请至少设置一个填空（包含__）')
              return
            }
          }
          this.questionForm.questionBankTags = []
          this.tagTypeList.forEach(item => {
            this.questionForm.questionBankTags.push({
              tagTypeId: item.tagTypeId,
              selected: item.tagIds.length > 0,
              tagIds: item.tagIds
            })
          })

          this.$emit('question-form', this.questionForm)
        } else {
          return false
        }
      })
    })
  },
  created() {
    this.initFormData()
  },
  methods: {
    radioChange(val) {
      var answer = this.questionForm.answer
      this.singleAnswerModel = null
      this.multiAnswerModel = []
      this.judgeAnswerModel = 1
      if (answer && answer.OptionAnswers) {
        if (this.questionForm.questionType === 0) {
          if (answer.OptionAnswers.length) {
            this.singleAnswerModel = answer.OptionAnswers[0].Order
          }
        } else if (this.questionForm.questionType === 1) {
          answer.OptionAnswers.forEach(item => {
            this.multiAnswerModel.push(item.Order)
          })
        } else if (this.questionForm.questionType === 2) {
          this.judgeAnswerModel = answer.JudgeAnswer
        }
      }
      // if (this.questionForm.questionType === 0) {
      //   if (this.singleAnswerModel == null) {
      //     if (this.questionForm.answer.OptionAnswers.length > 0) {
      //       this.singleAnswerModel = this.questionForm.answer.OptionAnswers[0].Order
      //     } else {
      //       this.singleAnswerModel = []
      //     }
      //   }
      // } else if (this.questionForm.questionType === 1) {
      //   if (this.multiAnswerModel.length === 0) {
      //     this.multiAnswerModel = []
      //     this.questionForm.answer.OptionAnswers.forEach(item => {
      //       this.multiAnswerModel.push(item.Order)
      //     })
      //   }
      // } else if (this.questionForm.questionType === 2) {
      //   if (this.judgeAnswerModel == null) {
      //     this.judgeAnswerModel = this.questionForm.answer.JudgeAnswer
      //   }
      // }
    },
    tagCheckboxChange(tagType) {
      console.log('tagRadioChange', tagType)
    },
    initFormData() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.questionForm.id = this.data.id
      this.questionForm.questionStem = JSON.parse(this.data.questionStem)
      this.questionForm.answer = JSON.parse(this.data.answer)
      this.questionForm.questionType = this.data.questionType
      this.questionForm.difficulty = this.data.difficulty
      this.questionForm.analysis = this.data.analysis

      if (this.questionForm.questionStem.Title_Imgs !== null) {
        var titleImgArr = []
        this.questionForm.questionStem.Title_Imgs = this.questionForm.questionStem.Title_Imgs.split(',')
        this.questionForm.questionStem.Title_Imgs.forEach(item => {
          titleImgArr.push({
            name: item,
            url: item,
            src: item
          })
        })
        this.questionForm.questionStem.Title_Imgs = titleImgArr
      } else {
        this.questionForm.questionStem.Title_Imgs = []
      }
      if (this.questionForm.questionType === 0 || this.questionForm.questionType === 1) {
        this.questionForm.questionStem.Options.forEach((op, i) => {
          if (op.Images !== null) {
            var optionImgArr = []
            op.Images = op.Images.split(',')
            op.Images.forEach(item => {
              optionImgArr.push({
                name: item,
                url: item,
                src: item
              })
            })
            op.Images = optionImgArr
          } else {
            op.Images = []
          }
        })
      }

      this.radioChange()
      this.getCategorys()
    },
    getCategorys() {
      if (!this.isEdit) {
        apiQuesBankCategory.getCategorys().then(response => {
          this.loadCategoryOptions(response.items)
          this.tempCategoryList = response.items
        })
      }
      this.categoryOptionVules = null
      this.categoryOptionVules = this.data.categoryOptionVules
      if (this.data.categoryOptionVules.length > 0) {
        this.questionForm.questionBankCategoryId = this.data.categoryOptionVules[this.data.categoryOptionVules.length - 1]
      }
      this.getTagTypes()
    },
    categoryHandleChange(value) {
      // console.log('categoryHandleChange', value)
      if (value.length === 1) {
        this.categoryOptionVules = []
        this.$message({ type: 'warning', message: '只能选择二级分类' })
        this.questionForm.questionBankCategoryId = null
        return
      }
      this.quesCategoryIds = value
      if (value.length > 0) {
        this.questionForm.questionBankCategoryId = value[value.length - 1]
      }
      this.getTagTypes()
    },
    loadCategoryOptions(items) {
      this.categoryOptions = []
      items.forEach((item) => {
        if (item.parentId === null) {
          var element = {
            value: item.id,
            label: item.name
          }
          this.categoryOptions.push(element)
        }
      })
      this.setChildren(this.categoryOptions, items)
    },
    setChildren(roots, items) {
      roots.forEach((element) => {
        items.forEach((item) => {
          if (item.parentId === element.value) {
            if (!element.children) element.children = []
            element.children.push({
              value: item.id,
              label: item.name
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },
    getTagTypes() {
      this.tagTypeList = []
      this.quesTagIds = []
      if (this.isEdit) {
        apiQuestionBank.quesTagIds(this.questionForm.id).then(res => {
          this.quesTagIds = res.items
        })
      }
      this.categoryOptionVules.forEach(async(v) => {
        apiQuestionBank.tagTypes({
          MaxResultCount: 999,
          SkipCount: 0,
          Sorting: 'sort',
          QuestionBankCategoryId: v
        }).then(response => {
          response.items.forEach(async(item) => {
            var tags = await this.getTags(item.id)
            var selTags = tags.filter(x => this.quesTagIds.indexOf(x.id) > -1)
            var _type = {
              tagTypeName: item.name,
              tagTypeId: item.id,
              selected: false,
              tagIds: [],
              tags: tags
            }
            if (selTags.length > 0) {
              selTags.forEach(t => {
                _type.tagIds.push(t.id)
              })
              _type.selected = true
            }
            this.tagTypeList.push(_type)
          })
        })
      })
    },
    async getTags(typeId) {
      var tags = await apiQuestionBank.tags({
        MaxResultCount: 999,
        SkipCount: 0,
        Sorting: 'sort',
        QuestionBankTagTypeId: typeId
      })
      return tags.items
    },
    handleAddOption() {
      if (this.questionForm.questionStem.Options.length > 25) {
        this.$message.warning('选项个数已达到上限')
      } else {
        // var repeat = this.isRepeat(this.questionForm.questionStem.Options)

        // if (repeat === 1) {
        //   this.$message.warning('有重复的选项')
        //   return
        // } else if (repeat === 2) {
        //   this.$message.warning('请完善新选项')
        //   return
        // } else {
        this.questionForm.questionStem.Options.push({
          Order: this.questionForm.questionStem.Options.length + 1,
          Title: '选项' + (this.questionForm.questionStem.Options.length + 1),
          Images: null
        })
        // }
      }
    },
    handleDeleteOption(index, Order) {
      if (this.questionForm.questionStem.Options.length <= 2) {
        this.$message({ type: 'warning', message: '题目选项最少2条!' })
        return
      }
      this.questionForm.questionStem.Options.splice(index, 1)
      if (this.questionForm.questionType === 0) {
        if (this.singleAnswerModel === Order) {
          this.singleAnswerModel = null
        }
        this.questionForm.questionStem.Options.forEach(item => {
          if (item.Order > Order) {
            item.Order -= 1
          }
        })
        if (this.singleAnswerModel > Order) {
          this.singleAnswerModel = this.singleAnswerModel - 1
        }
      } else if (this.questionForm.questionType === 1) {
        for (var i = 0; i < this.multiAnswerModel.length; i++) {
          if (this.multiAnswerModel[i] === Order) {
            this.multiAnswerModel.splice(i, 1)
            break
          }
        }
        this.questionForm.questionStem.Options.forEach(item => {
          if (item.Order > Order) {
            item.Order -= 1
          }
        })
        this.multiAnswerModel.forEach((item, index) => {
          if (item > Order) {
            this.multiAnswerModel[index] = this.multiAnswerModel[index] - 1
          }
        })
      } else if (this.questionForm.questionType === 2) {
        return
      }
      // for (var i = 0; i < this.questionForm.answer.OptionAnswers.length; i++) {
      //   if (this.questionForm.answer.OptionAnswers[i].Order === Order) {
      //     this.questionForm.answer.OptionAnswers.splice(i, 1)
      //     break
      //   }
      // }
    },
    handleSingleAnswerChange(t) {
    },
    handleMultiAnswerChange(t) {
    },
    handleJudgeAnswerChange(t) {
    },
    // 上传成功组件回调
    handleResponse(url, fileForm) {
      if (!this.questionForm.questionStem.Title_Imgs) {
        this.questionForm.questionStem.Title_Imgs = []
      }
      // else if (Object.prototype.toString.call(this.questionForm.questionStem.Title_Imgs) === '[object String]') {
      //   this.questionForm.questionStem.Title_Imgs = this.questionForm.questionStem.Title_Imgs.split(',')
      // }

      this.questionForm.questionStem.Title_Imgs.push({ url: url, src: url, name: url })
    },
    handleOptionResponse(url, fileForm, index) {
      if (!this.questionForm.questionStem.Options[index].Images) {
        this.questionForm.questionStem.Options[index].Images = []
      }
      // else if (Object.prototype.toString.call(this.questionForm.questionStem.Options[index].Images) === '[object String]') {
      //   this.questionForm.questionStem.Options[index].Images = this.questionForm.questionStem.Options[index].Images.split(',')
      // }
      this.questionForm.questionStem.Options[index].Images.push({ url: url, src: url, name: url })
    },
    // 移除上传资源
    handleRemoveUploadImage(i, index, source) {
      source.splice(index, 1)
      // if (t === 0) {
      //   this.questionForm.questionStem.Title_Imgs.splice(i, 1)
      // } else if (t === 1) {
      //   this.questionForm.questionStem.Options[index].Images.splice(i, 1)
      // }
    },
    handleRemoveTitleUploadImage(index, source) {
      source.splice(index, 1)
    },
    handleRemoveUploadFile(file) {
      var index = this.questionForm.questionStem.Title_Imgs.indexOf(file)
      this.questionForm.questionStem.Title_Imgs.splice(index, 1)
    },
    isRepeat(arr) {
      var hash = {}
      for (var i in arr) {
        if (hash[arr[i].Title]) {
          return 1
        }
        // else if (arr[i].Title === '请输入选项') {
        //   return 2
        // }
        // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
        hash[arr[i].Title] = true
      }
      return 0
    },
    numSwitchChar(index) {
      switch (index) {
        case 1:
          return 'A'
        case 2:
          return 'B'
        case 3:
          return 'C'
        case 4:
          return 'D'
        case 5:
          return 'E'
        case 6:
          return 'F'
        case 7:
          return 'G'
        case 8:
          return 'H'
        case 9:
          return 'I'
        case 10:
          return 'J'
        case 11:
          return 'K'
        case 12:
          return 'L'
        case 13:
          return 'M'
        case 14:
          return 'N'
        case 15:
          return 'O'
        case 16:
          return 'P'
        case 17:
          return 'Q'
        case 18:
          return 'R'
        case 19:
          return 'S'
        case 20:
          return 'T'
        case 21:
          return 'U'
        case 22:
          return 'V'
        case 23:
          return 'W'
        case 24:
          return 'X'
        case 25:
          return 'Y'
        case 26:
          return 'Z'
        default:
          break
      }
    },
    handleClickItem() {
      this.$nextTick(() => {
        // 获取遮罩层dom
        const domImageMask = document.querySelector('.el-image-viewer__mask')
        if (!domImageMask) {
          return
        }
        domImageMask.addEventListener('click', () => {
          // 点击遮罩层时调用关闭按钮的 click 事件
          document.querySelector('.el-image-viewer__close').click()
        })
      })
    }
  }
}
</script>
<style scoped>
/* .el-radio-group ::v-deep .el-radio,
.el-checkbox-group ::v-deep .el-checkbox {
  margin-top: 10px;
  margin-bottom: 10px;
  display: inline-block;
} */
.question_filter ::v-deep .el-radio,
.question_filter ::v-deep .el-checkbox
{
  margin-top: 10px;
  margin-bottom: 10px;
  display: inline-block;
}
/* .el-checkbox ::v-deep .el-checkbox__label,.el-radio ::v-deep .el-radio__label {
  width: 90%;
} */
.el-radio ::v-deep .el-radio__inner {
  margin-bottom: -2px;
}
/* .option_images {
  display: block;
}
.image_item {
  position: relative;
  display: inline-block;
  width: 200px;
  height: 120px;
}
.option_image {
  display: inline-block;
  width: 200px;
  height: 120px;
  margin: 10px 10px 0 0;
  border: 1px solid #eee;
  position: relative;
}
.image_delete {
  position: absolute;
  right: -5px;
  top: -5px;
  z-index: 900;
} */
.question_filter .el-checkbox {
  display: inline-block;
    /* white-space: nowrap; */
    margin-top: 0;
    margin-bottom: 0;
    width: auto;
}
.question_options {
  margin-left: 8px;
}
.question_options .el-radio,.el-checkbox {
    display: block;
    width: 100%;
    text-overflow: ellipsis;
    white-space: normal;
    margin-bottom: 10px;
}
.question_options .el-checkbox {
  display: flex
}
.question_options ::v-deep .el-checkbox__inner {
  margin-top: 10px;
}
/* .question_img ::v-deep .el-image {
  max-height: 120px;
  border: 1px solid #eee;
  margin-right: 10px;
  display: inline-block;
}
.question_img ::v-deep .el-image .el-image__inner {
  max-height: 120px;
  width: auto;
}
.question_img ::v-deep .el-icon-circle-close {
  color: white;
} */
/* .option_image ::v-deep .el-icon-circle-close {
  color: white;
} */

/* 选项item */
.option_title {
  display: inline-block;width: 98px;font-size: 14px; color: #606266; text-align: right;font-weight: 700;padding-right: 12px
}
.option_form_item {
  display: inline-block;
}
.option_form_item ::v-deep .el-form-item__content {
  margin-left: 0 !important;
}
</style>

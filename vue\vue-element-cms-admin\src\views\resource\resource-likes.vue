<template>
  <div class="app-container">
    <el-card shadow="never">
      <div slot="header">
        <span>资源评分</span>
      </div>
      <div class="header_flex_box">
        <el-select v-model="listQuery.ResourceId" size="small" clearable>
          <el-option v-for="item in resourceList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list" size="small" @sort-change="sortChange">
        <el-table-column label="资源名称" prop="resourceName" sortable="resourceName" width="250" />
        <!-- <el-table-column label="内容" prop="content" sortable="content" show-overflow-tooltip /> -->
        <el-table-column label="评分" prop="likes" sortable="likes">
          <template slot-scope="{row}">
            <el-rate v-model="row.likes" disabled :allow-half="true" show-likes text-color="#ff9900" :likes-template="row.likes + '分'" />
          </template>
        </el-table-column>
        <el-table-column label="用户" prop="author" sortable="author" width="150" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="110">
          <template slot-scope="{ row }">
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleResourceLikesDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page" :limit.sync="listQuery.MaxResultCount" @pagination="getResourceLikesList" />
    </el-card>
  </div>
</template>
<script>
import {
  resourceList,
  addResourceLikes,
  resourceLikesList,
  deletesResourceLikes
} from '@/api/resource'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'ResourceLikes',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        ResourceId: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      resourceList: []

    }
  },
  created() {
    // this.getResourceLikesList()
    // this.getResourceList()
  },
  methods: {

    handleResourceLikesDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesResourceLikes(row.id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getResourceLikesList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },

    handleRefreshList() {
      this.listQuery.page = 1
      this.getResourceLikesList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceLikesList()
    },
    getResourceLikesList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceLikesList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    addResourceLikes() {
      var data = {
        resourceId: '5c35d565-3e2a-92d5-5171-3a02b1f97228',
        resourceName: '测试资源',
        content: '资源挺好测试',
        likes: 10
      }
      addResourceLikes(data).then((res) => {
        this.resourceList = res.items
      })
        .catch(() => {
          this.$message.error('获取资源列表失败')
        })
    },
    getResourceList() {
      var data = {
        Filter: '',
        Status: '',
        ResourceCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      resourceList(data)
        .then((res) => {
          this.resourceList = res.items
        })
        .catch(() => {
          this.$message.error('获取资源列表失败')
        })
    }
  }
}
</script>

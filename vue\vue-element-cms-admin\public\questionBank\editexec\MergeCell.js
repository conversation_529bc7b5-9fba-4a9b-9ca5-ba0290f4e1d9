﻿/**
* 操作表格 合并单元格 行
*/
(function ($) {
    // 看过jquery源码就可以发现$.fn就是$.prototype, 只是为了兼容早期版本的插件
    // 才保留了jQuery.prototype这个形式
    $.fn.mergeCell = function (options) {
        return this.each(function () {
            var cols = options.cols;
            for (var i = cols.length - 1; cols[i] != undefined; i--) {
                // fixbug console调试
                // console.debug(cols[i]);
                mergeCell($(this), cols[i]);
            }
            dispose($(this));
        });
    };
    // 如果对javascript的closure和scope概念比较清楚, 这是个插件内部使用的private方法
    function mergeCell($table, colIndex) {

        $table.data('col-content', ''); // 存放单元格内容
        $table.data('col-rowspan', 1);  // 存放计算的rowspan值  默认为1
        $table.data('col-td', $());     // 存放发现的第一个与前一行比较结果不同td(jQuery封装过的), 默认一个"空"的jquery对象
        $table.data('trNum', $('tbody tr', $table).length); // 要处理表格的总行数, 用于最后一行做特殊处理时进行判断之用

        // 我们对每一行数据进行"扫面"处理 关键是定位col-td, 和其对应的rowspan
        $('tbody tr', $table).each(function (index) {
            // td:eq中的colIndex即列索引
            var $td = $('td:eq(' + colIndex + ')', this);

            // 取出单元格的当前内容
            var currentContent = $td.html();

            // 第一次时走此分支
            if ($table.data('col-content') == '') {

                $table.data('col-content', currentContent);
                $table.data('col-td', $td);

            } else {
                // 上一行与当前行内容相同
                if ($table.data('col-content') == currentContent) {
                    // 上一行与当前行内容相同则col-rowspan累加, 保存新值
                    var rowspan = $table.data('col-rowspan') + 1;
                    $table.data('col-rowspan', rowspan);
                    // 值得注意的是  如果用了$td.remove()就会对其他列的处理造成影响
                    $td.hide();

                    // 最后一行的情况比较特殊一点
                    // 比如最后2行 td中的内容是一样的, 那么到最后一行就应该把此时的col-td里保存的td设置rowspan
                    if (++index == $table.data('trNum'))
                        $table.data('col-td').attr('rowspan', $table.data('col-rowspan'));

                } else { // 上一行与当前行内容不同
                    // col-rowspan默认为1, 如果统计出的col-rowspan没有变化, 不处理
                    if ($table.data('col-rowspan') != 1) {
                        $table.data('col-td').attr('rowspan', $table.data('col-rowspan'));
                    }
                    // 保存第一次出现不同内容的td, 和其内容, 重置col-rowspan
                    $table.data('col-td', $td);
                    $table.data('col-content', $td.html());
                    $table.data('col-rowspan', 1);
                }
            }
        });
    }

    // 同样是个private函数  清理内存之用
    function dispose($table) {
        $table.removeData();
    }
})(jQuery);



/**
 * desc : 合并指定表格（表格id为table_id）指定列（列数为table_colnum）的相同文本的相邻单元格
 * @table_id 表格id : 为需要进行合并单元格的表格的id。如在HTMl中指定表格 id="data" ，此参数应为 #data
 * @table_colnum : 为需要合并单元格的所在列.参考jQuery中nth-child的参数.若为数字，从最左边第一列为1开始算起;"even" 表示偶数列;"odd" 表示奇数列; "3n+1" 表示的列数为1、4、7、......
 * @table_minrow ? : 可选的,表示要合并列的行数最小的列,省略表示从第0行开始 (闭区间)
 * @table_maxrow ? : 可选的,表示要合并列的行数最大的列,省略表示最大行列数为表格最后一行 (开区间)
 */

function table_rowspan(table_id, table_colnum) {
    if (table_colnum == "even") {
        table_colnum = "2n";
    }
    else if (table_colnum == "odd") {
        table_colnum = "2n+1";
    }
    else {
        table_colnum = "" + table_colnum;
    }
    var cols = [];
    var all_row_num = $(table_id + " tr td:nth-child(1)").length;
    var all_col_num = $(table_id + " tr:nth-child(1)").children().length;
    if (table_colnum.indexOf("n") == -1) {
        cols[0] = table_colnum;
    }
    else {
        var n = 0;
        var a = table_colnum.substring(0, table_colnum.indexOf("n"));
        var b = table_colnum.substring(table_colnum.indexOf("n") + 1);
        a = a ? parseInt(a) : 1;
        b = b ? parseInt(b) : 0;
        while (a * n + b <= all_col_num) {
            cols[n] = a * n + b;
            n++;
        }
    }
    var table_minrow = arguments[2] ? arguments[2] : 0;
    var table_maxrow = arguments[3] ? arguments[3] : all_row_num + 1;
    var table_firsttd = "";
    var table_currenttd = "";
    var table_SpanNum = 0;
    for (var j = 0; j < cols.length; j++) {
        $(table_id + " tr td:nth-child(" + cols[j] + ")").slice(table_minrow, table_maxrow).each(function (i) {
            var table_col_obj = $(this);
            if (table_col_obj.html() != " ") {
                if (i == 0) {
                    table_firsttd = $(this);
                    table_SpanNum = 1;
                }
                else {
                    table_currenttd = $(this);
                    if (table_firsttd.text() == table_currenttd.text() && table_firsttd.text() != "" && table_currenttd.text()!="") {
                        table_SpanNum++;
                        table_currenttd.hide(); //remove();
                        table_firsttd.attr("rowSpan", table_SpanNum);
                    } else {
                        table_firsttd = $(this);
                        table_SpanNum = 1;
                    }
                }
            }
        });
    }
}
/**
 * desc : 合并指定表格（表格id为table_id）指定行（行数为table_rownum）的相同文本的相邻单元格
 * @table_id 表格id : 为需要进行合并单元格的表格的id。如在HTMl中指定表格 id="data" ，此参数应为 #data
 * @table_rownum : 为需要合并单元格的所在行.参考jQuery中nth-child的参数.若为数字，从最左边第一列为1开始算起;"even" 表示偶数行;"odd" 表示奇数行; "3n+1" 表示的行数为1、4、7、......
 * @table_mincolnum ? : 可选的,表示要合并行中的最小列,省略表示从第0列开始(闭区间)
 * @table_maxcolnum ? : 可选的,表示要合并行中的最大列,省略表示表格的最大列数(开区间)
 */
function table_colspan(table_id, table_rownum) {
    var table_mincolnum = arguments[2] ? arguments[2] : 0;
    var table_maxcolnum;
    var table_firsttd = "";
    var table_currenttd = "";
    var table_SpanNum = 0;
    $(table_id + " tr:nth-child(" + table_rownum + ")").each(function (i) {
        table_row_obj = $(this).children();
        table_maxcolnum = arguments[3] ? arguments[3] : table_row_obj.length;
        table_row_obj.slice(table_mincolnum, table_maxcolnum).each(function (i) {
            if (i == 0) {
                table_firsttd = $(this);
                table_SpanNum = 1;
            } else if ((table_maxcolnum > 0) && (i > table_maxcolnum)) {
                return "";
            } else {
                table_currenttd = $(this);
                if (table_firsttd.text() == table_currenttd.text() && table_firsttd.text() != "" && table_currenttd.text() != "") {
                    table_SpanNum++;
                    if (table_currenttd.is(":visible")) {
                        table_firsttd.width(parseInt(table_firsttd.width()) + parseInt(table_currenttd.width()));
                    }
                    table_currenttd.hide(); //remove();
                    table_firsttd.attr("colSpan", table_SpanNum);
                } else {
                    table_firsttd = $(this);
                    table_SpanNum = 1;
                }
            }
        });
    });
}


//在div光标后插入字符
function insertHtmlAtCaret(html) {
    var sel, range;
    if (window.getSelection) {
        // IE9 and non-IE
        sel = window.getSelection();
        if (sel.getRangeAt && sel.rangeCount) {
            range = sel.getRangeAt(0);
            range.deleteContents();
            // Range.createContextualFragment() would be useful here but is
            // non-standard and not supported in all browsers (IE9, for one)
            var el = document.createElement("div");
            el.innerHTML = html;
            var frag = document.createDocumentFragment(), node, lastNode;
            while ((node = el.firstChild)) {
                lastNode = frag.appendChild(node);
            }
            range.insertNode(frag);
            // Preserve the selection
            if (lastNode) {
                range = range.cloneRange();
                range.setStartAfter(lastNode);
                range.collapse(true);
                sel.removeAllRanges();
                sel.addRange(range);
            }
        }
    } else if (document.selection && document.selection.type != "Control") {
        // IE < 9
        document.selection.createRange().pasteHTML(html);
    }
}


jQuery.fn.RevertTable = function () {
    $("tr", this).each(function (trindex, tritem) {
        $(tritem).find("td").each(function (tdindex, tditem) {
            var rowspanCount = $(tditem).attr("rowspan");
            var colspanCount = $(tditem).attr("colspan");
            var value = $(tditem).text();
            var newtd = "<td>" + value + "</td>";
            if (rowspanCount > 1) {
                var parent = $(tditem).parent("tr")[0];
                while (rowspanCount-- > 1) {
                    $($(parent).next()[0].children[tdindex-1]).after(newtd);
                    parent = $(parent).next();
                }
                $(tditem).attr("rowspan", 1);
            }
            if (colspanCount > 1) {
                while (colspanCount-- > 1) {
                    $(tditem).after(newtd);
                }
                $(tditem).attr("colspan", 1);
            }
        });
    });
}

jQuery.fn.rowspan = function (colIdx) {
    return this.each(function () {
        var that;
        $('tr', this).each(function (row) {
            $('td:eq(' + colIdx + ')', this).filter(':visible').each(function (col) {
                if (that != null && $(this).html() == $(that).html()) {
                    rowspan = $(that).attr("rowSpan");
                    if (rowspan == undefined) {
                        $(that).attr("rowSpan", 1);
                        rowspan = $(that).attr("rowSpan");
                    }
                    rowspan = Number(rowspan) + 1;
                    $(that).attr("rowSpan", rowspan);
                    $(this).hide();
                } else {
                    that = this;
                }
            });
        });
    });
}
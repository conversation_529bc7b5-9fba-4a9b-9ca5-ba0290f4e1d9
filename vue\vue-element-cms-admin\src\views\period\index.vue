<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-row>
        <el-radio-group v-model="filterType" size="small" @change="handleFilterTypeChange">
          <el-radio-button :label="false">按部门</el-radio-button>
          <el-radio-button :label="true">按班级</el-radio-button>
        </el-radio-group>
        <el-cascader
          v-show="!filterType"
          v-model="listQuery.OuId"
          filterable
          :options="orgList"
          :props="cascaderProps"
          clearable
          size="small"
          placeholder="请选择..."
          style="margin: 0 10px"
          @change="handleFilterChange"
        />
        <el-cascader
          v-show="filterType"
          v-model="listQuery.ClassId"
          filterable
          :options="nodeList"
          :props="classProps"
          clearable
          size="small"
          placeholder="请选择..."
          style="margin: 0 10px"
          @change="handleFilterChange"
        />
        <el-date-picker
          v-model="listQuery.StartTimeS"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          placeholder="选择开始时间"
          @change="handleRefreshList"
        />
        ~
        <el-date-picker
          v-model="listQuery.EndTimeS"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          placeholder="选择开始时间"
          @change="handleRefreshList"
        />

        <el-date-picker
          v-model="listQuery.StartTimeE"
          size="small"
          style="margin-left: 10px"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          placeholder="选择结束时间"
          @change="handleRefreshList"
        />
        ~
        <el-date-picker
          v-model="listQuery.EndTimeE"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          placeholder="选择结束时间"
          @change="handleRefreshList"
        />
      </el-row>
      <el-row style="margin: 20px 0;">
        <el-select v-model="listQuery.FaceCreditHour" placeholder="请选择" size="small">
          <el-option
            label="全部"
            :value="false"
          />
          <el-option
            :key="true"
            label="面授学时大于0"
            :value="true"
          />

        </el-select>
        <el-select v-model="listQuery.NetCreditHour" placeholder="请选择" size="small">
          <el-option
            label="全部"
            :value="false"
          />
          <el-option
            label="网络学时大于0"
            :value="true"
          />

        </el-select>

        <el-input v-model="listQuery.Filter" size="small" class="small_input" style="margin-left: 10px" clearable placeholder="输入用户名搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button size="small" round type="primary" icon="el-icon-download" @click="handleExport">导出</el-button>
        <el-button size="small" round type="primary" icon="el-icon-download" @click="handleExportUsersDetail">导出学时明细</el-button>

      </el-row>
      <el-table v-loading="listLoading" :data="list" size="small" @sort-change="sortChange">
        <el-table-column label="用户名" prop="userName" width="160px">
          <template slot-scope="{ row }">
            <span>{{ row.user.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="name" width="160px">
          <template slot-scope="{row}">
            <span>{{ row.user.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门" prop="extraProperties.OUName" min-width="150" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span>{{ cacheFindParent(row.user.extraProperties.OUId).join('/') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="岗位" prop="extraProperties.Position" width="180" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span>{{ row.user.extraProperties.Position }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="extraProperties.Remarks" min-width="200" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span>{{ row.user.extraProperties.Remarks }}</span>
          </template>
        </el-table-column>

        <el-table-column label="手机号码" prop="phoneNumber" width="120px">
          <template slot-scope="{row}">
            <span>{{ row.user.phoneNumber }}</span>
          </template>
        </el-table-column>
        <el-table-column label="角色" prop="role" width="120px">
          <template slot-scope="{row}">
            <span>{{ roleTransformation(row.user.extraProperties.Roles) }}</span>
            <!-- <span>{{ scope.row.extraProperties.Roles=='student'?'学生':(
                  scope.row.extraProperties.Roles=='teacher'?'老师':(
                    scope.row.extraProperties.Roles=='admin'?'管理员':scope.row.extraProperties.Roles
                  )
                ) }}</span> -->
          </template>
        </el-table-column>
        <el-table-column label="面授学时" prop="faceCreditHour" sortable="faceCreditHour" width="100">
          <template slot-scope="{row}">
            <span class="link-type" @click="handleViewCreditHourDetail(0, row)">{{
              parseFloat(row.faceCreditHour.toFixed(2))
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="网络学时" prop="netCreditHour" sortable="netCreditHour" width="100">
          <template slot-scope="{row}">
            <span class="link-type" @click="handleViewCreditHourDetail(1, row)">{{
              parseFloat(row.netCreditHour.toFixed(2))
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getPeriodList"
      />
    </el-card>
    <el-dialog v-if="recordDialog" title="详情" :visible.sync="recordDialog" top="5vh" width="1000px">
      <el-table v-loading="recordListLoading" :data="recordList">
        <el-table-column label="类型" prop="recordType" width="120">
          <template slot-scope="{row}">
            <span v-if="row.recordType === 0">线上面授课程</span>
            <span v-else-if="row.recordType === 1">线上网络课程</span>
            <!-- <span v-else-if="row.recordType === 2">线下直播</span> -->
            <span v-else-if="row.recordType === 3&&row.creditHourType===0">线下面授课程</span>
            <span v-else-if="row.recordType === 3&&row.creditHourType===1">线下网络课程</span>
            <span v-else>其他</span>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="recordTitle" show-overflow-tooltip />
        <el-table-column label="课时" prop="classHour" width="120" />
        <el-table-column label="开始时间" prop="startTime" width="160">
          <template slot-scope="{ row }">
            {{ row.startTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endTime" width="160">
          <template slot-scope="{ row }">
            {{ row.endTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="获得学时" prop="creditHour" width="120" />
      </el-table>
      <pagination
        v-show="recordListQuery.totalCount > 0"
        :total="recordListQuery.totalCount"
        :page.sync="recordListQuery.page"
        :limit.sync="recordListQuery.MaxResultCount"
        @pagination="getRecordDetailList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="recordDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import { formatDateTime } from '@/utils/filters'
import { loadNodes, classesData, periodList, creditHourDetailList, creditHourUsersDetailList, orgsData } from '@/api/user'
export default {
  name: 'Period',
  directives: { permission },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        ClassId: '',
        OuId: '',
        FaceCreditHour: false,
        NetCreditHour: false,
        Filter: '',
        StartTimeS: null,
        EndTimeS: null,
        StartTimeE: null,
        EndTimeE: null,
        page: 1,
        totalCount: 0,
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: ''
      },
      allOrgs: null,

      exportLoading: false,
      fileTemp: null,

      filterType: false,
      orgList: [],
      nodeList: [],
      cascaderProps: {
        label: 'displayName',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },

      classProps: {
        label: 'name',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      recordDialog: false,
      recordListLoading: false,
      recordList: [],
      recordListQuery: {
        UserId: '',
        CreditHourType: 0,
        Filter: '',
        StartTimeS: null,
        EndTimeS: null,
        StartTimeE: null,
        EndTimeE: null,
        page: 1,
        totalCount: 0,
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: ''
      },

      map: null,
      cacheDate: new Map()
    }
  },
  created() {
    this.initClassList()
    // this.loadClass()
  },
  methods: {
    periodList(args) {
      return periodList(args)
    },
    handleViewCreditHourDetail(t, row) {
      this.recordList = []
      this.recordListQuery.page = 1
      this.recordListQuery.UserId = row.user.id
      this.recordListQuery.CreditHourType = t
      this.recordListQuery.StartTimeS = this.listQuery.StartTimeS
      this.recordListQuery.EndTimeS = this.listQuery.EndTimeS
      this.recordListQuery.StartTimeE = this.listQuery.StartTimeE
      this.recordListQuery.EndTimeE = this.listQuery.EndTimeE
      this.getRecordDetailList()
      this.recordDialog = true
    },
    handleFilterTypeChange(val) {
      if (val) {
        this.listQuery.OuId = ''
      } else {
        this.listQuery.ClassId = ''
      }
      this.handleRefreshList()
    },
    handleFilterChange(val) {
      if (this.filterType) {
        this.listQuery.OuId = ''
      } else {
        this.listQuery.ClassId = ''
      }
      this.handleRefreshList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getPeriodList()
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j
        ) => {
          if (j == 'recordType') {
            switch (v[j]) {
              case 0:
                return '线上面授课程'
              case 1:
                return '线上网络课程'
              case 2:
                return '线下直播'
              case 3:
                if (v['creditHourType'] === 0) { return '线下面授课程' } else { return '线下网络课程' }
              default:
                return '其他'
            }
          }
          if (j == 'startTime' || j == 'endTime') return formatDateTime(v[j])

          return v[j]
        })
      )
    },
    getPeriodList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      periodList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getPeriodList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getPeriodList()
    },
    getRecordDetailList() {
      this.recordListLoading = true
      this.recordListQuery.SkipCount = (this.recordListQuery.page - 1) * this.recordListQuery.MaxResultCount
      creditHourDetailList(this.recordListQuery).then(res => {
        this.recordList = res.items
        this.recordListQuery.totalCount = res.totalCount
        this.recordListLoading = false
      })
    },
    // 初始化专业班级
    async initClassList() {
      this.nodeList = []
      this.orgList = []
      this.allOrgs = await orgsData()
      this.orgList = this.deleteChildren(this.allOrgs.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
      await classesData().then(res => {
        this.nodeList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
          if (item.parentId === null) {
            total.push({
              ...item,
              children: list.filter((f) => f.parentId === item.id)
            })
          } else {
            item.children = list.filter((f) => f.parentId === item.id)
          }
          return total
        }, []))
      })

      this.loadClass()
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    roleTransformation(val) {
      if (Object.prototype.toString.call(val) === '[object String]') {
        var rolesList = val.split(',')
        var roleName = ''
        rolesList.forEach(item => {
          if (item === 'student') {
            roleName += '学生' + ','
          } else if (item === 'teacher') {
            roleName += '老师' + ','
          } else if (item === 'admin') {
            roleName += '管理员' + ','
          } else {
            roleName += item + ','
          }
        })
        return this.rtrim(roleName, ',')
      } else {
        return val
      }
    },
    rtrim(val, char, type) {
      if (char) {
        if (type === 'left') {
          return val.replace(new RegExp('^\\' + char + '+', 'g'), '')
        } else if (type === 'right') {
          return val.replace(new RegExp('\\' + char + '+$', 'g'), '')
        }
        return val.replace(new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'), '')
      }
      return val.replace(/^\s+|\s+$/g, '')
    },
    async handleExport() {
      var count2 = 50000
      if (this.listQuery.totalCount > count2) {
        this.$message.warning('数量超过' + count2 + ',不能导出')
        return
      }

      var totalList = []
      const count = 500
      const times = this.listQuery.totalCount % count === 0 ? this.listQuery.totalCount / count : (Math.floor(this.listQuery.totalCount / count) + 1)
      for (let i = 0; i < times; i++) {
        var data = {
          ClassId: this.listQuery.ClassId,
          OuId: this.listQuery.OuId,
          FaceCreditHour: this.listQuery.FaceCreditHour,
          NetCreditHour: this.listQuery.NetCreditHour,
          Filter: this.listQuery.Filter,
          StartTimeS: this.listQuery.StartTimeS,
          EndTimeS: this.listQuery.EndTimeS,
          StartTimeE: this.listQuery.StartTimeE,
          EndTimeE: this.listQuery.EndTimeE,
          Sorting: this.listQuery.Sorting,
          SkipCount: i * count,
          MaxResultCount: count
        }
        const res = await periodList(data)
        totalList = totalList.concat(res.items)
      }
      totalList.forEach(item => {
        var orgs = this.cacheFindParent(item.user.extraProperties.OUId)
        item.className1 = orgs.length ? orgs[0] : ''
        item.className2 = orgs.length > 1 ? orgs[1] : ''
        item.className3 = orgs.length > 2 ? orgs[2] : ''
        item.userName = item.user.userName
        item.name = item.user.name
        item.position = item.user.extraProperties.Position
        item.remarks = item.user.extraProperties.Remarks
        item.phoneNumber = item.user.phoneNumber
        item.roles = this.roleTransformation(item.user.extraProperties.Roles)
      })

      import('@/vendor/Export2Excel').then((excel) => {
        const filterVal = ['userName', 'name', 'className1', 'className2', 'className3', 'position', 'remarks', 'phoneNumber', 'roles', 'faceCreditHour', 'netCreditHour']
        const header = ['用户名', '姓名', '一级部门', '二级部门', '三级部门', '岗位', '备注', '手机号码', '角色', '面授学时', '网络学时']
        const data = this.formatJson(filterVal, totalList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: header,
          data,
          filename: '学时导出_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    cacheFindParent(id) {
      if (this.cacheDate.get(id)) {
        return this.cacheDate.get(id)
      }
      return this.findParent(id)
    },
    async loadClass() {
      // const res = await orgsData()
      // this.orgDatas = res.items
      this.map = new Map()
      this.allOrgs.items.forEach(item => {
        this.map.set(item.id, item)
      })
    },
    findParent(id) {
      const result = []
      if (!this.map) {
        return []
      }
      const d = this.map.get(id)
      if (d) {
        result.unshift(d?.displayName)
      }
      if (d?.parentId) {
        let next = this.map.get(d.parentId)
        while (next) {
          result.unshift(next.displayName)
          next = this.map.get(next.parentId)
        }
      }
      this.cacheDate.set(id, result)
      return result
    },
    async handleExportUsersDetail() {
      var query = {
        ClassId: this.listQuery.ClassId,
        OuId: this.listQuery.OuId,
        Filter: this.listQuery.Filter,
        StartTimeS: this.listQuery.StartTimeS,
        EndTimeS: this.listQuery.EndTimeS,
        StartTimeE: this.listQuery.StartTimeE,
        EndTimeE: this.listQuery.EndTimeE,
        Sorting: 'UserId',
        SkipCount: 0,
        MaxResultCount: 1
      }

      if ((query.ClassId == '' || query.ClassId == null) && (query.OuId == '' || query.OuId == null)) {
        query.Sorting = ''
      }

      var temp = await creditHourUsersDetailList(query)

      if (temp.totalCount > 100000) {
        this.$message.warning('数量超过100000,不能导出')
        return
      }

      var totalList = []
      const count = 500
      const times = temp.totalCount % count === 0 ? temp.totalCount / count : (Math.floor(temp.totalCount / count) + 1)
      for (let i = 0; i < times; i++) {
        var data = {
          ClassId: this.listQuery.ClassId,
          OuId: this.listQuery.OuId,
          Filter: this.listQuery.Filter,
          StartTimeS: this.listQuery.StartTimeS,
          EndTimeS: this.listQuery.EndTimeS,
          StartTimeE: this.listQuery.StartTimeE,
          EndTimeE: this.listQuery.EndTimeE,
          Sorting: 'UserId',
          SkipCount: i * count,
          MaxResultCount: count
        }
        if ((data.ClassId == '' || data.ClassId == null) && (data.OuId == '' || data.OuId == null)) {
          data.Sorting = ''
        }
        const res = await creditHourUsersDetailList(data)
        totalList = totalList.concat(res.items)
      }
      totalList.forEach(item => {
        item.className1 = this.cacheFindParent(item.user.extraProperties.OUId).length ? this.cacheFindParent(item.user.extraProperties.OUId)[0] : ''
        item.className2 = this.cacheFindParent(item.user.extraProperties.OUId).length > 1 ? this.cacheFindParent(item.user.extraProperties.OUId)[1] : ''
        item.className3 = this.cacheFindParent(item.user.extraProperties.OUId).length > 2 ? this.cacheFindParent(item.user.extraProperties.OUId)[2] : ''
        item.userName = item.user.userName
        item.name = item.user.name
        item.position = item.user.extraProperties.Position
        item.remarks = item.user.extraProperties.Remarks
        item.phoneNumber = item.user.phoneNumber
        item.roles = this.roleTransformation(item.user.extraProperties.Roles)
      })

      import('@/vendor/Export2Excel').then((excel) => {
        const filterVal = ['userName', 'name', 'className1', 'className2', 'className3', 'position', 'remarks', 'phoneNumber', 'roles', 'recordTitle', 'recordType', 'classHour', 'startTime', 'endTime', 'creditHour']
        const header = ['用户名', '姓名', '一级部门', '二级部门', '三级部门', '岗位', '备注', '手机号码', '角色', '课程名称', '课程类型', '课时', '开始时间', '结束时间', '学时']
        const data = this.formatJson(filterVal, totalList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: header,
          data,
          filename: '学时详情导出_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    }
  }
}
</script>

!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).baidubce=t()}(function(){return function t(e,n,r){function i(a,s){if(!n[a]){if(!e[a]){var c="function"==typeof require&&require;if(!s&&c)return c(a,!0);if(o)return o(a,!0);throw c=Error("Cannot find module '"+a+"'"),c.code="MODULE_NOT_FOUND",c}c=n[a]={exports:{}},e[a][0].call(c.exports,function(t){var n=e[a][1][t];return i(n?n:t)},c,c.exports,t,e,n,r)}return n[a].exports}for(var o="function"==typeof require&&require,a=0;a<r.length;a++)i(r[a]);return i}({1:[function(t,e,n){n=t(166);var r=t(164);t=t(165),e.exports={bos:{Uploader:r},utils:t,sdk:n}},{164:164,165:165,166:166}],2:[function(t,e,n){!function(){function t(t){this.message=t}var e="undefined"!=typeof n?n:this;t.prototype=Error(),t.prototype.name="InvalidCharacterError",e.btoa||(e.btoa=function(e){for(var n,r,i=0,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a="";e.charAt(0|i)||(o="=",i%1);a+=o.charAt(63&n>>8-i%1*8)){if(r=e.charCodeAt(i+=.75),r>255)throw new t("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");n=n<<8|r}return a}),e.atob||(e.atob=function(e){if(e=e.replace(/=+$/,""),1==e.length%4)throw new t("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,r,i=0,o=0,a="";r=e.charAt(o++);~r&&(n=i%4?64*n+r:r,i++%4)?a+=String.fromCharCode(255&n>>(-2*i&6)):0)r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r);return a})}()},{}],3:[function(t,e,n){n.bignum=t(19),n.define=t(4).define,n.base=t(6),n.constants=t(10),n.decoders=t(12),n.encoders=t(15)},{10:10,12:12,15:15,19:19,4:4,6:6}],4:[function(t,e,n){function r(t,e){this.name=t,this.body=e,this.decoders={},this.encoders={}}var i=t(3),o=t(101);n.define=function(t,e){return new r(t,e)},r.prototype._createNamed=function(e){var n;try{n=t(151).runInThisContext("(function "+this.name+"(entity) {\n  this._initNamed(entity);\n})")}catch(r){n=function(t){this._initNamed(t)}}return o(n,e),n.prototype._initNamed=function(t){e.call(this,t)},new n(this)},r.prototype._getDecoder=function(t){return this.decoders.hasOwnProperty(t)||(this.decoders[t]=this._createNamed(i.decoders[t])),this.decoders[t]},r.prototype.decode=function(t,e,n){return this._getDecoder(e).decode(t,n)},r.prototype._getEncoder=function(t){return this.encoders.hasOwnProperty(t)||(this.encoders[t]=this._createNamed(i.encoders[t])),this.encoders[t]},r.prototype.encode=function(t,e,n){return this._getEncoder(e).encode(t,n)}},{101:101,151:151,3:3}],5:[function(t,e,n){function r(t,e){o.call(this,e),a.isBuffer(t)?(this.base=t,this.offset=0,this.length=t.length):this.error("Input not Buffer")}function i(t,e){if(Array.isArray(t))this.length=0,this.value=t.map(function(t){return t instanceof i||(t=new i(t,e)),this.length+=t.length,t},this);else if("number"==typeof t){if(!(t>=0&&255>=t))return e.error("non-byte EncoderBuffer value");this.value=t,this.length=1}else if("string"==typeof t)this.value=t,this.length=a.byteLength(t);else{if(!a.isBuffer(t))return e.error("Unsupported type: "+typeof t);this.value=t,this.length=t.length}}e=t(101);var o=t(6).Reporter,a=t(48).Buffer;e(r,o),n.DecoderBuffer=r,r.prototype.save=function(){return{offset:this.offset,reporter:o.prototype.save.call(this)}},r.prototype.restore=function(t){var e=new r(this.base);return e.offset=t.offset,e.length=this.offset,this.offset=t.offset,o.prototype.restore.call(this,t.reporter),e},r.prototype.isEmpty=function(){return this.offset===this.length},r.prototype.readUInt8=function(t){return this.offset+1<=this.length?this.base.readUInt8(this.offset++,!0):this.error(t||"DecoderBuffer overrun")},r.prototype.skip=function(t,e){if(!(this.offset+t<=this.length))return this.error(e||"DecoderBuffer overrun");var n=new r(this.base);return n._reporterState=this._reporterState,n.offset=this.offset,n.length=this.offset+t,this.offset+=t,n},r.prototype.raw=function(t){return this.base.slice(t?t.offset:this.offset,this.length)},n.EncoderBuffer=i,i.prototype.join=function(t,e){return t||(t=new a(this.length)),e||(e=0),0===this.length?t:(Array.isArray(this.value)?this.value.forEach(function(n){n.join(t,e),e+=n.length}):("number"==typeof this.value?t[e]=this.value:"string"==typeof this.value?t.write(this.value,e):a.isBuffer(this.value)&&this.value.copy(t,e),e+=this.length),t)}},{101:101,48:48,6:6}],6:[function(t,e,n){n.Reporter=t(8).Reporter,n.DecoderBuffer=t(5).DecoderBuffer,n.EncoderBuffer=t(5).EncoderBuffer,n.Node=t(7)},{5:5,7:7,8:8}],7:[function(t,e,n){function r(t,e){var n={};this._baseState=n,n.enc=t,n.parent=e||null,n.children=null,n.tag=null,n.args=null,n.reverseArgs=null,n.choice=null,n.optional=!1,n.any=!1,n.obj=!1,n.use=null,n.useDecoder=null,n.key=null,n["default"]=null,n.explicit=null,n.implicit=null,n.contains=null,n.parent||(n.children=[],this._wrap())}var i=t(6).Reporter,o=t(6).EncoderBuffer,a=t(6).DecoderBuffer,s=t(105);t="seq seqof set setof objid bool gentime utctime null_ enum int bitstr bmpstr charstr genstr graphstr ia5str iso646str numstr octstr printstr t61str unistr utf8str videostr".split(" ");var c="key obj use optional explicit implicit def choice any contains".split(" ").concat(t);e.exports=r;var f="enc parent children tag args reverseArgs choice optional any obj use alteredUse key default explicit implicit".split(" ");r.prototype.clone=function(){var t=this._baseState,e={};f.forEach(function(n){e[n]=t[n]});var n=new this.constructor(e.parent);return n._baseState=e,n},r.prototype._wrap=function(){var t=this._baseState;c.forEach(function(e){this[e]=function(){var n=new this.constructor(this);return t.children.push(n),n[e].apply(n,arguments)}},this)},r.prototype._init=function(t){var e=this._baseState;s(null===e.parent),t.call(this),e.children=e.children.filter(function(t){return t._baseState.parent===this},this),s.equal(e.children.length,1,"Root node can have only one child")},r.prototype._useArgs=function(t){var e=this._baseState,n=t.filter(function(t){return t instanceof this.constructor},this);t=t.filter(function(t){return!(t instanceof this.constructor)},this),0!==n.length&&(s(null===e.children),e.children=n,n.forEach(function(t){t._baseState.parent=this},this)),0!==t.length&&(s(null===e.args),e.args=t,e.reverseArgs=t.map(function(t){if("object"!=typeof t||t.constructor!==Object)return t;var e={};return Object.keys(t).forEach(function(n){n==(0|n)&&(n|=0),e[t[n]]=n}),e}))},"_peekTag _decodeTag _use _decodeStr _decodeObjid _decodeTime _decodeNull _decodeInt _decodeBool _decodeList _encodeComposite _encodeStr _encodeObjid _encodeTime _encodeNull _encodeInt _encodeBool".split(" ").forEach(function(t){r.prototype[t]=function(){throw Error(t+" not implemented for encoding: "+this._baseState.enc)}}),t.forEach(function(t){r.prototype[t]=function(){var e=this._baseState,n=Array.prototype.slice.call(arguments);return s(null===e.tag),e.tag=t,this._useArgs(n),this}}),r.prototype.use=function(t){var e=this._baseState;return s(null===e.use),e.use=t,this},r.prototype.optional=function(){return this._baseState.optional=!0,this},r.prototype.def=function(t){var e=this._baseState;return s(null===e["default"]),e["default"]=t,e.optional=!0,this},r.prototype.explicit=function(t){var e=this._baseState;return s(null===e.explicit&&null===e.implicit),e.explicit=t,this},r.prototype.implicit=function(t){var e=this._baseState;return s(null===e.explicit&&null===e.implicit),e.implicit=t,this},r.prototype.obj=function(){var t=this._baseState,e=Array.prototype.slice.call(arguments);return t.obj=!0,0!==e.length&&this._useArgs(e),this},r.prototype.key=function(t){var e=this._baseState;return s(null===e.key),e.key=t,this},r.prototype.any=function(){return this._baseState.any=!0,this},r.prototype.choice=function(t){var e=this._baseState;return s(null===e.choice),e.choice=t,this._useArgs(Object.keys(t).map(function(e){return t[e]})),this},r.prototype.contains=function(t){var e=this._baseState;return s(null===e.use),e.contains=t,this},r.prototype._decode=function(t){var e=this._baseState;if(null===e.parent)return t.wrapResult(e.children[0]._decode(t));var n,r=e["default"],i=!0;if(null!==e.key&&(n=t.enterKey(e.key)),e.optional){var o=null;if(null!==e.explicit?o=e.explicit:null!==e.implicit?o=e.implicit:null!==e.tag&&(o=e.tag),null!==o||e.any){if(i=this._peekTag(t,o,e.any),t.isError(i))return i}else{var s=t.save();try{null===e.choice?this._decodeGeneric(e.tag,t):this._decodeChoice(t),i=!0}catch(c){i=!1}t.restore(s)}}var f;if(e.obj&&i&&(f=t.enterObject()),i){if(null!==e.explicit){if(o=this._decodeTag(t,e.explicit),t.isError(o))return o;t=o}if(null===e.use&&null===e.choice){if(e.any&&(s=t.save()),o=this._decodeTag(t,null!==e.implicit?e.implicit:e.tag,e.any),t.isError(o))return o;e.any?r=t.raw(s):t=o}if(e.any||(r=null===e.choice?this._decodeGeneric(e.tag,t):this._decodeChoice(t)),t.isError(r))return r;e.any||null!==e.choice||null===e.children||e.children.forEach(function(e){e._decode(t)}),!e.contains||"octstr"!==e.tag&&"bitstr"!==e.tag||(r=new a(r),r=this._getUse(e.contains,t._reporterState.obj)._decode(r))}return e.obj&&i&&(r=t.leaveObject(f)),null===e.key||null===r&&!0!==i||t.leaveKey(n,e.key,r),r},r.prototype._decodeGeneric=function(t,e){var n=this._baseState;return"seq"===t||"set"===t?null:"seqof"===t||"setof"===t?this._decodeList(e,t,n.args[0]):/str$/.test(t)?this._decodeStr(e,t):"objid"===t&&n.args?this._decodeObjid(e,n.args[0],n.args[1]):"objid"===t?this._decodeObjid(e,null,null):"gentime"===t||"utctime"===t?this._decodeTime(e,t):"null_"===t?this._decodeNull(e):"bool"===t?this._decodeBool(e):"int"===t||"enum"===t?this._decodeInt(e,n.args&&n.args[0]):null!==n.use?this._getUse(n.use,e._reporterState.obj)._decode(e):e.error("unknown tag: "+t)},r.prototype._getUse=function(t,e){var n=this._baseState;return n.useDecoder=this._use(t,e),s(null===n.useDecoder._baseState.parent),n.useDecoder=n.useDecoder._baseState.children[0],n.implicit!==n.useDecoder._baseState.implicit&&(n.useDecoder=n.useDecoder.clone(),n.useDecoder._baseState.implicit=n.implicit),n.useDecoder},r.prototype._decodeChoice=function(t){var e=this._baseState,n=null,r=!1;return Object.keys(e.choice).some(function(i){var o=t.save(),a=e.choice[i];try{var s=a._decode(t);if(t.isError(s))return!1;n={type:i,value:s},r=!0}catch(c){return t.restore(o),!1}return!0},this),r?n:t.error("Choice not matched")},r.prototype._createEncoderBuffer=function(t){return new o(t,this.reporter)},r.prototype._encode=function(t,e,n){var r=this._baseState;return null!==r["default"]&&r["default"]===t||(t=this._encodeValue(t,e,n),void 0===t||this._skipDefault(t,e,n))?void 0:t},r.prototype._encodeValue=function(t,e,n){var r=this._baseState;if(null===r.parent)return r.children[0]._encode(t,e||new i);var o=null;if(this.reporter=e,r.optional&&void 0===t){if(null===r["default"])return;t=r["default"]}var a=null,s=!1;if(r.any)o=this._createEncoderBuffer(t);else if(r.choice)o=this._encodeChoice(t,e);else if(r.contains)a=this._getUse(r.contains,n)._encode(t,e),s=!0;else if(r.children)a=r.children.map(function(n){if("null_"===n._baseState.tag)return n._encode(null,e,t);if(null===n._baseState.key)return e.error("Child should have a key");var r=e.enterKey(n._baseState.key);return"object"!=typeof t?e.error("Child expected, but input is not object"):(n=n._encode(t[n._baseState.key],e,t),e.leaveKey(r),n)},this).filter(function(t){return t}),a=this._createEncoderBuffer(a);else if("seqof"===r.tag||"setof"===r.tag){if(!r.args||1!==r.args.length)return e.error("Too many args for : "+r.tag);if(!Array.isArray(t))return e.error("seqof/setof, but data is not Array");a=this.clone(),a._baseState.implicit=null,a=this._createEncoderBuffer(t.map(function(n){return this._getUse(this._baseState.args[0],t)._encode(n,e)},a))}else null!==r.use?o=this._getUse(r.use,n)._encode(t,e):(a=this._encodePrimitive(r.tag,t),s=!0);if(!r.any&&null===r.choice){n=null!==r.implicit?r.implicit:r.tag;var c=null===r.implicit?"universal":"context";null===n?null===r.use&&e.error("Tag could be ommited only for .use()"):null===r.use&&(o=this._encodeComposite(n,s,c,a))}return null!==r.explicit&&(o=this._encodeComposite(r.explicit,!1,"context",o)),o},r.prototype._encodeChoice=function(t,e){var n=this._baseState,r=n.choice[t.type];return r||s(!1,t.type+" not found in "+JSON.stringify(Object.keys(n.choice))),r._encode(t.value,e)},r.prototype._encodePrimitive=function(t,e){var n=this._baseState;if(/str$/.test(t))return this._encodeStr(e,t);if("objid"===t&&n.args)return this._encodeObjid(e,n.reverseArgs[0],n.args[1]);if("objid"===t)return this._encodeObjid(e,null,null);if("gentime"===t||"utctime"===t)return this._encodeTime(e,t);if("null_"===t)return this._encodeNull();if("int"===t||"enum"===t)return this._encodeInt(e,n.args&&n.reverseArgs[0]);if("bool"===t)return this._encodeBool(e);throw Error("Unsupported tag: "+t)},r.prototype._isNumstr=function(t){return/^[0-9 ]*$/.test(t)},r.prototype._isPrintstr=function(t){return/^[A-Za-z0-9 '\(\)\+,\-\.\/:=\?]*$/.test(t)}},{105:105,6:6}],8:[function(t,e,n){function r(t){this._reporterState={obj:null,path:[],options:t||{},errors:[]}}function i(t,e){this.path=t,this.rethrow(e)}t=t(101),n.Reporter=r,r.prototype.isError=function(t){return t instanceof i},r.prototype.save=function(){var t=this._reporterState;return{obj:t.obj,pathLen:t.path.length}},r.prototype.restore=function(t){var e=this._reporterState;e.obj=t.obj,e.path=e.path.slice(0,t.pathLen)},r.prototype.enterKey=function(t){return this._reporterState.path.push(t)},r.prototype.leaveKey=function(t,e,n){var r=this._reporterState;r.path=r.path.slice(0,t-1),null!==r.obj&&(r.obj[e]=n)},r.prototype.enterObject=function(){var t=this._reporterState,e=t.obj;return t.obj={},e},r.prototype.leaveObject=function(t){var e=this._reporterState,n=e.obj;return e.obj=t,n},r.prototype.error=function(t){var e=this._reporterState,n=t instanceof i;if(t=n?t:new i(e.path.map(function(t){return"["+JSON.stringify(t)+"]"}).join(""),t.message||t,t.stack),!e.options.partial)throw t;return n||e.errors.push(t),t},r.prototype.wrapResult=function(t){var e=this._reporterState;return e.options.partial?{result:this.isError(t)?null:t,errors:e.errors}:t},t(i,Error),i.prototype.rethrow=function(t){return this.message=t+" at: "+(this.path||"(shallow)"),Error.captureStackTrace(this,i),this}},{101:101}],9:[function(t,e,n){t=t(10),n.tagClass={0:"universal",1:"application",2:"context",3:"private"},n.tagClassByName=t._reverse(n.tagClass),n.tag={0:"end",1:"bool",2:"int",3:"bitstr",4:"octstr",5:"null_",6:"objid",7:"objDesc",8:"external",9:"real",10:"enum",11:"embed",12:"utf8str",13:"relativeOid",16:"seq",17:"set",18:"numstr",19:"printstr",20:"t61str",21:"videostr",22:"ia5str",23:"utctime",24:"gentime",25:"graphstr",26:"iso646str",27:"genstr",28:"unistr",29:"charstr",30:"bmpstr"},n.tagByName=t._reverse(n.tag)},{10:10}],10:[function(t,e,n){n._reverse=function(t){var e={};return Object.keys(t).forEach(function(n){(0|n)==n&&(n|=0),e[t[n]]=n}),e},n.der=t(9)},{9:9}],11:[function(t,e,n){function r(t){this.enc="der",this.name=t.name,this.entity=t,this.tree=new i,this.tree._init(t.body)}function i(t){s.Node.call(this,"der",t)}function o(t,e){var n=t.readUInt8(e);if(t.isError(n))return n;var r=f.tagClass[n>>6],i=0===(32&n);if(31===(31&n))for(var o=n,n=0;128===(128&o);){if(o=t.readUInt8(e),t.isError(o))return o;n<<=7,n|=127&o}else n&=31;return{cls:r,primitive:i,tag:n,tagStr:f.tag[n]}}function a(t,e,n){var r=t.readUInt8(n);if(t.isError(r))return r;if(!e&&128===r)return null;if(0===(128&r))return r;if(e=127&r,e>=4)return t.error("length octect is too long");for(var i=r=0;e>i;i++){var r=r<<8,o=t.readUInt8(n);if(t.isError(o))return o;r|=o}return r}n=t(101),t=t(3);var s=t.base,c=t.bignum,f=t.constants.der;e.exports=r,r.prototype.decode=function(t,e){return t instanceof s.DecoderBuffer||(t=new s.DecoderBuffer(t,e)),this.tree._decode(t,e)},n(i,s.Node),i.prototype._peekTag=function(t,e,n){if(t.isEmpty())return!1;var r=t.save(),i=o(t,'Failed to peek tag: "'+e+'"');return t.isError(i)?i:(t.restore(r),i.tag===e||i.tagStr===e||i.tagStr+"of"===e||n)},i.prototype._decodeTag=function(t,e,n){var r=o(t,'Failed to decode tag of "'+e+'"');if(t.isError(r))return r;var i=a(t,r.primitive,'Failed to get length of "'+e+'"');return t.isError(i)?i:n||r.tag===e||r.tagStr===e||r.tagStr+"of"===e?r.primitive||null!==i?t.skip(i,'Failed to match body of: "'+e+'"'):(n=t.save(),i=this._skipUntilEnd(t,'Failed to skip indefinite length body: "'+this.tag+'"'),t.isError(i)?i:(i=t.offset-n.offset,t.restore(n),t.skip(i,'Failed to match body of: "'+e+'"'))):t.error('Failed to match tag: "'+e+'"')},i.prototype._skipUntilEnd=function(t,e){for(;;){var n=o(t,e);if(t.isError(n))return n;var r=a(t,n.primitive,e);if(t.isError(r))return r;if(r=n.primitive||null!==r?t.skip(r):this._skipUntilEnd(t,e),t.isError(r))return r;if("end"===n.tagStr)break}},i.prototype._decodeList=function(t,e,n){for(e=[];!t.isEmpty();){var r=this._peekTag(t,"end");if(t.isError(r))return r;var i=n.decode(t,"der");if(t.isError(i)&&r)break;e.push(i)}return e},i.prototype._decodeStr=function(t,e){if("bitstr"===e){var n=t.readUInt8();return t.isError(n)?n:{unused:n,data:t.raw()}}if("bmpstr"===e){if(n=t.raw(),1===n.length%2)return t.error("Decoding of string type: bmpstr length mismatch");for(var r="",i=0;i<n.length/2;i++)r+=String.fromCharCode(n.readUInt16BE(2*i));return r}return"numstr"===e?(n=t.raw().toString("ascii"),this._isNumstr(n)?n:t.error("Decoding of string type: numstr unsupported characters")):"octstr"===e?t.raw():"printstr"===e?(n=t.raw().toString("ascii"),this._isPrintstr(n)?n:t.error("Decoding of string type: printstr unsupported characters")):/str$/.test(e)?t.raw().toString():t.error("Decoding of string type: "+e+" unsupported")},i.prototype._decodeObjid=function(t,e,n){for(var r=[],i=0;!t.isEmpty();){var o=t.readUInt8(),i=i<<7,i=i|127&o;0===(128&o)&&(r.push(i),i=0)}return 128&o&&r.push(i),t=r[0]/40|0,i=r[0]%40,n=n?r:[t,i].concat(r.slice(1)),e&&(r=e[n.join(" ")],void 0===r&&(r=e[n.join(".")]),void 0!==r&&(n=r)),n},i.prototype._decodeTime=function(t,e){var n=t.raw().toString();if("gentime"===e)var r=0|n.slice(0,4),i=0|n.slice(4,6),o=0|n.slice(6,8),a=0|n.slice(8,10),s=0|n.slice(10,12),n=0|n.slice(12,14);else{if("utctime"!==e)return t.error("Decoding "+e+" time is not supported yet");r=0|n.slice(0,2),i=0|n.slice(2,4),o=0|n.slice(4,6),a=0|n.slice(6,8),s=0|n.slice(8,10),n=0|n.slice(10,12),r=70>r?2e3+r:1900+r}return Date.UTC(r,i-1,o,a,s,n,0)},i.prototype._decodeNull=function(t){return null},i.prototype._decodeBool=function(t){var e=t.readUInt8();return t.isError(e)?e:0!==e},i.prototype._decodeInt=function(t,e){var n=t.raw(),n=new c(n);return e&&(n=e[n.toString(10)]||n),n},i.prototype._use=function(t,e){return"function"==typeof t&&(t=t(e)),t._getDecoder("der").tree}},{101:101,3:3}],12:[function(t,e,n){n.der=t(11),n.pem=t(13)},{11:11,13:13}],13:[function(t,e,n){function r(t){o.call(this,t),this.enc="pem"}n=t(101);var i=t(48).Buffer;t(3);var o=t(11);n(r,o),e.exports=r,r.prototype.decode=function(t,e){for(var n=t.toString().split(/[\r\n]+/g),r=e.label.toUpperCase(),a=/^-----(BEGIN|END) ([^-]+)-----$/,s=-1,c=-1,f=0;f<n.length;f++){var u=n[f].match(a);if(null!==u&&u[2]===r){if(-1!==s){if("END"!==u[1])break;c=f;break}if("BEGIN"!==u[1])break;s=f}}if(-1===s||-1===c)throw Error("PEM section not found for: "+r);return n=n.slice(s+1,c).join(""),n.replace(/[^a-z0-9\+\/=]+/gi,""),n=new i(n,"base64"),o.prototype.decode.call(this,n,e)}},{101:101,11:11,3:3,48:48}],14:[function(t,e,n){function r(t){this.enc="der",this.name=t.name,this.entity=t,this.tree=new i,this.tree._init(t.body)}function i(t){s.Node.call(this,"der",t)}function o(t){return 10>t?"0"+t:t}n=t(101);var a=t(48).Buffer;t=t(3);var s=t.base,c=t.constants.der;e.exports=r,r.prototype.encode=function(t,e){return this.tree._encode(t,e).join()},n(i,s.Node),i.prototype._encodeComposite=function(t,e,n,r){t:{var i=t;if(t=this.reporter,"seqof"===i?i="seq":"setof"===i&&(i="set"),c.tagByName.hasOwnProperty(i))i=c.tagByName[i];else if("number"!=typeof i||(0|i)!==i){t=t.error("Unknown tag: "+i);break t}i>=31?t=t.error("Multi-octet tag encoding unsupported"):(e||(i|=32),t=i|=c.tagClassByName[n||"universal"]<<6)}if(128>r.length)return e=new a(2),e[0]=t,e[1]=r.length,this._createEncoderBuffer([e,r]);for(i=1,n=r.length;n>=256;n>>=8)i++;for(e=new a(2+i),e[0]=t,e[1]=128|i,n=1+i,t=r.length;t>0;n--,t>>=8)e[n]=255&t;return this._createEncoderBuffer([e,r])},i.prototype._encodeStr=function(t,e){if("bitstr"===e)return this._createEncoderBuffer([0|t.unused,t.data]);if("bmpstr"===e){for(var n=new a(2*t.length),r=0;r<t.length;r++)n.writeUInt16BE(t.charCodeAt(r),2*r);return this._createEncoderBuffer(n)}return"numstr"===e?this._isNumstr(t)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: numstr supports only digits and space"):"printstr"===e?this._isPrintstr(t)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: printstr supports only latin upper and lower case letters, digits, space, apostrophe, left and rigth parenthesis, plus sign, comma, hyphen, dot, slash, colon, equal sign, question mark"):/str$/.test(e)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: "+e+" unsupported")},i.prototype._encodeObjid=function(t,e,n){if("string"==typeof t){if(!e)return this.reporter.error("string objid given, but no values map found");if(!e.hasOwnProperty(t))return this.reporter.error("objid not found in values map");for(t=e[t].split(/[\s\.]+/g),e=0;e<t.length;e++)t[e]|=0}else if(Array.isArray(t))for(t=t.slice(),e=0;e<t.length;e++)t[e]|=0;if(!Array.isArray(t))return this.reporter.error("objid() should be either array or string, got: "+JSON.stringify(t));if(!n){if(40<=t[1])return this.reporter.error("Second objid identifier OOB");t.splice(0,2,40*t[0]+t[1])}var r=0;for(e=0;e<t.length;e++)for(n=t[e],r++;n>=128;n>>=7)r++;var r=new a(r),i=r.length-1;for(e=t.length-1;e>=0;e--)for(n=t[e],r[i--]=127&n;0<(n>>=7);)r[i--]=128|127&n;return this._createEncoderBuffer(r)},i.prototype._encodeTime=function(t,e){var n,r=new Date(t);return"gentime"===e?n=[o(r.getFullYear()),o(r.getUTCMonth()+1),o(r.getUTCDate()),o(r.getUTCHours()),o(r.getUTCMinutes()),o(r.getUTCSeconds()),"Z"].join(""):"utctime"===e?n=[o(r.getFullYear()%100),o(r.getUTCMonth()+1),o(r.getUTCDate()),o(r.getUTCHours()),o(r.getUTCMinutes()),o(r.getUTCSeconds()),"Z"].join(""):this.reporter.error("Encoding "+e+" time is not supported yet"),this._encodeStr(n,"octstr")},i.prototype._encodeNull=function(){return this._createEncoderBuffer("")},i.prototype._encodeInt=function(t,e){if("string"==typeof t){if(!e)return this.reporter.error("String int or enum given, but no values map");if(!e.hasOwnProperty(t))return this.reporter.error("Values map doesn't contain: "+JSON.stringify(t));t=e[t]}if("number"!=typeof t&&!a.isBuffer(t)){var n=t.toArray();!t.sign&&128&n[0]&&n.unshift(0),t=new a(n)}if(a.isBuffer(t)){var r=t.length;return 0===t.length&&r++,r=new a(r),t.copy(r),0===t.length&&(r[0]=0),this._createEncoderBuffer(r)}if(128>t)return this._createEncoderBuffer(t);if(256>t)return this._createEncoderBuffer([0,t]);for(r=1,n=t;n>=256;n>>=8)r++;for(r=Array(r),n=r.length-1;n>=0;n--)r[n]=255&t,t>>=8;return 128&r[0]&&r.unshift(0),this._createEncoderBuffer(new a(r))},i.prototype._encodeBool=function(t){return this._createEncoderBuffer(t?255:0)},i.prototype._use=function(t,e){return"function"==typeof t&&(t=t(e)),t._getEncoder("der").tree},i.prototype._skipDefault=function(t,e,n){var r=this._baseState;if(null===r["default"])return!1;if(t=t.join(),void 0===r.defaultBuffer&&(r.defaultBuffer=this._encodeValue(r["default"],e,n).join()),t.length!==r.defaultBuffer.length)return!1;for(e=0;e<t.length;e++)if(t[e]!==r.defaultBuffer[e])return!1;return!0}},{101:101,3:3,48:48}],15:[function(t,e,n){n.der=t(14),n.pem=t(16)},{14:14,16:16}],16:[function(t,e,n){function r(t){i.call(this,t),this.enc="pem"}n=t(101),t(48),t(3);var i=t(14);n(r,i),e.exports=r,r.prototype.encode=function(t,e){for(var n=i.prototype.encode.call(this,t).toString("base64"),r=["-----BEGIN "+e.label+"-----"],o=0;o<n.length;o+=64)r.push(n.slice(o,o+64));return r.push("-----END "+e.label+"-----"),r.join("\n")}},{101:101,14:14,3:3,48:48}],17:[function(t,e,n){(function(t,n){!function(){function r(){}function i(t){return t}function o(t){return!!t}function a(t){return!t}function s(t){return function(){if(null===t)throw Error("Callback was already called.");t.apply(this,arguments),t=null}}function c(t){return function(){null!==t&&(t.apply(this,arguments),t=null)}}function f(t){return z(t)||"number"==typeof t.length&&0<=t.length&&0===t.length%1}function u(t,e){for(var n=-1,r=t.length;++n<r;)e(t[n],n,t)}function d(t,e){for(var n=-1,r=t.length,i=Array(r);++n<r;)i[n]=e(t[n],n,t);return i}function p(t){return d(Array(t),function(t,e){return e})}function h(t,e,n){return u(t,function(t,r,i){n=e(n,t,r,i)}),n}function l(t,e){u(D(t),function(n){e(t[n],n)})}function b(t,e){for(var n=0;n<t.length;n++)if(t[n]===e)return n;return-1}function m(t){var e,n,r=-1;return f(t)?(e=t.length,function(){return r++,e>r?r:null}):(n=D(t),e=n.length,function(){return r++,e>r?n[r]:null})}function v(t,e){return e=null==e?t.length-1:+e,function(){for(var n=Math.max(arguments.length-e,0),r=Array(n),i=0;n>i;i++)r[i]=arguments[i+e];switch(e){case 0:return t.call(this,r);case 1:return t.call(this,arguments[0],r)}}}function y(t){return function(e,n,r){return t(e,r)}}function g(t){return function(e,n,i){i=c(i||r),e=e||[];var o=m(e);if(0>=t)return i(null);var a=!1,f=0,u=!1;!function d(){if(a&&0>=f)return i(null);for(;t>f&&!u;){var r=o();if(null===r){a=!0,0>=f&&i(null);break}f+=1,n(e[r],r,s(function(t){--f,t?(i(t),u=!0):d()}))}}()}}function _(t){return function(e,n,r){return t(P.eachOf,e,n,r)}}function w(t){return function(e,n,r,i){return t(g(n),e,r,i)}}function x(t){return function(e,n,r){return t(P.eachOfSeries,e,n,r)}}function k(t,e,n,i){i=c(i||r),e=e||[];var o=f(e)?[]:{};t(e,function(t,e,r){n(t,function(t,n){o[e]=n,r(t)})},function(t){i(t,o)})}function E(t,e,n,r){var i=[];t(e,function(t,e,r){n(t,function(n){n&&i.push({index:e,value:t}),r()})},function(){r(d(i.sort(function(t,e){return t.index-e.index}),function(t){return t.value}))})}function S(t,e,n,r){E(t,e,function(t,e){n(t,function(t){e(!t)})},r)}function M(t,e,n){return function(r,i,o,a){function s(){a&&a(n(!1,void 0))}function c(t,r,i){return a?void o(t,function(r){a&&e(r)&&(a(n(!0,t)),a=o=!1),i()}):i()}3<arguments.length?t(r,i,c,s):(a=o,o=i,t(r,c,s))}}function A(t,e){return e}function T(t,e,n){n=n||r;var i=f(e)?[]:{};t(e,function(t,e,n){t(v(function(t,r){1>=r.length&&(r=r[0]),i[e]=r,n(t)}))},function(t){n(t,i)})}function j(t,e,n,r){var i=[];t(e,function(t,e,r){n(t,function(t,e){i=i.concat(e||[]),r(t)})},function(t){r(t,i)})}function I(t,e,n){function i(t,e,n,i){if(null!=i&&"function"!=typeof i)throw Error("task callback must be a function");return t.started=!0,z(e)||(e=[e]),0===e.length&&t.idle()?P.setImmediate(function(){t.drain()}):(u(e,function(e){e={data:e,callback:i||r},n?t.tasks.unshift(e):t.tasks.push(e),t.tasks.length===t.concurrency&&t.saturated()}),void P.setImmediate(t.process))}function o(t,e){return function(){--a;var n=!1,r=arguments;u(e,function(t){u(c,function(e,r){e!==t||n||(c.splice(r,1),n=!0)}),t.callback.apply(t,r)}),0===t.tasks.length+a&&t.drain(),t.process()}}if(null==e)e=1;else if(0===e)throw Error("Concurrency must not be zero");var a=0,c=[],f={tasks:[],concurrency:e,payload:n,saturated:r,empty:r,drain:r,started:!1,paused:!1,push:function(t,e){i(f,t,!1,e)},kill:function(){f.drain=r,f.tasks=[]},unshift:function(t,e){i(f,t,!0,e)},process:function(){for(;!f.paused&&a<f.concurrency&&f.tasks.length;){var e=f.payload?f.tasks.splice(0,f.payload):f.tasks.splice(0,f.tasks.length),n=d(e,function(t){return t.data});0===f.tasks.length&&f.empty(),a+=1,c.push(e[0]),e=s(o(f,e)),t(n,e)}},length:function(){return f.tasks.length},running:function(){return a},workersList:function(){return c},idle:function(){return 0===f.tasks.length+a},pause:function(){f.paused=!0},resume:function(){if(!1!==f.paused){f.paused=!1;for(var t=Math.min(f.concurrency,f.tasks.length),e=1;t>=e;e++)P.setImmediate(f.process)}}};return f}function R(t){return v(function(e,n){e.apply(null,n.concat([v(function(e,n){"object"==typeof console&&(e?console.error&&console.error(e):console[t]&&u(n,function(e){console[t](e)}))})]))})}function O(t){return function(e,n,r){t(p(e),n,r)}}function B(t){return v(function(e,n){var r=v(function(n){var r=this,i=n.pop();return t(e,function(t,e,i){t.apply(r,n.concat([i]))},i)});return n.length?r.apply(this,n):r})}function N(t){return v(function(e){var n=e.pop();e.push(function(){var t=arguments;r?P.setImmediate(function(){n.apply(null,t)}):n.apply(null,t)});var r=!0;t.apply(this,e),r=!1})}var C,P={},q="object"==typeof self&&self.self===self&&self||"object"==typeof n&&n.global===n&&n||this;null!=q&&(C=q.async),P.noConflict=function(){return q.async=C,P};var U=Object.prototype.toString,z=Array.isArray||function(t){return"[object Array]"===U.call(t)},L=function(t){var e=typeof t;return"function"===e||"object"===e&&!!t},D=Object.keys||function(t){var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n},F="function"==typeof setImmediate&&setImmediate,H=F?function(t){F(t)}:function(t){setTimeout(t,0)};P.nextTick="object"==typeof t&&"function"==typeof t.nextTick?t.nextTick:H,P.setImmediate=F?H:P.nextTick,P.forEach=P.each=function(t,e,n){return P.eachOf(t,y(e),n)},P.forEachSeries=P.eachSeries=function(t,e,n){return P.eachOfSeries(t,y(e),n)},P.forEachLimit=P.eachLimit=function(t,e,n,r){return g(e)(t,y(n),r)},P.forEachOf=P.eachOf=function(t,e,n){function i(t){f--,t?n(t):null===o&&0>=f&&n(null)}n=c(n||r),t=t||[];for(var o,a=m(t),f=0;null!=(o=a());)f+=1,e(t[o],o,s(i));0===f&&n(null)},P.forEachOfSeries=P.eachOfSeries=function(t,e,n){function i(){var r=!0;return null===a?n(null):(e(t[a],a,s(function(t){if(t)n(t);else{if(a=o(),null===a)return n(null);r?P.setImmediate(i):i()}})),void(r=!1))}n=c(n||r),t=t||[];var o=m(t),a=o();i()},P.forEachOfLimit=P.eachOfLimit=function(t,e,n,r){g(e)(t,n,r)},P.map=_(k),P.mapSeries=x(k),P.mapLimit=w(k),P.inject=P.foldl=P.reduce=function(t,e,n,r){P.eachOfSeries(t,function(t,r,i){n(e,t,function(t,n){e=n,i(t)})},function(t){r(t,e)})},P.foldr=P.reduceRight=function(t,e,n,r){t=d(t,i).reverse(),P.reduce(t,e,n,r)},P.transform=function(t,e,n,r){3===arguments.length&&(r=n,n=e,e=z(t)?[]:{}),P.eachOf(t,function(t,r,i){n(e,t,r,i)},function(t){r(t,e)})},P.select=P.filter=_(E),P.selectLimit=P.filterLimit=w(E),P.selectSeries=P.filterSeries=x(E),P.reject=_(S),P.rejectLimit=w(S),P.rejectSeries=x(S),P.any=P.some=M(P.eachOf,o,i),P.someLimit=M(P.eachOfLimit,o,i),P.all=P.every=M(P.eachOf,a,a),P.everyLimit=M(P.eachOfLimit,a,a),P.detect=M(P.eachOf,i,A),P.detectSeries=M(P.eachOfSeries,i,A),P.detectLimit=M(P.eachOfLimit,i,A),P.sortBy=function(t,e,n){function r(t,e){var n=t.criteria,r=e.criteria;return r>n?-1:n>r?1:0}P.map(t,function(t,n){e(t,function(e,r){e?n(e):n(null,{value:t,criteria:r})})},function(t,e){return t?n(t):void n(null,d(e.sort(r),function(t){return t.value}))})},P.auto=function(t,e,n){function i(){a--,u(p.slice(0),function(t){t()})}"function"==typeof e&&(n=e,e=null),n=c(n||r);var o=D(t),a=o.length;if(!a)return n(null);e||(e=a);var s={},f=0,d=!1,p=[];p.unshift(function(){a||n(null,s)}),u(o,function(r){function o(){return e>f&&h(y,function(t,e){return t&&s.hasOwnProperty(e)},!0)&&!s.hasOwnProperty(r)}function a(){if(o()){f++;var t=b(p,a);t>=0&&p.splice(t,1),u[u.length-1](m,s)}}if(!d){for(var c,u=z(t[r])?t[r]:[t[r]],m=v(function(t,e){if(f--,1>=e.length&&(e=e[0]),t){var o={};l(s,function(t,e){o[e]=t}),o[r]=e,d=!0,n(t,o)}else s[r]=e,P.setImmediate(i)}),y=u.slice(0,u.length-1),g=y.length;g--;){if(!(c=t[y[g]]))throw Error("Has nonexistent dependency in "+y.join(", "));if(z(c)&&0<=b(c,r))throw Error("Has cyclic dependencies")}o()?(f++,
u[u.length-1](m,s)):p.unshift(a)}})},P.retry=function(t,e,n){function r(t,e){if("number"==typeof e)t.times=parseInt(e,10)||5;else{if("object"!=typeof e)throw Error("Unsupported argument type for 'times': "+typeof e);t.times=parseInt(e.times,10)||5,t.interval=parseInt(e.interval,10)||0}}function i(t,e){function n(t,n){return function(r){t(function(t,e){r(!t||n,{err:t,result:e})},e)}}function r(t){return function(e){setTimeout(function(){e(null)},t)}}for(;a.times;){var i=!--a.times;o.push(n(a.task,i)),!i&&0<a.interval&&o.push(r(a.interval))}P.series(o,function(e,n){n=n[n.length-1],(t||a.callback)(n.err,n.result)})}var o=[],a={times:5,interval:0},s=arguments.length;if(1>s||s>3)throw Error("Invalid arguments - must be either (task), (task, callback), (times, task) or (times, task, callback)");return 2>=s&&"function"==typeof t&&(n=e,e=t),"function"!=typeof t&&r(a,t),a.callback=n,a.task=e,a.callback?i():i},P.waterfall=function(t,e){function n(t){return v(function(r,i){if(r)e.apply(null,[r].concat(i));else{var o=t.next();o?i.push(n(o)):i.push(e),N(t).apply(null,i)}})}return e=c(e||r),z(t)?t.length?void n(P.iterator(t))():e():e(Error("First argument to waterfall must be an array of functions"))},P.parallel=function(t,e){T(P.eachOf,t,e)},P.parallelLimit=function(t,e,n){T(g(e),t,n)},P.series=function(t,e){T(P.eachOfSeries,t,e)},P.iterator=function(t){function e(n){function r(){return t.length&&t[n].apply(null,arguments),r.next()}return r.next=function(){return n<t.length-1?e(n+1):null},r}return e(0)},P.apply=v(function(t,e){return v(function(n){return t.apply(null,e.concat(n))})}),P.concat=_(j),P.concatSeries=x(j),P.whilst=function(t,e,n){if(n=n||r,t()){var i=v(function(r,o){r?n(r):t.apply(this,o)?e(i):n.apply(null,[null].concat(o))});e(i)}else n(null)},P.doWhilst=function(t,e,n){var r=0;return P.whilst(function(){return 1>=++r||e.apply(this,arguments)},t,n)},P.until=function(t,e,n){return P.whilst(function(){return!t.apply(this,arguments)},e,n)},P.doUntil=function(t,e,n){return P.doWhilst(t,function(){return!e.apply(this,arguments)},n)},P.during=function(t,e,n){n=n||r;var i=v(function(e,r){e?n(e):(r.push(o),t.apply(this,r))}),o=function(t,r){t?n(t):r?e(i):n(null)};t(o)},P.doDuring=function(t,e,n){var r=0;P.during(function(t){1>r++?t(null,!0):e.apply(this,arguments)},t,n)},P.queue=function(t,e){return I(function(e,n){t(e[0],n)},e,1)},P.priorityQueue=function(t,e){function n(t,e){return t.priority-e.priority}function i(t,e,n){for(var r=-1,i=t.length-1;i>r;){var o=r+(i-r+1>>>1);0<=n(e,t[o])?r=o:i=o-1}return r}function o(t,e,o,a){if(null!=a&&"function"!=typeof a)throw Error("task callback must be a function");return t.started=!0,z(e)||(e=[e]),0===e.length?P.setImmediate(function(){t.drain()}):void u(e,function(e){e={data:e,priority:o,callback:"function"==typeof a?a:r},t.tasks.splice(i(t.tasks,e,n)+1,0,e),t.tasks.length===t.concurrency&&t.saturated(),P.setImmediate(t.process)})}var a=P.queue(t,e);return a.push=function(t,e,n){o(a,t,e,n)},delete a.unshift,a},P.cargo=function(t,e){return I(t,1,e)},P.log=R("log"),P.dir=R("dir"),P.memoize=function(t,e){var n={},r={},o=Object.prototype.hasOwnProperty;e=e||i;var a=v(function(i){var a=i.pop(),s=e.apply(null,i);o.call(n,s)?P.setImmediate(function(){a.apply(null,n[s])}):o.call(r,s)?r[s].push(a):(r[s]=[a],t.apply(null,i.concat([v(function(t){n[s]=t;var e=r[s];delete r[s];for(var i=0,o=e.length;o>i;i++)e[i].apply(null,t)})])))});return a.memo=n,a.unmemoized=t,a},P.unmemoize=function(t){return function(){return(t.unmemoized||t).apply(null,arguments)}},P.times=O(P.map),P.timesSeries=O(P.mapSeries),P.timesLimit=function(t,e,n,r){return P.mapLimit(p(t),e,n,r)},P.seq=function(){var t=arguments;return v(function(e){var n=this,i=e[e.length-1];"function"==typeof i?e.pop():i=r,P.reduce(t,e,function(t,e,r){e.apply(n,t.concat([v(function(t,e){r(t,e)})]))},function(t,e){i.apply(n,[t].concat(e))})})},P.compose=function(){return P.seq.apply(null,Array.prototype.reverse.call(arguments))},P.applyEach=B(P.eachOf),P.applyEachSeries=B(P.eachOfSeries),P.forever=function(t,e){function n(t){return t?i(t):void o(n)}var i=s(e||r),o=N(t);n()},P.ensureAsync=N,P.constant=v(function(t){var e=[null].concat(t);return function(t){return t.apply(this,e)}}),P.wrapSync=P.asyncify=function(t){return v(function(e){var n,r=e.pop();try{n=t.apply(this,e)}catch(i){return r(i)}L(n)&&"function"==typeof n.then?n.then(function(t){r(null,t)})["catch"](function(t){r(t.message?t:Error(t))}):r(null,n)})},"object"==typeof e&&e.exports?e.exports=P:q.async=P}()}).call(this,t(113),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{113:113}],18:[function(t,e,n){!function(t){function e(t){return t=t.charCodeAt(0),43===t||45===t?62:47===t||95===t?63:48>t?-1:58>t?t-48+52:91>t?t-65:123>t?t-97+26:void 0}var n="undefined"!=typeof Uint8Array?Uint8Array:Array;t.toByteArray=function(t){function r(t){c[f++]=t}var i,o,a,s,c;if(0<t.length%4)throw Error("Invalid string. Length must be a multiple of 4");i=t.length,s="="===t.charAt(i-2)?2:"="===t.charAt(i-1)?1:0,c=new n(3*t.length/4-s),o=s>0?t.length-4:t.length;var f=0;for(i=0;o>i;i+=4)a=e(t.charAt(i))<<18|e(t.charAt(i+1))<<12|e(t.charAt(i+2))<<6|e(t.charAt(i+3)),r((16711680&a)>>16),r((65280&a)>>8),r(255&a);return 2===s?(a=e(t.charAt(i))<<2|e(t.charAt(i+1))>>4,r(255&a)):1===s&&(a=e(t.charAt(i))<<10|e(t.charAt(i+1))<<4|e(t.charAt(i+2))>>2,r(a>>8&255),r(255&a)),c},t.fromByteArray=function(t){var e,n,r,i=t.length%3,o="";for(e=0,r=t.length-i;r>e;e+=3)n=(t[e]<<16)+(t[e+1]<<8)+t[e+2],n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>18&63)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>12&63)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>6&63)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(63&n),o+=n;switch(i){case 1:n=t[t.length-1],o+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>2),o+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n<<4&63),o+="==";break;case 2:n=(t[t.length-2]<<8)+t[t.length-1],o+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>10),o+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>4&63),o+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n<<2&63),o+="="}return o}}("undefined"==typeof n?this.base64js={}:n)},{}],19:[function(t,e,n){!function(e,n){function r(t,e){if(!t)throw Error(e||"Assertion failed")}function i(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}function o(t,e,n){return o.isBN(t)?t:(this.negative=0,this.words=null,this.length=0,this.red=null,void(null!==t&&("le"!==e&&"be"!==e||(n=e,e=10),this._init(t||0,e||10,n||"be"))))}function a(t,e,n){var r=0;for(n=Math.min(t.length,n);n>e;e++)var i=t.charCodeAt(e)-48,r=r<<4,r=i>=49&&54>=i?r|i-49+10:i>=17&&22>=i?r|i-17+10:r|15&i;return r}function s(t,e,n,r){var i=0;for(n=Math.min(t.length,n);n>e;e++)var o=t.charCodeAt(e)-48,i=i*r,i=o>=49?i+(o-49+10):o>=17?i+(o-17+10):i+o;return i}function c(t,e,n){n.negative=e.negative^t.negative;var r=t.length+e.length|0;n.length=r;var r=r-1|0,i=0|t.words[0],o=0|e.words[0],i=i*o,a=i/67108864|0;n.words[0]=67108863&i;for(var s=1;r>s;s++){for(var c=a>>>26,f=67108863&a,a=Math.min(s,e.length-1),u=Math.max(0,s-t.length+1);a>=u;u++)i=0|t.words[s-u|0],o=0|e.words[u],i=i*o+f,c+=i/67108864|0,f=67108863&i;n.words[s]=0|f,a=0|c}return 0!==a?n.words[s]=0|a:n.length--,n.strip()}function f(t,e){this.x=t,this.y=e}function u(t,e){this.name=t,this.p=new o(e,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function d(){u.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function p(){u.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function h(){u.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function l(){u.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function b(t){"string"==typeof t?(t=o._prime(t),this.m=t.p,this.prime=t):(this.m=t,this.prime=null)}function m(t){b.call(this,t),this.shift=this.m.bitLength(),0!==this.shift%26&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}"object"==typeof e?e.exports=o:n.BN=o,o.BN=o,o.wordSize=26;var v;try{v=t("buffer").Buffer}catch(y){}o.isBN=function(t){return null!==t&&"object"==typeof t&&"BN"===t.constructor.name&&Array.isArray(t.words)},o.max=function(t,e){return 0<t.cmp(e)?t:e},o.min=function(t,e){return 0>t.cmp(e)?t:e},o.prototype._init=function(t,e,n){if("number"==typeof t)return this._initNumber(t,e,n);if("object"==typeof t)return this._initArray(t,e,n);"hex"===e&&(e=16),r(e===(0|e)&&e>=2&&36>=e),t=t.toString().replace(/\s+/g,"");var i=0;"-"===t[0]&&i++,16===e?this._parseHex(t,i):this._parseBase(t,e,i),"-"===t[0]&&(this.negative=1),this.strip(),"le"===n&&this._initArray(this.toArray(),e,n)},o.prototype._initNumber=function(t,e,n){0>t&&(this.negative=1,t=-t),67108864>t?(this.words=[67108863&t],this.length=1):4503599627370496>t?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(r(9007199254740992>t),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===n&&this._initArray(this.toArray(),e,n)},o.prototype._initArray=function(t,e,n){if(r("number"==typeof t.length),0>=t.length)return this.words=[0],this.length=1,this;for(this.length=Math.ceil(t.length/3),this.words=Array(this.length),e=0;e<this.length;e++)this.words[e]=0;var i,o=0;if("be"===n)for(e=t.length-1,n=0;e>=0;e-=3)i=t[e]|t[e-1]<<8|t[e-2]<<16,this.words[n]|=i<<o&67108863,this.words[n+1]=i>>>26-o&67108863,o+=24,o>=26&&(o-=26,n++);else if("le"===n)for(n=e=0;e<t.length;e+=3)i=t[e]|t[e+1]<<8|t[e+2]<<16,this.words[n]|=i<<o&67108863,this.words[n+1]=i>>>26-o&67108863,o+=24,o>=26&&(o-=26,n++);return this.strip()},o.prototype._parseHex=function(t,e){this.length=Math.ceil((t.length-e)/6),this.words=Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var r,i,o=0,n=t.length-6;for(r=0;n>=e;n-=6)i=a(t,n,n+6),this.words[r]|=i<<o&67108863,this.words[r+1]|=i>>>26-o&4194303,o+=24,o>=26&&(o-=26,r++);n+6!==e&&(i=a(t,e,n+6),this.words[r]|=i<<o&67108863,this.words[r+1]|=i>>>26-o&4194303),this.strip()},o.prototype._parseBase=function(t,e,n){this.words=[0],this.length=1;for(var r=0,i=1;67108863>=i;i*=e)r++;r--;for(var i=i/e|0,o=t.length-n,a=o%r,o=Math.min(o,o-a)+n,c=n;o>c;c+=r)n=s(t,c,c+r,e),this.imuln(i),67108864>this.words[0]+n?this.words[0]+=n:this._iaddn(n);if(0!==a){for(r=1,n=s(t,c,t.length,e),c=0;a>c;c++)r*=e;this.imuln(r),67108864>this.words[0]+n?this.words[0]+=n:this._iaddn(n)}},o.prototype.copy=function(t){t.words=Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;1<this.length&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var g=" 0 00 000 0000 00000 000000 0000000 00000000 000000000 0000000000 00000000000 000000000000 0000000000000 00000000000000 000000000000000 0000000000000000 00000000000000000 000000000000000000 0000000000000000000 00000000000000000000 000000000000000000000 0000000000000000000000 00000000000000000000000 000000000000000000000000 0000000000000000000000000".split(" "),_=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],w=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];o.prototype.toString=function(t,e){t=t||10,e=0|e||1;var n;if(16===t||"hex"===t){n="";for(var i=0,o=0,a=0;a<this.length;a++){var s=this.words[a],c=(16777215&(s<<i|o)).toString(16),o=s>>>24-i&16777215;n=0!==o||a!==this.length-1?g[6-c.length]+c+n:c+n,i+=2,i>=26&&(i-=26,a--)}for(0!==o&&(n=o.toString(16)+n);0!==n.length%e;)n="0"+n;return 0!==this.negative&&(n="-"+n),n}if(t===(0|t)&&t>=2&&36>=t){for(i=_[t],o=w[t],n="",a=this.clone(),a.negative=0;!a.isZero();)s=a.modn(o).toString(t),a=a.idivn(o),n=a.isZero()?s+n:g[i-s.length]+s+n;for(this.isZero()&&(n="0"+n);0!==n.length%e;)n="0"+n;return 0!==this.negative&&(n="-"+n),n}r(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t,e=this.bitLength();return 26>=e?t=this.words[0]:52>=e?t=67108864*this.words[1]+this.words[0]:53===e?t=4503599627370496+67108864*this.words[1]+this.words[0]:r(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,e){return r("undefined"!=typeof v),this.toArrayLike(v,t,e)},o.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},o.prototype.toArrayLike=function(t,e,n){var i=this.byteLength();n=n||Math.max(1,i),r(n>=i,"byte array longer than desired length"),r(n>0,"Requested array length <= 0"),this.strip();var o="le"===e;if(t=new t(n),e=this.clone(),o){for(o=0;!e.isZero();o++)i=e.andln(255),e.iushrn(8),t[o]=i;for(;n>o;o++)t[o]=0}else{for(o=0;n-i>o;o++)t[o]=0;for(o=0;!e.isZero();o++)i=e.andln(255),e.iushrn(8),t[n-o-1]=i}return t},o.prototype._countBits=Math.clz32?function(t){return 32-Math.clz32(t)}:function(t){var e=0;return t>=4096&&(e+=13,t>>>=13),t>=64&&(e+=7,t>>>=7),t>=8&&(e+=4,t>>>=4),t>=2&&(e+=2,t>>>=2),e+t},o.prototype._zeroBits=function(t){if(0===t)return 26;var e=0;return 0===(8191&t)&&(e+=13,t>>>=13),0===(127&t)&&(e+=7,t>>>=7),0===(15&t)&&(e+=4,t>>>=4),0===(3&t)&&(e+=2,t>>>=2),0===(1&t)&&e++,e},o.prototype.bitLength=function(){var t=this._countBits(this.words[this.length-1]);return 26*(this.length-1)+t},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var n=this._zeroBits(this.words[e]),t=t+n;if(26!==n)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]|=t.words[e];return this.strip()},o.prototype.ior=function(t){return r(0===(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var n=0;n<e.length;n++)this.words[n]&=t.words[n];return this.length=e.length,this.strip()},o.prototype.iand=function(t){return r(0===(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var e;this.length>t.length?e=this:(e=t,t=this);for(var n=0;n<t.length;n++)this.words[n]=e.words[n]^t.words[n];if(this!==e)for(;n<e.length;n++)this.words[n]=e.words[n];return this.length=e.length,this.strip()},o.prototype.ixor=function(t){return r(0===(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){r("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26);t%=26,this._expand(e),t>0&&e--;for(var n=0;e>n;n++)this.words[n]=67108863&~this.words[n];return t>0&&(this.words[n]=~this.words[n]&67108863>>26-t),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,e){r("number"==typeof t&&t>=0);var n=t/26|0,i=t%26;return this._expand(n+1),this.words[n]=e?this.words[n]|1<<i:this.words[n]&~(1<<i),this.strip()},o.prototype.iadd=function(t){var e;if(0!==this.negative&&0===t.negative)return this.negative=0,this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();var n;this.length>t.length?n=this:(n=t,t=this);for(var r=e=0;r<t.length;r++)e=(0|n.words[r])+(0|t.words[r])+e,this.words[r]=67108863&e,e>>>=26;for(;0!==e&&r<n.length;r++)e=(0|n.words[r])+e,this.words[r]=67108863&e,e>>>=26;if(this.length=n.length,0!==e)this.words[this.length]=e,this.length++;else if(n!==this)for(;r<n.length;r++)this.words[r]=n.words[r];return this},o.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();if(e=this.cmp(t),0===e)return this.negative=0,this.length=1,this.words[0]=0,this;var n;e>0?n=this:(n=t,t=this);for(var r=0,i=0;i<t.length;i++)e=(0|n.words[i])-(0|t.words[i])+r,r=e>>26,this.words[i]=67108863&e;for(;0!==r&&i<n.length;i++)e=(0|n.words[i])+r,r=e>>26,this.words[i]=67108863&e;if(0===r&&i<n.length&&n!==this)for(;i<n.length;i++)this.words[i]=n.words[i];return this.length=Math.max(this.length,i),n!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var x=function(t,e,n){var r,i=t.words,o=e.words,a=n.words,s=0,c=0|i[0],f=8191&c,u=c>>>13,d=0|i[1],c=8191&d,p=d>>>13,h=0|i[2],d=8191&h,l=h>>>13,b=0|i[3],h=8191&b,m=b>>>13,v=0|i[4],b=8191&v,y=v>>>13,g=0|i[5],v=8191&g,_=g>>>13,w=0|i[6],g=8191&w,x=w>>>13,k=0|i[7],w=8191&k,E=k>>>13,S=0|i[8],k=8191&S,S=S>>>13,M=0|i[9],i=8191&M,M=M>>>13,A=0|o[0],T=8191&A,j=A>>>13,I=0|o[1],A=8191&I,R=I>>>13,O=0|o[2],I=8191&O,B=O>>>13,N=0|o[3],O=8191&N,C=N>>>13,P=0|o[4],N=8191&P,q=P>>>13,U=0|o[5],P=8191&U,z=U>>>13,L=0|o[6],U=8191&L,D=L>>>13,F=0|o[7],L=8191&F,H=F>>>13,G=0|o[8],F=8191&G,G=G>>>13,K=0|o[9],o=8191&K,K=K>>>13;n.negative=t.negative^e.negative,n.length=19,r=Math.imul(f,T),t=Math.imul(f,j),t+=Math.imul(u,T),e=Math.imul(u,j);var Y=s+r+((8191&t)<<13),s=e+(t>>>13)+(Y>>>26),Y=67108863&Y;r=Math.imul(c,T),t=Math.imul(c,j),t+=Math.imul(p,T),e=Math.imul(p,j),r+=Math.imul(f,A),t+=Math.imul(f,R),t+=Math.imul(u,A),e+=Math.imul(u,R);var X=s+r+((8191&t)<<13),s=e+(t>>>13)+(X>>>26),X=67108863&X;r=Math.imul(d,T),t=Math.imul(d,j),t+=Math.imul(l,T),e=Math.imul(l,j),r+=Math.imul(c,A),t+=Math.imul(c,R),t+=Math.imul(p,A),e+=Math.imul(p,R),r+=Math.imul(f,I),t+=Math.imul(f,B),t+=Math.imul(u,I),e+=Math.imul(u,B);var J=s+r+((8191&t)<<13),s=e+(t>>>13)+(J>>>26),J=67108863&J;r=Math.imul(h,T),t=Math.imul(h,j),t+=Math.imul(m,T),e=Math.imul(m,j),r+=Math.imul(d,A),t+=Math.imul(d,R),t+=Math.imul(l,A),e+=Math.imul(l,R),r+=Math.imul(c,I),t+=Math.imul(c,B),t+=Math.imul(p,I),e+=Math.imul(p,B),r+=Math.imul(f,O),t+=Math.imul(f,C),t+=Math.imul(u,O),e+=Math.imul(u,C);var V=s+r+((8191&t)<<13),s=e+(t>>>13)+(V>>>26),V=67108863&V;r=Math.imul(b,T),t=Math.imul(b,j),t+=Math.imul(y,T),e=Math.imul(y,j),r+=Math.imul(h,A),t+=Math.imul(h,R),t+=Math.imul(m,A),e+=Math.imul(m,R),r+=Math.imul(d,I),t+=Math.imul(d,B),t+=Math.imul(l,I),e+=Math.imul(l,B),r+=Math.imul(c,O),t+=Math.imul(c,C),t+=Math.imul(p,O),e+=Math.imul(p,C),r+=Math.imul(f,N),t+=Math.imul(f,q),t+=Math.imul(u,N),e+=Math.imul(u,q);var W=s+r+((8191&t)<<13),s=e+(t>>>13)+(W>>>26),W=67108863&W;r=Math.imul(v,T),t=Math.imul(v,j),t+=Math.imul(_,T),e=Math.imul(_,j),r+=Math.imul(b,A),t+=Math.imul(b,R),t+=Math.imul(y,A),e+=Math.imul(y,R),r+=Math.imul(h,I),t+=Math.imul(h,B),t+=Math.imul(m,I),e+=Math.imul(m,B),r+=Math.imul(d,O),t+=Math.imul(d,C),t+=Math.imul(l,O),e+=Math.imul(l,C),r+=Math.imul(c,N),t+=Math.imul(c,q),t+=Math.imul(p,N),e+=Math.imul(p,q),r+=Math.imul(f,P),t+=Math.imul(f,z),t+=Math.imul(u,P),e+=Math.imul(u,z);var Q=s+r+((8191&t)<<13),s=e+(t>>>13)+(Q>>>26),Q=67108863&Q;r=Math.imul(g,T),t=Math.imul(g,j),t+=Math.imul(x,T),e=Math.imul(x,j),r+=Math.imul(v,A),t+=Math.imul(v,R),t+=Math.imul(_,A),e+=Math.imul(_,R),r+=Math.imul(b,I),t+=Math.imul(b,B),t+=Math.imul(y,I),e+=Math.imul(y,B),r+=Math.imul(h,O),t+=Math.imul(h,C),t+=Math.imul(m,O),e+=Math.imul(m,C),r+=Math.imul(d,N),t+=Math.imul(d,q),t+=Math.imul(l,N),e+=Math.imul(l,q),r+=Math.imul(c,P),t+=Math.imul(c,z),t+=Math.imul(p,P),e+=Math.imul(p,z),r+=Math.imul(f,U),t+=Math.imul(f,D),t+=Math.imul(u,U),e+=Math.imul(u,D);var Z=s+r+((8191&t)<<13),s=e+(t>>>13)+(Z>>>26),Z=67108863&Z;r=Math.imul(w,T),t=Math.imul(w,j),t+=Math.imul(E,T),e=Math.imul(E,j),r+=Math.imul(g,A),t+=Math.imul(g,R),t+=Math.imul(x,A),e+=Math.imul(x,R),r+=Math.imul(v,I),t+=Math.imul(v,B),t+=Math.imul(_,I),e+=Math.imul(_,B),r+=Math.imul(b,O),t+=Math.imul(b,C),t+=Math.imul(y,O),e+=Math.imul(y,C),r+=Math.imul(h,N),t+=Math.imul(h,q),t+=Math.imul(m,N),e+=Math.imul(m,q),r+=Math.imul(d,P),t+=Math.imul(d,z),t+=Math.imul(l,P),e+=Math.imul(l,z),r+=Math.imul(c,U),t+=Math.imul(c,D),t+=Math.imul(p,U),e+=Math.imul(p,D),r+=Math.imul(f,L),t+=Math.imul(f,H),t+=Math.imul(u,L),e+=Math.imul(u,H);var $=s+r+((8191&t)<<13),s=e+(t>>>13)+($>>>26),$=67108863&$;r=Math.imul(k,T),t=Math.imul(k,j),t+=Math.imul(S,T),e=Math.imul(S,j),r+=Math.imul(w,A),t+=Math.imul(w,R),t+=Math.imul(E,A),e+=Math.imul(E,R),r+=Math.imul(g,I),t+=Math.imul(g,B),t+=Math.imul(x,I),e+=Math.imul(x,B),r+=Math.imul(v,O),t+=Math.imul(v,C),t+=Math.imul(_,O),e+=Math.imul(_,C),r+=Math.imul(b,N),t+=Math.imul(b,q),t+=Math.imul(y,N),e+=Math.imul(y,q),r+=Math.imul(h,P),t+=Math.imul(h,z),t+=Math.imul(m,P),e+=Math.imul(m,z),r+=Math.imul(d,U),t+=Math.imul(d,D),t+=Math.imul(l,U),e+=Math.imul(l,D),r+=Math.imul(c,L),t+=Math.imul(c,H),t+=Math.imul(p,L),e+=Math.imul(p,H),r+=Math.imul(f,F),t+=Math.imul(f,G),t+=Math.imul(u,F),e+=Math.imul(u,G);var tt=s+r+((8191&t)<<13),s=e+(t>>>13)+(tt>>>26),tt=67108863&tt;return r=Math.imul(i,T),t=Math.imul(i,j),t+=Math.imul(M,T),e=Math.imul(M,j),r+=Math.imul(k,A),t+=Math.imul(k,R),t+=Math.imul(S,A),e+=Math.imul(S,R),r+=Math.imul(w,I),t+=Math.imul(w,B),t+=Math.imul(E,I),e+=Math.imul(E,B),r+=Math.imul(g,O),t+=Math.imul(g,C),t+=Math.imul(x,O),e+=Math.imul(x,C),r+=Math.imul(v,N),t+=Math.imul(v,q),t+=Math.imul(_,N),e+=Math.imul(_,q),r+=Math.imul(b,P),t+=Math.imul(b,z),t+=Math.imul(y,P),e+=Math.imul(y,z),r+=Math.imul(h,U),t+=Math.imul(h,D),t+=Math.imul(m,U),e+=Math.imul(m,D),r+=Math.imul(d,L),t+=Math.imul(d,H),t+=Math.imul(l,L),e+=Math.imul(l,H),r+=Math.imul(c,F),t+=Math.imul(c,G),t+=Math.imul(p,F),e+=Math.imul(p,G),r+=Math.imul(f,o),t+=Math.imul(f,K),t+=Math.imul(u,o),e+=Math.imul(u,K),f=s+r+((8191&t)<<13),s=e+(t>>>13)+(f>>>26),f&=67108863,r=Math.imul(i,A),t=Math.imul(i,R),t+=Math.imul(M,A),e=Math.imul(M,R),r+=Math.imul(k,I),t+=Math.imul(k,B),t+=Math.imul(S,I),e+=Math.imul(S,B),r+=Math.imul(w,O),t+=Math.imul(w,C),t+=Math.imul(E,O),e+=Math.imul(E,C),r+=Math.imul(g,N),t+=Math.imul(g,q),t+=Math.imul(x,N),e+=Math.imul(x,q),r+=Math.imul(v,P),t+=Math.imul(v,z),t+=Math.imul(_,P),e+=Math.imul(_,z),r+=Math.imul(b,U),t+=Math.imul(b,D),t+=Math.imul(y,U),e+=Math.imul(y,D),r+=Math.imul(h,L),t+=Math.imul(h,H),t+=Math.imul(m,L),e+=Math.imul(m,H),r+=Math.imul(d,F),t+=Math.imul(d,G),t+=Math.imul(l,F),e+=Math.imul(l,G),r+=Math.imul(c,o),t+=Math.imul(c,K),t+=Math.imul(p,o),e+=Math.imul(p,K),c=s+r+((8191&t)<<13),s=e+(t>>>13)+(c>>>26),c&=67108863,r=Math.imul(i,I),t=Math.imul(i,B),t+=Math.imul(M,I),e=Math.imul(M,B),r+=Math.imul(k,O),t+=Math.imul(k,C),t+=Math.imul(S,O),e+=Math.imul(S,C),r+=Math.imul(w,N),t+=Math.imul(w,q),t+=Math.imul(E,N),e+=Math.imul(E,q),r+=Math.imul(g,P),t+=Math.imul(g,z),t+=Math.imul(x,P),e+=Math.imul(x,z),r+=Math.imul(v,U),t+=Math.imul(v,D),t+=Math.imul(_,U),e+=Math.imul(_,D),r+=Math.imul(b,L),t+=Math.imul(b,H),t+=Math.imul(y,L),e+=Math.imul(y,H),r+=Math.imul(h,F),t+=Math.imul(h,G),t+=Math.imul(m,F),e+=Math.imul(m,G),r+=Math.imul(d,o),t+=Math.imul(d,K),t+=Math.imul(l,o),e+=Math.imul(l,K),d=s+r+((8191&t)<<13),s=e+(t>>>13)+(d>>>26),d&=67108863,r=Math.imul(i,O),t=Math.imul(i,C),t+=Math.imul(M,O),e=Math.imul(M,C),r+=Math.imul(k,N),t+=Math.imul(k,q),t+=Math.imul(S,N),e+=Math.imul(S,q),r+=Math.imul(w,P),t+=Math.imul(w,z),t+=Math.imul(E,P),e+=Math.imul(E,z),r+=Math.imul(g,U),t+=Math.imul(g,D),t+=Math.imul(x,U),e+=Math.imul(x,D),r+=Math.imul(v,L),t+=Math.imul(v,H),t+=Math.imul(_,L),e+=Math.imul(_,H),r+=Math.imul(b,F),t+=Math.imul(b,G),t+=Math.imul(y,F),e+=Math.imul(y,G),r+=Math.imul(h,o),t+=Math.imul(h,K),t+=Math.imul(m,o),e+=Math.imul(m,K),h=s+r+((8191&t)<<13),s=e+(t>>>13)+(h>>>26),h&=67108863,r=Math.imul(i,N),t=Math.imul(i,q),t+=Math.imul(M,N),e=Math.imul(M,q),r+=Math.imul(k,P),t+=Math.imul(k,z),t+=Math.imul(S,P),e+=Math.imul(S,z),r+=Math.imul(w,U),t+=Math.imul(w,D),t+=Math.imul(E,U),e+=Math.imul(E,D),r+=Math.imul(g,L),t+=Math.imul(g,H),t+=Math.imul(x,L),e+=Math.imul(x,H),r+=Math.imul(v,F),t+=Math.imul(v,G),t+=Math.imul(_,F),e+=Math.imul(_,G),r+=Math.imul(b,o),t+=Math.imul(b,K),t+=Math.imul(y,o),e+=Math.imul(y,K),b=s+r+((8191&t)<<13),s=e+(t>>>13)+(b>>>26),b&=67108863,r=Math.imul(i,P),t=Math.imul(i,z),t+=Math.imul(M,P),e=Math.imul(M,z),r+=Math.imul(k,U),t+=Math.imul(k,D),t+=Math.imul(S,U),e+=Math.imul(S,D),r+=Math.imul(w,L),t+=Math.imul(w,H),t+=Math.imul(E,L),e+=Math.imul(E,H),r+=Math.imul(g,F),t+=Math.imul(g,G),t+=Math.imul(x,F),e+=Math.imul(x,G),r+=Math.imul(v,o),t+=Math.imul(v,K),t+=Math.imul(_,o),e+=Math.imul(_,K),v=s+r+((8191&t)<<13),s=e+(t>>>13)+(v>>>26),v&=67108863,r=Math.imul(i,U),t=Math.imul(i,D),t+=Math.imul(M,U),e=Math.imul(M,D),r+=Math.imul(k,L),t+=Math.imul(k,H),t+=Math.imul(S,L),e+=Math.imul(S,H),r+=Math.imul(w,F),t+=Math.imul(w,G),t+=Math.imul(E,F),e+=Math.imul(E,G),r+=Math.imul(g,o),t+=Math.imul(g,K),t+=Math.imul(x,o),e+=Math.imul(x,K),g=s+r+((8191&t)<<13),s=e+(t>>>13)+(g>>>26),g&=67108863,r=Math.imul(i,L),t=Math.imul(i,H),t+=Math.imul(M,L),e=Math.imul(M,H),r+=Math.imul(k,F),t+=Math.imul(k,G),t+=Math.imul(S,F),e+=Math.imul(S,G),r+=Math.imul(w,o),t+=Math.imul(w,K),t+=Math.imul(E,o),e+=Math.imul(E,K),w=s+r+((8191&t)<<13),s=e+(t>>>13)+(w>>>26),w&=67108863,r=Math.imul(i,F),t=Math.imul(i,G),t+=Math.imul(M,F),e=Math.imul(M,G),r+=Math.imul(k,o),t+=Math.imul(k,K),t+=Math.imul(S,o),e+=Math.imul(S,K),k=s+r+((8191&t)<<13),s=e+(t>>>13)+(k>>>26),k&=67108863,r=Math.imul(i,o),t=Math.imul(i,K),t+=Math.imul(M,o),e=Math.imul(M,K),u=s+r+((8191&t)<<13),s=e+(t>>>13)+(u>>>26),a[0]=Y,a[1]=X,a[2]=J,a[3]=V,a[4]=W,a[5]=Q,a[6]=Z,a[7]=$,a[8]=tt,a[9]=f,a[10]=c,a[11]=d,a[12]=h,a[13]=b,a[14]=v,a[15]=g,a[16]=w,a[17]=k,a[18]=67108863&u,0!==s&&(a[19]=s,n.length++),n};Math.imul||(x=c),o.prototype.mulTo=function(t,e){var n;if(n=this.length+t.length,10===this.length&&10===t.length)n=x(this,t,e);else if(63>n)n=c(this,t,e);else if(1024>n){e.negative=t.negative^this.negative,e.length=this.length+t.length;for(var r=0,i=n=0;i<e.length-1;i++){var o=n;n=0;for(var r=67108863&r,a=Math.min(i,t.length-1),s=Math.max(0,i-this.length+1);a>=s;s++){var u=(0|this.words[i-s])*(0|t.words[s]),d=67108863&u,o=o+(u/67108864|0)|0,d=d+r|0,r=67108863&d,o=o+(d>>>26)|0;n+=o>>>26,o&=67108863}e.words[i]=r,r=o}0!==r?e.words[i]=r:e.length--,n=e.strip()}else n=(new f).mulp(this,t,e);return n},f.prototype.makeRBT=function(t){for(var e=Array(t),n=o.prototype._countBits(t)-1,r=0;t>r;r++)e[r]=this.revBin(r,n,t);return e},f.prototype.revBin=function(t,e,n){if(0===t||t===n-1)return t;for(var r=n=0;e>r;r++)n|=(1&t)<<e-r-1,t>>=1;return n},f.prototype.permute=function(t,e,n,r,i,o){for(var a=0;o>a;a++)r[a]=e[t[a]],i[a]=n[t[a]]},f.prototype.transform=function(t,e,n,r,i,o){for(this.permute(o,t,e,n,r,i),t=1;i>t;t<<=1){e=t<<1,o=Math.cos(2*Math.PI/e);for(var a=Math.sin(2*Math.PI/e),s=0;i>s;s+=e)for(var c=o,f=a,u=0;t>u;u++){var d=n[s+u],p=r[s+u],h=n[s+u+t],l=r[s+u+t],b=c*h-f*l,l=c*l+f*h,h=b;n[s+u]=d+h,r[s+u]=p+l,n[s+u+t]=d-h,r[s+u+t]=p-l,u!==e&&(b=o*c-a*f,f=o*f+a*c,c=b)}}},f.prototype.guessLen13b=function(t,e){for(var n=1|Math.max(e,t),r=1&n,i=0,n=n/2|0;n;n>>>=1)i++;return 1<<i+1+r},f.prototype.conjugate=function(t,e,n){if(!(1>=n))for(var r=0;n/2>r;r++){var i=t[r];t[r]=t[n-r-1],t[n-r-1]=i,i=e[r],e[r]=-e[n-r-1],e[n-r-1]=-i}},f.prototype.normalize13b=function(t,e){for(var n=0,r=0;e/2>r;r++)n=8192*Math.round(t[2*r+1]/e)+Math.round(t[2*r]/e)+n,t[r]=67108863&n,n=67108864>n?0:n/67108864|0;return t},f.prototype.convert13b=function(t,e,n,i){for(var o=0,a=0;e>a;a++)o+=0|t[a],n[2*a]=8191&o,o>>>=13,n[2*a+1]=8191&o,o>>>=13;for(a=2*e;i>a;++a)n[a]=0;r(0===o),r(0===(-8192&o))},f.prototype.stub=function(t){for(var e=Array(t),n=0;t>n;n++)e[n]=0;return e},f.prototype.mulp=function(t,e,n){var r=2*this.guessLen13b(t.length,e.length),i=this.makeRBT(r),o=this.stub(r),a=Array(r),s=Array(r),c=Array(r),f=Array(r),u=Array(r),d=Array(r),p=n.words;for(p.length=r,this.convert13b(t.words,t.length,a,r),this.convert13b(e.words,e.length,f,r),this.transform(a,o,s,c,r,i),this.transform(f,o,u,d,r,i),a=0;r>a;a++)f=s[a]*u[a]-c[a]*d[a],c[a]=s[a]*d[a]+c[a]*u[a],s[a]=f;return this.conjugate(s,c,r),this.transform(s,c,p,o,r,i),this.conjugate(p,o,r),this.normalize13b(p,r),n.negative=t.negative^e.negative,n.length=t.length+e.length,n.strip()},o.prototype.mul=function(t){var e=new o(null);return e.words=Array(this.length+t.length),this.mulTo(t,e)},o.prototype.mulf=function(t){var e=new o(null);return e.words=Array(this.length+t.length),(new f).mulp(this,t,e)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){r("number"==typeof t),r(67108864>t);for(var e=0,n=0;n<this.length;n++){var i=(0|this.words[n])*t,o=(67108863&i)+(67108863&e),e=e>>26,e=e+(i/67108864|0),e=e+(o>>>26);this.words[n]=67108863&o}return 0!==e&&(this.words[n]=e,this.length++),this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){for(var e=Array(t.bitLength()),n=0;n<e.length;n++){var r=n%26;e[n]=(t.words[n/26|0]&1<<r)>>>r}if(0===e.length)return new o(1);for(t=this,n=0;n<e.length&&0===e[n];n++,t=t.sqr());if(++n<e.length)for(r=t.sqr();n<e.length;n++,r=r.sqr())0!==e[n]&&(t=t.mul(r));return t},o.prototype.iushln=function(t){r("number"==typeof t&&t>=0);var e=t%26;t=(t-e)/26;var n,i=67108863>>>26-e<<26-e;if(0!==e){var o=0;for(n=0;n<this.length;n++){var a=this.words[n]&i;this.words[n]=(0|this.words[n])-a<<e|o,o=a>>>26-e}o&&(this.words[n]=o,this.length++)}if(0!==t){for(n=this.length-1;n>=0;n--)this.words[n+t]=this.words[n];for(n=0;t>n;n++)this.words[n]=0;this.length+=t}return this.strip()},o.prototype.ishln=function(t){return r(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,e,n){r("number"==typeof t&&t>=0);var i=t%26,o=Math.min((t-i)/26,this.length);if(t=67108863^67108863>>>i<<i,e=Math.max(0,(e?(e-e%26)/26:0)-o),n){for(var a=0;o>a;a++)n.words[a]=this.words[a];n.length=o}if(0!==o)if(this.length>o)for(this.length-=o,a=0;a<this.length;a++)this.words[a]=this.words[a+o];else this.words[0]=0,this.length=1;for(o=0,a=this.length-1;a>=0&&(0!==o||a>=e);a--){var s=0|this.words[a];this.words[a]=o<<26-i|s>>>i,
o=s&t}return n&&0!==o&&(n.words[n.length++]=o),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,e,n){return r(0===this.negative),this.iushrn(t,e,n)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){r("number"==typeof t&&t>=0);var e=t%26;return t=(t-e)/26,this.length<=t?!1:!!(this.words[t]&1<<e)},o.prototype.imaskn=function(t){r("number"==typeof t&&t>=0);var e=t%26;return t=(t-e)/26,r(0===this.negative,"imaskn works only with positive numbers"),0!==e&&t++,this.length=Math.min(t,this.length),0!==e&&(this.words[this.length-1]&=67108863^67108863>>>e<<e),this.strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return r("number"==typeof t),r(67108864>t),0>t?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){for(this.words[0]+=t,t=0;t<this.length&&67108864<=this.words[t];t++)this.words[t]-=67108864,t===this.length-1?this.words[t+1]=1:this.words[t+1]++;return this.length=Math.max(this.length,t+1),this},o.prototype.isubn=function(t){if(r("number"==typeof t),r(67108864>t),0>t)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&0>this.words[0])this.words[0]=-this.words[0],this.negative=1;else for(t=0;t<this.length&&0>this.words[t];t++)this.words[t]+=67108864,--this.words[t+1];return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,e,n){var i;this._expand(t.length+n);var o,a=0;for(i=0;i<t.length;i++)o=(0|this.words[i+n])+a,a=(0|t.words[i])*e,o-=67108863&a,a=(o>>26)-(a/67108864|0),this.words[i+n]=67108863&o;for(;i<this.length-n;i++)o=(0|this.words[i+n])+a,a=o>>26,this.words[i+n]=67108863&o;if(0===a)return this.strip();for(r(-1===a),i=a=0;i<this.length;i++)o=-(0|this.words[i])+a,a=o>>26,this.words[i]=67108863&o;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,e){var n,r=this.clone(),i=t,a=0|i.words[i.length-1];n=26-this._countBits(a),0!==n&&(i=i.ushln(n),r.iushln(n),a=0|i.words[i.length-1]);var s,c=r.length-i.length;if("mod"!==e){s=new o(null),s.length=c+1,s.words=Array(s.length);for(var f=0;f<s.length;f++)s.words[f]=0}for(f=r.clone()._ishlnsubmul(i,1,c),0===f.negative&&(r=f,s&&(s.words[c]=1)),--c;c>=0;c--){for(f=67108864*(0|r.words[i.length+c])+(0|r.words[i.length+c-1]),f=Math.min(f/a|0,67108863),r._ishlnsubmul(i,f,c);0!==r.negative;)f--,r.negative=0,r._ishlnsubmul(i,1,c),r.isZero()||(r.negative^=1);s&&(s.words[c]=f)}return s&&s.strip(),r.strip(),"div"!==e&&0!==n&&r.iushrn(n),{div:s||null,mod:r}},o.prototype.divmod=function(t,e,n){if(r(!t.isZero()),this.isZero())return{div:new o(0),mod:new o(0)};var i,a,s;return 0!==this.negative&&0===t.negative?(s=this.neg().divmod(t,e),"mod"!==e&&(i=s.div.neg()),"div"!==e&&(a=s.mod.neg(),n&&0!==a.negative&&a.iadd(t)),{div:i,mod:a}):0===this.negative&&0!==t.negative?(s=this.divmod(t.neg(),e),"mod"!==e&&(i=s.div.neg()),{div:i,mod:s.mod}):0!==(this.negative&t.negative)?(s=this.neg().divmod(t.neg(),e),"div"!==e&&(a=s.mod.neg(),n&&0!==a.negative&&a.isub(t)),{div:s.div,mod:a}):t.length>this.length||0>this.cmp(t)?{div:new o(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,e)},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var n=0!==e.div.negative?e.mod.isub(t):e.mod,r=t.ushrn(1);return t=t.andln(1),n=n.cmp(r),0>n||1===t&&0===n?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},o.prototype.modn=function(t){r(67108863>=t);for(var e=67108864%t,n=0,i=this.length-1;i>=0;i--)n=(e*n+(0|this.words[i]))%t;return n},o.prototype.idivn=function(t){r(67108863>=t);for(var e=0,n=this.length-1;n>=0;n--)e=(0|this.words[n])+67108864*e,this.words[n]=e/t|0,e%=t;return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){r(0===t.negative),r(!t.isZero());var e=this,n=t.clone(),e=0!==e.negative?e.umod(t):e.clone();t=new o(1);for(var i=new o(0),a=new o(0),s=new o(1),c=0;e.isEven()&&n.isEven();)e.iushrn(1),n.iushrn(1),++c;for(var f=n.clone(),u=e.clone();!e.isZero();){for(var d=0,p=1;0===(e.words[0]&p)&&26>d;++d,p<<=1);if(d>0)for(e.iushrn(d);0<d--;)(t.isOdd()||i.isOdd())&&(t.iadd(f),i.isub(u)),t.iushrn(1),i.iushrn(1);for(d=0,p=1;0===(n.words[0]&p)&&26>d;++d,p<<=1);if(d>0)for(n.iushrn(d);0<d--;)(a.isOdd()||s.isOdd())&&(a.iadd(f),s.isub(u)),a.iushrn(1),s.iushrn(1);0<=e.cmp(n)?(e.isub(n),t.isub(a),i.isub(s)):(n.isub(e),a.isub(t),s.isub(i))}return{a:a,b:s,gcd:n.iushln(c)}},o.prototype._invmp=function(t){r(0===t.negative),r(!t.isZero());for(var e=this,n=t.clone(),e=0!==e.negative?e.umod(t):e.clone(),i=new o(1),a=new o(0),s=n.clone();0<e.cmpn(1)&&0<n.cmpn(1);){for(var c=0,f=1;0===(e.words[0]&f)&&26>c;++c,f<<=1);if(c>0)for(e.iushrn(c);0<c--;)i.isOdd()&&i.iadd(s),i.iushrn(1);for(c=0,f=1;0===(n.words[0]&f)&&26>c;++c,f<<=1);if(c>0)for(n.iushrn(c);0<c--;)a.isOdd()&&a.iadd(s),a.iushrn(1);0<=e.cmp(n)?(e.isub(n),i.isub(a)):(n.isub(e),a.isub(i))}return e=0===e.cmpn(1)?i:a,0>e.cmpn(0)&&e.iadd(t),e},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone();t=t.clone(),e.negative=0;for(var n=t.negative=0;e.isEven()&&t.isEven();n++)e.iushrn(1),t.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;t.isEven();)t.iushrn(1);var r=e.cmp(t);if(0>r)r=e,e=t,t=r;else if(0===r||0===t.cmpn(1))break;e.isub(t)}return t.iushln(n)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return 0===(1&this.words[0])},o.prototype.isOdd=function(){return 1===(1&this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){r("number"==typeof t);var e=t%26;if(t=(t-e)/26,e=1<<e,this.length<=t)return this._expand(t+1),this.words[t]|=e,this;for(;0!==e&&t<this.length;t++){var n=0|this.words[t],n=n+e,e=n>>>26,n=67108863&n;this.words[t]=n}return 0!==e&&(this.words[t]=e,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var e=0>t;return 0===this.negative||e?0===this.negative&&e?1:(this.strip(),1<this.length?t=1:(e&&(t=-t),r(67108863>=t,"Number is too big"),e=0|this.words[0],t=e===t?0:t>e?-1:1),0!==this.negative?0|-t:t):-1},o.prototype.cmp=function(t){return 0!==this.negative&&0===t.negative?-1:0===this.negative&&0!==t.negative?1:(t=this.ucmp(t),0!==this.negative?0|-t:t)},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,n=this.length-1;n>=0;n--){var r=0|this.words[n],i=0|t.words[n];if(r!==i){i>r?e=-1:r>i&&(e=1);break}}return e},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return 0<=this.cmpn(t)},o.prototype.gte=function(t){return 0<=this.cmp(t)},o.prototype.ltn=function(t){return-1===this.cmpn(t)},o.prototype.lt=function(t){return-1===this.cmp(t)},o.prototype.lten=function(t){return 0>=this.cmpn(t)},o.prototype.lte=function(t){return 0>=this.cmp(t)},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new b(t)},o.prototype.toRed=function(t){return r(!this.red,"Already a number in reduction context"),r(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return r(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return r(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return r(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return r(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return r(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return r(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return r(this.red,"redShl works only with red numbers"),this.red.ushl(this,t)},o.prototype.redMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return r(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return r(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return r(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return r(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return r(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return r(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var k={k256:null,p224:null,p192:null,p25519:null};u.prototype._tmp=function(){var t=new o(null);return t.words=Array(Math.ceil(this.n/13)),t},u.prototype.ireduce=function(t){var e;do this.split(t,this.tmp),t=this.imulK(t),t=t.iadd(this.tmp),e=t.bitLength();while(e>this.n);return e=e<this.n?-1:t.ucmp(this.p),0===e?(t.words[0]=0,t.length=1):e>0?t.isub(this.p):t.strip(),t},u.prototype.split=function(t,e){t.iushrn(this.n,0,e)},u.prototype.imulK=function(t){return t.imul(this.k)},i(d,u),d.prototype.split=function(t,e){for(var n=Math.min(t.length,9),r=0;n>r;r++)e.words[r]=t.words[r];if(e.length=n,9>=t.length)t.words[0]=0,t.length=1;else{for(n=t.words[9],e.words[e.length++]=4194303&n,r=10;r<t.length;r++){var i=0|t.words[r];t.words[r-10]=(4194303&i)<<4|n>>>22,n=i}n>>>=22,t.words[r-10]=n,t.length=0===n&&10<t.length?t.length-10:t.length-9}},d.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,n=0;n<t.length;n++){var r=0|t.words[n],e=e+977*r;t.words[n]=67108863&e,e=64*r+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(p,u),i(h,u),i(l,u),l.prototype.imulK=function(t){for(var e=0,n=0;n<t.length;n++){var e=19*(0|t.words[n])+e,r=67108863&e,e=e>>>26;t.words[n]=r}return 0!==e&&(t.words[t.length++]=e),t},o._prime=function(t){if(k[t])return k[t];var e;if("k256"===t)e=new d;else if("p224"===t)e=new p;else if("p192"===t)e=new h;else{if("p25519"!==t)throw Error("Unknown prime "+t);e=new l}return k[t]=e},b.prototype._verify1=function(t){r(0===t.negative,"red works only with positives"),r(t.red,"red works only with red numbers")},b.prototype._verify2=function(t,e){r(0===(t.negative|e.negative),"red works only with positives"),r(t.red&&t.red===e.red,"red works only with red numbers")},b.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},b.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},b.prototype.add=function(t,e){this._verify2(t,e);var n=t.add(e);return 0<=n.cmp(this.m)&&n.isub(this.m),n._forceRed(this)},b.prototype.iadd=function(t,e){this._verify2(t,e);var n=t.iadd(e);return 0<=n.cmp(this.m)&&n.isub(this.m),n},b.prototype.sub=function(t,e){this._verify2(t,e);var n=t.sub(e);return 0>n.cmpn(0)&&n.iadd(this.m),n._forceRed(this)},b.prototype.isub=function(t,e){this._verify2(t,e);var n=t.isub(e);return 0>n.cmpn(0)&&n.iadd(this.m),n},b.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},b.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},b.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},b.prototype.isqr=function(t){return this.imul(t,t.clone())},b.prototype.sqr=function(t){return this.mul(t,t)},b.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(r(1===e%2),3===e)return e=this.m.add(new o(1)).iushrn(2),this.pow(t,e);for(var n=this.m.subn(1),i=0;!n.isZero()&&0===n.andln(1);)i++,n.iushrn(1);r(!n.isZero());for(var e=new o(1).toRed(this),a=e.redNeg(),s=this.m.subn(1).iushrn(1),c=this.m.bitLength(),c=new o(2*c*c).toRed(this);0!==this.pow(c,s).cmp(a);)c.redIAdd(a);for(s=this.pow(c,n),a=this.pow(t,n.addn(1).iushrn(1)),t=this.pow(t,n),n=i;0!==t.cmp(e);){for(c=t,i=0;0!==c.cmp(e);i++)c=c.redSqr();r(n>i),s=this.pow(s,new o(1).iushln(n-i-1)),a=a.redMul(s),s=s.redSqr(),t=t.redMul(s),n=i}return a},b.prototype.invm=function(t){return t=t._invmp(this.m),0!==t.negative?(t.negative=0,this.imod(t).redNeg()):this.imod(t)},b.prototype.pow=function(t,e){if(e.isZero())return new o(1);if(0===e.cmpn(1))return t.clone();var n=Array(16);n[0]=new o(1).toRed(this),n[1]=t;for(var r=2;r<n.length;r++)n[r]=this.mul(n[r-1],t);var i=n[0],a=0,s=0,c=e.bitLength()%26;for(0===c&&(c=26),r=e.length-1;r>=0;r--){for(var f=e.words[r],c=c-1;c>=0;c--){var u=f>>c&1;i!==n[0]&&(i=this.sqr(i)),0===u&&0===a?s=0:(a<<=1,a|=u,s++,(4===s||0===r&&0===c)&&(i=this.mul(i,n[a]),a=s=0))}c=26}return i},b.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},b.prototype.convertFrom=function(t){return t=t.clone(),t.red=null,t},o.mont=function(t){return new m(t)},i(m,b),m.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},m.prototype.convertFrom=function(t){return t=this.imod(t.mul(this.rinv)),t.red=null,t},m.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var n=t.imul(e),r=n.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),r=n=n.isub(r).iushrn(this.shift);return 0<=n.cmp(this.m)?r=n.isub(this.m):0>n.cmpn(0)&&(r=n.iadd(this.m)),r._forceRed(this)},m.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new o(0)._forceRed(this);var n=t.mul(e),r=n.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),r=n=n.isub(r).iushrn(this.shift);return 0<=n.cmp(this.m)?r=n.isub(this.m):0>n.cmpn(0)&&(r=n.iadd(this.m)),r._forceRed(this)},m.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}("undefined"==typeof e||e,this)},{}],20:[function(t,e,n){function r(t){this.rand=t}var i;if(e.exports=function(t){return i||(i=new r(null)),i.generate(t)},e.exports.Rand=r,r.prototype.generate=function(t){return this._rand(t)},"object"==typeof window)r.prototype._rand=window.crypto&&window.crypto.getRandomValues?function(t){return t=new Uint8Array(t),window.crypto.getRandomValues(t),t}:window.msCrypto&&window.msCrypto.getRandomValues?function(t){return t=new Uint8Array(t),window.msCrypto.getRandomValues(t),t}:function(){throw Error("Not implemented yet")};else try{var o=t("crypto");r.prototype._rand=function(t){return o.randomBytes(t)}}catch(a){r.prototype._rand=function(t){t=new Uint8Array(t);for(var e=0;e<t.length;e++)t[e]=this.rand.getByte();return t}}},{}],21:[function(t,e,n){},{}],22:[function(t,e,n){(function(t){function e(t){var e;return t>s||0>t?(e=Math.abs(t)%s,0>t?s-e:e):t}function r(t){for(;0<t.length;t++)t[0]=0;return!1}function i(){this.SBOX=[],this.INV_SBOX=[],this.SUB_MIX=[[],[],[],[]],this.INV_SUB_MIX=[[],[],[],[]],this.init(),this.RCON=[0,1,2,4,8,16,32,64,128,27,54]}function o(t){for(var e=t.length/4,n=Array(e),r=-1;++r<e;)n[r]=t.readUInt32BE(4*r);return n}function a(t){this._key=o(t),this._doReset()}var s=Math.pow(2,32);i.prototype.init=function(){var t,e,n,r,i,o,a,s,c;for(c=[],t=e=0;256>e;t=++e)128>t?c.push(t<<1):c.push(t<<1^283);for(s=a=n=0;256>s;++s)t=a^a<<1^a<<2^a<<3^a<<4,t=t>>>8^255&t^99,this.SBOX[n]=t,this.INV_SBOX[t]=n,r=c[n],i=c[r],o=c[i],e=257*c[t]^16843008*t,this.SUB_MIX[0][n]=e<<24|e>>>8,this.SUB_MIX[1][n]=e<<16|e>>>16,this.SUB_MIX[2][n]=e<<8|e>>>24,this.SUB_MIX[3][n]=e,e=16843009*o^65537*i^257*r^16843008*n,this.INV_SUB_MIX[0][t]=e<<24|e>>>8,this.INV_SUB_MIX[1][t]=e<<16|e>>>16,this.INV_SUB_MIX[2][t]=e<<8|e>>>24,this.INV_SUB_MIX[3][t]=e,0===n?n=a=1:(n=r^c[c[c[o^r]]],a^=c[c[a]]);return!0};var c=new i;a.blockSize=16,a.prototype.blockSize=a.blockSize,a.keySize=32,a.prototype.keySize=a.keySize,a.prototype._doReset=function(){var t,e,n,r,i;for(e=this._key,t=e.length,this._nRounds=t+6,r=4*(this._nRounds+1),this._keySchedule=[],n=0;r>n;n++)this._keySchedule[n]=t>n?e[n]:(i=this._keySchedule[n-1],0===n%t?(i=i<<8|i>>>24,i=c.SBOX[i>>>24]<<24|c.SBOX[i>>>16&255]<<16|c.SBOX[i>>>8&255]<<8|c.SBOX[255&i],i^=c.RCON[n/t|0]<<24):t>6&&4===n%t?i=c.SBOX[i>>>24]<<24|c.SBOX[i>>>16&255]<<16|c.SBOX[i>>>8&255]<<8|c.SBOX[255&i]:void 0,this._keySchedule[n-t]^i);for(this._invKeySchedule=[],t=0;r>t;t++)n=r-t,i=this._keySchedule[n-(t%4?0:4)],this._invKeySchedule[t]=4>t||4>=n?i:c.INV_SUB_MIX[0][c.SBOX[i>>>24]]^c.INV_SUB_MIX[1][c.SBOX[i>>>16&255]]^c.INV_SUB_MIX[2][c.SBOX[i>>>8&255]]^c.INV_SUB_MIX[3][c.SBOX[255&i]];return!0},a.prototype.encryptBlock=function(e){e=o(new t(e)),e=this._doCryptBlock(e,this._keySchedule,c.SUB_MIX,c.SBOX);var n=new t(16);return n.writeUInt32BE(e[0],0),n.writeUInt32BE(e[1],4),n.writeUInt32BE(e[2],8),n.writeUInt32BE(e[3],12),n},a.prototype.decryptBlock=function(e){e=o(new t(e));var n=[e[3],e[1]];return e[1]=n[0],e[3]=n[1],e=this._doCryptBlock(e,this._invKeySchedule,c.INV_SUB_MIX,c.INV_SBOX),n=new t(16),n.writeUInt32BE(e[0],0),n.writeUInt32BE(e[3],4),n.writeUInt32BE(e[2],8),n.writeUInt32BE(e[1],12),n},a.prototype.scrub=function(){r(this._keySchedule),r(this._invKeySchedule),r(this._key)},a.prototype._doCryptBlock=function(t,n,r,i){var o,a,s,c,f,u,d;o=t[0]^n[0],a=t[1]^n[1],s=t[2]^n[2],c=t[3]^n[3],t=4;for(var p=1;p<this._nRounds;p++)f=r[0][o>>>24]^r[1][a>>>16&255]^r[2][s>>>8&255]^r[3][255&c]^n[t++],u=r[0][a>>>24]^r[1][s>>>16&255]^r[2][c>>>8&255]^r[3][255&o]^n[t++],d=r[0][s>>>24]^r[1][c>>>16&255]^r[2][o>>>8&255]^r[3][255&a]^n[t++],c=r[0][c>>>24]^r[1][o>>>16&255]^r[2][a>>>8&255]^r[3][255&s]^n[t++],o=f,a=u,s=d;return f=(i[o>>>24]<<24|i[a>>>16&255]<<16|i[s>>>8&255]<<8|i[255&c])^n[t++],u=(i[a>>>24]<<24|i[s>>>16&255]<<16|i[c>>>8&255]<<8|i[255&o])^n[t++],d=(i[s>>>24]<<24|i[c>>>16&255]<<16|i[o>>>8&255]<<8|i[255&a])^n[t++],c=(i[c>>>24]<<24|i[o>>>16&255]<<16|i[a>>>8&255]<<8|i[255&s])^n[t++],[e(f),e(u),e(d),e(c)]},n.AES=a}).call(this,t(48).Buffer)},{48:48}],23:[function(t,e,n){(function(n){function r(t,e,a,c){return this instanceof r?(o.call(this),this._finID=n.concat([a,new n([0,0,0,1])]),a=n.concat([a,new n([0,0,0,2])]),this._cipher=new i.AES(e),this._prev=new n(a.length),this._cache=new n(""),this._secCache=new n(""),this._decrypt=c,this._len=this._alen=0,a.copy(this._prev),this._mode=t,t=new n(4),t.fill(0),this._ghash=new s(this._cipher.encryptBlock(t)),this._authTag=null,void(this._called=!1)):new r(t,e,a)}var i=t(22),o=t(50),a=t(101),s=t(27),c=t(47);a(r,o),e.exports=r,r.prototype._update=function(t){if(!this._called&&this._alen){var e=16-this._alen%16;16>e&&(e=new n(e),e.fill(0),this._ghash.update(e))}return this._called=!0,e=this._mode.encrypt(this,t),this._decrypt?this._ghash.update(t):this._ghash.update(e),this._len+=t.length,e},r.prototype._final=function(){if(this._decrypt&&!this._authTag)throw Error("Unsupported state or unable to authenticate data");var t=c(this._ghash["final"](8*this._alen,8*this._len),this._cipher.encryptBlock(this._finID));if(this._decrypt){var e=this._authTag,n=0;t.length!==e.length&&n++;for(var r=Math.min(t.length,e.length),i=-1;++i<r;)n+=t[i]^e[i];if(n)throw Error("Unsupported state or unable to authenticate data")}else this._authTag=t;this._cipher.scrub()},r.prototype.getAuthTag=function(){if(!this._decrypt&&n.isBuffer(this._authTag))return this._authTag;throw Error("Attempting to get auth tag in unsupported state")},r.prototype.setAuthTag=function(t){if(!this._decrypt)throw Error("Attempting to set auth tag in unsupported state");this._authTag=t},r.prototype.setAAD=function(t){if(this._called)throw Error("Attempting to set AAD in unsupported state");this._ghash.update(t),this._alen+=t.length}}).call(this,t(48).Buffer)},{101:101,22:22,27:27,47:47,48:48,50:50}],24:[function(t,e,n){e=t(26),n.createCipher=n.Cipher=e.createCipher,n.createCipheriv=n.Cipheriv=e.createCipheriv,e=t(25),n.createDecipher=n.Decipher=e.createDecipher,n.createDecipheriv=n.Decipheriv=e.createDecipheriv;var r=t(28);n.listCiphers=n.getCiphers=function(){return Object.keys(r)}},{25:25,26:26,28:28}],25:[function(t,e,n){(function(e){function r(t,n,o){return this instanceof r?(s.call(this),this._cache=new i,this._last=void 0,this._cipher=new a.AES(n),this._prev=new e(o.length),o.copy(this._prev),this._mode=t,void(this._autopadding=!0)):new r(t,n,o)}function i(){return this instanceof i?void(this.cache=new e("")):new i}function o(t,n,i){if(t=f[t.toLowerCase()],!t)throw new TypeError("invalid suite type");if("string"==typeof i&&(i=new e(i)),"string"==typeof n&&(n=new e(n)),n.length!==t.key/8)throw new TypeError("invalid key length "+n.length);if(i.length!==t.iv)throw new TypeError("invalid iv length "+i.length);return"stream"===t.type?new u(h[t.mode],n,i,!0):"auth"===t.type?new d(h[t.mode],n,i,!0):new r(h[t.mode],n,i)}var a=t(22),s=t(50),c=t(101),f=t(28),u=t(36),d=t(23),p=t(88);c(r,s),r.prototype._update=function(t){this._cache.add(t);var n;for(t=[];n=this._cache.get(this._autopadding);)n=this._mode.decrypt(this,n),t.push(n);return e.concat(t)},r.prototype._final=function(){var t=this._cache.flush();if(this._autopadding){for(var t=this._mode.decrypt(this,t),e=t[15],n=-1;++n<e;)if(t[n+(16-e)]!==e)throw Error("unable to decrypt data");return t=16===e?void 0:t.slice(0,16-e)}if(t)throw Error("data not multiple of block length")},r.prototype.setAutoPadding=function(t){return this._autopadding=!!t,this},i.prototype.add=function(t){this.cache=e.concat([this.cache,t])},i.prototype.get=function(t){if(t){if(16<this.cache.length)return t=this.cache.slice(0,16),this.cache=this.cache.slice(16),t}else if(16<=this.cache.length)return t=this.cache.slice(0,16),this.cache=this.cache.slice(16),t;return null},i.prototype.flush=function(){return this.cache.length?this.cache:void 0};var h={ECB:t(34),CBC:t(29),CFB:t(30),CFB8:t(32),CFB1:t(31),OFB:t(35),CTR:t(33),GCM:t(33)};n.createDecipher=function(t,e){var n=f[t.toLowerCase()];if(!n)throw new TypeError("invalid suite type");return n=p(e,!1,n.key,n.iv),o(t,n.key,n.iv)},n.createDecipheriv=o}).call(this,t(48).Buffer)},{101:101,22:22,23:23,28:28,29:29,30:30,31:31,32:32,33:33,34:34,35:35,36:36,48:48,50:50,88:88}],26:[function(t,e,n){(function(e){function r(t,n,o){return this instanceof r?(s.call(this),this._cache=new i,this._cipher=new a.AES(n),this._prev=new e(o.length),o.copy(this._prev),this._mode=t,void(this._autopadding=!0)):new r(t,n,o)}function i(){return this instanceof i?void(this.cache=new e("")):new i}function o(t,n,i){if(t=f[t.toLowerCase()],!t)throw new TypeError("invalid suite type");if("string"==typeof i&&(i=new e(i)),"string"==typeof n&&(n=new e(n)),n.length!==t.key/8)throw new TypeError("invalid key length "+n.length);if(i.length!==t.iv)throw new TypeError("invalid iv length "+i.length);return"stream"===t.type?new d(h[t.mode],n,i):"auth"===t.type?new p(h[t.mode],n,i):new r(h[t.mode],n,i)}var a=t(22),s=t(50),c=t(101),f=t(28),u=t(88),d=t(36),p=t(23);c(r,s),r.prototype._update=function(t){this._cache.add(t);var n;for(t=[];n=this._cache.get();)n=this._mode.encrypt(this,n),t.push(n);return e.concat(t)},r.prototype._final=function(){var t=this._cache.flush();if(this._autopadding)return t=this._mode.encrypt(this,t),this._cipher.scrub(),t;if("10101010101010101010101010101010"!==t.toString("hex"))throw this._cipher.scrub(),Error("data not multiple of block length")},r.prototype.setAutoPadding=function(t){return this._autopadding=!!t,this},i.prototype.add=function(t){this.cache=e.concat([this.cache,t])},i.prototype.get=function(){if(15<this.cache.length){var t=this.cache.slice(0,16);return this.cache=this.cache.slice(16),t}return null},i.prototype.flush=function(){for(var t=16-this.cache.length,n=new e(t),r=-1;++r<t;)n.writeUInt8(t,r);return e.concat([this.cache,n])};var h={ECB:t(34),CBC:t(29),CFB:t(30),CFB8:t(32),CFB1:t(31),OFB:t(35),CTR:t(33),GCM:t(33)};n.createCipheriv=o,n.createCipher=function(t,e){var n=f[t.toLowerCase()];if(!n)throw new TypeError("invalid suite type");return n=u(e,!1,n.key,n.iv),o(t,n.key,n.iv)}}).call(this,t(48).Buffer)},{101:101,22:22,23:23,28:28,29:29,30:30,31:31,32:32,33:33,34:34,35:35,36:36,48:48,50:50,88:88}],27:[function(t,e,n){(function(t){function n(e){this.h=e,this.state=new t(16),this.state.fill(0),this.cache=new t("")}function r(e){e=e.map(i);var n=new t(16);return n.writeUInt32BE(e[0],0),n.writeUInt32BE(e[1],4),n.writeUInt32BE(e[2],8),n.writeUInt32BE(e[3],12),n}function i(t){var e;return t>a||0>t?(e=Math.abs(t)%a,0>t?a-e:e):t}var o=new t(16);o.fill(0),e.exports=n,n.prototype.ghash=function(t){for(var e=-1;++e<t.length;)this.state[e]^=t[e];this._multiply()},n.prototype._multiply=function(){var t;t=this.h,t=[t.readUInt32BE(0),t.readUInt32BE(4),t.readUInt32BE(8),t.readUInt32BE(12)];for(var e,n,i=[0,0,0,0],o=-1;128>++o;){for((e=0!==(this.state[~~(o/8)]&1<<7-o%8))&&(i=[i[0]^t[0],i[1]^t[1],i[2]^t[2],i[3]^t[3]]),n=0!==(1&t[3]),e=3;e>0;e--)t[e]=t[e]>>>1|(1&t[e-1])<<31;t[0]>>>=1,n&&(t[0]^=-520093696)}this.state=r(i)},n.prototype.update=function(e){for(this.cache=t.concat([this.cache,e]);16<=this.cache.length;)e=this.cache.slice(0,16),this.cache=this.cache.slice(16),this.ghash(e)},n.prototype["final"]=function(e,n){return this.cache.length&&this.ghash(t.concat([this.cache,o],16)),this.ghash(r([0,e,0,n])),this.state};var a=Math.pow(2,32)}).call(this,t(48).Buffer)},{48:48}],28:[function(t,e,n){n["aes-128-ecb"]={cipher:"AES",key:128,iv:0,mode:"ECB",type:"block"},n["aes-192-ecb"]={cipher:"AES",key:192,iv:0,mode:"ECB",type:"block"},n["aes-256-ecb"]={cipher:"AES",key:256,iv:0,mode:"ECB",type:"block"},n["aes-128-cbc"]={cipher:"AES",key:128,iv:16,mode:"CBC",type:"block"},n["aes-192-cbc"]={cipher:"AES",key:192,iv:16,mode:"CBC",type:"block"},n["aes-256-cbc"]={cipher:"AES",key:256,iv:16,mode:"CBC",type:"block"},n.aes128=n["aes-128-cbc"],n.aes192=n["aes-192-cbc"],n.aes256=n["aes-256-cbc"],n["aes-128-cfb"]={cipher:"AES",key:128,iv:16,mode:"CFB",type:"stream"},n["aes-192-cfb"]={cipher:"AES",key:192,iv:16,mode:"CFB",type:"stream"},n["aes-256-cfb"]={cipher:"AES",key:256,iv:16,mode:"CFB",type:"stream"},n["aes-128-cfb8"]={cipher:"AES",key:128,iv:16,mode:"CFB8",type:"stream"},n["aes-192-cfb8"]={cipher:"AES",key:192,iv:16,mode:"CFB8",type:"stream"},n["aes-256-cfb8"]={cipher:"AES",key:256,iv:16,mode:"CFB8",type:"stream"},n["aes-128-cfb1"]={cipher:"AES",key:128,iv:16,mode:"CFB1",type:"stream"},n["aes-192-cfb1"]={cipher:"AES",key:192,iv:16,mode:"CFB1",type:"stream"},n["aes-256-cfb1"]={cipher:"AES",key:256,iv:16,mode:"CFB1",type:"stream"},n["aes-128-ofb"]={cipher:"AES",key:128,iv:16,mode:"OFB",type:"stream"},n["aes-192-ofb"]={cipher:"AES",key:192,iv:16,mode:"OFB",type:"stream"},n["aes-256-ofb"]={cipher:"AES",key:256,iv:16,mode:"OFB",type:"stream"},n["aes-128-ctr"]={cipher:"AES",key:128,iv:16,mode:"CTR",type:"stream"},n["aes-192-ctr"]={cipher:"AES",key:192,iv:16,mode:"CTR",type:"stream"},n["aes-256-ctr"]={cipher:"AES",key:256,iv:16,mode:"CTR",type:"stream"},n["aes-128-gcm"]={cipher:"AES",key:128,iv:12,mode:"GCM",type:"auth"},n["aes-192-gcm"]={cipher:"AES",key:192,iv:12,mode:"GCM",type:"auth"},n["aes-256-gcm"]={cipher:"AES",key:256,iv:12,mode:"GCM",type:"auth"}},{}],29:[function(t,e,n){var r=t(47);n.encrypt=function(t,e){var n=r(e,t._prev);return t._prev=t._cipher.encryptBlock(n),t._prev},n.decrypt=function(t,e){var n=t._prev;t._prev=e;var i=t._cipher.decryptBlock(e);return r(i,n)}},{47:47}],30:[function(t,e,n){(function(e){function r(t,n,r){var o=n.length,a=i(n,t._cache);return t._cache=t._cache.slice(o),t._prev=e.concat([t._prev,r?n:a]),a}var i=t(47);n.encrypt=function(t,n,i){for(var o,a=new e("");n.length;){if(0===t._cache.length&&(t._cache=t._cipher.encryptBlock(t._prev),t._prev=new e("")),!(t._cache.length<=n.length)){a=e.concat([a,r(t,n,i)]);break}o=t._cache.length,a=e.concat([a,r(t,n.slice(0,o),i)]),n=n.slice(o)}return a}}).call(this,t(48).Buffer)},{47:47,48:48}],31:[function(t,e,n){(function(t){n.encrypt=function(e,n,r){for(var i=n.length,o=new t(i),a=-1;++a<i;){for(var s,c,f,u=a,d=e,p=n[a],h=r,l=-1,b=0;8>++l;){s=d._cipher.encryptBlock(d._prev),c=p&1<<7-l?128:0,f=s[0]^c,b+=(128&f)>>l%8,s=d;var m=d._prev;c=h?c:f,f=m.length;for(var v=-1,y=new t(m.length),m=t.concat([m,new t([c])]);++v<f;)y[v]=m[v]<<1|m[v+1]>>7;s._prev=y}o[u]=b}return o}}).call(this,t(48).Buffer)},{48:48}],32:[function(t,e,n){(function(t){n.encrypt=function(e,n,r){for(var i=n.length,o=new t(i),a=-1;++a<i;){var s=a,c=e,f=n[a],u=r,d=c._cipher.encryptBlock(c._prev)[0]^f;c._prev=t.concat([c._prev.slice(1),new t([u?f:d])]),o[s]=d}return o}}).call(this,t(48).Buffer)},{48:48}],33:[function(t,e,n){(function(e){function r(t){var e=t._cipher.encryptBlock(t._prev);t=t._prev;for(var n,r=t.length;r--;){if(n=t.readUInt8(r),255!==n){n++,t.writeUInt8(n,r);break}t.writeUInt8(0,r)}return e}var i=t(47);n.encrypt=function(t,n){for(;t._cache.length<n.length;)t._cache=e.concat([t._cache,r(t)]);var o=t._cache.slice(0,n.length);return t._cache=t._cache.slice(n.length),i(n,o)}}).call(this,t(48).Buffer)},{47:47,48:48}],34:[function(t,e,n){n.encrypt=function(t,e){return t._cipher.encryptBlock(e)},n.decrypt=function(t,e){return t._cipher.decryptBlock(e)}},{}],35:[function(t,e,n){(function(e){function r(t){return t._prev=t._cipher.encryptBlock(t._prev),t._prev}var i=t(47);n.encrypt=function(t,n){for(;t._cache.length<n.length;)t._cache=e.concat([t._cache,r(t)]);var o=t._cache.slice(0,n.length);return t._cache=t._cache.slice(n.length),i(n,o)}}).call(this,t(48).Buffer)},{47:47,48:48}],36:[function(t,e,n){(function(n){function r(t,e,a,s){return this instanceof r?(o.call(this),this._cipher=new i.AES(e),this._prev=new n(a.length),this._cache=new n(""),this._secCache=new n(""),this._decrypt=s,a.copy(this._prev),void(this._mode=t)):new r(t,e,a)}var i=t(22),o=t(50);t(101)(r,o),e.exports=r,r.prototype._update=function(t){return this._mode.encrypt(this,t,this._decrypt)},r.prototype._final=function(){this._cipher.scrub()}}).call(this,t(48).Buffer)},{101:101,22:22,48:48,50:50}],37:[function(t,e,n){function r(t,e,n){if(t=t.toLowerCase(),f[t])return a.createCipheriv(t,e,n);if(c[t])return new s({key:e,iv:n,mode:t});throw new TypeError("invalid suite type")}function i(t,e,n){if(t=t.toLowerCase(),f[t])return a.createDecipheriv(t,e,n);if(c[t])return new s({key:e,iv:n,mode:t,decrypt:!0});throw new TypeError("invalid suite type")}var o=t(88),a=t(24),s=t(38),c=t(39),f=t(28);n.createCipher=n.Cipher=function(t,e){var n,i;if(t=t.toLowerCase(),f[t])n=f[t].key,i=f[t].iv;else{if(!c[t])throw new TypeError("invalid suite type");n=8*c[t].key,i=c[t].iv}return n=o(e,!1,n,i),r(t,n.key,n.iv)},n.createCipheriv=n.Cipheriv=r,n.createDecipher=n.Decipher=function(t,e){var n,r;if(t=t.toLowerCase(),f[t])n=f[t].key,r=f[t].iv;else{if(!c[t])throw new TypeError("invalid suite type");n=8*c[t].key,r=c[t].iv}return n=o(e,!1,n,r),i(t,n.key,n.iv)},n.createDecipheriv=n.Decipheriv=i,
n.listCiphers=n.getCiphers=function(){return Object.keys(c).concat(a.getCiphers())}},{24:24,28:28,38:38,39:39,88:88}],38:[function(t,e,n){(function(n){function r(t){i.call(this);var e,r=t.mode.toLowerCase(),o=s[r];e=t.decrypt?"decrypt":"encrypt";var a=t.key;"des-ede"!==r&&"des-ede-cbc"!==r||(a=n.concat([a,a.slice(0,8)])),this._des=o.create({key:a,iv:t.iv,type:e})}var i=t(50),o=t(60),a=t(101),s={"des-ede3-cbc":o.CBC.instantiate(o.EDE),"des-ede3":o.EDE,"des-ede-cbc":o.CBC.instantiate(o.EDE),"des-ede":o.EDE,"des-cbc":o.CBC.instantiate(o.DES),"des-ecb":o.DES};s.des=s["des-cbc"],s.des3=s["des-ede3-cbc"],e.exports=r,a(r,i),r.prototype._update=function(t){return new n(this._des.update(t))},r.prototype._final=function(){return new n(this._des["final"]())}}).call(this,t(48).Buffer)},{101:101,48:48,50:50,60:60}],39:[function(t,e,n){n["des-ecb"]={key:8,iv:0},n["des-cbc"]=n.des={key:8,iv:8},n["des-ede3-cbc"]=n.des3={key:24,iv:8},n["des-ede3"]={key:24,iv:0},n["des-ede-cbc"]={key:16,iv:8},n["des-ede"]={key:16,iv:0}},{}],40:[function(t,e,n){(function(n){function r(t,e){var r,a;a=i(e),r=a.toRed(o.mont(e.modulus)).redPow(new o(e.publicExponent)).fromRed(),a=a.invm(e.modulus);var s=e.modulus.byteLength();o.mont(e.modulus),r=new o(t).mul(r).umod(e.modulus);var c=r.toRed(o.mont(e.prime1)),f=r.toRed(o.mont(e.prime2)),u=e.coefficient,d=e.prime1;return r=e.prime2,c=c.redPow(e.exponent1),f=f.redPow(e.exponent2),c=c.fromRed(),f=f.fromRed(),u=c.isub(f).imul(u).umod(d),u.imul(r),f.iadd(u),new n(f.imul(a).umod(e.modulus).toArray(!1,s))}function i(t){for(var e=t.modulus.byteLength(),n=new o(a(e));0<=n.cmp(t.modulus)||!n.umod(t.prime1)||!n.umod(t.prime2);)n=new o(a(e));return n}var o=t(19),a=t(124);e.exports=r,r.getr=i}).call(this,t(48).Buffer)},{124:124,19:19,48:48}],41:[function(t,e,n){t=t(48).Buffer,n["RSA-SHA224"]=n.sha224WithRSAEncryption={sign:"rsa",hash:"sha224",id:new t("302d300d06096086480165030402040500041c","hex")},n["RSA-SHA256"]=n.sha256WithRSAEncryption={sign:"rsa",hash:"sha256",id:new t("3031300d060960864801650304020105000420","hex")},n["RSA-SHA384"]=n.sha384WithRSAEncryption={sign:"rsa",hash:"sha384",id:new t("3041300d060960864801650304020205000430","hex")},n["RSA-SHA512"]=n.sha512WithRSAEncryption={sign:"rsa",hash:"sha512",id:new t("3051300d060960864801650304020305000440","hex")},n["RSA-SHA1"]={sign:"rsa",hash:"sha1",id:new t("3021300906052b0e03021a05000414","hex")},n["ecdsa-with-SHA1"]={sign:"ecdsa",hash:"sha1",id:new t("","hex")},n.DSA=n["DSA-SHA1"]=n["DSA-SHA"]={sign:"dsa",hash:"sha1",id:new t("","hex")},n["DSA-SHA224"]=n["DSA-WITH-SHA224"]={sign:"dsa",hash:"sha224",id:new t("","hex")},n["DSA-SHA256"]=n["DSA-WITH-SHA256"]={sign:"dsa",hash:"sha256",id:new t("","hex")},n["DSA-SHA384"]=n["DSA-WITH-SHA384"]={sign:"dsa",hash:"sha384",id:new t("","hex")},n["DSA-SHA512"]=n["DSA-WITH-SHA512"]={sign:"dsa",hash:"sha512",id:new t("","hex")},n["DSA-RIPEMD160"]={sign:"dsa",hash:"rmd160",id:new t("","hex")},n["RSA-RIPEMD160"]=n.ripemd160WithRSA={sign:"rsa",hash:"rmd160",id:new t("3021300906052b2403020105000414","hex")},n["RSA-MD5"]=n.md5WithRSAEncryption={sign:"rsa",hash:"md5",id:new t("3020300c06082a864886f70d020505000410","hex")}},{48:48}],42:[function(t,e,n){(function(n){function r(t){if(d.Writable.call(this),t=h[t],!t)throw Error("Unknown message digest");this._hashType=t.hash,this._hash=c(t.hash),this._tag=t.id,this._signType=t.sign}function i(t){if(d.Writable.call(this),t=h[t],!t)throw Error("Unknown message digest");this._hash=c(t.hash),this._tag=t.id,this._signType=t.sign}function o(t){return new r(t)}function a(t){return new i(t)}var s=t(41),c=t(53),f=t(101),u=t(44),d=t(145),p=t(45),h={};Object.keys(s).forEach(function(t){h[t]=h[t.toLowerCase()]=s[t]}),f(r,d.Writable),r.prototype._write=function(t,e,n){this._hash.update(t),n()},r.prototype.update=function(t,e){return"string"==typeof t&&(t=new n(t,e)),this._hash.update(t),this},r.prototype.sign=function(t,e){this.end();var r=this._hash.digest(),r=u(n.concat([this._tag,r]),t,this._hashType,this._signType);return e?r.toString(e):r},f(i,d.Writable),i.prototype._write=function(t,e,n){this._hash.update(t),n()},i.prototype.update=function(t,e){return"string"==typeof t&&(t=new n(t,e)),this._hash.update(t),this},i.prototype.verify=function(t,e,r){return"string"==typeof e&&(e=new n(e,r)),this.end(),r=this._hash.digest(),p(e,n.concat([this._tag,r]),t,this._signType)},e.exports={Sign:o,Verify:a,createSign:o,createVerify:a}}).call(this,t(48).Buffer)},{101:101,145:145,41:41,44:44,45:45,48:48,53:53}],43:[function(t,e,n){n["1.3.132.0.10"]="secp256k1",n["1.3.132.0.33"]="p224",n["1.2.840.10045.3.1.1"]="p192",n["1.2.840.10045.3.1.7"]="p256",n["1.3.132.0.34"]="p384",n["1.3.132.0.35"]="p521"},{}],44:[function(t,e,n){(function(n){function r(t,e,r,o){if(t=new n(t.toArray()),t.length<e.byteLength()){var s=new n(e.byteLength()-t.length);s.fill(0),t=n.concat([s,t])}return s=r.length,r=i(r,e),r=r.mod(e),r=new n(r.toArray()),r.length<e.byteLength()&&(e=new n(e.byteLength()-r.length),e.fill(0),r=n.concat([e,r])),e=r,r=new n(s),r.fill(1),s=new n(s),s.fill(0),s=a(o,s).update(r).update(new n([0])).update(t).update(e).digest(),r=a(o,s).update(r).digest(),s=a(o,s).update(r).update(new n([1])).update(t).update(e).digest(),r=a(o,s).update(r).digest(),{k:s,v:r}}function i(t,e){var n=new d(t),r=(t.length<<3)-e.bitLength();return r>0&&n.ishrn(r),n}function o(t,e,r){var o;do{for(o=new n("");8*o.length<t.bitLength();)e.v=a(r,e.k).update(e.v).digest(),o=n.concat([o,e.v]);o=i(o,t),e.k=a(r,e.k).update(e.v).update(new n([0])).digest(),e.v=a(r,e.k).update(e.v).digest()}while(-1!==o.cmp(t));return o}var a=t(56),s=t(40),c=t(43),f=t(70),u=t(110),d=t(19),p=f.ec;e.exports=function(t,e,a,f){var h=u(e);if(h.curve){if("ecdsa"!==f)throw Error("wrong private key type");if(a=c[h.curve.join(".")],!a)throw Error("unknown curve "+h.curve.join("."));return a=new p(a).genKeyPair(),a._importPrivate(h.privateKey),t=a.sign(t),new n(t.toDER())}if("dsa"===h.type){if("dsa"!==f)throw Error("wrong private key type");f=h.params.priv_key,e=h.params.p;var l,b=h.params.q,h=h.params.g,m=new d(0),v=i(t,b).mod(b);for(l=!1,t=r(f,b,t,a);!1===l;){var m=l=o(b,t,a),y=b,m=h.toRed(d.mont(e)).redPow(m).fromRed().mod(y);l=l.invm(b).imul(v.add(f.mul(m))).mod(b),l.cmpn(0)||(l=!1,m=new d(0))}return t=l,a=m.toArray(),t=t.toArray(),128&a[0]&&(a=[0].concat(a)),128&t[0]&&(t=[0].concat(t)),f=[48,a.length+t.length+4,2,a.length],f=f.concat(a,[2,t.length],t),new n(f)}if("rsa"!==f)throw Error("wrong private key type");for(f=h.modulus.byteLength(),a=[0,1];t.length+a.length+1<f;)a.push(255);for(a.push(0),f=-1;++f<t.length;)a.push(t[f]);return s(a,h)},e.exports.getKey=r,e.exports.makeKey=o}).call(this,t(48).Buffer)},{110:110,19:19,40:40,43:43,48:48,56:56,70:70}],45:[function(t,e,n){(function(n){function r(t,e){if(0>=t.cmpn(0))throw Error("invalid sig");if(t.cmp(e)>=e)throw Error("invalid sig")}var i=t(43),o=t(70),a=t(110),s=t(19),c=o.ec;e.exports=function(t,e,o,f){if(o=a(o),"ec"===o.type){if("ecdsa"!==f)throw Error("wrong public key type");if(f=i[o.data.algorithm.curve.join(".")],!f)throw Error("unknown curve "+o.data.algorithm.curve.join("."));return new c(f).verify(e,t,o.data.subjectPrivateKey.data)}if("dsa"===o.type){if("dsa"!==f)throw Error("wrong public key type");f=o.data.p;var u=o.data.q,d=o.data.g;o=o.data.pub_key;var p=a.signature.decode(t,"der");t=p.s,p=p.r,r(t,u),r(p,u);var h=s.mont(f);return t=t.invm(u),!d.toRed(h).redPow(new s(e).mul(t).mod(u)).fromRed().mul(o.toRed(h).redPow(p.mul(t).mod(u)).fromRed()).mod(f).mod(u).cmp(p)}if("rsa"!==f)throw Error("wrong public key type");for(d=o.modulus.byteLength(),f=[1],u=0;e.length+f.length+2<d;)f.push(255),u++;for(f.push(0),p=-1;++p<e.length;)f.push(e[p]);for(f=new n(f),e=s.mont(o.modulus),t=new s(t).toRed(e),t=t.redPow(new s(o.publicExponent)),t=new n(t.fromRed().toArray()),e=0,8>u&&(e=1),d=Math.min(t.length,f.length),t.length!==f.length&&(e=1),p=-1;++p<d;)e|=t[p]^f[p];return 0===e}}).call(this,t(48).Buffer)},{110:110,19:19,43:43,48:48,70:70}],46:[function(t,e,n){arguments[4][21][0].apply(n,arguments)},{21:21}],47:[function(t,e,n){(function(t){e.exports=function(e,n){for(var r=Math.min(e.length,n.length),i=new t(r),o=0;r>o;++o)i[o]=e[o]^n[o];return i}}).call(this,t(48).Buffer)},{48:48}],48:[function(t,e,n){(function(e){function r(){function t(){}try{var e=new Uint8Array(1);return e.foo=function(){return 42},e.constructor=t,42===e.foo()&&e.constructor===t&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(n){return!1}}function i(t){if(!(this instanceof i))return 1<arguments.length?new i(t,arguments[1]):new i(t);if(i.TYPED_ARRAY_SUPPORT||(this.length=0,this.parent=void 0),"number"==typeof t){var e;if(e=s(this,0>t?0:0|c(t)),!i.TYPED_ARRAY_SUPPORT)for(var n=0;t>n;n++)e[n]=0;return e}if("string"==typeof t){e=this,n=1<arguments.length?arguments[1]:"utf8","string"==typeof n&&""!==n||(n="utf8");var r=0|u(t,n);return e=s(e,r),e.write(t,n),e}return o(this,t)}function o(t,e){if(i.isBuffer(e)){var n=t,r=0|c(e.length),n=s(n,r);return e.copy(n,0,0,r),n}if(M(e)){for(var n=t,r=0|c(e.length),n=s(n,r),o=0;r>o;o+=1)n[o]=255&e[o];return n}if(null==e)throw new TypeError("must start with number, buffer, array or string");if("undefined"!=typeof ArrayBuffer){if(e.buffer instanceof ArrayBuffer)return a(t,e);if(e instanceof ArrayBuffer)return n=t,i.TYPED_ARRAY_SUPPORT?(e.byteLength,n=i._augment(new Uint8Array(e))):n=a(n,new Uint8Array(e)),n}if(e.length){for(n=t,r=0|c(e.length),n=s(n,r),o=0;r>o;o+=1)n[o]=255&e[o];return n}n=t,o=0,"Buffer"===e.type&&M(e.data)&&(r=e.data,o=0|c(r.length));for(var n=s(n,o),f=0;o>f;f+=1)n[f]=255&r[f];return n}function a(t,e){var n=0|c(e.length);t=s(t,n);for(var r=0;n>r;r+=1)t[r]=255&e[r];return t}function s(t,e){return i.TYPED_ARRAY_SUPPORT?(t=i._augment(new Uint8Array(e)),t.__proto__=i.prototype):(t.length=e,t._isBuffer=!0),0!==e&&e<=i.poolSize>>>1&&(t.parent=A),t}function c(t){if(t>=(i.TYPED_ARRAY_SUPPORT?2147483647:1073741823))throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+(i.TYPED_ARRAY_SUPPORT?2147483647:1073741823).toString(16)+" bytes");return 0|t}function f(t,e){if(!(this instanceof f))return new f(t,e);var n=new i(t,e);return delete n.parent,n}function u(t,e){"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"binary":case"raw":case"raws":return n;case"utf8":case"utf-8":return w(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return E.toByteArray(_(t)).length;default:if(r)return w(t).length;e=(""+e).toLowerCase(),r=!0}}function d(t,e,n){var r=!1;if(e|=0,n=void 0===n||1/0===n?this.length:0|n,t||(t="utf8"),0>e&&(e=0),n>this.length&&(n=this.length),e>=n)return"";for(;;)switch(t){case"hex":for(t=e,e=n,n=this.length,(!t||0>t)&&(t=0),(!e||0>e||e>n)&&(e=n),r="",n=t;e>n;n++)t=r,r=this[n],r=16>r?"0"+r.toString(16):r.toString(16),r=t+r;return r;case"utf8":case"utf-8":return p(this,e,n);case"ascii":for(t="",n=Math.min(this.length,n);n>e;e++)t+=String.fromCharCode(127&this[e]);return t;case"binary":for(t="",n=Math.min(this.length,n);n>e;e++)t+=String.fromCharCode(this[e]);return t;case"base64":return e=0===e&&n===this.length?E.fromByteArray(this):E.fromByteArray(this.slice(e,n));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":for(e=this.slice(e,n),n="",t=0;t<e.length;t+=2)n+=String.fromCharCode(e[t]+256*e[t+1]);return n;default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function p(t,e,n){n=Math.min(t.length,n);for(var r=[];n>e;){var i=t[e],o=null,a=i>239?4:i>223?3:i>191?2:1;if(n>=e+a){var s,c,f;switch(a){case 1:128>i&&(o=i);break;case 2:s=t[e+1],128===(192&s)&&(i=(31&i)<<6|63&s,i>127&&(o=i));break;case 3:s=t[e+1],c=t[e+2],128===(192&s)&&128===(192&c)&&(i=(15&i)<<12|(63&s)<<6|63&c,i>2047&&(55296>i||i>57343)&&(o=i));break;case 4:s=t[e+1],c=t[e+2],f=t[e+3],128===(192&s)&&128===(192&c)&&128===(192&f)&&(i=(15&i)<<18|(63&s)<<12|(63&c)<<6|63&f,i>65535&&1114112>i&&(o=i))}}null===o?(o=65533,a=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),e+=a}if(t=r.length,T>=t)r=String.fromCharCode.apply(String,r);else{for(n="",e=0;t>e;)n+=String.fromCharCode.apply(String,r.slice(e,e+=T));r=n}return r}function h(t,e,n){if(0!==t%1||0>t)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function l(t,e,n,r,o,a){if(!i.isBuffer(t))throw new TypeError("buffer must be a Buffer instance");if(e>o||a>e)throw new RangeError("value is out of bounds");if(n+r>t.length)throw new RangeError("index out of range")}function b(t,e,n,r){0>e&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-n,2);o>i;i++)t[n+i]=(e&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function m(t,e,n,r){0>e&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-n,4);o>i;i++)t[n+i]=e>>>8*(r?i:3-i)&255}function v(t,e,n,r,i,o){if(e>i||o>e)throw new RangeError("value is out of bounds");if(n+r>t.length)throw new RangeError("index out of range");if(0>n)throw new RangeError("index out of range")}function y(t,e,n,r,i){return i||v(t,e,n,4,3.4028234663852886e38,-3.4028234663852886e38),S.write(t,e,n,r,23,4),n+4}function g(t,e,n,r,i){return i||v(t,e,n,8,1.7976931348623157e308,-1.7976931348623157e308),S.write(t,e,n,r,52,8),n+8}function _(t){if(t=t.trim?t.trim():t.replace(/^\s+|\s+$/g,""),t=t.replace(I,""),2>t.length)return"";for(;0!==t.length%4;)t+="=";return t}function w(t,e){e=e||1/0;for(var n,r=t.length,i=null,o=[],a=0;r>a;a++){if(n=t.charCodeAt(a),n>55295&&57344>n){if(!i){if(n>56319){-1<(e-=3)&&o.push(239,191,189);continue}if(a+1===r){-1<(e-=3)&&o.push(239,191,189);continue}i=n;continue}if(56320>n){-1<(e-=3)&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&-1<(e-=3)&&o.push(239,191,189);if(i=null,128>n){if(0>--e)break;o.push(n)}else if(2048>n){if(0>(e-=2))break;o.push(n>>6|192,63&n|128)}else if(65536>n){if(0>(e-=3))break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(1114112>n))throw Error("Invalid code point");if(0>(e-=4))break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function x(t){for(var e=[],n=0;n<t.length;n++)e.push(255&t.charCodeAt(n));return e}function k(t,e,n,r){for(var i=0;r>i&&!(i+n>=e.length||i>=t.length);i++)e[i+n]=t[i];return i}var E=t(18),S=t(99),M=t(49);n.Buffer=i,n.SlowBuffer=f,n.INSPECT_MAX_BYTES=50,i.poolSize=8192;var A={};i.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:r(),i.TYPED_ARRAY_SUPPORT?(i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array):(i.prototype.length=void 0,i.prototype.parent=void 0),i.isBuffer=function(t){return!(null==t||!t._isBuffer)},i.compare=function(t,e){if(!i.isBuffer(t)||!i.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,o=0,a=Math.min(n,r);a>o&&t[o]===e[o];)++o;return o!==a&&(n=t[o],r=e[o]),r>n?-1:n>r?1:0},i.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(t,e){if(!M(t))throw new TypeError("list argument must be an Array of Buffers.");if(0===t.length)return new i(0);var n;if(void 0===e)for(n=e=0;n<t.length;n++)e+=t[n].length;var r=new i(e),o=0;for(n=0;n<t.length;n++){var a=t[n];a.copy(r,o),o+=a.length}return r},i.byteLength=u,i.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?p(this,0,t):d.apply(this,arguments)},i.prototype.equals=function(t){if(!i.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:0===i.compare(this,t)},i.prototype.inspect=function(){var t="",e=n.INSPECT_MAX_BYTES;return 0<this.length&&(t=this.toString("hex",0,e).match(/.{2}/g).join(" "),this.length>e&&(t+=" ... ")),"<Buffer "+t+">"},i.prototype.compare=function(t){if(!i.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?0:i.compare(this,t)},i.prototype.indexOf=function(t,e){function n(t,e,n){for(var r=-1,i=0;n+i<t.length;i++)if(t[n+i]===e[-1===r?0:i-r]){if(-1===r&&(r=i),i-r+1===e.length)return n+r}else r=-1;return-1}if(e>2147483647?e=2147483647:-2147483648>e&&(e=-2147483648),e>>=0,0===this.length||e>=this.length)return-1;if(0>e&&(e=Math.max(this.length+e,0)),"string"==typeof t)return 0===t.length?-1:String.prototype.indexOf.call(this,t,e);if(i.isBuffer(t))return n(this,t,e);if("number"==typeof t)return i.TYPED_ARRAY_SUPPORT&&"function"===Uint8Array.prototype.indexOf?Uint8Array.prototype.indexOf.call(this,t,e):n(this,[t],e);throw new TypeError("val must be string, number or Buffer")},i.prototype.get=function(t){return console.log(".get() is deprecated. Access using array indexes instead."),this.readUInt8(t)},i.prototype.set=function(t,e){return console.log(".set() is deprecated. Access using array indexes instead."),this.writeUInt8(t,e)},i.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else if(isFinite(e))e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0);else{var i=r;r=e,e=0|n,n=i}if(i=this.length-e,(void 0===n||n>i)&&(n=i),0<t.length&&(0>n||0>e)||e>this.length)throw new RangeError("attempt to write outside buffer bounds");for(r||(r="utf8"),i=!1;;)switch(r){case"hex":if(e=Number(e)||0,r=this.length-e,n?(n=Number(n),n>r&&(n=r)):n=r,r=t.length,0!==r%2)throw Error("Invalid hex string");for(n>r/2&&(n=r/2),r=0;n>r;r++){if(i=parseInt(t.substr(2*r,2),16),isNaN(i))throw Error("Invalid hex string");this[e+r]=i}return r;case"utf8":case"utf-8":return k(w(t,this.length-e),this,e,n);case"ascii":return k(x(t),this,e,n);case"binary":return k(x(t),this,e,n);case"base64":return k(E.toByteArray(_(t)),this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":r=this.length-e;for(var o,a=[],s=0;s<t.length&&!(0>(r-=2));s++)o=t.charCodeAt(s),i=o>>8,o%=256,a.push(o),a.push(i);return k(a,this,e,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var T=4096;i.prototype.slice=function(t,e){var n=this.length;if(t=~~t,e=void 0===e?n:~~e,0>t?(t+=n,0>t&&(t=0)):t>n&&(t=n),0>e?(e+=n,0>e&&(e=0)):e>n&&(e=n),t>e&&(e=t),i.TYPED_ARRAY_SUPPORT)n=i._augment(this.subarray(t,e));else for(var r=e-t,n=new i(r,void 0),o=0;r>o;o++)n[o]=this[o+t];return n.length&&(n.parent=this.parent||this),n},i.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||h(t,e,this.length),n=this[t];for(var r=1,i=0;++i<e&&(r*=256);)n+=this[t+i]*r;return n},i.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||h(t,e,this.length),n=this[t+--e];for(var r=1;e>0&&(r*=256);)n+=this[t+--e]*r;return n},i.prototype.readUInt8=function(t,e){return e||h(t,1,this.length),this[t]},i.prototype.readUInt16LE=function(t,e){return e||h(t,2,this.length),this[t]|this[t+1]<<8},i.prototype.readUInt16BE=function(t,e){return e||h(t,2,this.length),this[t]<<8|this[t+1]},i.prototype.readUInt32LE=function(t,e){return e||h(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},i.prototype.readUInt32BE=function(t,e){return e||h(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},i.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||h(t,e,this.length),n=this[t];for(var r=1,i=0;++i<e&&(r*=256);)n+=this[t+i]*r;return n>=128*r&&(n-=Math.pow(2,8*e)),n},i.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||h(t,e,this.length),n=e;for(var r=1,i=this[t+--n];n>0&&(r*=256);)i+=this[t+--n]*r;return i>=128*r&&(i-=Math.pow(2,8*e)),i},i.prototype.readInt8=function(t,e){return e||h(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},i.prototype.readInt16LE=function(t,e){e||h(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(t,e){e||h(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(t,e){return e||h(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},i.prototype.readInt32BE=function(t,e){return e||h(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},i.prototype.readFloatLE=function(t,e){return e||h(t,4,this.length),S.read(this,t,!0,23,4)},i.prototype.readFloatBE=function(t,e){return e||h(t,4,this.length),S.read(this,t,!1,23,4)},i.prototype.readDoubleLE=function(t,e){return e||h(t,8,this.length),S.read(this,t,!0,52,8)},i.prototype.readDoubleBE=function(t,e){return e||h(t,8,this.length),S.read(this,t,!1,52,8)},i.prototype.writeUIntLE=function(t,e,n,r){t=+t,e|=0,n|=0,r||l(this,t,e,n,Math.pow(2,8*n),0),r=1;var i=0;for(this[e]=255&t;++i<n&&(r*=256);)this[e+i]=t/r&255;return e+n},i.prototype.writeUIntBE=function(t,e,n,r){t=+t,e|=0,n|=0,r||l(this,t,e,n,Math.pow(2,8*n),0),r=n-1;var i=1;for(this[e+r]=255&t;0<=--r&&(i*=256);)this[e+r]=t/i&255;return e+n},i.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,1,255,0),i.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},i.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):b(this,t,e,!0),e+2},i.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):b(this,t,e,!1),e+2},i.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):m(this,t,e,!0),e+4},i.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):m(this,t,e,!1),e+4},i.prototype.writeIntLE=function(t,e,n,r){t=+t,e|=0,r||(r=Math.pow(2,8*n-1),l(this,t,e,n,r-1,-r)),r=0;var i=1,o=0>t?1:0;for(this[e]=255&t;++r<n&&(i*=256);)this[e+r]=(t/i>>0)-o&255;return e+n},i.prototype.writeIntBE=function(t,e,n,r){t=+t,e|=0,r||(r=Math.pow(2,8*n-1),l(this,t,e,n,r-1,-r)),r=n-1;var i=1,o=0>t?1:0;for(this[e+r]=255&t;0<=--r&&(i*=256);)this[e+r]=(t/i>>0)-o&255;return e+n},i.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,1,127,-128),i.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),0>t&&(t=255+t+1),this[e]=255&t,e+1},i.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):b(this,t,e,!0),e+2},i.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):b(this,t,e,!1),e+2},i.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,4,2147483647,-2147483648),i.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):m(this,t,e,!0),e+4},i.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||l(this,t,e,4,2147483647,-2147483648),0>t&&(t=4294967295+t+1),i.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):m(this,t,e,!1),e+4},i.prototype.writeFloatLE=function(t,e,n){return y(this,t,e,!0,n)},i.prototype.writeFloatBE=function(t,e,n){return y(this,t,e,!1,n)},i.prototype.writeDoubleLE=function(t,e,n){return g(this,t,e,!0,n)},i.prototype.writeDoubleBE=function(t,e,n){return g(this,t,e,!1,n)},i.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&n>r&&(r=n),r===n||0===t.length||0===this.length)return 0;if(0>e)throw new RangeError("targetStart out of bounds");if(0>n||n>=this.length)throw new RangeError("sourceStart out of bounds");if(0>r)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var o=r-n;if(this===t&&e>n&&r>e)for(r=o-1;r>=0;r--)t[r+e]=this[r+n];else if(1e3>o||!i.TYPED_ARRAY_SUPPORT)for(r=0;o>r;r++)t[r+e]=this[r+n];else t._set(this.subarray(n,n+o),e);return o},i.prototype.fill=function(t,e,n){if(t||(t=0),e||(e=0),n||(n=this.length),e>n)throw new RangeError("end < start");if(n!==e&&0!==this.length){if(0>e||e>=this.length)throw new RangeError("start out of bounds");if(0>n||n>this.length)throw new RangeError("end out of bounds");if("number"==typeof t)for(;n>e;e++)this[e]=t;else{t=w(t.toString());for(var r=t.length;n>e;e++)this[e]=t[e%r]}return this}},i.prototype.toArrayBuffer=function(){if("undefined"!=typeof Uint8Array){if(i.TYPED_ARRAY_SUPPORT)return new i(this).buffer;for(var t=new Uint8Array(this.length),e=0,n=t.length;n>e;e+=1)t[e]=this[e];return t.buffer}throw new TypeError("Buffer.toArrayBuffer not supported in this browser")};var j=i.prototype;i._augment=function(t){return t.constructor=i,t._isBuffer=!0,t._set=t.set,t.get=j.get,t.set=j.set,t.write=j.write,t.toString=j.toString,t.toLocaleString=j.toString,t.toJSON=j.toJSON,t.equals=j.equals,t.compare=j.compare,t.indexOf=j.indexOf,t.copy=j.copy,t.slice=j.slice,t.readUIntLE=j.readUIntLE,t.readUIntBE=j.readUIntBE,t.readUInt8=j.readUInt8,t.readUInt16LE=j.readUInt16LE,t.readUInt16BE=j.readUInt16BE,t.readUInt32LE=j.readUInt32LE,t.readUInt32BE=j.readUInt32BE,t.readIntLE=j.readIntLE,t.readIntBE=j.readIntBE,t.readInt8=j.readInt8,t.readInt16LE=j.readInt16LE,t.readInt16BE=j.readInt16BE,t.readInt32LE=j.readInt32LE,t.readInt32BE=j.readInt32BE,t.readFloatLE=j.readFloatLE,t.readFloatBE=j.readFloatBE,t.readDoubleLE=j.readDoubleLE,t.readDoubleBE=j.readDoubleBE,t.writeUInt8=j.writeUInt8,t.writeUIntLE=j.writeUIntLE,t.writeUIntBE=j.writeUIntBE,t.writeUInt16LE=j.writeUInt16LE,t.writeUInt16BE=j.writeUInt16BE,t.writeUInt32LE=j.writeUInt32LE,t.writeUInt32BE=j.writeUInt32BE,t.writeIntLE=j.writeIntLE,t.writeIntBE=j.writeIntBE,t.writeInt8=j.writeInt8,t.writeInt16LE=j.writeInt16LE,t.writeInt16BE=j.writeInt16BE,t.writeInt32LE=j.writeInt32LE,t.writeInt32BE=j.writeInt32BE,t.writeFloatLE=j.writeFloatLE,t.writeFloatBE=j.writeFloatBE,t.writeDoubleLE=j.writeDoubleLE,t.writeDoubleBE=j.writeDoubleBE,t.fill=j.fill,t.inspect=j.inspect,t.toArrayBuffer=j.toArrayBuffer,t};var I=/[^+\/0-9A-Za-z-_]/g}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{18:18,49:49,99:99}],49:[function(t,e,n){var r={}.toString;e.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},{}],50:[function(t,e,n){(function(n){function r(t){i.call(this),(this.hashMode="string"==typeof t)?this[t]=this._finalOrDigest:this["final"]=this._finalOrDigest,this._encoding=this._decoder=null}var i=t(145).Transform,o=t(101),a=t(146).StringDecoder;e.exports=r,o(r,i),r.prototype.update=function(t,e,r){return"string"==typeof t&&(t=new n(t,e)),t=this._update(t),this.hashMode?this:(r&&(t=this._toString(t,r)),t)},r.prototype.setAutoPadding=function(){},r.prototype.getAuthTag=function(){throw Error("trying to get auth tag in unsupported state")},r.prototype.setAuthTag=function(){throw Error("trying to set auth tag in unsupported state")},r.prototype.setAAD=function(){throw Error("trying to set aad in unsupported state")},r.prototype._transform=function(t,e,n){var r;try{this.hashMode?this._update(t):this.push(this._update(t))}catch(i){r=i}finally{n(r)}},r.prototype._flush=function(t){var e;try{this.push(this._final())}catch(n){e=n}finally{t(e)}},r.prototype._finalOrDigest=function(t){var e=this._final()||new n("");return t&&(e=this._toString(e,t,!0)),e},r.prototype._toString=function(t,e,n){if(this._decoder||(this._decoder=new a(e),this._encoding=e),this._encoding!==e)throw Error("can't switch encodings");return t=this._decoder.write(t),n&&(t+=this._decoder.end()),t}}).call(this,t(48).Buffer)},{101:101,145:145,146:146,48:48}],51:[function(t,e,n){(function(t){n.isArray=function(t){return Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)},n.isBoolean=function(t){return"boolean"==typeof t},n.isNull=function(t){return null===t},n.isNullOrUndefined=function(t){return null==t},n.isNumber=function(t){return"number"==typeof t},n.isString=function(t){return"string"==typeof t},n.isSymbol=function(t){return"symbol"==typeof t},n.isUndefined=function(t){return void 0===t},n.isRegExp=function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},n.isObject=function(t){return"object"==typeof t&&null!==t},n.isDate=function(t){return"[object Date]"===Object.prototype.toString.call(t)},n.isError=function(t){return"[object Error]"===Object.prototype.toString.call(t)||t instanceof Error},n.isFunction=function(t){return"function"==typeof t},n.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||"undefined"==typeof t},n.isBuffer=t.isBuffer}).call(this,{isBuffer:t(102)})},{102:102}],52:[function(t,e,n){(function(n){function r(t){this.curveType=s[t],this.curveType||(this.curveType={name:t}),this.curve=new o.ec(this.curveType.name),this.keys=void 0}function i(t,e,r){return Array.isArray(t)||(t=t.toArray()),t=new n(t),r&&t.length<r&&(r=new n(r-t.length),r.fill(0),t=n.concat([r,t])),e?t.toString(e):t}var o=t(70),a=t(19);e.exports=function(t){return new r(t)};var s={secp256k1:{name:"secp256k1",byteLength:32},secp224r1:{name:"p224",byteLength:28},prime256v1:{name:"p256",byteLength:32},prime192v1:{name:"p192",byteLength:24},ed25519:{name:"ed25519",byteLength:32},secp384r1:{name:"p384",byteLength:48},secp521r1:{name:"p521",byteLength:66}};s.p224=s.secp224r1,s.p256=s.secp256r1=s.prime256v1,s.p192=s.secp192r1=s.prime192v1,s.p384=s.secp384r1,s.p521=s.secp521r1,r.prototype.generateKeys=function(t,e){return this.keys=this.curve.genKeyPair(),this.getPublicKey(t,e)},r.prototype.computeSecret=function(t,e,r){return e=e||"utf8",n.isBuffer(t)||(t=new n(t,e)),t=this.curve.keyFromPublic(t).getPublic().mul(this.keys.getPrivate()).getX(),i(t,r,this.curveType.byteLength)},r.prototype.getPublicKey=function(t,e){var n=this.keys.getPublic("compressed"===e,!0);return"hybrid"===e&&(n[0]=n[n.length-1]%2?7:6),i(n,t)},r.prototype.getPrivateKey=function(t){return i(this.keys.getPrivate(),t)},r.prototype.setPublicKey=function(t,e){return e=e||"utf8",n.isBuffer(t)||(t=new n(t,e)),this.keys._importPublic(t),this},r.prototype.setPrivateKey=function(t,e){e=e||"utf8",n.isBuffer(t)||(t=new n(t,e));var r=new a(t),r=r.toString(16);return this.keys._importPrivate(r),this}}).call(this,t(48).Buffer)},{19:19,48:48,70:70}],53:[function(t,e,n){(function(n){function r(t){f.call(this,"digest"),this._hash=t,this.buffers=[]}function i(t){f.call(this,"digest"),this._hash=t}var o=t(101),a=t(55),s=t(135),c=t(137),f=t(50);o(r,f),r.prototype._update=function(t){this.buffers.push(t)},r.prototype._final=function(){var t=n.concat(this.buffers),t=this._hash(t);return this.buffers=null,t},o(i,f),i.prototype._update=function(t){this._hash.update(t)},i.prototype._final=function(){return this._hash.digest()},e.exports=function(t){return t=t.toLowerCase(),"md5"===t?new r(a):"rmd160"===t||"ripemd160"===t?new r(s):new i(c(t))}}).call(this,t(48).Buffer)},{101:101,135:135,137:137,48:48,50:50,55:55}],54:[function(t,e,n){(function(t){var e=new t(4);e.fill(0),n.hash=function(n,r,i,o){t.isBuffer(n)||(n=new t(n));var a=n;0!==a.length%4&&(a=t.concat([a,e],a.length+(4-a.length%4)));for(var s=[],c=o?a.readInt32BE:a.readInt32LE,f=0;f<a.length;f+=4)s.push(c.call(a,f));for(n=r(s,8*n.length),i=new t(i),o=o?i.writeInt32BE:i.writeInt32LE,r=0;r<n.length;r++)o.call(i,n[r],4*r,!0);return i}}).call(this,t(48).Buffer)},{48:48}],55:[function(t,e,n){function r(t,e){t[e>>5]|=128<<e%32,t[(e+64>>>9<<4)+14]=e;for(var n=1732584193,r=-271733879,f=-1732584194,u=271733878,d=0;d<t.length;d+=16)var p=n,h=r,l=f,b=u,n=o(n,r,f,u,t[d+0],7,-680876936),u=o(u,n,r,f,t[d+1],12,-389564586),f=o(f,u,n,r,t[d+2],17,606105819),r=o(r,f,u,n,t[d+3],22,-1044525330),n=o(n,r,f,u,t[d+4],7,-176418897),u=o(u,n,r,f,t[d+5],12,1200080426),f=o(f,u,n,r,t[d+6],17,-1473231341),r=o(r,f,u,n,t[d+7],22,-45705983),n=o(n,r,f,u,t[d+8],7,1770035416),u=o(u,n,r,f,t[d+9],12,-1958414417),f=o(f,u,n,r,t[d+10],17,-42063),r=o(r,f,u,n,t[d+11],22,-1990404162),n=o(n,r,f,u,t[d+12],7,1804603682),u=o(u,n,r,f,t[d+13],12,-40341101),f=o(f,u,n,r,t[d+14],17,-1502002290),r=o(r,f,u,n,t[d+15],22,1236535329),n=a(n,r,f,u,t[d+1],5,-165796510),u=a(u,n,r,f,t[d+6],9,-1069501632),f=a(f,u,n,r,t[d+11],14,643717713),r=a(r,f,u,n,t[d+0],20,-373897302),n=a(n,r,f,u,t[d+5],5,-701558691),u=a(u,n,r,f,t[d+10],9,38016083),f=a(f,u,n,r,t[d+15],14,-660478335),r=a(r,f,u,n,t[d+4],20,-405537848),n=a(n,r,f,u,t[d+9],5,568446438),u=a(u,n,r,f,t[d+14],9,-1019803690),f=a(f,u,n,r,t[d+3],14,-187363961),r=a(r,f,u,n,t[d+8],20,1163531501),n=a(n,r,f,u,t[d+13],5,-1444681467),u=a(u,n,r,f,t[d+2],9,-51403784),f=a(f,u,n,r,t[d+7],14,1735328473),r=a(r,f,u,n,t[d+12],20,-1926607734),n=i(r^f^u,n,r,t[d+5],4,-378558),u=i(n^r^f,u,n,t[d+8],11,-2022574463),f=i(u^n^r,f,u,t[d+11],16,1839030562),r=i(f^u^n,r,f,t[d+14],23,-35309556),n=i(r^f^u,n,r,t[d+1],4,-1530992060),u=i(n^r^f,u,n,t[d+4],11,1272893353),f=i(u^n^r,f,u,t[d+7],16,-155497632),r=i(f^u^n,r,f,t[d+10],23,-1094730640),n=i(r^f^u,n,r,t[d+13],4,681279174),u=i(n^r^f,u,n,t[d+0],11,-358537222),f=i(u^n^r,f,u,t[d+3],16,-722521979),r=i(f^u^n,r,f,t[d+6],23,76029189),n=i(r^f^u,n,r,t[d+9],4,-640364487),u=i(n^r^f,u,n,t[d+12],11,-421815835),f=i(u^n^r,f,u,t[d+15],16,530742520),r=i(f^u^n,r,f,t[d+2],23,-995338651),n=s(n,r,f,u,t[d+0],6,-198630844),u=s(u,n,r,f,t[d+7],10,1126891415),f=s(f,u,n,r,t[d+14],15,-1416354905),r=s(r,f,u,n,t[d+5],21,-57434055),n=s(n,r,f,u,t[d+12],6,1700485571),u=s(u,n,r,f,t[d+3],10,-1894986606),f=s(f,u,n,r,t[d+10],15,-1051523),r=s(r,f,u,n,t[d+1],21,-2054922799),n=s(n,r,f,u,t[d+8],6,1873313359),u=s(u,n,r,f,t[d+15],10,-30611744),f=s(f,u,n,r,t[d+6],15,-1560198380),r=s(r,f,u,n,t[d+13],21,1309151649),n=s(n,r,f,u,t[d+4],6,-145523070),u=s(u,n,r,f,t[d+11],10,-1120210379),f=s(f,u,n,r,t[d+2],15,718787259),r=s(r,f,u,n,t[d+9],21,-343485551),n=c(n,p),r=c(r,h),f=c(f,l),u=c(u,b);
return[n,r,f,u]}function i(t,e,n,r,i,o){return t=c(c(e,t),c(r,o)),c(t<<i|t>>>32-i,n)}function o(t,e,n,r,o,a,s){return i(e&n|~e&r,t,e,o,a,s)}function a(t,e,n,r,o,a,s){return i(e&r|n&~r,t,e,o,a,s)}function s(t,e,n,r,o,a,s){return i(n^(e|~r),t,e,o,a,s)}function c(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}var f=t(54);e.exports=function(t){return f.hash(t,r,16)}},{54:54}],56:[function(t,e,n){(function(n){function r(t,e){a.call(this),t=t.toLowerCase(),"string"==typeof e&&(e=new n(e));var r="sha512"===t||"sha384"===t?128:64;this._alg=t,this._key=e,e.length>r?e=i(t).update(e).digest():e.length<r&&(e=n.concat([e,s],r));for(var o=this._ipad=new n(r),c=this._opad=new n(r),f=0;r>f;f++)o[f]=54^e[f],c[f]=92^e[f];this._hash=i(t).update(o)}var i=t(53),o=t(101),a=t(145).Transform,s=new n(128);s.fill(0),o(r,a),r.prototype.update=function(t,e){return this._hash.update(t,e),this},r.prototype._transform=function(t,e,n){this._hash.update(t),n()},r.prototype._flush=function(t){this.push(this.digest()),t()},r.prototype.digest=function(t){var e=this._hash.digest();return i(this._alg).update(this._opad).update(e).digest(t)},e.exports=function(t,e){return new r(t,e)}}).call(this,t(48).Buffer)},{101:101,145:145,48:48,53:53}],57:[function(t,e,n){n.randomBytes=n.rng=n.pseudoRandomBytes=n.prng=t(124),n.createHash=n.Hash=t(53),n.createHmac=n.Hmac=t(56);var r="sha1 sha224 sha256 sha384 sha512 md5 rmd160".split(" ").concat(Object.keys(t(41)));n.getHashes=function(){return r},e=t(112),n.pbkdf2=e.pbkdf2,n.pbkdf2Sync=e.pbkdf2Sync;var i=t(37);"Cipher createCipher Cipheriv createCipheriv Decipher createDecipher Decipheriv createDecipheriv getCiphers listCiphers".split(" ").forEach(function(t){n[t]=i[t]});var o=t(66);["DiffieHellmanGroup","createDiffieHellmanGroup","getDiffieHellman","createDiffieHellman","DiffieHellman"].forEach(function(t){n[t]=o[t]});var a=t(42);["createSign","Sign","createVerify","Verify"].forEach(function(t){n[t]=a[t]}),n.createECDH=t(52);var s=t(114);["publicEncrypt","privateEncrypt","publicDecrypt","privateDecrypt"].forEach(function(t){n[t]=s[t]}),["createCredentials"].forEach(function(t){n[t]=function(){throw Error(["sorry, "+t+" is not implemented yet","we accept pull requests\nhttps://github.com/crypto-browserify/crypto-browserify"].join("\n"))}})},{112:112,114:114,124:124,37:37,41:41,42:42,52:52,53:53,56:56,66:66}],58:[function(t,e,n){function r(){var t;try{t=n.storage.debug}catch(e){}return t}n=e.exports=t(59),n.log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},n.formatArgs=function(){var t=arguments,e=this.useColors;if(t[0]=(e?"%c":"")+this.namespace+(e?" %c":" ")+t[0]+(e?"%c ":" ")+"+"+n.humanize(this.diff),!e)return t;var e="color: "+this.color,t=[t[0],e,"color: inherit"].concat(Array.prototype.slice.call(t,1)),r=0,i=0;return t[0].replace(/%[a-z%]/g,function(t){"%%"!==t&&(r++,"%c"===t&&(i=r))}),t.splice(i,0,e),t},n.save=function(t){try{null==t?n.storage.removeItem("debug"):n.storage.debug=t}catch(e){}},n.load=r,n.useColors=function(){return"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)};var i;if("undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage)i=chrome.storage.local;else t:{try{i=window.localStorage;break t}catch(o){}i=void 0}n.storage=i,n.colors="lightseagreen forestgreen goldenrod dodgerblue darkorchid crimson".split(" "),n.formatters.j=function(t){return JSON.stringify(t)},n.enable(r())},{59:59}],59:[function(t,e,n){n=e.exports=function(t){function e(){}function o(){var t=+new Date;o.diff=t-(r||t),o.prev=r,r=o.curr=t,null==o.useColors&&(o.useColors=n.useColors()),null==o.color&&o.useColors&&(o.color=n.colors[i++%n.colors.length]);var e=Array.prototype.slice.call(arguments);e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&(e=["%o"].concat(e));var a=0;e[0]=e[0].replace(/%([a-z%])/g,function(t,r){if("%%"===t)return t;a++;var i=n.formatters[r];return"function"==typeof i&&(t=i.call(o,e[a]),e.splice(a,1),a--),t}),"function"==typeof n.formatArgs&&(e=n.formatArgs.apply(o,e)),(o.log||n.log||console.log.bind(console)).apply(o,e)}e.enabled=!1,o.enabled=!0;var a=n.enabled(t)?o:e;return a.namespace=t,a},n.coerce=function(t){return t instanceof Error?t.stack||t.message:t},n.disable=function(){n.enable("")},n.enable=function(t){n.save(t);for(var e=(t||"").split(/[\s,]+/),r=e.length,i=0;r>i;i++)e[i]&&(t=e[i].replace(/\*/g,".*?"),"-"===t[0]?n.skips.push(new RegExp("^"+t.substr(1)+"$")):n.names.push(new RegExp("^"+t+"$")))},n.enabled=function(t){var e,r;for(e=0,r=n.skips.length;r>e;e++)if(n.skips[e].test(t))return!1;for(e=0,r=n.names.length;r>e;e++)if(n.names[e].test(t))return!0;return!1},n.humanize=t(106),n.names=[],n.skips=[],n.formatters={};var r,i=0},{106:106}],60:[function(t,e,n){n.utils=t(65),n.Cipher=t(62),n.DES=t(63),n.CBC=t(61),n.EDE=t(64)},{61:61,62:62,63:63,64:64,65:65}],61:[function(t,e,n){function r(t){i.equal(t.length,8,"Invalid IV length"),this.iv=Array(8);for(var e=0;e<this.iv.length;e++)this.iv[e]=t[e]}var i=t(105),o=t(101),a={};n.instantiate=function(t){function e(e){t.call(this,e),this._cbcInit()}o(e,t);for(var n=Object.keys(a),r=0;r<n.length;r++){var i=n[r];e.prototype[i]=a[i]}return e.create=function(t){return new e(t)},e},a._cbcInit=function(){this._cbcState=new r(this.options.iv)},a._update=function(t,e,n,r){var i=this.constructor.super_.prototype,o=this._cbcState.iv;if("encrypt"===this.type){for(var a=0;a<this.blockSize;a++)o[a]^=t[e+a];for(i._update.call(this,o,0,n,r),a=0;a<this.blockSize;a++)o[a]=n[r+a]}else{for(i._update.call(this,t,e,n,r),a=0;a<this.blockSize;a++)n[r+a]^=o[a];for(a=0;a<this.blockSize;a++)o[a]=t[e+a]}}},{101:101,105:105}],62:[function(t,e,n){function r(t){this.options=t,this.type=this.options.type,this.blockSize=8,this._init(),this.buffer=Array(this.blockSize),this.bufferOff=0}var i=t(105);e.exports=r,r.prototype._init=function(){},r.prototype.update=function(t){return 0===t.length?[]:"decrypt"===this.type?this._updateDecrypt(t):this._updateEncrypt(t)},r.prototype._buffer=function(t,e){for(var n=Math.min(this.buffer.length-this.bufferOff,t.length-e),r=0;n>r;r++)this.buffer[this.bufferOff+r]=t[e+r];return this.bufferOff+=n,n},r.prototype._flushBuffer=function(t,e){return this._update(this.buffer,0,t,e),this.bufferOff=0,this.blockSize},r.prototype._updateEncrypt=function(t){var e=0,n=0,r=Array(((this.bufferOff+t.length)/this.blockSize|0)*this.blockSize);0!==this.bufferOff&&(e+=this._buffer(t,e),this.bufferOff===this.buffer.length&&(n+=this._flushBuffer(r,n)));for(var i=t.length-(t.length-e)%this.blockSize;i>e;e+=this.blockSize)this._update(t,e,r,n),n+=this.blockSize;for(;e<t.length;e++,this.bufferOff++)this.buffer[this.bufferOff]=t[e];return r},r.prototype._updateDecrypt=function(t){for(var e=0,n=0,r=Math.ceil((this.bufferOff+t.length)/this.blockSize)-1,i=Array(r*this.blockSize);r>0;r--)e+=this._buffer(t,e),n+=this._flushBuffer(i,n);return this._buffer(t,e),i},r.prototype["final"]=function(t){var e;return t&&(e=this.update(t)),t="encrypt"===this.type?this._finalEncrypt():this._finalDecrypt(),e?e.concat(t):t},r.prototype._pad=function(t,e){if(0===e)return!1;for(;e<t.length;)t[e++]=0;return!0},r.prototype._finalEncrypt=function(){if(!this._pad(this.buffer,this.bufferOff))return[];var t=Array(this.blockSize);return this._update(this.buffer,0,t,0),t},r.prototype._unpad=function(t){return t},r.prototype._finalDecrypt=function(){i.equal(this.bufferOff,this.blockSize,"Not enough data to decrypt");var t=Array(this.blockSize);return this._flushBuffer(t,0),this._unpad(t)}},{105:105}],63:[function(t,e,n){function r(){this.tmp=Array(2),this.keys=null}function i(t){s.call(this,t);var e=new r;this._desState=e,this.deriveKeys(e,t.key)}var o=t(105);n=t(101),t=t(60);var a=t.utils,s=t.Cipher;n(i,s),e.exports=i,i.create=function(t){return new i(t)};var c=[1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1];i.prototype.deriveKeys=function(t,e){t.keys=Array(32),o.equal(e.length,this.blockSize,"Invalid key length");var n=a.readUInt32BE(e,0),r=a.readUInt32BE(e,4);a.pc1(n,r,t.tmp,0);for(var n=t.tmp[0],r=t.tmp[1],i=0;i<t.keys.length;i+=2){var s=c[i>>>1],n=a.r28shl(n,s),r=a.r28shl(r,s);a.pc2(n,r,t.keys,i)}},i.prototype._update=function(t,e,n,r){var i=this._desState,o=a.readUInt32BE(t,e);t=a.readUInt32BE(t,e+4),a.ip(o,t,i.tmp,0),o=i.tmp[0],t=i.tmp[1],"encrypt"===this.type?this._encrypt(i,o,t,i.tmp,0):this._decrypt(i,o,t,i.tmp,0),o=i.tmp[0],t=i.tmp[1],a.writeUInt32BE(n,o,r),a.writeUInt32BE(n,t,r+4)},i.prototype._pad=function(t,e){for(var n=t.length-e,r=e;r<t.length;r++)t[r]=n;return!0},i.prototype._unpad=function(t){for(var e=t[t.length-1],n=t.length-e;n<t.length;n++)o.equal(t[n],e);return t.slice(0,t.length-e)},i.prototype._encrypt=function(t,e,n,r,i){for(var o=0;o<t.keys.length;o+=2){var s=t.keys[o],c=t.keys[o+1];a.expand(n,t.tmp,0),s^=t.tmp[0],c^=t.tmp[1],s=a.substitute(s,c),s=a.permute(s),c=n,n=(e^s)>>>0,e=c}a.rip(n,e,r,i)},i.prototype._decrypt=function(t,e,n,r,i){for(var o=t.keys.length-2;o>=0;o-=2){var s=t.keys[o],c=t.keys[o+1];a.expand(n,t.tmp,0),s^=t.tmp[0],c^=t.tmp[1],s=a.substitute(s,c),s=a.permute(s),c=n,n=(e^s)>>>0,e=c}a.rip(n,e,r,i)}},{101:101,105:105,60:60}],64:[function(t,e,n){function r(t,e){o.equal(e.length,24,"Invalid key length");var n=e.slice(0,8),r=e.slice(8,16),i=e.slice(16,24);this.ciphers="encrypt"===t?[s.create({type:"encrypt",key:n}),s.create({type:"decrypt",key:r}),s.create({type:"encrypt",key:i})]:[s.create({type:"decrypt",key:i}),s.create({type:"encrypt",key:r}),s.create({type:"decrypt",key:n})]}function i(t){a.call(this,t),this._edeState=new r(this.type,this.options.key)}var o=t(105);n=t(101),t=t(60);var a=t.Cipher,s=t.DES;n(i,a),e.exports=i,i.create=function(t){return new i(t)},i.prototype._update=function(t,e,n,r){var i=this._edeState;i.ciphers[0]._update(t,e,n,r),i.ciphers[1]._update(n,r,n,r),i.ciphers[2]._update(n,r,n,r)},i.prototype._pad=s.prototype._pad,i.prototype._unpad=s.prototype._unpad},{101:101,105:105,60:60}],65:[function(t,e,n){n.readUInt32BE=function(t,e){return(t[0+e]<<24|t[1+e]<<16|t[2+e]<<8|t[3+e])>>>0},n.writeUInt32BE=function(t,e,n){t[0+n]=e>>>24,t[1+n]=e>>>16&255,t[2+n]=e>>>8&255,t[3+n]=255&e},n.ip=function(t,e,n,r){for(var i=0,o=0,a=6;a>=0;a-=2){for(var s=0;24>=s;s+=8)i<<=1,i|=e>>>s+a&1;for(s=0;24>=s;s+=8)i<<=1,i|=t>>>s+a&1}for(a=6;a>=0;a-=2){for(s=1;25>=s;s+=8)o<<=1,o|=e>>>s+a&1;for(s=1;25>=s;s+=8)o<<=1,o|=t>>>s+a&1}n[r+0]=i>>>0,n[r+1]=o>>>0},n.rip=function(t,e,n,r){for(var i=0,o=0,a=0;4>a;a++)for(var s=24;s>=0;s-=8)i<<=1,i|=e>>>s+a&1,i<<=1,i|=t>>>s+a&1;for(a=4;8>a;a++)for(s=24;s>=0;s-=8)o<<=1,o|=e>>>s+a&1,o<<=1,o|=t>>>s+a&1;n[r+0]=i>>>0,n[r+1]=o>>>0},n.pc1=function(t,e,n,r){for(var i=0,o=0,a=7;a>=5;a--){for(var s=0;24>=s;s+=8)i<<=1,i|=e>>s+a&1;for(s=0;24>=s;s+=8)i<<=1,i|=t>>s+a&1}for(s=0;24>=s;s+=8)i<<=1,i|=e>>s+a&1;for(a=1;3>=a;a++){for(s=0;24>=s;s+=8)o<<=1,o|=e>>s+a&1;for(s=0;24>=s;s+=8)o<<=1,o|=t>>s+a&1}for(s=0;24>=s;s+=8)o<<=1,o|=t>>s+a&1;n[r+0]=i>>>0,n[r+1]=o>>>0},n.r28shl=function(t,e){return t<<e&268435455|t>>>28-e};var r=[14,11,17,4,27,23,25,0,13,22,7,18,5,9,16,24,2,20,12,21,1,8,15,26,15,4,25,19,9,1,26,16,5,11,23,8,12,7,17,0,22,3,10,14,6,20,27,24];n.pc2=function(t,e,n,i){for(var o=0,a=0,s=r.length>>>1,c=0;s>c;c++)o<<=1,o|=t>>>r[c]&1;for(c=s;c<r.length;c++)a<<=1,a|=e>>>r[c]&1;n[i+0]=o>>>0,n[i+1]=a>>>0},n.expand=function(t,e,n){var r,i=0;r=(1&t)<<5|t>>>27;for(var o=23;o>=15;o-=4)r<<=6,r|=t>>>o&63;for(o=11;o>=3;o-=4)i|=t>>>o&63,i<<=6;e[n+0]=r>>>0,e[n+1]=(i|(31&t)<<1|t>>>31)>>>0};var i=[14,0,4,15,13,7,1,4,2,14,15,2,11,13,8,1,3,10,10,6,6,12,12,11,5,9,9,5,0,3,7,8,4,15,1,12,14,8,8,2,13,4,6,9,2,1,11,7,15,5,12,11,9,3,7,14,3,10,10,0,5,6,0,13,15,3,1,13,8,4,14,7,6,15,11,2,3,8,4,14,9,12,7,0,2,1,13,10,12,6,0,9,5,11,10,5,0,13,14,8,7,10,11,1,10,3,4,15,13,4,1,2,5,11,8,6,12,7,6,12,9,0,3,5,2,14,15,9,10,13,0,7,9,0,14,9,6,3,3,4,15,6,5,10,1,2,13,8,12,5,7,14,11,12,4,11,2,15,8,1,13,1,6,10,4,13,9,0,8,6,15,9,3,8,0,7,11,4,1,15,2,14,12,3,5,11,10,5,14,2,7,12,7,13,13,8,14,11,3,5,0,6,6,15,9,0,10,3,1,4,2,7,8,2,5,12,11,1,12,10,4,14,15,9,10,3,6,15,9,0,0,6,12,10,11,1,7,13,13,8,15,9,1,4,3,5,14,11,5,12,2,7,8,2,4,14,2,14,12,11,4,2,1,12,7,4,10,7,11,13,6,1,8,5,5,0,3,15,15,10,13,3,0,9,14,8,9,6,4,11,2,8,1,12,11,7,10,1,13,14,7,2,8,13,15,6,9,15,12,0,5,9,6,10,3,4,0,5,14,3,12,10,1,15,10,4,15,2,9,7,2,12,6,9,8,5,0,6,13,1,3,13,4,14,14,0,7,11,5,3,11,8,9,4,14,3,15,2,5,12,2,9,8,5,12,15,3,10,7,11,0,14,4,1,10,7,1,6,13,0,11,8,6,13,4,13,11,0,2,11,14,7,15,4,0,9,8,1,13,10,3,14,12,3,9,5,7,12,5,2,10,15,6,8,1,6,1,6,4,11,11,13,13,8,12,1,3,4,7,10,14,7,10,9,15,5,6,0,8,15,0,14,5,2,9,3,2,12,13,1,2,15,8,13,4,8,6,10,15,3,11,7,1,4,10,12,9,5,3,6,14,11,5,0,0,14,12,9,7,2,7,2,11,1,4,14,1,7,9,4,12,10,14,8,2,13,0,15,6,12,10,9,13,0,15,3,3,5,5,6,8,11];n.substitute=function(t,e){for(var n=0,r=0;4>r;r++)var o=t>>>18-6*r&63,o=i[64*r+o],n=n<<4,n=n|o;for(r=0;4>r;r++)o=e>>>18-6*r&63,o=i[256+64*r+o],n<<=4,n|=o;return n>>>0};var o=[16,25,12,11,3,20,4,15,31,17,9,6,27,14,1,22,30,24,8,18,0,5,29,23,13,19,2,26,10,21,28,7];n.permute=function(t){for(var e=0,n=0;n<o.length;n++)e<<=1,e|=t>>>o[n]&1;return e>>>0},n.padSplit=function(t,e,n){for(t=t.toString(2);t.length<e;)t="0"+t;for(var r=[],i=0;e>i;i+=n)r.push(t.slice(i,i+n));return r.join(" ")}},{}],66:[function(t,e,n){(function(e){function r(t,n,o,c){return e.isBuffer(n)||void 0===s[n]?r(t,"binary",n,o):(n=n||"binary",c=c||"binary",o=o||new e([2]),e.isBuffer(o)||(o=new e(o,c)),"number"==typeof t?new a(i(t,o),o,!0):(e.isBuffer(t)||(t=new e(t,n)),new a(t,o,!0)))}var i=t(68),o=t(69),a=t(67),s={binary:!0,hex:!0,base64:!0};n.DiffieHellmanGroup=n.createDiffieHellmanGroup=n.getDiffieHellman=function(t){var n=new e(o[t].prime,"hex");return t=new e(o[t].gen,"hex"),new a(n,t)},n.createDiffieHellman=n.DiffieHellman=r}).call(this,t(48).Buffer)},{48:48,67:67,68:68,69:69}],67:[function(t,e,n){(function(n){function r(t,e){return e=e||"utf8",n.isBuffer(t)||(t=new n(t,e)),this._pub=new s(t),this}function i(t,e){return e=e||"utf8",n.isBuffer(t)||(t=new n(t,e)),this._priv=new s(t),this}function o(t,e,n){this.setGenerator(e),this.__prime=new s(t),this._prime=s.mont(this.__prime),this._primeLen=t.length,this._primeCode=this._priv=this._pub=void 0,n?(this.setPublicKey=r,this.setPrivateKey=i):this._primeCode=8}function a(t,e){var r=new n(t.toArray());return e?r.toString(e):r}var s=t(19),c=new(t(104)),f=new s(24),u=new s(11),d=new s(10),p=new s(3),h=new s(7),l=t(68),b=t(124);e.exports=o;var m={};Object.defineProperty(o.prototype,"verifyError",{enumerable:!0,get:function(){if("number"!=typeof this._primeCode){var t,e=this.__prime,n=this.__gen.toString("hex");if(t=[n,e.toString(16)].join("_"),t in m)t=m[t];else{var r=0;if(!e.isEven()&&l.simpleSieve&&l.fermatTest(e)&&c.test(e))switch(c.test(e.shrn(1))||(r+=2),n){case"02":e.mod(f).cmp(u)&&(r+=8);break;case"05":e=e.mod(d),e.cmp(p)&&e.cmp(h)&&(r+=8);break;default:r+=4}else r+=1,r="02"===n||"05"===n?r+8:r+4;t=m[t]=r}this._primeCode=t}return this._primeCode}}),o.prototype.generateKeys=function(){return this._priv||(this._priv=new s(b(this._primeLen))),this._pub=this._gen.toRed(this._prime).redPow(this._priv).fromRed(),this.getPublicKey()},o.prototype.computeSecret=function(t){t=new s(t),t=t.toRed(this._prime),t=t.redPow(this._priv).fromRed(),t=new n(t.toArray());var e=this.getPrime();return t.length<e.length&&(e=new n(e.length-t.length),e.fill(0),t=n.concat([e,t])),t},o.prototype.getPublicKey=function(t){return a(this._pub,t)},o.prototype.getPrivateKey=function(t){return a(this._priv,t)},o.prototype.getPrime=function(t){return a(this.__prime,t)},o.prototype.getGenerator=function(t){return a(this._gen,t)},o.prototype.setGenerator=function(t,e){return e=e||"utf8",n.isBuffer(t)||(t=new n(t,e)),this.__gen=t,this._gen=new s(t),this}}).call(this,t(48).Buffer)},{104:104,124:124,19:19,48:48,68:68}],68:[function(t,e,n){function r(t){var e;if(null!==v)e=v;else{e=[2];for(var n=1,r=3;1048576>r;r+=2){for(var i=Math.ceil(Math.sqrt(r)),o=0;n>o&&e[o]<=i&&0!==r%e[o];o++);n!==o&&e[o]<=i||(e[n++]=r)}v=e}for(n=0;n<e.length;n++)if(0===t.modn(e[n])){if(0===t.cmpn(e[n]))break;return!1}return!0}function i(t){var e=s.mont(t);return 0===d.toRed(e).redPow(t.subn(1)).fromRed().cmpn(1)}function o(t,e){if(16>t)return new s(2===e||5===e?[140,123]:[140,39]);e=new s(e);for(var n,o;;){for(n=new s(a(Math.ceil(t/8)));n.bitLength()>t;)n.ishrn(1);if(n.isEven()&&n.iadd(u),n.testn(1)||n.iadd(d),e.cmp(d)){if(!e.cmp(p))for(;n.mod(h).cmp(l);)n.iadd(m)}else for(;n.mod(c).cmp(b);)n.iadd(m);if(o=n.shrn(1),r(o)&&r(n)&&i(o)&&i(n)&&f.test(o)&&f.test(n))return n}}var a=t(124);e.exports=o,o.simpleSieve=r,o.fermatTest=i;var s=t(19),c=new s(24),f=new(t(104)),u=new s(1),d=new s(2),p=new s(5);new s(16),new s(8);var h=new s(10),l=new s(3);new s(7);var b=new s(11),m=new s(4);new s(12);var v=null},{104:104,124:124,19:19}],69:[function(t,e,n){e.exports={modp1:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a63a3620ffffffffffffffff"},modp2:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece65381ffffffffffffffff"},modp5:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca237327ffffffffffffffff"},modp14:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aacaa68ffffffffffffffff"},modp15:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a93ad2caffffffffffffffff"},modp16:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a92108011a723c12a787e6d788719a10bdba5b2699c327186af4e23c1a946834b6150bda2583e9ca2ad44ce8dbbbc2db04de8ef92e8efc141fbecaa6287c59474e6bc05d99b2964fa090c3a2233ba186515be7ed1f612970cee2d7afb81bdd762170481cd0069127d5b05aa993b4ea988d8fddc186ffb7dc90a6c08f4df435c934063199ffffffffffffffff"},modp17:{gen:"02",prime:"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a92108011a723c12a787e6d788719a10bdba5b2699c327186af4e23c1a946834b6150bda2583e9ca2ad44ce8dbbbc2db04de8ef92e8efc141fbecaa6287c59474e6bc05d99b2964fa090c3a2233ba186515be7ed1f612970cee2d7afb81bdd762170481cd0069127d5b05aa993b4ea988d8fddc186ffb7dc90a6c08f4df435c93402849236c3fab4d27c7026c1d4dcb2602646dec9751e763dba37bdf8ff9406ad9e530ee5db382f413001aeb06a53ed9027d831179727b0865a8918da3edbebcf9b14ed44ce6cbaced4bb1bdb7f1447e6cc254b332051512bd7af426fb8f401378cd2bf5983ca01c64b92ecf032ea15d1721d03f482d7ce6e74fef6d55e702f46980c82b5a84031900b1c9e59e7c97fbec7e8f323a97a7e36cc88be0f1d45b7ff585ac54bd407b22b4154aacc8f6d7ebf48e1d814cc5ed20f8037e0a79715eef29be32806a1d58bb7c5da76f550aa3d8a1fbff0eb19ccb1a313d55cda56c9ec2ef29632387fe8d76e3c0468043e8f663f4860ee12bf2d5b0b7474d6e694f91e6dcc4024ffffffffffffffff"},modp18:{gen:"02",prime:"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"}}},{}],70:[function(t,e,n){n.version=t(86).version,n.utils=t(85),n.rand=t(20),n.hmacDRBG=t(83),n.curve=t(73),n.curves=t(76),n.ec=t(77),n.eddsa=t(80)},{20:20,73:73,76:76,77:77,80:80,83:83,85:85,86:86}],71:[function(t,e,n){function r(t,e){this.type=t,this.p=new o(e.p,16),this.red=e.prime?o.red(e.prime):o.mont(this.p),this.zero=new o(0).toRed(this.red),this.one=new o(1).toRed(this.red),this.two=new o(2).toRed(this.red),this.n=e.n&&new o(e.n,16),this.g=e.g&&this.pointFromJSON(e.g,e.gRed),this._wnafT1=Array(4),this._wnafT2=Array(4),this._wnafT3=Array(4),this._wnafT4=Array(4)}function i(t,e){this.curve=t,this.type=e,this.precomputed=null}var o=t(19),a=t(70).utils,s=a.getNAF,c=a.getJSF,f=a.assert;e.exports=r,r.prototype.point=function(){throw Error("Not implemented")},r.prototype.validate=function(){throw Error("Not implemented")},r.prototype._fixedNafMul=function(t,e){f(t.precomputed);for(var n=t._getDoubles(),r=s(e,1),i=(1<<n.step+1)-(0===n.step%2?2:1),i=i/3,o=[],a=0;a<r.length;a+=n.step){var c=0;for(e=a+n.step-1;e>=a;e--)c=(c<<1)+r[e];o.push(c)}for(var r=this.jpoint(null,null,null),u=this.jpoint(null,null,null);i>0;i--){for(a=0;a<o.length;a++)c=o[a],c===i?u=u.mixedAdd(n.points[a]):c===-i&&(u=u.mixedAdd(n.points[a].neg()));r=r.add(u)}return r.toP()},r.prototype._wnafMul=function(t,e){for(var n=t._getNAFPoints(4),r=n.points,n=s(e,n.wnd),i=this.jpoint(null,null,null),o=n.length-1;o>=0;o--){for(e=0;o>=0&&0===n[o];o--)e++;if(o>=0&&e++,i=i.dblp(e),0>o)break;var a=n[o];f(0!==a),i="affine"===t.type?a>0?i.mixedAdd(r[a-1>>1]):i.mixedAdd(r[-a-1>>1].neg()):a>0?i.add(r[a-1>>1]):i.add(r[-a-1>>1].neg())}return"affine"===t.type?i.toP():i},r.prototype._wnafMulAdd=function(t,e,n,r){for(var i=this._wnafT1,o=this._wnafT2,a=this._wnafT3,f=0,u=0;r>u;u++){var d=e[u],p=d._getNAFPoints(t);i[u]=p.wnd,o[u]=p.points}for(u=r-1;u>=1;u-=2){var p=u-1,h=u;if(1!==i[p]||1!==i[h])a[p]=s(n[p],i[p]),a[h]=s(n[h],i[h]),f=Math.max(a[p].length,f),f=Math.max(a[h].length,f);else{var l=[e[p],null,null,e[h]];0===e[p].y.cmp(e[h].y)?(l[1]=e[p].add(e[h]),l[2]=e[p].toJ().mixedAdd(e[h].neg())):0===e[p].y.cmp(e[h].y.redNeg())?(l[1]=e[p].toJ().mixedAdd(e[h]),l[2]=e[p].add(e[h].neg())):(l[1]=e[p].toJ().mixedAdd(e[h]),l[2]=e[p].toJ().mixedAdd(e[h].neg()));var b=[-3,-1,-5,-7,0,7,5,1,3],m=c(n[p],n[h]),f=Math.max(m[0].length,f);for(a[p]=Array(f),a[h]=Array(f),t=0;f>t;t++)a[p][t]=b[3*((0|m[0][t])+1)+((0|m[1][t])+1)],a[h][t]=0,o[p]=l}}for(e=this.jpoint(null,null,null),n=this._wnafT4,u=f;u>=0;u--){for(f=0;u>=0;){for(i=!0,t=0;r>t;t++)n[t]=0|a[t][u],0!==n[t]&&(i=!1);if(!i)break;f++,u--}if(u>=0&&f++,e=e.dblp(f),0>u)break;for(t=0;r>t;t++)f=n[t],0!==f&&(f>0?d=o[t][f-1>>1]:0>f&&(d=o[t][-f-1>>1].neg()),e="affine"===d.type?e.mixedAdd(d):e.add(d))}for(u=0;r>u;u++)o[u]=null;return e.toP()},r.BasePoint=i,i.prototype.eq=function(){throw Error("Not implemented")},i.prototype.validate=function(){return this.curve.validate(this)},r.prototype.decodePoint=function(t,e){t=a.toArray(t,e);var n=this.p.byteLength();if(4===t[0]&&t.length-1===2*n)return this.point(t.slice(1,1+n),t.slice(1+n,1+2*n));if((2===t[0]||3===t[0])&&t.length-1===n)return this.pointFromX(t.slice(1,1+n),3===t[0]);throw Error("Unknown point format")},i.prototype.encodeCompressed=function(t){return this.encode(t,!0)},i.prototype._encode=function(t){var e=this.curve.p.byteLength(),n=this.getX().toArray("be",e);return t?[this.getY().isEven()?2:3].concat(n):[4].concat(n,this.getY().toArray("be",e))},i.prototype.encode=function(t,e){return a.encode(this._encode(e),t)},i.prototype.precompute=function(t){if(this.precomputed)return this;var e={doubles:null,naf:null,beta:null};return e.naf=this._getNAFPoints(8),e.doubles=this._getDoubles(4,t),e.beta=this._getBeta(),this.precomputed=e,this},i.prototype._hasDoubles=function(t){if(!this.precomputed)return!1;var e=this.precomputed.doubles;return e?e.points.length>=Math.ceil((t.bitLength()+1)/e.step):!1},i.prototype._getDoubles=function(t,e){if(this.precomputed&&this.precomputed.doubles)return this.precomputed.doubles;for(var n=[this],r=this,i=0;e>i;i+=t){for(var o=0;t>o;o++)r=r.dbl();n.push(r)}return{step:t,points:n}},i.prototype._getNAFPoints=function(t){if(this.precomputed&&this.precomputed.naf)return this.precomputed.naf;for(var e=[this],n=(1<<t)-1,r=1===n?null:this.dbl(),i=1;n>i;i++)e[i]=e[i-1].add(r);return{wnd:t,points:e}},i.prototype._getBeta=function(){return null},i.prototype.dblp=function(t){for(var e=this,n=0;t>n;n++)e=e.dbl();return e}},{19:19,70:70}],72:[function(t,e,n){function r(t){this.extended=this.mOneA=(this.twisted=1!==(0|t.a))&&-1===(0|t.a),s.call(this,"edwards",t),this.a=new a(t.a,16).umod(this.red.m),this.a=this.a.toRed(this.red),this.c=new a(t.c,16).toRed(this.red),this.c2=this.c.redSqr(),this.d=new a(t.d,16).toRed(this.red),this.dd=this.d.redAdd(this.d),c(!this.twisted||0===this.c.fromRed().cmpn(1)),this.oneC=1===(0|t.c)}function i(t,e,n,r,i){s.BasePoint.call(this,t,"projective"),null===e&&null===n&&null===r?(this.x=this.curve.zero,this.z=this.y=this.curve.one,this.t=this.curve.zero,this.zOne=!0):(this.x=new a(e,16),this.y=new a(n,16),this.z=r?new a(r,16):this.curve.one,this.t=i&&new a(i,16),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.t&&!this.t.red&&(this.t=this.t.toRed(this.curve.red)),this.zOne=this.z===this.curve.one,this.curve.extended&&!this.t&&(this.t=this.x.redMul(this.y),this.zOne||(this.t=this.t.redMul(this.z.redInvm()))))}n=t(73);var o=t(70),a=t(19);t=t(101);var s=n.base,c=o.utils.assert;t(r,s),e.exports=r,r.prototype._mulA=function(t){return this.mOneA?t.redNeg():this.a.redMul(t)},r.prototype._mulC=function(t){return this.oneC?t:this.c.redMul(t)},r.prototype.jpoint=function(t,e,n,r){return this.point(t,e,n,r)},r.prototype.pointFromX=function(t,e){t=new a(t,16),t.red||(t=t.toRed(this.red));var n=t.redSqr(),r=this.c2.redSub(this.a.redMul(n)),n=this.one.redSub(this.c2.redMul(this.d).redMul(n)),n=r.redMul(n.redInvm()),r=n.redSqrt();if(0!==r.redSqr().redSub(n).cmp(this.zero))throw Error("invalid point");return n=r.fromRed().isOdd(),(e&&!n||!e&&n)&&(r=r.redNeg()),this.point(t,r)},r.prototype.pointFromY=function(t,e){t=new a(t,16),t.red||(t=t.toRed(this.red));var n=t.redSqr(),r=n.redSub(this.one),n=n.redMul(this.d).redAdd(this.one),r=r.redMul(n.redInvm());if(0===r.cmp(this.zero)){if(e)throw Error("invalid point");return this.point(this.zero,t)}if(n=r.redSqrt(),0!==n.redSqr().redSub(r).cmp(this.zero))throw Error("invalid point");return n.isOdd()!==e&&(n=n.redNeg()),this.point(n,t)},r.prototype.validate=function(t){if(t.isInfinity())return!0;t.normalize();var e=t.x.redSqr(),n=t.y.redSqr();return t=e.redMul(this.a).redAdd(n),e=this.c2.redMul(this.one.redAdd(this.d.redMul(e).redMul(n))),0===t.cmp(e)},t(i,s.BasePoint),r.prototype.pointFromJSON=function(t){return i.fromJSON(this,t)},r.prototype.point=function(t,e,n,r){return new i(this,t,e,n,r)},i.fromJSON=function(t,e){return new i(t,e[0],e[1],e[2])},i.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+" z: "+this.z.fromRed().toString(16,2)+">"},i.prototype.isInfinity=function(){return 0===this.x.cmpn(0)&&0===this.y.cmp(this.z)},i.prototype._extDbl=function(){var t=this.x.redSqr(),e=this.y.redSqr(),n=this.z.redSqr(),n=n.redIAdd(n),r=this.curve._mulA(t),i=this.x.redAdd(this.y).redSqr().redISub(t).redISub(e),t=r.redAdd(e),n=t.redSub(n),o=r.redSub(e),e=i.redMul(n),r=t.redMul(o),i=i.redMul(o),t=n.redMul(t);return this.curve.point(e,r,t,i)},i.prototype._projDbl=function(){var t,e=this.x.redAdd(this.y).redSqr(),n=this.x.redSqr(),r=this.y.redSqr();if(this.curve.twisted){t=this.curve._mulA(n);var i=t.redAdd(r);if(this.zOne)e=e.redSub(n).redSub(r).redMul(i.redSub(this.curve.two)),n=i.redMul(t.redSub(r)),t=i.redSqr().redSub(i).redSub(i);else{var o=this.z.redSqr(),o=i.redSub(o).redISub(o),e=e.redSub(n).redISub(r).redMul(o),n=i.redMul(t.redSub(r));t=i.redMul(o)}}else t=n.redAdd(r),o=this.curve._mulC(this.c.redMul(this.z)).redSqr(),o=t.redSub(o).redSub(o),e=this.curve._mulC(e.redISub(t)).redMul(o),n=this.curve._mulC(t).redMul(n.redISub(r)),
t=t.redMul(o);return this.curve.point(e,n,t)},i.prototype.dbl=function(){return this.isInfinity()?this:this.curve.extended?this._extDbl():this._projDbl()},i.prototype._extAdd=function(t){var e=this.y.redSub(this.x).redMul(t.y.redSub(t.x)),n=this.y.redAdd(this.x).redMul(t.y.redAdd(t.x)),r=this.t.redMul(this.curve.dd).redMul(t.t),i=this.z.redMul(t.z.redAdd(t.z)),o=n.redSub(e);return t=i.redSub(r),r=i.redAdd(r),i=n.redAdd(e),e=o.redMul(t),n=r.redMul(i),o=o.redMul(i),t=t.redMul(r),this.curve.point(e,n,t,o)},i.prototype._projAdd=function(t){var e=this.z.redMul(t.z),n=e.redSqr(),r=this.x.redMul(t.x),i=this.y.redMul(t.y),o=this.curve.d.redMul(r).redMul(i),a=n.redSub(o),n=n.redAdd(o);return t=this.x.redAdd(this.y).redMul(t.x.redAdd(t.y)).redISub(r).redISub(i),t=e.redMul(a).redMul(t),this.curve.twisted?(e=e.redMul(n).redMul(i.redSub(this.curve._mulA(r))),a=a.redMul(n)):(e=e.redMul(n).redMul(i.redSub(r)),a=this.curve._mulC(a).redMul(n)),this.curve.point(t,e,a)},i.prototype.add=function(t){return this.isInfinity()?t:t.isInfinity()?this:this.curve.extended?this._extAdd(t):this._projAdd(t)},i.prototype.mul=function(t){return this._hasDoubles(t)?this.curve._fixedNafMul(this,t):this.curve._wnafMul(this,t)},i.prototype.mulAdd=function(t,e,n){return this.curve._wnafMulAdd(1,[this,e],[t,n],2)},i.prototype.normalize=function(){if(this.zOne)return this;var t=this.z.redInvm();return this.x=this.x.redMul(t),this.y=this.y.redMul(t),this.t&&(this.t=this.t.redMul(t)),this.z=this.curve.one,this.zOne=!0,this},i.prototype.neg=function(){return this.curve.point(this.x.redNeg(),this.y,this.z,this.t&&this.t.redNeg())},i.prototype.getX=function(){return this.normalize(),this.x.fromRed()},i.prototype.getY=function(){return this.normalize(),this.y.fromRed()},i.prototype.eq=function(t){return this===t||0===this.getX().cmp(t.getX())&&0===this.getY().cmp(t.getY())},i.prototype.toP=i.prototype.normalize,i.prototype.mixedAdd=i.prototype.add},{101:101,19:19,70:70,73:73}],73:[function(t,e,n){n.base=t(71),n["short"]=t(75),n.mont=t(74),n.edwards=t(72)},{71:71,72:72,74:74,75:75}],74:[function(t,e,n){function r(t){s.call(this,"mont",t),this.a=new o(t.a,16).toRed(this.red),this.b=new o(t.b,16).toRed(this.red),this.i4=new o(4).toRed(this.red).redInvm(),this.two=new o(2).toRed(this.red),this.a24=this.i4.redMul(this.a.redAdd(this.two))}function i(t,e,n){s.BasePoint.call(this,t,"projective"),null===e&&null===n?(this.x=this.curve.one,this.z=this.curve.zero):(this.x=new o(e,16),this.z=new o(n,16),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)))}n=t(73);var o=t(19),a=t(101),s=n.base,c=t(70).utils;a(r,s),e.exports=r,r.prototype.validate=function(t){t=t.normalize().x;var e=t.redSqr();return t=e.redMul(t).redAdd(e.redMul(this.a)).redAdd(t),0===t.redSqrt().redSqr().cmp(t)},a(i,s.BasePoint),r.prototype.decodePoint=function(t,e){return this.point(c.toArray(t,e),1)},r.prototype.point=function(t,e){return new i(this,t,e)},r.prototype.pointFromJSON=function(t){return i.fromJSON(this,t)},i.prototype.precompute=function(){},i.prototype._encode=function(){return this.getX().toArray("be",this.curve.p.byteLength())},i.fromJSON=function(t,e){return new i(t,e[0],e[1]||t.one)},i.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" z: "+this.z.fromRed().toString(16,2)+">"},i.prototype.isInfinity=function(){return 0===this.z.cmpn(0)},i.prototype.dbl=function(){var t=this.x.redAdd(this.z).redSqr(),e=this.x.redSub(this.z).redSqr(),n=t.redSub(e),t=t.redMul(e),e=n.redMul(e.redAdd(this.curve.a24.redMul(n)));return this.curve.point(t,e)},i.prototype.add=function(){throw Error("Not supported on Montgomery curve")},i.prototype.diffAdd=function(t,e){var n=this.x.redAdd(this.z),r=this.x.redSub(this.z),i=t.x.redAdd(t.z),n=t.x.redSub(t.z).redMul(n),i=i.redMul(r),r=e.z.redMul(n.redAdd(i).redSqr()),n=e.x.redMul(n.redISub(i).redSqr());return this.curve.point(r,n)},i.prototype.mul=function(t){var e=t.clone();t=this;for(var n=this.curve.point(null,null),r=[];0!==e.cmpn(0);e.iushrn(1))r.push(e.andln(1));for(e=r.length-1;e>=0;e--)0===r[e]?(t=t.diffAdd(n,this),n=n.dbl()):(n=t.diffAdd(n,this),t=t.dbl());return n},i.prototype.mulAdd=function(){throw Error("Not supported on Montgomery curve")},i.prototype.eq=function(t){return 0===this.getX().cmp(t.getX())},i.prototype.normalize=function(){return this.x=this.x.redMul(this.z.redInvm()),this.z=this.curve.one,this},i.prototype.getX=function(){return this.normalize(),this.x.fromRed()}},{101:101,19:19,70:70,73:73}],75:[function(t,e,n){function r(t){c.call(this,"short",t),this.a=new s(t.a,16).toRed(this.red),this.b=new s(t.b,16).toRed(this.red),this.tinv=this.two.redInvm(),this.zeroA=0===this.a.fromRed().cmpn(0),this.threeA=0===this.a.fromRed().sub(this.p).cmpn(-3),this.endo=this._getEndomorphism(t),this._endoWnafT1=Array(4),this._endoWnafT2=Array(4)}function i(t,e,n,r){c.BasePoint.call(this,t,"affine"),null===e&&null===n?(this.y=this.x=null,this.inf=!0):(this.x=new s(e,16),this.y=new s(n,16),r&&(this.x.forceRed(this.curve.red),this.y.forceRed(this.curve.red)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.inf=!1)}function o(t,e,n,r){c.BasePoint.call(this,t,"jacobian"),null===e&&null===n&&null===r?(this.y=this.x=this.curve.one,this.z=new s(0)):(this.x=new s(e,16),this.y=new s(n,16),this.z=new s(r,16)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.zOne=this.z===this.curve.one}n=t(73);var a=t(70),s=t(19);t=t(101);var c=n.base,f=a.utils.assert;t(r,c),e.exports=r,r.prototype._getEndomorphism=function(t){if(this.zeroA&&this.g&&this.n&&1===this.p.modn(3)){var e,n;return t.beta?e=new s(t.beta,16).toRed(this.red):(e=this._getEndoRoots(this.p),e=0>e[0].cmp(e[1])?e[0]:e[1],e=e.toRed(this.red)),t.lambda?n=new s(t.lambda,16):(n=this._getEndoRoots(this.n),0===this.g.mul(n[0]).x.cmp(this.g.x.redMul(e))?n=n[0]:(n=n[1],f(0===this.g.mul(n).x.cmp(this.g.x.redMul(e))))),t=t.basis?t.basis.map(function(t){return{a:new s(t.a,16),b:new s(t.b,16)}}):this._getEndoBasis(n),{beta:e,lambda:n,basis:t}}},r.prototype._getEndoRoots=function(t){var e=t===this.p?this.red:s.mont(t),n=new s(2).toRed(e).redInvm();return t=n.redNeg(),n=new s(3).toRed(e).redNeg().redSqrt().redMul(n),e=t.redAdd(n).fromRed(),t=t.redSub(n).fromRed(),[e,t]},r.prototype._getEndoBasis=function(t){for(var e,n,r,i,o,a,c,f=this.n.ushrn(Math.floor(this.n.bitLength()/2)),u=this.n.clone(),d=new s(1),p=new s(0),h=new s(0),l=new s(1),b=0;0!==t.cmpn(0);){var m=u.div(t);if(c=u.sub(m.mul(t)),o=h.sub(m.mul(d)),m=l.sub(m.mul(p)),!r&&0>c.cmp(f))e=a.neg(),n=d,r=c.neg(),i=o;else if(r&&2===++b)break;a=c,u=t,t=c,h=d,d=o,l=p,p=m}return f=c.neg(),a=r.sqr().add(i.sqr()),0<=f.sqr().add(o.sqr()).cmp(a)&&(f=e,o=n),r.negative&&(r=r.neg(),i=i.neg()),f.negative&&(f=f.neg(),o=o.neg()),[{a:r,b:i},{a:f,b:o}]},r.prototype._endoSplit=function(t){var e=this.endo.basis,n=e[0],r=e[1],i=r.b.mul(t).divRound(this.n),o=n.b.neg().mul(t).divRound(this.n),e=i.mul(n.a),a=o.mul(r.a),n=i.mul(n.b),r=o.mul(r.b);return t=t.sub(e).sub(a),e=n.add(r).neg(),{k1:t,k2:e}},r.prototype.pointFromX=function(t,e){t=new s(t,16),t.red||(t=t.toRed(this.red));var n=t.redSqr().redMul(t).redIAdd(t.redMul(this.a)).redIAdd(this.b),r=n.redSqrt();if(0!==r.redSqr().redSub(n).cmp(this.zero))throw Error("invalid point");return n=r.fromRed().isOdd(),(e&&!n||!e&&n)&&(r=r.redNeg()),this.point(t,r)},r.prototype.validate=function(t){if(t.inf)return!0;var e=t.x;t=t.y;var n=this.a.redMul(e),e=e.redSqr().redMul(e).redIAdd(n).redIAdd(this.b);return 0===t.redSqr().redISub(e).cmpn(0)},r.prototype._endoWnafMulAdd=function(t,e){for(var n=this._endoWnafT1,r=this._endoWnafT2,i=0;i<t.length;i++){var o=this._endoSplit(e[i]),a=t[i],s=a._getBeta();o.k1.negative&&(o.k1.ineg(),a=a.neg(!0)),o.k2.negative&&(o.k2.ineg(),s=s.neg(!0)),n[2*i]=a,n[2*i+1]=s,r[2*i]=o.k1,r[2*i+1]=o.k2}for(o=this._wnafMulAdd(1,n,r,2*i),a=0;2*i>a;a++)n[a]=null,r[a]=null;return o},t(i,c.BasePoint),r.prototype.point=function(t,e,n){return new i(this,t,e,n)},r.prototype.pointFromJSON=function(t,e){return i.fromJSON(this,t,e)},i.prototype._getBeta=function(){if(this.curve.endo){var t=this.precomputed;if(t&&t.beta)return t.beta;var e=this.curve.point(this.x.redMul(this.curve.endo.beta),this.y);if(t){var n=this.curve,r=function(t){return n.point(t.x.redMul(n.endo.beta),t.y)};t.beta=e,e.precomputed={beta:null,naf:t.naf&&{wnd:t.naf.wnd,points:t.naf.points.map(r)},doubles:t.doubles&&{step:t.doubles.step,points:t.doubles.points.map(r)}}}return e}},i.prototype.toJSON=function(){return this.precomputed?[this.x,this.y,this.precomputed&&{doubles:this.precomputed.doubles&&{step:this.precomputed.doubles.step,points:this.precomputed.doubles.points.slice(1)},naf:this.precomputed.naf&&{wnd:this.precomputed.naf.wnd,points:this.precomputed.naf.points.slice(1)}}]:[this.x,this.y]},i.fromJSON=function(t,e,n){function r(e){return t.point(e[0],e[1],n)}"string"==typeof e&&(e=JSON.parse(e));var i=t.point(e[0],e[1],n);return e[2]?(e=e[2],i.precomputed={beta:null,doubles:e.doubles&&{step:e.doubles.step,points:[i].concat(e.doubles.points.map(r))},naf:e.naf&&{wnd:e.naf.wnd,points:[i].concat(e.naf.points.map(r))}},i):i},i.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+">"},i.prototype.isInfinity=function(){return this.inf},i.prototype.add=function(t){if(this.inf)return t;if(t.inf)return this;if(this.eq(t))return this.dbl();if(this.neg().eq(t)||0===this.x.cmp(t.x))return this.curve.point(null,null);var e=this.y.redSub(t.y);return 0!==e.cmpn(0)&&(e=e.redMul(this.x.redSub(t.x).redInvm())),t=e.redSqr().redISub(this.x).redISub(t.x),e=e.redMul(this.x.redSub(t)).redISub(this.y),this.curve.point(t,e)},i.prototype.dbl=function(){if(this.inf)return this;var t=this.y.redAdd(this.y);if(0===t.cmpn(0))return this.curve.point(null,null);var e=this.curve.a,n=this.x.redSqr(),t=t.redInvm(),n=n.redAdd(n).redIAdd(n).redIAdd(e).redMul(t),e=n.redSqr().redISub(this.x.redAdd(this.x)),n=n.redMul(this.x.redSub(e)).redISub(this.y);return this.curve.point(e,n)},i.prototype.getX=function(){return this.x.fromRed()},i.prototype.getY=function(){return this.y.fromRed()},i.prototype.mul=function(t){return t=new s(t,16),this._hasDoubles(t)?this.curve._fixedNafMul(this,t):this.curve.endo?this.curve._endoWnafMulAdd([this],[t]):this.curve._wnafMul(this,t)},i.prototype.mulAdd=function(t,e,n){return e=[this,e],t=[t,n],this.curve.endo?this.curve._endoWnafMulAdd(e,t):this.curve._wnafMulAdd(1,e,t,2)},i.prototype.eq=function(t){return this===t||this.inf===t.inf&&(this.inf||0===this.x.cmp(t.x)&&0===this.y.cmp(t.y))},i.prototype.neg=function(t){if(this.inf)return this;var e=this.curve.point(this.x,this.y.redNeg());if(t&&this.precomputed){t=this.precomputed;var n=function(t){return t.neg()};e.precomputed={naf:t.naf&&{wnd:t.naf.wnd,points:t.naf.points.map(n)},doubles:t.doubles&&{step:t.doubles.step,points:t.doubles.points.map(n)}}}return e},i.prototype.toJ=function(){return this.inf?this.curve.jpoint(null,null,null):this.curve.jpoint(this.x,this.y,this.curve.one)},t(o,c.BasePoint),r.prototype.jpoint=function(t,e,n){return new o(this,t,e,n)},o.prototype.toP=function(){if(this.isInfinity())return this.curve.point(null,null);var t=this.z.redInvm(),e=t.redSqr(),n=this.x.redMul(e),t=this.y.redMul(e).redMul(t);return this.curve.point(n,t)},o.prototype.neg=function(){return this.curve.jpoint(this.x,this.y.redNeg(),this.z)},o.prototype.add=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.z.redSqr(),n=this.z.redSqr(),r=this.x.redMul(e),i=t.x.redMul(n),e=this.y.redMul(e.redMul(t.z)),n=t.y.redMul(n.redMul(this.z)),i=r.redSub(i),n=e.redSub(n);if(0===i.cmpn(0))return 0!==n.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var o=i.redSqr(),a=o.redMul(i),o=r.redMul(o),r=n.redSqr().redIAdd(a).redISub(o).redISub(o),e=n.redMul(o.redISub(r)).redISub(e.redMul(a));return t=this.z.redMul(t.z).redMul(i),this.curve.jpoint(r,e,t)},o.prototype.mixedAdd=function(t){if(this.isInfinity())return t.toJ();if(t.isInfinity())return this;var e=this.z.redSqr(),n=this.x,r=t.x.redMul(e),i=this.y;if(t=t.y.redMul(e).redMul(this.z),r=n.redSub(r),t=i.redSub(t),0===r.cmpn(0))return 0!==t.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var o=r.redSqr(),e=o.redMul(r),o=n.redMul(o),n=t.redSqr().redIAdd(e).redISub(o).redISub(o),i=t.redMul(o.redISub(n)).redISub(i.redMul(e)),r=this.z.redMul(r);return this.curve.jpoint(n,i,r)},o.prototype.dblp=function(t){if(0===t||this.isInfinity())return this;if(!t)return this.dbl();if(this.curve.zeroA||this.curve.threeA){for(var e=this,n=0;t>n;n++)e=e.dbl();return e}for(var e=this.curve.a,r=this.curve.tinv,i=this.x,n=this.y,o=this.z,a=o.redSqr().redSqr(),s=n.redAdd(n),n=0;t>n;n++){var c=i.redSqr(),f=s.redSqr(),u=f.redSqr(),c=c.redAdd(c).redIAdd(c).redIAdd(e.redMul(a)),f=i.redMul(f),i=c.redSqr().redISub(f.redAdd(f)),f=f.redISub(i),c=c.redMul(f),c=c.redIAdd(c).redISub(u),o=s.redMul(o);t>n+1&&(a=a.redMul(u)),s=c}return this.curve.jpoint(i,s.redMul(r),o)},o.prototype.dbl=function(){return this.isInfinity()?this:this.curve.zeroA?this._zeroDbl():this.curve.threeA?this._threeDbl():this._dbl()},o.prototype._zeroDbl=function(){var t,e,n;if(this.zOne){n=this.x.redSqr(),e=this.y.redSqr(),t=e.redSqr(),e=this.x.redAdd(e).redSqr().redISub(n).redISub(t),e=e.redIAdd(e),n=n.redAdd(n).redIAdd(n);var r=n.redSqr().redISub(e).redISub(e),i=t.redIAdd(t),i=i.redIAdd(i),i=i.redIAdd(i);t=r,e=n.redMul(e.redISub(r)).redISub(i),n=this.y.redAdd(this.y)}else n=this.x.redSqr(),e=this.y.redSqr(),t=e.redSqr(),e=this.x.redAdd(e).redSqr().redISub(n).redISub(t),e=e.redIAdd(e),n=n.redAdd(n).redIAdd(n),r=n.redSqr(),i=t.redIAdd(t),i=i.redIAdd(i),i=i.redIAdd(i),t=r.redISub(e).redISub(e),e=n.redMul(e.redISub(t)).redISub(i),n=this.y.redMul(this.z),n=n.redIAdd(n);return this.curve.jpoint(t,e,n)},o.prototype._threeDbl=function(){var t,e,n;if(this.zOne){t=this.x.redSqr();var r=this.y.redSqr();e=r.redSqr();var r=this.x.redAdd(r).redSqr().redISub(t).redISub(e),r=r.redIAdd(r),i=t.redAdd(t).redIAdd(t).redIAdd(this.curve.a);t=n=i.redSqr().redISub(r).redISub(r),e=e.redIAdd(e),e=e.redIAdd(e),e=e.redIAdd(e),e=i.redMul(r.redISub(n)).redISub(e),n=this.y.redAdd(this.y)}else n=this.z.redSqr(),r=this.y.redSqr(),t=this.x.redMul(r),e=this.x.redSub(n).redMul(this.x.redAdd(n)),e=e.redAdd(e).redIAdd(e),i=t.redIAdd(t),i=i.redIAdd(i),t=i.redAdd(i),t=e.redSqr().redISub(t),n=this.y.redAdd(this.z).redSqr().redISub(r).redISub(n),r=r.redSqr(),r=r.redIAdd(r),r=r.redIAdd(r),r=r.redIAdd(r),e=e.redMul(i.redISub(t)).redISub(r);return this.curve.jpoint(t,e,n)},o.prototype._dbl=function(){var t=this.curve.a,e=this.x,n=this.y,r=this.z,i=r.redSqr().redSqr(),o=e.redSqr(),a=n.redSqr(),t=o.redAdd(o).redIAdd(o).redIAdd(t.redMul(i)),e=e.redAdd(e),e=e.redIAdd(e),i=e.redMul(a),e=t.redSqr().redISub(i.redAdd(i)),i=i.redISub(e),a=a.redSqr(),a=a.redIAdd(a),a=a.redIAdd(a),a=a.redIAdd(a),a=t.redMul(i).redISub(a),n=n.redAdd(n).redMul(r);return this.curve.jpoint(e,a,n)},o.prototype.trpl=function(){if(!this.curve.zeroA)return this.dbl().add(this);var t=this.x.redSqr(),e=this.y.redSqr(),n=this.z.redSqr(),r=e.redSqr(),i=t.redAdd(t).redIAdd(t),o=i.redSqr(),t=this.x.redAdd(e).redSqr().redISub(t).redISub(r),t=t.redIAdd(t),t=t.redAdd(t).redIAdd(t),t=t.redISub(o),a=t.redSqr(),r=r.redIAdd(r),r=r.redIAdd(r),r=r.redIAdd(r),r=r.redIAdd(r),i=i.redIAdd(t).redSqr().redISub(o).redISub(a).redISub(r),e=e.redMul(i),e=e.redIAdd(e),e=e.redIAdd(e),e=this.x.redMul(a).redISub(e),e=e.redIAdd(e),e=e.redIAdd(e),r=this.y.redMul(i.redMul(r.redISub(i)).redISub(t.redMul(a))),r=r.redIAdd(r),r=r.redIAdd(r),r=r.redIAdd(r),n=this.z.redAdd(t).redSqr().redISub(n).redISub(a);return this.curve.jpoint(e,r,n)},o.prototype.mul=function(t,e){return t=new s(t,e),this.curve._wnafMul(this,t)},o.prototype.eq=function(t){if("affine"===t.type)return this.eq(t.toJ());if(this===t)return!0;var e=this.z.redSqr(),n=t.z.redSqr();return 0!==this.x.redMul(n).redISub(t.x.redMul(e)).cmpn(0)?!1:(e=e.redMul(this.z),n=n.redMul(t.z),0===this.y.redMul(n).redISub(t.y.redMul(e)).cmpn(0))},o.prototype.inspect=function(){return this.isInfinity()?"<EC JPoint Infinity>":"<EC JPoint x: "+this.x.toString(16,2)+" y: "+this.y.toString(16,2)+" z: "+this.z.toString(16,2)+">"},o.prototype.isInfinity=function(){return 0===this.z.cmpn(0)}},{101:101,19:19,70:70,73:73}],76:[function(t,e,n){function r(t){this.curve="short"===t.type?new a.curve["short"](t):"edwards"===t.type?new a.curve.edwards(t):new a.curve.mont(t),this.g=this.curve.g,this.n=this.curve.n,this.hash=t.hash,s(this.g.validate(),"Invalid curve"),s(this.g.mul(this.n).isInfinity(),"Invalid curve, G*N != O")}function i(t,e){Object.defineProperty(o,t,{configurable:!0,enumerable:!0,get:function(){var n=new r(e);return Object.defineProperty(o,t,{configurable:!0,enumerable:!0,value:n}),n}})}var o=n;e=t(89);var a=t(70),s=a.utils.assert;o.PresetCurve=r,i("p192",{type:"short",prime:"p192",p:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff",a:"ffffffff ffffffff ffffffff fffffffe ffffffff fffffffc",b:"64210519 e59c80e7 0fa7e9ab 72243049 feb8deec c146b9b1",n:"ffffffff ffffffff ffffffff 99def836 146bc9b1 b4d22831",hash:e.sha256,gRed:!1,g:["188da80e b03090f6 7cbf20eb 43a18800 f4ff0afd 82ff1012","07192b95 ffc8da78 631011ed 6b24cdd5 73f977a1 1e794811"]}),i("p224",{type:"short",prime:"p224",p:"ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001",a:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff fffffffe",b:"b4050a85 0c04b3ab f5413256 5044b0b7 d7bfd8ba 270b3943 2355ffb4",n:"ffffffff ffffffff ffffffff ffff16a2 e0b8f03e 13dd2945 5c5c2a3d",hash:e.sha256,gRed:!1,g:["b70e0cbd 6bb4bf7f 321390b9 4a03c1d3 56c21122 343280d6 115c1d21","bd376388 b5f723fb 4c22dfe6 cd4375a0 5a074764 44d58199 85007e34"]}),i("p256",{type:"short",prime:null,p:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff ffffffff",a:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff fffffffc",b:"5ac635d8 aa3a93e7 b3ebbd55 769886bc 651d06b0 cc53b0f6 3bce3c3e 27d2604b",n:"ffffffff 00000000 ffffffff ffffffff bce6faad a7179e84 f3b9cac2 fc632551",hash:e.sha256,gRed:!1,g:["6b17d1f2 e12c4247 f8bce6e5 63a440f2 77037d81 2deb33a0 f4a13945 d898c296","4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16 2bce3357 6b315ece cbb64068 37bf51f5"]}),i("p384",{type:"short",prime:null,p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 ffffffff",a:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 fffffffc",b:"b3312fa7 e23ee7e4 988e056b e3f82d19 181d9c6e fe814112 0314088f 5013875a c656398d 8a2ed19d 2a85c8ed d3ec2aef",n:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff c7634d81 f4372ddf 581a0db2 48b0a77a ecec196a ccc52973",hash:e.sha384,gRed:!1,g:["aa87ca22 be8b0537 8eb1c71e f320ad74 6e1d3b62 8ba79b98 59f741e0 82542a38 5502f25d bf55296c 3a545e38 72760ab7","3617de4a 96262c6f 5d9e98bf 9292dc29 f8f41dbd 289a147c e9da3113 b5f0b8c0 0a60b1ce 1d7e819d 7a431d7c 90ea0e5f"]}),i("p521",{type:"short",prime:null,p:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff",a:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffc",b:"00000051 953eb961 8e1c9a1f 929a21a0 b68540ee a2da725b 99b315f3 b8b48991 8ef109e1 56193951 ec7e937b 1652c0bd 3bb1bf07 3573df88 3d2c34f1 ef451fd4 6b503f00",n:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffa 51868783 bf2f966b 7fcc0148 f709a5d0 3bb5c9b8 899c47ae bb6fb71e 91386409",hash:e.sha512,gRed:!1,g:["000000c6 858e06b7 0404e9cd 9e3ecb66 2395b442 9c648139 053fb521 f828af60 6b4d3dba a14b5e77 efe75928 fe1dc127 a2ffa8de 3348b3c1 856a429b f97e7e31 c2e5bd66","00000118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9 98f54449 579b4468 17afbd17 273e662c 97ee7299 5ef42640 c550b901 3fad0761 353c7086 a272c240 88be9476 9fd16650"]}),i("curve25519",{type:"mont",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"76d06",b:"0",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:e.sha256,gRed:!1,g:["9"]}),i("ed25519",{type:"edwards",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"-1",c:"1",d:"52036cee2b6ffe73 8cc740797779e898 00700a4d4141d8ab 75eb4dca135978a3",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:e.sha256,gRed:!1,g:["216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a","6666666666666666666666666666666666666666666666666666666666666658"]});var c;try{c=t(84)}catch(f){c=void 0}i("secp256k1",{type:"short",prime:"k256",p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f",a:"0",b:"7",n:"ffffffff ffffffff ffffffff fffffffe baaedce6 af48a03b bfd25e8c d0364141",h:"1",hash:e.sha256,beta:"7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee",lambda:"5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72",basis:[{a:"3086d221a7d46bcde86c90e49284eb15",b:"-e4437ed6010e88286f547fa90abfe4c3"},{a:"114ca50f7a8e2f3f657c1108d9d44cfd8",b:"3086d221a7d46bcde86c90e49284eb15"}],gRed:!1,g:["79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798","483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8",c]})},{70:70,84:84,89:89}],77:[function(t,e,n){function r(t){return this instanceof r?("string"==typeof t&&(a(o.curves.hasOwnProperty(t),"Unknown curve "+t),t=o.curves[t]),t instanceof o.curves.PresetCurve&&(t={curve:t}),this.curve=t.curve.curve,this.n=this.curve.n,this.nh=this.n.ushrn(1),this.g=this.curve.g,this.g=t.curve.g,this.g.precompute(t.curve.n.bitLength()+1),void(this.hash=t.hash||t.curve.hash)):new r(t)}var i=t(19),o=t(70),a=o.utils.assert,s=t(78),c=t(79);e.exports=r,r.prototype.keyPair=function(t){return new s(this,t)},r.prototype.keyFromPrivate=function(t,e){return s.fromPrivate(this,t,e)},r.prototype.keyFromPublic=function(t,e){return s.fromPublic(this,t,e)},r.prototype.genKeyPair=function(t){t||(t={}),t=new o.hmacDRBG({hash:this.hash,pers:t.pers,entropy:t.entropy||o.rand(this.hash.hmacStrength),nonce:this.n.toArray()});for(var e=this.n.byteLength(),n=this.n.sub(new i(2));;){var r=new i(t.generate(e));if(!(0<r.cmp(n)))return r.iaddn(1),this.keyFromPrivate(r)}},r.prototype._truncateToN=function(t,e){var n=8*t.byteLength()-this.n.bitLength();return n>0&&(t=t.ushrn(n)),!e&&0<=t.cmp(this.n)?t.sub(this.n):t},r.prototype.sign=function(t,e,n,r){"object"==typeof n&&(r=n,n=null),r||(r={}),e=this.keyFromPrivate(e,n),t=this._truncateToN(new i(t,16));var a=this.n.byteLength();n=e.getPrivate().toArray("be",a);for(var a=t.toArray("be",a),a=new o.hmacDRBG({hash:this.hash,entropy:n,nonce:a,pers:r.pers,persEnc:r.persEnc}),s=this.n.sub(new i(1)),f=0;;f++){var u=r.k?r.k(f):new i(a.generate(this.n.byteLength())),u=this._truncateToN(u,!0);if(!(0>=u.cmpn(1)||0<=u.cmp(s))){var d=this.g.mul(u);if(!d.isInfinity()){var p=d.getX();if(n=p.umod(this.n),0!==n.cmpn(0)&&(u=u.invm(this.n).mul(n.mul(e.getPrivate()).iadd(t)),u=u.umod(this.n),0!==u.cmpn(0)))return t=(d.getY().isOdd()?1:0)|(0!==p.cmp(n)?2:0),r.canonical&&0<u.cmp(this.nh)&&(u=this.n.sub(u),t^=1),new c({r:n,s:u,recoveryParam:t})}}}},r.prototype.verify=function(t,e,n,r){return t=this._truncateToN(new i(t,16)),n=this.keyFromPublic(n,r),e=new c(e,"hex"),r=e.r,e=e.s,0>r.cmpn(1)||0<=r.cmp(this.n)||0>e.cmpn(1)||0<=e.cmp(this.n)?!1:(e=e.invm(this.n),t=e.mul(t).umod(this.n),e=e.mul(r).umod(this.n),n=this.g.mulAdd(t,n.getPublic(),e),n.isInfinity()?!1:0===n.getX().umod(this.n).cmp(r))},r.prototype.recoverPubKey=function(t,e,n,r){a((3&n)===n,"The recovery param is more than two bits"),e=new c(e,r),r=this.n;var o=new i(t);t=e.r;var s=e.s,f=1&n;if(n>>=1,0<=t.cmp(this.curve.p.umod(this.curve.n))&&n)throw Error("Unable to find sencond key candinate");return t=n?this.curve.pointFromX(t.add(this.curve.n),f):this.curve.pointFromX(t,f),n=r.sub(o),e=e.r.invm(r),this.g.mulAdd(n,t,s).mul(e)},r.prototype.getKeyRecoveryParam=function(t,e,n,r){if(e=new c(e,r),null!==e.recoveryParam)return e.recoveryParam;for(r=0;4>r;r++){var i;try{i=this.recoverPubKey(t,e,r)}catch(o){continue}if(i.eq(n))return r}throw Error("Unable to find valid recovery factor")}},{19:19,70:70,78:78,79:79}],78:[function(t,e,n){function r(t,e){this.ec=t,this.pub=this.priv=null,e.priv&&this._importPrivate(e.priv,e.privEnc),e.pub&&this._importPublic(e.pub,e.pubEnc)}var i=t(19);e.exports=r,r.fromPublic=function(t,e,n){return e instanceof r?e:new r(t,{pub:e,pubEnc:n})},r.fromPrivate=function(t,e,n){return e instanceof r?e:new r(t,{priv:e,privEnc:n})},r.prototype.validate=function(){var t=this.getPublic();return t.isInfinity()?{result:!1,reason:"Invalid public key"}:t.validate()?t.mul(this.ec.curve.n).isInfinity()?{result:!0,reason:null}:{result:!1,reason:"Public key * N != O"}:{result:!1,reason:"Public key is not a point"}},r.prototype.getPublic=function(t,e){return"string"==typeof t&&(e=t,t=null),this.pub||(this.pub=this.ec.g.mul(this.priv)),e?this.pub.encode(e,t):this.pub},r.prototype.getPrivate=function(t){return"hex"===t?this.priv.toString(16,2):this.priv},r.prototype._importPrivate=function(t,e){this.priv=new i(t,e||16),this.priv=this.priv.umod(this.ec.curve.n)},r.prototype._importPublic=function(t,e){this.pub=t.x||t.y?this.ec.curve.point(t.x,t.y):this.ec.curve.decodePoint(t,e)},r.prototype.derive=function(t){return t.mul(this.priv).getX()},r.prototype.sign=function(t,e,n){return this.ec.sign(t,this,e,n)},r.prototype.verify=function(t,e){return this.ec.verify(t,e,this)},r.prototype.inspect=function(){return"<Key priv: "+(this.priv&&this.priv.toString(16,2))+" pub: "+(this.pub&&this.pub.inspect())+" >"}},{19:19}],79:[function(t,e,n){function r(t,e){return t instanceof r?t:void(this._importDER(t,e)||(u(t.r&&t.s,"Signature without r or s"),this.r=new c(t.r,16),this.s=new c(t.s,16),this.recoveryParam=void 0===t.recoveryParam?null:t.recoveryParam))}function i(){this.place=0}function o(t,e){var n=t[e.place++];if(!(128&n))return n;for(var n=15&n,r=0,i=0,o=e.place;n>i;i++,o++)r<<=8,r|=t[o];return e.place=o,r}function a(t){for(var e=0,n=t.length-1;!(t[e]||128&t[e+1])&&n>e;)e++;return 0===e?t:t.slice(e)}function s(t,e){if(!(128>e)){var n=1+(Math.log(e)/Math.LN2>>>3);for(t.push(128|n);--n;)t.push(e>>>(n<<3)&255)}t.push(e)}var c=t(19),f=t(70).utils,u=f.assert;e.exports=r,r.prototype._importDER=function(t,e){t=f.toArray(t,e);var n=new i;if(48!==t[n.place++]||o(t,n)+n.place!==t.length||2!==t[n.place++])return!1;var r=o(t,n),a=t.slice(n.place,r+n.place);return n.place+=r,2!==t[n.place++]?!1:(r=o(t,n),t.length!==r+n.place?!1:(n=t.slice(n.place,r+n.place),0===a[0]&&128&a[1]&&(a=a.slice(1)),0===n[0]&&128&n[1]&&(n=n.slice(1)),this.r=new c(a),this.s=new c(n),this.recoveryParam=null,!0))},r.prototype.toDER=function(t){var e=this.r.toArray(),n=this.s.toArray();for(128&e[0]&&(e=[0].concat(e)),128&n[0]&&(n=[0].concat(n)),e=a(e),n=a(n);!(n[0]||128&n[1]);)n=n.slice(1);var r=[2];return s(r,e.length),r=r.concat(e),r.push(2),s(r,n.length),e=r.concat(n),n=[48],s(n,e.length),n=n.concat(e),f.encode(n,t)}},{19:19,70:70}],80:[function(t,e,n){function r(t){return s("ed25519"===t,"only tested with ed25519 so far"),this instanceof r?(this.curve=t=o.curves[t].curve,this.g=t.g,this.g.precompute(t.n.bitLength()+1),this.pointClass=t.point().constructor,this.encodingLength=Math.ceil(t.n.bitLength()/8),void(this.hash=i.sha512)):new r(t)}var i=t(89),o=t(70),a=o.utils,s=a.assert,c=a.parseBytes,f=t(81),u=t(82);e.exports=r,r.prototype.sign=function(t,e){t=c(t);var n=this.keyFromSecret(e),r=this.hashInt(n.messagePrefix(),t),i=this.g.mul(r),o=this.encodePoint(i),n=this.hashInt(o,n.pubBytes(),t).mul(n.priv()),r=r.add(n).umod(this.curve.n);return this.makeSignature({R:i,S:r,Rencoded:o})},r.prototype.verify=function(t,e,n){t=c(t),e=this.makeSignature(e),n=this.keyFromPublic(n),t=this.hashInt(e.Rencoded(),n.pubBytes(),t);var r=this.g.mul(e.S());return e.R().add(n.pub().mul(t)).eq(r)},r.prototype.hashInt=function(){for(var t=this.hash(),e=0;e<arguments.length;e++)t.update(arguments[e]);return a.intFromLE(t.digest()).umod(this.curve.n)},r.prototype.keyFromPublic=function(t){return f.fromPublic(this,t)},r.prototype.keyFromSecret=function(t){return f.fromSecret(this,t)},r.prototype.makeSignature=function(t){return t instanceof u?t:new u(this,t)},r.prototype.encodePoint=function(t){var e=t.getY().toArray("le",this.encodingLength);return e[this.encodingLength-1]|=t.getX().isOdd()?128:0,e},r.prototype.decodePoint=function(t){t=a.parseBytes(t);var e=t.length-1,n=t.slice(0,e).concat(-129&t[e]);return t=0!==(128&t[e]),n=a.intFromLE(n),this.curve.pointFromY(n,t)},r.prototype.encodeInt=function(t){return t.toArray("le",this.encodingLength)},r.prototype.decodeInt=function(t){return a.intFromLE(t)},r.prototype.isPoint=function(t){return t instanceof this.pointClass}},{70:70,81:81,82:82,89:89}],81:[function(t,e,n){function r(t,e){this.eddsa=t,this._secret=a(e.secret),t.isPoint(e.pub)?this._pub=e.pub:this._pubBytes=a(e.pub)}var i=t(70).utils,o=i.assert,a=i.parseBytes;t=i.cachedProperty,r.fromPublic=function(t,e){return e instanceof r?e:new r(t,{pub:e})},r.fromSecret=function(t,e){return e instanceof r?e:new r(t,{secret:e})},r.prototype.secret=function(){return this._secret},t(r,function(){return this.eddsa.encodePoint(this.pub())}),t(r,function(){return this._pubBytes?this.eddsa.decodePoint(this._pubBytes):this.eddsa.g.mul(this.priv())}),t(r,function(){var t=this.eddsa,e=this.hash(),n=t.encodingLength-1,t=e.slice(0,t.encodingLength);return t[0]&=248,t[n]&=127,t[n]|=64,t}),t(r,function(){return this.eddsa.decodeInt(this.privBytes())}),t(r,function(){return this.eddsa.hash().update(this.secret()).digest()}),t(r,function(){return this.hash().slice(this.eddsa.encodingLength)}),r.prototype.sign=function(t){return o(this._secret,"KeyPair can only verify"),this.eddsa.sign(t,this)},r.prototype.verify=function(t,e){return this.eddsa.verify(t,e,this)},r.prototype.getSecret=function(t){return o(this._secret,"KeyPair is public only"),i.encode(this.secret(),t)},r.prototype.getPublic=function(t){return i.encode(this.pubBytes(),t)},e.exports=r},{70:70}],82:[function(t,e,n){function r(t,e){this.eddsa=t,"object"!=typeof e&&(e=s(e)),Array.isArray(e)&&(e={R:e.slice(0,t.encodingLength),S:e.slice(t.encodingLength)}),a(e.R&&e.S,"Signature without R or S"),t.isPoint(e.R)&&(this._R=e.R),e.S instanceof i&&(this._S=e.S),this._Rencoded=Array.isArray(e.R)?e.R:e.Rencoded,this._Sencoded=Array.isArray(e.S)?e.S:e.Sencoded}var i=t(19),o=t(70).utils,a=o.assert;t=o.cachedProperty;var s=o.parseBytes;t(r,function(){return this.eddsa.decodeInt(this.Sencoded())}),t(r,function(){return this.eddsa.decodePoint(this.Rencoded())}),t(r,function(){return this.eddsa.encodePoint(this.R())}),t(r,function(){return this.eddsa.encodeInt(this.S())}),r.prototype.toBytes=function(){return this.Rencoded().concat(this.Sencoded())},r.prototype.toHex=function(){return o.encode(this.toBytes(),"hex").toUpperCase()},e.exports=r},{19:19,70:70}],83:[function(t,e,n){function r(t){if(!(this instanceof r))return new r(t);this.hash=t.hash,this.predResist=!!t.predResist,this.outLen=this.hash.outSize,this.minEntropy=t.minEntropy||this.hash.hmacStrength,this.V=this.K=this.reseedInterval=this.reseed=null;var e=o.toArray(t.entropy,t.entropyEnc),n=o.toArray(t.nonce,t.nonceEnc);t=o.toArray(t.pers,t.persEnc),a(e.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._init(e,n,t)}var i=t(89),o=t(70).utils,a=o.assert;e.exports=r,r.prototype._init=function(t,e,n){for(t=t.concat(e).concat(n),this.K=Array(this.outLen/8),this.V=Array(this.outLen/8),e=0;e<this.V.length;e++)this.K[e]=0,this.V[e]=1;this._update(t),this.reseed=1,this.reseedInterval=281474976710656;
},r.prototype._hmac=function(){return new i.hmac(this.hash,this.K)},r.prototype._update=function(t){var e=this._hmac().update(this.V).update([0]);t&&(e=e.update(t)),this.K=e.digest(),this.V=this._hmac().update(this.V).digest(),t&&(this.K=this._hmac().update(this.V).update([1]).update(t).digest(),this.V=this._hmac().update(this.V).digest())},r.prototype.reseed=function(t,e,n,r){"string"!=typeof e&&(r=n,n=e,e=null),t=o.toBuffer(t,e),n=o.toBuffer(n,r),a(t.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._update(t.concat(n||[])),this.reseed=1},r.prototype.generate=function(t,e,n,r){if(this.reseed>this.reseedInterval)throw Error("Reseed is required");for("string"!=typeof e&&(r=n,n=e,e=null),n&&(n=o.toArray(n,r),this._update(n)),r=[];r.length<t;)this.V=this._hmac().update(this.V).digest(),r=r.concat(this.V);return t=r.slice(0,t),this._update(n),this.reseed++,o.encode(t,e)}},{70:70,89:89}],84:[function(t,e,n){e.exports={doubles:{step:4,points:[["e60fce93b59e9ec53011aabc21c23e97b2a31369b87a5ae9c44ee89e2a6dec0a","f7e3507399e595929db99f34f57937101296891e44d23f0be1f32cce69616821"],["8282263212c609d9ea2a6e3e172de238d8c39cabd5ac1ca10646e23fd5f51508","11f8a8098557dfe45e8256e830b60ace62d613ac2f7b17bed31b6eaff6e26caf"],["175e159f728b865a72f99cc6c6fc846de0b93833fd2222ed73fce5b551e5b739","d3506e0d9e3c79eba4ef97a51ff71f5eacb5955add24345c6efa6ffee9fed695"],["363d90d447b00c9c99ceac05b6262ee053441c7e55552ffe526bad8f83ff4640","4e273adfc732221953b445397f3363145b9a89008199ecb62003c7f3bee9de9"],["8b4b5f165df3c2be8c6244b5b745638843e4a781a15bcd1b69f79a55dffdf80c","4aad0a6f68d308b4b3fbd7813ab0da04f9e336546162ee56b3eff0c65fd4fd36"],["723cbaa6e5db996d6bf771c00bd548c7b700dbffa6c0e77bcb6115925232fcda","96e867b5595cc498a921137488824d6e2660a0653779494801dc069d9eb39f5f"],["eebfa4d493bebf98ba5feec812c2d3b50947961237a919839a533eca0e7dd7fa","5d9a8ca3970ef0f269ee7edaf178089d9ae4cdc3a711f712ddfd4fdae1de8999"],["100f44da696e71672791d0a09b7bde459f1215a29b3c03bfefd7835b39a48db0","cdd9e13192a00b772ec8f3300c090666b7ff4a18ff5195ac0fbd5cd62bc65a09"],["e1031be262c7ed1b1dc9227a4a04c017a77f8d4464f3b3852c8acde6e534fd2d","9d7061928940405e6bb6a4176597535af292dd419e1ced79a44f18f29456a00d"],["feea6cae46d55b530ac2839f143bd7ec5cf8b266a41d6af52d5e688d9094696d","e57c6b6c97dce1bab06e4e12bf3ecd5c981c8957cc41442d3155debf18090088"],["da67a91d91049cdcb367be4be6ffca3cfeed657d808583de33fa978bc1ec6cb1","9bacaa35481642bc41f463f7ec9780e5dec7adc508f740a17e9ea8e27a68be1d"],["53904faa0b334cdda6e000935ef22151ec08d0f7bb11069f57545ccc1a37b7c0","5bc087d0bc80106d88c9eccac20d3c1c13999981e14434699dcb096b022771c8"],["8e7bcd0bd35983a7719cca7764ca906779b53a043a9b8bcaeff959f43ad86047","10b7770b2a3da4b3940310420ca9514579e88e2e47fd68b3ea10047e8460372a"],["385eed34c1cdff21e6d0818689b81bde71a7f4f18397e6690a841e1599c43862","283bebc3e8ea23f56701de19e9ebf4576b304eec2086dc8cc0458fe5542e5453"],["6f9d9b803ecf191637c73a4413dfa180fddf84a5947fbc9c606ed86c3fac3a7","7c80c68e603059ba69b8e2a30e45c4d47ea4dd2f5c281002d86890603a842160"],["3322d401243c4e2582a2147c104d6ecbf774d163db0f5e5313b7e0e742d0e6bd","56e70797e9664ef5bfb019bc4ddaf9b72805f63ea2873af624f3a2e96c28b2a0"],["85672c7d2de0b7da2bd1770d89665868741b3f9af7643397721d74d28134ab83","7c481b9b5b43b2eb6374049bfa62c2e5e77f17fcc5298f44c8e3094f790313a6"],["948bf809b1988a46b06c9f1919413b10f9226c60f668832ffd959af60c82a0a","53a562856dcb6646dc6b74c5d1c3418c6d4dff08c97cd2bed4cb7f88d8c8e589"],["6260ce7f461801c34f067ce0f02873a8f1b0e44dfc69752accecd819f38fd8e8","bc2da82b6fa5b571a7f09049776a1ef7ecd292238051c198c1a84e95b2b4ae17"],["e5037de0afc1d8d43d8348414bbf4103043ec8f575bfdc432953cc8d2037fa2d","4571534baa94d3b5f9f98d09fb990bddbd5f5b03ec481f10e0e5dc841d755bda"],["e06372b0f4a207adf5ea905e8f1771b4e7e8dbd1c6a6c5b725866a0ae4fce725","7a908974bce18cfe12a27bb2ad5a488cd7484a7787104870b27034f94eee31dd"],["213c7a715cd5d45358d0bbf9dc0ce02204b10bdde2a3f58540ad6908d0559754","4b6dad0b5ae462507013ad06245ba190bb4850f5f36a7eeddff2c27534b458f2"],["4e7c272a7af4b34e8dbb9352a5419a87e2838c70adc62cddf0cc3a3b08fbd53c","17749c766c9d0b18e16fd09f6def681b530b9614bff7dd33e0b3941817dcaae6"],["fea74e3dbe778b1b10f238ad61686aa5c76e3db2be43057632427e2840fb27b6","6e0568db9b0b13297cf674deccb6af93126b596b973f7b77701d3db7f23cb96f"],["76e64113f677cf0e10a2570d599968d31544e179b760432952c02a4417bdde39","c90ddf8dee4e95cf577066d70681f0d35e2a33d2b56d2032b4b1752d1901ac01"],["c738c56b03b2abe1e8281baa743f8f9a8f7cc643df26cbee3ab150242bcbb891","893fb578951ad2537f718f2eacbfbbbb82314eef7880cfe917e735d9699a84c3"],["d895626548b65b81e264c7637c972877d1d72e5f3a925014372e9f6588f6c14b","febfaa38f2bc7eae728ec60818c340eb03428d632bb067e179363ed75d7d991f"],["b8da94032a957518eb0f6433571e8761ceffc73693e84edd49150a564f676e03","2804dfa44805a1e4d7c99cc9762808b092cc584d95ff3b511488e4e74efdf6e7"],["e80fea14441fb33a7d8adab9475d7fab2019effb5156a792f1a11778e3c0df5d","eed1de7f638e00771e89768ca3ca94472d155e80af322ea9fcb4291b6ac9ec78"],["a301697bdfcd704313ba48e51d567543f2a182031efd6915ddc07bbcc4e16070","7370f91cfb67e4f5081809fa25d40f9b1735dbf7c0a11a130c0d1a041e177ea1"],["90ad85b389d6b936463f9d0512678de208cc330b11307fffab7ac63e3fb04ed4","e507a3620a38261affdcbd9427222b839aefabe1582894d991d4d48cb6ef150"],["8f68b9d2f63b5f339239c1ad981f162ee88c5678723ea3351b7b444c9ec4c0da","662a9f2dba063986de1d90c2b6be215dbbea2cfe95510bfdf23cbf79501fff82"],["e4f3fb0176af85d65ff99ff9198c36091f48e86503681e3e6686fd5053231e11","1e63633ad0ef4f1c1661a6d0ea02b7286cc7e74ec951d1c9822c38576feb73bc"],["8c00fa9b18ebf331eb961537a45a4266c7034f2f0d4e1d0716fb6eae20eae29e","efa47267fea521a1a9dc343a3736c974c2fadafa81e36c54e7d2a4c66702414b"],["e7a26ce69dd4829f3e10cec0a9e98ed3143d084f308b92c0997fddfc60cb3e41","2a758e300fa7984b471b006a1aafbb18d0a6b2c0420e83e20e8a9421cf2cfd51"],["b6459e0ee3662ec8d23540c223bcbdc571cbcb967d79424f3cf29eb3de6b80ef","67c876d06f3e06de1dadf16e5661db3c4b3ae6d48e35b2ff30bf0b61a71ba45"],["d68a80c8280bb840793234aa118f06231d6f1fc67e73c5a5deda0f5b496943e8","db8ba9fff4b586d00c4b1f9177b0e28b5b0e7b8f7845295a294c84266b133120"],["324aed7df65c804252dc0270907a30b09612aeb973449cea4095980fc28d3d5d","648a365774b61f2ff130c0c35aec1f4f19213b0c7e332843967224af96ab7c84"],["4df9c14919cde61f6d51dfdbe5fee5dceec4143ba8d1ca888e8bd373fd054c96","35ec51092d8728050974c23a1d85d4b5d506cdc288490192ebac06cad10d5d"],["9c3919a84a474870faed8a9c1cc66021523489054d7f0308cbfc99c8ac1f98cd","ddb84f0f4a4ddd57584f044bf260e641905326f76c64c8e6be7e5e03d4fc599d"],["6057170b1dd12fdf8de05f281d8e06bb91e1493a8b91d4cc5a21382120a959e5","9a1af0b26a6a4807add9a2daf71df262465152bc3ee24c65e899be932385a2a8"],["a576df8e23a08411421439a4518da31880cef0fba7d4df12b1a6973eecb94266","40a6bf20e76640b2c92b97afe58cd82c432e10a7f514d9f3ee8be11ae1b28ec8"],["7778a78c28dec3e30a05fe9629de8c38bb30d1f5cf9a3a208f763889be58ad71","34626d9ab5a5b22ff7098e12f2ff580087b38411ff24ac563b513fc1fd9f43ac"],["928955ee637a84463729fd30e7afd2ed5f96274e5ad7e5cb09eda9c06d903ac","c25621003d3f42a827b78a13093a95eeac3d26efa8a8d83fc5180e935bcd091f"],["85d0fef3ec6db109399064f3a0e3b2855645b4a907ad354527aae75163d82751","1f03648413a38c0be29d496e582cf5663e8751e96877331582c237a24eb1f962"],["ff2b0dce97eece97c1c9b6041798b85dfdfb6d8882da20308f5404824526087e","493d13fef524ba188af4c4dc54d07936c7b7ed6fb90e2ceb2c951e01f0c29907"],["827fbbe4b1e880ea9ed2b2e6301b212b57f1ee148cd6dd28780e5e2cf856e241","c60f9c923c727b0b71bef2c67d1d12687ff7a63186903166d605b68baec293ec"],["eaa649f21f51bdbae7be4ae34ce6e5217a58fdce7f47f9aa7f3b58fa2120e2b3","be3279ed5bbbb03ac69a80f89879aa5a01a6b965f13f7e59d47a5305ba5ad93d"],["e4a42d43c5cf169d9391df6decf42ee541b6d8f0c9a137401e23632dda34d24f","4d9f92e716d1c73526fc99ccfb8ad34ce886eedfa8d8e4f13a7f7131deba9414"],["1ec80fef360cbdd954160fadab352b6b92b53576a88fea4947173b9d4300bf19","aeefe93756b5340d2f3a4958a7abbf5e0146e77f6295a07b671cdc1cc107cefd"],["146a778c04670c2f91b00af4680dfa8bce3490717d58ba889ddb5928366642be","b318e0ec3354028add669827f9d4b2870aaa971d2f7e5ed1d0b297483d83efd0"],["fa50c0f61d22e5f07e3acebb1aa07b128d0012209a28b9776d76a8793180eef9","6b84c6922397eba9b72cd2872281a68a5e683293a57a213b38cd8d7d3f4f2811"],["da1d61d0ca721a11b1a5bf6b7d88e8421a288ab5d5bba5220e53d32b5f067ec2","8157f55a7c99306c79c0766161c91e2966a73899d279b48a655fba0f1ad836f1"],["a8e282ff0c9706907215ff98e8fd416615311de0446f1e062a73b0610d064e13","7f97355b8db81c09abfb7f3c5b2515888b679a3e50dd6bd6cef7c73111f4cc0c"],["174a53b9c9a285872d39e56e6913cab15d59b1fa512508c022f382de8319497c","ccc9dc37abfc9c1657b4155f2c47f9e6646b3a1d8cb9854383da13ac079afa73"],["959396981943785c3d3e57edf5018cdbe039e730e4918b3d884fdff09475b7ba","2e7e552888c331dd8ba0386a4b9cd6849c653f64c8709385e9b8abf87524f2fd"],["d2a63a50ae401e56d645a1153b109a8fcca0a43d561fba2dbb51340c9d82b151","e82d86fb6443fcb7565aee58b2948220a70f750af484ca52d4142174dcf89405"],["64587e2335471eb890ee7896d7cfdc866bacbdbd3839317b3436f9b45617e073","d99fcdd5bf6902e2ae96dd6447c299a185b90a39133aeab358299e5e9faf6589"],["8481bde0e4e4d885b3a546d3e549de042f0aa6cea250e7fd358d6c86dd45e458","38ee7b8cba5404dd84a25bf39cecb2ca900a79c42b262e556d64b1b59779057e"],["13464a57a78102aa62b6979ae817f4637ffcfed3c4b1ce30bcd6303f6caf666b","69be159004614580ef7e433453ccb0ca48f300a81d0942e13f495a907f6ecc27"],["bc4a9df5b713fe2e9aef430bcc1dc97a0cd9ccede2f28588cada3a0d2d83f366","d3a81ca6e785c06383937adf4b798caa6e8a9fbfa547b16d758d666581f33c1"],["8c28a97bf8298bc0d23d8c749452a32e694b65e30a9472a3954ab30fe5324caa","40a30463a3305193378fedf31f7cc0eb7ae784f0451cb9459e71dc73cbef9482"],["8ea9666139527a8c1dd94ce4f071fd23c8b350c5a4bb33748c4ba111faccae0","620efabbc8ee2782e24e7c0cfb95c5d735b783be9cf0f8e955af34a30e62b945"],["dd3625faef5ba06074669716bbd3788d89bdde815959968092f76cc4eb9a9787","7a188fa3520e30d461da2501045731ca941461982883395937f68d00c644a573"],["f710d79d9eb962297e4f6232b40e8f7feb2bc63814614d692c12de752408221e","ea98e67232d3b3295d3b535532115ccac8612c721851617526ae47a9c77bfc82"]]},naf:{wnd:7,points:[["f9308a019258c31049344f85f89d5229b531c845836f99b08601f113bce036f9","388f7b0f632de8140fe337e62a37f3566500a99934c2231b6cb9fd7584b8e672"],["2f8bde4d1a07209355b4a7250a5c5128e88b84bddc619ab7cba8d569b240efe4","d8ac222636e5e3d6d4dba9dda6c9c426f788271bab0d6840dca87d3aa6ac62d6"],["5cbdf0646e5db4eaa398f365f2ea7a0e3d419b7e0330e39ce92bddedcac4f9bc","6aebca40ba255960a3178d6d861a54dba813d0b813fde7b5a5082628087264da"],["acd484e2f0c7f65309ad178a9f559abde09796974c57e714c35f110dfc27ccbe","cc338921b0a7d9fd64380971763b61e9add888a4375f8e0f05cc262ac64f9c37"],["774ae7f858a9411e5ef4246b70c65aac5649980be5c17891bbec17895da008cb","d984a032eb6b5e190243dd56d7b7b365372db1e2dff9d6a8301d74c9c953c61b"],["f28773c2d975288bc7d1d205c3748651b075fbc6610e58cddeeddf8f19405aa8","ab0902e8d880a89758212eb65cdaf473a1a06da521fa91f29b5cb52db03ed81"],["d7924d4f7d43ea965a465ae3095ff41131e5946f3c85f79e44adbcf8e27e080e","581e2872a86c72a683842ec228cc6defea40af2bd896d3a5c504dc9ff6a26b58"],["defdea4cdb677750a420fee807eacf21eb9898ae79b9768766e4faa04a2d4a34","4211ab0694635168e997b0ead2a93daeced1f4a04a95c0f6cfb199f69e56eb77"],["2b4ea0a797a443d293ef5cff444f4979f06acfebd7e86d277475656138385b6c","85e89bc037945d93b343083b5a1c86131a01f60c50269763b570c854e5c09b7a"],["352bbf4a4cdd12564f93fa332ce333301d9ad40271f8107181340aef25be59d5","321eb4075348f534d59c18259dda3e1f4a1b3b2e71b1039c67bd3d8bcf81998c"],["2fa2104d6b38d11b0230010559879124e42ab8dfeff5ff29dc9cdadd4ecacc3f","2de1068295dd865b64569335bd5dd80181d70ecfc882648423ba76b532b7d67"],["9248279b09b4d68dab21a9b066edda83263c3d84e09572e269ca0cd7f5453714","73016f7bf234aade5d1aa71bdea2b1ff3fc0de2a887912ffe54a32ce97cb3402"],["daed4f2be3a8bf278e70132fb0beb7522f570e144bf615c07e996d443dee8729","a69dce4a7d6c98e8d4a1aca87ef8d7003f83c230f3afa726ab40e52290be1c55"],["c44d12c7065d812e8acf28d7cbb19f9011ecd9e9fdf281b0e6a3b5e87d22e7db","2119a460ce326cdc76c45926c982fdac0e106e861edf61c5a039063f0e0e6482"],["6a245bf6dc698504c89a20cfded60853152b695336c28063b61c65cbd269e6b4","e022cf42c2bd4a708b3f5126f16a24ad8b33ba48d0423b6efd5e6348100d8a82"],["1697ffa6fd9de627c077e3d2fe541084ce13300b0bec1146f95ae57f0d0bd6a5","b9c398f186806f5d27561506e4557433a2cf15009e498ae7adee9d63d01b2396"],["605bdb019981718b986d0f07e834cb0d9deb8360ffb7f61df982345ef27a7479","2972d2de4f8d20681a78d93ec96fe23c26bfae84fb14db43b01e1e9056b8c49"],["62d14dab4150bf497402fdc45a215e10dcb01c354959b10cfe31c7e9d87ff33d","80fc06bd8cc5b01098088a1950eed0db01aa132967ab472235f5642483b25eaf"],["80c60ad0040f27dade5b4b06c408e56b2c50e9f56b9b8b425e555c2f86308b6f","1c38303f1cc5c30f26e66bad7fe72f70a65eed4cbe7024eb1aa01f56430bd57a"],["7a9375ad6167ad54aa74c6348cc54d344cc5dc9487d847049d5eabb0fa03c8fb","d0e3fa9eca8726909559e0d79269046bdc59ea10c70ce2b02d499ec224dc7f7"],["d528ecd9b696b54c907a9ed045447a79bb408ec39b68df504bb51f459bc3ffc9","eecf41253136e5f99966f21881fd656ebc4345405c520dbc063465b521409933"],["49370a4b5f43412ea25f514e8ecdad05266115e4a7ecb1387231808f8b45963","758f3f41afd6ed428b3081b0512fd62a54c3f3afbb5b6764b653052a12949c9a"],["77f230936ee88cbbd73df930d64702ef881d811e0e1498e2f1c13eb1fc345d74","958ef42a7886b6400a08266e9ba1b37896c95330d97077cbbe8eb3c7671c60d6"],["f2dac991cc4ce4b9ea44887e5c7c0bce58c80074ab9d4dbaeb28531b7739f530","e0dedc9b3b2f8dad4da1f32dec2531df9eb5fbeb0598e4fd1a117dba703a3c37"],["463b3d9f662621fb1b4be8fbbe2520125a216cdfc9dae3debcba4850c690d45b","5ed430d78c296c3543114306dd8622d7c622e27c970a1de31cb377b01af7307e"],["f16f804244e46e2a09232d4aff3b59976b98fac14328a2d1a32496b49998f247","cedabd9b82203f7e13d206fcdf4e33d92a6c53c26e5cce26d6579962c4e31df6"],["caf754272dc84563b0352b7a14311af55d245315ace27c65369e15f7151d41d1","cb474660ef35f5f2a41b643fa5e460575f4fa9b7962232a5c32f908318a04476"],["2600ca4b282cb986f85d0f1709979d8b44a09c07cb86d7c124497bc86f082120","4119b88753c15bd6a693b03fcddbb45d5ac6be74ab5f0ef44b0be9475a7e4b40"],["7635ca72d7e8432c338ec53cd12220bc01c48685e24f7dc8c602a7746998e435","91b649609489d613d1d5e590f78e6d74ecfc061d57048bad9e76f302c5b9c61"],["754e3239f325570cdbbf4a87deee8a66b7f2b33479d468fbc1a50743bf56cc18","673fb86e5bda30fb3cd0ed304ea49a023ee33d0197a695d0c5d98093c536683"],["e3e6bd1071a1e96aff57859c82d570f0330800661d1c952f9fe2694691d9b9e8","59c9e0bba394e76f40c0aa58379a3cb6a5a2283993e90c4167002af4920e37f5"],["186b483d056a033826ae73d88f732985c4ccb1f32ba35f4b4cc47fdcf04aa6eb","3b952d32c67cf77e2e17446e204180ab21fb8090895138b4a4a797f86e80888b"],["df9d70a6b9876ce544c98561f4be4f725442e6d2b737d9c91a8321724ce0963f","55eb2dafd84d6ccd5f862b785dc39d4ab157222720ef9da217b8c45cf2ba2417"],["5edd5cc23c51e87a497ca815d5dce0f8ab52554f849ed8995de64c5f34ce7143","efae9c8dbc14130661e8cec030c89ad0c13c66c0d17a2905cdc706ab7399a868"],["290798c2b6476830da12fe02287e9e777aa3fba1c355b17a722d362f84614fba","e38da76dcd440621988d00bcf79af25d5b29c094db2a23146d003afd41943e7a"],["af3c423a95d9f5b3054754efa150ac39cd29552fe360257362dfdecef4053b45","f98a3fd831eb2b749a93b0e6f35cfb40c8cd5aa667a15581bc2feded498fd9c6"],["766dbb24d134e745cccaa28c99bf274906bb66b26dcf98df8d2fed50d884249a","744b1152eacbe5e38dcc887980da38b897584a65fa06cedd2c924f97cbac5996"],["59dbf46f8c94759ba21277c33784f41645f7b44f6c596a58ce92e666191abe3e","c534ad44175fbc300f4ea6ce648309a042ce739a7919798cd85e216c4a307f6e"],["f13ada95103c4537305e691e74e9a4a8dd647e711a95e73cb62dc6018cfd87b8","e13817b44ee14de663bf4bc808341f326949e21a6a75c2570778419bdaf5733d"],["7754b4fa0e8aced06d4167a2c59cca4cda1869c06ebadfb6488550015a88522c","30e93e864e669d82224b967c3020b8fa8d1e4e350b6cbcc537a48b57841163a2"],["948dcadf5990e048aa3874d46abef9d701858f95de8041d2a6828c99e2262519","e491a42537f6e597d5d28a3224b1bc25df9154efbd2ef1d2cbba2cae5347d57e"],["7962414450c76c1689c7b48f8202ec37fb224cf5ac0bfa1570328a8a3d7c77ab","100b610ec4ffb4760d5c1fc133ef6f6b12507a051f04ac5760afa5b29db83437"],["3514087834964b54b15b160644d915485a16977225b8847bb0dd085137ec47ca","ef0afbb2056205448e1652c48e8127fc6039e77c15c2378b7e7d15a0de293311"],["d3cc30ad6b483e4bc79ce2c9dd8bc54993e947eb8df787b442943d3f7b527eaf","8b378a22d827278d89c5e9be8f9508ae3c2ad46290358630afb34db04eede0a4"],["1624d84780732860ce1c78fcbfefe08b2b29823db913f6493975ba0ff4847610","68651cf9b6da903e0914448c6cd9d4ca896878f5282be4c8cc06e2a404078575"],["733ce80da955a8a26902c95633e62a985192474b5af207da6df7b4fd5fc61cd4","f5435a2bd2badf7d485a4d8b8db9fcce3e1ef8e0201e4578c54673bc1dc5ea1d"],["15d9441254945064cf1a1c33bbd3b49f8966c5092171e699ef258dfab81c045c","d56eb30b69463e7234f5137b73b84177434800bacebfc685fc37bbe9efe4070d"],["a1d0fcf2ec9de675b612136e5ce70d271c21417c9d2b8aaaac138599d0717940","edd77f50bcb5a3cab2e90737309667f2641462a54070f3d519212d39c197a629"],["e22fbe15c0af8ccc5780c0735f84dbe9a790badee8245c06c7ca37331cb36980","a855babad5cd60c88b430a69f53a1a7a38289154964799be43d06d77d31da06"],["311091dd9860e8e20ee13473c1155f5f69635e394704eaa74009452246cfa9b3","66db656f87d1f04fffd1f04788c06830871ec5a64feee685bd80f0b1286d8374"],["34c1fd04d301be89b31c0442d3e6ac24883928b45a9340781867d4232ec2dbdf","9414685e97b1b5954bd46f730174136d57f1ceeb487443dc5321857ba73abee"],["f219ea5d6b54701c1c14de5b557eb42a8d13f3abbcd08affcc2a5e6b049b8d63","4cb95957e83d40b0f73af4544cccf6b1f4b08d3c07b27fb8d8c2962a400766d1"],["d7b8740f74a8fbaab1f683db8f45de26543a5490bca627087236912469a0b448","fa77968128d9c92ee1010f337ad4717eff15db5ed3c049b3411e0315eaa4593b"],["32d31c222f8f6f0ef86f7c98d3a3335ead5bcd32abdd94289fe4d3091aa824bf","5f3032f5892156e39ccd3d7915b9e1da2e6dac9e6f26e961118d14b8462e1661"],["7461f371914ab32671045a155d9831ea8793d77cd59592c4340f86cbc18347b5","8ec0ba238b96bec0cbdddcae0aa442542eee1ff50c986ea6b39847b3cc092ff6"],["ee079adb1df1860074356a25aa38206a6d716b2c3e67453d287698bad7b2b2d6","8dc2412aafe3be5c4c5f37e0ecc5f9f6a446989af04c4e25ebaac479ec1c8c1e"],["16ec93e447ec83f0467b18302ee620f7e65de331874c9dc72bfd8616ba9da6b5","5e4631150e62fb40d0e8c2a7ca5804a39d58186a50e497139626778e25b0674d"],["eaa5f980c245f6f038978290afa70b6bd8855897f98b6aa485b96065d537bd99","f65f5d3e292c2e0819a528391c994624d784869d7e6ea67fb18041024edc07dc"],["78c9407544ac132692ee1910a02439958ae04877151342ea96c4b6b35a49f51","f3e0319169eb9b85d5404795539a5e68fa1fbd583c064d2462b675f194a3ddb4"],["494f4be219a1a77016dcd838431aea0001cdc8ae7a6fc688726578d9702857a5","42242a969283a5f339ba7f075e36ba2af925ce30d767ed6e55f4b031880d562c"],["a598a8030da6d86c6bc7f2f5144ea549d28211ea58faa70ebf4c1e665c1fe9b5","204b5d6f84822c307e4b4a7140737aec23fc63b65b35f86a10026dbd2d864e6b"],["c41916365abb2b5d09192f5f2dbeafec208f020f12570a184dbadc3e58595997","4f14351d0087efa49d245b328984989d5caf9450f34bfc0ed16e96b58fa9913"],["841d6063a586fa475a724604da03bc5b92a2e0d2e0a36acfe4c73a5514742881","73867f59c0659e81904f9a1c7543698e62562d6744c169ce7a36de01a8d6154"],["5e95bb399a6971d376026947f89bde2f282b33810928be4ded112ac4d70e20d5","39f23f366809085beebfc71181313775a99c9aed7d8ba38b161384c746012865"],["36e4641a53948fd476c39f8a99fd974e5ec07564b5315d8bf99471bca0ef2f66","d2424b1b1abe4eb8164227b085c9aa9456ea13493fd563e06fd51cf5694c78fc"],["336581ea7bfbbb290c191a2f507a41cf5643842170e914faeab27c2c579f726","ead12168595fe1be99252129b6e56b3391f7ab1410cd1e0ef3dcdcabd2fda224"],["8ab89816dadfd6b6a1f2634fcf00ec8403781025ed6890c4849742706bd43ede","6fdcef09f2f6d0a044e654aef624136f503d459c3e89845858a47a9129cdd24e"],["1e33f1a746c9c5778133344d9299fcaa20b0938e8acff2544bb40284b8c5fb94","60660257dd11b3aa9c8ed618d24edff2306d320f1d03010e33a7d2057f3b3b6"],["85b7c1dcb3cec1b7ee7f30ded79dd20a0ed1f4cc18cbcfcfa410361fd8f08f31","3d98a9cdd026dd43f39048f25a8847f4fcafad1895d7a633c6fed3c35e999511"],["29df9fbd8d9e46509275f4b125d6d45d7fbe9a3b878a7af872a2800661ac5f51","b4c4fe99c775a606e2d8862179139ffda61dc861c019e55cd2876eb2a27d84b"],["a0b1cae06b0a847a3fea6e671aaf8adfdfe58ca2f768105c8082b2e449fce252","ae434102edde0958ec4b19d917a6a28e6b72da1834aff0e650f049503a296cf2"],["4e8ceafb9b3e9a136dc7ff67e840295b499dfb3b2133e4ba113f2e4c0e121e5","cf2174118c8b6d7a4b48f6d534ce5c79422c086a63460502b827ce62a326683c"],["d24a44e047e19b6f5afb81c7ca2f69080a5076689a010919f42725c2b789a33b","6fb8d5591b466f8fc63db50f1c0f1c69013f996887b8244d2cdec417afea8fa3"],["ea01606a7a6c9cdd249fdfcfacb99584001edd28abbab77b5104e98e8e3b35d4","322af4908c7312b0cfbfe369f7a7b3cdb7d4494bc2823700cfd652188a3ea98d"],["af8addbf2b661c8a6c6328655eb96651252007d8c5ea31be4ad196de8ce2131f","6749e67c029b85f52a034eafd096836b2520818680e26ac8f3dfbcdb71749700"],["e3ae1974566ca06cc516d47e0fb165a674a3dabcfca15e722f0e3450f45889","2aeabe7e4531510116217f07bf4d07300de97e4874f81f533420a72eeb0bd6a4"],["591ee355313d99721cf6993ffed1e3e301993ff3ed258802075ea8ced397e246","b0ea558a113c30bea60fc4775460c7901ff0b053d25ca2bdeee98f1a4be5d196"],["11396d55fda54c49f19aa97318d8da61fa8584e47b084945077cf03255b52984","998c74a8cd45ac01289d5833a7beb4744ff536b01b257be4c5767bea93ea57a4"],["3c5d2a1ba39c5a1790000738c9e0c40b8dcdfd5468754b6405540157e017aa7a","b2284279995a34e2f9d4de7396fc18b80f9b8b9fdd270f6661f79ca4c81bd257"],["cc8704b8a60a0defa3a99a7299f2e9c3fbc395afb04ac078425ef8a1793cc030","bdd46039feed17881d1e0862db347f8cf395b74fc4bcdc4e940b74e3ac1f1b13"],["c533e4f7ea8555aacd9777ac5cad29b97dd4defccc53ee7ea204119b2889b197","6f0a256bc5efdf429a2fb6242f1a43a2d9b925bb4a4b3a26bb8e0f45eb596096"],["c14f8f2ccb27d6f109f6d08d03cc96a69ba8c34eec07bbcf566d48e33da6593","c359d6923bb398f7fd4473e16fe1c28475b740dd098075e6c0e8649113dc3a38"],["a6cbc3046bc6a450bac24789fa17115a4c9739ed75f8f21ce441f72e0b90e6ef","21ae7f4680e889bb130619e2c0f95a360ceb573c70603139862afd617fa9b9f"],["347d6d9a02c48927ebfb86c1359b1caf130a3c0267d11ce6344b39f99d43cc38","60ea7f61a353524d1c987f6ecec92f086d565ab687870cb12689ff1e31c74448"],["da6545d2181db8d983f7dcb375ef5866d47c67b1bf31c8cf855ef7437b72656a","49b96715ab6878a79e78f07ce5680c5d6673051b4935bd897fea824b77dc208a"],["c40747cc9d012cb1a13b8148309c6de7ec25d6945d657146b9d5994b8feb1111","5ca560753be2a12fc6de6caf2cb489565db936156b9514e1bb5e83037e0fa2d4"],["4e42c8ec82c99798ccf3a610be870e78338c7f713348bd34c8203ef4037f3502","7571d74ee5e0fb92a7a8b33a07783341a5492144cc54bcc40a94473693606437"],["3775ab7089bc6af823aba2e1af70b236d251cadb0c86743287522a1b3b0dedea","be52d107bcfa09d8bcb9736a828cfa7fac8db17bf7a76a2c42ad961409018cf7"],["cee31cbf7e34ec379d94fb814d3d775ad954595d1314ba8846959e3e82f74e26","8fd64a14c06b589c26b947ae2bcf6bfa0149ef0be14ed4d80f448a01c43b1c6d"],["b4f9eaea09b6917619f6ea6a4eb5464efddb58fd45b1ebefcdc1a01d08b47986","39e5c9925b5a54b07433a4f18c61726f8bb131c012ca542eb24a8ac07200682a"],["d4263dfc3d2df923a0179a48966d30ce84e2515afc3dccc1b77907792ebcc60e","62dfaf07a0f78feb30e30d6295853ce189e127760ad6cf7fae164e122a208d54"],["48457524820fa65a4f8d35eb6930857c0032acc0a4a2de422233eeda897612c4","25a748ab367979d98733c38a1fa1c2e7dc6cc07db2d60a9ae7a76aaa49bd0f77"],["dfeeef1881101f2cb11644f3a2afdfc2045e19919152923f367a1767c11cceda","ecfb7056cf1de042f9420bab396793c0c390bde74b4bbdff16a83ae09a9a7517"],["6d7ef6b17543f8373c573f44e1f389835d89bcbc6062ced36c82df83b8fae859","cd450ec335438986dfefa10c57fea9bcc521a0959b2d80bbf74b190dca712d10"],["e75605d59102a5a2684500d3b991f2e3f3c88b93225547035af25af66e04541f","f5c54754a8f71ee540b9b48728473e314f729ac5308b06938360990e2bfad125"],["eb98660f4c4dfaa06a2be453d5020bc99a0c2e60abe388457dd43fefb1ed620c","6cb9a8876d9cb8520609af3add26cd20a0a7cd8a9411131ce85f44100099223e"],["13e87b027d8514d35939f2e6892b19922154596941888336dc3563e3b8dba942","fef5a3c68059a6dec5d624114bf1e91aac2b9da568d6abeb2570d55646b8adf1"],["ee163026e9fd6fe017c38f06a5be6fc125424b371ce2708e7bf4491691e5764a","1acb250f255dd61c43d94ccc670d0f58f49ae3fa15b96623e5430da0ad6c62b2"],["b268f5ef9ad51e4d78de3a750c2dc89b1e626d43505867999932e5db33af3d80","5f310d4b3c99b9ebb19f77d41c1dee018cf0d34fd4191614003e945a1216e423"],["ff07f3118a9df035e9fad85eb6c7bfe42b02f01ca99ceea3bf7ffdba93c4750d","438136d603e858a3a5c440c38eccbaddc1d2942114e2eddd4740d098ced1f0d8"],["8d8b9855c7c052a34146fd20ffb658bea4b9f69e0d825ebec16e8c3ce2b526a1","cdb559eedc2d79f926baf44fb84ea4d44bcf50fee51d7ceb30e2e7f463036758"],["52db0b5384dfbf05bfa9d472d7ae26dfe4b851ceca91b1eba54263180da32b63","c3b997d050ee5d423ebaf66a6db9f57b3180c902875679de924b69d84a7b375"],["e62f9490d3d51da6395efd24e80919cc7d0f29c3f3fa48c6fff543becbd43352","6d89ad7ba4876b0b22c2ca280c682862f342c8591f1daf5170e07bfd9ccafa7d"],["7f30ea2476b399b4957509c88f77d0191afa2ff5cb7b14fd6d8e7d65aaab1193","ca5ef7d4b231c94c3b15389a5f6311e9daff7bb67b103e9880ef4bff637acaec"],["5098ff1e1d9f14fb46a210fada6c903fef0fb7b4a1dd1d9ac60a0361800b7a00","9731141d81fc8f8084d37c6e7542006b3ee1b40d60dfe5362a5b132fd17ddc0"],["32b78c7de9ee512a72895be6b9cbefa6e2f3c4ccce445c96b9f2c81e2778ad58","ee1849f513df71e32efc3896ee28260c73bb80547ae2275ba497237794c8753c"],["e2cb74fddc8e9fbcd076eef2a7c72b0ce37d50f08269dfc074b581550547a4f7","d3aa2ed71c9dd2247a62df062736eb0baddea9e36122d2be8641abcb005cc4a4"],["8438447566d4d7bedadc299496ab357426009a35f235cb141be0d99cd10ae3a8","c4e1020916980a4da5d01ac5e6ad330734ef0d7906631c4f2390426b2edd791f"],["4162d488b89402039b584c6fc6c308870587d9c46f660b878ab65c82c711d67e","67163e903236289f776f22c25fb8a3afc1732f2b84b4e95dbda47ae5a0852649"],["3fad3fa84caf0f34f0f89bfd2dcf54fc175d767aec3e50684f3ba4a4bf5f683d","cd1bc7cb6cc407bb2f0ca647c718a730cf71872e7d0d2a53fa20efcdfe61826"],["674f2600a3007a00568c1a7ce05d0816c1fb84bf1370798f1c69532faeb1a86b","299d21f9413f33b3edf43b257004580b70db57da0b182259e09eecc69e0d38a5"],["d32f4da54ade74abb81b815ad1fb3b263d82d6c692714bcff87d29bd5ee9f08f","f9429e738b8e53b968e99016c059707782e14f4535359d582fc416910b3eea87"],["30e4e670435385556e593657135845d36fbb6931f72b08cb1ed954f1e3ce3ff6","462f9bce619898638499350113bbc9b10a878d35da70740dc695a559eb88db7b"],["be2062003c51cc3004682904330e4dee7f3dcd10b01e580bf1971b04d4cad297","62188bc49d61e5428573d48a74e1c655b1c61090905682a0d5558ed72dccb9bc"],["93144423ace3451ed29e0fb9ac2af211cb6e84a601df5993c419859fff5df04a","7c10dfb164c3425f5c71a3f9d7992038f1065224f72bb9d1d902a6d13037b47c"],["b015f8044f5fcbdcf21ca26d6c34fb8197829205c7b7d2a7cb66418c157b112c","ab8c1e086d04e813744a655b2df8d5f83b3cdc6faa3088c1d3aea1454e3a1d5f"],["d5e9e1da649d97d89e4868117a465a3a4f8a18de57a140d36b3f2af341a21b52","4cb04437f391ed73111a13cc1d4dd0db1693465c2240480d8955e8592f27447a"],["d3ae41047dd7ca065dbf8ed77b992439983005cd72e16d6f996a5316d36966bb","bd1aeb21ad22ebb22a10f0303417c6d964f8cdd7df0aca614b10dc14d125ac46"],["463e2763d885f958fc66cdd22800f0a487197d0a82e377b49f80af87c897b065","bfefacdb0e5d0fd7df3a311a94de062b26b80c61fbc97508b79992671ef7ca7f"],["7985fdfd127c0567c6f53ec1bb63ec3158e597c40bfe747c83cddfc910641917","603c12daf3d9862ef2b25fe1de289aed24ed291e0ec6708703a5bd567f32ed03"],["74a1ad6b5f76e39db2dd249410eac7f99e74c59cb83d2d0ed5ff1543da7703e9","cc6157ef18c9c63cd6193d83631bbea0093e0968942e8c33d5737fd790e0db08"],["30682a50703375f602d416664ba19b7fc9bab42c72747463a71d0896b22f6da3","553e04f6b018b4fa6c8f39e7f311d3176290d0e0f19ca73f17714d9977a22ff8"],["9e2158f0d7c0d5f26c3791efefa79597654e7a2b2464f52b1ee6c1347769ef57","712fcdd1b9053f09003a3481fa7762e9ffd7c8ef35a38509e2fbf2629008373"],["176e26989a43c9cfeba4029c202538c28172e566e3c4fce7322857f3be327d66","ed8cc9d04b29eb877d270b4878dc43c19aefd31f4eee09ee7b47834c1fa4b1c3"],["75d46efea3771e6e68abb89a13ad747ecf1892393dfc4f1b7004788c50374da8","9852390a99507679fd0b86fd2b39a868d7efc22151346e1a3ca4726586a6bed8"],["809a20c67d64900ffb698c4c825f6d5f2310fb0451c869345b7319f645605721","9e994980d9917e22b76b061927fa04143d096ccc54963e6a5ebfa5f3f8e286c1"],["1b38903a43f7f114ed4500b4eac7083fdefece1cf29c63528d563446f972c180","4036edc931a60ae889353f77fd53de4a2708b26b6f5da72ad3394119daf408f9"]]}}},{}],85:[function(t,e,n){function r(t){return 1===t.length?"0"+t:t}function i(t){for(var e="",n=0;n<t.length;n++)e+=r(t[n].toString(16));return e}var o=t(19);n.assert=function(t,e){if(!t)throw Error(e||"Assertion failed")},n.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var n=[];if("string"!=typeof t){for(var r=0;r<t.length;r++)n[r]=0|t[r];return n}if(e){if("hex"===e)for(t=t.replace(/[^a-z0-9]+/gi,""),0!==t.length%2&&(t="0"+t),r=0;r<t.length;r+=2)n.push(parseInt(t[r]+t[r+1],16))}else for(r=0;r<t.length;r++){var i=t.charCodeAt(r),o=i>>8,i=255&i;o?n.push(o,i):n.push(i)}return n},n.zero2=r,n.toHex=i,n.encode=function(t,e){return"hex"===e?i(t):t},n.getNAF=function(t,e){for(var n=[],r=1<<e+1,i=t.clone();0<=i.cmpn(1);){var o;i.isOdd()?(o=i.andln(r-1),o=o>(r>>1)-1?(r>>1)-o:o,i.isubn(o)):o=0,n.push(o),o=0!==i.cmpn(0)&&0===i.andln(r-1)?e+1:1;for(var a=1;o>a;a++)n.push(0);i.iushrn(o)}return n},n.getJSF=function(t,e){var n=[[],[]];t=t.clone(),e=e.clone();for(var r=0,i=0;0<t.cmpn(-r)||0<e.cmpn(-i);){var o=t.andln(3)+r&3,a=e.andln(3)+i&3;3===o&&(o=-1),3===a&&(a=-1);var s;if(0===(1&o))s=0;else{var c=t.andln(7)+r&7;s=3!==c&&5!==c||2!==a?o:-o}n[0].push(s),0===(1&a)?o=0:(c=e.andln(7)+i&7,o=3!==c&&5!==c||2!==o?a:-a),n[1].push(o),2*r===s+1&&(r=1-r),2*i===o+1&&(i=1-i),t.iushrn(1),e.iushrn(1)}return n},n.cachedProperty=function(t,e){var n=e.name,r="_"+n;t.prototype[n]=function(){return void 0!==this[r]?this[r]:this[r]=e.call(this)}},n.parseBytes=function(t){return"string"==typeof t?n.toArray(t,"hex"):t},n.intFromLE=function(t){return new o(t,"hex","le")}},{19:19}],86:[function(t,e,n){e.exports={_args:[["elliptic@^6.0.0","/Volumes/HDD/Users/<USER>/local/case/inf/bos/bce-bos-uploader/node_modules/browserify-sign"]],_cnpm_publish_time:1454054444416,_from:"elliptic@>=6.0.0 <7.0.0",_id:"elliptic@6.2.3",_inCache:!0,_installable:!0,_location:"/elliptic",_nodeVersion:"5.4.1",_npmUser:{email:"<EMAIL>",name:"indutny"},_npmVersion:"3.3.12",_phantomChildren:{},_requested:{name:"elliptic",raw:"elliptic@^6.0.0",rawSpec:"^6.0.0",scope:null,spec:">=6.0.0 <7.0.0",type:"range"},_requiredBy:["/browserify-sign","/create-ecdh"],_resolved:"http://registry.npm.taobao.org/elliptic/download/elliptic-6.2.3.tgz",_shasum:"18e46d7306b0951275a2d42063270a14b74ebe99",_shrinkwrap:null,_spec:"elliptic@^6.0.0",_where:"/Volumes/HDD/Users/<USER>/local/case/inf/bos/bce-bos-uploader/node_modules/browserify-sign",author:{email:"<EMAIL>",name:"Fedor Indutny"},bugs:{url:"https://github.com/indutny/elliptic/issues"},dependencies:{"bn.js":"^4.0.0",brorand:"^1.0.1","hash.js":"^1.0.0",inherits:"^2.0.1"},description:"EC cryptography",devDependencies:{coveralls:"^2.11.3",istanbul:"^0.4.2",jscs:"^2.9.0",jshint:"^2.6.0",mocha:"^2.1.0"},directories:{},dist:{shasum:"18e46d7306b0951275a2d42063270a14b74ebe99",tarball:"http://registry.npm.baidu.com/elliptic/-/elliptic-6.2.3.tgz"},files:["lib"],gitHead:"c32f20b22b420eb6af3c6dda28963deb7facf823",homepage:"https://github.com/indutny/elliptic",keywords:["Cryptography","EC","Elliptic","curve"],license:"MIT",main:"lib/elliptic.js",maintainers:[{name:"indutny",email:"<EMAIL>"}],name:"elliptic",optionalDependencies:{},readme:"ERROR: No README data found!",repository:{type:"git",url:"git+ssh://**************/indutny/elliptic.git"},scripts:{coverage:"npm run unit --coverage",coveralls:"npm run coverage && cat ./coverage/lcov.info | coveralls",jscs:"jscs benchmarks/*.js lib/*.js lib/**/*.js lib/**/**/*.js test/*.js",jshint:"jscs benchmarks/*.js lib/*.js lib/**/*.js lib/**/**/*.js test/*.js",lint:"npm run jscs && npm run jshint",test:"npm run lint && npm run unit",unit:"istanbul test _mocha --reporter=spec test/*-test.js"},version:"6.2.3"}},{}],87:[function(t,e,n){function r(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function i(t){return"function"==typeof t}function o(t){return"object"==typeof t&&null!==t}e.exports=r,r.EventEmitter=r,r.prototype._events=void 0,r.prototype._maxListeners=void 0,r.defaultMaxListeners=10,r.prototype.setMaxListeners=function(t){if("number"!=typeof t||0>t||isNaN(t))throw TypeError("n must be a positive number");return this._maxListeners=t,this},r.prototype.emit=function(t){var e,n,r,a;if(this._events||(this._events={}),"error"===t&&(!this._events.error||o(this._events.error)&&!this._events.error.length)){if(e=arguments[1],e instanceof Error)throw e;throw TypeError('Uncaught, unspecified "error" event.')}if(n=this._events[t],void 0===n)return!1;if(i(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:for(e=arguments.length,r=Array(e-1),a=1;e>a;a++)r[a-1]=arguments[a];n.apply(this,r)}else if(o(n)){for(e=arguments.length,r=Array(e-1),a=1;e>a;a++)r[a-1]=arguments[a];for(n=n.slice(),e=n.length,a=0;e>a;a++)n[a].apply(this,r)}return!0},r.prototype.addListener=function(t,e){
var n;if(!i(e))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",t,i(e.listener)?e.listener:e),this._events[t]?o(this._events[t])?this._events[t].push(e):this._events[t]=[this._events[t],e]:this._events[t]=e,o(this._events[t])&&!this._events[t].warned&&(n=void 0!==this._maxListeners?this._maxListeners:r.defaultMaxListeners)&&n>0&&this._events[t].length>n&&(this._events[t].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[t].length),"function"==typeof console.trace&&console.trace()),this},r.prototype.on=r.prototype.addListener,r.prototype.once=function(t,e){function n(){this.removeListener(t,n),r||(r=!0,e.apply(this,arguments))}if(!i(e))throw TypeError("listener must be a function");var r=!1;return n.listener=e,this.on(t,n),this},r.prototype.removeListener=function(t,e){var n,r,a;if(!i(e))throw TypeError("listener must be a function");if(!this._events||!this._events[t])return this;if(n=this._events[t],a=n.length,r=-1,n===e||i(n.listener)&&n.listener===e)delete this._events[t],this._events.removeListener&&this.emit("removeListener",t,e);else if(o(n)){for(;0<a--;)if(n[a]===e||n[a].listener&&n[a].listener===e){r=a;break}if(0>r)return this;1===n.length?(n.length=0,delete this._events[t]):n.splice(r,1),this._events.removeListener&&this.emit("removeListener",t,e)}return this},r.prototype.removeAllListeners=function(t){var e;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[t]&&delete this._events[t],this;if(0===arguments.length){for(e in this._events)"removeListener"!==e&&this.removeAllListeners(e);return this.removeAllListeners("removeListener"),this._events={},this}if(e=this._events[t],i(e))this.removeListener(t,e);else for(;e.length;)this.removeListener(t,e[e.length-1]);return delete this._events[t],this},r.prototype.listeners=function(t){return this._events&&this._events[t]?i(this._events[t])?[this._events[t]]:this._events[t].slice():[]},r.listenerCount=function(t,e){return t._events&&t._events[e]?i(t._events[e])?1:t._events[e].length:0}},{}],88:[function(t,e,n){(function(n){var r=t(55);e.exports=function(t,e,i,o){n.isBuffer(t)||(t=new n(t,"binary")),e&&!n.isBuffer(e)&&(e=new n(e,"binary")),i/=8,o=o||0;for(var a,s,c=0,f=0,u=new n(i),d=new n(o),p=0,h=[];;){if(0<p++&&h.push(a),h.push(t),e&&h.push(e),a=r(n.concat(h)),h=[],s=0,i>0)for(;0!==i&&s!==a.length;)u[c++]=a[s],i--,s++;if(o>0&&s!==a.length)for(;0!==o&&s!==a.length;)d[f++]=a[s],o--,s++;if(0===i&&0===o)break}for(s=0;s<a.length;s++)a[s]=0;return{key:u,iv:d}}}).call(this,t(48).Buffer)},{48:48,55:55}],89:[function(t,e,n){n.utils=t(94),n.common=t(90),n.sha=t(93),n.ripemd=t(92),n.hmac=t(91),n.sha1=n.sha.sha1,n.sha256=n.sha.sha256,n.sha224=n.sha.sha224,n.sha384=n.sha.sha384,n.sha512=n.sha.sha512,n.ripemd160=n.ripemd.ripemd160},{90:90,91:91,92:92,93:93,94:94}],90:[function(t,e,n){function r(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}var i=t(89).utils,o=i.assert;n.BlockHash=r,r.prototype.update=function(t,e){if(t=i.toArray(t,e),this.pending=this.pending?this.pending.concat(t):t,this.pendingTotal+=t.length,this.pending.length>=this._delta8){t=this.pending;var n=t.length%this._delta8;for(this.pending=t.slice(t.length-n,t.length),0===this.pending.length&&(this.pending=null),t=i.join32(t,0,t.length-n,this.endian),n=0;n<t.length;n+=this._delta32)this._update(t,n,n+this._delta32)}return this},r.prototype.digest=function(t){return this.update(this._pad()),o(null===this.pending),this._digest(t)},r.prototype._pad=function(){var t=this.pendingTotal,e=this._delta8,n=e-(t+this.padLength)%e,e=Array(n+this.padLength);e[0]=128;for(var r=1;n>r;r++)e[r]=0;if(t<<=3,"big"===this.endian){for(n=8;n<this.padLength;n++)e[r++]=0;e[r++]=0,e[r++]=0,e[r++]=0,e[r++]=0,e[r++]=t>>>24&255,e[r++]=t>>>16&255,e[r++]=t>>>8&255,e[r++]=255&t}else for(e[r++]=255&t,e[r++]=t>>>8&255,e[r++]=t>>>16&255,e[r++]=t>>>24&255,e[r++]=0,e[r++]=0,e[r++]=0,e[r++]=0,n=8;n<this.padLength;n++)e[r++]=0;return e}},{89:89}],91:[function(t,e,n){function r(t,e,n){return this instanceof r?(this.Hash=t,this.blockSize=t.blockSize/8,this.outSize=t.outSize/8,this.outer=this.inner=null,void this._init(i.toArray(e,n))):new r(t,e,n)}var i=t(89).utils,o=i.assert;e.exports=r,r.prototype._init=function(t){t.length>this.blockSize&&(t=(new this.Hash).update(t).digest()),o(t.length<=this.blockSize);for(var e=t.length;e<this.blockSize;e++)t.push(0);for(e=0;e<t.length;e++)t[e]^=54;for(this.inner=(new this.Hash).update(t),e=0;e<t.length;e++)t[e]^=106;this.outer=(new this.Hash).update(t)},r.prototype.update=function(t,e){return this.inner.update(t,e),this},r.prototype.digest=function(t){return this.outer.update(this.inner.digest()),this.outer.digest(t)}},{89:89}],92:[function(t,e,n){function r(){return this instanceof r?(u.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],void(this.endian="little")):new r}function i(t,e,n,r){return 15>=t?e^n^r:31>=t?e&n|~e&r:47>=t?(e|~n)^r:63>=t?e&r|n&~r:e^(n|~r)}t=t(89);var o=t.utils,a=o.rotl32,s=o.sum32,c=o.sum32_3,f=o.sum32_4,u=t.common.BlockHash;o.inherits(r,u),n.ripemd160=r,r.blockSize=512,r.outSize=160,r.hmacStrength=192,r.padLength=64,r.prototype._update=function(t,e){for(var n=this.h[0],r=this.h[1],o=this.h[2],u=this.h[3],b=this.h[4],m=n,v=r,y=o,g=u,_=b,w=0;80>w;w++)var x=s(a(f(n,i(w,r,o,u),t[d[w]+e],15>=w?0:31>=w?1518500249:47>=w?1859775393:63>=w?2400959708:2840853838),h[w]),b),n=b,b=u,u=a(o,10),o=r,r=x,x=s(a(f(m,i(79-w,v,y,g),t[p[w]+e],15>=w?1352829926:31>=w?1548603684:47>=w?1836072691:63>=w?2053994217:0),l[w]),_),m=_,_=g,g=a(y,10),y=v,v=x;x=c(this.h[1],o,g),this.h[1]=c(this.h[2],u,_),this.h[2]=c(this.h[3],b,m),this.h[3]=c(this.h[4],n,v),this.h[4]=c(this.h[0],r,y),this.h[0]=x},r.prototype._digest=function(t){return"hex"===t?o.toHex32(this.h,"little"):o.split32(this.h,"little")};var d=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],p=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],h=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],l=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]},{89:89}],93:[function(t,e,n){function r(){return this instanceof r?(M.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=A,void(this.W=Array(64))):new r}function i(){return this instanceof i?(r.call(this),void(this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])):new i}function o(){return this instanceof o?(M.call(this),this.h=[1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209],this.k=T,void(this.W=Array(160))):new o}function a(){return this instanceof a?(o.call(this),void(this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428])):new a}function s(){return this instanceof s?(M.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],void(this.W=Array(80))):new s}t=t(89);var c=t.utils,f=c.assert,u=c.rotr32,d=c.rotl32,p=c.sum32,h=c.sum32_4,l=c.sum32_5,b=c.rotr64_hi,m=c.rotr64_lo,v=c.shr64_hi,y=c.shr64_lo,g=c.sum64,_=c.sum64_hi,w=c.sum64_lo,x=c.sum64_4_hi,k=c.sum64_4_lo,E=c.sum64_5_hi,S=c.sum64_5_lo,M=t.common.BlockHash,A=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],T=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],j=[1518500249,1859775393,2400959708,3395469782];c.inherits(r,M),n.sha256=r,r.blockSize=512,r.outSize=256,r.hmacStrength=192,r.padLength=64,r.prototype._update=function(t,e){for(var n=this.W,r=0;16>r;r++)n[r]=t[e+r];for(;r<n.length;r++){var i,o=r;i=n[r-2],i=u(i,17)^u(i,19)^i>>>10;var a,s=n[r-7];a=n[r-15],a=u(a,7)^u(a,18)^a>>>3,n[o]=h(i,s,a,n[r-16])}o=this.h[0],i=this.h[1],s=this.h[2],a=this.h[3];var c=this.h[4],d=this.h[5],b=this.h[6],m=this.h[7];for(f(this.k.length===n.length),r=0;r<n.length;r++){var v=m,m=c,m=u(m,6)^u(m,11)^u(m,25),v=l(v,m,c&d^~c&b,this.k[r],n[r]),m=o,m=u(m,2)^u(m,13)^u(m,22),y=p(m,o&i^o&s^i&s),m=b,b=d,d=c,c=p(a,v);a=s,s=i,i=o,o=p(v,y)}this.h[0]=p(this.h[0],o),this.h[1]=p(this.h[1],i),this.h[2]=p(this.h[2],s),this.h[3]=p(this.h[3],a),this.h[4]=p(this.h[4],c),this.h[5]=p(this.h[5],d),this.h[6]=p(this.h[6],b),this.h[7]=p(this.h[7],m)},r.prototype._digest=function(t){return"hex"===t?c.toHex32(this.h,"big"):c.split32(this.h,"big")},c.inherits(i,r),n.sha224=i,i.blockSize=512,i.outSize=224,i.hmacStrength=192,i.padLength=64,i.prototype._digest=function(t){return"hex"===t?c.toHex32(this.h.slice(0,7),"big"):c.split32(this.h.slice(0,7),"big")},c.inherits(o,M),n.sha512=o,o.blockSize=1024,o.outSize=512,o.hmacStrength=192,o.padLength=128,o.prototype._prepareBlock=function(t,e){for(var n=this.W,r=0;32>r;r++)n[r]=t[e+r];for(;r<n.length;r+=2){var i,o=n[r-4],a=n[r-3];i=b(o,a,19);var s=b(a,o,29),o=v(o,a,6);i=i^s^o,0>i&&(i+=4294967296);var a=n[r-4],c=n[r-3],s=m(a,c,19),o=m(c,a,29),a=y(a,c,6),s=s^o^a;0>s&&(s+=4294967296);var o=n[r-14],a=n[r-13],f=n[r-30],u=n[r-29],c=b(f,u,1),d=b(f,u,8),f=v(f,u,7),c=c^d^f;0>c&&(c+=4294967296);var u=n[r-30],p=n[r-29],d=m(u,p,1),f=m(u,p,8),u=y(u,p,7),d=d^f^u;0>d&&(d+=4294967296),f=n[r-32],u=n[r-31],n[r]=x(i,s,o,a,c,d,f,u),n[r+1]=k(i,s,o,a,c,d,f,u)}},o.prototype._update=function(t,e){this._prepareBlock(t,e);var n=this.W,r=this.h[0],i=this.h[1],o=this.h[2],a=this.h[3],s=this.h[4],c=this.h[5],u=this.h[6],d=this.h[7],p=this.h[8],h=this.h[9],l=this.h[10],v=this.h[11],y=this.h[12],x=this.h[13],k=this.h[14],M=this.h[15];f(this.k.length===n.length);for(var A=0;A<n.length;A+=2){var T;T=p;var j=h,I=b(T,j,14),R=b(T,j,18);T=b(j,T,9),I=I^R^T,0>I&&(I+=4294967296),T=I;var j=p,O=h,I=m(j,O,14),R=m(j,O,18),j=m(O,j,9),I=I^R^j;0>I&&(I+=4294967296),j=I,I=p&l^~p&y,0>I&&(I+=4294967296),R=I,I=h&v^~h&x,0>I&&(I+=4294967296);var O=I,B=this.k[A],N=this.k[A+1],C=n[A],P=n[A+1],I=E(k,M,T,j,R,O,B,N,C,P),R=S(k,M,T,j,R,O,B,N,C,P);T=r,j=i,k=b(T,j,28),M=b(j,T,2),T=b(j,T,7),k=k^M^T,0>k&&(k+=4294967296),j=r,O=i,M=m(j,O,28),T=m(O,j,2),j=m(O,j,7),M=M^T^j,0>M&&(M+=4294967296),T=r&o^r&s^o&s,0>T&&(T+=4294967296),j=i&a^i&c^a&c,0>j&&(j+=4294967296),O=_(k,M,T,j),T=w(k,M,T,j),k=y,M=x,y=l,x=v,l=p,v=h,p=_(u,d,I,R),h=w(d,d,I,R),u=s,d=c,s=o,c=a,o=r,a=i,r=_(I,R,O,T),i=w(I,R,O,T)}g(this.h,0,r,i),g(this.h,2,o,a),g(this.h,4,s,c),g(this.h,6,u,d),g(this.h,8,p,h),g(this.h,10,l,v),g(this.h,12,y,x),g(this.h,14,k,M)},o.prototype._digest=function(t){return"hex"===t?c.toHex32(this.h,"big"):c.split32(this.h,"big")},c.inherits(a,o),n.sha384=a,a.blockSize=1024,a.outSize=384,a.hmacStrength=192,a.padLength=128,a.prototype._digest=function(t){return"hex"===t?c.toHex32(this.h.slice(0,12),"big"):c.split32(this.h.slice(0,12),"big")},c.inherits(s,M),n.sha1=s,s.blockSize=512,s.outSize=160,s.hmacStrength=80,s.padLength=64,s.prototype._update=function(t,e){for(var n=this.W,r=0;16>r;r++)n[r]=t[e+r];for(;r<n.length;r++)n[r]=d(n[r-3]^n[r-8]^n[r-14]^n[r-16],1);for(var i=this.h[0],o=this.h[1],a=this.h[2],s=this.h[3],c=this.h[4],r=0;r<n.length;r++){var f,u=~~(r/20),h=d(i,5);f=u;var b=o,m=a,v=s;f=0===f?b&m^~b&v:1===f||3===f?b^m^v:2===f?b&m^b&v^m&v:void 0,u=l(h,f,c,n[r],j[u]),c=s,s=a,a=d(o,30),o=i,i=u}this.h[0]=p(this.h[0],i),this.h[1]=p(this.h[1],o),this.h[2]=p(this.h[2],a),this.h[3]=p(this.h[3],s),this.h[4]=p(this.h[4],c)},s.prototype._digest=function(t){return"hex"===t?c.toHex32(this.h,"big"):c.split32(this.h,"big")}},{89:89}],94:[function(t,e,n){function r(t){return(t>>>24|t>>>8&65280|t<<8&16711680|(255&t)<<24)>>>0}function i(t){return 1===t.length?"0"+t:t}function o(t){return 7===t.length?"0"+t:6===t.length?"00"+t:5===t.length?"000"+t:4===t.length?"0000"+t:3===t.length?"00000"+t:2===t.length?"000000"+t:1===t.length?"0000000"+t:t}function a(t,e){if(!t)throw Error(e||"Assertion failed")}t=t(101),n.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var n=[];if("string"==typeof t)if(e){if("hex"===e)for(t=t.replace(/[^a-z0-9]+/gi,""),0!==t.length%2&&(t="0"+t),r=0;r<t.length;r+=2)n.push(parseInt(t[r]+t[r+1],16))}else for(var r=0;r<t.length;r++){var i=t.charCodeAt(r),o=i>>8,i=255&i;o?n.push(o,i):n.push(i)}else for(r=0;r<t.length;r++)n[r]=0|t[r];return n},n.toHex=function(t){for(var e="",n=0;n<t.length;n++)e+=i(t[n].toString(16));return e},n.htonl=r,n.toHex32=function(t,e){for(var n="",i=0;i<t.length;i++){var a=t[i];"little"===e&&(a=r(a)),n+=o(a.toString(16))}return n},n.zero2=i,n.zero8=o,n.join32=function(t,e,n,r){n-=e,a(0===n%4),n=Array(n/4);for(var i=0;i<n.length;i++,e+=4)n[i]=("big"===r?t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]:t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e])>>>0;return n},n.split32=function(t,e){for(var n=Array(4*t.length),r=0,i=0;r<t.length;r++,i+=4){var o=t[r];"big"===e?(n[i]=o>>>24,n[i+1]=o>>>16&255,n[i+2]=o>>>8&255,n[i+3]=255&o):(n[i+3]=o>>>24,n[i+2]=o>>>16&255,n[i+1]=o>>>8&255,n[i]=255&o)}return n},n.rotr32=function(t,e){return t>>>e|t<<32-e},n.rotl32=function(t,e){return t<<e|t>>>32-e},n.sum32=function(t,e){return t+e>>>0},n.sum32_3=function(t,e,n){return t+e+n>>>0},n.sum32_4=function(t,e,n,r){return t+e+n+r>>>0},n.sum32_5=function(t,e,n,r,i){return t+e+n+r+i>>>0},n.assert=a,n.inherits=t,n.sum64=function(t,e,n,r){var i=r+t[e+1]>>>0;t[e]=(r>i?1:0)+n+t[e]>>>0,t[e+1]=i},n.sum64_hi=function(t,e,n,r){return(e>e+r>>>0?1:0)+t+n>>>0},n.sum64_lo=function(t,e,n,r){return e+r>>>0},n.sum64_4_hi=function(t,e,n,r,i,o,a,s){return r=e+r>>>0,e=0+(e>r?1:0),r=r+o>>>0,e+=o>r?1:0,t+n+i+a+(e+(s>r+s>>>0?1:0))>>>0},n.sum64_4_lo=function(t,e,n,r,i,o,a,s){return e+r+o+s>>>0},n.sum64_5_hi=function(t,e,n,r,i,o,a,s,c,f){return r=e+r>>>0,e=0+(e>r?1:0),r=r+o>>>0,e+=o>r?1:0,r=r+s>>>0,e+=s>r?1:0,t+n+i+a+c+(e+(f>r+f>>>0?1:0))>>>0},n.sum64_5_lo=function(t,e,n,r,i,o,a,s,c,f){return e+r+o+s+f>>>0},n.rotr64_hi=function(t,e,n){return(e<<32-n|t>>>n)>>>0},n.rotr64_lo=function(t,e,n){return(t<<32-n|e>>>n)>>>0},n.shr64_hi=function(t,e,n){return t>>>n},n.shr64_lo=function(t,e,n){return(t<<32-n|e>>>n)>>>0}},{101:101}],95:[function(t,e,n){var r=e.exports;t(87);var i=t(96),o=t(148);r.request=function(t,e){"string"==typeof t&&(t=o.parse(t)),t||(t={}),t.host||t.port||(t.port=parseInt(window.location.port,10)),!t.host&&t.hostname&&(t.host=t.hostname),t.protocol||(t.protocol=t.scheme?t.scheme+":":window.location.protocol),t.host||(t.host=window.location.hostname||window.location.host),/:/.test(t.host)&&(t.port||(t.port=t.host.split(":")[1]),t.host=t.host.split(":")[0]),t.port||(t.port="https:"==t.protocol?443:80);var n=new i(new a,t);return e&&n.on("response",e),n},r.get=function(t,e){t.method="GET";var n=r.request(t,e);return n.end(),n},r.Agent=function(){},r.Agent.defaultMaxSockets=4;var a=function(){if("undefined"==typeof window)throw Error("no window object present");if(window.XMLHttpRequest)return window.XMLHttpRequest;if(window.ActiveXObject)for(var t=["Msxml2.XMLHTTP.6.0","Msxml2.XMLHTTP.3.0","Microsoft.XMLHTTP"],e=0;e<t.length;e++)try{var n=new window.ActiveXObject(t[e]);return function(){if(n){var r=n;return n=null,r}return new window.ActiveXObject(t[e])}}catch(r){}throw Error("ajax not supported in this browser")}();r.STATUS_CODES={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",300:"Multiple Choices",301:"Moved Permanently",302:"Moved Temporarily",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Time-out",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Large",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Unordered Collection",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Time-out",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}},{148:148,87:87,96:96}],96:[function(t,e,n){n=t(145);var r=t(97),i=t(2);t=t(101);var o=e.exports=function(t,e){var n=this;n.writable=!0,n.xhr=t,n.body=[],n.uri=(e.protocol||"http:")+"//"+e.host+(e.port?":"+e.port:"")+(e.path||"/"),"undefined"==typeof e.withCredentials&&(e.withCredentials=!0);try{t.withCredentials=e.withCredentials}catch(o){}if(e.responseType)try{t.responseType=e.responseType}catch(o){}if(t.open(e.method||"GET",n.uri,!0),t.onerror=function(t){n.emit("error",Error("Network error"))},n._headers={},e.headers)for(var s=a(e.headers),c=0;c<s.length;c++){var f=s[c];n.isSafeRequestHeader(f)&&n.setHeader(f,e.headers[f])}e.auth&&this.setHeader("Authorization","Basic "+i.btoa(e.auth));var u=new r;u.on("close",function(){n.emit("close")}),u.on("ready",function(){n.emit("response",u)}),u.on("error",function(t){n.emit("error",t)}),t.onreadystatechange=function(){t.__aborted||u.handle(t)}};t(o,n),o.prototype.setHeader=function(t,e){this._headers[t.toLowerCase()]=e},o.prototype.getHeader=function(t){return this._headers[t.toLowerCase()]},o.prototype.removeHeader=function(t){delete this._headers[t.toLowerCase()]},o.prototype.write=function(t){this.body.push(t)},o.prototype.destroy=function(t){this.xhr.__aborted=!0,this.xhr.abort(),this.emit("close")},o.prototype.end=function(t){void 0!==t&&this.body.push(t);var e=a(this._headers);for(t=0;t<e.length;t++){var n=e[t],r=this._headers[n];if(s(r))for(var i=0;i<r.length;i++)this.xhr.setRequestHeader(n,r[i]);else this.xhr.setRequestHeader(n,r)}if(0===this.body.length)this.xhr.send("");else if("string"==typeof this.body[0])this.xhr.send(this.body.join(""));else if(s(this.body[0])){for(e=[],t=0;t<this.body.length;t++)e.push.apply(e,this.body[t]);this.xhr.send(e)}else if(/Array/.test(Object.prototype.toString.call(this.body[0]))){for(t=i=0;t<this.body.length;t++)i+=this.body[t].length;for(e=new this.body[0].constructor(i),t=n=0;t<this.body.length;t++)for(r=this.body[t],i=0;i<r.length;i++)e[n++]=r[i];this.xhr.send(e)}else if(t=this.body[0],t="undefined"!=typeof Blob&&t instanceof Blob?!0:"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?!0:"undefined"!=typeof FormData&&t instanceof FormData?!0:void 0)this.xhr.send(this.body[0]);else{for(e="",t=0;t<this.body.length;t++)e+=this.body[t].toString();this.xhr.send(e)}},o.unsafeHeaders="accept-charset accept-encoding access-control-request-headers access-control-request-method connection content-length cookie cookie2 content-transfer-encoding date expect host keep-alive origin referer te trailer transfer-encoding upgrade user-agent via".split(" "),o.prototype.isSafeRequestHeader=function(t){if(!t)return!1;var e;t:if(e=o.unsafeHeaders,t=t.toLowerCase(),e.indexOf)e=e.indexOf(t);else{for(var n=0;n<e.length;n++)if(e[n]===t){e=n;break t}e=-1}return-1===e};var a=Object.keys||function(t){var e,n=[];for(e in t)n.push(e);return n},s=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},{101:101,145:145,2:2,97:97}],97:[function(t,e,n){function r(t){t=t.getAllResponseHeaders().split(/\r?\n/);for(var e={},n=0;n<t.length;n++){var r=t[n];if(""!==r){var i=r.match(/^([^:]+):\s*(.*)/);i?(r=i[1].toLowerCase(),i=i[2],void 0!==e[r]?a(e[r])?e[r].push(i):e[r]=[e[r],i]:e[r]=i):e[r]=!0}}return e}n=t(145),t=t(150),e=e.exports=function(t){this.offset=0,this.readable=!0},t.inherits(e,n);var i=!0,o=!0;e.prototype.getResponse=function(t){var e=String(t.responseType).toLowerCase();return"blob"===e?t.responseBlob||t.response:"arraybuffer"===e?t.response:t.responseText},e.prototype.getHeader=function(t){return this.headers[t.toLowerCase()]},e.prototype.handle=function(t){if(2===t.readyState&&o){try{this.statusCode=t.status,this.headers=r(t)}catch(e){o=!1}o&&this.emit("ready")}else if(i&&3===t.readyState){try{this.statusCode||(this.statusCode=t.status,this.headers=r(t),this.emit("ready"))}catch(e){}try{this._emitData(t)}catch(e){i=!1}}else 4===t.readyState&&(this.statusCode||(this.statusCode=t.status,this.emit("ready")),this._emitData(t),t.error?this.emit("error",this.getResponse(t)):this.emit("end"),this.emit("close"))},e.prototype._emitData=function(t){t=this.getResponse(t)||"",t.toString().match(/ArrayBuffer/)?(this.emit("data",new Uint8Array(t,this.offset)),this.offset=t.byteLength):t.length>this.offset&&(this.emit("data",t.slice(this.offset)),this.offset=t.length)};var a=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},{145:145,150:150}],98:[function(t,e,n){var r=t(95);t=e.exports;for(var i in r)r.hasOwnProperty(i)&&(t[i]=r[i]);t.request=function(t,e){return t||(t={}),t.scheme="https",t.protocol="https:",r.request.call(this,t,e)}},{95:95}],99:[function(t,e,n){n.read=function(t,e,n,r,i){var o;o=8*i-r-1;var a=(1<<o)-1,s=a>>1,c=-7;i=n?i-1:0;var f=n?-1:1,u=t[e+i];for(i+=f,n=u&(1<<-c)-1,u>>=-c,c+=o;c>0;n=256*n+t[e+i],i+=f,c-=8);for(o=n&(1<<-c)-1,n>>=-c,c+=r;c>0;o=256*o+t[e+i],i+=f,c-=8);if(0===n)n=1-s;else{if(n===a)return o?NaN:1/0*(u?-1:1);o+=Math.pow(2,r),n-=s}return(u?-1:1)*o*Math.pow(2,n-r)},n.write=function(t,e,n,r,i,o){var a,s=8*o-i-1,c=(1<<s)-1,f=c>>1,u=23===i?Math.pow(2,-24)-Math.pow(2,-77):0;o=r?0:o-1;var d=r?1:-1,p=0>e||0===e&&0>1/e?1:0;for(e=Math.abs(e),isNaN(e)||1/0===e?(e=isNaN(e)?1:0,r=c):(r=Math.floor(Math.log(e)/Math.LN2),1>e*(a=Math.pow(2,-r))&&(r--,a*=2),e=r+f>=1?e+u/a:e+u*Math.pow(2,1-f),e*a>=2&&(r++,a/=2),r+f>=c?(e=0,r=c):r+f>=1?(e=(e*a-1)*Math.pow(2,i),r+=f):(e=e*Math.pow(2,f-1)*Math.pow(2,i),r=0));i>=8;t[n+o]=255&e,o+=d,e/=256,i-=8);for(r=r<<i|e,s+=i;s>0;t[n+o]=255&r,o+=d,r/=256,s-=8);t[n+o-d]|=128*p}},{}],100:[function(t,e,n){var r=[].indexOf;e.exports=function(t,e){if(r)return t.indexOf(e);for(var n=0;n<t.length;++n)if(t[n]===e)return n;return-1}},{}],101:[function(t,e,n){e.exports="function"==typeof Object.create?function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:function(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},{}],102:[function(t,e,n){e.exports=function(t){return!(null==t||!(t._isBuffer||t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)))}},{}],103:[function(t,e,n){e.exports=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)}},{}],104:[function(t,e,n){function r(t){this.rand=t||new o.Rand}var i=t(19),o=t(20);e.exports=r,r.create=function(t){return new r(t)},r.prototype._rand=function(t){var e=t.bitLength();return t=this.rand.generate(Math.ceil(e/8)),t[0]|=3,e&=7,0!==e&&(t[t.length-1]>>=7-e),new i(t)},r.prototype.test=function(t,e,n){var r=t.bitLength(),o=i.mont(t),a=new i(1).toRed(o);e||(e=Math.max(1,r/48|0));for(var s=t.subn(1),r=s.subn(1),c=0;!s.testn(c);c++);for(t=t.shrn(c),s=s.toRed(o);e>0;e--){var f=this._rand(r);if(n&&n(f),f=f.toRed(o).redPow(t),0!==f.cmp(a)&&0!==f.cmp(s)){for(var u=1;c>u;u++){if(f=f.redSqr(),0===f.cmp(a))return!1;if(0===f.cmp(s))break}if(u===c)return!1}}return!0},r.prototype.getDivisor=function(t,e){var n=t.bitLength(),r=i.mont(t),o=new i(1).toRed(r);e||(e=Math.max(1,n/48|0));for(var a=t.subn(1),n=a.subn(1),s=0;!a.testn(s);s++);for(var c=t.shrn(s),a=a.toRed(r);e>0;e--){var f=this._rand(n),u=t.gcd(f);if(0!==u.cmpn(1))return u;if(f=f.toRed(r).redPow(c),0!==f.cmp(o)&&0!==f.cmp(a)){for(u=1;s>u;u++){if(f=f.redSqr(),0===f.cmp(o))return f.fromRed().subn(1).gcd(t);if(0===f.cmp(a))break}if(u===s)return f=f.redSqr(),f.fromRed().subn(1).gcd(t)}}return!1}},{19:19,20:20}],105:[function(t,e,n){function r(t,e){if(!t)throw Error(e||"Assertion failed")}e.exports=r,r.equal=function(t,e,n){if(t!=e)throw Error(n||"Assertion failed: "+t+" != "+e)}},{}],106:[function(t,e,n){function r(t){if(t=""+t,!(1e4<t.length)&&(t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t))){var e=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*e;case"days":case"day":case"d":return 864e5*e;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*e;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*e;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return e}}}function i(t,e,n){return e>t?void 0:1.5*e>t?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}e.exports=function(t,e){return e=e||{},"string"==typeof t?r(t):e["long"]?i(t,864e5,"day")||i(t,36e5,"hour")||i(t,6e4,"minute")||i(t,1e3,"second")||t+" ms":t>=864e5?Math.round(t/864e5)+"d":t>=36e5?Math.round(t/36e5)+"h":t>=6e4?Math.round(t/6e4)+"m":t>=1e3?Math.round(t/1e3)+"s":t+"ms"}},{}],107:[function(t,e,n){e.exports={"2.16.840.1.101.3.4.1.1":"aes-128-ecb","2.16.840.1.101.3.4.1.2":"aes-128-cbc","2.16.840.1.101.3.4.1.3":"aes-128-ofb","2.16.840.1.101.3.4.1.4":"aes-128-cfb","2.16.840.1.101.3.4.1.21":"aes-192-ecb","2.16.840.1.101.3.4.1.22":"aes-192-cbc","2.16.840.1.101.3.4.1.23":"aes-192-ofb","2.16.840.1.101.3.4.1.24":"aes-192-cfb","2.16.840.1.101.3.4.1.41":"aes-256-ecb","2.16.840.1.101.3.4.1.42":"aes-256-cbc","2.16.840.1.101.3.4.1.43":"aes-256-ofb","2.16.840.1.101.3.4.1.44":"aes-256-cfb"}},{}],108:[function(t,e,n){t=t(3),e=t.define("RSAPrivateKey",function(){this.seq().obj(this.key("version")["int"](),this.key("modulus")["int"](),this.key("publicExponent")["int"](),this.key("privateExponent")["int"](),this.key("prime1")["int"](),this.key("prime2")["int"](),this.key("exponent1")["int"](),this.key("exponent2")["int"](),this.key("coefficient")["int"]())}),n.RSAPrivateKey=e,e=t.define("RSAPublicKey",function(){this.seq().obj(this.key("modulus")["int"](),this.key("publicExponent")["int"]())}),n.RSAPublicKey=e,e=t.define("SubjectPublicKeyInfo",function(){this.seq().obj(this.key("algorithm").use(r),this.key("subjectPublicKey").bitstr())}),n.PublicKey=e;var r=t.define("AlgorithmIdentifier",function(){this.seq().obj(this.key("algorithm").objid(),this.key("none").null_().optional(),this.key("curve").objid().optional(),this.key("params").seq().obj(this.key("p")["int"](),this.key("q")["int"](),this.key("g")["int"]()).optional())});e=t.define("PrivateKeyInfo",function(){this.seq().obj(this.key("version")["int"](),this.key("algorithm").use(r),this.key("subjectPrivateKey").octstr())}),n.PrivateKey=e,e=t.define("EncryptedPrivateKeyInfo",function(){this.seq().obj(this.key("algorithm").seq().obj(this.key("id").objid(),this.key("decrypt").seq().obj(this.key("kde").seq().obj(this.key("id").objid(),this.key("kdeparams").seq().obj(this.key("salt").octstr(),this.key("iters")["int"]())),this.key("cipher").seq().obj(this.key("algo").objid(),this.key("iv").octstr()))),this.key("subjectPrivateKey").octstr())}),n.EncryptedPrivateKey=e,e=t.define("DSAPrivateKey",function(){this.seq().obj(this.key("version")["int"](),this.key("p")["int"](),this.key("q")["int"](),this.key("g")["int"](),this.key("pub_key")["int"](),this.key("priv_key")["int"]())}),n.DSAPrivateKey=e,n.DSAparam=t.define("DSAparam",function(){this["int"]()}),e=t.define("ECPrivateKey",function(){this.seq().obj(this.key("version")["int"](),this.key("privateKey").octstr(),this.key("parameters").optional().explicit(0).use(i),this.key("publicKey").optional().explicit(1).bitstr())}),n.ECPrivateKey=e;var i=t.define("ECParameters",function(){this.choice({namedCurve:this.objid()})});n.signature=t.define("signature",function(){this.seq().obj(this.key("r")["int"](),this.key("s")["int"]())})},{3:3}],109:[function(t,e,n){(function(n){var r=/Proc-Type: 4,ENCRYPTED\r?\nDEK-Info: AES-((?:128)|(?:192)|(?:256))-CBC,([0-9A-H]+)\r?\n\r?\n([0-9A-z\n\r\+\/\=]+)\r?\n/m,i=/^-----BEGIN (.*) KEY-----\r?\n/m,o=/^-----BEGIN (.*) KEY-----\r?\n([0-9A-z\n\r\+\/\=]+)\r?\n-----END \1 KEY-----$/m,a=t(88),s=t(24);e.exports=function(t,e){var c,f=t.toString(),u=f.match(r);if(u){var d="aes"+u[1],p=new n(u[2],"hex");c=new n(u[3].replace(/\r?\n/g,""),"base64");var h=a(e,p.slice(0,8),parseInt(u[1],10)).key,u=[],d=s.createDecipheriv(d,h,p);u.push(d.update(c)),u.push(d["final"]()),c=n.concat(u)}else c=f.match(o),c=new n(c[2].replace(/\r?\n/g,""),"base64");return{tag:f.match(i)[1]+" KEY",data:c}}}).call(this,t(48).Buffer)},{24:24,48:48,88:88}],110:[function(t,e,n){(function(n){function r(t){var e;"object"!=typeof t||n.isBuffer(t)||(e=t.passphrase,t=t.key),"string"==typeof t&&(t=new n(t));
var r=a(t,e);switch(t=r.tag,r=r.data,t){case"PUBLIC KEY":switch(e=i.PublicKey.decode(r,"der"),t=e.algorithm.algorithm.join(".")){case"1.2.840.113549.1.1.1":return i.RSAPublicKey.decode(e.subjectPublicKey.data,"der");case"1.2.840.10045.2.1":return e.subjectPrivateKey=e.subjectPublicKey,{type:"ec",data:e};case"1.2.840.10040.4.1":return e.algorithm.params.pub_key=i.DSAparam.decode(e.subjectPublicKey.data,"der"),{type:"dsa",data:e.algorithm.params};default:throw Error("unknown key id "+t)}throw Error("unknown key type "+t);case"ENCRYPTED PRIVATE KEY":t=r=i.EncryptedPrivateKey.decode(r,"der");var f=e,u=t.algorithm.decrypt.kde.kdeparams.salt,d=parseInt(t.algorithm.decrypt.kde.kdeparams.iters.toString(),10);e=o[t.algorithm.decrypt.cipher.algo.join(".")],r=t.algorithm.decrypt.cipher.iv,t=t.subjectPrivateKey;var p=parseInt(e.split("-")[1],10)/8,f=c.pbkdf2Sync(f,u,d,p);e=s.createDecipheriv(e,f,r),r=[],r.push(e.update(t)),r.push(e["final"]()),r=n.concat(r);case"PRIVATE KEY":switch(e=i.PrivateKey.decode(r,"der"),t=e.algorithm.algorithm.join(".")){case"1.2.840.113549.1.1.1":return i.RSAPrivateKey.decode(e.subjectPrivateKey,"der");case"1.2.840.10045.2.1":return{curve:e.algorithm.curve,privateKey:i.ECPrivateKey.decode(e.subjectPrivateKey,"der").privateKey};case"1.2.840.10040.4.1":return e.algorithm.params.priv_key=i.DSAparam.decode(e.subjectPrivateKey,"der"),{type:"dsa",params:e.algorithm.params};default:throw Error("unknown key id "+t)}throw Error("unknown key type "+t);case"RSA PUBLIC KEY":return i.RSAPublicKey.decode(r,"der");case"RSA PRIVATE KEY":return i.RSAPrivateKey.decode(r,"der");case"DSA PRIVATE KEY":return{type:"dsa",params:i.DSAPrivateKey.decode(r,"der")};case"EC PRIVATE KEY":return r=i.ECPrivateKey.decode(r,"der"),{curve:r.parameters.value,privateKey:r.privateKey};default:throw Error("unknown key type "+t)}}var i=t(108),o=t(107),a=t(109),s=t(24),c=t(112);e.exports=r,r.signature=i.signature}).call(this,t(48).Buffer)},{107:107,108:108,109:109,112:112,24:24,48:48}],111:[function(t,e,n){(function(t){function e(t,e){for(var n=0,r=t.length-1;r>=0;r--){var i=t[r];"."===i?t.splice(r,1):".."===i?(t.splice(r,1),n++):n&&(t.splice(r,1),n--)}if(e)for(;n--;n)t.unshift("..");return t}function r(t,e){if(t.filter)return t.filter(e);for(var n=[],r=0;r<t.length;r++)e(t[r],r,t)&&n.push(t[r]);return n}var i=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;n.resolve=function(){for(var n="",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var a=o>=0?arguments[o]:t.cwd();if("string"!=typeof a)throw new TypeError("Arguments to path.resolve must be strings");a&&(n=a+"/"+n,i="/"===a.charAt(0))}return n=e(r(n.split("/"),function(t){return!!t}),!i).join("/"),(i?"/":"")+n||"."},n.normalize=function(t){var i=n.isAbsolute(t),a="/"===o(t,-1);return(t=e(r(t.split("/"),function(t){return!!t}),!i).join("/"))||i||(t="."),t&&a&&(t+="/"),(i?"/":"")+t},n.isAbsolute=function(t){return"/"===t.charAt(0)},n.join=function(){var t=Array.prototype.slice.call(arguments,0);return n.normalize(r(t,function(t,e){if("string"!=typeof t)throw new TypeError("Arguments to path.join must be strings");return t}).join("/"))},n.relative=function(t,e){function r(t){for(var e=0;e<t.length&&""===t[e];e++);for(var n=t.length-1;n>=0&&""===t[n];n--);return e>n?[]:t.slice(e,n-e+1)}t=n.resolve(t).substr(1),e=n.resolve(e).substr(1);for(var i=r(t.split("/")),o=r(e.split("/")),a=Math.min(i.length,o.length),s=a,c=0;a>c;c++)if(i[c]!==o[c]){s=c;break}for(a=[],c=s;c<i.length;c++)a.push("..");return a=a.concat(o.slice(s)),a.join("/")},n.sep="/",n.delimiter=":",n.dirname=function(t){var e=i.exec(t).slice(1);return t=e[0],e=e[1],t||e?(e&&(e=e.substr(0,e.length-1)),t+e):"."},n.basename=function(t,e){var n=i.exec(t).slice(1)[2];return e&&n.substr(-1*e.length)===e&&(n=n.substr(0,n.length-e.length)),n},n.extname=function(t){return i.exec(t).slice(1)[3]};var o="b"==="ab".substr(-1)?function(t,e,n){return t.substr(e,n)}:function(t,e,n){return 0>e&&(e=t.length+e),t.substr(e,n)}}).call(this,t(113))},{113:113}],112:[function(t,e,n){(function(e){function r(t,n,r,a,s){if("number"!=typeof r)throw new TypeError("Iterations not a number");if(0>r)throw new TypeError("Bad iterations");if("number"!=typeof a)throw new TypeError("Key length not a number");if(0>a||a>o)throw new TypeError("Bad key length");s=s||"sha1",e.isBuffer(t)||(t=new e(t,"binary")),e.isBuffer(n)||(n=new e(n,"binary"));var c,f=1,u=new e(a),d=new e(n.length+4);n.copy(d,0,0,n.length);for(var p,h,l=1;f>=l;l++){d.writeUInt32BE(l,n.length);var b=i(s,t).update(d).digest();c||(c=b.length,h=new e(c),f=Math.ceil(a/c),p=a-(f-1)*c),b.copy(h,0,0,c);for(var m=1;r>m;m++)for(var b=i(s,t).update(b).digest(),v=0;c>v;v++)h[v]^=b[v];h.copy(u,(l-1)*c,0,l===f?p:c)}return u}var i=t(56),o=Math.pow(2,30)-1;n.pbkdf2=function(t,e,n,i,o,a){if("function"==typeof o&&(a=o,o=void 0),"function"!=typeof a)throw Error("No callback provided to pbkdf2");var s=r(t,e,n,i,o);setTimeout(function(){a(void 0,s)})},n.pbkdf2Sync=r}).call(this,t(48).Buffer)},{48:48,56:56}],113:[function(t,e,n){function r(){f=!1,s.length?c=s.concat(c):u=-1,c.length&&i()}function i(){if(!f){var t=setTimeout(r);f=!0;for(var e=c.length;e;){for(s=c,c=[];++u<e;)s&&s[u].run();u=-1,e=c.length}s=null,f=!1,clearTimeout(t)}}function o(t,e){this.fun=t,this.array=e}function a(){}t=e.exports={};var s,c=[],f=!1,u=-1;t.nextTick=function(t){var e=Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];c.push(new o(t,e)),1!==c.length||f||setTimeout(i,0)},o.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=a,t.addListener=a,t.once=a,t.off=a,t.removeListener=a,t.removeAllListeners=a,t.emit=a,t.binding=function(t){throw Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(t){throw Error("process.chdir is not supported")},t.umask=function(){return 0}},{}],114:[function(t,e,n){n.publicEncrypt=t(117),n.privateDecrypt=t(116),n.privateEncrypt=function(t,e){return n.publicEncrypt(t,e,!0)},n.publicDecrypt=function(t,e){return n.privateDecrypt(t,e,!0)}},{116:116,117:117}],115:[function(t,e,n){(function(n){var r=t(53);e.exports=function(t,e){for(var i,o=new n(""),a=0;o.length<e;){i=a++;var s=new n(4);s.writeUInt32BE(i,0),i=s,o=n.concat([o,r("sha1").update(t).update(i).digest()])}return o.slice(0,e)}}).call(this,t(48).Buffer)},{48:48,53:53}],116:[function(t,e,n){(function(n){var r=t(110),i=t(115),o=t(119),a=t(19),s=t(40),c=t(53),f=t(118);e.exports=function(t,e,u){var d;d=t.padding?t.padding:u?1:4,t=r(t);var p=t.modulus.byteLength();if(e.length>p||0<=new a(e).cmp(t.modulus))throw Error("decryption error");e=u?f(new a(e),t):s(e,t);var h=new n(p-e.length);if(h.fill(0),e=n.concat([h,e],p),4===d){if(p=e,d=t.modulus.byteLength(),t=c("sha1").update(new n("")).digest(),u=t.length,0!==p[0])throw Error("decryption error");e=p.slice(1,u+1),p=p.slice(u+1),e=o(e,i(p,u)),d=o(p,i(e,d-u-1)),p=d.slice(0,u),t=new n(t),p=new n(p),e=0,h=t.length,t.length!==p.length&&(e++,h=Math.min(t.length,p.length));for(var l=-1;++l<h;)e+=t[l]^p[l];if(e)throw Error("decryption error");for(;0===d[u];)u++;if(1!==d[u++])throw Error("decryption error");return d.slice(u)}if(1===d){for(d=e,t=d.slice(0,2),p=2,e=0;0!==d[p++];)if(p>=d.length){e++;break}if(h=d.slice(2,p-1),d.slice(p-1,p),("0002"!==t.toString("hex")&&!u||"0001"!==t.toString("hex")&&u)&&e++,8>h.length&&e++,e)throw Error("decryption error");return d.slice(p)}if(3===d)return e;throw Error("unknown padding")}}).call(this,t(48).Buffer)},{110:110,115:115,118:118,119:119,19:19,40:40,48:48,53:53}],117:[function(t,e,n){(function(n){var r=t(110),i=t(124),o=t(53),a=t(115),s=t(119),c=t(19),f=t(118),u=t(40);e.exports=function(t,e,d){var p;if(p=t.padding?t.padding:d?1:4,t=r(t),4===p){p=t.modulus.byteLength();var h=e.length,l=o("sha1").update(new n("")).digest(),b=l.length,m=2*b;if(h>p-m-2)throw Error("message too long");m=new n(p-h-m-2),m.fill(0);var v=p-b-1,h=i(b);e=s(n.concat([l,m,new n([1]),e],v),a(h,v)),b=s(h,a(e,b)),e=new c(n.concat([new n([0]),b,e],p))}else if(1===p){if(b=e.length,p=t.modulus.byteLength(),b>p-11)throw Error("message too long");if(d)b=new n(p-b-3),b.fill(255);else{for(var y,b=p-b-3,l=new n(b),h=0,m=i(2*b),v=0;b>h;)v===m.length&&(m=i(2*b),v=0),(y=m[v++])&&(l[h++]=y);b=l}e=new c(n.concat([new n([0,d?1:2]),b,new n([0]),e],p))}else{if(3!==p)throw Error("unknown padding");if(e=new c(e),0<=e.cmp(t.modulus))throw Error("data too long for modulus")}return d?u(e,t):f(e,t)}}).call(this,t(48).Buffer)},{110:110,115:115,118:118,119:119,124:124,19:19,40:40,48:48,53:53}],118:[function(t,e,n){(function(n){var r=t(19);e.exports=function(t,e){return new n(t.toRed(r.mont(e.modulus)).redPow(new r(e.publicExponent)).fromRed().toArray())}}).call(this,t(48).Buffer)},{19:19,48:48}],119:[function(t,e,n){e.exports=function(t,e){for(var n=t.length,r=-1;++r<n;)t[r]^=e[r];return t}},{}],120:[function(t,e,n){(function(t){!function(r){function i(t){throw new RangeError(_[t])}function o(t,e){for(var n=t.length,r=[];n--;)r[n]=e(t[n]);return r}function a(t,e){var n=t.split("@"),r="";return 1<n.length&&(r=n[0]+"@",t=n[1]),t=t.replace(g,"."),n=t.split("."),n=o(n,e).join("."),r+n}function s(t){for(var e,n,r=[],i=0,o=t.length;o>i;)e=t.charCodeAt(i++),e>=55296&&56319>=e&&o>i?(n=t.charCodeAt(i++),56320==(64512&n)?r.push(((1023&e)<<10)+(1023&n)+65536):(r.push(e),i--)):r.push(e);return r}function c(t){return o(t,function(t){var e="";return t>65535&&(t-=65536,e+=x(t>>>10&1023|55296),t=56320|1023&t),e+=x(t)}).join("")}function f(t,e){return t+22+75*(26>t)-((0!=e)<<5)}function u(t,e,n){var r=0;for(t=n?w(t/700):t>>1,t+=w(t/e);t>455;r+=36)t=w(t/35);return w(r+36*t/(t+38))}function d(t){var e,n,r,o,a,s,f=[],d=t.length,p=0,h=128,l=72;for(n=t.lastIndexOf("-"),0>n&&(n=0),r=0;n>r;++r)128<=t.charCodeAt(r)&&i("not-basic"),f.push(t.charCodeAt(r));for(n=n>0?n+1:0;d>n;){for(r=p,e=1,o=36;n>=d&&i("invalid-input"),a=t.charCodeAt(n++),a=10>a-48?a-22:26>a-65?a-65:26>a-97?a-97:36,(a>=36||a>w((2147483647-p)/e))&&i("overflow"),p+=a*e,s=l>=o?1:o>=l+26?26:o-l,!(s>a);o+=36)a=36-s,e>w(2147483647/a)&&i("overflow"),e*=a;e=f.length+1,l=u(p-r,e,0==r),w(p/e)>2147483647-h&&i("overflow"),h+=w(p/e),p%=e,f.splice(p++,0,h)}return c(f)}function p(t){var e,n,r,o,a,c,d,p,h,l,b,m,v=[];for(t=s(t),l=t.length,e=128,n=0,a=72,c=0;l>c;++c)h=t[c],128>h&&v.push(x(h));for((r=o=v.length)&&v.push("-");l>r;){for(d=2147483647,c=0;l>c;++c)h=t[c],h>=e&&d>h&&(d=h);for(b=r+1,d-e>w((2147483647-n)/b)&&i("overflow"),n+=(d-e)*b,e=d,c=0;l>c;++c)if(h=t[c],e>h&&2147483647<++n&&i("overflow"),h==e){for(p=n,d=36;h=a>=d?1:d>=a+26?26:d-a,!(h>p);d+=36)m=p-h,p=36-h,v.push(x(f(h+m%p,0))),p=w(m/p);v.push(x(f(p,0))),a=u(n,b,r==o),n=0,++r}++n,++e}return v.join("")}var h="object"==typeof n&&n&&!n.nodeType&&n,l="object"==typeof e&&e&&!e.nodeType&&e,b="object"==typeof t&&t;b.global!==b&&b.window!==b&&b.self!==b||(r=b);var m,v=/^xn--/,y=/[^\x20-\x7E]/,g=/[\x2E\u3002\uFF0E\uFF61]/g,_={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},w=Math.floor,x=String.fromCharCode,b={version:"1.3.2",ucs2:{decode:s,encode:c},decode:d,encode:p,toASCII:function(t){return a(t,function(t){return y.test(t)?"xn--"+p(t):t})},toUnicode:function(t){return a(t,function(t){return v.test(t)?d(t.slice(4).toLowerCase()):t})}};if(h&&l)if(e.exports==h)l.exports=b;else for(m in b)b.hasOwnProperty(m)&&(h[m]=b[m]);else r.punycode=b}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],121:[function(t,e,n){e.exports=function(t,e,n,i){n=n||"=";var o={};if("string"!=typeof t||0===t.length)return o;var a=/\+/g;for(t=t.split(e||"&"),e=1e3,i&&"number"==typeof i.maxKeys&&(e=i.maxKeys),i=t.length,e>0&&i>e&&(i=e),e=0;i>e;++e){var s,c=t[e].replace(a,"%20"),f=c.indexOf(n);f>=0?(s=c.substr(0,f),c=c.substr(f+1)):(s=c,c=""),s=decodeURIComponent(s),c=decodeURIComponent(c),Object.prototype.hasOwnProperty.call(o,s)?r(o[s])?o[s].push(c):o[s]=[o[s],c]:o[s]=c}return o};var r=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},{}],122:[function(t,e,n){function r(t,e){if(t.map)return t.map(e);for(var n=[],r=0;r<t.length;r++)n.push(e(t[r],r));return n}var i=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}};e.exports=function(t,e,n,s){return e=e||"&",n=n||"=",null===t&&(t=void 0),"object"==typeof t?r(a(t),function(a){var s=encodeURIComponent(i(a))+n;return o(t[a])?r(t[a],function(t){return s+encodeURIComponent(i(t))}).join(e):s+encodeURIComponent(i(t[a]))}).join(e):s?encodeURIComponent(i(s))+n+encodeURIComponent(i(t)):""};var o=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},a=Object.keys||function(t){var e,n=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.push(e);return n}},{}],123:[function(t,e,n){n.decode=n.parse=t(121),n.encode=n.stringify=t(122)},{121:121,122:122}],124:[function(t,e,n){(function(t,n,r){function i(){throw Error("secure random number generation not supported by this browser\nuse chrome, FireFox or Internet Explorer 11")}function o(e,i){if(e>65536)throw Error("requested too many random bytes");var o=new n.Uint8Array(e);e>0&&a.getRandomValues(o);var s=new r(o.buffer);return"function"==typeof i?t.nextTick(function(){i(null,s)}):s}var a=n.crypto||n.msCrypto;e.exports=a&&a.getRandomValues?o:i}).call(this,t(113),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},t(48).Buffer)},{113:113,48:48}],125:[function(t,e,n){e.exports=t(126)},{126:126}],126:[function(t,e,n){(function(n){function r(t){return this instanceof r?(s.call(this,t),c.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),void this.once("end",i)):new r(t)}function i(){this.allowHalfOpen||this._writableState.ended||n.nextTick(this.end.bind(this))}e.exports=r;var o=Object.keys||function(t){var e,n=[];for(e in t)n.push(e);return n},a=t(51);a.inherits=t(101);var s=t(128),c=t(130);a.inherits(r,s),function(t,e){for(var n=0,r=t.length;r>n;n++)e(t[n],n)}(o(c.prototype),function(t){r.prototype[t]||(r.prototype[t]=c.prototype[t])})}).call(this,t(113))},{101:101,113:113,128:128,130:130,51:51}],127:[function(t,e,n){function r(t){return this instanceof r?void i.call(this,t):new r(t)}e.exports=r;var i=t(129);e=t(51),e.inherits=t(101),e.inherits(r,i),r.prototype._transform=function(t,e,n){n(null,t)}},{101:101,129:129,51:51}],128:[function(t,e,n){(function(n){function r(e,n){var r=t(126);e=e||{};var i=e.highWaterMark,o=e.objectMode?16:16384;this.highWaterMark=i||0===i?i:o,this.highWaterMark=~~this.highWaterMark,this.buffer=[],this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.reading=this.endEmitted=this.ended=!1,this.sync=!0,this.readableListening=this.emittedReadable=this.needReadable=!1,this.objectMode=!!e.objectMode,n instanceof r&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.defaultEncoding=e.defaultEncoding||"utf8",this.ranOut=!1,this.awaitDrain=0,this.readingMore=!1,this.encoding=this.decoder=null,e.encoding&&(w||(w=t(146).StringDecoder),this.decoder=new w(e.encoding),this.encoding=e.encoding)}function i(e){return t(126),this instanceof i?(this._readableState=new r(e,this),this.readable=!0,void g.call(this)):new i(e)}function o(t,e,n,r,i){var o;o=n;var a=null;return _.isBuffer(o)||_.isString(o)||_.isNullOrUndefined(o)||e.objectMode||(a=new TypeError("Invalid non-string/buffer chunk")),(o=a)?t.emit("error",o):_.isNullOrUndefined(n)?(e.reading=!1,e.ended||(e.decoder&&!e.ended&&(n=e.decoder.end())&&n.length&&(e.buffer.push(n),e.length+=e.objectMode?1:n.length),e.ended=!0,s(t))):e.objectMode||n&&0<n.length?e.ended&&!i?(n=Error("stream.push() after EOF"),t.emit("error",n)):e.endEmitted&&i?(n=Error("stream.unshift() after end event"),t.emit("error",n)):(!e.decoder||i||r||(n=e.decoder.write(n)),i||(e.reading=!1),e.flowing&&0===e.length&&!e.sync?(t.emit("data",n),t.read(0)):(e.length+=e.objectMode?1:n.length,i?e.buffer.unshift(n):e.buffer.push(n),e.needReadable&&s(t)),f(t,e)):i||(e.reading=!1),!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}function a(t,e){if(0===e.length&&e.ended)return 0;if(e.objectMode)return 0===t?0:1;if(isNaN(t)||_.isNull(t))return e.flowing&&e.buffer.length?e.buffer[0].length:e.length;if(0>=t)return 0;if(t>e.highWaterMark){var n=t;if(n>=8388608)n=8388608;else{n--;for(var r=1;32>r;r<<=1)n|=n>>r;n++}e.highWaterMark=n}return t>e.length?e.ended?e.length:(e.needReadable=!0,0):t}function s(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(x("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?n.nextTick(function(){c(t)}):c(t))}function c(t){x("emit readable"),t.emit("readable"),p(t)}function f(t,e){e.readingMore||(e.readingMore=!0,n.nextTick(function(){for(var n=e.length;!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark&&(x("maybeReadMore read 0"),t.read(0),n!==e.length);)n=e.length;e.readingMore=!1}))}function u(t){return function(){var e=t._readableState;x("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&y.listenerCount(t,"data")&&(e.flowing=!0,p(t))}}function d(t,e){e.resumeScheduled||(e.resumeScheduled=!0,n.nextTick(function(){e.resumeScheduled=!1,t.emit("resume"),p(t),e.flowing&&!e.reading&&t.read(0)}))}function p(t){var e=t._readableState;if(x("flow",e.flowing),e.flowing)do var n=t.read();while(null!==n&&e.flowing)}function h(t,e){var n=e.buffer,r=e.length,i=!!e.decoder,o=!!e.objectMode;if(0===n.length)return null;if(0===r)r=null;else if(o)r=n.shift();else if(!t||t>=r)r=i?n.join(""):v.concat(n,r),n.length=0;else if(t<n[0].length)o=n[0],r=o.slice(0,t),n[0]=o.slice(t);else if(t===n[0].length)r=n.shift();else for(var r=i?"":new v(t),a=0,s=0,c=n.length;c>s&&t>a;s++){var o=n[0],f=Math.min(t-a,o.length);i?r+=o.slice(0,f):o.copy(r,a,0,f),f<o.length?n[0]=o.slice(f):n.shift(),a+=f}return r}function l(t){var e=t._readableState;if(0<e.length)throw Error("endReadable called on non-empty stream");e.endEmitted||(e.ended=!0,n.nextTick(function(){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}))}function b(t,e){for(var n=0,r=t.length;r>n;n++)e(t[n],n)}e.exports=i;var m=t(103),v=t(48).Buffer;i.ReadableState=r;var y=t(87).EventEmitter;y.listenerCount||(y.listenerCount=function(t,e){return t.listeners(e).length});var g=t(145),_=t(51);_.inherits=t(101);var w,x=t(21),x=x&&x.debuglog?x.debuglog("stream"):function(){};_.inherits(i,g),i.prototype.push=function(t,e){var n=this._readableState;return _.isString(t)&&!n.objectMode&&(e=e||n.defaultEncoding,e!==n.encoding&&(t=new v(t,e),e="")),o(this,n,t,e,!1)},i.prototype.unshift=function(t){return o(this,this._readableState,t,"",!0)},i.prototype.setEncoding=function(e){return w||(w=t(146).StringDecoder),this._readableState.decoder=new w(e),this._readableState.encoding=e,this},i.prototype.read=function(t){x("read",t);var e=this._readableState,n=t;if((!_.isNumber(t)||t>0)&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return x("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?l(this):s(this),null;if(t=a(t,e),0===t&&e.ended)return 0===e.length&&l(this),null;var r=e.needReadable;return x("need readable",r),(0===e.length||e.length-t<e.highWaterMark)&&(r=!0,x("length less than watermark",r)),(e.ended||e.reading)&&(r=!1,x("reading or ended",r)),r&&(x("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1),r&&!e.reading&&(t=a(n,e)),r=t>0?h(t,e):null,_.isNull(r)&&(e.needReadable=!0,t=0),e.length-=t,0!==e.length||e.ended||(e.needReadable=!0),n!==t&&e.ended&&0===e.length&&l(this),_.isNull(r)||this.emit("data",r),r},i.prototype._read=function(t){this.emit("error",Error("not implemented"))},i.prototype.pipe=function(t,e){function r(t){x("onunpipe"),t===p&&o()}function i(){x("onend"),t.end()}function o(){x("cleanup"),t.removeListener("close",c),t.removeListener("finish",f),t.removeListener("drain",b),t.removeListener("error",s),t.removeListener("unpipe",r),p.removeListener("end",i),p.removeListener("end",o),p.removeListener("data",a),!h.awaitDrain||t._writableState&&!t._writableState.needDrain||b()}function a(e){x("ondata"),!1===t.write(e)&&(x("false write response, pause",p._readableState.awaitDrain),p._readableState.awaitDrain++,p.pause())}function s(e){x("onerror",e),d(),t.removeListener("error",s),0===y.listenerCount(t,"error")&&t.emit("error",e)}function c(){t.removeListener("finish",f),d()}function f(){x("onfinish"),t.removeListener("close",c),d()}function d(){x("unpipe"),p.unpipe(t)}var p=this,h=this._readableState;switch(h.pipesCount){case 0:h.pipes=t;break;case 1:h.pipes=[h.pipes,t];break;default:h.pipes.push(t)}h.pipesCount+=1,x("pipe count=%d opts=%j",h.pipesCount,e);var l=e&&!1===e.end||t===n.stdout||t===n.stderr?o:i;h.endEmitted?n.nextTick(l):p.once("end",l),t.on("unpipe",r);var b=u(p);return t.on("drain",b),p.on("data",a),t._events&&t._events.error?m(t._events.error)?t._events.error.unshift(s):t._events.error=[s,t._events.error]:t.on("error",s),t.once("close",c),t.once("finish",f),t.emit("pipe",p),h.flowing||(x("pipe resume"),p.resume()),t},i.prototype.unpipe=function(t){var e=this._readableState;if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes?this:(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this),this);if(!t){t=e.pipes;var n=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var r=0;n>r;r++)t[r].emit("unpipe",this);return this}t:{for(var r=e.pipes,n=0,i=r.length;i>n;n++)if(r[n]===t){r=n;break t}r=-1}return-1===r?this:(e.pipes.splice(r,1),--e.pipesCount,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this),this)},i.prototype.on=function(t,e){var r=g.prototype.on.call(this,t,e);if("data"===t&&!1!==this._readableState.flowing&&this.resume(),"readable"===t&&this.readable){var i=this._readableState;if(!i.readableListening)if(i.readableListening=!0,i.emittedReadable=!1,i.needReadable=!0,i.reading)i.length&&s(this,i);else{var o=this;n.nextTick(function(){x("readable nexttick read 0"),o.read(0)})}}return r},i.prototype.addListener=i.prototype.on,i.prototype.resume=function(){var t=this._readableState;return t.flowing||(x("resume"),t.flowing=!0,t.reading||(x("resume read 0"),this.read(0)),d(this,t)),this},i.prototype.pause=function(){return x("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(x("pause"),this._readableState.flowing=!1,this.emit("pause")),this},i.prototype.wrap=function(t){var e=this._readableState,n=!1,r=this;t.on("end",function(){if(x("wrapped end"),e.decoder&&!e.ended){var t=e.decoder.end();t&&t.length&&r.push(t)}r.push(null)}),t.on("data",function(i){x("wrapped data"),e.decoder&&(i=e.decoder.write(i)),i&&(e.objectMode||i.length)&&!r.push(i)&&(n=!0,t.pause())});for(var i in t)_.isFunction(t[i])&&_.isUndefined(this[i])&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));return b(["error","close","destroy","pause","resume"],function(e){t.on(e,r.emit.bind(r,e))}),r._read=function(e){x("wrapped _read",e),n&&(n=!1,t.resume())},r},i._fromList=h}).call(this,t(113))},{101:101,103:103,113:113,126:126,145:145,146:146,21:21,48:48,51:51,87:87}],129:[function(t,e,n){function r(t,e){this.afterTransform=function(t,n){var r;r=e._transformState,r.transforming=!1;var i=r.writecb;return i?(r.writechunk=null,r.writecb=null,s.isNullOrUndefined(n)||e.push(n),i&&i(t),r=e._readableState,r.reading=!1,(r.needReadable||r.length<r.highWaterMark)&&e._read(r.highWaterMark),r=void 0):r=e.emit("error",Error("no writecb in Transform class")),r},this.transforming=this.needTransform=!1,this.writechunk=this.writecb=null}function i(t){if(!(this instanceof i))return new i(t);a.call(this,t),this._transformState=new r(t,this);var e=this;this._readableState.needReadable=!0,this._readableState.sync=!1,this.once("prefinish",function(){s.isFunction(this._flush)?this._flush(function(t){o(e,t)}):o(e)})}function o(t,e){if(e)return t.emit("error",e);var n=t._transformState;if(t._writableState.length)throw Error("calling transform done when ws.length != 0");if(n.transforming)throw Error("calling transform done when still transforming");return t.push(null)}e.exports=i;var a=t(126),s=t(51);s.inherits=t(101),s.inherits(i,a),i.prototype.push=function(t,e){return this._transformState.needTransform=!1,a.prototype.push.call(this,t,e)},i.prototype._transform=function(t,e,n){throw Error("not implemented")},i.prototype._write=function(t,e,n){var r=this._transformState;r.writecb=n,r.writechunk=t,r.writeencoding=e,r.transforming||(t=this._readableState,(r.needTransform||t.needReadable||t.length<t.highWaterMark)&&this._read(t.highWaterMark))},i.prototype._read=function(t){t=this._transformState,s.isNull(t.writechunk)||!t.writecb||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))}},{101:101,126:126,51:51}],130:[function(t,e,n){(function(n){function r(t,e,n){this.chunk=t,this.encoding=e,this.callback=n}function i(e,n){var r=t(126);e=e||{};var i=e.highWaterMark,o=e.objectMode?16:16384;this.highWaterMark=i||0===i?i:o,this.objectMode=!!e.objectMode,n instanceof r&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=~~this.highWaterMark,this.finished=this.ended=this.ending=this.needDrain=!1,this.decodeStrings=!1!==e.decodeStrings,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){p(n,t)},this.writecb=null,this.writelen=0,this.buffer=[],this.pendingcb=0,this.errorEmitted=this.prefinished=!1}function o(e){var n=t(126);return this instanceof o||this instanceof n?(this._writableState=new i(e,this),this.writable=!0,void y.call(this)):new o(e)}function a(t,e,r){var i=Error("write after end");t.emit("error",i),n.nextTick(function(){r(i)})}function s(t,e,r,i){var o=!0;if(!(v.isBuffer(r)||v.isString(r)||v.isNullOrUndefined(r)||e.objectMode)){var a=new TypeError("Invalid non-string/buffer chunk");t.emit("error",a),n.nextTick(function(){i(a)}),o=!1}return o}function c(t,e,n,i,o){var a=i;!e.objectMode&&!1!==e.decodeStrings&&v.isString(n)&&(n=new m(n,a)),v.isBuffer(n)&&(i="buffer"),a=e.objectMode?1:n.length,e.length+=a;var s=e.length<e.highWaterMark;return s||(e.needDrain=!0),e.writing||e.corked?e.buffer.push(new r(n,i,o)):f(t,e,!1,a,n,i,o),s}function f(t,e,n,r,i,o,a){e.writelen=r,e.writecb=a,e.writing=!0,e.sync=!0,n?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function u(t,e,r,i,o){r?n.nextTick(function(){e.pendingcb--,o(i)}):(e.pendingcb--,o(i)),t._writableState.errorEmitted=!0,t.emit("error",i)}function d(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function p(t,e){var r=t._writableState,i=r.sync,o=r.writecb;if(d(r),e)u(t,r,i,e,o);else{var a=r.ending&&0===r.length&&!r.finished&&!r.writing;a||r.corked||r.bufferProcessing||!r.buffer.length||l(t,r),i?n.nextTick(function(){h(t,r,a,o)}):h(t,r,a,o)}}function h(t,e,n,r){!n&&0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain")),e.pendingcb--,r(),b(t,e)}function l(t,e){if(e.bufferProcessing=!0,t._writev&&1<e.buffer.length){for(var n=[],r=0;r<e.buffer.length;r++)n.push(e.buffer[r].callback);e.pendingcb++,f(t,e,!0,e.length,e.buffer,"",function(t){for(var r=0;r<n.length;r++)e.pendingcb--,n[r](t)}),e.buffer=[]}else{for(r=0;r<e.buffer.length;r++){var i=e.buffer[r],o=i.chunk;if(f(t,e,!1,e.objectMode?1:o.length,o,i.encoding,i.callback),e.writing){r++;break}}r<e.buffer.length?e.buffer=e.buffer.slice(r):e.buffer.length=0}e.bufferProcessing=!1}function b(t,e){var n=e.ending&&0===e.length&&!e.finished&&!e.writing;return n&&(0===e.pendingcb?(e.prefinished||(e.prefinished=!0,t.emit("prefinish")),e.finished=!0,t.emit("finish")):e.prefinished||(e.prefinished=!0,t.emit("prefinish"))),n}e.exports=o;var m=t(48).Buffer;o.WritableState=i;var v=t(51);v.inherits=t(101);var y=t(145);v.inherits(o,y),o.prototype.pipe=function(){this.emit("error",Error("Cannot pipe. Not readable."))},o.prototype.write=function(t,e,n){var r=this._writableState,i=!1;return v.isFunction(e)&&(n=e,e=null),v.isBuffer(t)?e="buffer":e||(e=r.defaultEncoding),v.isFunction(n)||(n=function(){}),r.ended?a(this,r,n):s(this,r,t,n)&&(r.pendingcb++,i=c(this,r,t,e,n)),i},o.prototype.cork=function(){this._writableState.corked++},o.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.finished||t.bufferProcessing||!t.buffer.length||l(this,t))},o.prototype._write=function(t,e,n){n(Error("not implemented"))},o.prototype._writev=null,o.prototype.end=function(t,e,r){var i=this._writableState;v.isFunction(t)?(r=t,e=t=null):v.isFunction(e)&&(r=e,e=null),v.isNullOrUndefined(t)||this.write(t,e),i.corked&&(i.corked=1,this.uncork()),i.ending||i.finished||(t=r,i.ending=!0,b(this,i),t&&(i.finished?n.nextTick(t):this.once("finish",t)),i.ended=!0)}}).call(this,t(113))},{101:101,113:113,126:126,145:145,48:48,51:51}],131:[function(t,e,n){e.exports=t(127)},{127:127}],132:[function(t,e,n){n=e.exports=t(128),n.Stream=t(145),n.Readable=n,n.Writable=t(130),n.Duplex=t(126),n.Transform=t(129),n.PassThrough=t(127)},{126:126,127:127,128:128,129:129,130:130,145:145}],133:[function(t,e,n){e.exports=t(129)},{129:129}],134:[function(t,e,n){e.exports=t(130)},{130:130}],135:[function(t,e,n){(function(t){function n(t,e){return t<<e|t>>>32-e}var r=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],i=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],o=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],a=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11],s=[0,1518500249,1859775393,2400959708,2840853838],c=[1352829926,1548603684,1836072691,2053994217,0];e.exports=function(e){var f=[1732584193,4023233417,2562383102,271733878,3285377520];"string"==typeof e&&(e=new t(e,"utf8"));for(var u=e,d=[],p=0,h=0;p<u.length;p++,h+=8)d[h>>>5]|=u[p]<<24-h%32;for(u=8*e.length,e=8*e.length,d[u>>>5]|=128<<24-u%32,d[(u+64>>>9<<4)+14]=16711935&(e<<8|e>>>24)|4278255360&(e<<24|e>>>8),e=0;e<d.length;e+=16){for(var u=f,p=d,h=e,l=0;16>l;l++){var b=h+l,m=p[b];p[b]=16711935&(m<<8|m>>>24)|4278255360&(m<<24|m>>>8)}var v,y,g,_,w,x,k,E;_=b=u[0],w=m=u[1],x=v=u[2],k=y=u[3],E=g=u[4];for(var S,l=0;80>l;l+=1)S=b+p[h+r[l]]|0,S=16>l?S+((m^v^y)+s[0]):32>l?S+((m&v|~m&y)+s[1]):48>l?S+(((m|~v)^y)+s[2]):64>l?S+((m&y|v&~y)+s[3]):S+((m^(v|~y))+s[4]),S|=0,S=n(S,o[l]),S=S+g|0,b=g,g=y,y=n(v,10),v=m,m=S,S=_+p[h+i[l]]|0,S=16>l?S+((w^(x|~k))+c[0]):32>l?S+((w&k|x&~k)+c[1]):48>l?S+(((w|~x)^k)+c[2]):64>l?S+((w&x|~w&k)+c[3]):S+((w^x^k)+c[4]),S|=0,S=n(S,a[l]),S=S+E|0,_=E,E=k,k=n(x,10),x=w,w=S;S=u[1]+v+k|0,u[1]=u[2]+y+E|0,u[2]=u[3]+g+_|0,u[3]=u[4]+b+w|0,u[4]=u[0]+m+x|0,u[0]=S}for(e=0;5>e;e++)d=f[e],f[e]=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8);for(d=[],e=0;e<32*f.length;e+=8)d.push(f[e>>>5]>>>24-e%32&255);return new t(d)}}).call(this,t(48).Buffer)},{48:48}],136:[function(t,e,n){(function(t){function n(e,n){this._block=new t(e),this._finalSize=n,this._blockSize=e,this._s=this._len=0}n.prototype.update=function(e,n){"string"==typeof e&&(e=new t(e,n||"utf8"));for(var r=this._len+=e.length,i=this._s||0,o=0,a=this._block;r>i;){for(var s=Math.min(e.length,o+this._blockSize-i%this._blockSize)-o,c=0;s>c;c++)a[i%this._blockSize+c]=e[c+o];
i+=s,o+=s,0===i%this._blockSize&&this._update(a)}return this._s=i,this},n.prototype.digest=function(t){var e=8*this._len;return this._block[this._len%this._blockSize]=128,this._block.fill(0,this._len%this._blockSize+1),e%(8*this._blockSize)>=8*this._finalSize&&(this._update(this._block),this._block.fill(0)),this._block.writeInt32BE(e,this._blockSize-4),e=this._update(this._block)||this._hash(),t?e.toString(t):e},n.prototype._update=function(){throw Error("_update must be implemented by subclass")},e.exports=n}).call(this,t(48).Buffer)},{48:48}],137:[function(t,e,n){n=e.exports=function(t){t=t.toLowerCase();var e=n[t];if(!e)throw Error(t+" is not supported (we accept pull requests)");return new e},n.sha=t(138),n.sha1=t(139),n.sha224=t(140),n.sha256=t(141),n.sha384=t(142),n.sha512=t(143)},{138:138,139:139,140:140,141:141,142:142,143:143}],138:[function(t,e,n){(function(n){function r(){this.init(),this._w=s,o.call(this,64,56)}var i=t(101),o=t(136),a=[1518500249,1859775393,-1894007588,-899497514],s=Array(80);i(r,o),r.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this},r.prototype._update=function(t){for(var e=this._w,n=0|this._a,r=0|this._b,i=0|this._c,o=0|this._d,s=0|this._e,c=0;16>c;++c)e[c]=t.readInt32BE(4*c);for(;80>c;++c)e[c]=e[c-3]^e[c-8]^e[c-14]^e[c-16];for(t=0;80>t;++t){var f,c=~~(t/20),u=n<<5|n>>>27;f=0===c?r&i|~r&o:2===c?r&i|r&o|i&o:r^i^o,c=u+f+s+e[t]+a[c]|0,s=o,o=i,i=r<<30|r>>>2,r=n,n=c}this._a=n+this._a|0,this._b=r+this._b|0,this._c=i+this._c|0,this._d=o+this._d|0,this._e=s+this._e|0},r.prototype._hash=function(){var t=new n(20);return t.writeInt32BE(0|this._a,0),t.writeInt32BE(0|this._b,4),t.writeInt32BE(0|this._c,8),t.writeInt32BE(0|this._d,12),t.writeInt32BE(0|this._e,16),t},e.exports=r}).call(this,t(48).Buffer)},{101:101,136:136,48:48}],139:[function(t,e,n){(function(n){function r(){this.init(),this._w=s,o.call(this,64,56)}var i=t(101),o=t(136),a=[1518500249,1859775393,-1894007588,-899497514],s=Array(80);i(r,o),r.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this},r.prototype._update=function(t){for(var e=this._w,n=0|this._a,r=0|this._b,i=0|this._c,o=0|this._d,s=0|this._e,c=0;16>c;++c)e[c]=t.readInt32BE(4*c);for(;80>c;++c)t=e[c-3]^e[c-8]^e[c-14]^e[c-16],e[c]=t<<1|t>>>31;for(c=0;80>c;++c){t=~~(c/20);var f,u=n<<5|n>>>27;f=0===t?r&i|~r&o:2===t?r&i|r&o|i&o:r^i^o,t=u+f+s+e[c]+a[t]|0,s=o,o=i,i=r<<30|r>>>2,r=n,n=t}this._a=n+this._a|0,this._b=r+this._b|0,this._c=i+this._c|0,this._d=o+this._d|0,this._e=s+this._e|0},r.prototype._hash=function(){var t=new n(20);return t.writeInt32BE(0|this._a,0),t.writeInt32BE(0|this._b,4),t.writeInt32BE(0|this._c,8),t.writeInt32BE(0|this._d,12),t.writeInt32BE(0|this._e,16),t},e.exports=r}).call(this,t(48).Buffer)},{101:101,136:136,48:48}],140:[function(t,e,n){(function(n){function r(){this.init(),this._w=s,a.call(this,64,56)}var i=t(101),o=t(141),a=t(136),s=Array(64);i(r,o),r.prototype.init=function(){return this._a=3238371032,this._b=914150663,this._c=812702999,this._d=4144912697,this._e=4290775857,this._f=1750603025,this._g=1694076839,this._h=3204075428,this},r.prototype._hash=function(){var t=new n(28);return t.writeInt32BE(this._a,0),t.writeInt32BE(this._b,4),t.writeInt32BE(this._c,8),t.writeInt32BE(this._d,12),t.writeInt32BE(this._e,16),t.writeInt32BE(this._f,20),t.writeInt32BE(this._g,24),t},e.exports=r}).call(this,t(48).Buffer)},{101:101,136:136,141:141,48:48}],141:[function(t,e,n){(function(n){function r(){this.init(),this._w=s,o.call(this,64,56)}var i=t(101),o=t(136),a=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],s=Array(64);i(r,o),r.prototype.init=function(){return this._a=1779033703,this._b=3144134277,this._c=1013904242,this._d=2773480762,this._e=1359893119,this._f=2600822924,this._g=528734635,this._h=1541459225,this},r.prototype._update=function(t){for(var e=this._w,n=0|this._a,r=0|this._b,i=0|this._c,o=0|this._d,s=0|this._e,c=0|this._f,f=0|this._g,u=0|this._h,d=0;16>d;++d)e[d]=t.readInt32BE(4*d);for(;64>d;++d){t=e[d-2];var p=e[d-15];e[d]=((t>>>17|t<<15)^(t>>>19|t<<13)^t>>>10)+e[d-7]+((p>>>7|p<<25)^(p>>>18|p<<14)^p>>>3)+e[d-16]|0}for(d=0;64>d;++d)t=u+((s>>>6|s<<26)^(s>>>11|s<<21)^(s>>>25|s<<7))+(f^s&(c^f))+a[d]+e[d]|0,p=((n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10))+(n&r|i&(n|r))|0,u=f,f=c,c=s,s=o+t|0,o=i,i=r,r=n,n=t+p|0;this._a=n+this._a|0,this._b=r+this._b|0,this._c=i+this._c|0,this._d=o+this._d|0,this._e=s+this._e|0,this._f=c+this._f|0,this._g=f+this._g|0,this._h=u+this._h|0},r.prototype._hash=function(){var t=new n(32);return t.writeInt32BE(this._a,0),t.writeInt32BE(this._b,4),t.writeInt32BE(this._c,8),t.writeInt32BE(this._d,12),t.writeInt32BE(this._e,16),t.writeInt32BE(this._f,20),t.writeInt32BE(this._g,24),t.writeInt32BE(this._h,28),t},e.exports=r}).call(this,t(48).Buffer)},{101:101,136:136,48:48}],142:[function(t,e,n){(function(n){function r(){this.init(),this._w=s,a.call(this,128,112)}var i=t(101),o=t(143),a=t(136),s=Array(160);i(r,o),r.prototype.init=function(){return this._ah=3418070365,this._bh=1654270250,this._ch=2438529370,this._dh=355462360,this._eh=1731405415,this._fh=2394180231,this._gh=3675008525,this._hh=1203062813,this._al=3238371032,this._bl=914150663,this._cl=812702999,this._dl=4144912697,this._el=4290775857,this._fl=1750603025,this._gl=1694076839,this._hl=3204075428,this},r.prototype._hash=function(){function t(t,n,r){e.writeInt32BE(t,r),e.writeInt32BE(n,r+4)}var e=new n(48);return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),e},e.exports=r}).call(this,t(48).Buffer)},{101:101,136:136,143:143,48:48}],143:[function(t,e,n){(function(n){function r(){this.init(),this._w=c,a.call(this,128,112)}function i(t,e){return e>>>0>t>>>0?1:0}var o=t(101),a=t(136),s=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],c=Array(160);o(r,a),r.prototype.init=function(){return this._ah=1779033703,this._bh=3144134277,this._ch=1013904242,this._dh=2773480762,this._eh=1359893119,this._fh=2600822924,this._gh=528734635,this._hh=1541459225,this._al=4089235720,this._bl=2227873595,this._cl=4271175723,this._dl=1595750129,this._el=2917565137,this._fl=725511199,this._gl=4215389547,this._hl=327033209,this},r.prototype._update=function(t){for(var e=this._w,n=0|this._ah,r=0|this._bh,o=0|this._ch,a=0|this._dh,c=0|this._eh,f=0|this._fh,u=0|this._gh,d=0|this._hh,p=0|this._al,h=0|this._bl,l=0|this._cl,b=0|this._dl,m=0|this._el,v=0|this._fl,y=0|this._gl,g=0|this._hl,_=0;32>_;_+=2)e[_]=t.readInt32BE(4*_),e[_+1]=t.readInt32BE(4*_+4);for(;160>_;_+=2){t=e[_-30];var w=e[_-30+1],x=(t>>>1|w<<31)^(t>>>8|w<<24)^t>>>7,k=(w>>>1|t<<31)^(w>>>8|t<<24)^(w>>>7|t<<25);t=e[_-4];var w=e[_-4+1],E=(t>>>19|w<<13)^(w>>>29|t<<3)^t>>>6,w=(w>>>19|t<<13)^(t>>>29|w<<3)^(w>>>6|t<<26),S=e[_-32],M=e[_-32+1];t=k+e[_-14+1]|0,x=x+e[_-14]+i(t,k)|0,t=t+w|0,x=x+E+i(t,w)|0,t=t+M|0,x=x+S+i(t,M)|0,e[_]=x,e[_+1]=t}for(_=0;160>_;_+=2){x=e[_],t=e[_+1];var w=n&r|o&(n|r),A=p&h|l&(p|h),S=(n>>>28|p<<4)^(p>>>2|n<<30)^(p>>>7|n<<25),M=(p>>>28|n<<4)^(n>>>2|p<<30)^(n>>>7|p<<25),T=s[_],j=s[_+1],I=u^c&(f^u),R=y^m&(v^y),E=g+((m>>>14|c<<18)^(m>>>18|c<<14)^(c>>>9|m<<23))|0,k=d+((c>>>14|m<<18)^(c>>>18|m<<14)^(m>>>9|c<<23))+i(E,g)|0,E=E+R|0,k=k+I+i(E,R)|0,E=E+j|0,k=k+T+i(E,j)|0,E=E+t|0,k=k+x+i(E,t)|0;t=M+A|0,x=S+w+i(t,M)|0,d=u,g=y,u=f,y=v,f=c,v=m,m=b+E|0,c=a+k+i(m,b)|0,a=o,b=l,o=r,l=h,r=n,h=p,p=E+t|0,n=k+x+i(p,E)|0}this._al=this._al+p|0,this._bl=this._bl+h|0,this._cl=this._cl+l|0,this._dl=this._dl+b|0,this._el=this._el+m|0,this._fl=this._fl+v|0,this._gl=this._gl+y|0,this._hl=this._hl+g|0,this._ah=this._ah+n+i(this._al,p)|0,this._bh=this._bh+r+i(this._bl,h)|0,this._ch=this._ch+o+i(this._cl,l)|0,this._dh=this._dh+a+i(this._dl,b)|0,this._eh=this._eh+c+i(this._el,m)|0,this._fh=this._fh+f+i(this._fl,v)|0,this._gh=this._gh+u+i(this._gl,y)|0,this._hh=this._hh+d+i(this._hl,g)|0},r.prototype._hash=function(){function t(t,n,r){e.writeInt32BE(t,r),e.writeInt32BE(n,r+4)}var e=new n(64);return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),t(this._gh,this._gl,48),t(this._hh,this._hl,56),e},e.exports=r}).call(this,t(48).Buffer)},{101:101,136:136,48:48}],144:[function(t,e,n){!function(t){if("object"==typeof n)e.exports=t();else{var r;try{r=window}catch(i){r=self}r.SparkMD5=t()}}(function(t){function e(t,e,n,r,i,o){return e=h(h(e,t),h(r,o)),h(e<<i|e>>>32-i,n)}function n(t,n,r,i,o,a,s){return e(n&r|~n&i,t,n,o,a,s)}function r(t,n,r,i,o,a,s){return e(n&i|r&~i,t,n,o,a,s)}function i(t,n,r,i,o,a,s){return e(r^(n|~i),t,n,o,a,s)}function o(t,o){var a=t[0],s=t[1],c=t[2],f=t[3],a=n(a,s,c,f,o[0],7,-680876936),f=n(f,a,s,c,o[1],12,-389564586),c=n(c,f,a,s,o[2],17,606105819),s=n(s,c,f,a,o[3],22,-1044525330),a=n(a,s,c,f,o[4],7,-176418897),f=n(f,a,s,c,o[5],12,1200080426),c=n(c,f,a,s,o[6],17,-1473231341),s=n(s,c,f,a,o[7],22,-45705983),a=n(a,s,c,f,o[8],7,1770035416),f=n(f,a,s,c,o[9],12,-1958414417),c=n(c,f,a,s,o[10],17,-42063),s=n(s,c,f,a,o[11],22,-1990404162),a=n(a,s,c,f,o[12],7,1804603682),f=n(f,a,s,c,o[13],12,-40341101),c=n(c,f,a,s,o[14],17,-1502002290),s=n(s,c,f,a,o[15],22,1236535329),a=r(a,s,c,f,o[1],5,-165796510),f=r(f,a,s,c,o[6],9,-1069501632),c=r(c,f,a,s,o[11],14,643717713),s=r(s,c,f,a,o[0],20,-373897302),a=r(a,s,c,f,o[5],5,-701558691),f=r(f,a,s,c,o[10],9,38016083),c=r(c,f,a,s,o[15],14,-660478335),s=r(s,c,f,a,o[4],20,-405537848),a=r(a,s,c,f,o[9],5,568446438),f=r(f,a,s,c,o[14],9,-1019803690),c=r(c,f,a,s,o[3],14,-187363961),s=r(s,c,f,a,o[8],20,1163531501),a=r(a,s,c,f,o[13],5,-1444681467),f=r(f,a,s,c,o[2],9,-51403784),c=r(c,f,a,s,o[7],14,1735328473),s=r(s,c,f,a,o[12],20,-1926607734),a=e(s^c^f,a,s,o[5],4,-378558),f=e(a^s^c,f,a,o[8],11,-2022574463),c=e(f^a^s,c,f,o[11],16,1839030562),s=e(c^f^a,s,c,o[14],23,-35309556),a=e(s^c^f,a,s,o[1],4,-1530992060),f=e(a^s^c,f,a,o[4],11,1272893353),c=e(f^a^s,c,f,o[7],16,-155497632),s=e(c^f^a,s,c,o[10],23,-1094730640),a=e(s^c^f,a,s,o[13],4,681279174),f=e(a^s^c,f,a,o[0],11,-358537222),c=e(f^a^s,c,f,o[3],16,-722521979),s=e(c^f^a,s,c,o[6],23,76029189),a=e(s^c^f,a,s,o[9],4,-640364487),f=e(a^s^c,f,a,o[12],11,-421815835),c=e(f^a^s,c,f,o[15],16,530742520),s=e(c^f^a,s,c,o[2],23,-995338651),a=i(a,s,c,f,o[0],6,-198630844),f=i(f,a,s,c,o[7],10,1126891415),c=i(c,f,a,s,o[14],15,-1416354905),s=i(s,c,f,a,o[5],21,-57434055),a=i(a,s,c,f,o[12],6,1700485571),f=i(f,a,s,c,o[3],10,-1894986606),c=i(c,f,a,s,o[10],15,-1051523),s=i(s,c,f,a,o[1],21,-2054922799),a=i(a,s,c,f,o[8],6,1873313359),f=i(f,a,s,c,o[15],10,-30611744),c=i(c,f,a,s,o[6],15,-1560198380),s=i(s,c,f,a,o[13],21,1309151649),a=i(a,s,c,f,o[4],6,-145523070),f=i(f,a,s,c,o[11],10,-1120210379),c=i(c,f,a,s,o[2],15,718787259),s=i(s,c,f,a,o[9],21,-343485551);t[0]=h(a,t[0]),t[1]=h(s,t[1]),t[2]=h(c,t[2]),t[3]=h(f,t[3])}function a(t){var e,n=[];for(e=0;64>e;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}function s(t){var e,n=[];for(e=0;64>e;e+=4)n[e>>2]=t[e]+(t[e+1]<<8)+(t[e+2]<<16)+(t[e+3]<<24);return n}function c(t){var e,n,r,i=t.length,s=[1732584193,-271733879,-1732584194,271733878];for(e=64;i>=e;e+=64)o(s,a(t.substring(e-64,e)));for(t=t.substring(e-64),n=t.length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e=0;n>e;e+=1)r[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(r[e>>2]|=128<<(e%4<<3),e>55)for(o(s,r),e=0;16>e;e+=1)r[e]=0;return i=(8*i).toString(16).match(/(.*?)(.{0,8})$/),t=parseInt(i[2],16),i=parseInt(i[1],16)||0,r[14]=t,r[15]=i,o(s,r),s}function f(t){var e;for(e=0;e<t.length;e+=1){var n,r=e,i=t[e],o="";for(n=0;4>n;n+=1)o+=l[i>>8*n+4&15]+l[i>>8*n&15];t[r]=o}return t.join("")}function u(t){return/[\u0080-\uFFFF]/.test(t)&&(t=unescape(encodeURIComponent(t))),t}function d(t){var e,n=[],r=t.length;for(e=0;r-1>e;e+=2)n.push(parseInt(t.substr(e,2),16));return String.fromCharCode.apply(String,n)}function p(){this.reset()}var h=function(t,e){return t+e&4294967295},l="0123456789abcdef".split("");return"5d41402abc4b2a76b9719d911017c592"!==f(c("hello"))&&(h=function(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}),"undefined"==typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function e(t,e){return t=0|t||0,0>t?Math.max(t+e,0):Math.min(t,e)}ArrayBuffer.prototype.slice=function(n,r){var i,o=this.byteLength,a=e(n,o),s=o;return r!==t&&(s=e(r,o)),a>s?new ArrayBuffer(0):(i=s-a,o=new ArrayBuffer(i),s=new Uint8Array(o),a=new Uint8Array(this,a,i),s.set(a),o)}}(),p.prototype.append=function(t){return this.appendBinary(u(t)),this},p.prototype.appendBinary=function(t){this._buff+=t,this._length+=t.length,t=this._buff.length;var e;for(e=64;t>=e;e+=64)o(this._hash,a(this._buff.substring(e-64,e)));return this._buff=this._buff.substring(e-64),this},p.prototype.end=function(t){var e,n=this._buff,r=n.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;r>e;e+=1)i[e>>2]|=n.charCodeAt(e)<<(e%4<<3);return this._finish(i,r),n=f(this._hash),t&&(n=d(n)),this.reset(),n},p.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},p.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash}},p.prototype.setState=function(t){return this._buff=t.buff,this._length=t.length,this._hash=t.hash,this},p.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},p.prototype._finish=function(t,e){var n,r=e;if(t[r>>2]|=128<<(r%4<<3),r>55)for(o(this._hash,t),r=0;16>r;r+=1)t[r]=0;n=8*this._length,n=n.toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(n[2],16),n=parseInt(n[1],16)||0,t[14]=r,t[15]=n,o(this._hash,t)},p.hash=function(t,e){return p.hashBinary(u(t),e)},p.hashBinary=function(t,e){var n=c(t),n=f(n);return e?d(n):n},p.ArrayBuffer=function(){this.reset()},p.ArrayBuffer.prototype.append=function(t){var e=this._buff.buffer,n=new Uint8Array(e.byteLength+t.byteLength);for(n.set(new Uint8Array(e)),n.set(new Uint8Array(t),e.byteLength),e=n.length,this._length+=t.byteLength,t=64;e>=t;t+=64)o(this._hash,s(n.subarray(t-64,t)));return this._buff=e>t-64?new Uint8Array(n.buffer.slice(t-64)):new Uint8Array(0),this},p.ArrayBuffer.prototype.end=function(t){var e,n=this._buff,r=n.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;r>e;e+=1)i[e>>2]|=n[e]<<(e%4<<3);return this._finish(i,r),n=f(this._hash),t&&(n=d(n)),this.reset(),n},p.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},p.ArrayBuffer.prototype.getState=function(){var t=p.prototype.getState.call(this);return t.buff=String.fromCharCode.apply(null,new Uint8Array(t.buff)),t},p.ArrayBuffer.prototype.setState=function(t){var e,n=t.buff,r=n.length,i=new ArrayBuffer(r),i=new Uint8Array(i);for(e=0;r>e;e+=1)i[e]=n.charCodeAt(e);return t.buff=i,p.prototype.setState.call(this,t)},p.ArrayBuffer.prototype.destroy=p.prototype.destroy,p.ArrayBuffer.prototype._finish=p.prototype._finish,p.ArrayBuffer.hash=function(t,e){var n,r,i,a=new Uint8Array(t),c=a.length,u=[1732584193,-271733879,-1732584194,271733878];for(n=64;c>=n;n+=64)o(u,s(a.subarray(n-64,n)));for(a=c>n-64?a.subarray(n-64):new Uint8Array(0),r=a.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],n=0;r>n;n+=1)i[n>>2]|=a[n]<<(n%4<<3);if(i[n>>2]|=128<<(n%4<<3),n>55)for(o(u,i),n=0;16>n;n+=1)i[n]=0;return c=(8*c).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(c[2],16),c=parseInt(c[1],16)||0,i[14]=a,i[15]=c,o(u,i),u=f(u),e?d(u):u},p})},{}],145:[function(t,e,n){function r(){i.call(this)}e.exports=r;var i=t(87).EventEmitter;t(101)(r,i),r.Readable=t(132),r.Writable=t(134),r.Duplex=t(125),r.Transform=t(133),r.PassThrough=t(131),r.Stream=r,r.prototype.pipe=function(t,e){function n(e){t.writable&&!1===t.write(e)&&f.pause&&f.pause()}function r(){f.readable&&f.resume&&f.resume()}function o(){u||(u=!0,t.end())}function a(){u||(u=!0,"function"==typeof t.destroy&&t.destroy())}function s(t){if(c(),0===i.listenerCount(this,"error"))throw t}function c(){f.removeListener("data",n),t.removeListener("drain",r),f.removeListener("end",o),f.removeListener("close",a),f.removeListener("error",s),t.removeListener("error",s),f.removeListener("end",c),f.removeListener("close",c),t.removeListener("close",c)}var f=this;f.on("data",n),t.on("drain",r),t._isStdio||e&&!1===e.end||(f.on("end",o),f.on("close",a));var u=!1;return f.on("error",s),t.on("error",s),f.on("end",c),f.on("close",c),t.on("close",c),t.emit("pipe",f),t}},{101:101,125:125,131:131,132:132,133:133,134:134,87:87}],146:[function(t,e,n){function r(t){return t.toString(this.encoding)}function i(t){this.charLength=(this.charReceived=t.length%2)?2:0}function o(t){this.charLength=(this.charReceived=t.length%3)?3:0}var a=t(48).Buffer,s=a.isEncoding||function(t){switch(t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};t=n.StringDecoder=function(t){if(this.encoding=(t||"utf8").toLowerCase().replace(/[-_]/,""),t&&!s(t))throw Error("Unknown encoding: "+t);switch(this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=i;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=o;break;default:return void(this.write=r)}this.charBuffer=new a(6),this.charLength=this.charReceived=0},t.prototype.write=function(t){for(var e="";this.charLength;){if(e=t.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:t.length,t.copy(this.charBuffer,this.charReceived,0,e),this.charReceived+=e,this.charReceived<this.charLength)return"";t=t.slice(e,t.length);var e=this.charBuffer.slice(0,this.charLength).toString(this.encoding),n=e.charCodeAt(e.length-1);if(!(n>=55296&&56319>=n)){if(this.charReceived=this.charLength=0,0===t.length)return e;break}this.charLength+=this.surrogateSize,e=""}this.detectIncompleteChar(t);var r=t.length;return this.charLength&&(t.copy(this.charBuffer,0,t.length-this.charReceived,r),r-=this.charReceived),e+=t.toString(this.encoding,0,r),r=e.length-1,n=e.charCodeAt(r),n>=55296&&56319>=n?(n=this.surrogateSize,this.charLength+=n,this.charReceived+=n,this.charBuffer.copy(this.charBuffer,n,0,n),t.copy(this.charBuffer,0,0,n),e.substring(0,r)):e},t.prototype.detectIncompleteChar=function(t){for(var e=3<=t.length?3:t.length;e>0;e--){var n=t[t.length-e];if(1==e&&6==n>>5){this.charLength=2;break}if(2>=e&&14==n>>4){this.charLength=3;break}if(3>=e&&30==n>>3){this.charLength=4;break}}this.charReceived=e},t.prototype.end=function(t){var e="";return t&&t.length&&(e=this.write(t)),this.charReceived&&(t=this.encoding,e+=this.charBuffer.slice(0,this.charReceived).toString(t)),e}},{48:48}],147:[function(t,e,n){(function(){function t(t){return function(e,n,r,i){n=_(n,i,4);var o=!A(e)&&g.keys(e),a=(o||e).length,s=t>0?0:a-1;3>arguments.length&&(r=e[o?o[s]:s],s+=t);for(var c=n,f=r;s>=0&&a>s;s+=t)var u=o?o[s]:s,f=c(f,e[u],u,e);return f}}function r(t){return function(e,n,r){n=w(n,r),r=M(e);for(var i=t>0?0:r-1;i>=0&&r>i;i+=t)if(n(e[i],i,e))return i;return-1}}function i(t,e,n){return function(r,i,o){var a=0,s=M(r);if("number"==typeof o)t>0?a=o>=0?o:Math.max(o+s,a):s=o>=0?Math.min(o+1,s):o+s+1;else if(n&&o&&s)return o=n(r,i),r[o]===i?o:-1;if(i!==i)return o=e(d.call(r,a,s),g.isNaN),o>=0?o+a:-1;for(o=t>0?a:s-1;o>=0&&s>o;o+=t)if(r[o]===i)return o;return-1}}function o(t,e){var n=O.length,r=t.constructor,r=g.isFunction(r)&&r.prototype||f,i="constructor";for(g.has(t,i)&&!g.contains(e,i)&&e.push(i);n--;)i=O[n],i in t&&t[i]!==r[i]&&!g.contains(e,i)&&e.push(i)}var a=this,s=a._,c=Array.prototype,f=Object.prototype,u=c.push,d=c.slice,p=f.toString,h=f.hasOwnProperty,l=Array.isArray,b=Object.keys,m=Function.prototype.bind,v=Object.create,y=function(){},g=function(t){return t instanceof g?t:this instanceof g?void(this._wrapped=t):new g(t)};"undefined"!=typeof n?("undefined"!=typeof e&&e.exports&&(n=e.exports=g),n._=g):a._=g,g.VERSION="1.8.3";var _=function(t,e,n){if(void 0===e)return t;switch(null==n?3:n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,i){return t.call(e,n,r,i)};case 4:return function(n,r,i,o){return t.call(e,n,r,i,o)}}return function(){return t.apply(e,arguments)}},w=function(t,e,n){return null==t?g.identity:g.isFunction(t)?_(t,e,n):g.isObject(t)?g.matcher(t):g.property(t)};g.iteratee=function(t,e){return w(t,e,1/0)};var x=function(t,e){return function(n){var r=arguments.length;if(2>r||null==n)return n;for(var i=1;r>i;i++)for(var o=arguments[i],a=t(o),s=a.length,c=0;s>c;c++){var f=a[c];e&&void 0!==n[f]||(n[f]=o[f])}return n}},k=function(t){return g.isObject(t)?v?v(t):(y.prototype=t,t=new y,y.prototype=null,t):{}},E=function(t){return function(e){return null==e?void 0:e[t]}},S=Math.pow(2,53)-1,M=E("length"),A=function(t){return t=M(t),"number"==typeof t&&t>=0&&S>=t};g.each=g.forEach=function(t,e,n){e=_(e,n);var r;if(A(t))for(n=0,r=t.length;r>n;n++)e(t[n],n,t);else{var i=g.keys(t);for(n=0,r=i.length;r>n;n++)e(t[i[n]],i[n],t)}return t},g.map=g.collect=function(t,e,n){e=w(e,n),n=!A(t)&&g.keys(t);for(var r=(n||t).length,i=Array(r),o=0;r>o;o++){var a=n?n[o]:o;i[o]=e(t[a],a,t)}return i},g.reduce=g.foldl=g.inject=t(1),g.reduceRight=g.foldr=t(-1),g.find=g.detect=function(t,e,n){return e=A(t)?g.findIndex(t,e,n):g.findKey(t,e,n),void 0!==e&&-1!==e?t[e]:void 0},g.filter=g.select=function(t,e,n){var r=[];return e=w(e,n),g.each(t,function(t,n,i){e(t,n,i)&&r.push(t)}),r},g.reject=function(t,e,n){return g.filter(t,g.negate(w(e)),n)},g.every=g.all=function(t,e,n){e=w(e,n),n=!A(t)&&g.keys(t);for(var r=(n||t).length,i=0;r>i;i++){var o=n?n[i]:i;if(!e(t[o],o,t))return!1}return!0},g.some=g.any=function(t,e,n){e=w(e,n),n=!A(t)&&g.keys(t);for(var r=(n||t).length,i=0;r>i;i++){var o=n?n[i]:i;if(e(t[o],o,t))return!0}return!1},g.contains=g.includes=g.include=function(t,e,n,r){return A(t)||(t=g.values(t)),("number"!=typeof n||r)&&(n=0),0<=g.indexOf(t,e,n)},g.invoke=function(t,e){var n=d.call(arguments,2),r=g.isFunction(e);return g.map(t,function(t){var i=r?e:t[e];return null==i?i:i.apply(t,n)})},g.pluck=function(t,e){return g.map(t,g.property(e))},g.where=function(t,e){return g.filter(t,g.matcher(e))},g.findWhere=function(t,e){return g.find(t,g.matcher(e))},g.max=function(t,e,n){var r,i=-(1/0),o=-(1/0);if(null==e&&null!=t){t=A(t)?t:g.values(t);for(var a=0,s=t.length;s>a;a++)n=t[a],n>i&&(i=n)}else e=w(e,n),g.each(t,function(t,n,a){r=e(t,n,a),(r>o||-(1/0)===r&&-(1/0)===i)&&(i=t,o=r)});return i},g.min=function(t,e,n){var r,i=1/0,o=1/0;if(null==e&&null!=t){t=A(t)?t:g.values(t);for(var a=0,s=t.length;s>a;a++)n=t[a],i>n&&(i=n)}else e=w(e,n),g.each(t,function(t,n,a){r=e(t,n,a),(o>r||1/0===r&&1/0===i)&&(i=t,o=r)});return i},g.shuffle=function(t){t=A(t)?t:g.values(t);for(var e,n=t.length,r=Array(n),i=0;n>i;i++)e=g.random(0,i),e!==i&&(r[i]=r[e]),r[e]=t[i];return r},g.sample=function(t,e,n){return null==e||n?(A(t)||(t=g.values(t)),t[g.random(t.length-1)]):g.shuffle(t).slice(0,Math.max(0,e))},g.sortBy=function(t,e,n){return e=w(e,n),g.pluck(g.map(t,function(t,n,r){return{value:t,index:n,criteria:e(t,n,r)}}).sort(function(t,e){var n=t.criteria,r=e.criteria;if(n!==r){if(n>r||void 0===n)return 1;if(r>n||void 0===r)return-1}return t.index-e.index}),"value")};var T=function(t){return function(e,n,r){var i={};return n=w(n,r),g.each(e,function(r,o){var a=n(r,o,e);t(i,r,a)}),i}};g.groupBy=T(function(t,e,n){g.has(t,n)?t[n].push(e):t[n]=[e]}),g.indexBy=T(function(t,e,n){t[n]=e}),g.countBy=T(function(t,e,n){g.has(t,n)?t[n]++:t[n]=1}),g.toArray=function(t){return t?g.isArray(t)?d.call(t):A(t)?g.map(t,g.identity):g.values(t):[]},g.size=function(t){return null==t?0:A(t)?t.length:g.keys(t).length},g.partition=function(t,e,n){e=w(e,n);var r=[],i=[];return g.each(t,function(t,n,o){(e(t,n,o)?r:i).push(t)}),[r,i]},g.first=g.head=g.take=function(t,e,n){return null==t?void 0:null==e||n?t[0]:g.initial(t,t.length-e)},g.initial=function(t,e,n){return d.call(t,0,Math.max(0,t.length-(null==e||n?1:e)))},g.last=function(t,e,n){return null==t?void 0:null==e||n?t[t.length-1]:g.rest(t,Math.max(0,t.length-e))},g.rest=g.tail=g.drop=function(t,e,n){return d.call(t,null==e||n?1:e)},g.compact=function(t){return g.filter(t,g.identity)};var j=function(t,e,n,r){var i=[],o=0;r=r||0;for(var a=M(t);a>r;r++){var s=t[r];if(A(s)&&(g.isArray(s)||g.isArguments(s))){e||(s=j(s,e,n));var c=0,f=s.length;for(i.length+=f;f>c;)i[o++]=s[c++]}else n||(i[o++]=s)}return i};g.flatten=function(t,e){return j(t,e,!1)},g.without=function(t){return g.difference(t,d.call(arguments,1))},g.uniq=g.unique=function(t,e,n,r){g.isBoolean(e)||(r=n,n=e,e=!1),null!=n&&(n=w(n,r)),r=[];for(var i=[],o=0,a=M(t);a>o;o++){var s=t[o],c=n?n(s,o,t):s;e?(o&&i===c||r.push(s),i=c):n?g.contains(i,c)||(i.push(c),r.push(s)):g.contains(r,s)||r.push(s)}return r},g.union=function(){return g.uniq(j(arguments,!0,!0))},g.intersection=function(t){for(var e=[],n=arguments.length,r=0,i=M(t);i>r;r++){var o=t[r];if(!g.contains(e,o)){for(var a=1;n>a&&g.contains(arguments[a],o);a++);a===n&&e.push(o)}}return e},g.difference=function(t){var e=j(arguments,!0,!0,1);return g.filter(t,function(t){return!g.contains(e,t)})},g.zip=function(){return g.unzip(arguments)},g.unzip=function(t){for(var e=t&&g.max(t,M).length||0,n=Array(e),r=0;e>r;r++)n[r]=g.pluck(t,r);return n},g.object=function(t,e){for(var n={},r=0,i=M(t);i>r;r++)e?n[t[r]]=e[r]:n[t[r][0]]=t[r][1];return n},g.findIndex=r(1),g.findLastIndex=r(-1),g.sortedIndex=function(t,e,n,r){n=w(n,r,1),e=n(e),r=0;for(var i=M(t);i>r;){var o=Math.floor((r+i)/2);n(t[o])<e?r=o+1:i=o}return r},g.indexOf=i(1,g.findIndex,g.sortedIndex),g.lastIndexOf=i(-1,g.findLastIndex),g.range=function(t,e,n){null==e&&(e=t||0,t=0),n=n||1,e=Math.max(Math.ceil((e-t)/n),0);for(var r=Array(e),i=0;e>i;i++,t+=n)r[i]=t;return r};var I=function(t,e,n,r,i){return r instanceof e?(e=k(t.prototype),t=t.apply(e,i),g.isObject(t)?t:e):t.apply(n,i)};g.bind=function(t,e){if(m&&t.bind===m)return m.apply(t,d.call(arguments,1));if(!g.isFunction(t))throw new TypeError("Bind must be called on a function");var n=d.call(arguments,2),r=function(){return I(t,r,e,this,n.concat(d.call(arguments)))};return r},g.partial=function(t){var e=d.call(arguments,1),n=function(){for(var r=0,i=e.length,o=Array(i),a=0;i>a;a++)o[a]=e[a]===g?arguments[r++]:e[a];for(;r<arguments.length;)o.push(arguments[r++]);return I(t,n,this,this,o)};return n},g.bindAll=function(t){var e,n,r=arguments.length;if(1>=r)throw Error("bindAll must be passed function names");for(e=1;r>e;e++)n=arguments[e],t[n]=g.bind(t[n],t);return t},g.memoize=function(t,e){var n=function(r){var i=n.cache,o=""+(e?e.apply(this,arguments):r);return g.has(i,o)||(i[o]=t.apply(this,arguments)),i[o]};return n.cache={},n},g.delay=function(t,e){var n=d.call(arguments,2);return setTimeout(function(){return t.apply(null,n)},e)},g.defer=g.partial(g.delay,g,1),g.throttle=function(t,e,n){var r,i,o,a=null,s=0;n||(n={});var c=function(){s=!1===n.leading?0:g.now(),a=null,o=t.apply(r,i),a||(r=i=null)};return function(){var f=g.now();s||!1!==n.leading||(s=f);var u=e-(f-s);return r=this,i=arguments,0>=u||u>e?(a&&(clearTimeout(a),a=null),s=f,o=t.apply(r,i),a||(r=i=null)):a||!1===n.trailing||(a=setTimeout(c,u)),o}},g.debounce=function(t,e,n){var r,i,o,a,s,c=function(){var f=g.now()-a;e>f&&f>=0?r=setTimeout(c,e-f):(r=null,n||(s=t.apply(o,i),r||(o=i=null)))};return function(){o=this,i=arguments,a=g.now();var f=n&&!r;return r||(r=setTimeout(c,e)),f&&(s=t.apply(o,i),o=i=null),s}},g.wrap=function(t,e){return g.partial(e,t)},g.negate=function(t){return function(){return!t.apply(this,arguments)}},g.compose=function(){var t=arguments,e=t.length-1;return function(){for(var n=e,r=t[e].apply(this,arguments);n--;)r=t[n].call(this,r);return r}},g.after=function(t,e){return function(){return 1>--t?e.apply(this,arguments):void 0}},g.before=function(t,e){var n;return function(){return 0<--t&&(n=e.apply(this,arguments)),1>=t&&(e=null),n}},g.once=g.partial(g.before,2);var R=!{toString:null}.propertyIsEnumerable("toString"),O="valueOf isPrototypeOf toString propertyIsEnumerable hasOwnProperty toLocaleString".split(" ");g.keys=function(t){if(!g.isObject(t))return[];if(b)return b(t);var e,n=[];for(e in t)g.has(t,e)&&n.push(e);return R&&o(t,n),n},g.allKeys=function(t){if(!g.isObject(t))return[];var e,n=[];for(e in t)n.push(e);return R&&o(t,n),n},g.values=function(t){for(var e=g.keys(t),n=e.length,r=Array(n),i=0;n>i;i++)r[i]=t[e[i]];return r},g.mapObject=function(t,e,n){e=w(e,n),n=g.keys(t);for(var r,i=n.length,o={},a=0;i>a;a++)r=n[a],o[r]=e(t[r],r,t);return o},g.pairs=function(t){for(var e=g.keys(t),n=e.length,r=Array(n),i=0;n>i;i++)r[i]=[e[i],t[e[i]]];return r},g.invert=function(t){for(var e={},n=g.keys(t),r=0,i=n.length;i>r;r++)e[t[n[r]]]=n[r];return e},g.functions=g.methods=function(t){var e,n=[];for(e in t)g.isFunction(t[e])&&n.push(e);return n.sort()},g.extend=x(g.allKeys),g.extendOwn=g.assign=x(g.keys),g.findKey=function(t,e,n){e=w(e,n),n=g.keys(t);for(var r,i=0,o=n.length;o>i;i++)if(r=n[i],e(t[r],r,t))return r},g.pick=function(t,e,n){var r,i,o={},a=t;if(null==a)return o;g.isFunction(e)?(i=g.allKeys(a),r=_(e,n)):(i=j(arguments,!1,!1,1),r=function(t,e,n){return e in n},a=Object(a));for(var s=0,c=i.length;c>s;s++){var f=i[s],u=a[f];r(u,f,a)&&(o[f]=u)}return o},g.omit=function(t,e,n){if(g.isFunction(e))e=g.negate(e);else{var r=g.map(j(arguments,!1,!1,1),String);e=function(t,e){return!g.contains(r,e)}}return g.pick(t,e,n)},g.defaults=x(g.allKeys,!0),g.create=function(t,e){var n=k(t);return e&&g.extendOwn(n,e),n},g.clone=function(t){return g.isObject(t)?g.isArray(t)?t.slice():g.extend({},t):t;
},g.tap=function(t,e){return e(t),t},g.isMatch=function(t,e){var n=g.keys(e),r=n.length;if(null==t)return!r;for(var i=Object(t),o=0;r>o;o++){var a=n[o];if(e[a]!==i[a]||!(a in i))return!1}return!0};var B=function(t,e,n,r){if(t===e)return 0!==t||1/t===1/e;if(null==t||null==e)return t===e;t instanceof g&&(t=t._wrapped),e instanceof g&&(e=e._wrapped);var i=p.call(t);if(i!==p.call(e))return!1;switch(i){case"[object RegExp]":case"[object String]":return""+t==""+e;case"[object Number]":return+t!==+t?+e!==+e:0===+t?1/+t===1/e:+t===+e;case"[object Date]":case"[object Boolean]":return+t===+e}if(i="[object Array]"===i,!i){if("object"!=typeof t||"object"!=typeof e)return!1;var o=t.constructor,a=e.constructor;if(o!==a&&!(g.isFunction(o)&&o instanceof o&&g.isFunction(a)&&a instanceof a)&&"constructor"in t&&"constructor"in e)return!1}for(n=n||[],r=r||[],o=n.length;o--;)if(n[o]===t)return r[o]===e;if(n.push(t),r.push(e),i){if(o=t.length,o!==e.length)return!1;for(;o--;)if(!B(t[o],e[o],n,r))return!1}else{if(i=g.keys(t),o=i.length,g.keys(e).length!==o)return!1;for(;o--;)if(a=i[o],!g.has(e,a)||!B(t[a],e[a],n,r))return!1}return n.pop(),r.pop(),!0};g.isEqual=function(t,e){return B(t,e)},g.isEmpty=function(t){return null==t?!0:A(t)&&(g.isArray(t)||g.isString(t)||g.isArguments(t))?0===t.length:0===g.keys(t).length},g.isElement=function(t){return!(!t||1!==t.nodeType)},g.isArray=l||function(t){return"[object Array]"===p.call(t)},g.isObject=function(t){var e=typeof t;return"function"===e||"object"===e&&!!t},g.each("Arguments Function String Number Date RegExp Error".split(" "),function(t){g["is"+t]=function(e){return p.call(e)==="[object "+t+"]"}}),g.isArguments(arguments)||(g.isArguments=function(t){return g.has(t,"callee")}),"function"!=typeof/./&&"object"!=typeof Int8Array&&(g.isFunction=function(t){return"function"==typeof t||!1}),g.isFinite=function(t){return isFinite(t)&&!isNaN(parseFloat(t))},g.isNaN=function(t){return g.isNumber(t)&&t!==+t},g.isBoolean=function(t){return!0===t||!1===t||"[object Boolean]"===p.call(t)},g.isNull=function(t){return null===t},g.isUndefined=function(t){return void 0===t},g.has=function(t,e){return null!=t&&h.call(t,e)},g.noConflict=function(){return a._=s,this},g.identity=function(t){return t},g.constant=function(t){return function(){return t}},g.noop=function(){},g.property=E,g.propertyOf=function(t){return null==t?function(){}:function(e){return t[e]}},g.matcher=g.matches=function(t){return t=g.extendOwn({},t),function(e){return g.isMatch(e,t)}},g.times=function(t,e,n){var r=Array(Math.max(0,t));for(e=_(e,n,1),n=0;t>n;n++)r[n]=e(n);return r},g.random=function(t,e){return null==e&&(e=t,t=0),t+Math.floor(Math.random()*(e-t+1))},g.now=Date.now||function(){return(new Date).getTime()},l={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},x=g.invert(l),E=function(t){var e=function(e){return t[e]},n="(?:"+g.keys(t).join("|")+")",r=RegExp(n),i=RegExp(n,"g");return function(t){return t=null==t?"":""+t,r.test(t)?t.replace(i,e):t}},g.escape=E(l),g.unescape=E(x),g.result=function(t,e,n){return e=null==t?void 0:t[e],void 0===e&&(e=n),g.isFunction(e)?e.call(t):e};var N=0;g.uniqueId=function(t){var e=++N+"";return t?t+e:e},g.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var C=/(.)^/,P={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},q=/\\|'|\r|\n|\u2028|\u2029/g,U=function(t){return"\\"+P[t]};g.template=function(t,e,n){!e&&n&&(e=n),e=g.defaults({},e,g.templateSettings),n=RegExp([(e.escape||C).source,(e.interpolate||C).source,(e.evaluate||C).source].join("|")+"|$","g");var r=0,i="__p+='";t.replace(n,function(e,n,o,a,s){return i+=t.slice(r,s).replace(q,U),r=s+e.length,n?i+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'":o?i+="'+\n((__t=("+o+"))==null?'':__t)+\n'":a&&(i+="';\n"+a+"\n__p+='"),e}),i+="';\n",e.variable||(i="with(obj||{}){\n"+i+"}\n"),i="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+i+"return __p;\n";try{var o=new Function(e.variable||"obj","_",i)}catch(a){throw a.source=i,a}return n=function(t){return o.call(this,t,g)},n.source="function("+(e.variable||"obj")+"){\n"+i+"}",n},g.chain=function(t){return t=g(t),t._chain=!0,t};var z=function(t,e){return t._chain?g(e).chain():e};g.mixin=function(t){g.each(g.functions(t),function(e){var n=g[e]=t[e];g.prototype[e]=function(){var t=[this._wrapped];return u.apply(t,arguments),z(this,n.apply(g,t))}})},g.mixin(g),g.each("pop push reverse shift sort splice unshift".split(" "),function(t){var e=c[t];g.prototype[t]=function(){var n=this._wrapped;return e.apply(n,arguments),"shift"!==t&&"splice"!==t||0!==n.length||delete n[0],z(this,n)}}),g.each(["concat","join","slice"],function(t){var e=c[t];g.prototype[t]=function(){return z(this,e.apply(this._wrapped,arguments))}}),g.prototype.value=function(){return this._wrapped},g.prototype.valueOf=g.prototype.toJSON=g.prototype.value,g.prototype.toString=function(){return""+this._wrapped}}).call(this)},{}],148:[function(t,e,n){function r(){this.href=this.path=this.pathname=this.query=this.search=this.hash=this.hostname=this.port=this.host=this.auth=this.slashes=this.protocol=null}function i(t,e,n){if(t&&o(t)&&t instanceof r)return t;var i=new r;return i.parse(t,e,n),i}function o(t){return"object"==typeof t&&null!==t}var a=t(120);n.parse=i,n.resolve=function(t,e){return i(t,!1,!0).resolve(e)},n.resolveObject=function(t,e){return t?i(t,!1,!0).resolveObject(e):e},n.format=function(t){return"string"==typeof t&&(t=i(t)),t instanceof r?t.format():r.prototype.format.call(t)},n.Url=r;var s=/^([a-z0-9.+-]+:)/i,c=/:[0-9]*$/;e="{}|\\^`".split("").concat('<>"` \r\n	'.split(""));var f=["'"].concat(e),u=["%","/","?",";","#"].concat(f),d=["/","?","#"],p=/^[a-z0-9A-Z_-]{0,63}$/,h=/^([a-z0-9A-Z_-]{0,63})(.*)$/,l={javascript:!0,"javascript:":!0},b={javascript:!0,"javascript:":!0},m={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},v=t(123);r.prototype.parse=function(t,e,n){if("string"!=typeof t)throw new TypeError("Parameter 'url' must be a string, not "+typeof t);t=t.trim();var r=s.exec(t);if(r){var r=r[0],i=r.toLowerCase();this.protocol=i,t=t.substr(r.length)}if(n||r||t.match(/^\/\/[^@\/]+@[^@\/]+/)){var o="//"===t.substr(0,2);!o||r&&b[r]||(t=t.substr(2),this.slashes=!0)}if(!b[r]&&(o||r&&!m[r])){for(o=-1,n=0;n<d.length;n++)r=t.indexOf(d[n]),-1!==r&&(-1===o||o>r)&&(o=r);for(o=-1===o?t.lastIndexOf("@"):t.lastIndexOf("@",o),-1!==o&&(n=t.slice(0,o),t=t.slice(o+1),this.auth=decodeURIComponent(n)),o=-1,n=0;n<u.length;n++)r=t.indexOf(u[n]),-1!==r&&(-1===o||o>r)&&(o=r);if(-1===o&&(o=t.length),this.host=t.slice(0,o),t=t.slice(o),this.parseHost(),this.hostname=this.hostname||"",o="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1],!o){var c=this.hostname.split(/\./);for(n=0,r=c.length;r>n;n++){var y=c[n];if(y&&!y.match(p)){for(var g="",_=0,w=y.length;w>_;_++)g=127<y.charCodeAt(_)?g+"x":g+y[_];if(!g.match(p)){r=c.slice(0,n),n=c.slice(n+1),(y=y.match(h))&&(r.push(y[1]),n.unshift(y[2])),n.length&&(t="/"+n.join(".")+t),this.hostname=r.join(".");break}}}}if(this.hostname=255<this.hostname.length?"":this.hostname.toLowerCase(),!o){for(y=this.hostname.split("."),c=[],n=0;n<y.length;++n)r=y[n],c.push(r.match(/[^A-Za-z0-9_-]/)?"xn--"+a.encode(r):r);this.hostname=c.join(".")}n=this.port?":"+this.port:"",this.host=(this.hostname||"")+n,this.href+=this.host,o&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==t[0]&&(t="/"+t))}if(!l[i])for(n=0,r=f.length;r>n;n++)o=f[n],y=encodeURIComponent(o),y===o&&(y=escape(o)),t=t.split(o).join(y);return n=t.indexOf("#"),-1!==n&&(this.hash=t.substr(n),t=t.slice(0,n)),n=t.indexOf("?"),-1!==n?(this.search=t.substr(n),this.query=t.substr(n+1),e&&(this.query=v.parse(this.query)),t=t.slice(0,n)):e&&(this.search="",this.query={}),t&&(this.pathname=t),m[i]&&this.hostname&&!this.pathname&&(this.pathname="/"),(this.pathname||this.search)&&(n=this.pathname||"",r=this.search||"",this.path=n+r),this.href=this.format(),this},r.prototype.format=function(){var t=this.auth||"";t&&(t=encodeURIComponent(t),t=t.replace(/%3A/i,":"),t+="@");var e=this.protocol||"",n=this.pathname||"",r=this.hash||"",i=!1,a="";return this.host?i=t+this.host:this.hostname&&(i=t+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&o(this.query)&&Object.keys(this.query).length&&(a=v.stringify(this.query)),t=this.search||a&&"?"+a||"",e&&":"!==e.substr(-1)&&(e+=":"),this.slashes||(!e||m[e])&&!1!==i?(i="//"+(i||""),n&&"/"!==n.charAt(0)&&(n="/"+n)):i||(i=""),r&&"#"!==r.charAt(0)&&(r="#"+r),t&&"?"!==t.charAt(0)&&(t="?"+t),n=n.replace(/[?#]/g,function(t){return encodeURIComponent(t)}),t=t.replace("#","%23"),e+i+n+t+r},r.prototype.resolve=function(t){return this.resolveObject(i(t,!1,!0)).format()},r.prototype.resolveObject=function(t){if("string"==typeof t){var e=new r;e.parse(t,!1,!0),t=e}var n=new r;if(Object.keys(this).forEach(function(t){n[t]=this[t]},this),n.hash=t.hash,""===t.href)return n.href=n.format(),n;if(t.slashes&&!t.protocol)return Object.keys(t).forEach(function(e){"protocol"!==e&&(n[e]=t[e])}),m[n.protocol]&&n.hostname&&!n.pathname&&(n.path=n.pathname="/"),n.href=n.format(),n;if(t.protocol&&t.protocol!==n.protocol){if(!m[t.protocol])return Object.keys(t).forEach(function(e){n[e]=t[e]}),n.href=n.format(),n;if(n.protocol=t.protocol,t.host||b[t.protocol])n.pathname=t.pathname;else{for(var i=(t.pathname||"").split("/");i.length&&!(t.host=i.shift()););t.host||(t.host=""),t.hostname||(t.hostname=""),""!==i[0]&&i.unshift(""),2>i.length&&i.unshift(""),n.pathname=i.join("/")}return n.search=t.search,n.query=t.query,n.host=t.host||"",n.auth=t.auth,n.hostname=t.hostname||t.host,n.port=t.port,(n.pathname||n.search)&&(n.path=(n.pathname||"")+(n.search||"")),n.slashes=n.slashes||t.slashes,n.href=n.format(),n}var e=n.pathname&&"/"===n.pathname.charAt(0),o=t.host||t.pathname&&"/"===t.pathname.charAt(0),a=e=o||e||n.host&&t.pathname,s=n.pathname&&n.pathname.split("/")||[],i=t.pathname&&t.pathname.split("/")||[],c=n.protocol&&!m[n.protocol];if(c&&(n.hostname="",n.port=null,n.host&&(""===s[0]?s[0]=n.host:s.unshift(n.host)),n.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(""===i[0]?i[0]=t.host:i.unshift(t.host)),t.host=null),e=e&&(""===i[0]||""===s[0])),o)n.host=t.host||""===t.host?t.host:n.host,n.hostname=t.hostname||""===t.hostname?t.hostname:n.hostname,n.search=t.search,n.query=t.query,s=i;else if(i.length)s||(s=[]),s.pop(),s=s.concat(i),n.search=t.search,n.query=t.query;else if(null!=t.search)return c&&(n.hostname=n.host=s.shift(),c=n.host&&0<n.host.indexOf("@")?n.host.split("@"):!1)&&(n.auth=c.shift(),n.host=n.hostname=c.shift()),n.search=t.search,n.query=t.query,null===n.pathname&&null===n.search||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.href=n.format(),n;if(!s.length)return n.pathname=null,n.path=n.search?"/"+n.search:null,n.href=n.format(),n;for(var o=s.slice(-1)[0],i=(n.host||t.host)&&("."===o||".."===o)||""===o,f=0,u=s.length;u>=0;u--)o=s[u],"."==o?s.splice(u,1):".."===o?(s.splice(u,1),f++):f&&(s.splice(u,1),f--);if(!e&&!a)for(;f--;f)s.unshift("..");return!e||""===s[0]||s[0]&&"/"===s[0].charAt(0)||s.unshift(""),i&&"/"!==s.join("/").substr(-1)&&s.push(""),a=""===s[0]||s[0]&&"/"===s[0].charAt(0),c&&(n.hostname=n.host=a?"":s.length?s.shift():"",c=n.host&&0<n.host.indexOf("@")?n.host.split("@"):!1)&&(n.auth=c.shift(),n.host=n.hostname=c.shift()),(e=e||n.host&&s.length)&&!a&&s.unshift(""),s.length?n.pathname=s.join("/"):(n.pathname=null,n.path=null),null===n.pathname&&null===n.search||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.auth=t.auth||n.auth,n.slashes=n.slashes||t.slashes,n.href=n.format(),n},r.prototype.parseHost=function(){var t=this.host,e=c.exec(t);e&&(e=e[0],":"!==e&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)}},{120:120,123:123}],149:[function(t,e,n){e.exports=function(t){return t&&"object"==typeof t&&"function"==typeof t.copy&&"function"==typeof t.fill&&"function"==typeof t.readUInt8}},{}],150:[function(t,e,n){(function(e,r){function i(t,e){var r={seen:[],stylize:a};return 3<=arguments.length&&(r.depth=arguments[2]),4<=arguments.length&&(r.colors=arguments[3]),b(e)?r.showHidden=e:e&&n._extend(r,e),y(r.showHidden)&&(r.showHidden=!1),y(r.depth)&&(r.depth=2),y(r.colors)&&(r.colors=!1),y(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=o),c(r,t,r.depth)}function o(t,e){var n=i.styles[e];return n?"["+i.colors[n][0]+"m"+t+"["+i.colors[n][1]+"m":t}function a(t,e){return t}function s(t){var e={};return t.forEach(function(t,n){e[t]=!0}),e}function c(t,e,r){if(t.customInspect&&e&&k(e.inspect)&&e.inspect!==n.inspect&&(!e.constructor||e.constructor.prototype!==e)){var i=e.inspect(r,t);return v(i)||(i=c(t,i,r)),i}if(i=f(t,e))return i;var o=Object.keys(e),a=s(o);if(t.showHidden&&(o=Object.getOwnPropertyNames(e)),x(e)&&(0<=o.indexOf("message")||0<=o.indexOf("description")))return u(e);if(0===o.length){if(k(e))return t.stylize("[Function"+(e.name?": "+e.name:"")+"]","special");if(g(e))return t.stylize(RegExp.prototype.toString.call(e),"regexp");if(w(e))return t.stylize(Date.prototype.toString.call(e),"date");if(x(e))return u(e)}var i="",b=!1,m=["{","}"];return l(e)&&(b=!0,m=["[","]"]),k(e)&&(i=" [Function"+(e.name?": "+e.name:"")+"]"),g(e)&&(i=" "+RegExp.prototype.toString.call(e)),w(e)&&(i=" "+Date.prototype.toUTCString.call(e)),x(e)&&(i=" "+u(e)),0!==o.length||b&&0!=e.length?0>r?g(e)?t.stylize(RegExp.prototype.toString.call(e),"regexp"):t.stylize("[Object]","special"):(t.seen.push(e),o=b?d(t,e,r,a,o):o.map(function(n){return p(t,e,r,a,n,b)}),t.seen.pop(),h(o,i,m)):m[0]+i+m[1]}function f(t,e){if(y(e))return t.stylize("undefined","undefined");if(v(e)){var n="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(n,"string")}return m(e)?t.stylize(""+e,"number"):b(e)?t.stylize(""+e,"boolean"):null===e?t.stylize("null","null"):void 0}function u(t){return"["+Error.prototype.toString.call(t)+"]"}function d(t,e,n,r,i){for(var o=[],a=0,s=e.length;s>a;++a)Object.prototype.hasOwnProperty.call(e,String(a))?o.push(p(t,e,n,r,String(a),!0)):o.push("");return i.forEach(function(i){i.match(/^\d+$/)||o.push(p(t,e,n,r,i,!0))}),o}function p(t,e,n,r,i,o){var a,s;if(e=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]},e.get?s=e.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):e.set&&(s=t.stylize("[Setter]","special")),Object.prototype.hasOwnProperty.call(r,i)||(a="["+i+"]"),s||(0>t.seen.indexOf(e.value)?(s=null===n?c(t,e.value,null):c(t,e.value,n-1),-1<s.indexOf("\n")&&(s=o?s.split("\n").map(function(t){return"  "+t}).join("\n").substr(2):"\n"+s.split("\n").map(function(t){return"   "+t}).join("\n"))):s=t.stylize("[Circular]","special")),y(a)){if(o&&i.match(/^\d+$/))return s;a=JSON.stringify(""+i),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=t.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=t.stylize(a,"string"))}return a+": "+s}function h(t,e,n){var r=0;return 60<t.reduce(function(t,e){return r++,0<=e.indexOf("\n")&&r++,t+e.replace(/\u001b\[\d\d?m/g,"").length+1},0)?n[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+n[1]:n[0]+e+" "+t.join(", ")+" "+n[1]}function l(t){return Array.isArray(t)}function b(t){return"boolean"==typeof t}function m(t){return"number"==typeof t}function v(t){return"string"==typeof t}function y(t){return void 0===t}function g(t){return _(t)&&"[object RegExp]"===Object.prototype.toString.call(t)}function _(t){return"object"==typeof t&&null!==t}function w(t){return _(t)&&"[object Date]"===Object.prototype.toString.call(t)}function x(t){return _(t)&&("[object Error]"===Object.prototype.toString.call(t)||t instanceof Error)}function k(t){return"function"==typeof t}function E(t){return 10>t?"0"+t.toString(10):t.toString(10)}function S(){var t=new Date,e=[E(t.getHours()),E(t.getMinutes()),E(t.getSeconds())].join(":");return[t.getDate(),j[t.getMonth()],e].join(" ")}var M=/%[sdj%]/g;n.format=function(t){if(!v(t)){for(var e=[],n=0;n<arguments.length;n++)e.push(i(arguments[n]));return e.join(" ")}for(var n=1,r=arguments,o=r.length,e=String(t).replace(M,function(t){if("%%"===t)return"%";if(n>=o)return t;switch(t){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(e){return"[Circular]"}default:return t}}),a=r[n];o>n;a=r[++n])e=null!==a&&_(a)?e+(" "+i(a)):e+(" "+a);return e},n.deprecate=function(t,i){if(y(r.process))return function(){return n.deprecate(t,i).apply(this,arguments)};if(!0===e.noDeprecation)return t;var o=!1;return function(){if(!o){if(e.throwDeprecation)throw Error(i);e.traceDeprecation?console.trace(i):console.error(i),o=!0}return t.apply(this,arguments)}};var A,T={};n.debuglog=function(t){if(y(A)&&(A=e.env.NODE_DEBUG||""),t=t.toUpperCase(),!T[t])if(new RegExp("\\b"+t+"\\b","i").test(A)){var r=e.pid;T[t]=function(){var e=n.format.apply(n,arguments);console.error("%s %d: %s",t,r,e)}}else T[t]=function(){};return T[t]},n.inspect=i,i.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},i.styles={special:"cyan",number:"yellow","boolean":"yellow",undefined:"grey","null":"bold",string:"green",date:"magenta",regexp:"red"},n.isArray=l,n.isBoolean=b,n.isNull=function(t){return null===t},n.isNullOrUndefined=function(t){return null==t},n.isNumber=m,n.isString=v,n.isSymbol=function(t){return"symbol"==typeof t},n.isUndefined=y,n.isRegExp=g,n.isObject=_,n.isDate=w,n.isError=x,n.isFunction=k,n.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||"undefined"==typeof t},n.isBuffer=t(149);var j="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" ");n.log=function(){console.log("%s - %s",S(),n.format.apply(n,arguments))},n.inherits=t(101),n._extend=function(t,e){if(!e||!_(e))return t;for(var n=Object.keys(e),r=n.length;r--;)t[n[r]]=e[n[r]];return t}}).call(this,t(113),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{101:101,113:113,149:149}],151:[function(k,r,l){function g(){}var n=k(100),f=function(t){if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)n.push(e);return n},m=function(t,e){if(t.forEach)return t.forEach(e);for(var n=0;n<t.length;n++)e(t[n],n,t)},e=function(){try{return Object.defineProperty({},"_",{}),function(t,e,n){Object.defineProperty(t,e,{writable:!0,enumerable:!1,configurable:!0,value:n})}}catch(t){return function(t,e,n){t[e]=n}}}(),d="Array Boolean Date Error EvalError Function Infinity JSON Math NaN Number Object RangeError ReferenceError RegExp String SyntaxError TypeError URIError decodeURI decodeURIComponent encodeURI encodeURIComponent escape eval isFinite isNaN parseFloat parseInt undefined unescape".split(" ");g.prototype={};var b=l.Script=function(t){return this instanceof b?void(this.code=t):new b(t)};b.prototype.runInContext=function(t){if(!(t instanceof g))throw new TypeError("needs a 'context' argument.");var r=document.createElement("iframe");r.style||(r.style={}),r.style.display="none",document.body.appendChild(r);var i=r.contentWindow,o=i.eval,a=i.execScript;!o&&a&&(a.call(i,"null"),o=i.eval),m(f(t),function(e){i[e]=t[e]}),m(d,function(e){t[e]&&(i[e]=t[e])});var s=f(i),o=o.call(i,this.code);return m(f(i),function(e){(e in t||-1===n(s,e))&&(t[e]=i[e])}),m(d,function(n){n in t||e(t,n,i[n])}),document.body.removeChild(r),o},b.prototype.runInThisContext=function(){return eval(this.code)},b.prototype.runInNewContext=function(t){var e=b.createContext(t),n=this.runInContext(e);return m(f(e),function(n){t[n]=e[n]}),n},m(f(b.prototype),function(t){l[t]=b[t]=function(e){var n=b(e);return n[t].apply(n,[].slice.call(arguments,1))}}),l.createScript=function(t){return l.Script(t)},l.createContext=b.createContext=function(t){var e=new g;return"object"==typeof t&&m(f(t),function(n){e[n]=t[n]}),e}},{100:100}],152:[function(t,e,n){function r(){f.apply(this,arguments)}var i=t(17),o=t(147),a=t(166),s=t(165),c=t(154),f=t(162);s.inherits(r,f),r.prototype.start=function(){if(this.aborted)return a.Q.resolve();var t=this,e=this.eventDispatcher,n=this.options.file,r=this.options.bucket,f=this.options.object,u=this.options.metas,d=this.options.chunk_size,p=s.guessContentType(n);return this._getObjectMetadata(r,f).then(function(h){if(h=h.http_headers,!s.isAppendable(h)||+h["content-length"]>=n.size)return e.dispatchEvent(c.kUploadProgress,[n,1,null]),a.Q.resolve();h=+h["x-bce-next-append-offset"],e.dispatchEvent(c.kUploadProgress,[n,0>=n.size?0:h/n.size,null]),h=s.getAppendableTasks(n.size,h,d);var l=a.Q.defer();return i.mapLimit(h,1,function(i,a){var s=i.start,d=s>0?s:null,s=n.slice(s,i.stop+1),h=o.extend({"Content-Type":p,"Content-Length":i.partSize},u);return t.xhrRequesting=t.client.appendObjectFromBlob(r,f,s,d,h),t.xhrRequesting.then(function(t){e.dispatchEvent(c.kUploadProgress,[n,(i.stop+1)/n.size,null]),a(null,t)},function(t){a(t)})},function(t,e){t?l.reject(t):l.resolve(e)}),l.promise}).then(function(){e.dispatchEvent(c.kFileUploaded,[n,{http_headers:{},body:{bucket:r,object:f}}])})["catch"](function(r){return e.dispatchEvent(t.aborted?c.kAborted:c.kError,[r,n]),a.Q.resolve()})},r.prototype._getObjectMetadata=function(t,e){return this.client.getObjectMetadata(t,e)["catch"](function(t){if(404===t.status_code)return{http_headers:{"content-length":0,"x-bce-next-append-offset":0,"x-bce-object-type":"Appendable"},body:{}};throw t})},e.exports=r},{147:147,154:154,162:162,165:165,166:166,17:17}],153:[function(t,e,n){e.exports={runtimes:"html5",bos_endpoint:"http://bos.bj.baidubce.com",bos_credentials:null,bos_appendable:!1,bos_relay_server:"https://relay.efe.tech",multi_selection:!1,max_retries:0,auto_start:!1,max_file_size:"100mb",bos_multipart_min_size:"10mb",bos_multipart_parallel:1,bos_task_parallel:3,auth_stripped_headers:["User-Agent","Connection"],chunk_size:"4mb",bos_multipart_auto_continue:!0,bos_multipart_local_key_generator:"default",dir_selection:!1,get_new_uptoken:!0,uptoken_jsonp_timeout:5e3,tracker_id:"2e0bc8c5e7ceb25796ba4962e7b57387"}},{}],154:[function(t,e,n){e.exports={kPostInit:"PostInit",kKey:"Key",kListParts:"ListParts",kObjectMetas:"ObjectMetas",kFileFiltered:"FileFiltered",kFilesAdded:"FilesAdded",kFilesFilter:"FilesFilter",kNetworkSpeed:"NetworkSpeed",kBeforeUpload:"BeforeUpload",kUploadProgress:"UploadProgress",kFileUploaded:"FileUploaded",kUploadPartProgress:"UploadPartProgress",kChunkUploaded:"ChunkUploaded",kUploadResume:"UploadResume",kUploadResumeError:"UploadResumeError",kUploadComplete:"UploadComplete",kError:"Error",kAborted:"Aborted"}},{}],155:[function(t,e,n){function r(){f.apply(this,arguments)}var i=t(17),o=t(147),a=t(166),s=t(165),c=t(154),f=t(162);s.inherits(r,f),r.prototype.start=function(){if(this.aborted)return a.Q.resolve();var t=this,e=this.eventDispatcher,n=this.options.file,r=this.options.bucket,f=this.options.object,u=this.options.metas,d=this.options.chunk_size,p=this.options.bos_multipart_parallel,h={"Content-Type":s.guessContentType(n)},l=null;return this._initiateMultipartUpload(n,d,r,f,h).then(function(o){l=o.body.uploadId;var u=o.body.parts||[],h=a.Q.defer();o=s.getTasks(n,l,d,r,f),s.filterTasks(o,u);var u=u.length,b={lengthComputable:!0,loaded:u,total:o.length};return u&&e.dispatchEvent(c.kUploadProgress,[n,u/o.length,null]),i.mapLimit(o,p,t._uploadPart(b),function(t,e){t?h.reject(t):h.resolve(e)}),h.promise}).then(function(e){var i=[];return o.each(e,function(t,e){i.push({partNumber:e+1,eTag:t.http_headers.etag})}),t._generateLocalKey({blob:n,chunkSize:d,bucket:r,object:f}).then(function(t){s.removeUploadId(t)}),t.client.completeMultipartUpload(r,f,l,i,u)}).then(function(t){e.dispatchEvent(c.kUploadProgress,[n,1]),t.body.bucket=r,t.body.object=f,e.dispatchEvent(c.kFileUploaded,[n,t])})["catch"](function(r){e.dispatchEvent(t.aborted?c.kAborted:c.kError,[r,n])})},r.prototype._initiateMultipartUpload=function(t,e,n,r,i){function o(){return d.client.initiateMultipartUpload(n,r,i).then(function(t){return u&&s.setUploadId(u,t.body.uploadId),t.body.parts=[],t})}var f,u,d=this,p=this.eventDispatcher;return e={blob:t,chunkSize:e,bucket:n,object:r},(this.options.bos_multipart_auto_continue?this._generateLocalKey(e):a.Q.resolve(null)).then(function(e){return u=e,u&&(f=s.getUploadId(u))?d._listParts(t,n,r,f):o()}).then(function(e){return f&&u&&(p.dispatchEvent(c.kUploadResume,[t,e.body.parts,null]),e.body.uploadId=f),e})["catch"](function(e){if(f&&u)return p.dispatchEvent(c.kUploadResumeError,[t,e,null]),s.removeUploadId(u),o();throw e})},r.prototype._generateLocalKey=function(t){return s.generateLocalKey(t,this.options.bos_multipart_local_key_generator)},r.prototype._listParts=function(t,e,n,r){var i=this;return t=this.eventDispatcher.dispatchEvent(c.kListParts,[t,r]),a.Q.resolve(t).then(function(t){return o.isArray(t)&&t.length?{http_headers:{},body:{parts:t}}:i._listAllParts(e,n,r)})},r.prototype._listAllParts=function(t,e,n){function r(){i.client.listParts(t,e,n,{maxParts:1e3,partNumberMarker:f}).then(function(t){null==c&&(c=t),s.push.apply(s,t.body.parts),f=t.body.nextPartNumberMarker,!1===t.body.isTruncated?(c.body.parts=s,o.resolve(c)):r()})["catch"](function(t){o.reject(t)})}var i=this,o=a.Q.defer(),s=[],c=null,f=0;return r(),o.promise},r.prototype._uploadPart=function(t){function e(i,o){if(i.etag)return n.networkInfo.loadedBytes+=i.partSize,a.Q.resolve({http_headers:{etag:i.etag},body:{}});var s=null==o?n.options.max_retries:o,f=i.file.slice(i.start,i.stop+1);return f._previousLoaded=0,n.xhrRequesting=n.client.uploadPartFromBlob(i.bucket,i.object,i.uploadId,i.partNumber,i.partSize,f),n.xhrRequesting.then(function(e){return++t.loaded,r.dispatchEvent(c.kUploadProgress,[i.file,t.loaded/t.total,null]),r.dispatchEvent(c.kChunkUploaded,[i.file,{uploadId:i.uploadId,partNumber:i.partNumber,partSize:i.partSize,bucket:i.bucket,object:i.object,offset:i.start,total:f.size,response:e}]),e})["catch"](function(t){if(s>0)return e(i,s-1);throw t})}var n=this,r=this.eventDispatcher;return function(t,n){e(t).then(function(t){n(null,t)},function(t){n(t)})}},e.exports=r},{147:147,154:154,162:162,165:165,166:166,17:17}],156:[function(t,e,n){function r(){this.totalBytes=this.loadedBytes=0,this._startTime=i.now(),this.reset()}var i=t(165);r.prototype.dump=function(){return[this.loadedBytes,i.now()-this._startTime,this.totalBytes-this.loadedBytes]},r.prototype.reset=function(){this.loadedBytes=0,this._startTime=i.now()},e.exports=r},{165:165}],157:[function(t,e,n){(function(n){function r(t){this.options=t,this._cache={}}var i=t(166),o=t(165);r.prototype.get=function(t){var e=this;return null!=e._cache[t]?e._cache[t]:i.Q.resolve(this._getImpl(t)).then(function(n){return e._cache[t]=n})},r.prototype._getImpl=function(t){t=o.getDefaultPolicy(t);var e=this.options;return e.bos_credentials&&!e.uptoken?this._getFromLocal(t,e.bos_credentials):this._getFromRemote(t)},r.prototype._getFromLocal=function(t,e){var r=new i.Auth(e.ak,e.sk),o=new n(JSON.stringify(t)).toString("base64"),r=r.hash(o,e.sk);return{policy:o,signature:r,accessKey:e.ak}},r.prototype._getFromRemote=function(t){var e=this.options,n=e.uptoken_url,r=e.uptoken_jsonp_timeout,o=i.Q.defer();return $.ajax({url:n,jsonp:"callback",dataType:"jsonp",timeout:r,data:{policy:JSON.stringify(t)},success:function(t){o.resolve(t)},error:function(){o.reject(Error("Get policy signature timeout ("+r+"ms)."))}}),o.promise},e.exports=r}).call(this,t(48).Buffer)},{165:165,166:166,48:48}],158:[function(t,e,n){function r(){c.apply(this,arguments),this._policyManager=null}var i=t(147),o=t(166),a=t(165),s=t(154),c=t(162);a.inherits(r,c),r.prototype.setPolicyManager=function(t){this._policyManager=t},r.prototype.start=function(t){if(this.aborted)return o.Q.resolve();var e=this,n=this.eventDispatcher,r=this.options.file,i=this.options.bucket,c=this.options.object,f=this.client.config;return this._policyManager.get(i).then(function(t){var n=f.endpoint.replace(/^(https?:\/\/)/,"$1"+i+".");return t={"Content-Type":a.guessContentType(r,!0),key:c,policy:t.policy,signature:t.signature,accessKey:t.accessKey,"success-action-status":"201"},e._sendPostRequest(n,t,r)}).then(function(t){t.body.bucket=i,t.body.object=c,n.dispatchEvent(s.kFileUploaded,[r,t])})["catch"](function(t){n.dispatchEvent(e.aborted?s.kAborted:s.kError,[t,r])})},r.prototype._sendPostRequest=function(t,e,n){var r=this,a=this.eventDispatcher,c=o.Q.defer();if("undefined"==typeof mOxie||!i.isFunction(mOxie.FormData)||!i.isFunction(mOxie.XMLHttpRequest))return o.Q.reject(Error("mOxie is undefined."));var f=new mOxie.FormData;i.each(e,function(t,e){null!=t&&f.append(e,t)}),f.append("file",n);var u=this.xhrRequesting=new mOxie.XMLHttpRequest;return u.onload=function(t){200<=u.status&&300>u.status?c.resolve({http_headers:{},body:{}}):c.reject(Error("Invalid response statusCode "+u.status))},u.onerror=function(t){c.reject(t)},u.onabort=function(){c.reject(Error("xhr was aborted."))},u.upload&&(u.upload.onprogress=function(t){var e=t.loaded/t.total;r.networkInfo.loadedBytes+=t.loaded-n._previousLoaded,n._previousLoaded=t.loaded,a.dispatchEvent(s.kNetworkSpeed,r.networkInfo.dump()),a.dispatchEvent(s.kUploadProgress,[n,e,null])}),u.open("POST",t,!0),u.send(f,{runtime_order:"flash",swf_url:r.options.flash_swf_url}),c.promise},e.exports=r},{147:147,154:154,162:162,165:165,166:166}],159:[function(t,e,n){function r(){c.apply(this,arguments)}var i=t(147),o=t(166),a=t(165),s=t(154),c=t(162);a.inherits(r,c),r.prototype.start=function(t){if(this.aborted)return o.Q.resolve();var e=this,n=this.eventDispatcher,r=this.options.file,c=this.options.bucket,f=this.options.object,u=this.options.metas,d=null==t?this.options.max_retries:t;return t=a.guessContentType(r),u=i.extend({"Content-Type":t},u),this.xhrRequesting=this.client.putObjectFromBlob(c,f,r,u),this.xhrRequesting.then(function(t){n.dispatchEvent(s.kUploadProgress,[r,1]),t.body.bucket=c,t.body.object=f,n.dispatchEvent(s.kFileUploaded,[r,t])})["catch"](function(t){return n.dispatchEvent(e.aborted?s.kAborted:s.kError,[t,r]),!(t.status_code&&t.code&&t.request_id)&&d>0?e.start(d-1):o.Q.resolve()})},e.exports=r},{147:147,154:154,162:162,165:165,166:166}],160:[function(t,e,n){function r(t){this.collection=t}r.prototype.isEmpty=function(){return 0>=this.collection.length},r.prototype.size=function(){return this.collection.length},r.prototype.dequeue=function(){return this.collection.shift()},e.exports=r},{}],161:[function(t,e,n){function r(t){this.options=t,this._cache={}}var i=t(166),o=t(165);r.prototype.get=function(t){var e=this;return null!=e._cache[t]?e._cache[t]:i.Q.resolve(this._getImpl.apply(this,arguments)).then(function(n){return e._cache[t]=n})},r.prototype._getImpl=function(t){var e=this.options.uptoken_url,n=this.options.uptoken_jsonp_timeout,r=i.Q.defer();return $.ajax({url:e,jsonp:"callback",dataType:"jsonp",timeout:n,data:{sts:JSON.stringify(o.getDefaultACL(t))},success:function(t){r.resolve(t)},error:function(){r.reject(Error("Get sts token timeout ("+n+"ms)."))}}),r.promise},e.exports=r},{165:165,166:166}],162:[function(t,e,n){function r(t,e,n){this.xhrRequesting=null,this.aborted=!1,this.networkInfo=null,this.client=t,this.eventDispatcher=e,this.options=n}function i(){throw Error("unimplemented method.")}r.prototype.start=i,r.prototype.pause=i,r.prototype.resume=i,r.prototype.setNetworkInfo=function(t){this.networkInfo=t},r.prototype.abort=function(){this.xhrRequesting&&"function"==typeof this.xhrRequesting.abort&&(this.aborted=!0,this.xhrRequesting.abort(),this.xhrRequesting=null)},e.exports=r},{}],163:[function(t,e,n){n.init=function(t){var e=document.createElement("script");e.src="//hm.baidu.com/hm.js?"+t,t=document.getElementsByTagName("script")[0],
t.parentNode.insertBefore(e,t)}},{}],164:[function(t,e,n){function r(t){o.isString(t)&&(t=o.extend({browse_button:t,auto_start:!0},$(t).data())),this.options=o.extend({},u,{},t),this.options.max_file_size=s.parseSize(this.options.max_file_size),this.options.bos_multipart_min_size=s.parseSize(this.options.bos_multipart_min_size),this.options.chunk_size=s.parseSize(this.options.chunk_size),!this.options.bos_credentials&&this.options.bos_ak&&this.options.bos_sk&&(this.options.bos_credentials={ak:this.options.bos_ak,sk:this.options.bos_sk}),this.client=new i.BosClient({endpoint:s.normalizeEndpoint(this.options.bos_endpoint),credentials:this.options.bos_credentials,sessionToken:this.options.uptoken}),this._files=[],this._uploadingFiles={},this._working=this._abort=!1,this._xhr2Supported=s.isXhr2Supported(),this._policyManager=null,this._networkInfo=new v,this._init()}var i=t(166),o=t(147),a=t(58)("bce-bos-uploader"),s=t(165),c=t(163),f=t(154),u=t(153),d=t(159),p=t(152),h=t(155),l=t(158),b=t(161),m=t(157),v=t(156);r.prototype._getCustomizedSignature=function(t){var e=this.options,n=e.uptoken_jsonp_timeout;return function(r,a,s,c,f){/\bed=([\w\.]+)\b/.test(location.search)&&(f.Host=RegExp.$1),o.isArray(e.auth_stripped_headers)&&(f=o.omit(f,e.auth_stripped_headers));var u=i.Q.defer();return $.ajax({url:t,jsonp:"callback",dataType:"jsonp",timeout:n,data:{httpMethod:a,path:s,queries:JSON.stringify(c||{}),headers:JSON.stringify(f||{})},error:function(){u.reject(Error("Get authorization timeout ("+n+"ms)."))},success:function(t){200===t.statusCode&&t.signature?u.resolve(t.signature,t.xbceDate):u.reject(Error("createSignature failed, statusCode = "+t.statusCode))}}),u.promise}},r.prototype._invoke=function(t,e,n){var r=this.options.init||this.options.Init;if(r&&(r=r[t],"function"==typeof r))try{return e=null==e?[null]:[null].concat(e),r.apply(null,e)}catch(o){if(a("%s(%j) -> %s",t,e,o),!0===n)return i.Q.reject(o)}},r.prototype._init=function(){var t=this.options,e=t.accept;t.tracker_id&&c.init(t.tracker_id);var n=this;!this._xhr2Supported&&"undefined"!=typeof mOxie&&o.isFunction(mOxie.FileInput)&&(e=new mOxie.FileInput({runtime_order:"flash,html4",browse_button:$(t.browse_button).get(0),swf_url:t.flash_swf_url,accept:s.expandAcceptToArray(e),multiple:t.multi_selection,directory:t.dir_selection,file:"file"}),e.onchange=o.bind(this._onFilesAdded,this),e.onready=function(){n._initEvents(),n._invoke(f.kPostInit)},e.init()),e=i.Q.resolve(),n._xhr2Supported&&!t.bos_credentials&&t.uptoken_url&&!1===t.get_new_uptoken&&(e=new b(t).get(t.bos_bucket).then(function(e){t.bos_credentials={ak:e.AccessKeyId,sk:e.SecretAccessKey},t.uptoken=e.SessionToken,n.client=new i.BosClient({endpoint:s.normalizeEndpoint(t.bos_endpoint),credentials:t.bos_credentials,sessionToken:t.uptoken})})),e.then(function(){t.bos_credentials?n.client.createSignature=function(t,e,n,r,o){var a=t||this.config.credentials;return i.Q.fcall(function(){return new i.Auth(a.ak,a.sk).generateAuthorization(e,n,r,o)})}:t.uptoken_url&&!0===t.get_new_uptoken&&(n.client.createSignature=n._getCustomizedSignature(t.uptoken_url)),n._xhr2Supported&&(n._initEvents(),n._invoke(f.kPostInit)),n._policyManager=new m(t)})["catch"](function(t){a(t),n._invoke(f.kError,[t])})},r.prototype._initEvents=function(){var t=this.options;if(this._xhr2Supported){var e=$(t.browse_button);null==e.attr("multiple")&&e.attr("multiple",!!t.multi_selection),e.on("change",o.bind(this._onFilesAdded,this));var n=t.accept;null!=n&&e.attr("accept",s.expandAccept(n)),t.dir_selection&&(e.attr("directory",!0),e.attr("mozdirectory",!0),e.attr("webkitdirectory",!0))}this.client.on("progress",o.bind(this._onUploadProgress,this)),this.client.on("error",o.bind(this._onError,this)),this._xhr2Supported||(this.client.sendHTTPRequest=o.bind(s.fixXhr(this.options,!0),this.client),i.VodClient.prototype.sendHTTPRequest=s.fixXhr(this.options),i.DocClient.Document.prototype.sendHTTPRequest=s.fixXhr(this.options))},r.prototype._filterFiles=function(t){var e=this,n=this.options.max_file_size;return t=o.filter(t,function(t){return n>0&&t.size>n?(e._invoke(f.kFileFiltered,[t]),!1):!0}),this._invoke(f.kFilesFilter,[t])||t},r.prototype._onFilesAdded=function(t){var e=this,n=t.target.files;n||(n=[{name:t.target.value.split(/[\/\\]/).pop(),size:0}]),n=this._filterFiles(n),o.isArray(n)&&n.length&&(this._networkInfo.totalBytes+=o.reduce(n,function(t,n){return n.abort=function(){n._aborted=!0,e._invoke(f.kAborted,[null,n])},n.uuid=s.uuid(),t+n.size},0),this._files.push.apply(this._files,n),this._invoke(f.kFilesAdded,[n])),this.options.auto_start&&this.start()},r.prototype._onError=function(t){a(t)},r.prototype._onUploadProgress=function(t,e){var n=e.args,r=n.body;if(s.isBlob(r)){var i=t.lengthComputable?t.loaded/t.total:0;this._networkInfo.loadedBytes+=t.loaded-r._previousLoaded,this._invoke(f.kNetworkSpeed,this._networkInfo.dump()),r._previousLoaded=t.loaded;var o=f.kUploadProgress;n.params.partNumber&&n.params.uploadId&&(o=f.kUploadPartProgress),this._invoke(o,[r,i,t])}},r.prototype.remove=function(t){"string"==typeof t&&(t=this._uploadingFiles[t]||o.find(this._files,function(e){return e.uuid===t})),t&&"function"==typeof t.abort&&t.abort()},r.prototype.start=function(){var t=this;!this._working&&this._files.length&&(this._working=!0,this._abort=!1,this._networkInfo.reset(),s.eachLimit(this._files,this.options.bos_task_parallel,function(e,n){e._previousLoaded=0,t._uploadNext(e).fin(function(){delete t._uploadingFiles[e.uuid],n(null,e)})},function(e){t._working=!1,t._files.length=0,t._networkInfo.totalBytes=0,t._invoke(f.kUploadComplete)}))},r.prototype.stop=function(){this._abort=!0,this._working=!1},r.prototype._uploadNext=function(t){if(this._abort)return this._working=!1,i.Q.resolve();if(!0===t._aborted||!1===this._invoke(f.kBeforeUpload,[t]))return i.Q.resolve();var e=this,n=this.options,r=n.bos_bucket,a=t.name,s=o.pick(n,"flash_swf_url","max_retries","chunk_size","bos_multipart_parallel","bos_multipart_auto_continue","bos_multipart_local_key_generator");return i.Q.all([this._invoke(f.kKey,[t],!0),this._invoke(f.kObjectMetas,[t])]).then(function(i){var c=i[0],f=i[1];i="auto",o.isString(c)?a=c:o.isObject(c)&&(r=c.bucket||r,a=c.key||a,i=c.multipart||i);var c=e.client,f=o.extend(s,{file:t,bucket:r,object:a,metas:f}),u=null;return e._xhr2Supported?u=!0===n.bos_appendable?new p(c,e,f):"auto"===i&&t.size>n.bos_multipart_min_size?new h(c,e,f):new d(c,e,f):(u=new l(c,e,f),u.setPolicyManager(e._policyManager)),e._uploadingFiles[t.uuid]=t,t.abort=function(){return t._aborted=!0,u.abort()},u.setNetworkInfo(e._networkInfo),u.start()})},r.prototype.dispatchEvent=function(t,e,n){if(t===f.kAborted&&e&&e[1]){var r=e[1];0<r.size&&(this._networkInfo.totalBytes-=r.size-(r._previousLoaded||0),this._invoke(f.kNetworkSpeed,this._networkInfo.dump()))}return this._invoke(t,e,n)},e.exports=r},{147:147,152:152,153:153,154:154,155:155,156:156,157:157,158:158,159:159,161:161,163:163,165:165,166:166,58:58}],165:[function(t,e,n){function r(t,e){var n=a.filter(e||[],function(e){return+e.partNumber===t});return n.length?n[0].eTag:null}var i=t(148),o=t(123),a=t(147),s=t(166),c=t(144),f=t(160);n.getTasks=function(t,e,n,r,i){for(var o=t.size,a=0,s=1,c=[];o>0;){var f=Math.min(o,n);c.push({file:t,uploadId:e,bucket:r,object:i,partNumber:s,partSize:f,start:a,stop:a+f-1}),o-=f,a+=f,s+=1}return c},n.getAppendableTasks=function(t,e,n){t-=e;for(var r=[];t;){var i=Math.min(t,n);r.push({partSize:i,start:e,stop:e+i-1}),t-=i,e+=i}return r},n.parseSize=function(t){if("number"==typeof t)return t;var e=/^([\d\.]+)([mkg]?b?)$/i.exec(t);return e?(t=e[1],e=e[2],/^k/i.test(e)?1024*t:/^m/i.test(e)?1048576*t:/^g/i.test(e)?1073741824*t:+t):0},n.isXhr2Supported=function(){return"XMLHttpRequest"in window&&"withCredentials"in new XMLHttpRequest},n.isAppendable=function(t){return"Appendable"===t["x-bce-object-type"]},n.normalizeEndpoint=function(t){return t.replace(/(\/+)$/,"")},n.getDefaultACL=function(t){return{accessControlList:[{service:"bce:bos",region:"*",effect:"Allow",resource:[t+"/*"],permission:["READ","WRITE"]}]}},n.uuid=function(){var t=(Math.random()*Math.pow(2,32)).toString(36);return"u-"+(new Date).getTime()+"-"+t},n.generateLocalKey=function(t,e){return"default"===e?s.Q.resolve([t.blob.name,t.blob.size,t.chunkSize,t.bucket,t.object].join("&")):"md5"===e?n.md5sum(t.blob).then(function(e){return[e,t.blob.name,t.blob.size,t.chunkSize,t.bucket,t.object].join("&")}):s.Q.resolve(null)},n.md5sum=function(t){function e(){var e=2097152*i;a.readAsArrayBuffer(n.call(t,e,e+2097152>=t.size?t.size:e+2097152))}var n=File.prototype.slice||File.prototype.mozSlice||File.prototype.webkitSlice,r=Math.ceil(t.size/2097152),i=0,o=new c.ArrayBuffer,a=new FileReader,f=s.Q.defer();return a.onload=function(t){o.append(t.target.result),i++,r>i?e():f.resolve(o.end())},a.onerror=function(t){f.reject(t)},e(),f.promise},n.getDefaultPolicy=function(t){if(null==t)return null;var e=(new Date).getTime();return{expiration:new Date(e+864e5).toISOString().replace(/\.\d+Z$/,"Z"),conditions:[{bucket:t}]}},n.getUploadId=function(t){return localStorage.getItem(t)},n.setUploadId=function(t,e){localStorage.setItem(t,e)},n.removeUploadId=function(t){localStorage.removeItem(t)},n.filterTasks=function(t,e){a.each(t,function(t){var n=r(t.partNumber,e);n&&(t.etag=n)})},n.expandAccept=function(t){var e=[];return a.isArray(t)?a.each(t,function(t){t.extensions&&e.push.apply(e,t.extensions.split(","))}):a.isString(t)&&(e=t.split(",")),t=a.uniq(a.map(e,function(t){return-1!==t.indexOf("/")?t:s.MimeType.guess(t)})),e=a.filter(e,function(t){return-1===t.indexOf("/")}),t.concat(e).join(",")},n.expandAcceptToArray=function(t){return!t||a.isArray(t)?t:a.isString(t)?[{title:"All files",extensions:t}]:[]},n.transformUrl=function(t){return t.replace(/(https?:)\/\/([^\/]+)\/([^\/]+)\/([^\/]+)/,function(t,e,n,r,i){return/^v\d$/.test(r)?e+"//"+i+"."+n+"/"+r:e+"//"+r+"."+n+"/"+i})},n.isBlob=function(t){var e;if("undefined"!=typeof Blob)e=Blob;else{if("undefined"==typeof mOxie||!a.isFunction(mOxie.Blob))return!1;e=mOxie.Blob}return t instanceof e},n.now=function(){return(new Date).getTime()},n.toDHMS=function(t){var e=0,n=0,r=0;return t>=60&&(r=~~(t/60),t-=60*r),r>=60&&(n=~~(r/60),r-=60*n),n>=24&&(e=~~(n/24),n-=24*e),{DD:e,HH:n,MM:r,SS:t}},n.fixXhr=function(t,e){return function(r,a,c,f){var u=this,d=i.parse(f.endpoint).host;c.headers["x-bce-date"]=(new Date).toISOString().replace(/\.\d+Z$/,"Z"),c.headers.host=d,c.params[".stamp"]=(new Date).getTime();var p=r;"PUT"===r&&(r="POST");var h,l=r,b=c.body;"HEAD"===r?(h=n.normalizeEndpoint(t.bos_relay_server)+"/"+d+a,c.params.httpMethod=r,l="POST"):!0===e?(h=n.transformUrl(f.endpoint+a),c.headers.host=i.parse(h).host):h=f.endpoint+a,"POST"!==l||b||(b='{"FORCE_POST": true}');var m=s.Q.defer(),v=new mOxie.XMLHttpRequest;return v.onload=function(){var t=null;try{t=JSON.parse(v.response||"{}")}catch(e){t={}}200<=v.status&&300>v.status?"HEAD"===r?m.resolve(t):m.resolve({http_headers:{},body:t}):m.reject({status_code:v.status,message:t.message||"",code:t.code||"",request_id:t.requestId||""})},v.onerror=function(t){m.reject(t)},v.upload&&(v.upload.onprogress=function(t){"PUT"===p&&(t.lengthComputable=!0,u.emit("progress",t,{httpMethod:p,resource:a,args:c,config:f,xhr:v}))}),u.createSignature(u.config.credentials,r,a,c.params,c.headers).then(function(e,n){e&&(c.headers.authorization=e),n&&(c.headers["x-bce-date"]=n);var r=o.stringify(c.params);r&&(h+="?"+r),v.open(l,h,!0);for(var i in c.headers)c.headers.hasOwnProperty(i)&&"host"!==i&&v.setRequestHeader(i,c.headers[i]);v.send(b,{runtime_order:"flash",swf_url:t.flash_swf_url})})["catch"](function(t){m.reject(t)}),m.promise}},n.eachLimit=function(t,e,n,r){function i(){var t=c.dequeue();t&&(o++,n(t,function(t){o--,t?(s=a=!0,r(t)):c.isEmpty()||a?0>=o&&!s&&(s=!0,r()):setTimeout(i,0)}))}var o=0,a=!1,s=!1,c=new f(t);for(e=Math.min(e,c.size()),t=0;e>t;t++)i()},n.inherits=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},n.guessContentType=function(t,e){var n=t.type;return n||(n=t.name.split(/\./g).pop(),n=s.MimeType.guess(n)),e||/charset=/.test(n)||(n+="; charset=UTF-8"),n}},{123:123,144:144,147:147,148:148,160:160,166:166}],166:[function(t,e,n){n.Q=t(171),n.Auth=t(174),n.BosClient=t(178),n.BcsClient=t(177),n.BccClient=t(175),n.SesClient=t(193),n.QnsClient=t(192),n.LssClient=t(186),n.MctClient=t(187),n.FaceClient=t(182),n.OCRClient=t(191),n.MediaClient=t(188),n.HttpClient=t(185),n.MimeType=t(189),n.STS=t(195),n.VodClient=t(196),n.DocClient=t(181)},{171:171,174:174,175:175,177:177,178:178,181:181,182:182,185:185,186:186,187:187,188:188,189:189,191:191,192:192,193:193,195:195,196:196}],167:[function(t,e,n){(function(t,n){!function(){function r(){}function i(t){return t}function o(t){return!!t}function a(t){return!t}function s(t){return function(){if(null===t)throw Error("Callback was already called.");t.apply(this,arguments),t=null}}function c(t){return function(){null!==t&&(t.apply(this,arguments),t=null)}}function f(t){return z(t)||"number"==typeof t.length&&0<=t.length&&0===t.length%1}function u(t,e){for(var n=-1,r=t.length;++n<r;)e(t[n],n,t)}function d(t,e){for(var n=-1,r=t.length,i=Array(r);++n<r;)i[n]=e(t[n],n,t);return i}function p(t){return d(Array(t),function(t,e){return e})}function h(t,e,n){return u(t,function(t,r,i){n=e(n,t,r,i)}),n}function l(t,e){u(D(t),function(n){e(t[n],n)})}function b(t,e){for(var n=0;n<t.length;n++)if(t[n]===e)return n;return-1}function m(t){var e,n,r=-1;return f(t)?(e=t.length,function(){return r++,e>r?r:null}):(n=D(t),e=n.length,function(){return r++,e>r?n[r]:null})}function v(t,e){return e=null==e?t.length-1:+e,function(){for(var n=Math.max(arguments.length-e,0),r=Array(n),i=0;n>i;i++)r[i]=arguments[i+e];switch(e){case 0:return t.call(this,r);case 1:return t.call(this,arguments[0],r)}}}function y(t){return function(e,n,r){return t(e,r)}}function g(t){return function(e,n,i){i=c(i||r),e=e||[];var o=m(e);if(0>=t)return i(null);var a=!1,f=0,u=!1;!function d(){if(a&&0>=f)return i(null);for(;t>f&&!u;){var r=o();if(null===r){a=!0,0>=f&&i(null);break}f+=1,n(e[r],r,s(function(t){--f,t?(i(t),u=!0):d()}))}}()}}function _(t){return function(e,n,r){return t(P.eachOf,e,n,r)}}function w(t){return function(e,n,r,i){return t(g(n),e,r,i)}}function x(t){return function(e,n,r){return t(P.eachOfSeries,e,n,r)}}function k(t,e,n,i){i=c(i||r),e=e||[];var o=f(e)?[]:{};t(e,function(t,e,r){n(t,function(t,n){o[e]=n,r(t)})},function(t){i(t,o)})}function E(t,e,n,r){var i=[];t(e,function(t,e,r){n(t,function(n){n&&i.push({index:e,value:t}),r()})},function(){r(d(i.sort(function(t,e){return t.index-e.index}),function(t){return t.value}))})}function S(t,e,n,r){E(t,e,function(t,e){n(t,function(t){e(!t)})},r)}function M(t,e,n){return function(r,i,o,a){function s(){a&&a(n(!1,void 0))}function c(t,r,i){return a?void o(t,function(r){a&&e(r)&&(a(n(!0,t)),a=o=!1),i()}):i()}3<arguments.length?t(r,i,c,s):(a=o,o=i,t(r,c,s))}}function A(t,e){return e}function T(t,e,n){n=n||r;var i=f(e)?[]:{};t(e,function(t,e,n){t(v(function(t,r){1>=r.length&&(r=r[0]),i[e]=r,n(t)}))},function(t){n(t,i)})}function j(t,e,n,r){var i=[];t(e,function(t,e,r){n(t,function(t,e){i=i.concat(e||[]),r(t)})},function(t){r(t,i)})}function I(t,e,n){function i(t,e,n,i){if(null!=i&&"function"!=typeof i)throw Error("task callback must be a function");return t.started=!0,z(e)||(e=[e]),0===e.length&&t.idle()?P.setImmediate(function(){t.drain()}):(u(e,function(e){e={data:e,callback:i||r},n?t.tasks.unshift(e):t.tasks.push(e),t.tasks.length===t.concurrency&&t.saturated()}),void P.setImmediate(t.process))}function o(t,e){return function(){--a;var n=!1,r=arguments;u(e,function(t){u(c,function(e,r){e!==t||n||(c.splice(r,1),n=!0)}),t.callback.apply(t,r)}),0===t.tasks.length+a&&t.drain(),t.process()}}if(null==e)e=1;else if(0===e)throw Error("Concurrency must not be zero");var a=0,c=[],f={tasks:[],concurrency:e,payload:n,saturated:r,empty:r,drain:r,started:!1,paused:!1,push:function(t,e){i(f,t,!1,e)},kill:function(){f.drain=r,f.tasks=[]},unshift:function(t,e){i(f,t,!0,e)},process:function(){for(;!f.paused&&a<f.concurrency&&f.tasks.length;){var e=f.payload?f.tasks.splice(0,f.payload):f.tasks.splice(0,f.tasks.length),n=d(e,function(t){return t.data});0===f.tasks.length&&f.empty(),a+=1,c.push(e[0]),e=s(o(f,e)),t(n,e)}},length:function(){return f.tasks.length},running:function(){return a},workersList:function(){return c},idle:function(){return 0===f.tasks.length+a},pause:function(){f.paused=!0},resume:function(){if(!1!==f.paused){f.paused=!1;for(var t=Math.min(f.concurrency,f.tasks.length),e=1;t>=e;e++)P.setImmediate(f.process)}}};return f}function R(t){return v(function(e,n){e.apply(null,n.concat([v(function(e,n){"object"==typeof console&&(e?console.error&&console.error(e):console[t]&&u(n,function(e){console[t](e)}))})]))})}function O(t){return function(e,n,r){t(p(e),n,r)}}function B(t){return v(function(e,n){var r=v(function(n){var r=this,i=n.pop();return t(e,function(t,e,i){t.apply(r,n.concat([i]))},i)});return n.length?r.apply(this,n):r})}function N(t){return v(function(e){var n=e.pop();e.push(function(){var t=arguments;r?P.setImmediate(function(){n.apply(null,t)}):n.apply(null,t)});var r=!0;t.apply(this,e),r=!1})}var C,P={},q="object"==typeof self&&self.self===self&&self||"object"==typeof n&&n.global===n&&n||this;null!=q&&(C=q.async),P.noConflict=function(){return q.async=C,P};var U=Object.prototype.toString,z=Array.isArray||function(t){return"[object Array]"===U.call(t)},L=function(t){var e=typeof t;return"function"===e||"object"===e&&!!t},D=Object.keys||function(t){var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n},F="function"==typeof setImmediate&&setImmediate,H=F?function(t){F(t)}:function(t){setTimeout(t,0)};P.nextTick="object"==typeof t&&"function"==typeof t.nextTick?t.nextTick:H,P.setImmediate=F?H:P.nextTick,P.forEach=P.each=function(t,e,n){return P.eachOf(t,y(e),n)},P.forEachSeries=P.eachSeries=function(t,e,n){return P.eachOfSeries(t,y(e),n)},P.forEachLimit=P.eachLimit=function(t,e,n,r){return g(e)(t,y(n),r)},P.forEachOf=P.eachOf=function(t,e,n){function i(t){f--,t?n(t):null===o&&0>=f&&n(null)}n=c(n||r),t=t||[];for(var o,a=m(t),f=0;null!=(o=a());)f+=1,e(t[o],o,s(i));0===f&&n(null)},P.forEachOfSeries=P.eachOfSeries=function(t,e,n){function i(){var r=!0;return null===a?n(null):(e(t[a],a,s(function(t){if(t)n(t);else{if(a=o(),null===a)return n(null);r?P.setImmediate(i):i()}})),void(r=!1))}n=c(n||r),t=t||[];var o=m(t),a=o();i()},P.forEachOfLimit=P.eachOfLimit=function(t,e,n,r){g(e)(t,n,r)},P.map=_(k),P.mapSeries=x(k),P.mapLimit=w(k),P.inject=P.foldl=P.reduce=function(t,e,n,r){P.eachOfSeries(t,function(t,r,i){n(e,t,function(t,n){e=n,i(t)})},function(t){r(t,e)})},P.foldr=P.reduceRight=function(t,e,n,r){t=d(t,i).reverse(),P.reduce(t,e,n,r)},P.transform=function(t,e,n,r){3===arguments.length&&(r=n,n=e,e=z(t)?[]:{}),P.eachOf(t,function(t,r,i){n(e,t,r,i)},function(t){r(t,e)})},P.select=P.filter=_(E),P.selectLimit=P.filterLimit=w(E),P.selectSeries=P.filterSeries=x(E),P.reject=_(S),P.rejectLimit=w(S),P.rejectSeries=x(S),P.any=P.some=M(P.eachOf,o,i),P.someLimit=M(P.eachOfLimit,o,i),P.all=P.every=M(P.eachOf,a,a),P.everyLimit=M(P.eachOfLimit,a,a),P.detect=M(P.eachOf,i,A),P.detectSeries=M(P.eachOfSeries,i,A),P.detectLimit=M(P.eachOfLimit,i,A),P.sortBy=function(t,e,n){function r(t,e){var n=t.criteria,r=e.criteria;return r>n?-1:n>r?1:0}P.map(t,function(t,n){e(t,function(e,r){e?n(e):n(null,{value:t,criteria:r})})},function(t,e){return t?n(t):void n(null,d(e.sort(r),function(t){return t.value}))})},P.auto=function(t,e,n){function i(){a--,u(p.slice(0),function(t){t()})}"function"==typeof e&&(n=e,e=null),n=c(n||r);var o=D(t),a=o.length;if(!a)return n(null);e||(e=a);var s={},f=0,d=!1,p=[];p.unshift(function(){a||n(null,s)}),u(o,function(r){function o(){return e>f&&h(y,function(t,e){return t&&s.hasOwnProperty(e)},!0)&&!s.hasOwnProperty(r)}function a(){if(o()){f++;var t=b(p,a);t>=0&&p.splice(t,1),u[u.length-1](m,s)}}if(!d){for(var c,u=z(t[r])?t[r]:[t[r]],m=v(function(t,e){if(f--,1>=e.length&&(e=e[0]),t){var o={};l(s,function(t,e){o[e]=t}),o[r]=e,d=!0,n(t,o)}else s[r]=e,P.setImmediate(i)}),y=u.slice(0,u.length-1),g=y.length;g--;){if(!(c=t[y[g]]))throw Error("Has nonexistent dependency in "+y.join(", "));if(z(c)&&0<=b(c,r))throw Error("Has cyclic dependencies")}o()?(f++,u[u.length-1](m,s)):p.unshift(a)}})},P.retry=function(t,e,n){function r(t,e){if("number"==typeof e)t.times=parseInt(e,10)||5;else{if("object"!=typeof e)throw Error("Unsupported argument type for 'times': "+typeof e);t.times=parseInt(e.times,10)||5,t.interval=parseInt(e.interval,10)||0}}function i(t,e){function n(t,n){return function(r){t(function(t,e){r(!t||n,{err:t,result:e})},e)}}function r(t){return function(e){setTimeout(function(){e(null)},t)}}for(;a.times;){var i=!--a.times;o.push(n(a.task,i)),!i&&0<a.interval&&o.push(r(a.interval))}P.series(o,function(e,n){n=n[n.length-1],(t||a.callback)(n.err,n.result)})}var o=[],a={times:5,interval:0},s=arguments.length;if(1>s||s>3)throw Error("Invalid arguments - must be either (task), (task, callback), (times, task) or (times, task, callback)");return 2>=s&&"function"==typeof t&&(n=e,e=t),"function"!=typeof t&&r(a,t),a.callback=n,a.task=e,a.callback?i():i},P.waterfall=function(t,e){function n(t){return v(function(r,i){if(r)e.apply(null,[r].concat(i));else{var o=t.next();o?i.push(n(o)):i.push(e),N(t).apply(null,i)}})}return e=c(e||r),z(t)?t.length?void n(P.iterator(t))():e():e(Error("First argument to waterfall must be an array of functions"))},P.parallel=function(t,e){T(P.eachOf,t,e)},P.parallelLimit=function(t,e,n){T(g(e),t,n)},P.series=function(t,e){T(P.eachOfSeries,t,e)},P.iterator=function(t){function e(n){function r(){return t.length&&t[n].apply(null,arguments),r.next()}return r.next=function(){return n<t.length-1?e(n+1):null},r}return e(0)},P.apply=v(function(t,e){return v(function(n){return t.apply(null,e.concat(n))})}),P.concat=_(j),P.concatSeries=x(j),P.whilst=function(t,e,n){if(n=n||r,t()){var i=v(function(r,o){r?n(r):t.apply(this,o)?e(i):n.apply(null,[null].concat(o))});e(i)}else n(null)},P.doWhilst=function(t,e,n){var r=0;return P.whilst(function(){return 1>=++r||e.apply(this,arguments)},t,n)},P.until=function(t,e,n){return P.whilst(function(){return!t.apply(this,arguments)},e,n)},P.doUntil=function(t,e,n){return P.doWhilst(t,function(){return!e.apply(this,arguments)},n)},P.during=function(t,e,n){n=n||r;var i=v(function(e,r){e?n(e):(r.push(o),t.apply(this,r))}),o=function(t,r){t?n(t):r?e(i):n(null)};t(o)},P.doDuring=function(t,e,n){var r=0;P.during(function(t){1>r++?t(null,!0):e.apply(this,arguments)},t,n)},P.queue=function(t,e){return I(function(e,n){t(e[0],n)},e,1)},P.priorityQueue=function(t,e){function n(t,e){return t.priority-e.priority}function i(t,e,n){for(var r=-1,i=t.length-1;i>r;){var o=r+(i-r+1>>>1);0<=n(e,t[o])?r=o:i=o-1}return r}function o(t,e,o,a){if(null!=a&&"function"!=typeof a)throw Error("task callback must be a function");return t.started=!0,z(e)||(e=[e]),0===e.length?P.setImmediate(function(){t.drain()}):void u(e,function(e){e={data:e,priority:o,callback:"function"==typeof a?a:r},t.tasks.splice(i(t.tasks,e,n)+1,0,e),t.tasks.length===t.concurrency&&t.saturated(),P.setImmediate(t.process)})}var a=P.queue(t,e);return a.push=function(t,e,n){o(a,t,e,n)},delete a.unshift,a},P.cargo=function(t,e){return I(t,1,e)},P.log=R("log"),P.dir=R("dir"),P.memoize=function(t,e){var n={},r={},o=Object.prototype.hasOwnProperty;e=e||i;var a=v(function(i){var a=i.pop(),s=e.apply(null,i);o.call(n,s)?P.setImmediate(function(){a.apply(null,n[s])}):o.call(r,s)?r[s].push(a):(r[s]=[a],t.apply(null,i.concat([v(function(t){n[s]=t;var e=r[s];delete r[s];for(var i=0,o=e.length;o>i;i++)e[i].apply(null,t)})])))});return a.memo=n,a.unmemoized=t,a},P.unmemoize=function(t){return function(){return(t.unmemoized||t).apply(null,arguments)}},P.times=O(P.map),P.timesSeries=O(P.mapSeries),P.timesLimit=function(t,e,n,r){return P.mapLimit(p(t),e,n,r)},P.seq=function(){var t=arguments;return v(function(e){var n=this,i=e[e.length-1];"function"==typeof i?e.pop():i=r,P.reduce(t,e,function(t,e,r){e.apply(n,t.concat([v(function(t,e){r(t,e)})]))},function(t,e){i.apply(n,[t].concat(e))})})},P.compose=function(){return P.seq.apply(null,Array.prototype.reverse.call(arguments))},P.applyEach=B(P.eachOf),P.applyEachSeries=B(P.eachOfSeries),P.forever=function(t,e){function n(t){return t?i(t):void o(n)}var i=s(e||r),o=N(t);n()},P.ensureAsync=N,P.constant=v(function(t){var e=[null].concat(t);return function(t){return t.apply(this,e)}}),P.wrapSync=P.asyncify=function(t){return v(function(e){var n,r=e.pop();try{n=t.apply(this,e)}catch(i){return r(i)}L(n)&&"function"==typeof n.then?n.then(function(t){r(null,t)})["catch"](function(t){r(t.message?t:Error(t))}):r(null,n)})},"object"==typeof e&&e.exports?e.exports=P:q.async=P}()}).call(this,t(113),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{113:113}],168:[function(t,e,n){arguments[4][58][0].apply(n,arguments)},{169:169,58:58}],169:[function(t,e,n){arguments[4][59][0].apply(n,arguments)},{170:170,59:59}],170:[function(t,e,n){arguments[4][106][0].apply(n,arguments)},{106:106}],171:[function(t,e,n){(function(t){!function(t){if("function"==typeof bootstrap)bootstrap("promise",t);else if("object"==typeof n&&"object"==typeof e)e.exports=t();else if("undefined"!=typeof ses)ses.ok()&&(ses.makeQ=t);else{if("undefined"==typeof self)throw Error("This environment was not anticiapted by Q. Please file a bug.");self.Q=t()}}(function(){function e(t){return function(){return I.apply(t,arguments)}}function n(t,e){if(k&&e.stack&&"object"==typeof t&&null!==t&&t.stack&&-1===t.stack.indexOf("From previous event:")){for(var n=[],i=e;i;i=i.source)i.stack&&n.unshift(i.stack);n.unshift(t.stack);for(var n=n.join("\nFrom previous event:\n").split("\n"),i=[],o=0;o<n.length;++o){var a,s=n[o];if(a=r(s)){var c=a[1];a=a[0]===S&&c>=A&&F>=c}else a=!1;a||(a=s,a=-1!==a.indexOf("(module.js:")||-1!==a.indexOf("(node.js:")),a||!s||i.push(s)}n=i.join("\n"),t.stack=n}}function r(t){var e=/at .+ \((.+):(\d+):(?:\d+)\)$/.exec(t);return e||(e=/at ([^ ]+):(\d+):(?:\d+)$/.exec(t))?[e[1],Number(e[2])]:(t=/.*@(.+):(\d+)$/.exec(t))?[t[1],Number(t[2])]:void 0}function i(){if(k)try{throw Error()}catch(t){var e=t.stack.split("\n"),e=0<e[0].indexOf("@")?e[1]:e[2];if(e=r(e))return S=e[0],e[1]}}function o(t){return t instanceof f?t:h(t)?y(t):v(t)}function a(){function t(t){e=t,s.source=t,O(n,function(e,n){o.nextTick(function(){t.promiseDispatch.apply(t,n)})},void 0),r=n=void 0}var e,n=[],r=[],i=C(a.prototype),s=C(f.prototype);if(s.promiseDispatch=function(t,i,a){var s=R(arguments);n?(n.push(s),"when"===i&&a[1]&&r.push(a[1])):o.nextTick(function(){e.promiseDispatch.apply(e,s)})},s.valueOf=function(){if(n)return s;var t=d(e);return p(t)&&(e=t),t},s.inspect=function(){return e?e.inspect():{state:"pending"}},o.longStackSupport&&k)try{throw Error()}catch(c){s.stack=c.stack.substring(c.stack.indexOf("\n")+1)}return i.promise=s,i.resolve=function(n){e||t(o(n))},i.fulfill=function(n){e||t(v(n))},i.reject=function(n){e||t(m(n))},i.notify=function(t){e||O(r,function(e,n){o.nextTick(function(){n(t)})},void 0)},i}function s(t){if("function"!=typeof t)throw new TypeError("resolver must be a function.");var e=a();try{t(e.resolve,e.reject,e.notify)}catch(n){e.reject(n)}return e.promise}function c(t){return s(function(e,n){for(var r=0,i=t.length;i>r;r++)o(t[r]).then(e,n)})}function f(t,e,n){void 0===e&&(e=function(t){return m(Error("Promise does not support operation: "+t))}),void 0===n&&(n=function(){return{state:"unknown"}});var r=C(f.prototype);if(r.promiseDispatch=function(n,i,o){var a;try{a=t[i]?t[i].apply(r,o):e.call(r,i,o)}catch(s){a=m(s)}n&&n(a)},r.inspect=n){var i=n();"rejected"===i.state&&(r.exception=i.reason),r.valueOf=function(){var t=n();return"pending"===t.state||"rejected"===t.state?r:t.value}}return r}function u(t,e,n,r){return o(t).then(e,n,r)}function d(t){if(p(t)){var e=t.inspect();if("fulfilled"===e.state)return e.value}return t}function p(t){return t instanceof f}function h(t){return t===Object(t)&&"function"==typeof t.then}function l(){z.length=0,L.length=0,D||(D=!0)}function b(t,e){D&&(L.push(t),e&&"undefined"!=typeof e.stack?z.push(e.stack):z.push("(no stack) "+e))}function m(t){var e=f({when:function(e){if(e&&D){var n=B(L,this);-1!==n&&(L.splice(n,1),z.splice(n,1))}return e?e(t):this}},function(){return this},function(){return{state:"rejected",reason:t}});return b(e,t),e}function v(t){return f({when:function(){return t},get:function(e){return t[e]},set:function(e,n){t[e]=n},"delete":function(e){delete t[e]},post:function(e,n){return null===e||void 0===e?t.apply(void 0,n):t[e].apply(t,n)},apply:function(e,n){return t.apply(e,n)},keys:function(){return q(t)}},void 0,function(){return{state:"fulfilled",value:t}})}function y(t){var e=a();return o.nextTick(function(){try{t.then(e.resolve,e.reject,e.notify)}catch(n){e.reject(n)}}),e.promise}function g(t,e,n){return o(t).spread(e,n)}function _(t,e,n){return o(t).dispatch(e,n)}function w(t){return u(t,function(t){var e=0,n=a();return O(t,function(r,i,o){var a;p(i)&&"fulfilled"===(a=i.inspect()).state?t[o]=a.value:(++e,u(i,function(r){t[o]=r,0===--e&&n.resolve(t)},n.reject,function(t){n.notify({index:o,value:t})}))},void 0),0===e&&n.resolve(t),n.promise})}function x(t){return u(t,function(t){return t=N(t,o),u(w(N(t,function(t){return u(t,T,T)})),function(){return t})})}var k=!1;try{throw Error()}catch(E){k=!!E.stack}var S,M,A=i(),T=function(){},j=function(){function e(){for(;n.next;){n=n.next;var t=n.task;n.task=void 0;var r=n.domain;r&&(n.domain=void 0,r.enter());try{t()}catch(o){if(a)throw r&&r.exit(),setTimeout(e,0),r&&r.enter(),o;setTimeout(function(){throw o},0)}r&&r.exit()}i=!1}var n={task:void 0,next:null},r=n,i=!1,o=void 0,a=!1;if(j=function(e){r=r.next={task:e,domain:a&&t.domain,next:null},i||(i=!0,o())},"undefined"!=typeof t&&t.nextTick)a=!0,o=function(){t.nextTick(e)};else if("function"==typeof setImmediate)o="undefined"!=typeof window?setImmediate.bind(window,e):function(){setImmediate(e)};else if("undefined"!=typeof MessageChannel){var s=new MessageChannel;s.port1.onmessage=function(){o=c,s.port1.onmessage=e,e()};var c=function(){s.port2.postMessage(0)},o=function(){setTimeout(e,0),c()}}else o=function(){setTimeout(e,0)};return j}(),I=Function.call,R=e(Array.prototype.slice),O=e(Array.prototype.reduce||function(t,e){var n=0,r=this.length;if(1===arguments.length)for(;;){if(n in this){e=this[n++];break}if(++n>=r)throw new TypeError}for(;r>n;n++)n in this&&(e=t(e,this[n],n));return e}),B=e(Array.prototype.indexOf||function(t){for(var e=0;e<this.length;e++)if(this[e]===t)return e;return-1}),N=e(Array.prototype.map||function(t,e){var n=this,r=[];return O(n,function(i,o,a){r.push(t.call(e,o,a,n))},void 0),r}),C=Object.create||function(t){function e(){}return e.prototype=t,new e},P=e(Object.prototype.hasOwnProperty),q=Object.keys||function(t){var e,n=[];for(e in t)P(t,e)&&n.push(e);return n},U=e(Object.prototype.toString);M="undefined"!=typeof ReturnValue?ReturnValue:function(t){this.value=t},o.resolve=o,o.nextTick=j,o.longStackSupport=!1,"object"==typeof t&&t&&t.env&&t.env.Q_DEBUG&&(o.longStackSupport=!0),o.defer=a,a.prototype.makeNodeResolver=function(){var t=this;return function(e,n){e?t.reject(e):2<arguments.length?t.resolve(R(arguments,1)):t.resolve(n)}},o.Promise=s,o.promise=s,s.race=c,s.all=w,s.reject=m,s.resolve=o,o.passByCopy=function(t){return t},f.prototype.passByCopy=function(){return this},o.join=function(t,e){return o(t).join(e)},f.prototype.join=function(t){return o([this,t]).spread(function(t,e){if(t===e)return t;throw Error("Can't join: not the same: "+t+" "+e)})},o.race=c,f.prototype.race=function(){return this.then(o.race)},o.makePromise=f,f.prototype.toString=function(){return"[object Promise]"},f.prototype.then=function(t,e,r){function i(e){try{return"function"==typeof t?t(e):e;
}catch(n){return m(n)}}function s(t){if("function"==typeof e){n(t,c);try{return e(t)}catch(r){return m(r)}}return m(t)}var c=this,f=a(),u=!1;return o.nextTick(function(){c.promiseDispatch(function(t){u||(u=!0,f.resolve(i(t)))},"when",[function(t){u||(u=!0,f.resolve(s(t)))}])}),c.promiseDispatch(void 0,"when",[void 0,function(t){var e,n=!1;try{e="function"==typeof r?r(t):t}catch(i){if(n=!0,!o.onerror)throw i;o.onerror(i)}n||f.notify(e)}]),f.promise},o.tap=function(t,e){return o(t).tap(e)},f.prototype.tap=function(t){return t=o(t),this.then(function(e){return t.fcall(e).thenResolve(e)})},o.when=u,f.prototype.thenResolve=function(t){return this.then(function(){return t})},o.thenResolve=function(t,e){return o(t).thenResolve(e)},f.prototype.thenReject=function(t){return this.then(function(){throw t})},o.thenReject=function(t,e){return o(t).thenReject(e)},o.nearer=d,o.isPromise=p,o.isPromiseAlike=h,o.isPending=function(t){return p(t)&&"pending"===t.inspect().state},f.prototype.isPending=function(){return"pending"===this.inspect().state},o.isFulfilled=function(t){return!p(t)||"fulfilled"===t.inspect().state},f.prototype.isFulfilled=function(){return"fulfilled"===this.inspect().state},o.isRejected=function(t){return p(t)&&"rejected"===t.inspect().state},f.prototype.isRejected=function(){return"rejected"===this.inspect().state};var z=[],L=[],D=!0;o.resetUnhandledRejections=l,o.getUnhandledReasons=function(){return z.slice()},o.stopUnhandledRejectionTracking=function(){l(),D=!1},l(),o.reject=m,o.fulfill=v,o.master=function(t){return f({isDef:function(){}},function(e,n){return _(t,e,n)},function(){return o(t).inspect()})},o.spread=g,f.prototype.spread=function(t,e){return this.all().then(function(e){return t.apply(void 0,e)},e)},o.async=function(t){return function(){function e(t,e){var a;if("undefined"==typeof StopIteration){try{a=n[t](e)}catch(s){return m(s)}return a.done?o(a.value):u(a.value,r,i)}try{a=n[t](e)}catch(s){return"[object StopIteration]"===U(s)||s instanceof M?o(s.value):m(s)}return u(a,r,i)}var n=t.apply(this,arguments),r=e.bind(e,"next"),i=e.bind(e,"throw");return r()}},o.spawn=function(t){o.done(o.async(t)())},o["return"]=function(t){throw new M(t)},o.promised=function(t){return function(){return g([this,w(arguments)],function(e,n){return t.apply(e,n)})}},o.dispatch=_,f.prototype.dispatch=function(t,e){var n=this,r=a();return o.nextTick(function(){n.promiseDispatch(r.resolve,t,e)}),r.promise},o.get=function(t,e){return o(t).dispatch("get",[e])},f.prototype.get=function(t){return this.dispatch("get",[t])},o.set=function(t,e,n){return o(t).dispatch("set",[e,n])},f.prototype.set=function(t,e){return this.dispatch("set",[t,e])},o.del=o["delete"]=function(t,e){return o(t).dispatch("delete",[e])},f.prototype.del=f.prototype["delete"]=function(t){return this.dispatch("delete",[t])},o.mapply=o.post=function(t,e,n){return o(t).dispatch("post",[e,n])},f.prototype.mapply=f.prototype.post=function(t,e){return this.dispatch("post",[t,e])},o.send=o.mcall=o.invoke=function(t,e){return o(t).dispatch("post",[e,R(arguments,2)])},f.prototype.send=f.prototype.mcall=f.prototype.invoke=function(t){return this.dispatch("post",[t,R(arguments,1)])},o.fapply=function(t,e){return o(t).dispatch("apply",[void 0,e])},f.prototype.fapply=function(t){return this.dispatch("apply",[void 0,t])},o["try"]=o.fcall=function(t){return o(t).dispatch("apply",[void 0,R(arguments,1)])},f.prototype.fcall=function(){return this.dispatch("apply",[void 0,R(arguments)])},o.fbind=function(t){var e=o(t),n=R(arguments,1);return function(){return e.dispatch("apply",[this,n.concat(R(arguments))])}},f.prototype.fbind=function(){var t=this,e=R(arguments);return function(){return t.dispatch("apply",[this,e.concat(R(arguments))])}},o.keys=function(t){return o(t).dispatch("keys",[])},f.prototype.keys=function(){return this.dispatch("keys",[])},o.all=w,f.prototype.all=function(){return w(this)},o.allResolved=function(t,e,n){return function(){return"undefined"!=typeof console&&"function"==typeof console.warn&&console.warn(e+" is deprecated, use "+n+" instead.",Error("").stack),t.apply(t,arguments)}}(x,"allResolved","allSettled"),f.prototype.allResolved=function(){return x(this)},o.allSettled=function(t){return o(t).allSettled()},f.prototype.allSettled=function(){return this.then(function(t){return w(N(t,function(t){function e(){return t.inspect()}return t=o(t),t.then(e,e)}))})},o.fail=o["catch"]=function(t,e){return o(t).then(void 0,e)},f.prototype.fail=f.prototype["catch"]=function(t){return this.then(void 0,t)},o.progress=function(t,e){return o(t).then(void 0,void 0,e)},f.prototype.progress=function(t){return this.then(void 0,void 0,t)},o.fin=o["finally"]=function(t,e){return o(t)["finally"](e)},f.prototype.fin=f.prototype["finally"]=function(t){return t=o(t),this.then(function(e){return t.fcall().then(function(){return e})},function(e){return t.fcall().then(function(){throw e})})},o.done=function(t,e,n,r){return o(t).done(e,n,r)},f.prototype.done=function(e,r,i){var a=function(t){o.nextTick(function(){if(n(t,s),!o.onerror)throw t;o.onerror(t)})},s=e||r||i?this.then(e,r,i):this;"object"==typeof t&&t&&t.domain&&(a=t.domain.bind(a)),s.then(void 0,a)},o.timeout=function(t,e,n){return o(t).timeout(e,n)},f.prototype.timeout=function(t,e){var n=a(),r=setTimeout(function(){e&&"string"!=typeof e||(e=Error(e||"Timed out after "+t+" ms"),e.code="ETIMEDOUT"),n.reject(e)},t);return this.then(function(t){clearTimeout(r),n.resolve(t)},function(t){clearTimeout(r),n.reject(t)},n.notify),n.promise},o.delay=function(t,e){return void 0===e&&(e=t,t=void 0),o(t).delay(e)},f.prototype.delay=function(t){return this.then(function(e){var n=a();return setTimeout(function(){n.resolve(e)},t),n.promise})},o.nfapply=function(t,e){return o(t).nfapply(e)},f.prototype.nfapply=function(t){var e=a();return t=R(t),t.push(e.makeNodeResolver()),this.fapply(t).fail(e.reject),e.promise},o.nfcall=function(t){var e=R(arguments,1);return o(t).nfapply(e)},f.prototype.nfcall=function(){var t=R(arguments),e=a();return t.push(e.makeNodeResolver()),this.fapply(t).fail(e.reject),e.promise},o.nfbind=o.denodeify=function(t){var e=R(arguments,1);return function(){var n=e.concat(R(arguments)),r=a();return n.push(r.makeNodeResolver()),o(t).fapply(n).fail(r.reject),r.promise}},f.prototype.nfbind=f.prototype.denodeify=function(){var t=R(arguments);return t.unshift(this),o.denodeify.apply(void 0,t)},o.nbind=function(t,e){var n=R(arguments,2);return function(){var r=n.concat(R(arguments)),i=a();return r.push(i.makeNodeResolver()),o(function(){return t.apply(e,arguments)}).fapply(r).fail(i.reject),i.promise}},f.prototype.nbind=function(){var t=R(arguments,0);return t.unshift(this),o.nbind.apply(void 0,t)},o.nmapply=o.npost=function(t,e,n){return o(t).npost(e,n)},f.prototype.nmapply=f.prototype.npost=function(t,e){var n=R(e||[]),r=a();return n.push(r.makeNodeResolver()),this.dispatch("post",[t,n]).fail(r.reject),r.promise},o.nsend=o.nmcall=o.ninvoke=function(t,e){var n=R(arguments,2),r=a();return n.push(r.makeNodeResolver()),o(t).dispatch("post",[e,n]).fail(r.reject),r.promise},f.prototype.nsend=f.prototype.nmcall=f.prototype.ninvoke=function(t){var e=R(arguments,1),n=a();return e.push(n.makeNodeResolver()),this.dispatch("post",[t,e]).fail(n.reject),n.promise},o.nodeify=function(t,e){return o(t).nodeify(e)},f.prototype.nodeify=function(t){return t?void this.then(function(e){o.nextTick(function(){t(null,e)})},function(e){o.nextTick(function(){t(e)})}):this};var F=i();return o})}).call(this,t(113))},{113:113}],172:[function(t,e,n){(function(){var t=this,r=t._,i=Array.prototype,o=Object.prototype,a=i.push,s=i.slice,c=i.concat,f=o.toString,u=o.hasOwnProperty,o=Array.isArray,d=Object.keys,p=Function.prototype.bind,h=function(t){return t instanceof h?t:this instanceof h?void(this._wrapped=t):new h(t)};"undefined"!=typeof n?("undefined"!=typeof e&&e.exports&&(n=e.exports=h),n._=h):t._=h,h.VERSION="1.7.0";var l=function(t,e,n){if(void 0===e)return t;switch(null==n?3:n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,i){return t.call(e,n,r,i)};case 4:return function(n,r,i,o){return t.call(e,n,r,i,o)}}return function(){return t.apply(e,arguments)}};h.iteratee=function(t,e,n){return null==t?h.identity:h.isFunction(t)?l(t,e,n):h.isObject(t)?h.matches(t):h.property(t)},h.each=h.forEach=function(t,e,n){if(null==t)return t;e=l(e,n);var r=t.length;if(r===+r)for(n=0;r>n;n++)e(t[n],n,t);else{var i=h.keys(t);for(n=0,r=i.length;r>n;n++)e(t[i[n]],i[n],t)}return t},h.map=h.collect=function(t,e,n){if(null==t)return[];e=h.iteratee(e,n),n=t.length!==+t.length&&h.keys(t);for(var r,i=(n||t).length,o=Array(i),a=0;i>a;a++)r=n?n[a]:a,o[a]=e(t[r],r,t);return o},h.reduce=h.foldl=h.inject=function(t,e,n,r){null==t&&(t=[]),e=l(e,r,4);var i,o=t.length!==+t.length&&h.keys(t),a=(o||t).length,s=0;if(3>arguments.length){if(!a)throw new TypeError("Reduce of empty array with no initial value");n=t[o?o[s++]:s++]}for(;a>s;s++)i=o?o[s]:s,n=e(n,t[i],i,t);return n},h.reduceRight=h.foldr=function(t,e,n,r){null==t&&(t=[]),e=l(e,r,4);var i,o=t.length!==+t.length&&h.keys(t),a=(o||t).length;if(3>arguments.length){if(!a)throw new TypeError("Reduce of empty array with no initial value");n=t[o?o[--a]:--a]}for(;a--;)i=o?o[a]:a,n=e(n,t[i],i,t);return n},h.find=h.detect=function(t,e,n){var r;return e=h.iteratee(e,n),h.some(t,function(t,n,i){return e(t,n,i)?(r=t,!0):void 0}),r},h.filter=h.select=function(t,e,n){var r=[];return null==t?r:(e=h.iteratee(e,n),h.each(t,function(t,n,i){e(t,n,i)&&r.push(t)}),r)},h.reject=function(t,e,n){return h.filter(t,h.negate(h.iteratee(e)),n)},h.every=h.all=function(t,e,n){if(null==t)return!0;e=h.iteratee(e,n),n=t.length!==+t.length&&h.keys(t);var r,i,o=(n||t).length;for(r=0;o>r;r++)if(i=n?n[r]:r,!e(t[i],i,t))return!1;return!0},h.some=h.any=function(t,e,n){if(null==t)return!1;e=h.iteratee(e,n),n=t.length!==+t.length&&h.keys(t);var r,i,o=(n||t).length;for(r=0;o>r;r++)if(i=n?n[r]:r,e(t[i],i,t))return!0;return!1},h.contains=h.include=function(t,e){return null==t?!1:(t.length!==+t.length&&(t=h.values(t)),0<=h.indexOf(t,e))},h.invoke=function(t,e){var n=s.call(arguments,2),r=h.isFunction(e);return h.map(t,function(t){return(r?e:t[e]).apply(t,n)})},h.pluck=function(t,e){return h.map(t,h.property(e))},h.where=function(t,e){return h.filter(t,h.matches(e))},h.findWhere=function(t,e){return h.find(t,h.matches(e))},h.max=function(t,e,n){var r,i=-(1/0),o=-(1/0);if(null==e&&null!=t){t=t.length===+t.length?t:h.values(t);for(var a=0,s=t.length;s>a;a++)n=t[a],n>i&&(i=n)}else e=h.iteratee(e,n),h.each(t,function(t,n,a){r=e(t,n,a),(r>o||-(1/0)===r&&-(1/0)===i)&&(i=t,o=r)});return i},h.min=function(t,e,n){var r,i=1/0,o=1/0;if(null==e&&null!=t){t=t.length===+t.length?t:h.values(t);for(var a=0,s=t.length;s>a;a++)n=t[a],i>n&&(i=n)}else e=h.iteratee(e,n),h.each(t,function(t,n,a){r=e(t,n,a),(o>r||1/0===r&&1/0===i)&&(i=t,o=r)});return i},h.shuffle=function(t){t=t&&t.length===+t.length?t:h.values(t);for(var e,n=t.length,r=Array(n),i=0;n>i;i++)e=h.random(0,i),e!==i&&(r[i]=r[e]),r[e]=t[i];return r},h.sample=function(t,e,n){return null==e||n?(t.length!==+t.length&&(t=h.values(t)),t[h.random(t.length-1)]):h.shuffle(t).slice(0,Math.max(0,e))},h.sortBy=function(t,e,n){return e=h.iteratee(e,n),h.pluck(h.map(t,function(t,n,r){return{value:t,index:n,criteria:e(t,n,r)}}).sort(function(t,e){var n=t.criteria,r=e.criteria;if(n!==r){if(n>r||void 0===n)return 1;if(r>n||void 0===r)return-1}return t.index-e.index}),"value")};var b=function(t){return function(e,n,r){var i={};return n=h.iteratee(n,r),h.each(e,function(r,o){var a=n(r,o,e);t(i,r,a)}),i}};h.groupBy=b(function(t,e,n){h.has(t,n)?t[n].push(e):t[n]=[e]}),h.indexBy=b(function(t,e,n){t[n]=e}),h.countBy=b(function(t,e,n){h.has(t,n)?t[n]++:t[n]=1}),h.sortedIndex=function(t,e,n,r){n=h.iteratee(n,r,1),e=n(e),r=0;for(var i=t.length;i>r;){var o=r+i>>>1;n(t[o])<e?r=o+1:i=o}return r},h.toArray=function(t){return t?h.isArray(t)?s.call(t):t.length===+t.length?h.map(t,h.identity):h.values(t):[]},h.size=function(t){return null==t?0:t.length===+t.length?t.length:h.keys(t).length},h.partition=function(t,e,n){e=h.iteratee(e,n);var r=[],i=[];return h.each(t,function(t,n,o){(e(t,n,o)?r:i).push(t)}),[r,i]},h.first=h.head=h.take=function(t,e,n){return null==t?void 0:null==e||n?t[0]:0>e?[]:s.call(t,0,e)},h.initial=function(t,e,n){return s.call(t,0,Math.max(0,t.length-(null==e||n?1:e)))},h.last=function(t,e,n){return null==t?void 0:null==e||n?t[t.length-1]:s.call(t,Math.max(t.length-e,0))},h.rest=h.tail=h.drop=function(t,e,n){return s.call(t,null==e||n?1:e)},h.compact=function(t){return h.filter(t,h.identity)};var m=function(t,e,n,r){if(e&&h.every(t,h.isArray))return c.apply(r,t);for(var i=0,o=t.length;o>i;i++){var s=t[i];h.isArray(s)||h.isArguments(s)?e?a.apply(r,s):m(s,e,n,r):n||r.push(s)}return r};h.flatten=function(t,e){return m(t,e,!1,[])},h.without=function(t){return h.difference(t,s.call(arguments,1))},h.uniq=h.unique=function(t,e,n,r){if(null==t)return[];h.isBoolean(e)||(r=n,n=e,e=!1),null!=n&&(n=h.iteratee(n,r)),r=[];for(var i=[],o=0,a=t.length;a>o;o++){var s=t[o];if(e)o&&i===s||r.push(s),i=s;else if(n){var c=n(s,o,t);0>h.indexOf(i,c)&&(i.push(c),r.push(s))}else 0>h.indexOf(r,s)&&r.push(s)}return r},h.union=function(){return h.uniq(m(arguments,!0,!0,[]))},h.intersection=function(t){if(null==t)return[];for(var e=[],n=arguments.length,r=0,i=t.length;i>r;r++){var o=t[r];if(!h.contains(e,o)){for(var a=1;n>a&&h.contains(arguments[a],o);a++);a===n&&e.push(o)}}return e},h.difference=function(t){var e=m(s.call(arguments,1),!0,!0,[]);return h.filter(t,function(t){return!h.contains(e,t)})},h.zip=function(t){if(null==t)return[];for(var e=h.max(arguments,"length").length,n=Array(e),r=0;e>r;r++)n[r]=h.pluck(arguments,r);return n},h.object=function(t,e){if(null==t)return{};for(var n={},r=0,i=t.length;i>r;r++)e?n[t[r]]=e[r]:n[t[r][0]]=t[r][1];return n},h.indexOf=function(t,e,n){if(null==t)return-1;var r=0,i=t.length;if(n){if("number"!=typeof n)return r=h.sortedIndex(t,e),t[r]===e?r:-1;r=0>n?Math.max(0,i+n):n}for(;i>r;r++)if(t[r]===e)return r;return-1},h.lastIndexOf=function(t,e,n){if(null==t)return-1;var r=t.length;for("number"==typeof n&&(r=0>n?r+n+1:Math.min(r,n+1));0<=--r;)if(t[r]===e)return r;return-1},h.range=function(t,e,n){1>=arguments.length&&(e=t||0,t=0),n=n||1;for(var r=Math.max(Math.ceil((e-t)/n),0),i=Array(r),o=0;r>o;o++,t+=n)i[o]=t;return i};var v=function(){};h.bind=function(t,e){var n,r;if(p&&t.bind===p)return p.apply(t,s.call(arguments,1));if(!h.isFunction(t))throw new TypeError("Bind must be called on a function");return n=s.call(arguments,2),r=function(){if(!(this instanceof r))return t.apply(e,n.concat(s.call(arguments)));v.prototype=t.prototype;var i=new v;v.prototype=null;var o=t.apply(i,n.concat(s.call(arguments)));return h.isObject(o)?o:i}},h.partial=function(t){var e=s.call(arguments,1);return function(){for(var n=0,r=e.slice(),i=0,o=r.length;o>i;i++)r[i]===h&&(r[i]=arguments[n++]);for(;n<arguments.length;)r.push(arguments[n++]);return t.apply(this,r)}},h.bindAll=function(t){var e,n,r=arguments.length;if(1>=r)throw Error("bindAll must be passed function names");for(e=1;r>e;e++)n=arguments[e],t[n]=h.bind(t[n],t);return t},h.memoize=function(t,e){var n=function(r){var i=n.cache,o=e?e.apply(this,arguments):r;return h.has(i,o)||(i[o]=t.apply(this,arguments)),i[o]};return n.cache={},n},h.delay=function(t,e){var n=s.call(arguments,2);return setTimeout(function(){return t.apply(null,n)},e)},h.defer=function(t){return h.delay.apply(h,[t,1].concat(s.call(arguments,1)))},h.throttle=function(t,e,n){var r,i,o,a=null,s=0;n||(n={});var c=function(){s=!1===n.leading?0:h.now(),a=null,o=t.apply(r,i),a||(r=i=null)};return function(){var f=h.now();s||!1!==n.leading||(s=f);var u=e-(f-s);return r=this,i=arguments,0>=u||u>e?(clearTimeout(a),a=null,s=f,o=t.apply(r,i),a||(r=i=null)):a||!1===n.trailing||(a=setTimeout(c,u)),o}},h.debounce=function(t,e,n){var r,i,o,a,s,c=function(){var f=h.now()-a;e>f&&f>0?r=setTimeout(c,e-f):(r=null,n||(s=t.apply(o,i),r||(o=i=null)))};return function(){o=this,i=arguments,a=h.now();var f=n&&!r;return r||(r=setTimeout(c,e)),f&&(s=t.apply(o,i),o=i=null),s}},h.wrap=function(t,e){return h.partial(e,t)},h.negate=function(t){return function(){return!t.apply(this,arguments)}},h.compose=function(){var t=arguments,e=t.length-1;return function(){for(var n=e,r=t[e].apply(this,arguments);n--;)r=t[n].call(this,r);return r}},h.after=function(t,e){return function(){return 1>--t?e.apply(this,arguments):void 0}},h.before=function(t,e){var n;return function(){return 0<--t?n=e.apply(this,arguments):e=null,n}},h.once=h.partial(h.before,2),h.keys=function(t){if(!h.isObject(t))return[];if(d)return d(t);var e,n=[];for(e in t)h.has(t,e)&&n.push(e);return n},h.values=function(t){for(var e=h.keys(t),n=e.length,r=Array(n),i=0;n>i;i++)r[i]=t[e[i]];return r},h.pairs=function(t){for(var e=h.keys(t),n=e.length,r=Array(n),i=0;n>i;i++)r[i]=[e[i],t[e[i]]];return r},h.invert=function(t){for(var e={},n=h.keys(t),r=0,i=n.length;i>r;r++)e[t[n[r]]]=n[r];return e},h.functions=h.methods=function(t){var e,n=[];for(e in t)h.isFunction(t[e])&&n.push(e);return n.sort()},h.extend=function(t){if(!h.isObject(t))return t;for(var e,n,r=1,i=arguments.length;i>r;r++)for(n in e=arguments[r])u.call(e,n)&&(t[n]=e[n]);return t},h.pick=function(t,e,n){var r,i={};if(null==t)return i;if(h.isFunction(e))for(r in e=l(e,n),t){var o=t[r];e(o,r,t)&&(i[r]=o)}else{o=c.apply([],s.call(arguments,1)),t=Object(t);for(var a=0,f=o.length;f>a;a++)r=o[a],r in t&&(i[r]=t[r])}return i},h.omit=function(t,e,n){if(h.isFunction(e))e=h.negate(e);else{var r=h.map(c.apply([],s.call(arguments,1)),String);e=function(t,e){return!h.contains(r,e)}}return h.pick(t,e,n)},h.defaults=function(t){if(!h.isObject(t))return t;for(var e=1,n=arguments.length;n>e;e++){var r,i=arguments[e];for(r in i)void 0===t[r]&&(t[r]=i[r])}return t},h.clone=function(t){return h.isObject(t)?h.isArray(t)?t.slice():h.extend({},t):t},h.tap=function(t,e){return e(t),t};var y=function(t,e,n,r){if(t===e)return 0!==t||1/t===1/e;if(null==t||null==e)return t===e;t instanceof h&&(t=t._wrapped),e instanceof h&&(e=e._wrapped);var i=f.call(t);if(i!==f.call(e))return!1;switch(i){case"[object RegExp]":case"[object String]":return""+t==""+e;case"[object Number]":return+t!==+t?+e!==+e:0===+t?1/+t===1/e:+t===+e;case"[object Date]":case"[object Boolean]":return+t===+e}if("object"!=typeof t||"object"!=typeof e)return!1;for(var o=n.length;o--;)if(n[o]===t)return r[o]===e;var o=t.constructor,a=e.constructor;if(o!==a&&"constructor"in t&&"constructor"in e&&!(h.isFunction(o)&&o instanceof o&&h.isFunction(a)&&a instanceof a))return!1;if(n.push(t),r.push(e),"[object Array]"===i){if(i=t.length,a=i===e.length)for(;i--&&(a=y(t[i],e[i],n,r)););}else if(o=h.keys(t),i=o.length,a=h.keys(e).length===i)for(;i--&&(a=o[i],a=h.has(e,a)&&y(t[a],e[a],n,r)););return n.pop(),r.pop(),a};h.isEqual=function(t,e){return y(t,e,[],[])},h.isEmpty=function(t){if(null==t)return!0;if(h.isArray(t)||h.isString(t)||h.isArguments(t))return 0===t.length;for(var e in t)if(h.has(t,e))return!1;return!0},h.isElement=function(t){return!(!t||1!==t.nodeType)},h.isArray=o||function(t){return"[object Array]"===f.call(t)},h.isObject=function(t){var e=typeof t;return"function"===e||"object"===e&&!!t},h.each("Arguments Function String Number Date RegExp".split(" "),function(t){h["is"+t]=function(e){return f.call(e)==="[object "+t+"]"}}),h.isArguments(arguments)||(h.isArguments=function(t){return h.has(t,"callee")}),"function"!=typeof/./&&(h.isFunction=function(t){return"function"==typeof t||!1}),h.isFinite=function(t){return isFinite(t)&&!isNaN(parseFloat(t))},h.isNaN=function(t){return h.isNumber(t)&&t!==+t},h.isBoolean=function(t){return!0===t||!1===t||"[object Boolean]"===f.call(t)},h.isNull=function(t){return null===t},h.isUndefined=function(t){return void 0===t},h.has=function(t,e){return null!=t&&u.call(t,e)},h.noConflict=function(){return t._=r,this},h.identity=function(t){return t},h.constant=function(t){return function(){return t}},h.noop=function(){},h.property=function(t){return function(e){return e[t]}},h.matches=function(t){var e=h.pairs(t),n=e.length;return function(t){if(null==t)return!n;t=Object(t);for(var r=0;n>r;r++){var i=e[r],o=i[0];if(i[1]!==t[o]||!(o in t))return!1}return!0}},h.times=function(t,e,n){var r=Array(Math.max(0,t));for(e=l(e,n,1),n=0;t>n;n++)r[n]=e(n);return r},h.random=function(t,e){return null==e&&(e=t,t=0),t+Math.floor(Math.random()*(e-t+1))},h.now=Date.now||function(){return(new Date).getTime()};var o={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},b=h.invert(o),g=function(t){var e=function(e){return t[e]},n="(?:"+h.keys(t).join("|")+")",r=RegExp(n),i=RegExp(n,"g");return function(t){return t=null==t?"":""+t,r.test(t)?t.replace(i,e):t}};h.escape=g(o),h.unescape=g(b),h.result=function(t,e){if(null!=t){var n=t[e];return h.isFunction(n)?t[e]():n}};var _=0;h.uniqueId=function(t){var e=++_+"";return t?t+e:e},h.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var w=/(.)^/,x={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},k=/\\|'|\r|\n|\u2028|\u2029/g,E=function(t){return"\\"+x[t]};h.template=function(t,e,n){!e&&n&&(e=n),e=h.defaults({},e,h.templateSettings),n=RegExp([(e.escape||w).source,(e.interpolate||w).source,(e.evaluate||w).source].join("|")+"|$","g");var r=0,i="__p+='";t.replace(n,function(e,n,o,a,s){return i+=t.slice(r,s).replace(k,E),r=s+e.length,n?i+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'":o?i+="'+\n((__t=("+o+"))==null?'':__t)+\n'":a&&(i+="';\n"+a+"\n__p+='"),e}),i+="';\n",e.variable||(i="with(obj||{}){\n"+i+"}\n"),i="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+i+"return __p;\n";try{var o=new Function(e.variable||"obj","_",i)}catch(a){throw a.source=i,a}return n=function(t){return o.call(this,t,h)},n.source="function("+(e.variable||"obj")+"){\n"+i+"}",n},h.chain=function(t){return t=h(t),t._chain=!0,t};var S=function(t){return this._chain?h(t).chain():t};h.mixin=function(t){h.each(h.functions(t),function(e){var n=h[e]=t[e];h.prototype[e]=function(){var t=[this._wrapped];return a.apply(t,arguments),S.call(this,n.apply(h,t))}})},h.mixin(h),h.each("pop push reverse shift sort splice unshift".split(" "),function(t){var e=i[t];h.prototype[t]=function(){var n=this._wrapped;return e.apply(n,arguments),"shift"!==t&&"splice"!==t||0!==n.length||delete n[0],S.call(this,n)}}),h.each(["concat","join","slice"],function(t){var e=i[t];h.prototype[t]=function(){return S.call(this,e.apply(this._wrapped,arguments))}}),h.prototype.value=function(){return this._wrapped}}).call(this)},{}],173:[function(t,e,n){e.exports={name:"bce-sdk-js",version:"0.1.7",description:"Baidu Cloud Engine JavaScript SDK",main:"index.js",directories:{test:"test"},scripts:{test:"./test/run-all.sh",fecs:"fecs src",pack:"node_modules/.bin/browserify -s baidubce.sdk index.js -o baidubce-sdk.bundle.js && uglifyjs baidubce-sdk.bundle.js --compress --mangle -o baidubce-sdk.bundle.min.js"},repository:{type:"git",url:"https://github.com/baidubce/bce-sdk-js.git"},author:"<EMAIL>",license:"MIT",dependencies:{async:"^1.5.2",debug:"^2.2.0",q:"^1.1.2",underscore:"^1.7.0"},devDependencies:{browserify:"10.2.6",coveralls:"^2.11.8","expect.js":"^0.3.1",istanbul:"^0.4.2",mocha:"^2.4.5"}}},{}],174:[function(t,e,n){function r(t,e){this.ak=t,this.sk=e}var i=t(150),o=t(172),a=t(168)("bce-sdk:auth"),s=t(183),c=t(194);r.prototype.generateAuthorization=function(t,e,n,r,o,s,c){return o=i.format("bce-auth-v1/%s/%s/%d",this.ak,(o?new Date(1e3*o):new Date).toISOString().replace(/\.\d+Z$/,"Z"),s||1800),a("rawSessionKey = %j",o),s=this.hash(o,this.sk),e=this.uriCanonicalization(e),n=this.queryStringCanonicalization(n||{}),r=this.headersCanonicalization(r||{},c),c=r[0],r=r[1],a("canonicalUri = %j",e),a("canonicalQueryString = %j",n),a("canonicalHeaders = %j",c),a("signedHeaders = %j",r),t=i.format("%s\n%s\n%s\n%s",t,e,n,c),a("rawSignature = %j",t),a("sessionKey = %j",s),t=this.hash(t,s),r.length?i.format("%s/%s/%s",o,r.join(";"),t):i.format("%s//%s",o,t)},r.prototype.uriCanonicalization=function(t){return t},r.prototype.queryStringCanonicalization=function(t){var e=[];return Object.keys(t).forEach(function(n){n.toLowerCase()!==s.AUTHORIZATION.toLowerCase()&&e.push(n+"="+c.normalize(null==t[n]?"":t[n]))}),e.sort(),e.join("&")},r.prototype.headersCanonicalization=function(t,e){e&&e.length||(e=[s.HOST,s.CONTENT_MD5,s.CONTENT_LENGTH,s.CONTENT_TYPE]),a("headers = %j, headersToSign = %j",t,e);var n={};e.forEach(function(t){n[t.toLowerCase()]=!0});var r=[];Object.keys(t).forEach(function(e){var a=t[e],a=o.isString(a)?c.trim(a):a;null!=a&&""!==a&&(e=e.toLowerCase(),(/^x\-bce\-/.test(e)||!0===n[e])&&r.push(i.format("%s:%s",c.normalize(e),c.normalize(a))))}),r.sort();var f=[];return r.forEach(function(t){f.push(t.split(":")[0])}),[r.join("\n"),f]},r.prototype.hash=function(e,n){var r=t(57).createHmac("sha256",n);return r.update(e),r.digest("hex")},e.exports=r},{150:150,168:168,172:172,183:183,194:194,57:57}],175:[function(t,e,n){function r(t){s.call(this,t,"bcc",!0)}function i(){throw Error("unimplemented method")}n=t(150);var o=t(172),a=t(168)("bce-sdk:BccClient"),s=t(176);n.inherits(r,s),r.prototype.listInstances=function(t){t=t||{};var e=o.extend({maxKeys:1e3},o.pick(t,"maxKeys","marker"));return this.sendRequest("GET","/v1/instance",{params:e,config:t.config})},r.prototype.getPackages=function(t){return this.sendRequest("GET","/v1/instance/price",{config:(t||{}).config})},r.prototype.getImages=function(t){t=t||{};var e=o.extend({maxKeys:1e3,imageType:"All"},o.pick(t,"maxKeys","marker","imageType"));return this.sendRequest("GET","/v1/image",{config:t.config,params:e})},r.prototype.createInstance=function(t,e){var n=this;return this.getClientToken().then(function(r){var i=e||{};return r={clientToken:r.body.token},a("createInstance, params = %j, body = %j",r,t),n.sendRequest("POST","/v1/instance",{config:i.config,params:r,body:JSON.stringify(t)})})},r.prototype.getInstance=function(t,e){return this.sendRequest("GET","/v1/instance/"+t,{config:(e||{}).config})},r.prototype.startInstance=function(t,e){return this.sendRequest("PUT","/v1/instance/"+t,{params:{start:""},config:(e||{}).config})},r.prototype.stopInstance=function(t,e){return this.sendRequest("PUT","/v1/instance/"+t,{params:{stop:""},config:(e||{}).config})},r.prototype.restartInstance=function(t,e){return this.sendRequest("PUT","/v1/instance/"+t,{params:{reboot:""},config:(e||{}).config})},r.prototype.changeInstanceAdminPassword=i,r.prototype.rebuildInstance=i,r.prototype.deleteInstance=function(t,e){return this.sendRequest("DELETE","/v1/instance/"+t,{config:(e||{}).config})},r.prototype.joinSecurityGroup=i,r.prototype.leaveSecurityGroup=i,r.prototype.getVNCUrl=function(t,e){return this.sendRequest("GET","/v1/instance/"+t+"/vnc",{config:(e||{}).config})},r.prototype.getClientToken=function(t){return this.sendRequest("POST","/v1/token/create")},r.prototype._generateClientToken=function(){return"ClientToken:"+(Date.now().toString(16)+(Number.MAX_VALUE*Math.random()).toString(16).substr(0,8))},e.exports=r},{150:150,168:168,172:172,176:176}],176:[function(t,e,n){function r(t,e,n){o.call(this),this.config=s.extend({},c.DEFAULT_CONFIG,t),this.serviceId=e,this.regionSupported=!!n,this.config.endpoint=this._computeEndpoint(),this._httpAgent=null}var i=t(150),o=t(87).EventEmitter,a=t(171),s=t(172),c=t(179),f=t(174),u=t(185),d=t(183);i.inherits(r,o),r.prototype._computeEndpoint=function(){return this.config.endpoint?this.config.endpoint:this.regionSupported?i.format("%s://%s.%s.%s",this.config.protocol,this.serviceId,this.config.region,c.DEFAULT_SERVICE_DOMAIN):i.format("%s://%s.%s",this.config.protocol,this.serviceId,c.DEFAULT_SERVICE_DOMAIN)},r.prototype.createSignature=function(t,e,n,r,i){return a.fcall(function(){return new f(t.ak,t.sk).generateAuthorization(e,n,r,i)})},r.prototype.sendRequest=function(t,e,n){n=s.extend({body:null,headers:{},params:{},config:{},outputStream:null},n);var r=s.extend({},this.config,n.config);return r.sessionToken&&(n.headers[d.SESSION_TOKEN]=r.sessionToken),this.sendHTTPRequest(t,e,n,r)},r.prototype.sendHTTPRequest=function(t,e,n,r){var i=this,o=this._httpAgent=new u(r);return s.each(["progress","error","abort"],function(t){o.on(t,function(e){i.emit(t,e)})}),this._httpAgent.sendRequest(t,e,n.body,n.headers,n.params,s.bind(this.createSignature,this),n.outputStream)},e.exports=r},{150:150,171:171,172:172,174:174,179:179,183:183,185:185,87:87}],177:[function(t,e,n){(function(n){function r(t){d.call(this,t,"bcs",!0)}var i=t(57),o=t(150),a=t(111),s=t(46),c=t(172),f=t(183),u=t(185),d=t(176),p=t(189);o.inherits(r,d),r.prototype.listBuckets=function(t){return t=t||{},this.sendRequest("GET",{config:t.config})},r.prototype.createBucket=function(t,e){return e=e||{},this.sendRequest("PUT",{bucketName:t,config:e.config})},r.prototype.setBucketAcl=function(t,e,n){n=n||{};var r={};return r[f.CONTENT_TYPE]="application/json; charset=UTF-8",this.sendRequest("PUT",{bucketName:t,body:JSON.stringify({accessControlList:e}),headers:r,params:{acl:""},config:n.config})},r.prototype.setBucketCannedAcl=function(t,e,n){n=n||{};var r={};return r[f.X_BCE_ACL]=e,this.sendRequest("PUT",{bucketName:t,headers:r,params:{acl:""},config:n.config})},r.prototype.getBucketAcl=function(t,e){return e=e||{},this.sendRequest("GET",{bucketName:t,params:{acl:"1"},config:e.config})},r.prototype.deleteBucket=function(t,e){return e=e||{},this.sendRequest("DELETE",{bucketName:t,config:e.config})},r.prototype.deleteObject=function(t,e,n){return n=n||{},this.sendRequest("DELETE",{bucketName:t,key:e,config:n.config})},r.prototype.listObjects=function(t,e){e=e||{};var n=c.extend({},c.pick(e,"start","limit"));return this.sendRequest("GET",{bucketName:t,params:n,config:e.config})},r.prototype.getObjectMetadata=function(t,e,n){return n=n||{},this.sendRequest("HEAD",{bucketName:t,key:e,config:n.config})},r.prototype.putObject=function(t,e,n,r){if(!e)throw new TypeError("key should not be empty.");return r=this._checkOptions(r||{}),this.sendRequest("PUT",{bucketName:t,key:e,body:n,headers:r.headers,config:r.config})},r.prototype.putObjectFromBlob=function(t,e,n,r){var i={};return i[f.CONTENT_LENGTH]=n.size,r=c.extend(i,r),this.putObject(t,e,n,r)},r.prototype.putObjectFromString=function(e,r,i,o){var a={};return a[f.CONTENT_LENGTH]=n.byteLength(i),a[f.CONTENT_MD5]=t(180).md5sum(i,null,"hex"),o=c.extend(a,o),this.putObject(e,r,i,o)},r.prototype.putObjectFromFile=function(e,n,r,i){i=i||{};var o={};o[f.CONTENT_LENGTH]=s.statSync(r).size,o[f.CONTENT_TYPE]=i[f.CONTENT_TYPE]||p.guess(a.extname(r)),i=c.extend(o,i);var u=s.createReadStream(r);if(!c.has(i,f.CONTENT_MD5)){var d=this;return t(180).md5file(r,"hex").then(function(t){return i[f.CONTENT_MD5]=t,d.putObject(e,n,u,i)})}return this.putObject(e,n,u,i)},r.prototype.createSignature=function(t,e,r,o){return e="MBO\n"+["Method="+e,"Bucket="+r,"Object="+o].join("\n")+"\n",r=i.createHmac("sha1",t.sk),r.update(new n(e,"utf-8")),e=encodeURIComponent(r.digest("base64")).replace(/%2F/g,"/"),["MBO",t.ak,e].join(":")},r.prototype.sendRequest=function(e,n){var r=c.extend({bucketName:null,key:null,body:null,headers:{},params:{},config:{},outputStream:null},n),i=c.extend({},this.config,r.config),o="/";r.bucketName&&(o+=r.bucketName),r.key&&(o+="/"+r.key);var a=this.createSignature(i.credentials,e,r.bucketName?r.bucketName:"",r.key?"/"+r.key:"/"),s=this,f=this._httpAgent=new u(i);return c.each(["progress","error","abort"],function(t){f.on(t,function(e){s.emit(t,e)})}),f.buildQueryString=function(e){return(e=t(123).stringify(e))?"sign="+a+"&"+e:"sign="+a},f.sendRequest(e,o,r.body,r.headers,r.params,null,r.outputStream)},r.prototype._checkOptions=function(t,e){var n={};return n.config=t.config||{},n.headers=this._prepareObjectHeaders(t),
n.params=c.pick(t,e||[]),n},r.prototype._prepareObjectHeaders=function(t){var e=[f.CONTENT_LENGTH,f.CONTENT_ENCODING,f.CONTENT_MD5,f.CONTENT_TYPE,f.CONTENT_DISPOSITION,f.ETAG,f.SESSION_TOKEN],r=0;if(t=c.pick(t,function(t,i){return-1!==e.indexOf(i)?!0:/^x\-bce\-meta\-/.test(i)?(r+=n.byteLength(i)+n.byteLength(""+t),!0):void 0}),r>2048)throw new TypeError("Metadata size should not be greater than 2048.");if(c.has(t,f.CONTENT_LENGTH)){var i=t[f.CONTENT_LENGTH];if(0>i)throw new TypeError("content_length should not be negative.");if(i>5368709120)throw new TypeError("Object length should be less than 5368709120. Use multi-part upload instead.")}return c.has(t,"ETag")&&(i=t.ETag,/^"/.test(i)||(t.ETag=o.format('"%s"',i))),c.has(t,f.CONTENT_TYPE)||(t[f.CONTENT_TYPE]="application/octet-stream"),t},e.exports=r}).call(this,t(48).Buffer)},{111:111,123:123,150:150,172:172,176:176,180:180,183:183,185:185,189:189,46:46,48:48,57:57}],178:[function(t,e,n){(function(n){function r(t){b.call(this,t,"bos",!0),this._httpAgent=null}var i=t(150),o=t(111),a=t(46),s=t(123),c=t(172),f=t(171),u=t(183),d=t(194),p=t(174),h=t(180),l=t(185),b=t(176),m=t(189),v=t(197),y=t(190),g={scale:"s",width:"w",height:"h",quality:"q",format:"f",angle:"a",display:"d",limit:"l",crop:"c",offsetX:"x",offsetY:"y",watermark:"wm",key:"k",gravity:"g",gravityX:"x",gravityY:"y",opacity:"o",text:"t",fontSize:"sz",fontFamily:"ff",fontColor:"fc",fontStyle:"fs"};i.inherits(r,b),r.prototype.generatePresignedUrl=function(e,n,r,a,f,u,h,l){return l=c.extend({},this.config,l),u=u||{},e=o.normalize(o.join("/v1",d.normalize(e||""),d.normalize(n||"",!1))).replace(/\\/g,"/"),f=f||{},f.Host=t(148).parse(l.endpoint).host,n=l.credentials,r=new p(n.ak,n.sk).generateAuthorization("GET",e,u,f,r,a,h),u.authorization=r,i.format("%s%s?%s",l.endpoint,e,s.encode(u))},r.prototype.generateUrl=function(t,e,n,r){var a=o.normalize(o.join("/v1",d.normalize(t||""),d.normalize(e||"",!1))).replace(/\\/g,"/"),s="";return n&&(s=c.isString(n)?/^@/.test(n)?n:"@"+n:"@"+c.map(n,function(t){return c.map(t,function(t,e){return[g[e]||e,t].join("_")}).join(",")}).join("|")),s?r?i.format("http://%s/%s%s",r,o.normalize(e),s):i.format("http://%s.%s/%s%s",o.normalize(t),"bceimg.com",o.normalize(e),s):i.format("%s%s%s",this.config.endpoint,a,s)},r.prototype.listBuckets=function(t){return t=t||{},this.sendRequest("GET",{config:t.config})},r.prototype.createBucket=function(t,e){return e=e||{},this.sendRequest("PUT",{bucketName:t,config:e.config})},r.prototype.listObjects=function(t,e){e=e||{};var n=c.extend({maxKeys:1e3},c.pick(e,"maxKeys","prefix","marker","delimiter"));return this.sendRequest("GET",{bucketName:t,params:n,config:e.config})},r.prototype.doesBucketExist=function(t,e){return e=e||{},this.sendRequest("HEAD",{bucketName:t,config:e.config}).then(function(){return f(!0)},function(t){return t&&403===t[u.X_STATUS_CODE]?f(!0):t&&404===t[u.X_STATUS_CODE]?f(!1):f.reject(t)})},r.prototype.deleteBucket=function(t,e){return e=e||{},this.sendRequest("DELETE",{bucketName:t,config:e.config})},r.prototype.setBucketCannedAcl=function(t,e,n){n=n||{};var r={};return r[u.X_BCE_ACL]=e,this.sendRequest("PUT",{bucketName:t,headers:r,params:{acl:""},config:n.config})},r.prototype.setBucketAcl=function(t,e,n){n=n||{};var r={};return r[u.CONTENT_TYPE]="application/json; charset=UTF-8",this.sendRequest("PUT",{bucketName:t,body:JSON.stringify({accessControlList:e}),headers:r,params:{acl:""},config:n.config})},r.prototype.getBucketAcl=function(t,e){return e=e||{},this.sendRequest("GET",{bucketName:t,params:{acl:""},config:e.config})},r.prototype.getBucketLocation=function(t,e){return e=e||{},this.sendRequest("GET",{bucketName:t,params:{location:""},config:e.config})},r.prototype.deleteMultipleObjects=function(t,e,n){return n=n||{},e=c.map(e,function(t){return{key:t}}),this.sendRequest("POST",{bucketName:t,params:{"delete":""},body:JSON.stringify({"delete":{objects:e}}),config:n.config})},r.prototype.deleteObject=function(t,e,n){return n=n||{},this.sendRequest("DELETE",{bucketName:t,key:e,config:n.config})},r.prototype.putObject=function(t,e,n,r){if(!e)throw new TypeError("key should not be empty.");return r=this._checkOptions(r||{}),this.sendRequest("PUT",{bucketName:t,key:e,body:n,headers:r.headers,config:r.config})},r.prototype.putObjectFromBlob=function(t,e,n,r){var i={};return i[u.CONTENT_LENGTH]=n.size,r=c.extend(i,r),this.putObject(t,e,n,r)},r.prototype.putObjectFromDataUrl=function(t,e,r,i){r=new n(r,"base64");var o={};return o[u.CONTENT_LENGTH]=r.length,i=c.extend(o,i),this.putObject(t,e,r,i)},r.prototype.putObjectFromString=function(t,e,r,i){i=i||{};var a={};return a[u.CONTENT_LENGTH]=n.byteLength(r),a[u.CONTENT_TYPE]=i[u.CONTENT_TYPE]||m.guess(o.extname(e)),a[u.CONTENT_MD5]=h.md5sum(r),i=c.extend(a,i),this.putObject(t,e,r,i)},r.prototype.putObjectFromFile=function(t,e,n,r){r=r||{};var i={},s=a.statSync(n).size,f=c.has(r,u.CONTENT_LENGTH)?r[u.CONTENT_LENGTH]:s;if(f>s)throw Error("options['Content-Length'] should less than "+s);i[u.CONTENT_LENGTH]=f,i[u.CONTENT_TYPE]=r[u.CONTENT_TYPE]||m.guess(o.extname(n)),r=c.extend(i,r);var i={start:0,end:Math.max(0,f-1)},d=a.createReadStream(n,i);if(!c.has(r,u.CONTENT_MD5)){var p=this;return n=a.createReadStream(n,i),h.md5stream(n).then(function(n){return r[u.CONTENT_MD5]=n,p.putObject(t,e,d,r)})}return this.putObject(t,e,d,r)},r.prototype.getObjectMetadata=function(t,e,n){return n=n||{},this.sendRequest("HEAD",{bucketName:t,key:e,config:n.config})},r.prototype.getObject=function(t,e,n,r){r=r||{};var o=new v;return this.sendRequest("GET",{bucketName:t,key:e,headers:{Range:n?i.format("bytes=%s",n):""},config:r.config,outputStream:o}).then(function(t){return t.body=o.store,t})},r.prototype.getObjectToFile=function(t,e,n,r,o){return o=o||{},this.sendRequest("GET",{bucketName:t,key:e,headers:{Range:r?i.format("bytes=%s",r):""},config:o.config,outputStream:a.createWriteStream(n)})},r.prototype.copyObject=function(t,e,n,r,o){if(!t)throw new TypeError("sourceBucketName should not be empty");if(!e)throw new TypeError("sourceKey should not be empty");if(!n)throw new TypeError("targetBucketName should not be empty");if(!r)throw new TypeError("targetKey should not be empty");o=this._checkOptions(o||{});var a=!1;return c.some(o.headers,function(t,e){return 0===e.indexOf("x-bce-meta-")?a=!0:void 0}),o.headers["x-bce-copy-source"]=d.normalize(i.format("/%s/%s",t,e),!1),c.has(o.headers,"ETag")&&(o.headers["x-bce-copy-source-if-match"]=o.headers.ETag),o.headers["x-bce-metadata-directive"]=a?"replace":"copy",this.sendRequest("PUT",{bucketName:n,key:r,headers:o.headers,config:o.config})},r.prototype.initiateMultipartUpload=function(t,e,n){n=n||{};var r={};return r[u.CONTENT_TYPE]=n[u.CONTENT_TYPE]||m.guess(o.extname(e)),this.sendRequest("POST",{bucketName:t,key:e,params:{uploads:""},headers:r,config:n.config})},r.prototype.abortMultipartUpload=function(t,e,n,r){return r=r||{},this.sendRequest("DELETE",{bucketName:t,key:e,params:{uploadId:n},config:r.config})},r.prototype.completeMultipartUpload=function(t,e,n,r,i){var o={};return o[u.CONTENT_TYPE]="application/json; charset=UTF-8",i=this._checkOptions(c.extend(o,i)),this.sendRequest("POST",{bucketName:t,key:e,body:JSON.stringify({parts:r}),headers:i.headers,params:{uploadId:n},config:i.config})},r.prototype.uploadPartFromFile=function(t,e,n,r,i,o,s,c){return o=a.createReadStream(o,{start:s,end:s+i-1}),this.uploadPart(t,e,n,r,i,o,c)},r.prototype.uploadPartFromBlob=function(t,e,n,r,o,a,s){if(a.size!==o)throw new TypeError(i.format("Invalid partSize %d and data length %d",o,a.size));var f={};return f[u.CONTENT_LENGTH]=o,f[u.CONTENT_TYPE]="application/octet-stream",s=this._checkOptions(c.extend(f,s)),this.sendRequest("PUT",{bucketName:t,key:e,body:a,headers:s.headers,params:{partNumber:r,uploadId:n},config:s.config})},r.prototype.uploadPartFromDataUrl=function(t,e,r,o,a,s,f){if(s=new n(s,"base64"),s.length!==a)throw new TypeError(i.format("Invalid partSize %d and data length %d",a,s.length));var d={};return d[u.CONTENT_LENGTH]=a,d[u.CONTENT_TYPE]="application/octet-stream",f=this._checkOptions(c.extend(d,f)),this.sendRequest("PUT",{bucketName:t,key:e,body:s,headers:f.headers,params:{partNumber:o,uploadId:r},config:f.config})},r.prototype.uploadPart=function(t,e,n,r,o,s,f){function d(){return f=p._checkOptions(f),p.sendRequest("PUT",{bucketName:t,key:e,body:l,headers:f.headers,params:{partNumber:r,uploadId:n},config:f.config})}if(!t)throw new TypeError("bucketName should not be empty");if(!e)throw new TypeError("key should not be empty");if(1>r||r>1e4)throw new TypeError(i.format("Invalid partNumber %d. The valid range is from %d to %d.",r,1,1e4));var p=this,l=a.createReadStream(s.path,{start:s.start,end:s.end}),b={};return b[u.CONTENT_LENGTH]=o,b[u.CONTENT_TYPE]="application/octet-stream",f=c.extend(b,f),f[u.CONTENT_MD5]?d():h.md5stream(s).then(function(t){return f[u.CONTENT_MD5]=t,d()})},r.prototype.listParts=function(t,e,n,r){if(!n)throw new TypeError("uploadId should not empty");return r=this._checkOptions(r||{},["maxParts","partNumberMarker","uploadId"]),r.params.uploadId=n,this.sendRequest("GET",{bucketName:t,key:e,params:r.params,config:r.config})},r.prototype.listMultipartUploads=function(t,e){return e=this._checkOptions(e||{},["delimiter","maxUploads","keyMarker","prefix","uploads"]),e.params.uploads="",this.sendRequest("GET",{bucketName:t,params:e.params,config:e.config})},r.prototype.appendObject=function(t,e,n,r,i){if(!e)throw new TypeError("key should not be empty.");i=this._checkOptions(i||{});var o={append:""};return c.isNumber(r)&&(o.offset=r),this.sendRequest("POST",{bucketName:t,key:e,body:n,headers:i.headers,params:o,config:i.config})},r.prototype.appendObjectFromBlob=function(t,e,n,r,i){var o={};return o[u.CONTENT_LENGTH]=n.size,i=c.extend(o,i),this.appendObject(t,e,n,r,i)},r.prototype.appendObjectFromDataUrl=function(t,e,r,i,o){r=new n(r,"base64");var a={};return a[u.CONTENT_LENGTH]=r.length,o=c.extend(a,o),this.appendObject(t,e,r,i,o)},r.prototype.appendObjectFromString=function(t,e,r,i,a){a=a||{};var s={};return s[u.CONTENT_LENGTH]=n.byteLength(r),s[u.CONTENT_TYPE]=a[u.CONTENT_TYPE]||m.guess(o.extname(e)),s[u.CONTENT_MD5]=h.md5sum(r),a=c.extend(s,a),this.appendObject(t,e,r,i,a)},r.prototype.appendObjectFromFile=function(t,e,n,r,i,s){if(s=s||{},0===i)return this.appendObjectFromString(t,e,"",r,s);var f={},d=a.statSync(n).size;if(i+r>d)throw Error("Can't read the content beyond the end of file.");f[u.CONTENT_LENGTH]=i,f[u.CONTENT_TYPE]=s[u.CONTENT_TYPE]||m.guess(o.extname(n)),s=c.extend(f,s),i={start:r||0,end:(r||0)+i-1};var p=a.createReadStream(n,i);if(!c.has(s,u.CONTENT_MD5)){var l=this;return n=a.createReadStream(n,i),h.md5stream(n).then(function(n){return s[u.CONTENT_MD5]=n,l.appendObject(t,e,p,r,s)})}return this.appendObject(t,e,p,r,s)},r.prototype.signPostObjectPolicy=function(t){var e=this.config.credentials,r=new p(e.ak,e.sk);return t=new n(JSON.stringify(t)).toString("base64"),e=r.hash(t,e.sk),{policy:t,signature:e}},r.prototype.postObject=function(t,e,r,i){var o="MM8964"+(Math.random()*Math.pow(2,63)).toString(36),s="multipart/form-data; boundary="+o;if(c.isString(r))r=a.readFileSync(r);else if(!n.isBuffer(r))throw Error("Invalid data type.");var f=this.config.credentials.ak;i=c.omit(i||{},["signature","accessKey","key","file"]);var d,o=new y(o);for(d in i)i.hasOwnProperty(d)&&"policy"!==d&&o.addPart(d,i[d]);return i.policy&&(i=this.signPostObjectPolicy(i.policy),o.addPart("policy",i.policy),o.addPart("signature",i.signature)),o.addPart("accessKey",f),o.addPart("key",e),o.addPart("file",r),e=o.encode(),r={},r[u.CONTENT_TYPE]=s,this.sendRequest("POST",{bucketName:t,body:e,headers:r})},r.prototype.sendRequest=function(t,e){var n=c.extend({bucketName:null,key:null,body:null,headers:{},params:{},config:{},outputStream:null},e),r=c.extend({},this.config,n.config),i=o.normalize(o.join("/v1",d.normalize(n.bucketName||""),d.normalize(n.key||"",!1))).replace(/\\/g,"/");return r.sessionToken&&(n.headers[u.SESSION_TOKEN]=r.sessionToken),this.sendHTTPRequest(t,i,n,r)},r.prototype.sendHTTPRequest=function(t,e,n,r){var i=this,o=this._httpAgent=new l(r),a={httpMethod:t,resource:e,args:n,config:r};return c.each(["progress","error","abort"],function(t){o.on(t,function(e){i.emit(t,e,a)})}),t=this._httpAgent.sendRequest(t,e,n.body,n.headers,n.params,c.bind(this.createSignature,this),n.outputStream),t.abort=function(){o._req&&o._req.xhr&&o._req.xhr.abort()},t},r.prototype._checkOptions=function(t,e){var n={};return n.config=t.config||{},n.headers=this._prepareObjectHeaders(t),n.params=c.pick(t,e||[]),n},r.prototype._prepareObjectHeaders=function(t){var e=[u.CONTENT_LENGTH,u.CONTENT_ENCODING,u.CONTENT_MD5,u.X_BCE_CONTENT_SHA256,u.CONTENT_TYPE,u.CONTENT_DISPOSITION,u.ETAG,u.SESSION_TOKEN,u.CACHE_CONTROL,u.EXPIRES,u.X_BCE_OBJECT_ACL,u.X_BCE_OBJECT_GRANT_READ],r=0;if(t=c.pick(t,function(t,i){return-1!==e.indexOf(i)?!0:/^x\-bce\-meta\-/.test(i)?(r+=n.byteLength(i)+n.byteLength(""+t),!0):void 0}),r>2048)throw new TypeError("Metadata size should not be greater than 2048.");if(c.has(t,u.CONTENT_LENGTH)){var o=t[u.CONTENT_LENGTH];if(0>o)throw new TypeError("content_length should not be negative.");if(o>5368709120)throw new TypeError("Object length should be less than 5368709120. Use multi-part upload instead.")}return c.has(t,"ETag")&&(o=t.ETag,/^"/.test(o)||(t.ETag=i.format('"%s"',o))),c.has(t,u.CONTENT_TYPE)||(t[u.CONTENT_TYPE]="application/octet-stream"),t},e.exports=r}).call(this,t(48).Buffer)},{111:111,123:123,148:148,150:150,171:171,172:172,174:174,176:176,180:180,183:183,185:185,189:189,190:190,194:194,197:197,46:46,48:48}],179:[function(t,e,n){n.DEFAULT_SERVICE_DOMAIN="baidubce.com",n.DEFAULT_CONFIG={protocol:"http",region:"bj"}},{}],180:[function(t,e,n){(function(e){var r=t(46),i=t(57),o=t(171);n.md5sum=function(t,n,r){return e.isBuffer(t)||(t=new e(t,n||"UTF-8")),n=i.createHash("md5"),n.update(t),n.digest(r||"base64")},n.md5stream=function(t,e){var n=o.defer(),r=i.createHash("md5");return t.on("data",function(t){r.update(t)}),t.on("end",function(){n.resolve(r.digest(e||"base64"))}),t.on("error",function(t){n.reject(t)}),n.promise},n.md5file=function(t,e){return n.md5stream(r.createReadStream(t),e)},n.md5blob=function(t,e){var r=o.defer(),i=new FileReader;return i.readAsArrayBuffer(t),i.onerror=function(t){r.reject(i.error)},i.onloadend=function(t){t.target.readyState===FileReader.DONE&&(t=n.md5sum(t.target.result,null,e),r.resolve(t))},r.promise}}).call(this,t(48).Buffer)},{171:171,46:46,48:48,57:57}],181:[function(t,e,n){(function(e){function r(t){h.call(this,t,"doc",!1),this._documentId=null}function i(t){h.call(this,t,"doc",!1),this._endpoint=this._name=null}var o=t(46),a=t(111),s=t(150),c=t(148),f=t(171),u=t(172),d=t(168)("bce-sdk:Document"),p=t(178),h=t(176),l=t(184),b=t(180);s.inherits(r,h),r.prototype._buildUrl=function(){var t="/v2/document",e=u.toArray(arguments);return e.length&&(t+="/"+e.join("/")),t},r.prototype.getId=function(){return this._documentId},r.prototype.setId=function(t){return this._documentId=t,this},r.prototype.create=function(t,n){var r=u.extend({},n),i=-1,i=/^bos:\/\//;if(u.isString(t)){if(i.test(t))try{var s=c.parse(t),d=s.host,p=s.pathname.substr(1),r=u.extend(r,s.query),h=r.title||a.basename(p),l=r.format||a.extname(p).substr(1);return this.createFromBos(d,p,h,l,r.notification)}catch(m){return f.reject(m)}i=1,r.format=r.format||a.extname(t).substr(1),r.title=r.title||a.basename(t,a.extname(t))}else if(e.isBuffer(t)){if(null==r.format||null==r.title)return f.reject(Error("buffer type required options.format and options.title"));i=2}else{if(!("undefined"!=typeof Blob&&t instanceof Blob))return f.reject(Error("Unsupported dataType."));i=4,r.format=r.format||a.extname(t.name).substr(1),r.title=r.title||a.basename(t.name,a.extname(t.name))}if(!r.title||!r.format)return f.reject(Error("`title` and `format` are required."));if(r.meta.md5)return this._doCreate(t,r);var v=this;return 1===i?b.md5stream(o.createReadStream(t),"hex").then(function(e){return r.meta.md5=e,v._doCreate(t,r)}):4===i?b.md5blob(t,"hex").then(function(e){return r.meta.md5=e,v._doCreate(t,r)}):this._doCreate(t,r)},r.prototype._doCreate=function(t,e){var n=null,r=null,i=null,o=null,a=this;return a.register(e).then(function(e){return d("register[response = %j]",e),n=e.body.documentId,r=e.body.bucket,i=e.body.object,o=e.body.bosEndpoint,e=u.extend({},a.config,{endpoint:o}),e=new p(e),l.upload(e,r,i,t)}).then(function(t){return d("upload[response = %j]",t),a.publish()}).then(function(t){return d("publish[response = %j]",t),t.body={documentId:n,bucket:r,object:i,bosEndpoint:o},t})},r.prototype.register=function(t){d("register[options = %j]",t);var e=this,n=this._buildUrl();return this.sendRequest("POST",n,{params:{register:""},headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}).then(function(t){return e.setId(t.body.documentId),t})},r.prototype.publish=function(t){return t=this._buildUrl(t||this._documentId),this.sendRequest("PUT",t,{params:{publish:""}})},r.prototype.get=function(t){return t=this._buildUrl(t||this._documentId),this.sendRequest("GET",t)},r.prototype.read=function(t){return t=this._buildUrl(t||this._documentId),this.sendRequest("GET",t,{params:{read:""}})},r.prototype.download=function(t){return t=this._buildUrl(t||this._documentId),this.sendRequest("GET",t,{params:{download:""}})},r.prototype.createFromBos=function(t,e,n,r,i){var o=this._buildUrl(),s={bucket:t,object:e,title:n},c=r||a.extname(e).substr(1);if(!c)throw Error("Document format parameter required");s.format=c,i&&(s.notification=i),d("createFromBos:arguments = [%j], body = [%j]",arguments,s);var f=this;return this.sendRequest("POST",o,{params:{source:"bos"},headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}).then(function(t){return f.setId(t.body.documentId),t})},r.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=(e.body.documents||[]).map(function(e){return t.remove(e.documentId)}),f.all(e)})},r.prototype.remove=function(t){return t=this._buildUrl(t||this._documentId),this.sendRequest("DELETE",t)},r.prototype.list=function(t){t=t||"";var e=this._buildUrl();return this.sendRequest("GET",e,{params:{status:t}})},s.inherits(i,h),i.prototype._buildUrl=function(){var t="/v1/notification",e=u.toArray(arguments);return e.length&&(t+="/"+e.join("/")),t},i.prototype.create=function(t,e){var n=this,r=this._buildUrl();return n.sendRequest("POST",r,{headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t,endpoint:e})}).then(function(r){return n._name=t,n._endpoint=e,r})},i.prototype.get=function(t){return t=this._buildUrl(t||this._name),this.sendRequest("GET",t)},i.prototype.list=function(){return this.sendRequest("GET",this._buildUrl())},i.prototype.remove=function(t){return t=this._buildUrl(t||this._name),this.sendRequest("DELETE",t)},i.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=(e.body.notifications||[]).map(function(e){return t.remove(e.name)}),f.all(e)})},n.Document=r,n.Notification=i}).call(this,{isBuffer:t(102)})},{102:102,111:111,148:148,150:150,168:168,171:171,172:172,176:176,178:178,180:180,184:184,46:46}],182:[function(t,e,n){(function(n){function r(t){s.call(this,t,"face",!0)}var i=t(150),o=t(172),a=t(168)("bce-sdk:FaceClient"),s=t(176);i.inherits(r,s),r.prototype.createApp=function(t){return t=t||{},this.sendRequest("POST","/v1/app",{config:t.config})},r.prototype.listApps=function(t){return t=t||{},this.sendRequest("GET","/v1/app",{config:t.config})},r.prototype.createGroup=function(t,e,n){return n=n||{},this.sendRequest("POST","/v1/app/"+t+"/group",{body:JSON.stringify({groupName:e}),config:n.config})},r.prototype.deleteGroup=function(t,e,n){return n=n||{},this.sendRequest("DELETE","/v1/app/"+t+"/group/"+e,{config:n.config})},r.prototype.getGroup=function(t,e,n){return n=n||{},this.sendRequest("GET","/v1/app/"+t+"/group/"+e,{config:n.config})},r.prototype.listGroups=function(t,e){return e=e||{},this.sendRequest("GET","/v1/app/"+t+"/group",{config:e.config})},r.prototype.createPerson=function(t,e,n,r,i){return i=i||{},r=r.map(function(t){return{bosPath:t}}),a("Create Person Faces = %j",r),this.sendRequest("POST","/v1/app/"+t+"/person",{body:JSON.stringify({personName:n,groupName:e,faces:r}),config:i.config})},r.prototype.deletePerson=function(t,e,n){return n=n||{},this.sendRequest("DELETE","/v1/app/"+t+"/person/"+e,{config:n.config})},r.prototype.updatePerson=function(t,e,n,r){return r=r||{},n=n.map(function(t){return{bosPath:t}}),this.sendRequest("PUT","/v1/app/"+t+"/person/"+e,{body:JSON.stringify({faces:n}),config:r.config})},r.prototype.getPerson=function(t,e,n){return n=n||{},this.sendRequest("GET","/v1/app/"+t+"/person/"+e,{config:n.config})},r.prototype.listPersons=function(t,e){e=e||{};var n="/v1/app/"+t+"/person",r=o.pick(e,"groupName");return this.sendRequest("GET",n,{params:r,config:e.config})},r.prototype.identify=function(t,e,r,i){return i=i||{},r=n.isBuffer(r)?{base64:r.toString("base64")}:{bosPath:r},this.sendRequest("POST","/v1/app/"+t+"/group/"+e,{params:{identify:""},body:JSON.stringify(r),config:i.config})},r.prototype.verify=function(t,e,r,i){return i=i||{},r=n.isBuffer(r)?{base64:r.toString("base64")}:{bosPath:r},this.sendRequest("POST","/v1/app/"+t+"/person/"+e,{params:{verify:""},body:JSON.stringify(r),config:i.config})},e.exports=r}).call(this,{isBuffer:t(102)})},{102:102,150:150,168:168,172:172,176:176}],183:[function(t,e,n){n.CONTENT_TYPE="Content-Type",n.CONTENT_LENGTH="Content-Length",n.CONTENT_MD5="Content-MD5",n.CONTENT_ENCODING="Content-Encoding",n.CONTENT_DISPOSITION="Content-Disposition",n.ETAG="ETag",n.CONNECTION="Connection",n.HOST="Host",n.USER_AGENT="User-Agent",n.CACHE_CONTROL="Cache-Control",n.EXPIRES="Expires",n.AUTHORIZATION="Authorization",n.X_BCE_DATE="x-bce-date",n.X_BCE_ACL="x-bce-acl",n.X_BCE_REQUEST_ID="x-bce-request-id",n.X_BCE_CONTENT_SHA256="x-bce-content-sha256",n.X_BCE_OBJECT_ACL="x-bce-object-acl",n.X_BCE_OBJECT_GRANT_READ="x-bce-object-grant-read",n.X_HTTP_HEADERS="http_headers",n.X_BODY="body",n.X_STATUS_CODE="status_code",n.X_MESSAGE="message",n.X_CODE="code",n.X_REQUEST_ID="request_id",n.SESSION_TOKEN="x-bce-security-token",n.X_VOD_MEDIA_TITLE="x-vod-media-title",n.X_VOD_MEDIA_DESCRIPTION="x-vod-media-description",n.ACCEPT_ENCODING="accept-encoding",n.ACCEPT="accept"},{}],184:[function(t,e,n){(function(e){function r(t,e,n,r,a,s,p,h){var l;return t.initiateMultipartUpload(r,a,h).then(function(f){l=f.body.uploadId,d("initiateMultipartUpload = %j",f);var h=u.defer();return f=o(e,l,r,a,s,p),c.mapLimit(f,2,i(t,n,{lengthComputable:!0,loaded:0,total:f.length}),function(t,e){t?h.reject(t):h.resolve(e)}),h.promise}).then(function(e){return e=f.map(e,function(t,e){return{partNumber:e+1,eTag:t.http_headers.etag}}),d("parts = %j",e),t.completeMultipartUpload(r,a,l,e)})}function i(t,e,n){return function(r,i){var o=function(e){++n.loaded,t.emit("progress",n),i(null,e)},a=function(t){i(t)};if(1===e)return d("client.uploadPartFromFile(%j)",f.omit(r,"data")),t.uploadPartFromFile(r.bucket,r.object,r.uploadId,r.partNumber,r.partSize,r.data,r.start).then(o,a);if(2===e){d("client.uploadPartFromDataUrl(%j)",f.omit(r,"data"));var s=r.data.slice(r.start,r.stop+1).toString("base64");return t.uploadPartFromDataUrl(r.bucket,r.object,r.uploadId,r.partNumber,r.partSize,s).then(o,a)}return 4===e?(d("client.uploadPartFromBlob(%j)",f.omit(r,"data")),s=r.data.slice(r.start,r.stop+1),t.uploadPartFromBlob(r.bucket,r.object,r.uploadId,r.partNumber,r.partSize,s).then(o,a)):void 0}}function o(t,e,n,r,i,o){for(var a=0,s=1,c=[];i>0;){var f=Math.min(i,o);c.push({data:t,uploadId:e,bucket:n,object:r,partNumber:s,partSize:f,start:a,stop:a+f-1}),i-=f,a+=f,s+=1}return c}var a=t(46),s=t(145),c=t(167),f=t(172),u=t(171),d=t(168)("bce-sdk:helper");n.upload=function(t,n,i,o,c){var f=0,u=-1;if("string"==typeof o?(f=a.lstatSync(o).size,u=1):e.isBuffer(o)?(f=o.length,u=2):o instanceof s.Readable?u=3:"undefined"!=typeof Blob&&o instanceof Blob&&(f=o.size,u=4),-1===u)throw Error("Unsupported `data` type.");if(3===u)return t.putObject(n,i,o,c);if(5242880>=f){if(1===u)return t.putObjectFromFile(n,i,o,c);if(2===u)return t.putObject(n,i,o,c);if(4===u)return t.putObjectFromBlob(n,i,o,c)}else if(f>5242880)return d("%s > %s -> multi-part",f,5242880),r(t,o,u,n,i,f,1048576,c)}}).call(this,{isBuffer:t(102)})},{102:102,145:145,167:167,168:168,171:171,172:172,46:46}],185:[function(t,e,n){(function(n,r){function i(t){d.call(this),this.config=t,this._req=null}function o(t,e){var n={};return n[b.X_HTTP_HEADERS]=t,n[b.X_BODY]=e,n}function a(t,e,n,i){var o={};return o[b.X_STATUS_CODE]=t,o[b.X_MESSAGE]=r.isBuffer(e)?String(e):e,n&&(o[b.X_CODE]=n),i&&(o[b.X_REQUEST_ID]=i),o}var s=t(95),c=t(98),f=t(150),u=t(145),d=t(87).EventEmitter,p=t(172),h=t(171),l=t(168)("bce-sdk:HttpClient"),b=t(183);f.inherits(i,d),i.prototype.sendRequest=function(e,r,i,o,a,s,c){var u=this._getRequestUrl(r,a),d=t(148).parse(u);l("httpMethod = %s, requestUrl = %s, options = %j",e,u,d),u={},u[b.USER_AGENT]="object"==typeof navigator?navigator.userAgent:f.format("bce-sdk-nodejs/%s/%s/%s",t(173).version,n.platform,n.version),u[b.X_BCE_DATE]=(new Date).toISOString().replace(/\.\d+Z$/,"Z"),u[b.CONNECTION]="close",u[b.CONTENT_TYPE]="application/json; charset=UTF-8",u[b.HOST]=d.host,o=p.extend({},u,o),o.hasOwnProperty(b.CONTENT_LENGTH)||(u=this._guessContentLength(i),0===u&&/GET|HEAD/i.test(e)||(o[b.CONTENT_LENGTH]=u));var h=this;if(d.method=e,d.headers=o,d.mode="prefer-fast",d.rejectUnauthorized=!1,"function"==typeof s){if(e=s(this.config.credentials,e,r,a,o),e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then)return e.then(function(t,e){return o[b.AUTHORIZATION]=t,e&&(o[b.X_BCE_DATE]=e),l("options = %j",d),h._doRequest(d,i,c)});if("string"!=typeof e)throw Error("Invalid signature = ("+e+")");o[b.AUTHORIZATION]=e,l("options = %j",d)}return h._doRequest(d,i,c)},i.prototype._isValidStatus=function(t){return t>=200&&300>t},i.prototype._doRequest=function(t,e,n){var r=h.defer(),i=this,a=i._req=("https:"===t.protocol?c:s).request(t,function(t){i._isValidStatus(t.statusCode)&&n&&n instanceof u.Writable?(t.pipe(n),n.on("finish",function(){r.resolve(o(i._fixHeaders(t.headers),{}))}),n.on("error",function(t){r.reject(t)})):r.resolve(i._recvResponse(t))});a.xhr&&"object"==typeof a.xhr.upload&&p.each(["progress","error","abort"],function(t){a.xhr.upload.addEventListener(t,function(e){i.emit(t,e)},!1)}),a.on("error",function(t){r.reject(t)});try{i._sendRequest(a,e)}catch(f){r.reject(f)}return r.promise},i.prototype._generateRequestId=function(){function t(){var t=(~~(65535*Math.random())).toString(16);return 4>t.length&&(t+=Array(4-t.length+1).join("0")),t}return f.format("%s%s-%s-%s-%s-%s%s%s",t(),t(),t(),t(),t(),t(),t(),t())},i.prototype._guessContentLength=function(t){if(null==t)return 0;if("string"==typeof t)return r.byteLength(t);if("object"==typeof t){if("undefined"!=typeof Blob&&t instanceof Blob)return t.size;if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer)return t.byteLength}if(r.isBuffer(t))return t.length;throw Error("No Content-Length is specified.")},i.prototype._fixHeaders=function(t){var e={};return t&&Object.keys(t).forEach(function(n){var r=t[n].trim();r&&(n=n.toLowerCase(),"etag"===n&&(r=r.replace(/"/g,"")),e[n]=r)}),e},i.prototype._recvResponse=function(t){var e=this._fixHeaders(t.headers),n=t.statusCode,i=h.defer(),s=[];return t.on("data",function(t){r.isBuffer(t)?s.push(t):s.push(new r(t))}),t.on("error",function(t){i.reject(t)}),t.on("end",function(){var t=r.concat(s),c=null;try{var f=e["content-type"],c=t.length?f&&/(application|text)\/json/.test(f)?JSON.parse(t.toString()):t:{}}catch(u){return void i.reject(u)}n>=100&&200>n?i.reject(a(n,"Can not handle 1xx http status code.")):(100>n||n>=300)&&(c.requestId?i.reject(a(n,c.message,c.code,c.requestId)):i.reject(a(n,c))),i.resolve(o(e,c))}),i.promise},i.prototype._sendRequest=function(t,e){if(e){"string"==typeof e&&(e=new r(e));var n;if((n=r.isBuffer(e))||(n="undefined"!=typeof Blob&&e instanceof Blob?!0:"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?!0:"undefined"!=typeof FormData&&e instanceof FormData?!0:void 0),n)t.write(e),t.end();else{if(!(e instanceof u.Readable))throw Error("Invalid body type = "+typeof e);if(!e.readable)throw Error("stream is not readable");e.on("data",function(e){t.write(e)}),e.on("end",function(){t.end()})}}else t.end()},i.prototype.buildQueryString=function(e){return t(123).stringify(e)},i.prototype._getRequestUrl=function(t,e){var n=t,r=this.buildQueryString(e);return r&&(n+="?"+r),this.config.endpoint+n},e.exports=i}).call(this,t(113),t(48).Buffer)},{113:113,123:123,145:145,148:148,150:150,168:168,171:171,172:172,173:173,183:183,48:48,87:87,95:95,98:98}],186:[function(t,e,n){function r(t){s.call(this,t,"lss",!0),this._name=null}function i(t){s.call(this,t,"lss",!0),this._sessionId=null}function o(t){s.call(this,t,"lss",!0),this._endpoint=this._name=null}e=t(150);var a=t(171),s=t(176);e.inherits(r,s),r.prototype._buildUrl=function(t){return"/v3/live/preset"+(t?"/"+t:"")},r.prototype.setName=function(t){return this._name=t,this},r.prototype.create=function(t){var e=this,n=e._buildUrl(!1);return e.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(n){return e.setName(t.presetName),n})},r.prototype.remove=function(t){return t=this._buildUrl(t||this._name),this.sendRequest("DELETE",t)},r.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=e.body.presets.filter(function(t){return!/^(bce|lss)\./.test(t.presetName)}).map(function(e){return t.remove(e.presetName)}),a.all(e)})},r.prototype.get=function(t){return t=this._buildUrl(t||this._name),this.sendRequest("GET",t)},r.prototype.list=function(){var t=this._buildUrl();return this.sendRequest("GET",t)},e.inherits(i,s),i.prototype._buildUrl=function(t){return"/v3/live/session"+(t?"/"+t:"")},i.prototype.setSessionId=function(t){return this._sessionId=t,this},i.prototype.create=function(t){var e=this,n=e._buildUrl(!1);return e.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(t){return e.setSessionId(t.body.sessionId),t})},i.prototype.remove=function(t){return t=this._buildUrl(t||this._sessionId),this.sendRequest("DELETE",t)},i.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=e.body.liveInfos.map(function(e){return t.remove(e.sessionId)}),a.all(e)})},i.prototype.get=function(t){return t=this._buildUrl(t||this._sessionId),this.sendRequest("GET",t)},i.prototype.list=function(){var t=this._buildUrl();return this.sendRequest("GET",t)},i.prototype.pause=function(t){return t=this._buildUrl(t||this._sessionId),this.sendRequest("PUT",t,{params:{stop:""}})},i.prototype.resume=function(t){return t=this._buildUrl(t||this._sessionId),this.sendRequest("PUT",t,{params:{resume:""}})},i.prototype.refresh=function(t){return t=this._buildUrl(t||this._sessionId),this.sendRequest("PUT",t,{params:{refresh:""}})},e.inherits(o,s),o.prototype._buildUrl=function(t){return"/v3/live/notification"+(t?"/"+t:"")},o.prototype.create=function(t,e){var n=this._buildUrl(),r=this;return this.sendRequest("POST",n,{body:JSON.stringify({name:t,endpoint:e})}).then(function(n){return r._name=t,r._endpoint=e,n})},o.prototype.get=function(t){if(t=t||this._name,!t)throw new TypeError("name is required");return t=this._buildUrl(t),this.sendRequest("GET",t)},o.prototype.remove=function(t){if(t=t||this._name,!t)throw new TypeError("name is required");return t=this._buildUrl(t),this.sendRequest("DELETE",t)},o.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=(e.body.notifications||[]).map(function(e){return t.remove(e.name)}),a.all(e)})},o.prototype.list=function(){var t=this._buildUrl();return this.sendRequest("GET",t)},n.Preset=r,n.Session=i,n.Notification=o},{150:150,171:171,176:176}],187:[function(t,e,n){function r(t){u.call(this,t,"media",!0),
this._id=null}function i(t){u.call(this,t,"media",!0)}function o(t){u.call(this,t,"media",!0),this._name=null}function a(t){u.call(this,t,"media",!0),this._name=null}function s(t){u.call(this,t,"media",!0),this._jobId=null}function c(t){u.call(this,t,"media",!0),this._jobId=null}e=t(150);var f=t(171),u=t(176);e.inherits(r,u),r.prototype._buildUrl=function(t){return!1===t?"/v3/watermark":(t=t||this._id,"/v3/watermark"+(t?"/"+t:""))},r.prototype.setId=function(t){return this._id=t,this},r.prototype.create=function(t){var e=this,n=e._buildUrl(!1);return e.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(t){return e.setId(t.body.watermarkId),t})},r.prototype.remove=function(t){return t=this._buildUrl(t),this.sendRequest("DELETE",t)},r.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=e.body.watermarks.map(function(e){return t.remove(e.watermarkId)}),f.all(e)})},r.prototype.get=function(t){var e=this;return t=e._buildUrl(t),e.sendRequest("GET",t).then(function(t){return e.setId(t.body.watermarkId),t})},r.prototype.list=function(){var t=this._buildUrl(!1);return this.sendRequest("GET",t)},e.inherits(i,u),i.prototype.get=function(t,e){return this.sendRequest("GET","/v3/mediainfo",{params:{bucket:t,key:e}})},e.inherits(o,u),o.prototype.setName=function(t){return this._name=t,this},o.prototype._buildUrl=function(t){return!1===t?"/v3/pipeline":(t=t||this._name,"/v3/pipeline"+(t?"/"+t:""))},o.prototype.create=function(t){var e=this,n=e._buildUrl(!1);return e.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(n){return e.setName(t.pipelineName),n})},o.prototype.get=function(t){var e=this;return t=e._buildUrl(t),e.sendRequest("GET",t).then(function(t){return e.setName(t.body.pipelineName),t})},o.prototype.list=function(){var t=this._buildUrl(!1);return this.sendRequest("GET",t)},o.prototype.remove=function(t){return t=this._buildUrl(t),this.sendRequest("DELETE",t)},o.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=e.body.pipelines.filter(function(t){return t=t.jobStatus,0>=t.running+t.pending}).map(function(e){return t.remove(e.pipelineName)}),f.all(e)})},o.prototype.addTranscodingJob=function(t){var e=t.pipelineName||this._name;if(!e)throw Error("`pipelineName` is required.");t.pipelineName=e;var n=new c(this.config);return n.create(t).then(function(t){return n})},o.prototype.addThumbnailJob=function(t){var e=t.pipelineName||this._name;if(!e)throw Error("`pipelineName` is required.");t.pipelineName=e;var n=new s(this.config);return n.create(t).then(function(t){return n})},o.prototype.getTranscodingJobs=function(t){if(t=t||this._name,!t)throw Error("`pipelineName` is required.");return new c(this.config).list(t)},o.prototype.getThumbnailJobs=function(t){if(t=t||this._name,!t)throw Error("`pipelineName` is required");return new s(this.config).list(t)},e.inherits(a,u),a.prototype._buildUrl=function(t){return!1===t?"/v3/preset":(t=t||this._name,"/v3/preset"+(t?"/"+t:""))},a.prototype.setName=function(t){return this._name=t,this},a.prototype.create=function(t){var e=this,n=this._buildUrl(!1);return this.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(n){return e.setName(t.presetName),n})},a.prototype.get=function(t){var e=this;return t=e._buildUrl(t),e.sendRequest("GET",t).then(function(t){return e.setName(t.body.presetName),t})},a.prototype.list=function(){var t=this._buildUrl(!1);return this.sendRequest("GET",t)},a.prototype.remove=function(t){return t=this._buildUrl(t),this.sendRequest("DELETE",t)},a.prototype.removeAll=function(){var t=this;return t.list().then(function(e){return e=e.body.presets.filter(function(t){return!/^bce\./.test(t.presetName)}).map(function(e){return t.remove(e.presetName)}),f.all(e)})},e.inherits(s,u),s.prototype._buildUrl=function(){return"/v3/job/thumbnail"},s.prototype.create=function(t){var e=this,n=this._buildUrl();return this.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(t){return e._jobId=t.body.jobId,t})},s.prototype.get=function(t){var e=t||this._jobId;if(!e)throw Error("`jobId` is required");var n=this;return t=this._buildUrl()+"/"+e,this.sendRequest("GET",t).then(function(t){return n._jobId=e,t})},s.prototype.list=function(t){var e=this._buildUrl();return this.sendRequest("GET",e,{params:{pipelineName:t}})},e.inherits(c,u),c.prototype._buildUrl=function(){return"/v3/job/transcoding"},c.prototype.create=function(t){var e=this,n=e._buildUrl();return e.sendRequest("POST",n,{body:JSON.stringify(t)}).then(function(t){return e._jobId=t.body.jobId,t})},c.prototype.get=function(t){var e=t||this._jobId;if(!e)throw Error("`jobId` is required");var n=this;return t=n._buildUrl()+"/"+e,n.sendRequest("GET",t).then(function(t){return n._jobId=e,t})},c.prototype.list=function(t){var e=this._buildUrl();return this.sendRequest("GET",e,{params:{pipelineName:t}})},n.Watermark=r,n.MediaInfo=i,n.Transcoding=c,n.Thumbnail=s,n.Pipeline=o,n.Preset=a},{150:150,171:171,176:176}],188:[function(t,e,n){function r(t){s.call(this,t,"media",!0)}n=t(150);var i=t(172),o=t(174),a=t(185),s=t(176);n.inherits(r,s),r.prototype.createPipeline=function(t,e,n,r,i,o){return o=o||{},t=JSON.stringify({pipelineName:t,sourceBucket:e,targetBucket:n,config:r||{capacity:5},description:i||""}),this.sendRequest("POST","/v3/pipeline",{body:t,config:o.config})},r.prototype.getPipeline=function(t,e){return this.sendRequest("GET","/v3/pipeline/"+t,{config:(e||{}).config})},r.prototype.deletePipeline=function(t,e){return this.sendRequest("DELETE","/v3/pipeline/"+t,{config:(e||{}).config})},r.prototype.getAllPipelines=function(t){return this.sendRequest("GET","/v3/pipeline",{config:(t||{}).config})},r.prototype.createJob=function(t,e,n,r,i){return i=i||{},t=JSON.stringify({pipelineName:t,source:e,target:n,presetName:r}),this.sendRequest("POST","/v3/job",{body:t,config:i.config})},r.prototype.getAllJobs=function(t,e){return this.sendRequest("GET","/v3/job",{params:{pipelineName:t},config:(e||{}).config})},r.prototype.getJob=function(t,e){return this.sendRequest("GET","/v3/job/"+t,{config:(e||{}).config})},r.prototype.createPreset=function(t,e,n,r,i,o,a,s,c){return c=c||{},t={presetName:t,container:e},n&&(t.clip=n),r&&(t.audio=r),i&&(t.video=i),o&&(t.encryption=o),null!=a&&(t.transmux=a),s&&(t.description=s),this.sendRequest("POST","/v3/preset",{body:JSON.stringify(t),config:c.config})},r.prototype.getPreset=function(t,e){return this.sendRequest("GET","/v3/preset/"+t,{config:(e||{}).config})},r.prototype.deletePreset=function(t,e){return this.sendRequest("DELETE","/v3/preset/"+t,{config:(e||{}).config})},r.prototype.getMediainfo=function(t,e,n){return this.sendRequest("GET","/v3/mediainfo",{params:{bucket:t,key:e},config:(n||{}).config})},r.prototype.createSignature=function(t,e,n,r,i){return new o(t.ak,t.sk).generateAuthorization(e,n,r,i,0,0,["host"])},r.prototype.sendRequest=function(t,e,n){n=i.extend({bucketName:null,key:null,body:null,headers:{},params:{},config:{},outputStream:null},n);var r=i.extend({},this.config,n.config),o=this,s=this._httpAgent=new a(r);return i.each(["progress","error","abort"],function(t){s.on(t,function(e){o.emit(t,e)})}),this._httpAgent.sendRequest(t,e,n.body,n.headers,n.params,i.bind(this.createSignature,this),n.outputStream)},e.exports=r},{150:150,172:172,174:174,176:176,185:185}],189:[function(t,e,n){var r={ez:"application/andrew-inset",aw:"application/applixware",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomsvc:"application/atomsvc+xml",ccxml:"application/ccxml+xml",cdmia:"application/cdmi-capability",cdmic:"application/cdmi-container",cdmid:"application/cdmi-domain",cdmio:"application/cdmi-object",cdmiq:"application/cdmi-queue",cu:"application/cu-seeme",davmount:"application/davmount+xml",dbk:"application/docbook+xml",dssc:"application/dssc+der",xdssc:"application/dssc+xml",ecma:"application/ecmascript",emma:"application/emma+xml",epub:"application/epub+zip",exi:"application/exi",pfr:"application/font-tdpfr",gml:"application/gml+xml",gpx:"application/gpx+xml",gxf:"application/gxf",stk:"application/hyperstudio",ink:"application/inkml+xml",inkml:"application/inkml+xml",ipfix:"application/ipfix",jar:"application/java-archive",ser:"application/java-serialized-object","class":"application/java-vm",js:"application/javascript",json:"application/json",jsonml:"application/jsonml+json",lostxml:"application/lost+xml",hqx:"application/mac-binhex40",cpt:"application/mac-compactpro",mads:"application/mads+xml",mrc:"application/marc",mrcx:"application/marcxml+xml",ma:"application/mathematica",nb:"application/mathematica",mb:"application/mathematica",mathml:"application/mathml+xml",mbox:"application/mbox",mscml:"application/mediaservercontrol+xml",metalink:"application/metalink+xml",meta4:"application/metalink4+xml",mets:"application/mets+xml",mods:"application/mods+xml",m21:"application/mp21",mp21:"application/mp21",mp4s:"application/mp4",doc:"application/msword",dot:"application/msword",mxf:"application/mxf",bin:"application/octet-stream",dms:"application/octet-stream",lrf:"application/octet-stream",mar:"application/octet-stream",so:"application/octet-stream",dist:"application/octet-stream",distz:"application/octet-stream",pkg:"application/octet-stream",bpk:"application/octet-stream",dump:"application/octet-stream",elc:"application/octet-stream",deploy:"application/octet-stream",oda:"application/oda",opf:"application/oebps-package+xml",ogx:"application/ogg",omdoc:"application/omdoc+xml",onetoc:"application/onenote",onetoc2:"application/onenote",onetmp:"application/onenote",onepkg:"application/onenote",oxps:"application/oxps",xer:"application/patch-ops-error+xml",pdf:"application/pdf",pgp:"application/pgp-encrypted",asc:"application/pgp-signature",sig:"application/pgp-signature",prf:"application/pics-rules",p10:"application/pkcs10",p7m:"application/pkcs7-mime",p7c:"application/pkcs7-mime",p7s:"application/pkcs7-signature",p8:"application/pkcs8",ac:"application/pkix-attr-cert",cer:"application/pkix-cert",crl:"application/pkix-crl",pkipath:"application/pkix-pkipath",pki:"application/pkixcmp",pls:"application/pls+xml",ai:"application/postscript",eps:"application/postscript",ps:"application/postscript",cww:"application/prs.cww",pskcxml:"application/pskc+xml",rdf:"application/rdf+xml",rif:"application/reginfo+xml",rnc:"application/relax-ng-compact-syntax",rl:"application/resource-lists+xml",rld:"application/resource-lists-diff+xml",rs:"application/rls-services+xml",gbr:"application/rpki-ghostbusters",mft:"application/rpki-manifest",roa:"application/rpki-roa",rsd:"application/rsd+xml",rss:"application/rss+xml",rtf:"application/rtf",sbml:"application/sbml+xml",scq:"application/scvp-cv-request",scs:"application/scvp-cv-response",spq:"application/scvp-vp-request",spp:"application/scvp-vp-response",sdp:"application/sdp",setpay:"application/set-payment-initiation",setreg:"application/set-registration-initiation",shf:"application/shf+xml",smi:"application/smil+xml",smil:"application/smil+xml",rq:"application/sparql-query",srx:"application/sparql-results+xml",gram:"application/srgs",grxml:"application/srgs+xml",sru:"application/sru+xml",ssdl:"application/ssdl+xml",ssml:"application/ssml+xml",tei:"application/tei+xml",teicorpus:"application/tei+xml",tfi:"application/thraud+xml",tsd:"application/timestamped-data",plb:"application/vnd.3gpp.pic-bw-large",psb:"application/vnd.3gpp.pic-bw-small",pvb:"application/vnd.3gpp.pic-bw-var",tcap:"application/vnd.3gpp2.tcap",pwn:"application/vnd.3m.post-it-notes",aso:"application/vnd.accpac.simply.aso",imp:"application/vnd.accpac.simply.imp",acu:"application/vnd.acucobol",atc:"application/vnd.acucorp",acutc:"application/vnd.acucorp",air:"application/vnd.adobe.air-application-installer-package+zip",fcdt:"application/vnd.adobe.formscentral.fcdt",fxp:"application/vnd.adobe.fxp",fxpl:"application/vnd.adobe.fxp",xdp:"application/vnd.adobe.xdp+xml",xfdf:"application/vnd.adobe.xfdf",ahead:"application/vnd.ahead.space",azf:"application/vnd.airzip.filesecure.azf",azs:"application/vnd.airzip.filesecure.azs",azw:"application/vnd.amazon.ebook",acc:"application/vnd.americandynamics.acc",ami:"application/vnd.amiga.ami",apk:"application/vnd.android.package-archive",cii:"application/vnd.anser-web-certificate-issue-initiation",fti:"application/vnd.anser-web-funds-transfer-initiation",atx:"application/vnd.antix.game-component",mpkg:"application/vnd.apple.installer+xml",m3u8:"application/vnd.apple.mpegurl",swi:"application/vnd.aristanetworks.swi",iota:"application/vnd.astraea-software.iota",aep:"application/vnd.audiograph",mpm:"application/vnd.blueice.multipass",bmi:"application/vnd.bmi",rep:"application/vnd.businessobjects",cdxml:"application/vnd.chemdraw+xml",mmd:"application/vnd.chipnuts.karaoke-mmd",cdy:"application/vnd.cinderella",cla:"application/vnd.claymore",rp9:"application/vnd.cloanto.rp9",c4g:"application/vnd.clonk.c4group",c4d:"application/vnd.clonk.c4group",c4f:"application/vnd.clonk.c4group",c4p:"application/vnd.clonk.c4group",c4u:"application/vnd.clonk.c4group",c11amc:"application/vnd.cluetrust.cartomobile-config",c11amz:"application/vnd.cluetrust.cartomobile-config-pkg",csp:"application/vnd.commonspace",cdbcmsg:"application/vnd.contact.cmsg",cmc:"application/vnd.cosmocaller",clkx:"application/vnd.crick.clicker",clkk:"application/vnd.crick.clicker.keyboard",clkp:"application/vnd.crick.clicker.palette",clkt:"application/vnd.crick.clicker.template",clkw:"application/vnd.crick.clicker.wordbank",wbs:"application/vnd.criticaltools.wbs+xml",pml:"application/vnd.ctc-posml",ppd:"application/vnd.cups-ppd",car:"application/vnd.curl.car",pcurl:"application/vnd.curl.pcurl",dart:"application/vnd.dart",rdz:"application/vnd.data-vision.rdz",uvf:"application/vnd.dece.data",uvvf:"application/vnd.dece.data",uvd:"application/vnd.dece.data",uvvd:"application/vnd.dece.data",uvt:"application/vnd.dece.ttml+xml",uvvt:"application/vnd.dece.ttml+xml",uvx:"application/vnd.dece.unspecified",uvvx:"application/vnd.dece.unspecified",uvz:"application/vnd.dece.zip",uvvz:"application/vnd.dece.zip",fe_launch:"application/vnd.denovo.fcselayout-link",dna:"application/vnd.dna",mlp:"application/vnd.dolby.mlp",dpg:"application/vnd.dpgraph",dfac:"application/vnd.dreamfactory",kpxx:"application/vnd.ds-keypoint",ait:"application/vnd.dvb.ait",svc:"application/vnd.dvb.service",geo:"application/vnd.dynageo",mag:"application/vnd.ecowin.chart",nml:"application/vnd.enliven",esf:"application/vnd.epson.esf",msf:"application/vnd.epson.msf",qam:"application/vnd.epson.quickanime",slt:"application/vnd.epson.salt",ssf:"application/vnd.epson.ssf",es3:"application/vnd.eszigno3+xml",et3:"application/vnd.eszigno3+xml",ez2:"application/vnd.ezpix-album",ez3:"application/vnd.ezpix-package",fdf:"application/vnd.fdf",mseed:"application/vnd.fdsn.mseed",seed:"application/vnd.fdsn.seed",dataless:"application/vnd.fdsn.seed",gph:"application/vnd.flographit",ftc:"application/vnd.fluxtime.clip",fm:"application/vnd.framemaker",frame:"application/vnd.framemaker",maker:"application/vnd.framemaker",book:"application/vnd.framemaker",fnc:"application/vnd.frogans.fnc",ltf:"application/vnd.frogans.ltf",fsc:"application/vnd.fsc.weblaunch",oas:"application/vnd.fujitsu.oasys",oa2:"application/vnd.fujitsu.oasys2",oa3:"application/vnd.fujitsu.oasys3",fg5:"application/vnd.fujitsu.oasysgp",bh2:"application/vnd.fujitsu.oasysprs",ddd:"application/vnd.fujixerox.ddd",xdw:"application/vnd.fujixerox.docuworks",xbd:"application/vnd.fujixerox.docuworks.binder",fzs:"application/vnd.fuzzysheet",txd:"application/vnd.genomatix.tuxedo",ggb:"application/vnd.geogebra.file",ggt:"application/vnd.geogebra.tool",gex:"application/vnd.geometry-explorer",gre:"application/vnd.geometry-explorer",gxt:"application/vnd.geonext",g2w:"application/vnd.geoplan",g3w:"application/vnd.geospace",gmx:"application/vnd.gmx",kml:"application/vnd.google-earth.kml+xml",kmz:"application/vnd.google-earth.kmz",gqf:"application/vnd.grafeq",gqs:"application/vnd.grafeq",gac:"application/vnd.groove-account",ghf:"application/vnd.groove-help",gim:"application/vnd.groove-identity-message",grv:"application/vnd.groove-injector",gtm:"application/vnd.groove-tool-message",tpl:"application/vnd.groove-tool-template",vcg:"application/vnd.groove-vcard",hal:"application/vnd.hal+xml",zmm:"application/vnd.handheld-entertainment+xml",hbci:"application/vnd.hbci",les:"application/vnd.hhe.lesson-player",hpgl:"application/vnd.hp-hpgl",hpid:"application/vnd.hp-hpid",hps:"application/vnd.hp-hps",jlt:"application/vnd.hp-jlyt",pcl:"application/vnd.hp-pcl",pclxl:"application/vnd.hp-pclxl","sfd-hdstx":"application/vnd.hydrostatix.sof-data",mpy:"application/vnd.ibm.minipay",afp:"application/vnd.ibm.modcap",listafp:"application/vnd.ibm.modcap",list3820:"application/vnd.ibm.modcap",irm:"application/vnd.ibm.rights-management",sc:"application/vnd.ibm.secure-container",icc:"application/vnd.iccprofile",icm:"application/vnd.iccprofile",igl:"application/vnd.igloader",ivp:"application/vnd.immervision-ivp",ivu:"application/vnd.immervision-ivu",igm:"application/vnd.insors.igm",xpw:"application/vnd.intercon.formnet",xpx:"application/vnd.intercon.formnet",i2g:"application/vnd.intergeo",qbo:"application/vnd.intu.qbo",qfx:"application/vnd.intu.qfx",rcprofile:"application/vnd.ipunplugged.rcprofile",irp:"application/vnd.irepository.package+xml",xpr:"application/vnd.is-xpr",fcs:"application/vnd.isac.fcs",jam:"application/vnd.jam",rms:"application/vnd.jcp.javame.midlet-rms",jisp:"application/vnd.jisp",joda:"application/vnd.joost.joda-archive",ktz:"application/vnd.kahootz",ktr:"application/vnd.kahootz",karbon:"application/vnd.kde.karbon",chrt:"application/vnd.kde.kchart",kfo:"application/vnd.kde.kformula",flw:"application/vnd.kde.kivio",kon:"application/vnd.kde.kontour",kpr:"application/vnd.kde.kpresenter",kpt:"application/vnd.kde.kpresenter",ksp:"application/vnd.kde.kspread",kwd:"application/vnd.kde.kword",kwt:"application/vnd.kde.kword",htke:"application/vnd.kenameaapp",kia:"application/vnd.kidspiration",kne:"application/vnd.kinar",knp:"application/vnd.kinar",skp:"application/vnd.koan",skd:"application/vnd.koan",skt:"application/vnd.koan",skm:"application/vnd.koan",sse:"application/vnd.kodak-descriptor",lasxml:"application/vnd.las.las+xml",lbd:"application/vnd.llamagraphics.life-balance.desktop",lbe:"application/vnd.llamagraphics.life-balance.exchange+xml",123:"application/vnd.lotus-1-2-3",apr:"application/vnd.lotus-approach",pre:"application/vnd.lotus-freelance",nsf:"application/vnd.lotus-notes",org:"application/vnd.lotus-organizer",scm:"application/vnd.lotus-screencam",lwp:"application/vnd.lotus-wordpro",portpkg:"application/vnd.macports.portpkg",mcd:"application/vnd.mcd",mc1:"application/vnd.medcalcdata",cdkey:"application/vnd.mediastation.cdkey",mwf:"application/vnd.mfer",mfm:"application/vnd.mfmp",flo:"application/vnd.micrografx.flo",igx:"application/vnd.micrografx.igx",mif:"application/vnd.mif",daf:"application/vnd.mobius.daf",dis:"application/vnd.mobius.dis",mbk:"application/vnd.mobius.mbk",mqy:"application/vnd.mobius.mqy",msl:"application/vnd.mobius.msl",plc:"application/vnd.mobius.plc",txf:"application/vnd.mobius.txf",mpn:"application/vnd.mophun.application",mpc:"application/vnd.mophun.certificate",xul:"application/vnd.mozilla.xul+xml",cil:"application/vnd.ms-artgalry",cab:"application/vnd.ms-cab-compressed",xls:"application/vnd.ms-excel",xlm:"application/vnd.ms-excel",xla:"application/vnd.ms-excel",xlc:"application/vnd.ms-excel",xlt:"application/vnd.ms-excel",xlw:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xltm:"application/vnd.ms-excel.template.macroenabled.12",eot:"application/vnd.ms-fontobject",chm:"application/vnd.ms-htmlhelp",ims:"application/vnd.ms-ims",lrm:"application/vnd.ms-lrm",thmx:"application/vnd.ms-officetheme",cat:"application/vnd.ms-pki.seccat",stl:"application/vnd.ms-pki.stl",ppt:"application/vnd.ms-powerpoint",pps:"application/vnd.ms-powerpoint",pot:"application/vnd.ms-powerpoint",ppam:"application/vnd.ms-powerpoint.addin.macroenabled.12",pptm:"application/vnd.ms-powerpoint.presentation.macroenabled.12",sldm:"application/vnd.ms-powerpoint.slide.macroenabled.12",ppsm:"application/vnd.ms-powerpoint.slideshow.macroenabled.12",potm:"application/vnd.ms-powerpoint.template.macroenabled.12",mpp:"application/vnd.ms-project",mpt:"application/vnd.ms-project",docm:"application/vnd.ms-word.document.macroenabled.12",dotm:"application/vnd.ms-word.template.macroenabled.12",wps:"application/vnd.ms-works",wks:"application/vnd.ms-works",wcm:"application/vnd.ms-works",wdb:"application/vnd.ms-works",wpl:"application/vnd.ms-wpl",xps:"application/vnd.ms-xpsdocument",mseq:"application/vnd.mseq",mus:"application/vnd.musician",msty:"application/vnd.muvee.style",taglet:"application/vnd.mynfc",nlu:"application/vnd.neurolanguage.nlu",ntf:"application/vnd.nitf",nitf:"application/vnd.nitf",nnd:"application/vnd.noblenet-directory",nns:"application/vnd.noblenet-sealer",nnw:"application/vnd.noblenet-web",ngdat:"application/vnd.nokia.n-gage.data","n-gage":"application/vnd.nokia.n-gage.symbian.install",rpst:"application/vnd.nokia.radio-preset",rpss:"application/vnd.nokia.radio-presets",edm:"application/vnd.novadigm.edm",edx:"application/vnd.novadigm.edx",ext:"application/vnd.novadigm.ext",odc:"application/vnd.oasis.opendocument.chart",otc:"application/vnd.oasis.opendocument.chart-template",odb:"application/vnd.oasis.opendocument.database",odf:"application/vnd.oasis.opendocument.formula",odft:"application/vnd.oasis.opendocument.formula-template",odg:"application/vnd.oasis.opendocument.graphics",otg:"application/vnd.oasis.opendocument.graphics-template",odi:"application/vnd.oasis.opendocument.image",oti:"application/vnd.oasis.opendocument.image-template",odp:"application/vnd.oasis.opendocument.presentation",otp:"application/vnd.oasis.opendocument.presentation-template",ods:"application/vnd.oasis.opendocument.spreadsheet",ots:"application/vnd.oasis.opendocument.spreadsheet-template",odt:"application/vnd.oasis.opendocument.text",odm:"application/vnd.oasis.opendocument.text-master",ott:"application/vnd.oasis.opendocument.text-template",oth:"application/vnd.oasis.opendocument.text-web",xo:"application/vnd.olpc-sugar",dd2:"application/vnd.oma.dd2+xml",oxt:"application/vnd.openofficeorg.extension",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",sldx:"application/vnd.openxmlformats-officedocument.presentationml.slide",ppsx:"application/vnd.openxmlformats-officedocument.presentationml.slideshow",potx:"application/vnd.openxmlformats-officedocument.presentationml.template",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",mgp:"application/vnd.osgeo.mapguide.package",dp:"application/vnd.osgi.dp",esa:"application/vnd.osgi.subsystem",pdb:"application/vnd.palm",pqa:"application/vnd.palm",oprc:"application/vnd.palm",paw:"application/vnd.pawaafile",str:"application/vnd.pg.format",ei6:"application/vnd.pg.osasli",efif:"application/vnd.picsel",wg:"application/vnd.pmi.widget",plf:"application/vnd.pocketlearn",pbd:"application/vnd.powerbuilder6",box:"application/vnd.previewsystems.box",mgz:"application/vnd.proteus.magazine",qps:"application/vnd.publishare-delta-tree",ptid:"application/vnd.pvi.ptid1",qxd:"application/vnd.quark.quarkxpress",qxt:"application/vnd.quark.quarkxpress",qwd:"application/vnd.quark.quarkxpress",qwt:"application/vnd.quark.quarkxpress",qxl:"application/vnd.quark.quarkxpress",qxb:"application/vnd.quark.quarkxpress",bed:"application/vnd.realvnc.bed",mxl:"application/vnd.recordare.musicxml",musicxml:"application/vnd.recordare.musicxml+xml",cryptonote:"application/vnd.rig.cryptonote",cod:"application/vnd.rim.cod",rm:"application/vnd.rn-realmedia",rmvb:"application/vnd.rn-realmedia-vbr",link66:"application/vnd.route66.link66+xml",st:"application/vnd.sailingtracker.track",see:"application/vnd.seemail",sema:"application/vnd.sema",semd:"application/vnd.semd",semf:"application/vnd.semf",ifm:"application/vnd.shana.informed.formdata",itp:"application/vnd.shana.informed.formtemplate",iif:"application/vnd.shana.informed.interchange",ipk:"application/vnd.shana.informed.package",twd:"application/vnd.simtech-mindmapper",twds:"application/vnd.simtech-mindmapper",mmf:"application/vnd.smaf",teacher:"application/vnd.smart.teacher",sdkm:"application/vnd.solent.sdkm+xml",sdkd:"application/vnd.solent.sdkm+xml",dxp:"application/vnd.spotfire.dxp",sfs:"application/vnd.spotfire.sfs",sdc:"application/vnd.stardivision.calc",sda:"application/vnd.stardivision.draw",sdd:"application/vnd.stardivision.impress",smf:"application/vnd.stardivision.math",sdw:"application/vnd.stardivision.writer",vor:"application/vnd.stardivision.writer",sgl:"application/vnd.stardivision.writer-global",smzip:"application/vnd.stepmania.package",sm:"application/vnd.stepmania.stepchart",sxc:"application/vnd.sun.xml.calc",stc:"application/vnd.sun.xml.calc.template",sxd:"application/vnd.sun.xml.draw",std:"application/vnd.sun.xml.draw.template",sxi:"application/vnd.sun.xml.impress",sti:"application/vnd.sun.xml.impress.template",sxm:"application/vnd.sun.xml.math",sxw:"application/vnd.sun.xml.writer",sxg:"application/vnd.sun.xml.writer.global",stw:"application/vnd.sun.xml.writer.template",sus:"application/vnd.sus-calendar",susp:"application/vnd.sus-calendar",svd:"application/vnd.svd",sis:"application/vnd.symbian.install",sisx:"application/vnd.symbian.install",xsm:"application/vnd.syncml+xml",bdm:"application/vnd.syncml.dm+wbxml",xdm:"application/vnd.syncml.dm+xml",tao:"application/vnd.tao.intent-module-archive",pcap:"application/vnd.tcpdump.pcap",cap:"application/vnd.tcpdump.pcap",dmp:"application/vnd.tcpdump.pcap",tmo:"application/vnd.tmobile-livetv",tpt:"application/vnd.trid.tpt",mxs:"application/vnd.triscape.mxs",tra:"application/vnd.trueapp",ufd:"application/vnd.ufdl",ufdl:"application/vnd.ufdl",utz:"application/vnd.uiq.theme",umj:"application/vnd.umajin",unityweb:"application/vnd.unity",uoml:"application/vnd.uoml+xml",vcx:"application/vnd.vcx",vsd:"application/vnd.visio",vst:"application/vnd.visio",vss:"application/vnd.visio",vsw:"application/vnd.visio",vis:"application/vnd.visionary",vsf:"application/vnd.vsf",wbxml:"application/vnd.wap.wbxml",wmlc:"application/vnd.wap.wmlc",wmlsc:"application/vnd.wap.wmlscriptc",wtb:"application/vnd.webturbo",nbp:"application/vnd.wolfram.player",wpd:"application/vnd.wordperfect",wqd:"application/vnd.wqd",stf:"application/vnd.wt.stf",xar:"application/vnd.xara",xfdl:"application/vnd.xfdl",hvd:"application/vnd.yamaha.hv-dic",hvs:"application/vnd.yamaha.hv-script",hvp:"application/vnd.yamaha.hv-voice",osf:"application/vnd.yamaha.openscoreformat",osfpvg:"application/vnd.yamaha.openscoreformat.osfpvg+xml",saf:"application/vnd.yamaha.smaf-audio",spf:"application/vnd.yamaha.smaf-phrase",cmp:"application/vnd.yellowriver-custom-menu",zir:"application/vnd.zul",zirz:"application/vnd.zul",zaz:"application/vnd.zzazz.deck+xml",vxml:"application/voicexml+xml",wgt:"application/widget",hlp:"application/winhlp",wsdl:"application/wsdl+xml",wspolicy:"application/wspolicy+xml","7z":"application/x-7z-compressed",abw:"application/x-abiword",ace:"application/x-ace-compressed",dmg:"application/x-apple-diskimage",aab:"application/x-authorware-bin",x32:"application/x-authorware-bin",u32:"application/x-authorware-bin",vox:"application/x-authorware-bin",aam:"application/x-authorware-map",aas:"application/x-authorware-seg",bcpio:"application/x-bcpio",torrent:"application/x-bittorrent",blb:"application/x-blorb",blorb:"application/x-blorb",bz:"application/x-bzip",bz2:"application/x-bzip2",boz:"application/x-bzip2",cbr:"application/x-cbr",cba:"application/x-cbr",cbt:"application/x-cbr",cbz:"application/x-cbr",cb7:"application/x-cbr",vcd:"application/x-cdlink",cfs:"application/x-cfs-compressed",chat:"application/x-chat",pgn:"application/x-chess-pgn",nsc:"application/x-conference",cpio:"application/x-cpio",csh:"application/x-csh",deb:"application/x-debian-package",udeb:"application/x-debian-package",dgc:"application/x-dgc-compressed",dir:"application/x-director",dcr:"application/x-director",dxr:"application/x-director",cst:"application/x-director",cct:"application/x-director",cxt:"application/x-director",w3d:"application/x-director",fgd:"application/x-director",swa:"application/x-director",wad:"application/x-doom",ncx:"application/x-dtbncx+xml",dtb:"application/x-dtbook+xml",res:"application/x-dtbresource+xml",dvi:"application/x-dvi",evy:"application/x-envoy",eva:"application/x-eva",bdf:"application/x-font-bdf",gsf:"application/x-font-ghostscript",psf:"application/x-font-linux-psf",otf:"application/x-font-otf",pcf:"application/x-font-pcf",snf:"application/x-font-snf",ttf:"application/x-font-ttf",ttc:"application/x-font-ttf",pfa:"application/x-font-type1",pfb:"application/x-font-type1",pfm:"application/x-font-type1",afm:"application/x-font-type1",woff:"application/x-font-woff",arc:"application/x-freearc",spl:"application/x-futuresplash",gca:"application/x-gca-compressed",ulx:"application/x-glulx",gnumeric:"application/x-gnumeric",gramps:"application/x-gramps-xml",gtar:"application/x-gtar",tbz:"application/x-gtar",tgz:"application/x-gtar","tar.gz":"application/x-gtar",tbz2:"application/x-gtar","tar.bz2":"application/x-gtar",gz:"application/x-gzip",hdf:"application/x-hdf",install:"application/x-install-instructions",iso:"application/x-iso9660-image",jnlp:"application/x-java-jnlp-file",latex:"application/x-latex",lzh:"application/x-lzh-compressed",lha:"application/x-lzh-compressed",mie:"application/x-mie",prc:"application/x-mobipocket-ebook",mobi:"application/x-mobipocket-ebook",application:"application/x-ms-application",lnk:"application/x-ms-shortcut",wmd:"application/x-ms-wmd",xbap:"application/x-ms-xbap",mdb:"application/x-msaccess",obd:"application/x-msbinder",crd:"application/x-mscardfile",clp:"application/x-msclip",exe:"application/x-msdownload",dll:"application/x-msdownload",com:"application/x-msdownload",bat:"application/x-msdownload",msi:"application/x-msdownload",mvb:"application/x-msmediaview",m13:"application/x-msmediaview",m14:"application/x-msmediaview",wmf:"application/x-msmetafile",wmz:"application/x-msmetafile",emf:"application/x-msmetafile",emz:"application/x-msmetafile",mny:"application/x-msmoney",pub:"application/x-mspublisher",scd:"application/x-msschedule",trm:"application/x-msterminal",wri:"application/x-mswrite",nc:"application/x-netcdf",cdf:"application/x-netcdf",nzb:"application/x-nzb",p12:"application/x-pkcs12",pfx:"application/x-pkcs12",p7b:"application/x-pkcs7-certificates",spc:"application/x-pkcs7-certificates",p7r:"application/x-pkcs7-certreqresp",rar:"application/x-rar-compressed",ris:"application/x-research-info-systems",sh:"application/x-sh",shar:"application/x-shar",swf:"application/x-shockwave-flash",xap:"application/x-silverlight-app",sql:"application/x-sql",sit:"application/x-stuffit",sitx:"application/x-stuffitx",srt:"application/x-subrip",sv4cpio:"application/x-sv4cpio",sv4crc:"application/x-sv4crc",t3:"application/x-t3vm-image",gam:"application/x-tads",tar:"application/x-tar",tcl:"application/x-tcl",tex:"application/x-tex",tfm:"application/x-tex-tfm",texinfo:"application/x-texinfo",texi:"application/x-texinfo",obj:"application/x-tgif",ustar:"application/x-ustar",src:"application/x-wais-source",der:"application/x-x509-ca-cert",crt:"application/x-x509-ca-cert",fig:"application/x-xfig",xlf:"application/x-xliff+xml",xpi:"application/x-xpinstall",xz:"application/x-xz",z1:"application/x-zmachine",z2:"application/x-zmachine",z3:"application/x-zmachine",z4:"application/x-zmachine",z5:"application/x-zmachine",z6:"application/x-zmachine",
z7:"application/x-zmachine",z8:"application/x-zmachine",xaml:"application/xaml+xml",xdf:"application/xcap-diff+xml",xenc:"application/xenc+xml",xhtml:"application/xhtml+xml",xht:"application/xhtml+xml",xml:"application/xml",xsl:"application/xml",dtd:"application/xml-dtd",xop:"application/xop+xml",xpl:"application/xproc+xml",xslt:"application/xslt+xml",xspf:"application/xspf+xml",mxml:"application/xv+xml",xhvml:"application/xv+xml",xvml:"application/xv+xml",xvm:"application/xv+xml",yang:"application/yang",yin:"application/yin+xml",zip:"application/zip",adp:"audio/adpcm",au:"audio/basic",snd:"audio/basic",mid:"audio/midi",midi:"audio/midi",kar:"audio/midi",rmi:"audio/midi",mp4a:"audio/mp4",mpga:"audio/mpeg",mp2:"audio/mpeg",mp2a:"audio/mpeg",mp3:"audio/mpeg",m2a:"audio/mpeg",m3a:"audio/mpeg",oga:"audio/ogg",ogg:"audio/ogg",spx:"audio/ogg",s3m:"audio/s3m",sil:"audio/silk",uva:"audio/vnd.dece.audio",uvva:"audio/vnd.dece.audio",eol:"audio/vnd.digital-winds",dra:"audio/vnd.dra",dts:"audio/vnd.dts",dtshd:"audio/vnd.dts.hd",lvp:"audio/vnd.lucent.voice",pya:"audio/vnd.ms-playready.media.pya",ecelp4800:"audio/vnd.nuera.ecelp4800",ecelp7470:"audio/vnd.nuera.ecelp7470",ecelp9600:"audio/vnd.nuera.ecelp9600",rip:"audio/vnd.rip",weba:"audio/webm",aac:"audio/x-aac",aif:"audio/x-aiff",aiff:"audio/x-aiff",aifc:"audio/x-aiff",caf:"audio/x-caf",flac:"audio/x-flac",mka:"audio/x-matroska",m3u:"audio/x-mpegurl",wax:"audio/x-ms-wax",wma:"audio/x-ms-wma",ram:"audio/x-pn-realaudio",ra:"audio/x-pn-realaudio",rmp:"audio/x-pn-realaudio-plugin",wav:"audio/x-wav",xm:"audio/xm",cdx:"chemical/x-cdx",cif:"chemical/x-cif",cmdf:"chemical/x-cmdf",cml:"chemical/x-cml",csml:"chemical/x-csml",xyz:"chemical/x-xyz",bmp:"image/bmp",cgm:"image/cgm",g3:"image/g3fax",gif:"image/gif",ief:"image/ief",jpeg:"image/jpeg",jpg:"image/jpeg",jpe:"image/jpeg",ktx:"image/ktx",png:"image/png",btif:"image/prs.btif",sgi:"image/sgi",svg:"image/svg+xml",svgz:"image/svg+xml",tiff:"image/tiff",tif:"image/tiff",psd:"image/vnd.adobe.photoshop",uvi:"image/vnd.dece.graphic",uvvi:"image/vnd.dece.graphic",uvg:"image/vnd.dece.graphic",uvvg:"image/vnd.dece.graphic",sub:"image/vnd.dvb.subtitle",djvu:"image/vnd.djvu",djv:"image/vnd.djvu",dwg:"image/vnd.dwg",dxf:"image/vnd.dxf",fbs:"image/vnd.fastbidsheet",fpx:"image/vnd.fpx",fst:"image/vnd.fst",mmr:"image/vnd.fujixerox.edmics-mmr",rlc:"image/vnd.fujixerox.edmics-rlc",mdi:"image/vnd.ms-modi",wdp:"image/vnd.ms-photo",npx:"image/vnd.net-fpx",wbmp:"image/vnd.wap.wbmp",xif:"image/vnd.xiff",webp:"image/webp","3ds":"image/x-3ds",ras:"image/x-cmu-raster",cmx:"image/x-cmx",fh:"image/x-freehand",fhc:"image/x-freehand",fh4:"image/x-freehand",fh5:"image/x-freehand",fh7:"image/x-freehand",ico:"image/x-icon",sid:"image/x-mrsid-image",pcx:"image/x-pcx",pic:"image/x-pict",pct:"image/x-pict",pnm:"image/x-portable-anymap",pbm:"image/x-portable-bitmap",pgm:"image/x-portable-graymap",ppm:"image/x-portable-pixmap",rgb:"image/x-rgb",tga:"image/x-tga",xbm:"image/x-xbitmap",xpm:"image/x-xpixmap",xwd:"image/x-xwindowdump",eml:"message/rfc822",mime:"message/rfc822",igs:"model/iges",iges:"model/iges",msh:"model/mesh",mesh:"model/mesh",silo:"model/mesh",dae:"model/vnd.collada+xml",dwf:"model/vnd.dwf",gdl:"model/vnd.gdl",gtw:"model/vnd.gtw",mts:"model/vnd.mts",vtu:"model/vnd.vtu",wrl:"model/vrml",vrml:"model/vrml",x3db:"model/x3d+binary",x3dbz:"model/x3d+binary",x3dv:"model/x3d+vrml",x3dvz:"model/x3d+vrml",x3d:"model/x3d+xml",x3dz:"model/x3d+xml",appcache:"text/cache-manifest",ics:"text/calendar",ifb:"text/calendar",css:"text/css",csv:"text/csv",html:"text/html",htm:"text/html",n3:"text/n3",txt:"text/plain",text:"text/plain",conf:"text/plain",def:"text/plain",list:"text/plain",log:"text/plain","in":"text/plain",dsc:"text/prs.lines.tag",rtx:"text/richtext",sgml:"text/sgml",sgm:"text/sgml",tsv:"text/tab-separated-values",t:"text/troff",tr:"text/troff",roff:"text/troff",man:"text/troff",me:"text/troff",ms:"text/troff",ttl:"text/turtle",uri:"text/uri-list",uris:"text/uri-list",urls:"text/uri-list",vcard:"text/vcard",curl:"text/vnd.curl",dcurl:"text/vnd.curl.dcurl",scurl:"text/vnd.curl.scurl",mcurl:"text/vnd.curl.mcurl",fly:"text/vnd.fly",flx:"text/vnd.fmi.flexstor",gv:"text/vnd.graphviz","3dml":"text/vnd.in3d.3dml",spot:"text/vnd.in3d.spot",jad:"text/vnd.sun.j2me.app-descriptor",wml:"text/vnd.wap.wml",wmls:"text/vnd.wap.wmlscript",s:"text/x-asm",asm:"text/x-asm",c:"text/x-c",cc:"text/x-c",cxx:"text/x-c",cpp:"text/x-c",h:"text/x-c",hh:"text/x-c",dic:"text/x-c",f:"text/x-fortran","for":"text/x-fortran",f77:"text/x-fortran",f90:"text/x-fortran",java:"text/x-java-source",opml:"text/x-opml",p:"text/x-pascal",pas:"text/x-pascal",nfo:"text/x-nfo",etx:"text/x-setext",sfv:"text/x-sfv",uu:"text/x-uuencode",vcs:"text/x-vcalendar",vcf:"text/x-vcard","3gp":"video/3gpp","3g2":"video/3gpp2",h261:"video/h261",h263:"video/h263",h264:"video/h264",jpgv:"video/jpeg",jpm:"video/jpm",jpgm:"video/jpm",mj2:"video/mj2",mjp2:"video/mj2",mp4:"video/mp4",mp4v:"video/mp4",mpg4:"video/mp4",mpeg:"video/mpeg",mpg:"video/mpeg",mpe:"video/mpeg",m1v:"video/mpeg",m2v:"video/mpeg",ogv:"video/ogg",qt:"video/quicktime",mov:"video/quicktime",uvh:"video/vnd.dece.hd",uvvh:"video/vnd.dece.hd",uvm:"video/vnd.dece.mobile",uvvm:"video/vnd.dece.mobile",uvp:"video/vnd.dece.pd",uvvp:"video/vnd.dece.pd",uvs:"video/vnd.dece.sd",uvvs:"video/vnd.dece.sd",uvv:"video/vnd.dece.video",uvvv:"video/vnd.dece.video",dvb:"video/vnd.dvb.file",fvt:"video/vnd.fvt",mxu:"video/vnd.mpegurl",m4u:"video/vnd.mpegurl",pyv:"video/vnd.ms-playready.media.pyv",uvu:"video/vnd.uvvu.mp4",uvvu:"video/vnd.uvvu.mp4",viv:"video/vnd.vivo",webm:"video/webm",f4v:"video/x-f4v",fli:"video/x-fli",flv:"video/x-flv",m4v:"video/x-m4v",mkv:"video/x-matroska",mk3d:"video/x-matroska",mks:"video/x-matroska",mng:"video/x-mng",asf:"video/x-ms-asf",asx:"video/x-ms-asf",vob:"video/x-ms-vob",wm:"video/x-ms-wm",wmv:"video/x-ms-wmv",wmx:"video/x-ms-wmx",wvx:"video/x-ms-wvx",avi:"video/x-msvideo",movie:"video/x-sgi-movie",smv:"video/x-smv",ice:"x-conference/x-cooltalk"};n.guess=function(t){return t&&t.length?("."===t[0]&&(t=t.substr(1)),r[t.toLowerCase()]||"application/octet-stream"):"application/octet-stream"}},{}],190:[function(t,e,n){(function(n){function r(t){this._boundary=t,this._parts=[]}var i=t(150),o=t(172);r.prototype.addPart=function(t,e){var r=[],a=i.format('--%s\r\nContent-Disposition: form-data; name="%s"%s\r\n\r\n',this._boundary,t,"");if(r.push(new n(a)),n.isBuffer(e))r.push(e),r.push(new n("\r\n"));else{if(!o.isString(e))throw Error("Invalid data type.");r.push(new n(e+"\r\n"))}this._parts.push(n.concat(r))},r.prototype.encode=function(){return n.concat([n.concat(this._parts),new n(i.format("--%s--",this._boundary))])},e.exports=r}).call(this,t(48).Buffer)},{150:150,172:172,48:48}],191:[function(t,e,n){(function(n){function r(t){a.call(this,t,"face",!0)}var i=t(150),o=t(168)("bce-sdk:OCRClient"),a=t(176);i.inherits(r,a),r.prototype._apiCall=function(t,e,r,i){return o("url = %j, data = %j, language = %j, options = %j",t,e,r,i),i=i||{},e=n.isBuffer(e)?{base64:e.toString("base64")}:{bosPath:e},r&&(e.language=r),this.sendRequest("POST",t,{body:JSON.stringify(e),config:i.config})},r.prototype.allText=function(t,e,n){return this._apiCall("/v1/recognize/text",t,e,n)},r.prototype.oneLine=function(t,e,n){return this._apiCall("/v1/recognize/line",t,e,n)},r.prototype.singleCharacter=function(t,e,n){return this._apiCall("/v1/recognize/character",t,e,n)},e.exports=r}).call(this,{isBuffer:t(102)})},{102:102,150:150,168:168,176:176}],192:[function(t,e,n){function r(t,e,n){a.call(this,t,"qns",!0),this._account=e,this._name=n}function i(t,e,n){a.call(this,t,"qns",!0),this._account=e,this._name=n}e=t(150);var o=t(172),a=t(176);e.inherits(r,a),r.prototype._buildUrl=function(){return"/v1/"+this._account+"/topic/"+this._name},r.prototype.create=function(t){return t=t||{},t=o.pick(t,"delayInSeconds","maximumMessageSizeInBytes","messageRetentionPeriodInSeconds"),this.sendRequest("PUT",this._buildUrl(),{body:JSON.stringify(t)})},r.prototype.remove=function(){return this.sendRequest("DELETE",this._buildUrl())},r.prototype.get=function(){return this.sendRequest("GET",this._buildUrl())},r.prototype.update=function(t){return t=t||{},t=o.pick(t,"delayInSeconds","maximumMessageSizeInBytes","messageRetentionPeriodInSeconds"),this.sendRequest("PUT",this._buildUrl(),{headers:{"If-Match":"*"},body:JSON.stringify(t)})},r.prototype.sendMessages=function(t){var e=this._buildUrl()+"/message";return t=o.map(t,function(t){return o.isString(t)?{messageBody:t}:t}),this.sendRequest("POST",e,{body:JSON.stringify({messages:t})})},r.prototype.list=function(t){return t=t||{},t=o.pick(t,"marker","maxRecords"),this.sendRequest("GET","/v1/"+this._account+"/topic",{params:t})},r.prototype.createSubscription=function(t,e){e=e||{};var n=new i(this.config,this._account,t);return null==e.topic&&(e.topic=this._name),n.create(e)},e.inherits(i,a),i.prototype._buildUrl=function(){return"/v1/"+this._account+"/subscription/"+this._name},i.prototype.create=function(t){return t=t||{},t=o.pick(t,"receiveMessageWaitTimeInSeconds","topic","visibilityTimeoutInSeconds","pushConfig"),this.sendRequest("PUT",this._buildUrl(),{body:JSON.stringify(t)})},i.prototype.remove=function(){return this.sendRequest("DELETE",this._buildUrl())},i.prototype.get=function(){return this.sendRequest("GET",this._buildUrl())},i.prototype.update=function(t){return t=t||{},t=o.pick(t,"receiveMessageWaitTimeInSeconds","visibilityTimeoutInSeconds"),this.sendRequest("PUT",this._buildUrl(),{headers:{"If-Match":"*"},body:JSON.stringify(t)})},i.prototype.receiveMessages=function(t){t=t||{},t=o.pick(t,"waitInSeconds","maxMessages","peek");var e=this._buildUrl()+"/message";return this.sendRequest("GET",e,{body:JSON.stringify(t)})},i.prototype.deleteMessage=function(t){var e=this._buildUrl()+"/message";return this.sendRequest("DELETE",e,{params:{receiptHandle:t}})},i.prototype.changeVisibility=function(t,e){var n=this._buildUrl()+"/message";return this.sendRequest("PUT",n,{params:{receiptHandle:t,visibilityTimeoutInSeconds:e}})},i.prototype.list=function(t){return t=t||{},t=o.pick(t,"marker","maxRecords"),this.sendRequest("GET","/v1/"+this._account+"/subscription",{params:t})},n.Topic=r,n.Subscription=i},{150:150,172:172,176:176}],193:[function(t,e,n){function r(t){a.call(this,t,"ses",!0)}var i=t(46),o=t(111);n=t(150);var a=t(176);n.inherits(r,a),r.prototype.addVerifiedEmail=function(t){return t="/v1/verifiedEmail/"+encodeURIComponent(t),this.sendRequest("PUT",t)},r.prototype.getAllVerifiedEmails=function(){return this.sendRequest("GET","/v1/verifiedEmail")},r.prototype.getVerifiedEmail=function(t){return t="/v1/verifiedEmail/"+encodeURIComponent(t),this.sendRequest("GET",t)},r.prototype.deleteVerifiedEmail=function(t){return t="/v1/verifiedEmail/"+encodeURIComponent(t),this.sendRequest("DELETE",t)},r.prototype.getQuota=function(){return this.sendRequest("GET","/v1/quota")},r.prototype.setQuota=function(t){return Object.keys(t).forEach(function(e){t[e]=t[e].toString()}),this.sendRequest("PUT","/v1/quota",{body:JSON.stringify(t)})},r.prototype.sendMail=function(t){var e=t.from||"",n=t.to||[];"string"==typeof n&&(n=[n]);var r=t.cc||[];"string"==typeof r&&(r=[r]);var a=t.bcc||[];"string"==typeof a&&(a=[a]);var s=t.subject,c=t.text||"",f=t.html||"";return t=t.attachments||[],t=t.map(function(t){return"string"==typeof t?{file_name:o.basename(t),file_data:{data:i.readFileSync(t).toString("base64")}}:t}),e=JSON.stringify({mail:{source:{from:e},destination:{to_addr:n.map(function(t){return{addr:t}}),cc_addr:r.map(function(t){return{addr:t}}),bcc_addr:a.map(function(t){return{addr:t}})},subject:{charset:1,data:s},priority:1,message:{text:{charset:1,data:c},html:{charset:1,data:f}},attachments:t}}),this.sendRequest("POST","/v1/email",{body:e})},e.exports=r},{111:111,150:150,176:176,46:46}],194:[function(t,e,n){var r={"!":"%21","'":"%27","(":"%28",")":"%29","*":"%2A"};n.normalize=function(t,e){var n=encodeURIComponent(t),n=n.replace(/[!'\(\)\*]/g,function(t){return r[t]});return!1===e&&(n=n.replace(/%2F/gi,"/")),n},n.trim=function(t){return(t||"").replace(/^\s+|\s+$/g,"")}},{}],195:[function(t,e,n){function r(t){o.call(this,t,"sts",!0)}n=t(150);var i=t(172),o=t(176);n.inherits(r,o),r.prototype.getSessionToken=function(t,e,n){n=n||{};var r="";return e&&(e=i.pick(e,"id","accessControlList"),e.accessControlList&&(e.accessControlList=i.map(e.accessControlList,function(t){return i.pick(t,"eid","service","region","effect","resource","permission")})),r=JSON.stringify(e)),this.sendRequest("POST","/v1/sessionToken",{config:n.config,params:{durationSeconds:t},body:r})},e.exports=r},{150:150,172:172,176:176}],196:[function(t,e,n){function r(t){o.call(this,t,"vod",!1),t=this.config.bos||{},t.credentials||(t.credentials=this.config.credentials),t.sessionToken||(t.sessionToken=this.config.sessionToken),this._bosClient=new a(t);var e=this;this._bosClient.on("progress",function(t){e.emit("progress",t)})}n=t(150);var i=t(172),o=t(176),a=t(178),s=t(183),c=t(184);n.inherits(r,o),r.prototype.createMediaResource=function(t,e,n,r){r=r||{};var i,o=this,a=this._bosClient;return o._generateMediaId(r).then(function(t){return i=t.body.mediaId,c.upload(a,t.body.sourceBucket,t.body.sourceKey,n,r)}).then(function(){return o._internalCreateMediaResource(i,t,e,r)})},r.prototype.getMediaResource=function(t,e){return this.buildRequest("GET",t,null,e)},r.prototype.listMediaResource=function(t){return this.buildRequest("GET",null,null,t)},r.prototype.listMediaResources=r.prototype.listMediaResource,r.prototype.updateMediaResource=function(t,e,n,r){return r=r||{},this.buildRequest("PUT",t,"attributes",i.extend(r,{body:JSON.stringify({title:e,description:n})}))},r.prototype.stopMediaResource=function(t,e){return this.buildRequest("PUT",t,"disable",e)},r.prototype.publishMediaResource=function(t,e){return this.buildRequest("PUT",t,"publish",e)},r.prototype.deleteMediaResource=function(t,e){return this.buildRequest("DELETE",t,null,e)},r.prototype.rerunMediaResource=function(t,e){return this.buildRequest("PUT",t,"rerun",e)},r.prototype.getPlayableUrl=function(t,e){return e=e||{},this._buildRequest("GET","/v1/service/file",null,null,i.extend(e,{params:{media_id:t}}))},r.prototype.getPlayerCode=function(t,e,n,r,o){return o=o||{},this._buildRequest("GET","/v1/service/code",null,null,i.extend(o,{params:{media_id:t,ak:this.config.credentials.ak,width:e,height:n,auto_start:r}}))},r.prototype._generateMediaId=function(t){return this.buildRequest("GET","internal",null,t)},r.prototype._internalCreateMediaResource=function(t,e,n,r){return e={title:e},n&&(e.description=n),r=r||{},r.sourceExtension&&(e.sourceExtension=r.sourceExtension,delete r.sourceExtension),this.buildRequest("PUT",t,"process",i.extend(r,{body:JSON.stringify(e)}))},r.prototype.buildRequest=function(t,e,n,r){return this._buildRequest(t,"/v1/media",e,n,r)},r.prototype._buildRequest=function(t,e,n,r,o){return o=i.extend({body:null,headers:{},params:{},config:{}},o),n&&(e+="/"+n),r&&(o.params[r]=""),o.headers.hasOwnProperty(s.CONTENT_TYPE)||(o.headers[s.CONTENT_TYPE]="application/json"),o.headers.hasOwnProperty(s.ACCEPT_ENCODING)||(o.headers[s.ACCEPT_ENCODING]="gzip, deflate"),o.headers.hasOwnProperty(s.ACCEPT)||(o.headers[s.ACCEPT]="*/*"),this.sendRequest(t,e,o)},e.exports=r},{150:150,172:172,176:176,178:178,183:183,184:184}],197:[function(t,e,n){(function(n){function r(){i.Writable.call(this),this.store=new n("")}var i=t(145);t(150).inherits(r,i.Writable),r.prototype._write=function(t,e,r){t=n.isBuffer(t)?t:new n(t,e),this.store=n.concat([this.store,t]),r()},e.exports=r}).call(this,t(48).Buffer)},{145:145,150:150,48:48}]},{},[1])(1)});
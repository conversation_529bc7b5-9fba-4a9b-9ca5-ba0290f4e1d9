!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,e.baidubce=t()}}(function(){return function t(e,n,r){function o(s,a){if(!n[s]){if(!e[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(i)return i(s,!0);var c=new Error("Cannot find module '"+s+"'");throw c.code="MODULE_NOT_FOUND",c}var p=n[s]={exports:{}};e[s][0].call(p.exports,function(t){var n=e[s][1][t];return o(n?n:t)},p,p.exports,t,e,n,r)}return n[s].exports}for(var i="function"==typeof require&&require,s=0;s<r.length;s++)o(r[s]);return o}({1:[function(t,e,n){var r=t(44),o=t(17),i=t(15),s=t(31),a=t(32);e.exports={bos:{Uploader:s},utils:a,sdk:{Q:r,BosClient:o,Auth:i}}},{15:15,17:17,31:31,32:32,44:44}],2:[function(t,e,n){"use strict";var r=t(9),o=t(3);e.exports=o(r)},{3:3,9:9}],3:[function(t,e,n){"use strict";var r=t(4);e.exports=function(t){return function(e,n,o,i){return t(r(n),e,o,i)}}},{4:4}],4:[function(t,e,n){var r=t(11),o=t(10),i=t(12),s=t(7);e.exports=function(t){return function(e,n,a){a=r(a||o),e=e||[];var u=s(e);if(0>=t)return a(null);var c=!1,p=0,l=!1;!function f(){if(c&&0>=p)return a(null);for(;t>p&&!l;){var r=u();if(null===r)return c=!0,void(0>=p&&a(null));p+=1,n(e[r],r,i(function(t){p-=1,t?(a(t),l=!0):f()}))}}()}}},{10:10,11:11,12:12,7:7}],5:[function(t,e,n){"use strict";e.exports=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},{}],6:[function(t,e,n){"use strict";var r=t(5);e.exports=function(t){return r(t)||"number"==typeof t.length&&t.length>=0&&t.length%1===0}},{5:5}],7:[function(t,e,n){"use strict";var r=t(8),o=t(6);e.exports=function(t){var e,n,i=-1;return o(t)?(e=t.length,function(){return i++,e>i?i:null}):(n=r(t),e=n.length,function(){return i++,e>i?n[i]:null})}},{6:6,8:8}],8:[function(t,e,n){"use strict";e.exports=Object.keys||function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}},{}],9:[function(t,e,n){"use strict";var r=t(11),o=t(10),i=t(6);e.exports=function(t,e,n,s){s=r(s||o),e=e||[];var a=i(e)?[]:{};t(e,function(t,e,r){n(t,function(t,n){a[e]=n,r(t)})},function(t){s(t,a)})}},{10:10,11:11,6:6}],10:[function(t,e,n){"use strict";e.exports=function(){}},{}],11:[function(t,e,n){"use strict";e.exports=function(t){return function(){null!==t&&(t.apply(this,arguments),t=null)}}},{}],12:[function(t,e,n){"use strict";e.exports=function(t){return function(){if(null===t)throw new Error("Callback was already called.");t.apply(this,arguments),t=null}}},{}],13:[function(t,e,n){function r(t){return!!t&&"object"==typeof t}function o(t){return"number"==typeof t||r(t)&&a.call(t)==i}var i="[object Number]",s=Object.prototype,a=s.toString;e.exports=o},{}],14:[function(t,e,n){function r(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}e.exports=r},{}],15:[function(t,e,n){function r(t,e){this.ak=t,this.sk=e}var o=t(42),i=t(47),s=t(46),a=t(19),u=t(22);r.prototype.generateAuthorization=function(t,e,n,r,s,a,u){var c=s?new Date(1e3*s):new Date,p=i.format("bce-auth-v1/%s/%s/%d",this.ak,o.toUTCString(c),a||1800),l=this.hash(p,this.sk),f=this.uriCanonicalization(e),h=this.queryStringCanonicalization(n||{}),d=this.headersCanonicalization(r||{},u),_=d[0],v=d[1],y=i.format("%s\n%s\n%s\n%s",t,f,h,_),g=this.hash(y,l);return v.length?i.format("%s/%s/%s",p,v.join(";"),g):i.format("%s//%s",p,g)},r.prototype.uriCanonicalization=function(t){return t},r.prototype.queryStringCanonicalization=function(t){var e=[];return s.each(s.keys(t),function(n){if(n.toLowerCase()!==a.AUTHORIZATION.toLowerCase()){var r=null==t[n]?"":t[n];e.push(n+"="+u.normalize(r))}}),e.sort(),e.join("&")},r.prototype.headersCanonicalization=function(t,e){e&&e.length||(e=[a.HOST,a.CONTENT_MD5,a.CONTENT_LENGTH,a.CONTENT_TYPE]);var n={};s.each(e,function(t){n[t.toLowerCase()]=!0});var r=[];s.each(s.keys(t),function(e){var o=t[e];o=s.isString(o)?u.trim(o):o,null!=o&&""!==o&&(e=e.toLowerCase(),(/^x\-bce\-/.test(e)||n[e]===!0)&&r.push(i.format("%s:%s",u.normalize(e),u.normalize(o))))}),r.sort();var o=[];return s.each(r,function(t){o.push(t.split(":")[0])}),[r.join("\n"),o]},r.prototype.hash=function(e,n){var r=t(40),o=r.createHmac("sha256",n);return o.update(e),o.digest("hex")},e.exports=r},{19:19,22:22,40:40,42:42,46:46,47:47}],16:[function(t,e,n){function r(t,e,n){o.call(this),this.config=a.extend({},u.DEFAULT_CONFIG,t),this.serviceId=e,this.regionSupported=!!n,this.config.endpoint=this._computeEndpoint(),this._httpAgent=null}var o=t(41).EventEmitter,i=t(47),s=t(44),a=t(46),u=t(18),c=t(15);i.inherits(r,o),r.prototype._computeEndpoint=function(){return this.config.endpoint?this.config.endpoint:this.regionSupported?i.format("%s://%s.%s.%s",this.config.protocol,this.serviceId,this.config.region,u.DEFAULT_SERVICE_DOMAIN):i.format("%s://%s.%s",this.config.protocol,this.serviceId,u.DEFAULT_SERVICE_DOMAIN)},r.prototype.createSignature=function(t,e,n,r,o){return s.fcall(function(){var i=new c(t.ak,t.sk);return i.generateAuthorization(e,n,r,o)})},e.exports=r},{15:15,18:18,41:41,44:44,46:46,47:47}],17:[function(t,e,n){function r(t){l.call(this,t,"bos",!0),this._httpAgent=null}var o=t(43),i=t(47),s=t(46),a=t(33),u=t(19),c=t(22),p=t(20),l=t(16),f=t(21),h=5368709120,d=2048;i.inherits(r,l),r.prototype.deleteObject=function(t,e,n){return n=n||{},this.sendRequest("DELETE",{bucketName:t,key:e,config:n.config})},r.prototype.putObject=function(t,e,n,r){if(!e)throw new TypeError("key should not be empty.");return r=this._checkOptions(r||{}),this.sendRequest("PUT",{bucketName:t,key:e,body:n,headers:r.headers,config:r.config})},r.prototype.putObjectFromBlob=function(t,e,n,r){var o={};return o[u.CONTENT_LENGTH]=n.size,r=s.extend(o,r),this.putObject(t,e,n,r)},r.prototype.getObjectMetadata=function(t,e,n){return n=n||{},this.sendRequest("HEAD",{bucketName:t,key:e,config:n.config})},r.prototype.initiateMultipartUpload=function(t,e,n){n=n||{};var r={};return r[u.CONTENT_TYPE]=n[u.CONTENT_TYPE]||f.guess(o.extname(e)),this.sendRequest("POST",{bucketName:t,key:e,params:{uploads:""},headers:r,config:n.config})},r.prototype.abortMultipartUpload=function(t,e,n,r){return r=r||{},this.sendRequest("DELETE",{bucketName:t,key:e,params:{uploadId:n},config:r.config})},r.prototype.completeMultipartUpload=function(t,e,n,r,o){var i={};return i[u.CONTENT_TYPE]="application/json; charset=UTF-8",o=this._checkOptions(s.extend(i,o)),this.sendRequest("POST",{bucketName:t,key:e,body:JSON.stringify({parts:r}),headers:o.headers,params:{uploadId:n},config:o.config})},r.prototype.uploadPartFromBlob=function(t,e,n,r,o,a,c){if(a.size!==o)throw new TypeError(i.format("Invalid partSize %d and data length %d",o,a.size));var p={};return p[u.CONTENT_LENGTH]=o,p[u.CONTENT_TYPE]="application/octet-stream",c=this._checkOptions(s.extend(p,c)),this.sendRequest("PUT",{bucketName:t,key:e,body:a,headers:c.headers,params:{partNumber:r,uploadId:n},config:c.config})},r.prototype.listParts=function(t,e,n,r){if(!n)throw new TypeError("uploadId should not empty");var o=["maxParts","partNumberMarker","uploadId"];return r=this._checkOptions(r||{},o),r.params.uploadId=n,this.sendRequest("GET",{bucketName:t,key:e,params:r.params,config:r.config})},r.prototype.listMultipartUploads=function(t,e){var n=["delimiter","maxUploads","keyMarker","prefix","uploads"];return e=this._checkOptions(e||{},n),e.params.uploads="",this.sendRequest("GET",{bucketName:t,params:e.params,config:e.config})},r.prototype.appendObject=function(t,e,n,r,o){if(!e)throw new TypeError("key should not be empty.");o=this._checkOptions(o||{});var i={append:""};return s.isNumber(r)&&(i.offset=r),this.sendRequest("POST",{bucketName:t,key:e,body:n,headers:o.headers,params:i,config:o.config})},r.prototype.appendObjectFromBlob=function(t,e,n,r,o){var i={};return i[u.CONTENT_LENGTH]=n.size,o=s.extend(i,o),this.appendObject(t,e,n,r,o)},r.prototype.sendRequest=function(t,e){var n={bucketName:null,key:null,body:null,headers:{},params:{},config:{},outputStream:null},r=s.extend(n,e),o=s.extend({},this.config,r.config),i=["/v1",c.normalize(r.bucketName||""),c.normalize(r.key||"",!1)].join("/");return o.sessionToken&&(r.headers[u.SESSION_TOKEN]=o.sessionToken),this.sendHTTPRequest(t,i,r,o)},r.prototype.sendHTTPRequest=function(t,e,n,r){var o=this,i=this._httpAgent=new p(r),a={httpMethod:t,resource:e,args:n,config:r};s.each(["progress","error","abort"],function(t){i.on(t,function(e){o.emit(t,e,a)})});var u=this._httpAgent.sendRequest(t,e,n.body,n.headers,n.params,s.bind(this.createSignature,this),n.outputStream);return u.abort=function(){if(i._req&&i._req.xhr){var t=i._req.xhr;t.abort()}},u},r.prototype._checkOptions=function(t,e){var n={};return n.config=t.config||{},n.headers=this._prepareObjectHeaders(t),n.params=s.pick(t,e||[]),n},r.prototype._prepareObjectHeaders=function(t){var e={};s.each([u.CONTENT_LENGTH,u.CONTENT_ENCODING,u.CONTENT_MD5,u.X_BCE_CONTENT_SHA256,u.CONTENT_TYPE,u.CONTENT_DISPOSITION,u.ETAG,u.SESSION_TOKEN,u.CACHE_CONTROL,u.EXPIRES,u.X_BCE_OBJECT_ACL,u.X_BCE_OBJECT_GRANT_READ],function(t){e[t]=!0});var n=0,r=s.pick(t,function(t,r){return e[r]?!0:/^x\-bce\-meta\-/.test(r)?(n+=a.byteLength(r)+a.byteLength(""+t),!0):void 0});if(n>d)throw new TypeError("Metadata size should not be greater than "+d+".");if(r.hasOwnProperty(u.CONTENT_LENGTH)){var o=r[u.CONTENT_LENGTH];if(0>o)throw new TypeError("content_length should not be negative.");if(o>h)throw new TypeError("Object length should be less than "+h+". Use multi-part upload instead.")}if(r.hasOwnProperty("ETag")){var c=r.ETag;/^"/.test(c)||(r.ETag=i.format('"%s"',c))}return r.hasOwnProperty(u.CONTENT_TYPE)||(r[u.CONTENT_TYPE]="application/octet-stream"),r},e.exports=r},{16:16,19:19,20:20,21:21,22:22,33:33,43:43,46:46,47:47}],18:[function(t,e,n){n.DEFAULT_SERVICE_DOMAIN="baidubce.com",n.DEFAULT_CONFIG={protocol:"http",region:"bj"}},{}],19:[function(t,e,n){n.CONTENT_TYPE="Content-Type",n.CONTENT_LENGTH="Content-Length",n.CONTENT_MD5="Content-MD5",n.CONTENT_ENCODING="Content-Encoding",n.CONTENT_DISPOSITION="Content-Disposition",n.ETAG="ETag",n.CONNECTION="Connection",n.HOST="Host",n.USER_AGENT="User-Agent",n.CACHE_CONTROL="Cache-Control",n.EXPIRES="Expires",n.AUTHORIZATION="Authorization",n.X_BCE_DATE="x-bce-date",n.X_BCE_ACL="x-bce-acl",n.X_BCE_REQUEST_ID="x-bce-request-id",n.X_BCE_CONTENT_SHA256="x-bce-content-sha256",n.X_BCE_OBJECT_ACL="x-bce-object-acl",n.X_BCE_OBJECT_GRANT_READ="x-bce-object-grant-read",n.X_HTTP_HEADERS="http_headers",n.X_BODY="body",n.X_STATUS_CODE="status_code",n.X_MESSAGE="message",n.X_CODE="code",n.X_REQUEST_ID="request_id",n.SESSION_TOKEN="x-bce-security-token",n.X_VOD_MEDIA_TITLE="x-vod-media-title",n.X_VOD_MEDIA_DESCRIPTION="x-vod-media-description",n.ACCEPT_ENCODING="accept-encoding",n.ACCEPT="accept"},{}],20:[function(t,e,n){function r(t){o.call(this),this.config=t,this._req=null}var o=t(41).EventEmitter,i=t(33),s=t(44),a=t(42),u=t(46),c=t(47),p=t(19);c.inherits(r,o),r.prototype.sendRequest=function(t,e,n,r,o,i,c){var l=this._getRequestUrl(e,o),f={};f[p.X_BCE_DATE]=a.toUTCString(new Date),f[p.CONTENT_TYPE]="application/json; charset=UTF-8",f[p.HOST]=/^\w+:\/\/([^\/]+)/.exec(this.config.endpoint)[1];var h=u.extend(f,r);if(!h.hasOwnProperty(p.CONTENT_LENGTH)){var d=this._guessContentLength(n);0===d&&/GET|HEAD/i.test(t)||(h[p.CONTENT_LENGTH]=d)}var _=this,v=i||u.noop;try{return s.resolve(v(this.config.credentials,t,e,o,h)).then(function(e,r){return e&&(h[p.AUTHORIZATION]=e),r&&(h[p.X_BCE_DATE]=r),_._doRequest(t,l,u.omit(h,p.CONTENT_LENGTH,p.HOST),n,c)})}catch(y){return s.reject(y)}},r.prototype._doRequest=function(t,e,n,r,o){var i=s.defer(),a=this,c=new XMLHttpRequest;c.open(t,e,!0);for(var p in n)if(n.hasOwnProperty(p)){var l=n[p];c.setRequestHeader(p,l)}return c.onerror=function(t){i.reject(t)},c.onabort=function(){i.reject(new Error("xhr aborted"))},c.onreadystatechange=function(){if(4===c.readyState){var t=c.status;1223===t&&(t=204);var n=c.getResponseHeader("Content-Type"),r=/application\/json/.test(n),o=r?JSON.parse(c.responseText):c.responseText;o||(o={location:e});var s=t>=200&&300>t||304===t;if(s){var u=a._fixHeaders(c.getAllResponseHeaders());i.resolve({http_headers:u,body:o})}else i.reject({status_code:t,message:o.message||"<message>",code:o.code||"<code>",request_id:o.requestId||"<request_id>"})}},c.upload&&u.each(["progress","error","abort"],function(t){c.upload.addEventListener(t,function(e){"function"==typeof a.emit&&a.emit(t,e)},!1)}),c.send(r),a._req={xhr:c},i.promise},r.prototype._guessContentLength=function(t){if(null==t||""===t)return 0;if(u.isString(t))return i.byteLength(t);if("undefined"!=typeof Blob&&t instanceof Blob)return t.size;if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer)return t.byteLength;throw new Error("No Content-Length is specified.")},r.prototype._fixHeaders=function(t){var e={};return t&&u.each(t.split(/\r?\n/),function(t){var n=t.indexOf(":");if(-1!==n){var r=t.substring(0,n).toLowerCase(),o=t.substring(n+1).replace(/^\s+|\s+$/,"");"etag"===r&&(o=o.replace(/"/g,"")),e[r]=o}}),e},r.prototype.buildQueryString=function(e){var n=t(45).stringify(e);return n.replace(/[()'!~.*\-_]/g,function(t){return"%"+t.charCodeAt().toString(16)})},r.prototype._getRequestUrl=function(t,e){var n=t,r=this.buildQueryString(e);return r&&(n+="?"+r),this.config.endpoint+n},e.exports=r},{19:19,33:33,41:41,42:42,44:44,45:45,46:46,47:47}],21:[function(t,e,n){var r={};n.guess=function(t){return t&&t.length?("."===t[0]&&(t=t.substr(1)),r[t.toLowerCase()]||"application/octet-stream"):"application/octet-stream"}},{}],22:[function(t,e,n){var r={"!":"%21","'":"%27","(":"%28",")":"%29","*":"%2A"};n.normalize=function(t,e){var n=encodeURIComponent(t);return n=n.replace(/[!'\(\)\*]/g,function(t){return r[t]}),e===!1&&(n=n.replace(/%2F/gi,"/")),n},n.trim=function(t){return(t||"").replace(/^\s+|\s+$/g,"")}},{}],23:[function(t,e,n){var r={runtimes:"html5",bos_endpoint:"http://bj.bcebos.com",bos_ak:null,bos_sk:null,bos_credentials:null,bos_appendable:!1,bos_relay_server:"https://relay.efe.tech",multi_selection:!1,max_retries:0,retry_interval:1e3,auto_start:!1,max_file_size:"100mb",bos_multipart_min_size:"10mb",bos_multipart_parallel:1,bos_task_parallel:3,auth_stripped_headers:["User-Agent","Connection"],chunk_size:"4mb",bos_multipart_auto_continue:!1,bos_multipart_local_key_generator:"default",dir_selection:!1,get_new_uptoken:!0,uptoken_via_jsonp:!0,uptoken_timeout:5e3,uptoken_jsonp_timeout:5e3,tracker_id:null};e.exports=r},{}],24:[function(t,e,n){e.exports={kPostInit:"PostInit",kKey:"Key",kListParts:"ListParts",kObjectMetas:"ObjectMetas",kFileFiltered:"FileFiltered",kFilesAdded:"FilesAdded",kFilesFilter:"FilesFilter",kNetworkSpeed:"NetworkSpeed",kBeforeUpload:"BeforeUpload",kUploadProgress:"UploadProgress",kFileUploaded:"FileUploaded",kUploadPartProgress:"UploadPartProgress",kChunkUploaded:"ChunkUploaded",kUploadResume:"UploadResume",kUploadResumeError:"UploadResumeError",kUploadComplete:"UploadComplete",kError:"Error",kAborted:"Aborted"}},{}],25:[function(t,e,n){function r(){c.apply(this,arguments),this.xhrPools=[]}var o=t(44),i=t(35),s=t(46),a=t(32),u=t(24),c=t(30);a.inherits(r,c),r.prototype.start=function(){if(this.aborted)return o.resolve();var t=this,e=this.eventDispatcher,n=this.options.file,r=this.options.bucket,c=this.options.object,p=this.options.metas,l=this.options.chunk_size,f=this.options.bos_multipart_parallel,h=a.guessContentType(n),d={"Content-Type":h},_=null;return this._initiateMultipartUpload(n,l,r,c,d).then(function(s){_=s.body.uploadId;var p=s.body.parts||[],h=o.defer(),d=a.getTasks(n,_,l,r,c);a.filterTasks(d,p);var v=p.length,y={lengthComputable:!0,loaded:v,total:d.length};if(n._previousLoaded=v*l,v){var g=n._previousLoaded/n.size;e.dispatchEvent(u.kUploadProgress,[n,g,null])}return i.mapLimit(d,f,t._uploadPart(y),function(t,e){t?h.reject(t):h.resolve(e)}),h.promise}).then(function(e){var o=[];return s.each(e,function(t,e){o.push({partNumber:e+1,eTag:t.http_headers.etag})}),t._generateLocalKey({blob:n,chunkSize:l,bucket:r,object:c}).then(function(t){a.removeUploadId(t)}),t.client.completeMultipartUpload(r,c,_,o,p)}).then(function(t){e.dispatchEvent(u.kUploadProgress,[n,1]),t.body.bucket=r,t.body.object=c,e.dispatchEvent(u.kFileUploaded,[n,t])})["catch"](function(r){var o=t.aborted?u.kAborted:u.kError;e.dispatchEvent(o,[r,n])})},r.prototype._initiateMultipartUpload=function(t,e,n,r,i){function s(){return l.client.initiateMultipartUpload(n,r,i).then(function(t){return p&&a.setUploadId(p,t.body.uploadId),t.body.parts=[],t})}var c,p,l=this,f=this.eventDispatcher,h={blob:t,chunkSize:e,bucket:n,object:r},d=this.options.bos_multipart_auto_continue?this._generateLocalKey(h):o.resolve(null);return d.then(function(e){return(p=e)?(c=a.getUploadId(p),c?l._listParts(t,n,r,c):s()):s()}).then(function(e){if(c&&p){var n=e.body.parts;f.dispatchEvent(u.kUploadResume,[t,n,null]),e.body.uploadId=c}return e})["catch"](function(e){if(c&&p)return f.dispatchEvent(u.kUploadResumeError,[t,e,null]),a.removeUploadId(p),s();throw e})},r.prototype._generateLocalKey=function(t){var e=this.options.bos_multipart_local_key_generator;return a.generateLocalKey(t,e)},r.prototype._listParts=function(t,e,n,r){var i=this,a=this.eventDispatcher,c=a.dispatchEvent(u.kListParts,[t,r]);return o.resolve(c).then(function(t){return s.isArray(t)&&t.length?{http_headers:{},body:{parts:t}}:i._listAllParts(e,n,r)})},r.prototype._listAllParts=function(t,e,n){function r(){var o={maxParts:c,partNumberMarker:p};i.client.listParts(t,e,n,o).then(function(t){null==u&&(u=t),a.push.apply(a,t.body.parts),p=t.body.nextPartNumberMarker,t.body.isTruncated===!1?(u.body.parts=a,s.resolve(u)):r()})["catch"](function(t){s.reject(t)})}var i=this,s=o.defer(),a=[],u=null,c=1e3,p=0;return r(),s.promise},r.prototype._uploadPart=function(t){function e(i,s){if(i.etag)return n.networkInfo.loadedBytes+=i.partSize,o.resolve({http_headers:{etag:i.etag},body:{}});var c=null==s?n.options.max_retries:s,p=n.options.retry_interval,l=i.file.slice(i.start,i.stop+1);l._parentUUID=i.file.uuid,l._previousLoaded=0;var f=n.client.uploadPartFromBlob(i.bucket,i.object,i.uploadId,i.partNumber,i.partSize,l),h=n.xhrPools.push(f);return f.then(function(e){++t.loaded;var o={uploadId:i.uploadId,partNumber:i.partNumber,partSize:i.partSize,bucket:i.bucket,object:i.object,offset:i.start,total:l.size,response:e};return r.dispatchEvent(u.kChunkUploaded,[i.file,o]),n.xhrPools[h-1]=null,e})["catch"](function(t){if(c>0&&!n.aborted)return a.delay(p).then(function(){return e(i,c-1)});throw t})}var n=this,r=this.eventDispatcher;return function(t,n){var r=function(t){n(null,t)},o=function(t){n(t)};e(t).then(r,o)}},r.prototype.abort=function(){this.aborted=!0,this.xhrRequesting=null;for(var t=0;t<this.xhrPools.length;t++){var e=this.xhrPools[t];e&&"function"==typeof e.abort&&e.abort()}},e.exports=r},{24:24,30:30,32:32,35:35,44:44,46:46}],26:[function(t,e,n){function r(){this.loadedBytes=0,this.totalBytes=0,this._startTime=o.now(),this.reset()}var o=t(32);r.prototype.dump=function(){return[this.loadedBytes,o.now()-this._startTime,this.totalBytes-this.loadedBytes]},r.prototype.reset=function(){this.loadedBytes=0,this._startTime=o.now()},e.exports=r},{32:32}],27:[function(t,e,n){function r(){u.apply(this,arguments)}var o=t(44),i=t(46),s=t(32),a=t(24),u=t(30);s.inherits(r,u),r.prototype.start=function(t){if(this.aborted)return o.resolve();var e=this,n=this.eventDispatcher,r=this.options.file,u=this.options.bucket,c=this.options.object,p=this.options.metas,l=null==t?this.options.max_retries:t,f=this.options.retry_interval,h=s.guessContentType(r),d=i.extend({"Content-Type":h},p);return this.xhrRequesting=this.client.putObjectFromBlob(u,c,r,d),this.xhrRequesting.then(function(t){n.dispatchEvent(a.kUploadProgress,[r,1]),t.body.bucket=u,t.body.key=c,n.dispatchEvent(a.kFileUploaded,[r,t])})["catch"](function(t){var i=e.aborted?a.kAborted:a.kError;return n.dispatchEvent(i,[t,r]),t.status_code&&t.code&&t.request_id?o.resolve():l>0&&!e.aborted?s.delay(f).then(function(){return e.start(l-1)}):o.resolve()})},e.exports=r},{24:24,30:30,32:32,44:44,46:46}],28:[function(t,e,n){function r(t){this.collection=t}r.prototype.isEmpty=function(){return this.collection.length<=0},r.prototype.size=function(){return this.collection.length},r.prototype.dequeue=function(){return this.collection.shift()},e.exports=r},{}],29:[function(t,e,n){function r(t){this.options=t,this._cache={}}var o=t(44),i=t(32);r.prototype.get=function(t){var e=this;return null!=e._cache[t]?e._cache[t]:o.resolve(this._getImpl.apply(this,arguments)).then(function(n){return e._cache[t]=n,n})},r.prototype._getImpl=function(t){var e=this.options,n=e.uptoken_url,r=e.uptoken_timeout||e.uptoken_jsonp_timeout,s=e.uptoken_via_jsonp,a=o.defer();return $.ajax({url:n,jsonp:s?"callback":!1,dataType:s?"jsonp":"json",timeout:r,data:{sts:JSON.stringify(i.getDefaultACL(t))},success:function(t){a.resolve(t)},error:function(){a.reject(new Error("Get sts token timeout ("+r+"ms)."))}}),a.promise},e.exports=r},{32:32,44:44}],30:[function(t,e,n){function r(t,e,n){this.xhrRequesting=null,this.aborted=!1,this.networkInfo=null,this.client=t,this.eventDispatcher=e,this.options=n}function o(){throw new Error("unimplemented method.")}r.prototype.start=o,r.prototype.pause=o,r.prototype.resume=o,r.prototype.setNetworkInfo=function(t){this.networkInfo=t},r.prototype.abort=function(){this.xhrRequesting&&"function"==typeof this.xhrRequesting.abort&&(this.aborted=!0,this.xhrRequesting.abort(),this.xhrRequesting=null)},e.exports=r},{}],31:[function(t,e,n){function r(t){i.isString(t)&&(t=i.extend({browse_button:t,auto_start:!0},$(t).data()));var e={};this.options=i.extend({},u,e,t),this.options.max_file_size=s.parseSize(this.options.max_file_size),this.options.bos_multipart_min_size=s.parseSize(this.options.bos_multipart_min_size),this.options.chunk_size=s.parseSize(this.options.chunk_size);var n=this.options.bos_credentials;!n&&this.options.bos_ak&&this.options.bos_sk&&(this.options.bos_credentials={ak:this.options.bos_ak,sk:this.options.bos_sk}),this.client=new d({endpoint:s.normalizeEndpoint(this.options.bos_endpoint),credentials:this.options.bos_credentials,sessionToken:this.options.uptoken}),this._files=[],this._uploadingFiles={},this._abort=!1,this._working=!1,this._xhr2Supported=s.isXhr2Supported(),this._networkInfo=new f,this._init()}var o=t(44),i=t(46),s=t(32),a=t(24),u=t(23),c=t(27),p=t(25),l=t(29),f=t(26),h=t(15),d=t(17);r.prototype._getCustomizedSignature=function(t){var e=this.options,n=e.uptoken_timeout||e.uptoken_jsonp_timeout,r=e.uptoken_via_jsonp;return function(s,a,u,c,p){/\bed=([\w\.]+)\b/.test(location.search)&&(p.Host=RegExp.$1),i.isArray(e.auth_stripped_headers)&&(p=i.omit(p,e.auth_stripped_headers));var l=o.defer();return $.ajax({url:t,jsonp:r?"callback":!1,dataType:r?"jsonp":"json",timeout:n,data:{httpMethod:a,path:u,queries:JSON.stringify(c||{}),headers:JSON.stringify(p||{})},error:function(){l.reject(new Error("Get authorization timeout ("+n+"ms)."))},success:function(t){200===t.statusCode&&t.signature?l.resolve(t.signature,t.xbceDate):l.reject(new Error("createSignature failed, statusCode = "+t.statusCode))}}),l.promise}},r.prototype._invoke=function(t,e,n){var r=this.options.init||this.options.Init;if(r){var i=r[t];if("function"==typeof i)try{var s=null;return e=null==e?[s]:[s].concat(e),i.apply(null,e)}catch(a){if(n===!0)return o.reject(a)}}},r.prototype._init=function(){var t=this.options,e=t.accept,n=$(t.browse_button),r=n.prop("nodeName");if("INPUT"!==r){var u=n,c=u.outerWidth(),p=u.outerHeight(),l=$('<div class="bce-bos-uploader-input-container"><input type="file" /></div>');l.css({position:"absolute",top:0,left:0,width:c,height:p,overflow:"hidden","z-index":this._xhr2Supported?99:100}),l.find("input").css({position:"absolute",top:0,left:0,width:"100%",height:"100%","font-size":"999px",opacity:0}),u.css({position:"relative","z-index":this._xhr2Supported?100:99}),u.after(l),u.parent().css("position","relative"),t.browse_button=l.find("input"),this._xhr2Supported&&u.click(function(){t.browse_button.click()})}var f=this;if(!this._xhr2Supported&&"undefined"!=typeof mOxie&&i.isFunction(mOxie.FileInput)){var d=new mOxie.FileInput({runtime_order:"flash,html4",browse_button:$(t.browse_button).get(0),swf_url:t.flash_swf_url,accept:s.expandAcceptToArray(e),multiple:t.multi_selection,directory:t.dir_selection,file:"file"});d.onchange=i.bind(this._onFilesAdded,this),d.onready=function(){f._initEvents(),f._invoke(a.kPostInit)},d.init()}var _=t.bos_credentials?o.resolve():f.refreshStsToken();_.then(function(){t.bos_credentials?f.client.createSignature=function(t,e,n,r,i){var s=t||this.config.credentials;return o.fcall(function(){var t=new h(s.ak,s.sk);return t.generateAuthorization(e,n,r,i)})}:t.uptoken_url&&t.get_new_uptoken===!0&&(f.client.createSignature=f._getCustomizedSignature(t.uptoken_url)),f._xhr2Supported&&(f._initEvents(),f._invoke(a.kPostInit))})["catch"](function(t){f._invoke(a.kError,[t])})},r.prototype._initEvents=function(){var t=this.options;if(this._xhr2Supported){var e=$(t.browse_button);null==e.attr("multiple")&&e.attr("multiple",!!t.multi_selection),e.on("change",i.bind(this._onFilesAdded,this));var n=t.accept;if(null!=n){var r=s.expandAccept(n),o=/Safari/.test(navigator.userAgent)&&/Apple Computer/.test(navigator.vendor);o&&(r=s.extToMimeType(r)),e.attr("accept",r)}t.dir_selection&&(e.attr("directory",!0),e.attr("mozdirectory",!0),e.attr("webkitdirectory",!0))}this.client.on("progress",i.bind(this._onUploadProgress,this)),this.client.on("error",i.bind(this._onError,this)),this._xhr2Supported||(this.client.sendHTTPRequest=i.bind(s.fixXhr(this.options,!0),this.client))},r.prototype._filterFiles=function(t){var e=this,n=this.options.max_file_size,r=i.filter(t,function(t){return n>0&&t.size>n?(e._invoke(a.kFileFiltered,[t]),!1):!0});return this._invoke(a.kFilesFilter,[r])||r},r.prototype._onFilesAdded=function(t){var e=t.target.files;if(!e){var n=t.target.value.split(/[\/\\]/).pop();e=[{name:n,size:0}]}e=this._filterFiles(e),i.isArray(e)&&e.length&&this.addFiles(e),this.options.auto_start&&this.start()},r.prototype._onError=function(t){},r.prototype._onUploadProgress=function(t,e){var n=e.args,r=n.body;if(s.isBlob(r)){var o=t.lengthComputable?t.loaded/t.total:0,i=t.loaded-r._previousLoaded;this._networkInfo.loadedBytes+=i,this._invoke(a.kNetworkSpeed,this._networkInfo.dump()),r._previousLoaded=t.loaded;var u=a.kUploadProgress;if(n.params.partNumber&&n.params.uploadId){u=a.kUploadPartProgress,this._invoke(u,[r,o,t]);var c=r._parentUUID,p=this._uploadingFiles[c],l=0;p&&(p._previousLoaded+=i,l=Math.min(p._previousLoaded/p.size,1),this._invoke(a.kUploadProgress,[p,l,null]))}else this._invoke(u,[r,o,t])}},r.prototype.addFiles=function(t){function e(t,e){return function(){t._aborted=!0,e._invoke(a.kAborted,[null,t])}}for(var n=0,r=0;r<t.length;r++){var o=t[r];o.abort=e(o,this),o.uuid=s.uuid(),n+=o.size}this._networkInfo.totalBytes+=n,this._files.push.apply(this._files,t),this._invoke(a.kFilesAdded,[t])},r.prototype.addFile=function(t){this.addFiles([t])},r.prototype.remove=function(t){"string"==typeof t&&(t=this._uploadingFiles[t]||i.find(this._files,function(e){return e.uuid===t})),t&&"function"==typeof t.abort&&t.abort()},r.prototype.start=function(){var t=this;if(!this._working&&this._files.length){this._working=!0,this._abort=!1,this._networkInfo.reset();var e=this.options.bos_task_parallel;s.eachLimit(this._files,e,function(e,n){e._previousLoaded=0,t._uploadNext(e).then(function(){delete t._uploadingFiles[e.uuid],n(null,e)})["catch"](function(){delete t._uploadingFiles[e.uuid],n(null,e)})},function(e){t._working=!1,t._files.length=0,t._networkInfo.totalBytes=0,t._invoke(a.kUploadComplete)})}},r.prototype.stop=function(){this._abort=!0,this._working=!1},r.prototype.setOptions=function(t){var e=i.pick(t,"bos_credentials","bos_ak","bos_sk","uptoken","bos_bucket","bos_endpoint");this.options=i.extend(this.options,e);var n=this.client&&this.client.config;if(n){var r=null;t.bos_credentials?r=t.bos_credentials:t.bos_ak&&t.bos_sk&&(r={ak:t.bos_ak,sk:t.bos_sk}),r&&(this.options.bos_credentials=r,n.credentials=r),t.uptoken&&(n.sessionToken=t.uptoken),t.bos_endpoint&&(n.endpoint=s.normalizeEndpoint(t.bos_endpoint))}},r.prototype.refreshStsToken=function(){var t=this,e=t.options,n=e.bos_bucket&&e.uptoken_url&&e.get_new_uptoken===!1;if(n){var r=new l(e);return r.get(e.bos_bucket).then(function(e){return t.setOptions({bos_ak:e.AccessKeyId,bos_sk:e.SecretAccessKey,uptoken:e.SessionToken})})}return o.resolve()},r.prototype._uploadNext=function(t){if(this._abort)return this._working=!1,o.resolve();if(t._aborted===!0)return o.resolve();var e=!0,n=this._invoke(a.kBeforeUpload,[t],e);if(n===!1)return o.resolve();var r=this;return o.resolve(n).then(function(){return r._uploadNextImpl(t)})["catch"](function(e){r._invoke(a.kError,[e,t])})},r.prototype._uploadNextImpl=function(t){var e=this,n=this.options,r=t.name,s=!0,u=i.pick(n,"flash_swf_url","max_retries","chunk_size","retry_interval","bos_multipart_parallel","bos_multipart_auto_continue","bos_multipart_local_key_generator");return o.all([this._invoke(a.kKey,[t],s),this._invoke(a.kObjectMetas,[t])]).then(function(o){var s=n.bos_bucket,a=o[0],l=o[1],f="auto";i.isString(a)?r=a:i.isObject(a)&&(s=a.bucket||s,r=a.key||r,f=a.multipart||f);var h=e.client,d=e,_=i.extend(u,{file:t,bucket:s,object:r,metas:l}),v=c;"auto"===f&&e._xhr2Supported&&t.size>n.bos_multipart_min_size&&(v=p);var y=new v(h,d,_);return e._uploadingFiles[t.uuid]=t,t.abort=function(){return t._aborted=!0,y.abort()},y.setNetworkInfo(e._networkInfo),y.start()})},r.prototype.dispatchEvent=function(t,e,n){if(t===a.kAborted&&e&&e[1]){var r=e[1];if(r.size>0){var o=r._previousLoaded||0;this._networkInfo.totalBytes-=r.size-o,this._invoke(a.kNetworkSpeed,this._networkInfo.dump())}}return this._invoke(t,e,n)},e.exports=r},{15:15,17:17,23:23,24:24,25:25,26:26,27:27,29:29,32:32,44:44,46:46}],32:[function(t,e,n){function r(t,e){var n=a.filter(e||[],function(e){return+e.partNumber===t});return n.length?n[0].eTag:null}function o(t){var e=/^\w+:\/\/([^\/]+)/.exec(t);return e&&e[1]}var i=t(45),s=t(44),a=t(46),u=t(42),c=t(28),p=t(21);n.getTasks=function(t,e,n,r,o){for(var i=t.size,s=0,a=1,u=[];i>0;){var c=Math.min(i,n);u.push({file:t,uploadId:e,bucket:r,object:o,partNumber:a,partSize:c,start:s,stop:s+c-1}),i-=c,s+=c,a+=1}return u},n.getAppendableTasks=function(t,e,n){for(var r=t-e,o=[];r;){var i=Math.min(r,n);o.push({partSize:i,start:e,stop:e+i-1}),r-=i,e+=i}return o},n.parseSize=function(t){if("number"==typeof t)return t;var e=/^([\d\.]+)([mkg]?b?)$/i,n=e.exec(t);if(!n)return 0;var r=n[1],o=n[2];return/^k/i.test(o)?1024*r:/^m/i.test(o)?1024*r*1024:/^g/i.test(o)?1024*r*1024*1024:+r},n.isXhr2Supported=function(){return"XMLHttpRequest"in window&&"withCredentials"in new XMLHttpRequest},n.isAppendable=function(t){return"Appendable"===t["x-bce-object-type"]},n.delay=function(t){var e=s.defer();return setTimeout(function(){e.resolve()},t),e.promise},n.normalizeEndpoint=function(t){return t.replace(/(\/+)$/,"")},n.getDefaultACL=function(t){return{accessControlList:[{service:"bce:bos",region:"*",effect:"Allow",resource:[t+"/*"],permission:["READ","WRITE"]}]}},n.uuid=function(){var t=(Math.random()*Math.pow(2,32)).toString(36),e=(new Date).getTime();return"u-"+e+"-"+t},n.generateLocalKey=function(t,e){return"default"===e?s.resolve([t.blob.name,t.blob.size,t.chunkSize,t.bucket,t.object].join("&")):s.resolve(null)},n.getDefaultPolicy=function(t){if(null==t)return null;var e=(new Date).getTime(),n=new Date(e+864e5),r=u.toUTCString(n);return{expiration:r,conditions:[{bucket:t}]}},n.getUploadId=function(t){return localStorage.getItem(t)},n.setUploadId=function(t,e){localStorage.setItem(t,e)},n.removeUploadId=function(t){localStorage.removeItem(t)},n.filterTasks=function(t,e){a.each(t,function(t){
var n=t.partNumber,o=r(n,e);o&&(t.etag=o)})},n.expandAccept=function(t){var e=[];return a.isArray(t)?a.each(t,function(t){t.extensions&&e.push.apply(e,t.extensions.split(","))}):a.isString(t)&&(e=t.split(",")),e=a.map(e,function(t){return/^\./.test(t)?t:"."+t}),e.join(",")},n.extToMimeType=function(t){var e=a.map(t.split(","),function(t){return-1!==t.indexOf("/")?t:p.guess(t)});return e.join(",")},n.expandAcceptToArray=function(t){return!t||a.isArray(t)?t:a.isString(t)?[{title:"All files",extensions:t}]:[]},n.transformUrl=function(t){var e=/(https?:)\/\/([^\/]+)\/([^\/]+)\/([^\/]+)/;return t.replace(e,function(t,e,n,r,o){return/^v\d$/.test(r)?e+"//"+o+"."+n+"/"+r:e+"//"+r+"."+n+"/"+o})},n.isBlob=function(t){var e=null;if("undefined"!=typeof Blob)e=Blob;else{if("undefined"==typeof mOxie||!a.isFunction(mOxie.Blob))return!1;e=mOxie.Blob}return t instanceof e},n.now=function(){return(new Date).getTime()},n.toDHMS=function(t){var e=0,n=0,r=0;return t>=60&&(r=~~(t/60),t-=60*r),r>=60&&(n=~~(r/60),r-=60*n),n>=24&&(e=~~(n/24),n-=24*e),{DD:e,HH:n,MM:r,SS:t}},n.fixXhr=function(t,e){return function(r,a,c,p){var l=this,f=o(p.endpoint);c.headers["x-bce-date"]=u.toUTCString(new Date),c.headers.host=f,c.params[".stamp"]=(new Date).getTime();var h=r;"PUT"===r&&(r="POST");var d,_=r,v=c.body;if("HEAD"===r){var y=n.normalizeEndpoint(t.bos_relay_server);d=y+"/"+f+a,c.params.httpMethod=r,_="POST"}else e===!0?(d=n.transformUrl(p.endpoint+a),a=d.replace(/^\w+:\/\/[^\/]+\//,"/"),c.headers.host=o(d)):d=p.endpoint+a;"POST"!==_||v||(v='{"FORCE_POST": true}');var g=s.defer(),b=new mOxie.XMLHttpRequest;b.onload=function(){var t=null;try{t=JSON.parse(b.response||"{}"),t.location=d}catch(e){t={location:d}}b.status>=200&&b.status<300?"HEAD"===r?g.resolve(t):g.resolve({http_headers:{},body:t}):g.reject({status_code:b.status,message:t.message||"",code:t.code||"",request_id:t.requestId||""})},b.onerror=function(t){g.reject(t)},b.upload&&(b.upload.onprogress=function(t){if("PUT"===h){t.lengthComputable=!0;var e={httpMethod:h,resource:a,args:c,config:p,xhr:b};l.emit("progress",t,e)}});var m=l.createSignature(l.config.credentials,r,a,c.params,c.headers);return m.then(function(e,n){e&&(c.headers.authorization=e),n&&(c.headers["x-bce-date"]=n);var r=i.stringify(c.params);r&&(d+="?"+r),b.open(_,d,!0);for(var o in c.headers)if(c.headers.hasOwnProperty(o)&&!/(host|content\-length)/i.test(o)){var s=c.headers[o];b.setRequestHeader(o,s)}b.send(v,{runtime_order:"flash",swf_url:t.flash_swf_url})})["catch"](function(t){g.reject(t)}),g.promise}},n.eachLimit=function(t,e,n,r){function o(){var t=u.dequeue();t&&(i++,n(t,function(t){i--,t?(s=!0,a=!0,r(t)):u.isEmpty()||s?0>=i&&(a||(a=!0,r())):setTimeout(o,0)}))}var i=0,s=!1,a=!1,u=new c(t);e=Math.min(e,u.size());for(var p=0;e>p;p++)o()},n.inherits=function(e,n){return t(47).inherits(e,n)},n.guessContentType=function(t,e){var n=t.type;if(!n){var r=t.name,o=r.split(/\./g).pop();n=p.guess(o)}return e||/charset=/.test(n)||(n+="; charset=UTF-8"),n}},{21:21,28:28,42:42,44:44,45:45,46:46,47:47}],33:[function(t,e,n){function r(){}r.byteLength=function(t){var e=encodeURIComponent(t).match(/%[89ABab]/g);return t.length+(e?e.length:0)},e.exports=r},{}],34:[function(t,e,n){!function(t){function n(){}function r(t,e){return function(){t.apply(e,arguments)}}function o(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],p(t,this)}function i(t,e){for(;3===t._state;)t=t._value;return 0===t._state?void t._deferreds.push(e):(t._handled=!0,void o._immediateFn(function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null===n)return void(1===t._state?s:a)(e.promise,t._value);var r;try{r=n(t._value)}catch(o){return void a(e.promise,o)}s(e.promise,r)}))}function s(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof o)return t._state=3,t._value=e,void u(t);if("function"==typeof n)return void p(r(n,e),t)}t._state=1,t._value=e,u(t)}catch(i){a(t,i)}}function a(t,e){t._state=2,t._value=e,u(t)}function u(t){2===t._state&&0===t._deferreds.length&&o._immediateFn(function(){t._handled||o._unhandledRejectionFn(t._value)});for(var e=0,n=t._deferreds.length;n>e;e++)i(t,t._deferreds[e]);t._deferreds=null}function c(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function p(t,e){var n=!1;try{t(function(t){n||(n=!0,s(e,t))},function(t){n||(n=!0,a(e,t))})}catch(r){if(n)return;n=!0,a(e,r)}}var l=setTimeout;o.prototype["catch"]=function(t){return this.then(null,t)},o.prototype.then=function(t,e){var r=new this.constructor(n);return i(this,new c(t,e,r)),r},o.all=function(t){var e=Array.prototype.slice.call(t);return new o(function(t,n){function r(i,s){try{if(s&&("object"==typeof s||"function"==typeof s)){var a=s.then;if("function"==typeof a)return void a.call(s,function(t){r(i,t)},n)}e[i]=s,0===--o&&t(e)}catch(u){n(u)}}if(0===e.length)return t([]);for(var o=e.length,i=0;i<e.length;i++)r(i,e[i])})},o.resolve=function(t){return t&&"object"==typeof t&&t.constructor===o?t:new o(function(e){e(t)})},o.reject=function(t){return new o(function(e,n){n(t)})},o.race=function(t){return new o(function(e,n){for(var r=0,o=t.length;o>r;r++)t[r].then(e,n)})},o._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){l(t,0)},o._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},o._setImmediateFn=function(t){o._immediateFn=t},o._setUnhandledRejectionFn=function(t){o._unhandledRejectionFn=t},"undefined"!=typeof e&&e.exports?e.exports=o:t.Promise||(t.Promise=o)}(this)},{}],35:[function(t,e,n){n.mapLimit=t(2)},{2:2}],36:[function(t,e,n){var r=Object.create||function(){function t(){}return function(e){var n;return t.prototype=e,n=new t,t.prototype=null,n}}(),o={},i=o.algo={},s=o.lib={},a=s.Base=function(){return{extend:function(t){var e=r(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),u=s.WordArray=a.extend({init:function(t,e){t=this.words=t||[],void 0!=e?this.sigBytes=e:this.sigBytes=4*t.length},toString:function(t){return(t||p).stringify(this)},concat:function(t){var e=this.words,n=t.words,r=this.sigBytes,o=t.sigBytes;this.clamp();var i;if(r%4)for(i=0;o>i;i++){var s=n[i>>>2]>>>24-i%4*8&255;e[r+i>>>2]|=s<<24-(r+i)%4*8}else for(i=0;o>i;i+=4)e[r+i>>>2]=n[i>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,e=this.sigBytes;t[e>>>2]&=4294967295<<32-e%4*8,t.length=Math.ceil(e/4)},clone:function(){var t=a.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e,n=[],r=function(t){var e=987654321,n=4294967295;return function(){e=36969*(65535&e)+(e>>16)&n,t=18e3*(65535&t)+(t>>16)&n;var r=(e<<16)+t&n;return r/=4294967296,r+=.5,r*(Math.random()>.5?1:-1)}},o=0;t>o;o+=4){var i=r(4294967296*(e||Math.random()));e=987654071*i(),n.push(4294967296*i()|0)}return new u.init(n,t)}}),c=o.enc={},p=c.Hex={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],o=0;n>o;o++){var i=e[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(t){for(var e=t.length,n=[],r=0;e>r;r+=2)n[r>>>3]|=parseInt(t.substr(r,2),16)<<24-r%8*4;return new u.init(n,e/2)}},l=c.Latin1={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],o=0;n>o;o++){var i=e[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(t){for(var e=t.length,n=[],r=0;e>r;r++)n[r>>>2]|=(255&t.charCodeAt(r))<<24-r%4*8;return new u.init(n,e)}},f=c.Utf8={stringify:function(t){try{return decodeURIComponent(escape(l.stringify(t)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(t){return l.parse(unescape(encodeURIComponent(t)))}},h=s.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=f.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var e=this._data,n=e.words,r=e.sigBytes,o=this.blockSize,i=4*o,s=r/i;s=t?Math.ceil(s):Math.max((0|s)-this._minBufferSize,0);var a=s*o,c=Math.min(4*a,r);if(a){for(var p=0;a>p;p+=o)this._doProcessBlock(n,p);var l=n.splice(0,a);e.sigBytes-=c}return new u.init(l,c)},clone:function(){var t=a.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});s.Hasher=h.extend({cfg:a.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){h.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){t&&this._append(t);var e=this._doFinalize();return e},blockSize:16,_createHelper:function(t){return function(e,n){return new t.init(n).finalize(e)}},_createHmacHelper:function(t){return function(e,n){return new i.HMAC.init(t,n).finalize(e)}}}),e.exports=o},{}],37:[function(t,e,n){t(39),t(38);var r=t(36);e.exports=r.HmacSHA256},{36:36,38:38,39:39}],38:[function(t,e,n){var r=t(36),o=r,i=o.lib,s=i.Base,a=o.enc,u=a.Utf8,c=o.algo;c.HMAC=s.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=u.parse(e));var n=t.blockSize,r=4*n;e.sigBytes>r&&(e=t.finalize(e)),e.clamp();for(var o=this._oKey=e.clone(),i=this._iKey=e.clone(),s=o.words,a=i.words,c=0;n>c;c++)s[c]^=1549556828,a[c]^=909522486;o.sigBytes=i.sigBytes=r,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,n=e.finalize(t);e.reset();var r=e.finalize(this._oKey.clone().concat(n));return r}})},{36:36}],39:[function(t,e,n){var r=t(36),o=r,i=o.lib,s=i.WordArray,a=i.Hasher,u=o.algo,c=[],p=[];!function(){function t(t){for(var e=Math.sqrt(t),n=2;e>=n;n++)if(!(t%n))return!1;return!0}function e(t){return 4294967296*(t-(0|t))|0}for(var n=2,r=0;64>r;)t(n)&&(8>r&&(c[r]=e(Math.pow(n,.5))),p[r]=e(Math.pow(n,1/3)),r++),n++}();var l=[],f=u.SHA256=a.extend({_doReset:function(){this._hash=new s.init(c.slice(0))},_doProcessBlock:function(t,e){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],s=n[3],a=n[4],u=n[5],c=n[6],f=n[7],h=0;64>h;h++){if(16>h)l[h]=0|t[e+h];else{var d=l[h-15],_=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,v=l[h-2],y=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;l[h]=_+l[h-7]+y+l[h-16]}var g=a&u^~a&c,b=r&o^r&i^o&i,m=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),k=(a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25),T=f+k+g+p[h]+l[h],E=m+b;f=c,c=u,u=a,a=s+T|0,s=i,i=o,o=r,r=T+E|0}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+c|0,n[7]=n[7]+f|0},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,r=8*t.sigBytes;return e[r>>>5]|=128<<24-r%32,e[(r+64>>>9<<4)+14]=Math.floor(n/4294967296),e[(r+64>>>9<<4)+15]=n,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=a.clone.call(this);return t._hash=this._hash.clone(),t}});o.SHA256=a._createHelper(f),o.HmacSHA256=a._createHmacHelper(f),e.exports=r.SHA256},{36:36}],40:[function(t,e,n){var r=t(37),o=t(36).enc.Hex;n.createHmac=function(t,e){if("sha256"===t){var n=null,i={update:function(t){n=r(t,e).toString(o)},digest:function(){return n}};return i}}},{36:36,37:37}],41:[function(t,e,n){function r(){this.__events={}}r.prototype.emit=function(t,e){var n=this.__events[t];if(!n)return!1;for(var r=[].slice.call(arguments,1),o=0;o<n.length;o++){var i=n[o];try{i.apply(this,r)}catch(s){}}return!0},r.prototype.on=function(t,e){this.__events[t]?this.__events[t].push(e):this.__events[t]=[e]},n.EventEmitter=r},{}],42:[function(t,e,n){function r(t){return 10>t?"0"+t:t}n.toISOString=function(t){return t.toISOString?t.toISOString():t.getUTCFullYear()+"-"+r(t.getUTCMonth()+1)+"-"+r(t.getUTCDate())+"T"+r(t.getUTCHours())+":"+r(t.getUTCMinutes())+":"+r(t.getUTCSeconds())+"."+(t.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"},n.toUTCString=function(t){var e=n.toISOString(t);return e.replace(/\.\d+Z$/,"Z")}},{}],43:[function(t,e,n){function r(t){return s.exec(t).slice(1)}function o(t,e){for(var n=0,r=t.length-1;r>=0;r--){var o=t[r];"."===o?t.splice(r,1):".."===o?(t.splice(r,1),n++):n&&(t.splice(r,1),n--)}if(e)for(;n--;n)t.unshift("..");return t}var i=t(46),s=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,a="b"==="ab".substr(-1)?function(t,e,n){return t.substr(e,n)}:function(t,e,n){return 0>e&&(e=t.length+e),t.substr(e,n)};n.extname=function(t){return r(t)[3]},n.join=function(){var t=Array.prototype.slice.call(arguments,0);return n.normalize(i.filter(t,function(t,e){if("string"!=typeof t)throw new TypeError("Arguments to path.join must be strings");return t}).join("/"))},n.normalize=function(t){var e="/"===t.charAt(0),n="/"===a(t,-1);return t=o(i.filter(t.split("/"),function(t){return!!t}),!e).join("/"),t||e||(t="."),t&&n&&(t+="/"),(e?"/":"")+t}},{46:46}],44:[function(t,e,n){var r=t(34);n.resolve=function(){return r.resolve.apply(r,arguments)},n.reject=function(){return r.reject.apply(r,arguments)},n.all=function(){return r.all.apply(r,arguments)},n.fcall=function(t){try{return r.resolve(t())}catch(e){return r.reject(e)}},n.defer=function(){var t={};return t.promise=new r(function(e,n){t.resolve=function(){e.apply(null,arguments)},t.reject=function(){n.apply(null,arguments)}}),t}},{34:34}],45:[function(t,e,n){function r(t){return"string"==typeof t?t:"number"==typeof t&&isFinite(t)?""+t:"boolean"==typeof t?t?"true":"false":""}var o=t(46);n.stringify=function(t,e,n,i){e=e||"&",n=n||"=";var s=encodeURIComponent;if(i&&"function"==typeof i.encodeURIComponent&&(s=i.encodeURIComponent),null!==t&&"object"==typeof t){for(var a=o.keys(t),u=a.length,c=u-1,p="",l=0;u>l;++l){var f=a[l],h=t[f],d=s(r(f))+n;if(o.isArray(h)){for(var _=h.length,v=_-1,y=0;_>y;++y)p+=d+s(r(h[y])),v>y&&(p+=e);_&&c>l&&(p+=e)}else p+=d+s(r(h)),c>l&&(p+=e)}return p}return""}},{46:46}],46:[function(t,e,n){function r(t){return"string"==typeof t||k.call(t)===b}function o(t){return"function"==typeof t}function i(t,e){for(var n=1;n<arguments.length;n++){var r=arguments[n];if(r&&g(r))for(var o=d(r),i=0;i<o.length;i++){var s=o[i],a=r[s];t[s]=a}}return t}function s(t,e,n){for(var r=[],o=0;o<t.length;o++)r[o]=e.call(n,t[o],o,t);return r}function a(t,e,n){for(var r=0;r<t.length;r++)e.call(n,t[r],r,t)}function u(t,e,n){for(var r=0;r<t.length;r++){var o=t[r];if(e.call(n,o,r,t))return o}}function c(t,e,n){for(var r=[],o=0;o<t.length;o++){var i=t[o];e.call(n,i,o,t)&&r.push(i)}return r}function p(t,e){for(var n=0;n<t.length;n++)if(t[n]===e)return n;return-1}function l(t,e){for(var n=_(e)?e:[].slice.call(arguments,1),r={},o=d(t),i=0;i<o.length;i++){var s=o[i];-1===p(n,s)&&(r[s]=t[s])}return r}function f(t,e,n){var r,i,s,a={};if(o(e)){var u=e,c=d(t);for(r=0;r<c.length;r++)i=c[r],s=t[i],u.call(n,s,i,t)&&(a[i]=s)}else{var p=_(e)?e:[].slice.call(arguments,1);for(r=0;r<p.length;r++)i=p[r],t.hasOwnProperty(i)&&(a[i]=t[i])}return a}function h(t,e){return function(){return t.apply(e,[].slice.call(arguments))}}function d(t){var e,n,r=[];for(e in t)T.call(t,e)&&r.push(e);if(E)for(n=0;n<w.length;n++)T.call(t,w[n])&&r.push(w[n]);return r}var _=t(5),v=t(10),y=t(13),g=t(14),b="[object String]",m=Object.prototype,k=m.toString,T=Object.prototype.hasOwnProperty,E=!{toString:null}.propertyIsEnumerable("toString"),w=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"];n.bind=h,n.each=a,n.extend=i,n.filter=c,n.find=u,n.isArray=_,n.isFunction=o,n.isNumber=y,n.isObject=g,n.isString=r,n.map=s,n.omit=l,n.pick=f,n.keys=d,n.noop=v},{10:10,13:13,14:14,5:5}],47:[function(t,e,n){var r=t(46);n.inherits=function(t,e){var n=t.prototype,o=new Function;o.prototype=e.prototype,t.prototype=new o,t.prototype.constructor=t,r.extend(t.prototype,n)},n.format=function(t){var e=arguments.length;if(1===e)return t;for(var n="",r=1,o=0,i=0;i<t.length;){if(37===t.charCodeAt(i)&&i+1<t.length)switch(t.charCodeAt(i+1)){case 100:if(r>=e)break;i>o&&(n+=t.slice(o,i)),n+=Number(arguments[r++]),o=i+=2;continue;case 115:if(r>=e)break;i>o&&(n+=t.slice(o,i)),n+=String(arguments[r++]),o=i+=2;continue;case 37:i>o&&(n+=t.slice(o,i)),n+="%",o=i+=2;continue}++i}return 0===o?n=t:o<t.length&&(n+=t.slice(o)),n}},{46:46}]},{},[1])(1)});
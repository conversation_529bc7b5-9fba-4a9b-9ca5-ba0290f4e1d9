<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button v-permission="['Exam.ExerciseBanks']" size="small" round type="success" icon="el-icon-search"
          @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['Exam.ExerciseBanks.Create']" size="small" round type="primary" icon="el-icon-plus"
          @click="handleQuestionBankEdit(0, 0)">添加</el-button>
          <export-excel :header="['题库名称', '创建时间', '策略设置']" :filter-val="['name','creationTime','exercisePaperRoles']" :field="{1: [2], 2: ['未完成', '已完成']}" :apiFn="exerciseList" />
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
        <el-table-column label="题库名称" prop="name" sortable="name" min-width="200" />
        <!-- <el-table-column label="学习期限" prop="userCount" sortable="userCount" width="100" /> -->
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>

        <el-table-column label="策略设置" prop="exercisePaperRoles" sortable="exercisePaperRoles" width="120">
          <template slot-scope="{ row }">
            <el-tag v-if="row.exercisePaperRoles !== null" type="success" size="mini">已完成</el-tag>
            <el-tag v-if="row.exercisePaperRoles === null" type="info" size="mini">未完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320">
          <template slot-scope="{ row }">
            <el-button v-permission="['Exam.ExerciseBanks.Update']" size="mini" round type="warning"
              icon="el-icon-s-promotion" @click="handleDrawQuestions(row)">抽题策略</el-button>
            <el-button v-permission="['Exam.ExerciseBanks.Update']" size="mini" round type="primary" icon="el-icon-edit"
              @click="handleQuestionBankEdit(1, row)">编辑</el-button>
            <el-button v-permission="['Exam.ExerciseBanks.Delete']" size="mini" round type="danger"
              icon="el-icon-delete" @click="handleQuestionBankDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getExerciseList" />
    </el-card>
    <el-dialog :title="isEdit ? '编辑' : '添加'" :close-on-click-modal="false" :visible.sync="exerciseDialog" width="800px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="选择题库" prop="questionBankCategoryId">
          <el-select v-model="form.questionBankCategoryId" :disabled="isEdit" clearable filterable placeholder="选择题库"
            size="small" @change="handleQuestionBankSelectChange">
            <el-option v-for="item in questionBankList" :key="item.id" :label="item.name" :value="item.id"
              :title="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="封面" prop="thumbnailUrl">
          <lz-upload-images ref="previewFile" :limit="1" :file-size="500" :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList" @response-fn="handleImageResponse"
            @remove-upload="handleRemoveUploadImage" />
        </el-form-item>
        <el-form-item label="介绍" prop="introduce">
          <el-input v-model="form.introduce" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="选择学员" prop="exerciseBankUsers">
          <el-button round size="small" type="primary" icon="el-icon-plus" :loading="userLoading"
            @click="handleSelectStudent">选择用户</el-button>
          <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">选择班级</el-button>
          <span style="margin-left: 20px">已选择{{ selectUsers.length }}人</span>
        </el-form-item>
        <!-- <el-form-item label="学习期限" prop="learningPeriod">
          <el-input-number v-model="form.learningPeriod" :min="0" :max="365" /><span> 天</span>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="exerciseLoading" round type="primary" @click="handleExerciseSure">确 定</el-button>
        <el-button round @click="exerciseDialog = false">取 消</el-button>

      </span>
    </el-dialog>
    <el-dialog title="抽题策略" :visible.sync="drawQuestionDialog" :close-on-click-modal="false" width="1000px">
      <!-- <el-descriptions class="margin-top" label-class-name="descriptions_label" :column="2" border>
        <el-descriptions-item label="总题数">{{ drawQuertionInfo.questionNumber }}</el-descriptions-item>
        <el-descriptions-item label="总分数">{{ drawQuertionInfo.questionScore }}</el-descriptions-item>
      </el-descriptions> -->
      <div>
        <span>总题数：{{ drawQuertionInfo.questionNumber }}</span>
        <span style="margin-left:30px">总分数：{{ formatScore(drawQuertionInfo.questionScore) }}</span>
      </div>
      <el-table :key="tableKey" :data="drawQuertionForm.exercisePaperRoles">
        <el-table-column label="题型" prop="QuestionType" width="100">
          <template slot-scope="{row}">
            <span v-if="row.QuestionType === 2">判断题</span>
            <span v-if="row.QuestionType === 0">单选题</span>
            <span v-if="row.QuestionType === 1">多选题</span>
            <span v-if="row.QuestionType === 3">填空题</span>
            <span v-if="row.QuestionType === 6">问答题</span>
          </template>

        </el-table-column>
        <el-table-column label="题目难度" prop="Difficulty" width="120">
          <template slot-scope="{row}">
            <el-select v-model="row.Difficulty" size="small" clearable placeholder="请选择题目难度..."
              @change="questionDifficultyChange($event, row)">
              <el-option label="全部" :value="null">全部</el-option>
              <el-option label="易" :value="1">易</el-option>
              <el-option label="偏易" :value="2">偏易</el-option>
              <el-option label="适中" :value="3">适中</el-option>
              <el-option label="偏难" :value="4">偏难</el-option>
              <el-option label="难" :value="5">难</el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="知识点" prop="Knowledges">
          <template slot-scope="{row}">
            <el-select v-model="row.Knowledges" clearable filterable class="bigwidth_select" multiple placeholder="请选择知识点..."
              size="small" @change="questionKnowledgeChange($event, row)">
              <el-option v-for="item in knowledgeList" :key="item.id" :label="item.name" :value="item.id"
                :title="item.name" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="抽题数/可抽题数" prop="QustionCount" width="180">
          <template slot-scope="{row}">
            <el-input-number v-model="row.QustionCount" :min="0" :max="row.QuestionTotalNum" size="small"
              :controls="false" class="num_input" @input="scoreInput" /><span> / </span>
            <el-input-number v-model="row.QuestionTotalNum" :disabled="true" :controls="false" size="small"
              class="num_input" />
          </template>
        </el-table-column>
        <el-table-column label="每题分数" prop="QustionScore" width="140">
          <template slot-scope="{row}">
            <el-input-number v-model="row.QustionScore" size="small" :controls="false" :min="0" :step="0.1"
              :precision="1" class="num_input" @input="scoreInput" /><span> 分</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="drawQuestionDialog = false">取 消</el-button>
        <el-button :loading="dialogLoading" round type="primary" @click="handleDrawQuestionSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择用户" :visible.sync="chooseUserDialog" :close-on-click-modal="false" width="1000px">
      <choose-user v-if="chooseUserDialog" :all-org="allOrg" :all-student="allStudent"
        :current-select-student="selectUsers" @user-change="chooseStudent" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseUserDialog = false">取 消</el-button>
        <el-button :loading="dialogLoading" round type="primary" @click="handleChooseUserSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getCategorys } from '@/api/questionBankCategory'
import {
  questionBankCategory,
  questionBankCount
} from '@/api/questionBank'
import {
  loadNodes,
  getFilterUsers,
  getAllStudents,
  classesUsers
} from '@/api/user'
import { exerciseList, addExercise, editExercise, deleteExercise, drawQuertion, exerciseAllUsers, exerciseBankList } from '@/api/exercise'
import ChooseUser from '@/components/ChooseUser/choose'
import LzUploadImages from '@/components/LzUploadImages'
import ChooseClass from '@/components/ChooseClass'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import ExportExcel from '@/components/ExportExcel/index.vue'
export default {
  name: 'Exercise',
  directives: {
    permission
  },
  components: {
    Pagination,
    ChooseUser,
    ChooseClass,
    LzUploadImages,
    ExportExcel
},
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      isEdit: false,
      exerciseDialog: false,
      exerciseLoading: false,
      form: {
        questionBankCategoryId: '',
        name: '',
        exerciseBankUsers: [],
        introduce: '',
        thumbnailUrl: ''
      },
      questionBankList: [],
      rules: {},

      // 班级数据
      allOrg: [],
      // 全部学生
      allStudent: [],
      userLoading: false,
      // 选择用户Dialog
      chooseUserDialog: false,
      selectUsers: [],
      dialogLoading: false,

      drawQuestionDialog: false,
      knowledgeList: [],
      tableKey: null,
      drawQuertionInfo: {
        questionNumber: 0,
        questionScore: 0
      },
      drawQuertionForm: {
        exerciseBankId: '',
        questionBankCategoryId: '',
        exercisePaperRoles: [
          {
            QuestionType: 2,
            Difficulty: null,
            Knowledges: [],
            QustionCount: 0,
            QuestionTotalNum: 0,
            QustionScore: 0
          },
          {
            QuestionType: 0,
            Difficulty: null,
            Knowledges: [],
            QustionCount: 0,
            QuestionTotalNum: 0,
            QustionScore: 0
          },
          {
            QuestionType: 1,
            Difficulty: null,
            Knowledges: [],
            QustionCount: 0,
            QuestionTotalNum: 0,
            QustionScore: 0
          }
        ]
      },
      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],


      // 上传图片
      previewFileList: []

    }
  },
  created() {
    this.loadClassList()
    this.getExerciseList()
  },
  methods: {
    exerciseList(args) {
      return exerciseList(args)
    },
    formatScore(val) {
      if (val) {
        return val.toFixed(1)
      } else {
        return 0.0
      }
    },
    async handleDrawQuestions(row) {
      // Object.assign(this.$data.drawQuertionForm, this.$options.data().drawQuertionForm)
      this.resetDrawQuestionForm()
      this.drawQuertionForm.exerciseBankId = row.id
      this.drawQuertionForm.questionBankCategoryId = row.questionBankCategoryId
      await questionBankCategory(row.questionBankCategoryId).then(res => {
        // 知识点目录
        this.knowledgeList = res.items
      })
      if (row.exercisePaperRoles) {
        this.drawQuertionForm.exercisePaperRoles = JSON.parse(row.exercisePaperRoles)
        for await (var formItem of this.drawQuertionForm.exercisePaperRoles) {
          formItem.QuestionTotalNum = 0
          if (formItem.Knowledges && formItem.Knowledges.length > 0) {
            var form = {
              QuestionBankCategoryIds: formItem.Knowledges.toString(),
              Difficulty: formItem.Difficulty,
              QuestionType: formItem.QuestionType
            }
            const res = await questionBankCount(form)
            formItem.QuestionTotalNum = res.questionNumber
          }
          // this.getQuestionNumberList(formItem)
        }
        this.scoreInput()
      }

      this.drawQuestionDialog = true
    },
    async handleQuestionBankEdit(t, row) {
      this.$router.push({
          name: 'ExerciseDetial',
          query: {id: row.id, name: row.name}
        })
        return
      this.isEdit = !!t
      this.previewFileList = []
      this.resetForm()
      this.selectUsers = []
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      if (this.isEdit) {
        
        this.form.questionBankCategoryId = row.questionBankCategoryId
        this.form.name = row.name
        // this.form.introduce = ''
        this.$set(this.form, 'introduce', row.introduce)
        this.form.thumbnailUrl = row.thumbnailUrl
        this.form.id = row.id
        if (this.form.thumbnailUrl) {
          this.previewFileList.push({
            url: this.form.thumbnailUrl
          })
        }
        this.userLoading = true
        const res = await exerciseAllUsers(row.id)
        this.form.exerciseBankUsers = res.items
        res.items.forEach(item => {
          this.selectUsers.push({
            id: item.userId,
            userId: item.id,
            name: item.name,
            userName: item.userName,
            className: item.className,
            classId: item.classId
          })
        })
        // this.selectUsers = this.form.exerciseBankUsers
        this.userLoading = false
        // this.allStudent.forEach(allItem => {
        //   this.form.exerciseBankUsers.forEach(item => {
        //     if (item.userId === allItem.id) {
        //       this.selectUsers.push(allItem)
        //     }
        //   })
        // })
        this.getAllQuestionBankList()
      } else {
        this.getQuestionBankList()
      }

      this.exerciseDialog = true
    },
    handleExerciseSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.exerciseLoading = true
          if (this.isEdit) {
            editExercise(this.form.id, this.form).then(res => {
              this.$message.success('操作成功')
              this.exerciseLoading = false
              this.exerciseDialog = false
              this.getExerciseList()
            }).catch(() => {
              this.$message.error('编辑失败')
            })
          } else {
            addExercise(this.form).then(res => {
              this.$message.success('添加成功')
              this.exerciseLoading = false
              this.exerciseDialog = false
              this.getExerciseList()
            }).catch(() => {
              this.$message.error('添加失败')
            })
          }
        } else {
          return false
        }
      })
    },
    handleQuestionBankDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteExercise(row.id).then(res => {
          this.$message.success('删除成功')
          this.getExerciseList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 抽题难度
    questionDifficultyChange(val, row) {
      this.getQuestionNumberList(row)
    },
    // 知识点变化
    questionKnowledgeChange(val, row) {
      this.getQuestionNumberList(row)
    },
    scoreInput() {
      this.drawQuertionInfo = {
        questionNumber: 0,
        questionScore: 0
      }
      this.drawQuertionForm.exercisePaperRoles.forEach(item => {
        this.drawQuertionInfo.questionNumber += item.QustionCount
        this.drawQuertionInfo.questionScore += item.QustionCount * item.QustionScore
      })
    },
    handleDrawQuestionSure() {
      drawQuertion(this.drawQuertionForm).then(res => {
        this.$message.success('设置成功')
        this.drawQuestionDialog = false
        this.getExerciseList()
      })
    },
    getQuestionNumberList(row) {
      if (row.Knowledges && row.Knowledges.length > 0) {
        var form = {
          QuestionBankCategoryIds: row.Knowledges.toString(),
          Difficulty: row.Difficulty,
          QuestionType: row.QuestionType
        }
        questionBankCount(form).then(res => {
          row.QuestionTotalNum = res.questionNumber
          this.tableKey = Math.random()
        })
      } else {
        row.QuestionTotalNum = 0
      }
    },
    handleQuestionBankSelectChange(val) {
      var item = this.questionBankList.filter(item => item.id === val)
      if (item && item.length > 0) {
        this.form.name = item[0].name
      } else {
        this.form.name = ''
      }
    },
    // 选择学生
    handleSelectStudent() {
      this.chooseUserDialog = true
    },
    // 选择学生组件传值获取选择的学生
    chooseStudent(val) {
      this.selectUsers = val
    },
    // 选择用户确定
    handleChooseUserSure() {
      this.dialogSureLoading = true
      var tmp = []
      this.selectUsers.forEach(item => {
        tmp.push({
          id: item.id,
          // userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
          className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
          // userName: item.userName,
          // name: item.name
          userId: item.id,
          userName: item.userName,
          name: item.name
        })
      })
      this.form.exerciseBankUsers = tmp
      this.chooseUserDialog = false
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var newArr = []
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        this.selectUsers = this.selectUsers.concat(res.items)
      }
      // this.selectedClass.forEach(classItem => {
      //   var tmp = this.allStudent.filter(item => item.extraProperties.OUId === classItem.id)
      //   this.selectUsers.concat(tmp)
      // })

      var setA = new Set()
      this.selectUsers = this.selectUsers.filter(item => {
        const result = setA.has(item.id)
        setA.add(item.id)
        return !result
      })
      // var len = this.selectUsers.length
      // for (let i = 0; i < len; i++) {
      //   for (let j = i + 1; j < len; j++) {
      //     if (this.selectUsers[i].id === this.selectUsers[j].id) {
      //       this.selectUsers.splice(j, 1)
      //       len-- // 减少循环次数提高性能
      //       j-- // 保证j的值自加后不变
      //     }
      //   }
      // }
      this.selectUsers.forEach(item => {
        newArr.push({
          // userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
          className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
          // userName: item.userName,
          // name: item.name
          userId: item.id,
          userName: item.userName,
          name: item.name
        })
      })
      this.classLoading = false
      this.form.exerciseBankUsers = newArr
      this.chooseClassDialog = false
    },
    // 上传图片成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.form.thumbnailUrl = url
    },
    // 上传图片删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.form.thumbnailUrl = ''
    },
    async loadClassList() {
      loadNodes().then(res => {
        this.allOrg = res.items
      })
    },
    resetForm() {
      this.form = {
        questionBankCategoryId: '',
        name: '',
        exerciseBankUsers: []
      }
    },
    resetDrawQuestionForm() {
      this.drawQuertionForm = {
        exerciseBankId: '',
        questionBankCategoryId: '',
        exercisePaperRoles: [
          {
            QuestionType: 2,
            Difficulty: null,
            Knowledges: [],
            QustionCount: 0,
            QuestionTotalNum: 0,
            QustionScore: 0
          },
          {
            QuestionType: 0,
            Difficulty: null,
            Knowledges: [],
            QustionCount: 0,
            QuestionTotalNum: 0,
            QustionScore: 0
          },
          {
            QuestionType: 1,
            Difficulty: null,
            Knowledges: [],
            QustionCount: 0,
            QuestionTotalNum: 0,
            QustionScore: 0
          }
        ]
      }
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getExerciseList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getExerciseList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getExerciseList()
    },

    getExerciseList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      exerciseList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => { this.listLoading = false })
    },
    getQuestionBankList() {
      var form = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      exerciseBankList(form).then(res => {
        this.questionBankList = res.items
      })
    },
    getAllQuestionBankList() {
      getCategorys().then(res => {
        this.questionBankList = res.items.filter(item => item.parentId === null)
      })
    }

  }
}
</script>
<style scoped>
.input_color ::v-deep .el-input__inner {
  color: #337ab7;
}

.num_input {
  width: 70px;
}

.num_input ::v-deep .el-input {
  display: inline-block;
  width: 70px;
}

.num_input ::v-deep .el-input__inner {
  width: 70px;
}

.bigwidth_select {
  width: 400px;
}
</style>

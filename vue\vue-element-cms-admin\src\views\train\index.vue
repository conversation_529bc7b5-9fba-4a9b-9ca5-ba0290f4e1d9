<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="header_flex_box">
        <el-radio-group v-model="listQuery.TrainState" size="small" @change="handleRefreshList">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">未开始</el-radio-button>
          <el-radio-button :label="1">进行中</el-radio-button>
          <el-radio-button :label="2">已结束</el-radio-button>
        </el-radio-group>
        <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
        <el-button round size="small" type="success" clear icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['CourseManagement.Trains.Create']" size="small" round type="primary" icon="el-icon-plus" @click="handleTrainEdit(0, 0)">添加</el-button>
        <export-excel :header="['培训名称','培训开始时间','培训结束时间','培训状态','发布状态','培训人数','创建时间']" :filter-val="['name','startDate','endDate','trainState','isPublish','userCount','creationTime']" :field="{1:[1],2: [1], 3: ['未开始','进行中','已结束'],4: ['未发布','已发布'],6: [1]}" :api-fn="trainList" />
      </div>

      <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
        <el-table-column label="培训名称" prop="name" sortable="name" min-width="200" />
        <el-table-column label="培训开始时间" prop="startDate" sortable="startDate" width="180">
          <template slot-scope="{row}">
            {{ row.startDate | formatDatetime }}
          </template>
        </el-table-column>
        <el-table-column label="培训结束时间" prop="endDate" sortable="endDate" width="180">
          <template slot-scope="{row}">
            {{ row.endDate | formatDatetime }}
          </template>
        </el-table-column>
        <el-table-column label="培训状态" prop="trainState" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.trainState === 0">未开始</el-tag>
            <el-tag v-if="row.trainState === 1" type="success">进行中</el-tag>
            <el-tag v-if="row.trainState === 2" type="info">已结束</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发布状态" prop="isPublish" sortable="isPublish" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.isPublish" type="success">已发布</el-tag>
            <el-tag v-else type="info">未发布</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="培训人数" prop="userCount" sortable="userCount" width="120">
          <template slot-scope="{row}">
            {{ row.userCount }} 人
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDatetime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320">
          <template slot-scope="{row}">
            <el-button v-if="!row.isPublish" v-permission="['CourseManagement.Trains.Update']" round type="warning" size="mini" icon="el-icon-s-promotion" @click="handleTrainPublish(row)">发布</el-button>
            <el-button v-permission="['CourseManagement.Trains.Update']" round type="primary" size="mini" icon="el-icon-edit" @click="handleTrainEdit(1, row)">编辑</el-button>
            <el-button v-if="row.isPublish" v-permission="['CourseManagement.Trains']" round type="success" size="mini" icon="el-icon-info" @click="handleTrainView(row)">学习情况</el-button>
            <el-button v-permission="['CourseManagement.Trains.Delete']" round type="danger" size="mini" icon="el-icon-delete" @click="handleTrainDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getTrainsList"
      />
    </el-card>
  </div>
</template>
<script>
import {
  trainsList,
  trainsDelete,
  trainsPublish,
  trainsOffLineCourseList,
  trainsLiveList,
  trainsExamList
} from '@/api/train'
import permission from '@/directive/permission'
import Pagination from '@/components/Pagination'
import ExportExcel from '@/components/ExportExcel/index.vue'
export default {
  name: 'Train',
  directives: { permission },
  components: {
    Pagination,
    ExportExcel
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        TrainState: null,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      }
    }
  },
  created() {
    this.getTrainsList()
  },
  methods: {
    trainList(args) {
      return trainsList(args)
    },
    handleTrainPublish(row) {
      this.$confirm('发布后无法取消!是否确定发布? ', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        trainsPublish(row.id).then(res => {
          this.$message.success('发布成功')
          this.getTrainsList()
        }).catch(() => {
          this.$message.error('发布失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleTrainEdit(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'TrainNewEdits',
          query: {
            name: '添加培训'
          }
        })
      } else {
        // this.$router.push({
        //   name: 'TrainEdit',
        //   query: {
        //     name: row.name,
        //     id: row.id
        //   }
        // })
        this.$router.push({
          name: 'TrainNewEdits',
          query: {
            name: row.name,
            id: row.id
          }
        })
      }
    },
    handleTrainView(row) {
      this.$router.push({
        name: 'TrainStatistics',
        query: {
          id: row.id
        }
      })
    },

    handleTrainDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const exam = await trainsExamList(row.id)
        if (exam.items.length) {
          this.$message.warning('请先删除培训中考核后再进行删除')
          return
        }
        const resLive = await trainsLiveList({ TrainId: row.id, SkipCount: 0, MaxResultCount: 1 })
        if (resLive.items.length) {
          this.$message.warning('请先删除培训中直播课程后再进行删除')
          return
        }
        const res = await trainsOffLineCourseList({ TrainId: row.id, SkipCount: 0, MaxResultCount: 1 })
        if (res.items.length) {
          this.$message.warning('请先删除培训中线下课程后再进行删除')
          return
        }
        trainsDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getTrainsList()
        }).catch(() => {
          // this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getTrainsList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getTrainsList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getTrainsList()
    },
    getTrainsList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }

  }
}
</script>

﻿
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>题目导入</title>
    <link href="./codemirror2.css" rel="stylesheet" />
    <link href="./create.css" rel="stylesheet" />
    <link href="./protext.css" rel="stylesheet" />
    <link href="../editexec/loading1.css" rel="stylesheet" />
    <link href="./jsbox.css" rel="stylesheet" />
    <link href="./load.css" rel="stylesheet" />
</head>
<body>
    <div id="sys-loading" class="">
        <div class="spinner">
            <div class="loader-inner line-scale-pulse-out-rapid">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
            </div>
        </div>
    </div>
    <div class="textProject">
        <div class="editInfo">
            <div class="edit_head">
                <!-- <select class="category_control" name="category" id="category_control" data-action-box="true" data-live-search-placeholder="请选择"></select> -->
                <p>
                    请点击
                    <a class="help_lf" style="color:#53a4f4;margin-left: -5px;" href="javascript:;">示例文档</a>查看示例，将文本按规则粘贴（Ctrl+V）到下面编辑框中
                    <a class="help_bt" href="javascript:;" style="line-height:20px;">
                        <span>格式要求</span>
                        <img src="../editexec/img/exc_help.png" />
                    </a>
                </p>
            </div>
            <!-- <textarea class="editCon" id="editCon"></textarea> -->
            <div class="editCon" id="editCon"></div>
            <div class="edit_bottom">
                <a class="create_bt" href="javascript:;">下一步</a>
            </div>
        </div>
        <div class="previewProj">
            <div class="preview_head"></div>
            <div class="previewCon"></div>
        </div>
    </div>
    <div class="helpBox">
        <div>
            <div class="helpContx" style="padding: 5px 30px;overflow:scroll;max-height:540px;">
                <div class="part1 part" style="margin-bottom:30px;">
                    <p>
                        <span style="font-weight:800;font-size:18px;">  1、选择题</span>
                        <span class="helpContxt"><img src="./image/exectips/sg_ex_line.png" style="margin-right:5px;" />选择题题干设置</span>
                        <span class="helpContxtline"><span style="font-weight:600;margin-left: 10px;">格式要求</span>：可以在题干尾部加入[单选题]、[多选题]标识，如无标识，会智能识别1、或1.带序号的题目题干</span>
                        <span>
                            <img class="helpConimg" src="./image/exectips/sl_tl_01.png" />
                        </span>

                        <span class="helpContxt"><img src="./image/exectips/sg_ex_line.png" style="margin-right:5px;" />选择题选项设置</span>
                        <span class="helpContxtline"><span style="font-weight:600;margin-left: 10px;">格式要求</span>：选择题选项可以自动识别（A），A、，A.类型的选项，无需换行；如果没有字母标识，每个选项需要换行</span>
                        <span>
                            <img class="helpConimg" src="./image/exectips/sl_op_02.png" />
                        </span>

                        <span class="helpContxt"><img src="./image/exectips/sg_ex_line.png" style="margin-right:5px;" />选择题答案设置</span>
                        <span class="helpContxtline"><span style="font-weight:600;margin-left: 10px;">格式要求</span>：可以在题干中的括号内加入正确答案的编号（题目中存在多个括号的不支持）</span>
                        <span>
                            <img class="helpConimg" src="./image/exectips/sl_as_01.png" />
                        </span>
                    </p>

                    <p>
                        <span style="font-weight:800;font-size:18px;">  2、判断题</span>
                        <span class="helpContxt"><img src="./image/exectips/sg_ex_line.png" style="margin-right:5px;" />判断题题目答案设置</span>
                        <span class="helpContxtline"><span style="font-weight:600;margin-left: 10px;">格式要求</span>：每个题目单独一行，可以在题干尾部加入[判断题]标识，如无标识，会智能识别1、或1.带序号的题目；在每个题目结尾处，使用（√）（×）标注正确答案</span>
                        <span>
                            <img class="helpConimg" src="./image/exectips/sl_jg_01.png" />
                        </span>
                    </p>

                    <p>
                        <span style="font-weight:800;font-size:18px;">  3、文本</span>
                        <span class="helpContxt"><img src="./image/exectips/sg_ex_line.png" style="margin-right:5px;" />文本说明</span>
                        <span class="helpContxtline"><span style="font-weight:600;margin-left: 10px;">格式要求</span>：每个文本单独一行，可以在题干尾部加入[文本]标识，会识别成文本说明</span>
                        <span>
                            <img class="helpConimg" src="./image/exectips/sg_tx_01.png" />
                        </span>
                    </p>

                    <h2 style="margin-top: 20px;">注意事项</h2>
                    <p>
                        <span style="font-weight:600;margin-left: 40px;color:red">  导入题库每次最多支持100个题目，组卷最多支持100个题目， 每个选择题选项建议不超过10个。</span>
                    </p>
                    <p style="font-weight:600;margin-left: 40px;color:red">导入题目时出现与原题不符合的请手动调整;题目中有两个及以上括号时为了准确性不判断正确答案，需要手动设置。</p>
                    <p style="font-weight:600;margin-left: 40px;color:red">题目题干和选项中尽量避免使用英文的单引号和双引号，以免造成数据错误。</p>
                </div>
            </div>
            <div class="helpCon" style="padding: 5px 30px;">
                <div class="part1 part" style="margin-bottom:30px;">
                    <h2>操作演示</h2>
                    <a href="./image/操作演示.gif" target="_blank">
                        <img style="width:940px;height:auto;margin-bottom:20px;border:1px solid #ccc" src="./image/操作演示.gif" />
                    </a>
                </div>
                <div class="part2 part">
                    <h2>具体题型举例</h2>
                    <table>
                        <tr class="th">
                            <th>题型</th>
                            <th>编辑文本</th>
                            <th>预览题目</th>
                        </tr>
                        <tr class="QUESTION_TYPE_SINGLE">
                            <td>单选题</td>
                            <td class="tLeft">
                                <p class="qTitle">在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（B）°？[单选题]</p>
                                <p>（A）45°（B）90°</p>
                                <p>（C）120°（D）180°</p>
                            </td>
                            <td class="tLeft">
                                <p class="qTitle">1、在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（ ）°？</p>
                                <p><i></i>A、45°</p>
                                <p><i style="background-position: center bottom;"></i>B、90°</p>
                                <p><i></i>C、120°</p>
                                <p><i></i>D、180°</p>
                            </td>
                        </tr>

                        <tr class="QUESTION_TYPE_SINGLE">
                            <td>单选题</td>
                            <td class="tLeft">
                                <p class="qTitle">1、在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（B）°？</p>
                                <p>A.45°B.90°C.120°D.180°</p>
                            </td>
                            <td class="tLeft">
                                <p class="qTitle">1、在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（ ）°？</p>
                                <p><i></i>A、45°</p>
                                <p><i style="background-position: center bottom;"></i>B、90°</p>
                                <p><i></i>C、120°</p>
                                <p><i></i>D、180°</p>
                            </td>
                        </tr>

                        <tr class="QUESTION_TYPE_SINGLE">
                            <td>单选题</td>
                            <td class="tLeft">
                                <p class="qTitle">1、在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（B）°？</p>
                                <p>A、45°B、90°C、120°D、180°</p>
                            </td>
                            <td class="tLeft">
                                <p class="qTitle">1、在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（ ）°？</p>
                                <p><i></i>A、45°</p>
                                <p><i style="background-position: center bottom;"></i>B、90°</p>
                                <p><i></i>C、120°</p>
                                <p><i></i>D、180°</p>
                            </td>
                        </tr>

                        <tr class="QUESTION_TYPE_MULTIPLE">
                            <td>多选题</td>
                            <td class="tLeft">
                                <p class="qTitle">2、大众EA888曲轴皮带轮拆装时需要的专用工具有（ACD）？[多选题]</p>
                                <p>A、止动工具-T10355</p>
                                <p>B、装配工具-T10531/1</p>
                                <p>C、夹紧销T10531/2</p>
                                <p>D、夹紧销-T10531/3</p>
                            </td>
                            <td class="tLeft">
                                <p class="qTitle">2、大众EA888曲轴皮带轮拆装时需要的专用工具有（ ）？</p>
                                <p><i style="background-position: center bottom;"></i>A、止动工具-T10355</p>
                                <p><i></i>B、装配工具-T10531/1</p>
                                <p><i style="background-position: center bottom;"></i>C、夹紧销T10531/2</p>
                                <p><i style="background-position: center bottom;"></i>D、夹紧销-T10531/3</p>
                            </td>
                        </tr>

                        <tr class="QUESTION_TYPE_SINGLE">
                            <td>文本</td>
                            <td class="tLeft">
                                <p class="qTitle">插入一段文本提示[文本]</p>
                            </td>
                            <td class="tLeft">
                                <p class="qTitle">插入一段文本提示</p>
                            </td>
                        </tr>

                        <tr class="QUESTION_TYPE_SINGLE">
                            <td>判断题</td>
                            <td class="tLeft">
                                <p class="qTitle">判断题题目（√）[判断题]</p>
                            </td>
                            <td class="tLeft">
                                <p class="qTitle">判断题题目</p>
                                <p><i style="background-position: center bottom;"></i>正确</p>
                                <p><i></i>错误</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div>
            <div class="report_prompt">
                <div class="tccCon_t">
                    <p>您要放弃当前导入的题目吗？</p>
                </div>
                <div class="tccCon_a">
                    <div class="WJButton wj_blue go_back tcQz">确定</div>
                    <div class="WJButton wj_blue uniteC">取消</div>
                </div>
            </div>
        </div>
        <div>
            <div class="erorr_prompt">
                <div class="tccCon_t">
                    <p>导入文本格式有误，请根据提示进行修改。</p>
                </div>
                <div class="tccCon_a">
                    <div class="WJButton close_bt" onclick="cancel()">我知道了</div>
                </div>
            </div>
        </div>
    </div>
    <form action="" method="POST" id="create_project" callback="createProjectFn">
        <input type="hidden" name="project_json" />
    </form>
    <input type="hidden" id="dirId" value="0" />
    <input type="hidden" id="userid" value="0" />
    <script id="outbox" type="text/template">
        <div id="outProject" class="outProject">
            {#each data.question_list as question, index}
            <div class="question_model">
                {#if question.en_name == "QUESTION_TYPE_BLANK" }
                <p>${question.order}、$${question.showTxt}</p>
                {#else if question.en_name != "QUESTION_TYPE_TEXT" }
                <p>${question.order}、${question.title}</p>
                {#else}
                <p>${question.title}</p>
                {#/if}
                {#if question.en_name == "QUESTION_TYPE_SINGLE"}
                <div class="options">
                    {#each question.option_list as option,num}
                    <p class="option">
                        <label class="labelInfo ${question.en_name}">
                            {#if (data.tasktype == 6 && data.dirId > 0) || (data.tasktype == 4 && data.dirId <= 0) }
                            {#else}
                            {#if question.answer.indexOf(parseFloat(num)+1)>=0 }
                            <i class="active"></i>
                            {#else}
                            <i></i>
                            {#/if}
                            {#/if}

                            {#if parseFloat(num)+1 == 1}
                            A、
                            {#else if parseFloat(num)+1 == 2}
                            B、
                            {#else if parseFloat(num)+1 == 3}
                            C、
                            {#else if parseFloat(num)+1 == 4}
                            D、
                            {#else if parseFloat(num)+1 == 5}
                            E、
                            {#else if parseFloat(num)+1 == 6}
                            F、
                            {#else if parseFloat(num)+1 == 7}
                            G、
                            {#else if parseFloat(num)+1 == 8}
                            H、
                            {#else if parseFloat(num)+1 == 9}
                            I、
                            {#else if parseFloat(num)+1 == 10}
                            J、
                            {#else if parseFloat(num)+1 == 11}
                            K、
                            {#else if parseFloat(num)+1 == 12}
                            L、
                            {#else if parseFloat(num)+1 == 13}
                            M、
                            {#else if parseFloat(num)+1 == 14}
                            N、
                            {#else if parseFloat(num)+1 == 15}
                            O、
                            {#else if parseFloat(num)+1 == 16}
                            P、
                            {#else if parseFloat(num)+1 == 17}
                            Q、
                            {#else if parseFloat(num)+1 == 18}
                            R、
                            {#else if parseFloat(num)+1 == 19}
                            S、
                            {#else if parseFloat(num)+1 == 20}
                            T、
                            {#else if parseFloat(num)+1 == 21}
                            U、
                            {#else if parseFloat(num)+1 == 22}
                            V、
                            {#else if parseFloat(num)+1 == 23}
                            W、
                            {#else if parseFloat(num)+1 == 24}
                            X、
                            {#else if parseFloat(num)+1 == 25}
                            Y、
                            {#else if parseFloat(num)+1 == 26}
                            Z、
                            {#/if}

                            ${option.title}
                        </label>
                    </p>
                    {#/each}

                </div>
                {#if question.explain!=""}
                <p>解析：$${question.explain}</p>
                {#/if}
                {#else if question.en_name == "QUESTION_TYPE_MULTIPLE"}
                <div class="options">
                    {#each question.option_list as option,num}
                    <p class="option">
                        <label class="labelInfo ${question.en_name}">
                            {#if (data.tasktype == 6 && data.dirId > 0) || (data.tasktype == 4 && data.dirId <= 0) }
                            {#else}
                            {#if question.answer.indexOf(parseFloat(num)+1)>=0 }
                            <i class="active"></i>
                            {#else}
                            <i></i>
                            {#/if}
                            {#/if}

                            {#if parseFloat(num)+1 == 1}
                            A、
                            {#else if parseFloat(num)+1 == 2}
                            B、
                            {#else if parseFloat(num)+1 == 3}
                            C、
                            {#else if parseFloat(num)+1 == 4}
                            D、
                            {#else if parseFloat(num)+1 == 5}
                            E、
                            {#else if parseFloat(num)+1 == 6}
                            F、
                            {#else if parseFloat(num)+1 == 7}
                            G、
                            {#else if parseFloat(num)+1 == 8}
                            H、
                            {#else if parseFloat(num)+1 == 9}
                            I、
                            {#else if parseFloat(num)+1 == 10}
                            J、
                            {#else if parseFloat(num)+1 == 11}
                            K、
                            {#else if parseFloat(num)+1 == 12}
                            L、
                            {#else if parseFloat(num)+1 == 13}
                            M、
                            {#else if parseFloat(num)+1 == 14}
                            N、
                            {#else if parseFloat(num)+1 == 15}
                            O、
                            {#else if parseFloat(num)+1 == 16}
                            P、
                            {#else if parseFloat(num)+1 == 17}
                            Q、
                            {#else if parseFloat(num)+1 == 18}
                            R、
                            {#else if parseFloat(num)+1 == 19}
                            S、
                            {#else if parseFloat(num)+1 == 20}
                            T、
                            {#else if parseFloat(num)+1 == 21}
                            U、
                            {#else if parseFloat(num)+1 == 22}
                            V、
                            {#else if parseFloat(num)+1 == 23}
                            W、
                            {#else if parseFloat(num)+1 == 24}
                            X、
                            {#else if parseFloat(num)+1 == 25}
                            Y、
                            {#else if parseFloat(num)+1 == 26}
                            Z、
                            {#/if}

                            ${option.title}
                        </label>
                    </p>
                    {#/each}
                </div>
                {#if question.explain!=""}
                <p>解析：${question.explain}</p>
                {#/if}
                {#else if question.en_name == "QUESTION_TYPE_TEXT" }
                <div class="options">
                    <p class="option ${question.en_name}">
                    </p>
                </div>
                <!--  简答题 -->
                {#else if question.en_name == "QUESTION_TYPE_REPLY" }
                <div class="options">
                    <p class="option ${question.en_name}">
                        <textarea class="reply_textarea"></textarea>
                    </p>
                </div>
                <!-- 填空题 -->
                {#else if question.en_name == "QUESTION_TYPE_BLANK" }
                <div class="options">
                    <p class="option ${question.en_name}">
                        <div>
                    </p>
                </div>
                {#else if question.en_name == "QUESTION_TYPE_JUDGE"}
                <div class="options">
                    {#each question.option_list as option}
                    <p class="option ${question.en_name}">
                        {#if (data.tasktype == 6 && data.dirId > 0) || (data.tasktype == 4 && data.dirId <= 0) }
                        {#else}
                        {#if (option.title=="正确" && question.judgecheck==1) || (option.title=="错误" && question.judgecheck==0) }
                        <i class="active"></i>
                        {#else}
                        <i></i>
                        {#/if}
                        {#/if}
                        ${option.title}
                    </p>
                    {#/each}
                </div>
                {#if question.explain!=""}
                <p>解析：${question.explain}</p>
                {#/if}
                {#/if}
            </div>
            {#/each}
        </div>
    </script>

    <script src="./jquery.js"></script>   
    <script src="./create.js"></script>
    <script src="./codemirror2.js"></script>
    <script src="./juicer.js"></script>
    <script src="./jsbox.js"></script>
    <script src="./load.js"></script>
    <script src="../baseAjaxRequest.js"></script>
    <script src="./Import.js"></script>

    <script type="text/javascript">
    var p_type = "2";
    $(function () {
        // xhrGet('/api/exams/questionBank/category/all', {
           
        // }, function (res) {                            
        // }, function (res) {
        // })
    
        //加载loading
        document.onreadystatechange = subSomething;
        function subSomething() {
            //当页面加载状态
            if (document.readyState == "complete") {
                //延迟一秒关闭loading
                $('#sys-loading').delay(500).hide(0);
                $('.spinner').delay(500).fadeOut('slow');
            }
        };

    });
    
    function cancel() {
        $(".jsbox_close").click();
    }
    function UrlSearch(){
        var name,value;
        var str=location.href;
        var num=str.indexOf("?");
        str=str.substr(num+1);
        var arr=str.split("&");
        for(var i=0;i < arr.length;i++){
            num=arr[i].indexOf("=");
            if(num>0){
                name=arr[i].substring(0,num);
                value=arr[i].substr(num+1);
                this[name]=value;
            }
        }
    }
    function trimLeft(s) {
        if (s == null) {
            return "";
        }
        var whitespace = new String(" \t\n\r");
        var str = new String(s);
        if (whitespace.indexOf(str.charAt(0)) != -1) {
            var j = 0, i = str.length;
            while (j < i && whitespace.indexOf(str.charAt(j)) != -1) {
                j++;
            }
            str = str.substring(j, i);
        }
        return str;
    }
    String.prototype.replaceAll = function (FindText, RepText) {
        regExp = new RegExp(FindText, "g");
        return this.replace(regExp, RepText);
    }
  </script>
</body>
</html>


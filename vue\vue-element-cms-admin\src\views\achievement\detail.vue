<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="clearfix" style="height: 20px">
        <span class="role-span">{{ title }}_学生成绩详情</span>
      </div>
      <div>
        <el-input
          v-model="listQuery.Filter"
          clearable
          size="small"
          placeholder="搜索..."
          style="width: 200px"
          class="filter-item"
          @input="searchContentChange"
          @keyup.enter.native="searchHandle"
        />
        <el-button round class="filter-item" size="mini" type="success" icon="el-icon-search" @click="searchHandle">搜索
        </el-button>
        <el-button round type="primary" size="mini" icon="el-icon-edit" @click="handleBatchViewExam()">批量阅卷
        </el-button>
        <el-button
          :loading="exportLoading"
          round
          class="filter-item"
          size="mini"
          type="success"
          icon="el-icon-download"
          @click="exportHandle"
        >成绩导出
        </el-button>
        <export-word :examid="$route.query.examid" :examname="title" />
        <!-- handleSubmitAll -->
        <el-button
          v-if="examStatus==3"
          round
          type="primary"
          size="mini"
          icon="el-icon-check"
          @click="handleSubmitAll()"
        >
          批量提交</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" style="width: 100%" @sort-change="sortChange">
        <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
        <el-table-column prop="userName" label="用户名" header-align="left" width="150">
          <template slot-scope="{row}">
            <span> {{
              row.userName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" header-align="left" width="150" />
        <el-table-column prop="examName" label="考核名称" header-align="left" />
        <!-- <el-table-column
          prop="indentityCode"
          label="身份证号码"
          sortable="indentityCode"
          header-align="left"
          width="220"
        /> -->
        <el-table-column prop="className" label="部门" sortable="className" header-align="left" width="150" />
        <el-table-column prop="lastScore" label="成绩" sortable="lastScore" header-align="left" width="100" />
        <el-table-column prop="lastAnswerTimeLong" label="答题时长" header-align="left" width="120">
          <template slot-scope="{row}">
            {{ row.lastAnswerTimeLong | secondToMin }}
          </template>
        </el-table-column>
        <el-table-column prop="quitCount" label="退出次数" sortable="quitCount" header-align="left" width="100" />
        <el-table-column prop="isAnswered" label="考核状态" sortable="submitTimes" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.submitTimes > 0" size="medium">已提交</el-tag>
            <el-tag v-else-if="row.isAnswered === 0" size="medium" type="info">未进入考试</el-tag>
            <el-tag v-else-if="row.isAnswered === 1 && row.submitTimes === 0&&examStatus!=3" size="medium" type="success">正在考试</el-tag>
            <el-tag v-else-if="row.isAnswered === 1 && row.submitTimes === 0&&examStatus==3" size="medium" type="info">未提交</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="left" width="200">
          <template slot-scope="{row}">
            <el-button
              :disabled="!row.submitTimes"
              round
              type="primary"
              size="mini"
              icon="el-icon-view"
              @click="handleCheck(row)"
            >
              查看</el-button>
            <el-button
              v-if="row.submitTimes == 0 && row.isAnswered == 1&&examStatus==3"
              round
              type="primary"
              size="mini"
              icon="el-icon-check"
              @click="handleSubmit(row)"
            >
              提交</el-button>

          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="totalCount > 0"
        :total="totalCount"
        :page.sync="page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog
      v-if="achievementDetailDialog"
      class="achievementDetailDialog"
      title="学生答题详情"
      :visible.sync="achievementDetailDialog"
      top="5vh"
      width="950px"
    >
      <!-- <div class="student_exam_detail">
        <span class="exam_info">用户名:{{ studentExamRowDetail.userName }}</span><span class="exam_info">姓名:{{
          studentExamRowDetail.name
        }}</span><span class="exam_info">考核名称:{{ studentExamRowDetail.examName
        }}</span><span class="exam_info">身份证号码:{{ studentExamRowDetail.indentityCode }}</span><span
          class="exam_info"
        >部门:{{ studentExamRowDetail.className }}</span><span class="exam_info">成绩:{{
          studentExamRowDetail.lastScore
        }}分</span>
      </div> -->
      <el-descriptions title="用户信息" border :column="3" label-class-name="descriptions_label">
        <el-descriptions-item label="用户名">{{ studentExamRowDetail.userName }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ studentExamRowDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ studentExamRowDetail.className }}</el-descriptions-item>
        <el-descriptions-item label="答题时间">{{ studentExamRowDetail.creationTime | formatDatetime }}</el-descriptions-item>
        <el-descriptions-item label="提交时间">{{ studentExamRowDetail.submitDate | formatDatetime }}</el-descriptions-item>
        <el-descriptions-item label="成绩">{{ studentExamRowDetail.lastScore }} 分</el-descriptions-item>
      </el-descriptions>
      <div v-loading="achievementDetailLoading">
        <div v-for="item in newQuestionList" :key="item.type" class="examPaper_achieve">
          <el-collapse v-model="collapseActive">
            <el-collapse-item :name="item.type">
              <template slot="title">
                <div v-if="item.items && item.items.length > 0">
                  <span v-if="item.type === 0">单选题</span>
                  <span v-if="item.type === 1">多选题</span>
                  <span v-if="item.type === 2">判断题</span>
                  <span v-if="item.type === 3">填空题</span>
                  <span v-if="item.type === 6">问答题</span>
                  <span v-if="item.items && item.items.length > 0">
                    <span>(每题{{ item.score }}分, 共{{ item.items.length }}题, 得分{{ item.lastScore }},
                      满分{{ item.totalScore }})</span>
                  </span>
                </div>
              </template>
              <div v-for="(question, index) in item.items" :key="index">
                <exam-result-preview :order="question.order" :data="question" @score-change="scoreChange" />
              </div>
            </el-collapse-item>
          </el-collapse>

        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button round size="medium" @click="achievementDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="批量阅卷" :visible.sync="batchViewDialog" width="1000px" @closed="batchViewDialogClosed">
      <el-form :model="batchViewQuestionForm" label-width="80px">
        <el-form-item label="选择题目">
          <el-select
            v-model="selectQuestionId"
            placeholder="请选择题目..."
            style="width: 100%"
            @change="batchViewQuestionChange"
          >
            <el-option
              v-for="item in subjectiveQuestionList"
              :key="item.id"
              :label="JSON.parse(item.questionStem).Title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="解析">
          <el-input v-model="batchViewQuestionForm.analysis" :disabled="true" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item>
          <el-tabs v-model="currentScored" @tab-click="handleScoreTableClick">
            <el-tab-pane label="未阅" name="scored">
              <el-table v-loading="batchListLoading" :data="batchQuestionList" size="mini">
                <el-table-column label="用户名" prop="userName" width="140px" />
                <el-table-column label="答案" prop="answer">
                  <template slot-scope="{row}">
                    <span v-if="batchViewQuestionForm.questionType === 6">{{ row.answer }}</span>
                    <span v-if="batchViewQuestionForm.questionType === 3 && row.answer" class="blank-item">
                      <span
                        v-for="(str, i) in JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/)"
                        :key="i"
                      >
                        {{ str }}<span
                          v-if="i < JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/).length - 1"
                          class="answer"
                        >{{ row.answer ? JSON.parse(row.answer)[i] : '' }}</span>
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="分数" prop="score" width="120px">
                  <template slot-scope="{row}">
                    <el-select v-model="row.score" size="mini" @change="batchUserScoreChange(row)">
                      <el-option
                        v-for="i in batchViewQuestionForm.score * 2"
                        :key="i"
                        :label="i * 0.5"
                        :value="i * 0.5"
                      />
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="已阅" name="noScored">
              <el-table v-loading="batchListLoading" :data="batchQuestionList" size="mini">
                <el-table-column label="用户名" prop="userName" width="140px" />
                <el-table-column label="答案" prop="answer">
                  <template slot-scope="{row}">
                    <span v-if="batchViewQuestionForm.questionType === 6">{{ row.answer }}</span>
                    <span v-if="batchViewQuestionForm.questionType === 3 && row.answer" class="blank-item">
                      <span
                        v-for="(str, i) in JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/)"
                        :key="i"
                      >
                        {{ str }}<span
                          v-if="i < JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/).length - 1"
                          class="answer"
                        >{{ row.answer ? JSON.parse(row.answer)[i] : '' }}</span>
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="分数" prop="score" width="120px">
                  <template slot-scope="{row}">
                    <el-select v-model="row.score" size="mini" @change="batchUserScoreChange(row)">
                      <el-option
                        v-for="i in batchViewQuestionForm.score * 2"
                        :key="i"
                        :label="i * 0.5"
                        :value="i * 0.5"
                      />
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>

          <pagination
            v-show="subjectiveQuestionListQuery.totalCount > 0"
            :total="subjectiveQuestionListQuery.totalCount"
            :page.sync="subjectiveQuestionListQuery.page"
            :limit.sync="subjectiveQuestionListQuery.MaxResultCount"
            @pagination="getBatchQuestionList"
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button round @click="batchViewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import ExamResultPreview from '@/components/ExamResultPreview'
import Pagination from '@/components/Pagination'
import { subjectiveQuestionScore, subjectiveQuestionList, subjectiveQuestionAnswer } from '@/api/train'
import {
  parseTime
} from '@/utils'
import {
  studentExamResultList,
  examQuestionsDetail,
  studentExamDetail,
  submitRecord,
  submitRecords
} from '@/api/examusers'
import { formatSecond } from '@/utils/filters'
import ExportWord from '@/components/ExportWord/index.vue'
export default {
  name: 'AchievementDetail',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    ExamResultPreview,
    Pagination,
    ExportWord
  },
  data() {
    return {
      userId: null,
      list: [],
      listLoading: false,
      // 导出查询全部loading
      exportLoading: false,
      // 成绩预览loading
      achievementDetailLoading: false,
      examStatus: this.$route.query.status,
      title: this.$route.query.examTitle,
      achievementDetailDialog: false,
      listQuery: {
        ExaminationId: this.$route.query.examid,
        Filter: '',
        Sorting: '',
        MaxResultCount: 10,
        SkipCount: 0
      },
      totalCount: 0,
      page: 1,

      replyContent: [],
      // 单选集合
      singleQuestions: [],
      // 单选分数
      singleScore: 0,
      // 单选题得分
      singleLastScore: 0,
      // 判断集合
      judgeQuestions: [],
      // 判断分数
      judgeScore: 0,
      // 判断题得分
      judgeLastScore: 0,
      // 多选集合
      multipleQuestions: [],
      // 多选分数
      multipleScore: 0,
      // 多选题得分
      multipleLastScore: 0,
      // 填空集合
      blankQuestions: [],
      // 填空分数
      blankScore: 0,
      // 填空最后得分
      blankLastScore: 0,
      // 问答集合
      replayQuestions: [],
      // 问答分数
      replayScore: 0,
      // 问答最后得分
      replayLastScore: 0,
      // 新的合成数据
      newQuestionList: [],
      questionList: [],
      studentExamRowDetail: null,
      // 成绩预览折叠面板默认全部展开
      collapseActive: [0, 1, 2, 3, 6],

      // 批量阅卷
      currentScored: 'scored',
      batchViewDialog: false,
      // 学员答题答案
      batchListLoading: false,
      batchQuestionList: [],
      // 批量阅卷题目
      subjectiveQuestionList: [],
      subjectiveQuestionListQuery: {
        ExaminationId: '',
        QuestionId: '',
        IsScored: true,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 批量阅卷题目信息
      selectQuestionId: null,
      batchViewQuestionForm: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 所有学生成绩列表
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.page - 1) * this.listQuery.MaxResultCount
      studentExamResultList(this.listQuery).then(res => {
        this.list = res.items
        this.list.forEach(item => {
          this.$set(item, 'examName', this.title)
        })
        this.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    // 题目列表
    async getQuestionList() {
      await examQuestionsDetail(this.$route.query.examid).then(res => {
        this.questionList = res
      })
    },
    // 搜索
    searchHandle() {
      this.page = 1
      this.getList()
    },
    // 搜索框清空 请求列表
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    // 查看学生答题详情
    async handleCheck(row) {
      this.userId = row.userId
      this.newQuestionList = []
      this.judgeQuestions = []
      this.singleQuestions = []
      this.multipleQuestions = []
      this.blankQuestions = []
      this.replayQuestions = []

      this.replyContent = []
      this.multipleLastScore = 0
      this.judgeLastScore = 0
      this.singleLastScore = 0
      this.blankLastScore = 0
      this.replayLastScore = 0
      this.studentExamRowDetail = row
      this.achievementDetailLoading = true
      this.achievementDetailDialog = true
      var form = {
        id: this.$route.query.examid,
        userId: row.userId
      }
      await studentExamDetail(form).then(res => {
        if (res) {
          this.replyContent = JSON.parse(res.replyContent)
          this.studentExamRowDetail.startDate = res.startDate
          this.studentExamRowDetail.creationTime = res.creationTime
          this.studentExamRowDetail.submitDate = res.submitDate
        }
      }).catch(() => {
        this.replyContent = []
      })
      await this.getQuestionList()
      this.questionList.forEach(questionItem => {
        // 设置回显model 多选是数组
        if (questionItem.questionType === 1 || questionItem.questionType === 3) {
          this.$set(questionItem, 'answerModel', [])
        } else {
          this.$set(questionItem, 'answerModel', null)
        }
        this.$set(questionItem, 'IsRight', false)
        if (this.replyContent !== null) {
          this.replyContent.forEach(replyItem => {
            // 如果ID相同判断类型  回显model赋值
            if (questionItem.id === replyItem.Q) {
              if (questionItem.questionType === 1 && replyItem.O) {
                questionItem.answerModel = replyItem.O
                this.multipleLastScore += replyItem.S
              } else if (questionItem.questionType === 0 && replyItem.O) {
                questionItem.answerModel = replyItem.O[0]
                this.singleLastScore += replyItem.S
              } else if (questionItem.questionType === 2) {
                questionItem.answerModel = replyItem.J
                this.judgeLastScore += replyItem.S
              } else if (questionItem.questionType === 3 && replyItem.BA) {
                questionItem.answerModel = replyItem.BA
                this.blankLastScore += replyItem.S
              } else if (questionItem.questionType === 6) {
                questionItem.answerModel = replyItem.RA
                this.replayLastScore += replyItem.S
              }
              questionItem.finalScore = replyItem.S
              questionItem.IsRight = replyItem.R
              this.multipleLastScore = this.multipleLastScore ? this.multipleLastScore : 0
              this.singleLastScore = this.singleLastScore ? this.singleLastScore : 0
              this.judgeLastScore = this.judgeLastScore ? this.judgeLastScore : 0
              this.blankLastScore = this.blankLastScore ? this.blankLastScore : 0
              this.replayLastScore = this.replayLastScore ? this.replayLastScore : 0
            }
          })
        } else {
          this.multipleLastScore = 0
          this.singleLastScore = 0
          this.judgeLastScore = 0
          this.blankLastScore = 0
          this.replayLastScore = 0
        }
      })
      this.questionList.forEach(questionItem => {
        if (questionItem.questionType === 0) {
          this.singleQuestions.push(questionItem)
          this.singleScore = questionItem.score
        } else if (questionItem.questionType === 1) {
          this.multipleQuestions.push(questionItem)
          this.multipleScore = questionItem.score
        } else if (questionItem.questionType === 2) {
          this.judgeQuestions.push(questionItem)
          this.judgeScore = questionItem.score
        } else if (questionItem.questionType === 3) {
          this.blankQuestions.push(questionItem)
          this.blankScore = questionItem.score
        } else if (questionItem.questionType === 6) {
          this.replayQuestions.push(questionItem)
          this.replayScore = questionItem.score
        }
      })
      if (this.judgeQuestions.length) {
        this.newQuestionList.push({
          type: 2,
          score: (this.judgeScore).toFixed(1),
          totalScore: (this.judgeScore * this.judgeQuestions.length).toFixed(1),
          lastScore: this.judgeLastScore,
          items: this.judgeQuestions
        })
      }
      if (this.singleQuestions.length) {
        this.newQuestionList.push({
          type: 0,
          score: (this.singleScore).toFixed(1),
          totalScore: (this.singleScore * this.singleQuestions.length).toFixed(1),
          lastScore: this.singleLastScore,
          items: this.singleQuestions
        })
      }
      if (this.multipleQuestions.length) {
        this.newQuestionList.push({
          type: 1,
          score: (this.multipleScore).toFixed(1),
          totalScore: (this.multipleScore * this.multipleQuestions.length).toFixed(1),
          lastScore: this.multipleLastScore,
          items: this.multipleQuestions
        })
      }
      if (this.blankQuestions.length) {
        this.newQuestionList.push({
          type: 3,
          score: (this.blankScore).toFixed(1),
          totalScore: (this.blankScore * this.blankQuestions.length).toFixed(1),
          lastScore: this.blankLastScore,
          items: this.blankQuestions
        })
      }
      if (this.replayQuestions.length) {
        this.newQuestionList.push({
          type: 6,
          score: (this.replayScore).toFixed(1),
          totalScore: (this.replayScore * this.replayQuestions.length).toFixed(1),
          lastScore: this.replayLastScore,
          items: this.replayQuestions
        })
      }
      this.achievementDetailLoading = false
    },
    answerChange() {

    },
    // 批量阅卷
    async handleBatchViewExam(row) {
      // 请求数据
      //

      this.currentScored = 'scored'
      this.batchQuestionList = []
      this.subjectiveQuestionListQuery.IsScored = false
      this.subjectiveQuestionListQuery.QuestionId = null
      this.subjectiveQuestionListQuery.ExaminationId = this.$route.query.examid
      this.subjectiveQuestionList = []
      const res = await subjectiveQuestionList(this.$route.query.examid)
      this.subjectiveQuestionList = res

      this.batchViewDialog = true
    },
    // 批量阅卷题目变化
    async batchViewQuestionChange(val) {
      // this.batchViewQuestionForm = {}
      this.subjectiveQuestionListQuery.QuestionId = val
      this.subjectiveQuestionListQuery.page = 1
      this.batchViewQuestionForm = this.subjectiveQuestionList.find(item => { return item.id === this.selectQuestionId })
      // 获取最新列表
      this.getBatchQuestionList()
    },
    // 学生分数变化
    batchUserScoreChange(row) {
      // 提交数据
      var data = {
        examinationId: this.$route.query.examid,
        questionId: this.subjectiveQuestionListQuery.QuestionId,
        userId: row.userId,
        score: row.score
      }
      subjectiveQuestionScore(data).then(res => {

      })
    },
    async handleScoreTableClick(val, event) {
      this.subjectiveQuestionListQuery.page = 1
      if (val.name === 'scored') {
        this.subjectiveQuestionListQuery.IsScored = false
      } else {
        this.subjectiveQuestionListQuery.IsScored = true
      }
      this.getBatchQuestionList()
    },
    scoreChange(id, val, oldVal, type) {
      var data = {
        examinationId: this.$route.query.examid,
        questionId: id,
        userId: this.userId,
        score: val
      }
      subjectiveQuestionScore(data).then(res => {
        this.$message.success('评分成功')
        this.newQuestionList.forEach(item => {
          if (item.type === type) {
            item.lastScore = item.lastScore + val - oldVal
            this.studentExamRowDetail.lastScore = this.studentExamRowDetail.lastScore + val - oldVal
            return
          }
        })
      })
    },
    batchViewDialogClosed() {
      this.getList()
    },
    // 导出学生成绩
    async exportHandle() {
      this.exportLoading = true
      var allList = []
      const maxCount = 500
      var listForm = {
        ExaminationId: this.$route.query.examid,
        Sorting: '',
        MaxResultCount: maxCount,
        SkipCount: 0
      }
      // await studentExamResultList(listForm).then(res => {
      //   allList = res.items
      //   allList.forEach(item => {
      //     this.$set(item, 'examName', this.$route.query.examTitle)
      //   })
      //   this.exportLoading = false
      // }).catch(() => {
      //   this.exportLoading = false
      // })
      var res = await studentExamResultList(listForm)
      const times = res.totalCount % maxCount === 0 ? res.totalCount / maxCount : (Math.floor(res.totalCount / maxCount) + 1)
      var totalList = res.items
      for (let i = 1; i < times; i++) {
        listForm = {
          ExaminationId: this.$route.query.examid,
          Sorting: '',
          SkipCount: i * maxCount,
          MaxResultCount: maxCount
        }
        const res2 = await studentExamResultList(listForm)
        totalList = totalList.concat(res2.items)
      }
      this.exportLoading = false
      import('@/vendor/Export2Excel').then((excel) => {
        const tHeader = ['考核名称', '用户名', '姓名', '身份证号码', '部门', '成绩', '单选题得分', '多选题得分', '判断题得分', '填空题得分', '问答题得分', '答题时长(秒)', '备注']
        const filterVal = ['examName', 'userName', 'name', 'indentityCode', 'className', 'lastScore', 'singleScore', 'multiScore', 'judgeScore', 'blankScore', 'replyScore', 'lastAnswerTimeLong', 'remarks']
        // 成绩列表数据
        // const list = Response.items;
        const data = this.formatJson(filterVal, totalList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.$route.query.examTitle + '_学生成绩_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          if (j == 'examName') {
            return this.$route.query.examTitle
          }
          if (j === 'lastAnswerTimeLong') {
            return formatSecond(v[j])
          } else {
            return v[j]
          }
        })
      )
    },
    getBatchQuestionList() {
      this.batchListLoading = true
      this.subjectiveQuestionListQuery.SkipCount = (this.subjectiveQuestionListQuery.page - 1) * this.subjectiveQuestionListQuery.MaxResultCount
      subjectiveQuestionAnswer(this.subjectiveQuestionListQuery).then(res => {
        this.batchQuestionList = res.items
        this.batchListLoading = false
      })
    },
    // 列表排序
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleSubmit(row) {
      this.$confirm('是否确认提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var data = {
          ExaminationId: this.listQuery.ExaminationId,
          UserId: row.userId
        }
        submitRecord(data).then(res => {
          this.getList()
          this.$message.success('操作成功')
        })
      })
    },
    handleSubmitAll() {
      this.$confirm('是否确认提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var data = {
          ExaminationId: this.listQuery.ExaminationId
        }
        submitRecords(data).then(res => {
          this.getList()
          this.$message.success('操作成功')
        })
      })
    }
  }
}

</script>
<style scoped>
.question_type {
  width: 100%;
  font-size: 16px;
  background-color: rgb(233, 239, 247);
  height: 35px;
  line-height: 35px;
  padding-left: 10px;
  margin-bottom: 10px;
}

.question_score_info {
  float: right;
  font-size: 14px;
  margin-right: 10px;
}

.examPaper_achieve ::v-deep .el-collapse-item__header {
  border: none;
  height: auto;
}

.student_exam_detail {
  margin-bottom: 20px;
}

.exam_info {
  margin-right: 20px;
}

.blank-item .answer {
  /* height: 42px;
        line-height: 42px;
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 1px 10px;
        outline: none; */
  text-decoration: underline #ccc;
  text-underline-offset: 5px;
  margin: 0 5px;
}
.el-descriptions ::v-deep .descriptions_label {
  width: 100px;
}
</style>

<template>
  <div>
    <div style="margin-bottom: 10px;">
      <el-cascader
        v-model="listQuery.OUId"
        filterable
        :options="orgList"
        :props="cascaderProps"
        clearable
        size="mini"
        placeholder="请选择部门..."
        style="margin-right:10px"
        @change="handleClassChange"
      />
      <el-input
        v-model="listQuery.Filter"
        style="display: inline-block"
        class="small_input"
        size="mini"
        placeholder="姓名/用户名"
        @input="handleRefreshList"
      />
      <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    </div>
    <el-table v-loading="listLoading" :data="list" max-height="600px" size="mini" @selection-change="handleSelectUserChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column prop="name" label="姓名" header-align="left" /> -->
      <el-table-column prop="name" label="姓名" header-align="left">
        <template slot-scope="{row}">
          <span class="table_span">{{ row.name }}({{ row.userName }})</span>
        </template>
      </el-table-column>
      <el-table-column prop="extraProperties" label="部门" header-align="left">
        <template slot-scope="{row}">
          <span v-if="row.extraProperties" class="table_span">{{ row.extraProperties.OUName }}</span>
          <span v-else class="table_span">--</span>
        </template>
      </el-table-column>
      <el-table-column prop="cloudLearn" min-width="80">
        <template slot="header" slot-scope="{row}">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">允许学习</el-checkbox>
        </template>
        <template slot-scope="{row}">
          <el-checkbox v-model="row.cloudLearn" @change="handleCloudLearnChange" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      class="mini_pagination"
      :small="true"
      :total="listQuery.totalCount"
      layout="total,sizes,prev, pager, next"
      :page-size="listQuery.MaxResultCount"
      :page-sizes="[10,20,50,100,500, 1000]"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getList"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { getFilterUsers } from '@/api/user'
export default {
  name: 'ChooseUser',
  components: {
    Pagination
  },
  props: {
    // 班级数据(el-select)
    allOrg: {
      type: Array,
      require: true,
      default: null
    }
  },
  data() {
    return {
      orgList: [],
      cascaderProps: {
        label: 'displayName',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      listQuery: {
        OuId: null,
        GetChildren: true,
        Filter: '',
        Sorting: 'creationtime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        totalCount: 0,
        page: 1
      },
      list: [],
      listLoading: false,
      isIndeterminate: false,
      checkAll: true
    }
  },
  mounted() {
    this.getList()
    this.initClassList()
  },
  methods: {
    handleClassChange() {
      this.handleRefreshList()
    },
    handleSelectUserChange(val) {
      this.$emit('response-fn', val)
    },
    handleCheckAllChange(val) {
      this.list.forEach((item, i) => {
        this.$set(this.list, i, { ...item, cloudLearn: val })
      })

      this.isIndeterminate = false
    },
    handleCloudLearnChange(val) {
      const newArr = this.list.filter(item => item.cloudLearn === true)
      const checkedCount = newArr.length
      this.checkAll = checkedCount === this.list.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.list.length
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    // 初始化专业班级
    initClassList() {
      this.orgList = this.deleteChildren(this.allOrg.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      getFilterUsers(this.listQuery).then(res => {
        this.list = []
        res.items.forEach(item => {
          this.list.push({ ...item, cloudLearn: true })
        })
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      })
    }
  }
}
</script>

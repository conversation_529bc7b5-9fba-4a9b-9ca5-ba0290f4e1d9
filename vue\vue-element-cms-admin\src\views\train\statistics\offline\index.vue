<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-24 09:52:30
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-12 11:15:58
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/offline/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div>
    <export-excel
      :header="['课程名称', '课时']"
      :filter-val="['name', 'classHour']"
      :query="{'TrainId': trainId}"
      :api-fn="trainsOffLineCourseList"
    />
    <el-table v-loading="listLoading" :data="list" highlight-current-row>
      <el-table-column label="课程名称" prop="name">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewCourseDetail(row)">{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时" prop="classHour" />

    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getTrainOffLineCourse"
    />
    <el-dialog v-if="trainCourseUserDialog" class="trainCourseUserDialog" title="培训课程学生详情" :visible.sync="trainCourseUserDialog" top="5vh" width="1200px">
      <t-offline-course :course-id="courseId" :train-id="trainId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="trainCourseUserDialog = false">关  闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsOffLineCourseList } from '@/api/train'
import Pagination from '@/components/Pagination'
import TOfflineCourse from './detail.vue'
export default {
  name: 'TrainOfflineCourse',
  components: {
    TOfflineCourse,
    Pagination
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: '',
        TrainId: this.trainId,
        page: 1,
        totalCount: 0
      },
      trainCourseUserDialog: false,
      courseId: ''
    }
  },
  created() {
    this.getTrainOffLineCourse()
  },
  methods: {
    trainsOffLineCourseList(args) {
      return trainsOffLineCourseList(args)
    },
    handleViewCourseDetail(row) {
      this.courseId = row.id
      this.trainCourseUserDialog = true
    },
    // 获取培训线下课程
    getTrainOffLineCourse() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsOffLineCourseList(this.listQuery).then((res) => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {})
    }
  }
}
</script>


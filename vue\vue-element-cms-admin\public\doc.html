<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>文档预览</title>
</head>

<body>
  <div id="reader"></div>
  <script src="http://static.bcedocument.com/reader/v2/doc_reader_v2.js"></script>
  <script>
    (function () {
      function getQueryVariable(variable) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split("=");
          if (pair[0] == variable) {
            return pair[1];
          }
        }
        return (false);
      }
      var url = getQueryVariable("url");
      var option = {
        docId: url,
        token: 'TOKEN',
        host: 'BCEDOC',
        serverHost: 'http://doc.bj.baidubce.com',
        width: 920, //文档容器宽度
        zoom: false, //是否显示放大缩小按钮
        zoomStepWidth: 200,
        pn: 1, //定位到第几页，可选
        ready: function (handler) { // 设置字体大小和颜色, 背景颜色（可设置白天黑夜模式）
          handler.setFontSize(1);
          handler.setBackgroundColor('#000');
          handler.setFontColor('#fff');
        },
        flip: function (data) { // 翻页时回调函数, 可供客户进行统计等
        },
        fontSize: 'big',
        toolbarConf: {
          page: true, //上下翻页箭头图标
          pagenum: true, //几分之几页
          full: false, //是否显示全屏图标,点击后全屏
          copy: false, //是否可以复制文档内容
          position: 'center', // 设置 toolbar中翻页和放大图标的位置(值有left/center)
        } //文档顶部工具条配置对象,必选
      };
      new Document('reader', option);
    })();

  </script>

</body>

</html>

<template>
  <div>
    <el-row :gutter="15">
      <el-col :span="6">
        <el-card class="shadow_none card-padding-0">
          <div slot="header" class="clearfix">
            <el-button
              type="text"
              style="padding: 0;"
              @click="handleNodeClick()"
            >全部</el-button>
          </div>

          <el-tree
            v-loading="treeLoading"
            class="course-dir-tree"
            style="height: 811px;overflow: auto"
            default-expand-all
            highlight-current
            :data="data"
            :props="defaultProps"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="shadow_none" shadow="always">
          <div slot="header" class="flex_between_box">
            <span class="role-span">{{ knoweldgeTitle }}</span>
            <div class="">
              <el-input
                v-model="form.Filter"
                clearable
                size="small"
                placeholder="搜索..."
                style="width: 200px"
                class="filter-item"
                @input="searchContentChange"
                @keyup.enter.native="searchHandle"
              />
              <el-button
                class="filter-item"
                size="small"
                round
                type="success"
                icon="el-icon-search"
                @click="searchHandle"
              >搜索
              </el-button>
            </div>
          </div>
          <el-row>
            <span style="margin-right: 20px">题型</span>
            <el-radio-group
              v-model="form.QuestionType"
              @change="questionTypeChange"
            >
              <el-radio :label="null" aria-checked="true">全部</el-radio>
              <el-radio :label="0">单选题</el-radio>
              <el-radio :label="1">多选题</el-radio>
              <el-radio :label="2">判断题</el-radio>
            </el-radio-group>
          </el-row>
          <el-row>
            <span style="margin-right: 20px">难度</span>
            <el-radio-group
              v-model="form.Difficulty"
              @change="questionTypeChange"
            >
              <el-radio :label="null" aria-checked="true">全部</el-radio>
              <el-radio :label="1">易</el-radio>
              <el-radio :label="2">偏易</el-radio>
              <el-radio :label="3">适中</el-radio>
              <el-radio :label="4">偏难</el-radio>
              <el-radio :label="5">难</el-radio>
            </el-radio-group>
          </el-row>

          <el-table
            v-loading="listLoading"
            :data="list"
            size="medium"
            style="width: 100%"
            height="572px"
            @sort-change="sortChange"
          >
            <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
            <el-table-column
              prop="questionType"
              label="类型"
              sortable="questionType"
              header-align="left"
              width="120px"
            >
              <template slot-scope="scope">
                <el-tag
                  v-if="scope.row.questionType === 0"
                  type="success"
                  size="medium"
                >单选题</el-tag>
                <el-tag
                  v-if="scope.row.questionType === 1"
                  size="medium"
                >多选题</el-tag>
                <el-tag
                  v-if="scope.row.questionType === 2"
                  size="medium"
                  type="warning"
                >判断题</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="questionStem"
              label="题干"
              sortable="questionStem"
              header-align="left"
              min-width="200"
            >
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleCheck(row)">{{
                  JSON.parse(row.questionStem).Title
                }}</span>
              </template>
            </el-table-column>

            <el-table-column
              prop=""
              label="操作"
              header-align="left"
              width="120px"
            >
              <template slot-scope="{ row }">
                <el-button
                  round
                  type="primary"
                  size="mini"
                  icon="el-icon-view"
                  @click="handleCheck(row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="totalCount > 0"
            :total="totalCount"
            :page.sync="page"
            :limit.sync="form.MaxResultCount"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      v-if="questionDetailDialog"
      class="questionDetailDialog"
      title="题目预览"
      :visible.sync="questionDetailDialog"
      width="950px"
    >
      <question-preview :data="questionDetailItem" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="questionDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import QuestionPreview from '@/components/QuestionPreview'
import { questionBankTree, questionBankList } from '@/api/jgQuestionBank'
export default {
  name: 'JgQuestionBank',
  components: {
    Pagination,
    QuestionPreview
  },
  data() {
    return {
      // 当前选择的知识点
      knoweldgeTitle: '全部',
      data: [],
      defaultProps: {
        children: 'childs',
        label: 'name'
      },
      list: [],
      form: {
        QuestionBankPackageParentId: '',
        QuestionBankPackageId: '',
        Difficulty: null,
        QuestionType: null,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10
      },
      totalCount: 0,
      page: 1,
      questionDetailDialog: false,
      questionDetailItem: null,
      treeLoading: false,
      listLoading: false
    }
  },
  mounted() {
    this.getTreeList()
    this.getList()
  },
  methods: {
    getTreeList() {
      this.data = []
      this.treeLoading = true
      questionBankTree().then((res) => {
        res.forEach((item) => {
          this.data.push(item)
        })
        this.treeLoading = false
      })
    },
    getList() {
      this.listLoading = true
      this.form.SkipCount = (this.page - 1) * this.form.MaxResultCount
      questionBankList(this.form).then((res) => {
        this.list = res.items
        this.totalCount = res.totalCount
        this.listLoading = false
      })
    },
    // 树桩节点点击
    handleNodeClick(data) {
      if (data === undefined) {
        this.knoweldgeTitle = '全部'
        this.form.QuestionBankPackageParentId = null
        this.form.QuestionBankPackageId = null
      } else {
        this.knoweldgeTitle = data.name
        this.form.QuestionBankPackageParentId =
          data.parentId === null ? data.id : data.parentId
        this.form.QuestionBankPackageId =
          data.parentId === null ? null : data.id
      }
      this.page = 1
      this.getList()
    },
    // 蓝字点击
    handleCheck(row) {
      this.questionDetailItem = row
      this.questionDetailDialog = true
    },
    // 搜索
    searchHandle() {
      this.page = 1
      this.getList()
    },
    // 搜索框清空 请求列表
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.form.Sorting = prop + ' ' + order
      this.getList()
    },
    questionDifficultyChange() {
      this.getList()
    },
    questionTypeChange() {
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
// .el-card
// {
//   position: relative;
// }
.el-card .el-row {
  position: relative;
  margin: 0px 0px 20px 0px;
  &:last-child {
    margin-bottom: 0;
  }
}
.seach {
  float: right;
  position: absolute;
  top: 80px;
  right: 30px;
}
</style>


<template>
  <div class="app-container">

    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="clearfix" style="height: 20px">
        <!-- <span class="role-span">考核列表</span> -->
        <div style="">
          <el-radio-group v-model="active" size="small" style="margin-right: 15px" @change="checkStatusChange">
            <el-radio-button label="0">全部</el-radio-button>
            <el-radio-button label="1">未开始</el-radio-button>
            <el-radio-button label="2">进行中</el-radio-button>
            <el-radio-button label="3">已完成</el-radio-button>
          </el-radio-group>
          <el-input
            v-model="listQuery.Keyword"
            clearable
            size="small"
            placeholder="搜索..."
            style="width: 200px"
            class="filter-item"
            @input="searchContentChange"
            @keyup.enter.native="searchHandle"
          />
          <el-button class="filter-item" round size="small" type="success" icon="el-icon-search" @click="searchHandle">
            搜索
          </el-button>
          <el-button
            v-permission="['Exam.Examinations.Create']"
            class="filter-item"
            round
            size="small"
            type="primary"
            icon="el-icon-plus"
            :loading="userLoading"
            @click="handleEdit(0, 0)"
          >添加
          </el-button>
          <el-button
            v-permission="['Exam.Examinations.Create']"
            class="filter-item"
            round
            size="small"
            type="primary"
            icon="el-icon-plus"
            :loading="userLoading"
            @click="handleEdit(4, 0)"
          >批量添加
          </el-button>
          <export-excel
            :header="['考核名称', '类型', '试卷名称', '开始时间', '结束时间', '考核时长', '总分', '参与人数']"
            :filter-val="['name', 'examinationMode', 'examPaperName', 'startDate', 'endDate', 'timeLong', 'totalScore', 'studentCount']"
            :field="{ 1: ['考试', '练习', '外部'], 3: [2], 4: [2], 8: ['未开始', '进行中', '已结束'] }"
            :api-fn="examList"
          />
        </div>
      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" style="width: 100%" @sort-change="sortChange">
        <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
        <el-table-column prop="name" label="考核名称" sortable="name" min-width="300" />
        <el-table-column prop="examinationMode" label="类型" sortable="examinationMode" width="80">
          <template slot-scope="{row}">
            <span v-if="row.examinationMode === 0">
              考试
            </span>
            <span v-if="row.examinationMode === 1">
              练习
            </span>
            <span v-if="row.examinationMode === 2">
              外部
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="examPaperName" label="试卷名称" sortable="examPaperName" min-width="300" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span v-if="row.examinationMode === 2" class="link-type" @click="handleOpenUrl(row)">{{ row.examUrl }}</span>
            <span v-else class="link-type" @click="handlePreview(row)">{{
              row.examPaperName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" sortable="startDate" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.startDate | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束时间" sortable="endDate" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.endDate | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" label="创建时间" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.creationTime | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="timeLong" label="考核时长" sortable="timeLong" width="120">
          <template slot-scope="{row}">
            <span>{{
              row.timeLong
            }}分钟</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalScore" label="总分" sortable="totalScore" width="100">
          <template slot-scope="{row}">
            <span>{{
              row.totalScore ? row.totalScore +' 分' : '--'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="studentCount" label="参与人数" sortable="studentCount" width="100">
          <template slot-scope="{row}">
            <span>{{
              row.studentCount
            }}人</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="考核状态" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.status === 1" size="medium">未开始</el-tag>
            <el-tag v-if="row.status === 2" size="medium" type="success">进行中</el-tag>
            <el-tag v-if="row.status === 3" size="medium" type="info">已完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="380">
          <template slot-scope="{row}">
            <el-button
              v-if="row.isPublish === 0"
              v-permission="['Exam.Examinations.Update']"
              :loading="publishLoading"
              round
              type="success"
              size="mini"
              icon="el-icon-s-promotion"
              @click="handlePublish(row)"
            >
              发布</el-button>
            <el-button
              v-if="row.isPublish === 1"
              v-permission="['Exam.Examinations.Update']"
              :loading="publishLoading"
              round
              type="warning"
              size="mini"
              icon="el-icon-s-promotion"
              @click="handlePublish(row)"
            >
              取消发布</el-button>
            <el-button
              v-if="row.status !== 1"
              round
              type="primary"
              size="mini"
              icon="el-icon-view"
              @click="handleCheck(row)"
            >
              查看</el-button>
            <el-button
              v-if="row.status === 1"
              v-permission="['Exam.Examinations.Update']"
              :loading="editLoading || userLoading"
              round
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleEdit(1, row)"
            >编辑
            </el-button>
            <el-button
              v-if="row.status === 2 && row.allowRepeatSubmit == false"
              v-permission="['Exam.Examinations.Update']"
              round
              type="warning"
              size="mini"
              icon="el-icon-refresh-right"
              @click="handleEdit(2, row)"
            >续考
            </el-button>
            <!-- <el-button
              v-if="row.status === 2"
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleEdit(3, row)"
            >重考
            </el-button> -->
            <el-button
              v-permission="['Exam.Examinations.Delete']"
              :loading="deleteLoading"
              round
              type="danger"
              size="mini"
              icon="el-icon-delete"
              @click="handleDelete(row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="totalCount > 0"
        :total="totalCount"
        :page.sync="page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog class="studentDialog" :title="studentDialogTitle" :visible.sync="studentDialog" width="800px">
      <div class="choice_stu">
        <span style="font-size: 16px; color: #909399;line-height: 30px;">已选择学生:</span><span
          style="font-size: 16px;font-weight: bold;"
        >
          {{ continueStudentName && continueStudentName.length > 0 ? continueStudentName.length : 0 }}名学生 </span>
        <!-- <span v-for="item in continueStudentName" class="s_name">{{ item }}</span> -->
        <div style="float:right">
          <el-input
            v-model="examJoinListQuery.Keyword"
            clearable
            size="small"
            placeholder="搜索..."
            style="width: 200px"
            class="filter-item"
            @input="examJoinStudentSearchChange"
            @keyup.enter.native="examJoinStuSearchHandle"
          />
          <el-button
            class="filter-item"
            round
            size="mini"
            type="success"
            icon="el-icon-search"
            @click="examJoinStuSearchHandle"
            @input="examJoinStudentSearchChange"
          >搜索
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="examJoinListLoading"
        :data="examJoinStudentList"
        border
        style="width: 100%"
        @selection-change="handleContinueStuChange"
      >
        <el-table-column type="selection" :selectable="checkSelectable" width="55" />
        <el-table-column prop="userName" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="studentIDNumber" label="工号" />
        <el-table-column prop="className" label="部门" />
        <el-table-column prop="submitTimes" label="状态">
          <template slot-scope="{row}">
            <span v-if="row.submitTimes > 0">已提交</span>
            <span v-if="row.submitTimes == 0">未提交</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="examJoinListQuery.totalCount > 0"
        :total="examJoinListQuery.totalCount"
        :page.sync="examJoinListQuery.page"
        :limit.sync="examJoinListQuery.MaxResultCount"
        @pagination="getPageData"
      />
      <!-- <pagination v-show="examJoinListQuery.totalCount > 0" class="mini_pagination" :small="true"
        :total="examJoinListQuery.totalCount" layout="total,prev, pager, next" :page-size="10"
        :page.sync="examJoinListQuery.page" :limit.sync="examJoinListQuery.MaxResultCount"
        @current-change="clearSelectStudent" @pagination="getPageData" /> -->
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" :loading="continueLoading" round type="primary" @click="continueBtn">确 定</el-button>
        <el-button size="medium" round @click="studentDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      class="assessmentDialog"
      :close-on-click-modal="false"
      :title="assessmentDialogTitle"
      :visible.sync="assessmentDialog"
      top="5vh"
      width="950px"
    >
      <div style="height: 679px">
        <el-steps :active="activeStep" finish-status="success" simple style="margin-top: 20px">
          <el-step title="填写考核信息" />
          <el-step title="设置考生" />
          <el-step title="设置考卷" />
          <el-step title="确认完成" />
        </el-steps>
        <div v-show="activeStep === 0" class="test_form assessment">
          <el-form
            ref="examinationInfo"
            :model="assessmentForm.examinationsDto"
            label-width="150px"
            size="medium"
            :rules="assessmentRules"
          >
            <el-form-item label="考核类型" prop="examinationMode">
              <el-radio-group v-model="assessmentForm.examinationsDto.examinationMode" @change="examModelChange">
                <el-radio :label="0">考试</el-radio>
                <el-radio :label="1">练习</el-radio>
                <el-radio v-if="!isBatchAdd" :label="2">外部</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="!isBatchAdd" label="考核编号">
              <el-input
                v-model="assessmentForm.examinationsDto.code"
                class="small_input input_color"
                :disabled="true"
              />
            </el-form-item>

            <el-form-item label="考核名称" prop="name">
              <el-input v-model="assessmentForm.examinationsDto.name" class="small_input" />
            </el-form-item>
            <el-form-item label="考试时长" prop="timeLong">
              <el-input-number
                v-model.number="assessmentForm.examinationsDto.timeLong"
                :min="1"
                :max="limitTimeLong"
                label="描述文字"
                @change="timeLongChange"
              /> 分钟
            </el-form-item>
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="assessmentForm.examinationsDto.startDate"
                type="datetime"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptionsBegin"
                @change="startDateChange"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="assessmentForm.examinationsDto.endDate"
                :disabled="!assessmentForm.examinationsDto.examinationMode"
                type="datetime"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptionsBegin"
              />
            </el-form-item>
            <el-form-item v-if="!assessmentForm.examinationsDto.examinationMode" label="进入考试限制" prop="startLimitTime">
              <el-input-number
                v-model.number="assessmentForm.examinationsDto.startLimitTime"
                :min="0"
                :max="assessmentForm.examinationsDto.timeLong - 1"
                label="描述文字"
              />
              <span class="form_alert_info">{{ (assessmentForm.examinationsDto.startLimitTime !== 0 &&
                assessmentForm.examinationsDto.startLimitTime !== undefined) ? '考试开始' +
                assessmentForm.examinationsDto.startLimitTime + '分钟后,学生将无法进入考试' : '允许学生任意考试时间内开始考试'
              }}</span>
            </el-form-item>
            <el-form-item v-if="!assessmentForm.examinationsDto.examinationMode" label="交卷时间限制" prop="submitLimitTime">
              <el-input-number
                v-model.number="assessmentForm.examinationsDto.submitLimitTime"
                :min="0"
                :max="assessmentForm.examinationsDto.timeLong - 1"
                label="描述文字"
              />
              <span class="form_alert_info">{{ (assessmentForm.examinationsDto.submitLimitTime !== 0 &&
                assessmentForm.examinationsDto.submitLimitTime !== undefined) ? '考试开始' +
                assessmentForm.examinationsDto.submitLimitTime + '分钟后,方可允许交卷' : '允许学生任意考试时间内交卷'
              }}</span>
            </el-form-item>
            <el-form-item v-if="assessmentForm.examinationsDto.examinationMode !== 2" label="">
              <el-checkbox v-model="assessmentForm.examinationsDto.isDisorder" :true-label="1" :false-label="0">题目顺序随机显示
              </el-checkbox>
            </el-form-item>
          </el-form>
        </div>
        <div v-show="activeStep === 1" class="assessment">
          <el-button
            v-permission="['AppUserManagement.Classes']"
            style="margin-bottom: 5px"
            round
            size="small"
            type="primary"
            icon="el-icon-plus"
            @click="handleSelectClass"
          >选择班级</el-button>
          <choose-user
            v-if="activeStep === 1"
            :key="tableKey"
            :all-org="allOrg"
            :all-student="allStudent"
            :current-select-student="selectUsers"
            @user-change="chooseStudents"
          />
        </div>
        <div v-show="activeStep === 2" class="assessment">
          <div v-if="assessmentForm.examinationsDto.examinationMode !== 2">
            <div v-if="!isBatchAdd">
              <span style="font-size: 16px; color: #909399;line-height: 30px;">已选试卷: </span>
              <span style="font-size: 16px;font-weight: bold;">{{ assessmentForm.examinationsDto.examPaperName }}</span>
            </div>
            <div v-if="isBatchAdd">
              <span style="font-size: 16px; color: #909399;line-height: 30px;">已选试卷: </span>
              <span style="font-size: 16px;font-weight: bold;">{{ generatingPaperSelect.length }} 份</span>
            </div>
            <div style="float: right;margin-bottom: 20px">
              <el-input
                v-model="examPaperListQuery.Filter"
                clearable
                size="small"
                placeholder="搜索..."
                style="width: 200px"
                class="filter-item"
                @input="examPaperSearchChange"
                @keyup.enter.native="examPaperSearchHandle"
              />
              <el-button
                round
                class="filter-item"
                size="mini"
                type="success"
                icon="el-icon-search"
                @click="examPaperSearchHandle"
              >
                搜索
              </el-button>
            </div>
            <el-table
              ref="examination"
              v-loading="examPaperLoading"
              :data="examinationList"
              border
              size="mini"
              style="width: 100%"
              :row-key="getRowKeys"
              :reserve-selection="isBatchAdd"
              @selection-change="handleExaminationChange"
              @row-click="handleExaminationRowClick"
              @current-change="handleExaminationCurrentChange"
            >
              <el-table-column type="selection" :reserve-selection="true" width="55" />
              <el-table-column prop="code" label="试卷编号" width="250px" />

              <el-table-column prop="name" label="试卷名称" />
              <el-table-column prop="totalScore" label="总分" width="120px" />
            </el-table>
            <pagination
              v-show="examPaperListQuery.totalCount > 0"
              class="mini_pagination"
              :small="true"
              :total="examPaperListQuery.totalCount"
              layout="total,prev, pager, next"
              :page-size="10"
              :page.sync="examPaperListQuery.page"
              :limit.sync="examPaperListQuery.MaxResultCount"
              @pagination="getExamPaper"
            />
          </div>
          <div v-else>
            <el-form label-width="150px" size="medium">
              <el-form-item label="外链">
                <el-input v-model="assessmentForm.examinationsDto.examUrl" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div v-show="activeStep === 3" class="assessment">
          <el-form ref="assessmentForm" :inline="true" :model="assessmentForm.examinationsDto" label-width="100px">
            <el-row>
              <el-col :span="12">
                <el-form-item v-if="!isBatchAdd" label="考核编号">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.code }}</label>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="!isBatchAdd" label="考核名称">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.name }}</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="assessmentForm.examinationsDto.examinationMode !== 2" :span="12">
              <el-col :span="12">
                <el-form-item v-if="!isBatchAdd" label="总分">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.totalScore }}分</label>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item v-if="!isBatchAdd" label="试卷编号">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.examPaperCode }}</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="assessmentForm.examinationsDto.examinationMode !== 2">
              <el-col :span="12">
                <el-form-item v-if="!isBatchAdd" label="试卷名称">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.examPaperName }}</label>
                </el-form-item>
                <el-form-item v-if="isBatchAdd" label="试卷数量">
                  <label class="infoLabel">{{ generatingPaperSelect.length }}</label>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="!isBatchAdd" label="题目数">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.questionNumber }}道</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="assessmentForm.examinationsDto.examinationMode === 2">
              <el-col :span="24">
                <el-form-item label="试卷链接">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.examUrl }}</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="开始时间">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.startDate | formatDatetime }}</label>
                  <!-- <el-date-picker
                  v-model="assessmentForm.examinationsDto.startDate"
                  :disabled="true"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="时长(分钟)">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.timeLong }}分钟</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item v-if="!isBatchAdd" label="考试人数">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.studentCount }}人</label>
                </el-form-item>
                <el-form-item v-if="isBatchAdd" label="考试人数">
                  <label class="infoLabel">{{ selectUsers.length }}人</label>
                </el-form-item>
              </el-col>
              <el-col v-if="assessmentForm.examinationsDto.examinationMode !== 2" :span="12">
                <el-form-item label="进入考试限制">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.startLimitTime }}分钟</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="assessmentForm.examinationsDto.examinationMode !== 2">
              <el-col :span="12">
                <el-form-item label="交卷时间限制">
                  <label class="infoLabel">{{ assessmentForm.examinationsDto.submitLimitTime }}分钟</label>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="题目顺序">
                  <el-checkbox
                    v-model="assessmentForm.examinationsDto.isDisorder"
                    :disabled="true"
                    :true-label="1"
                    :false-label="0"
                  >随机
                  </el-checkbox>
                  <!-- <el-input v-model="assessmentForm.questionOrder ? '是' : '否'" /> -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="立即发布">
                  <el-checkbox v-model="assessmentForm.examinationsDto.isPublish" :true-label="1" :false-label="0">是
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="step_button">
          <el-button v-if="activeStep !== 0" round style="margin-top: 12px;" @click="stepPrev()">上一步</el-button>
          <el-button
            v-if="activeStep !== 3"
            :loading="stepNextLoading"
            round
            type="primary"
            style="margin-top: 12px;"
            @click.native.prevent="stepNext(0)"
          >下一步
          </el-button>
          <el-button
            v-if="activeStep === 3"
            :loading="doneLoading"
            round
            type="primary"
            style="margin-top: 12px;"
            @click="stepNext(1)"
          >完成
          </el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-if="examPaperPreviewDialog"
      class="examPaperPreviewDialog"
      title="试卷预览"
      :visible.sync="examPaperPreviewDialog"
      top="5vh"
      width="950px"
    >
      <div class="title">
        <span>{{ examPaperDetail.title }}</span>
        <span class="totalscore"> ( 共 {{ examPaperDetail.count }} 题 ，{{ examPaperDetail.totalScore }}分)</span>
      </div>
      <div v-loading="examPaperPreviewLoading">
        <div v-for="(item, index) in examPaperDetail.questionList" :key="item.id">
          <exam-preview :order="index + 1" :data="item" />
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button round size="medium" @click="examPaperPreviewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'

import Pagination from '@/components/Pagination'
import ChooseClass from '@/components/ChooseClass'
import ExamPreview from '@/components/ExamPreview'
import {
  parseTimeDate
} from '@/utils'
import permission from '@/directive/permission/index'
import {
  getExamPaperList,
  createExamPaperWithStudent,
  editExamPaperWithStudent,
  examList,
  examDetailInfo,
  examPublish,
  deleteExamination,
  examQuestions
} from '@/api/examPaper'
import ChooseUser from '@/components/ChooseUser/choose.vue'
import {
  examJoinUser,
  setContinueUser
} from '@/api/examniationUser'
import {
  Message
} from 'element-ui'
import {
  loadNodes,
  getAllStudents,
  classesUsers
} from '@/api/user'

export default {
  name: 'Examination',
  components: {
    Pagination,
    ExamPreview,
    ChooseUser,
    ChooseClass
  },
  directives: {
    permission
  },
  data() {
    var checkTimeLong = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('考试时长不能为空'))
      }
      setTimeout(() => {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          callback()
        }
      }, 1000)
    }
    var startDateChecked = (rule, value, callback) => {
      if (!value) {
        callback(new Error('开始时间不能为空'))
      } else if (moment(value).isBefore(moment(), 'minutes', '[]')) {
        callback(new Error('开始时间不能小于当前时间'))
      } else {
        callback()
      }
    }
    var endDateChecked = (rule, value, callback) => {
      if (!value) {
        callback(new Error('结束时间不能为空'))
      } else if (moment(value).isAfter(moment(this.assessmentForm.examinationsDto.startDate).add(this.assessmentForm
        .examinationsDto.timeLong - 1, 'minutes'), 'minutes', '[]')) {
        callback()
      } else {
        callback(new Error('结束时间不能小于开始时间加考试时长'))
      }
    }
    return {
      pickerOptionsBegin: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      limitTimeLong: 24 * 60,
      // 考核列表数据源
      list: [],
      // 列表加载转圈圈
      listLoading: false,
      // 试卷选择列表转圈圈
      examPaperLoading: false,
      // 编辑转圈圈
      editLoading: false,
      // 点击完成转圈圈
      doneLoading: false,
      // 学生续考转圈圈
      continueLoading: false,
      // 发布按钮点击Loading
      publishLoading: false,
      // 删除按钮loading
      deleteLoading: false,
      // 下一步按钮转圈圈
      stepNextLoading: false,
      // 学生选择续考重考Dialoig标题
      studentDialogTitle: '',
      // 学生选择续考重考Dialoig
      studentDialog: false,

      // 添加编辑考核Dialog
      assessmentDialog: false,
      // 添加编辑考核Dialog标题
      assessmentDialogTitle: '添加考核',
      // 编辑考核的id
      editExamId: '',
      // 是否是编辑
      isEdit: '',
      // 是否是批量添加
      isBatchAdd: false,
      // 状态 全部 未开始 进行中 ...
      active: '0',
      // step条当前选择
      activeStep: 0,
      // 考核添加编辑form
      assessmentForm: {
        examinationsDto: {
          code: '',
          name: '',
          totalScore: 0,
          examPaperCode: '',
          examPaperId: '',
          examUrl: null,
          examPaperName: '',
          startLimitTime: 0,
          submitLimitTime: 0,
          questionNumber: 0,
          startDate: null,
          endDate: null,
          timeLong: 60,
          studentCount: 0,
          isDisorder: 0,
          isPublish: 1,
          examinationMode: 0,
          allowRepeatSubmit: false,
          answerViewTiming: 0 // 0：结束后手动发布 1：提交后即可查看
        },
        examinationUserDtos: []
      },
      // 试卷列表
      examinationList: [],
      // 试卷预览信息
      examPaperDetail: {
        title: '',
        totalScore: 0,
        count: 0,
        questionList: []
      },
      // 试卷预览dialog
      examPaperPreviewDialog: false,
      // 试卷预览dialog的loading
      examPaperPreviewLoading: false,
      //  试卷分页
      examPaperListQuery: {
        Filter: '',
        ExamPaperType: 1,
        Sorting: 'creationTime desc',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1
      },
      // 考核列表 form
      listQuery: {
        Status: 0,
        Keyword: '',
        Sorting: 'creationTime desc',
        MaxResultCount: 10,
        SkipCount: 0
      },
      // 考核列表分页  总数
      totalCount: 0,
      // 考核列表分页  页数
      page: 1,

      cascaderProps: {
        label: 'displayName',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      // 专业班级数据
      orgList: [],
      // 全部用户
      allStudent: [],
      selectUsers: [],
      // 参加考试的学生列表总数据
      examJoinStudents: [],
      // 筛选参加考试学生数据
      examFilterJoinStudents: [],
      // 续考学生
      continueStudents: [],
      // 续考学生选择 学生名字
      continueStudentName: [],
      // 续考考核id
      continueExaminationId: '',
      // 参加考试学生列表数据源
      examJoinStudentList: [],
      // 参加考试学生分页
      examJoinListQuery: {
        totalCount: 0,
        SkipCount: 0,
        Keyword: '',
        page: 1,
        MaxResultCount: 10
      },
      // 续考参加学生分页loading
      examJoinListLoading: false,
      // 全部组织架构
      allOrg: [],
      // 选择的试卷
      generatingPaperSelect: [],
      // 表单验证
      assessmentRules: {
        name: [{
          required: true,
          message: '请输入考核标题',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        startDate: [{
          required: true,
          validator: startDateChecked,
          trigger: 'blur'
        }],

        endDate: [{
          required: false,
          validator: endDateChecked,
          trigger: 'blur'
        }],
        timeLong: [{
          required: true,
          validator: checkTimeLong,
          trigger: 'blur'
        }]

      },

      examStudent: [],
      // 选择班级
      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],
      tableKey: 0,
      userLoading: false

    }
  },
  mounted() {
    this.getList()
    this.loadClassList()
    // this.getExamPaper()
  },
  methods: {
    examList(args) {
      return examList(args)
    },
    // 获取考核列表
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.page - 1) * this.listQuery.MaxResultCount
      examList(this.listQuery).then(res => {
        this.list = res.items
        this.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    // 获取试卷列表
    getExamPaper() {
      this.examPaperLoading = true
      this.examPaperListQuery.SkipCount = (this.examPaperListQuery.page - 1) * this.examPaperListQuery
        .MaxResultCount
      getExamPaperList(this.examPaperListQuery).then(res => {
        this.examinationList = res.items
        this.examPaperListQuery.totalCount = res.totalCount
        this.examPaperLoading = false
        if (this.assessmentForm.examinationsDto.examPaperId) {
          this.examinationList.forEach(item => {
            if (item.id === this.assessmentForm.examinationsDto.examPaperId) {
              this.$refs.examination.toggleRowSelection(item, true)
            }
          })
        }
      }).catch(() => {
        this.examPaperLoading = false
      })
    },
    // 试卷分页检索
    examPaperSearchHandle() {
      this.examPaperListQuery.page = 1
      this.getExamPaper()
    },
    // 试卷搜索清空
    examPaperSearchChange(val) {
      if (val === '' || val.length === 0) {
        this.examPaperListQuery.page = 1
        this.getExamPaper()
      }
    },
    // 获取考核详情信息
    async getExamDetailInfo(id) {
      await examDetailInfo(id).then(res => {
        this.assessmentForm = JSON.parse(JSON.stringify(res))
        this.assessmentForm.examinationsDto.startDate = parseTimeDate(res.examinationsDto.startDate)
        this.assessmentForm.examinationsDto.endDate = parseTimeDate(res.examinationsDto.endDate)
      })
      await this.getExamJoinStudents(id)
      // 获取选中的学生和未选中学生
      // await this.allStudent.forEach(item => {
      //   this.examJoinStudents.forEach(joinItem => {
      //     if (joinItem.userId === item.id) {
      //       this.selectUsers.push(item)
      //     }
      //   })
      // })
      this.selectUsers = this.examJoinStudents
      // 获取选中的试卷
      this.generatingPaperSelect = this.examinationList.filter(item => {
        return item.id === this.assessmentForm.examinationsDto.examPaperId
      })
      this.assessmentDialog = true
      this.editLoading = false
    },

    async getExamJoinStudents(id) {
      this.examJoinListLoading = true
      this.examJoinStudents = []
      await examJoinUser(id).then(res => {
        res.items.forEach(item => {
          this.examJoinStudents.push({
            id: item.userId,
            userId: item.userId,
            classId: item.classId,
            className: item.className,
            indentityCode: item.indentityCode,
            name: item.name,
            userName: item.userName,
            studentIDNumber: item.studentIDNumber,
            studentNumber: item.studentNumber,
            submitTimes: item.submitTimes ? item.submitTimes : 0
          })
        })
        this.getPageData()
        this.examJoinListLoading = false
      }).catch(() => {
        this.examJoinListLoading = false
      })
    },
    // 获取学生分页数据
    getPageData() {
      this.examJoinListLoading = true
      this.examJoinListQuery.SkipCount = (this.examJoinListQuery.page - 1) * this.examJoinListQuery
        .MaxResultCount
      if (this.examJoinListQuery.Keyword) {
        var reg = RegExp(this.examJoinListQuery.Keyword)
        this.examFilterJoinStudents = this.examJoinStudents.filter(item => {
          return item.userName?.match(reg) || item.name?.match(reg) || item.studentNumber?.match(reg) ||
            item.className?.match(reg)
        })
      } else {
        this.examFilterJoinStudents = this.examJoinStudents
      }
      this.examJoinListQuery.totalCount = this.examFilterJoinStudents.length
      this.examJoinStudentList = this.examFilterJoinStudents.slice(this.examJoinListQuery.SkipCount, this
        .examJoinListQuery.SkipCount + this.examJoinListQuery.MaxResultCount)
      this.examJoinListLoading = false
    },
    // 续考学生检索
    examJoinStuSearchHandle() {
      this.examJoinListQuery.page = 1
      this.getPageData()
    },

    // 续考学生检索内容变化
    examJoinStudentSearchChange(val) {
      if (val === '' || val.length === 0) {
        this.examJoinListQuery.page = 1
        this.getPageData()
      }
    },
    handleChange() {

    },
    // 获取班级专业列表
    loadClassList() {
      this.userLoading = true
      loadNodes().then(res => {
        this.allOrg = res.items
        this.userLoading = false
        // this.initClassList()
        // getAllStudents().then(response => {
        //   this.sourceStudents = response.items
        //   this.allStudent = response.items
        //   this.userLoading = false
        // })
      })
    },
    chooseStudents(val) {
      this.selectUsers = val
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var newArr = []
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        newArr = newArr.concat(res.items)
      }
      this.selectUsers = this.selectUsers.concat(newArr)
      var setA = new Set()
      this.selectUsers = this.selectUsers.filter(item => {
        const result = setA.has(item.id)
        setA.add(item.id)
        return !result
      })
      this.tableKey = Math.random()
      this.assessmentForm.examinationsDto.studentCount = this.selectUsers.length
      var tmp = []
      this.selectUsers.forEach(item => {
        tmp.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
          className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
          indentityCode: item.indentityCode,
          name: item.name,
          userName: item.userName,
          studentIDNumber: item.studentIDNumber,
          studentNumber: item.studentNumber
        })
      })
      this.classLoading = false
      this.assessmentForm.examinationUserDtos = tmp
      this.chooseClassDialog = false
    },
    // 状态变化 全部 未开始  进行中 ....
    checkStatusChange() {
      this.page = 1
      this.listQuery.Status = this.active
      this.getList()
    },
    // 搜索
    searchHandle() {
      this.listQuery.page = 1
      this.getList()
    },
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    // 列表蓝字点击
    handleCheck(row) {
      this.$router.push({
        name: 'AchievementDetail',
        query: {
          'examid': row.id,
          'examTitle': row.name,
          'status': row.status
        }
      })
    },
    // 添加 编辑
    handleEdit(t, row) {
      this.restForm()
      if (this.$refs.examinationInfo) {
        this.$refs.examinationInfo.clearValidate()
      }
      // 清空试卷选择
      this.generatingPaperSelect = []
      this.selectUsers = []
      if (this.$refs.examination) {
        this.$refs.examination.clearSelection()
      }
      // 清除筛选状态

      this.examPaperListQuery.Filter = ''
      this.examPaperListQuery.page = 1
      if (t === 0) {
        this.isBatchAdd = false
        this.activeStep = 0
        this.isEdit = false
        this.assessmentDialogTitle = '添加考核'
        this.generatingPaperSelect = []
        var code = this.getAssessmentCode()

        // this.getUnsellectStudent()
        // this.getsellectStudent()
        this.assessmentForm.examinationsDto.code = code
        this.assessmentDialog = true
        this.getExamPaper()
      } else if (t === 1) {
        this.isBatchAdd = false
        this.editLoading = true
        this.activeStep = 0
        this.isEdit = true
        this.editExamId = row.id
        this.assessmentDialogTitle = '编辑考核'
        this.getExamDetailInfo(row.id)
        this.getExamPaper()
      } else if (t === 2) {
        this.isBatchAdd = false
        this.studentDialogTitle = '指定学生续考'
        this.continueStudentName = []
        this.examJoinStudentList = []
        this.continueExaminationId = row.id
        this.studentDialog = true
        this.getExamJoinStudents(row.id)
      } else if (t === 3) {
        this.isBatchAdd = false
        this.studentDialogTitle = '指定学生重考'
        this.studentDialog = true
      } else if (t === 4) {
        this.isBatchAdd = true
        this.activeStep = 0
        this.isEdit = false
        this.assessmentDialogTitle = '批量添加考核'
        this.generatingPaperSelect = []
        // var code = this.getAssessmentCode()

        // this.getUnsellectStudent()
        // this.getsellectStudent()
        this.getExamPaper()
        // this.assessmentForm.examinationsDto.code = code
        this.assessmentDialog = true
      }
    },
    // 发布
    handlePublish(row) {
      this.$confirm('是否确定' + (row.isPublish === 0 ? '发布' : '取消发布') + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.publishLoading = true
        examPublish(row.id).then(res => {
          this.$notify({
            title: '成功',
            message: '发布成功',
            type: 'success',
            duration: 2000
          })
          this.publishLoading = false
          this.getList()
        }).catch(() => {
          this.$notify({
            title: '失败',
            message: '发布失败',
            type: 'error',
            duration: 2000
          })
          this.publishLoading = false
        })
      }).catch(() => {

      })
    },
    // 选择学生续考 重考 确定
    continueBtn() {
      this.continueLoading = true
      var userIds = []
      if (!this.continueStudents || this.continueStudents.length === 0) {
        Message.warning('请选择续考学生')
        return
      }
      this.continueStudents.forEach(item => {
        userIds.push(item.userId)
      })
      var form = {
        examinationId: this.continueExaminationId,
        userIds: userIds
      }
      setContinueUser(form).then(res => {
        this.continueLoading = false
        this.studentDialog = false
        this.$notify({
          title: '成功',
          message: '设置成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.continueLoading = false
      })
    },
    // 删除考核
    handleDelete(row) {
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteLoading = true
        deleteExamination(row.id).then(res => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.deleteLoading = false
          this.getList()
        }).catch(() => {
          this.$notify({
            title: '失败',
            message: '删除失败',
            type: 'error',
            duration: 2000
          })
          this.deleteLoading = false
        })
      }).catch(() => {

      })
    },
    // step 下一步
    async stepNext() {
      this.stepNextLoading = true
      switch (this.activeStep) {
        case 0:
          this.$refs.examinationInfo.validate((valid) => {
            if (valid) {
              this.activeStep++
              this.stepNextLoading = false
            } else {
              this.stepNextLoading = false
              return false
            }
          })
          break
        case 1:
          // this.studentLoading = true
          this.$nextTick(() => {
            if (this.generatingPaperSelect.length > 0) {
              if (!this.isBatchAdd) {
                this.$refs.examination.toggleRowSelection(this.generatingPaperSelect[0], true)
              }
            }
          })

          if (this.selectUsers.length > 0) {
            if (this.activeStep++ > 2) {
              this.assessmentDialog = false
              this.activeStep = 0
            }
            // 是否是批量添加
            if (this.isBatchAdd) {
              // 选择的学生按班级进行筛选
              this.examStudentSetting()
            } else {
              this.assessmentForm.examinationsDto.studentCount = this.selectUsers.length
              var tmp = []
              this.selectUsers.forEach(item => {
                tmp.push({
                  userId: item.userId ? item.userId : item.id,
                  classId: item.extraProperties ? item.extraProperties.OUId : item.classId,
                  className: item.extraProperties ? item.extraProperties.OUName : item.className,
                  indentityCode: item.indentityCode,
                  studentNumber: item.studentNumber,
                  studentIDNumber: item.studentIDNumber
                })
              })
              this.assessmentForm.examinationUserDtos = tmp
            }
          } else {
            Message.warning('请选择学生')
          }
          this.stepNextLoading = false
          break
        case 2:
          if (this.assessmentForm.examinationsDto.examinationMode !== 2) {
            if (this.generatingPaperSelect !== null && this.generatingPaperSelect.length > 0) {
              if (this.activeStep++ > 2) {
                this.assessmentDialog = false
                this.activeStep = 0
              }
              if (this.isBatchAdd) {
                this.examPaperSetting()
              } else {
                this.assessmentForm.examinationsDto.examPaperName = this.generatingPaperSelect[0].name
                this.assessmentForm.examinationsDto.examPaperCode = this.generatingPaperSelect[0].code
                this.assessmentForm.examinationsDto.totalScore = this.generatingPaperSelect[0].totalScore
                this.assessmentForm.examinationsDto.examPaperId = this.generatingPaperSelect[0].id
                this.assessmentForm.examinationsDto.questionNumber = this.generatingPaperSelect[0].questionNumber
              }
            } else {
              Message.warning('请选择试卷')
            }
          } else {
            if (!this.assessmentForm.examinationsDto.examUrl?.length) {
              Message.warning('请填写试卷外部链接')
            } else {
              if (this.activeStep++ > 2) {
                this.assessmentDialog = false
                this.activeStep = 0
              }
            }
          }

          this.stepNextLoading = false
          break
        case 3:
          // this.$refs.assessmentForm.validate((valid) => {
          // if (valid) {
          this.doneLoading = true
          if (this.assessmentForm.examinationsDto.examinationMode === 1) {
            this.assessmentForm.examinationsDto.allowRepeatSubmit = true
            this.assessmentForm.examinationsDto.answerViewTiming = 1
          }
          if (this.assessmentForm.examinationsDto.examinationMode === 0) {
            this.assessmentForm.examinationsDto.allowRepeatSubmit = false
            this.assessmentForm.examinationsDto.answerViewTiming = 0
          }
          if (this.isEdit) {
            editExamPaperWithStudent(this.editExamId, this.assessmentForm).then(res => {
              if (this.activeStep++ > 2) {
                this.assessmentDialog = false
                this.activeStep = 0
                this.getList()
              }
              this.stepNextLoading = false
              this.doneLoading = false
            }).catch(() => {
              this.stepNextLoading = false
              this.doneLoading = false
            })
          } else {
            if (this.isBatchAdd) {
              var name = this.assessmentForm.examinationsDto.name
              this.generatingPaperSelect.forEach(async(item, index) => {
                this.assessmentForm.examinationsDto.code = this.getAssessmentCode()
                this.assessmentForm.examinationsDto.name = name + '_' + (index + 1)
                this.assessmentForm.examinationsDto.totalScore = item.totalScore
                this.assessmentForm.examinationsDto.examPaperId = item.id
                this.assessmentForm.examinationsDto.examPaperCode = item.examPaperCode
                this.assessmentForm.examinationsDto.examPaperName = item.name
                this.assessmentForm.examinationsDto.questionNumber = item.questionNumber
                this.assessmentForm.examinationsDto.studentCount = item.exam ? item.exam.length : 0
                this.assessmentForm.examinationUserDtos = item.exam
                await createExamPaperWithStudent(this.assessmentForm).then(res => {
                  this.stepNextLoading = false
                  this.doneLoading = false
                  if (this.activeStep++ > 2) {
                    this.assessmentDialog = false
                    this.activeStep = 0
                    this.getList()
                  }
                }).catch(() => {
                  this.stepNextLoading = false
                  this.doneLoading = false
                })
              })
              this.getList()
            } else {
              createExamPaperWithStudent(this.assessmentForm).then(res => {
                this.stepNextLoading = false
                this.doneLoading = false
                if (this.activeStep++ > 2) {
                  this.assessmentDialog = false
                  this.activeStep = 0
                  this.getList()
                }
              }).catch(() => {
                this.stepNextLoading = false
                this.doneLoading = false
              })
            }
          }

          break
        default:
          break
      }
    },
    stepPrev() {
      this.activeStep--
      switch (this.activeStep) {
        case 1:
          this.stepNextLoading = false
          break
        case 2:
          this.$nextTick(() => {
            if (this.generatingPaperSelect) {
              if (!this.isBatchAdd) {
                this.$refs.examination.toggleRowSelection(this.generatingPaperSelect[0], true)
              } else {
                this.generatingPaperSelect.forEach(item => {
                  this.$refs.examination.toggleRowSelection(item, true)
                })
              }
            }
          })
          this.stepNextLoading = false
          break
        case 3:
          this.stepNextLoading = false
          break
        default:
          break
      }
    },
    // 选择续考重考学生变化
    handleContinueStuChange(val) {
      this.continueStudents = val
      this.continueStudentName = []
      this.continueStudents.forEach((item, index) => {
        this.continueStudentName.push(item.name)
      })
    },
    // 试卷选择变化
    handleExaminationChange(val) {
      if (this.isBatchAdd) {
        this.generatingPaperSelect = val
      } else {
        if (val.length > 1) {
          this.$refs.examination.clearSelection()
          this.$refs.examination.toggleRowSelection(val.pop())
        } else {
          this.generatingPaperSelect = val
          if (this.generatingPaperSelect.length > 0) {
            this.assessmentForm.examinationsDto.examPaperName = this.generatingPaperSelect[0].name
          } else {
            this.assessmentForm.examinationsDto.examPaperName = ''
          }
        }
      }
    },
    // 表格当前行发生变化(没啥用)
    handleExaminationCurrentChange(val) {
      // this.$refs.examination.toggleRowSelection(val)
    },
    // row-key  配合type为selection的el-table-column上reserve-selection实现回显
    getRowKeys(row) {
      return row.id
    },
    //
    // getStudentKeys(row) {
    //   return row.userId
    // },
    // 页数变化 清空之前选择的学生
    clearSelectStudent() {
      this.continueStudents = []
    },
    // 点击试卷列表某行
    handleExaminationRowClick(row) {
      if (this.isBatchAdd) {
        if (this.generatingPaperSelect.length === 0) {
          this.generatingPaperSelect.push(row)
          this.$refs.examination.toggleRowSelection(row, true)
        } else {
          var index = this.generatingPaperSelect.findIndex(item => item === row)
          if (index > -1) {
            this.generatingPaperSelect.splice(index, 1)
            this.$refs.examination.toggleRowSelection(row, false)
          } else {
            this.generatingPaperSelect.push(row)
            this.$refs.examination.toggleRowSelection(row, true)
          }
        }
      } else {
        this.generatingPaperSelect = []
        this.$refs.examination.clearSelection()
        this.$refs.examination.toggleRowSelection(row)
        this.generatingPaperSelect.push(row)
        this.assessmentForm.examinationsDto.examPaperName = row.name
      }
    },
    // 根据时间生成随机码 年月日时分秒2位随机数
    getAssessmentCode() {
      const now = new Date()
      const year = now.getFullYear()
      let month = now.getMonth() + 1
      let day = now.getDate()
      let hour = now.getHours()
      let minutes = now.getMinutes()
      let seconds = now.getSeconds()
      String(month).length < 2 ? (month = String('0' + month)) : month
      String(day).length < 2 ? (day = String('0' + day)) : day
      String(hour).length < 2 ? (hour = String('0' + hour)) : hour
      String(minutes).length < 2 ? (minutes = String('0' + minutes)) : minutes
      String(seconds).length < 2 ? (seconds = String('0' + seconds)) : seconds
      const yyyyMMddHHmmss = `${year}${month}${day}${hour}${minutes}${seconds}`
      return yyyyMMddHHmmss + Math.random().toString().substr(2, 2)
    },
    // 考试模式变化
    examModelChange(val) {
      if (this.assessmentForm.examinationsDto.examinationMode === 0) {
        if (this.assessmentForm.examinationsDto.startDate) {
          this.startDateChange()
        }
      }
      this.assessmentForm.examinationsDto.startLimitTime = 0
      this.assessmentForm.examinationsDto.submitLimitTime = 0
    },
    // 考试时长限制 不能超过当天
    startDateChange(val) {
      if (!this.assessmentForm.examinationsDto.startDate) {
        this.$set(this.assessmentForm.examinationsDto, 'endDate', '')
        return
      }
      var tempDate = new Date(this.assessmentForm.examinationsDto.startDate)
      this.limitTimeLong = (24 - tempDate.getHours()) * 60 - tempDate.getMinutes()
      if (this.assessmentForm.examinationsDto.timeLong > this.limitTimeLong && !this.assessmentForm.examinationsDto
        .examinationMode) {
        this.$set(this.assessmentForm.examinationsDto, 'timeLong', this.limitTimeLong)
      }
      if (this.assessmentForm.examinationsDto.examinationMode === 0) {
        var _endDate = moment(this.assessmentForm.examinationsDto.startDate).add(this.assessmentForm.examinationsDto
          .timeLong, 'm')
        this.$set(this.assessmentForm.examinationsDto, 'endDate', parseTimeDate(_endDate.format('YYYY-MM-DD HH:mm')))
      }
    },
    timeLongChange(val) {
      if (this.assessmentForm.examinationsDto.examinationMode === 0) {
        var _endDate = moment(this.assessmentForm.examinationsDto.startDate).add(this.assessmentForm.examinationsDto
          .timeLong, 'm')
        this.$set(this.assessmentForm.examinationsDto, 'endDate', parseTimeDate(_endDate.format('YYYY-MM-DD HH:mm')))
      }
    },
    // 添加Dialog将要关闭提示
    assessmentBeforeClose() {
      // this.$confirm('关闭将清除已经添加的信息,是否确定关闭?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   this.assessmentForm = Object.assign({}, defaultAssessmentForm)
      //   this.allStudents = this.sourceStudents
      //   this.chooseStudent = []
      //   this.selectedStus = []
      //   this.unselectedStus = []
      //   this.assessmentDialog = false
      // }).catch(() => {

      // })
    },
    // 列表排序
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    // 试卷预览
    async handlePreview(row) {
      this.examPaperDetail = {}
      this.examPaperPreviewLoading = true
      this.examPaperPreviewDialog = true
      this.examPaperDetail.title = row.name
      this.examPaperDetail.totalScore = row.totalScore
      await examQuestions(row.id).then(res => {
        this.examPaperPreviewLoading = false
        this.examPaperDetail.count = res.length
        this.examPaperDetail.questionList = res
      })
    },
    changePage(t) {
      if (t === 0) {
        // 添加
        if (this.unselListQuery.page > 1) {
          this.unselListQuery.page -= 1
        } else {
          this.unselListQuery.page = 1
        }
      } else {
        if (this.selListQuery.page > 1) {
          this.selListQuery.page -= 1
        } else {
          this.selListQuery.page = 1
        }
      }
    },
    checkSelectable(row) {
      // return true
      return !(row.submitTimes > 0)
    },
    restForm() {
      this.assessmentForm = {
        examinationsDto: {
          code: '',
          name: '',
          totalScore: 0,
          examPaperCode: '',
          examPaperId: '',
          examPaperName: '',
          startLimitTime: 0,
          submitLimitTime: 0,
          questionNumber: 0,
          startDate: null,
          timeLong: 60,
          studentCount: 0,
          isDisorder: 0,
          isPublish: 1,
          examinationMode: 0,
          allowRepeatSubmit: false,
          answerViewTiming: 0
        },
        examinationUserDtos: []
      }
    },
    // 为学生分配试卷 同班不同卷
    examStudentSetting() {
      this.examStudent = this.selectUsers.reduce((prev, cur, index, arr) => {
        const classId = cur.extraProperties.OUId
        // 判断有无id，无id的存放新的地址
        if (classId) {
          // 判断有没有这个属性，如果有就push，没有就创建
          // Object.prototype.hasOwnProperty.call(prev, classId)
          if (Object.prototype.hasOwnProperty.call(prev, classId)) {
            prev[classId].push(cur)
          } else {
            prev[classId] = [cur]
          }
        } else {
          prev['nocategory'].push(cur)
        }
        return prev
      }, {
        nocategory: []
      })
    },
    examPaperSetting() {
      this.generatingPaperSelect.forEach(item => {
        item.exam = []
      })
      var classList = Object.keys(this.examStudent)
      for (var k = 0; k < this.generatingPaperSelect.length; k++) {
        for (var i = 0; i < classList.length; i++) {
          var studentItem = this.examStudent[classList[i]]
          for (var j = 0; j < studentItem.length; j++) {
            if (k === j % this.generatingPaperSelect.length) {
              this.generatingPaperSelect[k]['exam'].push({
                userId: studentItem[j].id,
                classId: studentItem[j].extraProperties.OUId,
                className: studentItem[j].extraProperties.OUName,
                indentityCode: studentItem[j].indentityCode,
                studentNumber: studentItem[j].studentNumber,
                studentIDNumber: studentItem[j].studentIDNumber
              })
            }
          }
        }
      }
    },
    handleOpenUrl(row) {
      window.open(row.examUrl + '?sojumpparm=admin', '_blank')
    }

  }
}

</script>
<style scoped>
.assessment {
  margin-top: 20px;
  height: 544px;
}

/* .test_form {
    width: 50%;
  } */

.step_button {
  padding-top: 12px;
  text-align: center;
  /* position: absolute;
    bottom: 20px;
    right: 20px; */
}

.mini_pagination {
  padding: 0 !important;
}

.assessmentDialog ::v-deep .el-dialog__body {
  padding: 0 15px 15px 15px;
}

.el-input ::v-deep .num_input {
  width: 70px;
}

.num_input ::v-deep .el-input {
  display: inline-block;
  width: 70px;
}

.num_input ::v-deep .el-input__inner {
  width: 70px;
}

.form_alert_info {
  display: block;
  font-size: 13px;
}

.small_input ::v-deep .el-input__inner {
  width: 220px;
}

.infoLabel {
  color: #606266;
  font: 400 11px system-ui;
}

.infoLabelMoreWidth {
  color: #606266;
  font: 400 11px system-ui;
  letter-spacing: normal;
  word-spacing: normal;
  text-indent: 0px;
}

.choice_stu {
  line-height: 35px;
}

.choice_stu span {
  font-size: 14px;
  color: #909399;
  font-weight: bold;
}

.choice_stu .s_name {
  color: #606266;
  font-size: 14px;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 5px 10px;
  border-radius: 30px;
  border: 1px solid #ddd;
  display: inline-block;
  line-height: 21px;

}

.examPaperPreviewDialog ::v-deep .el-dialog__body {
  padding: 30px 40px;
}

.examPaperPreviewDialog .title {
  text-align: center;
  height: 50px;
  font-size: 16px;
  font-weight: bold;
}

.examPaperPreviewDialog .totalscore {
  font-size: 14px;
  font-weight: initial;
}

.table_span {
  display: inline-block;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

}

.student_box_card ::v-deep .cell {
  height: 20px;
}
</style>

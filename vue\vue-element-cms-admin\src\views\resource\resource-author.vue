<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="header_flex_box">
        <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
        <el-button v-permission="['CourseManagement.KnowledgeResources']" round size="small" type="success" icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        <el-button v-permission="['CourseManagement.KnowledgeResources.Update']" round size="small" type="primary" icon="el-icon-plus" @click="handleResourceAuthorEdit(0,0)">添加</el-button>
        <!-- <export-excel
          :header="['头像', '名称', '创建时间']"
          :filter-val="['profilePhoto', 'name', 'creationTime']"
          :field="{ 2: [2] }"
          :api-fn="resourceAuthorList"
        /> -->
      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" highlight-current-row @sort-change="sortChange">
        <el-table-column label="序号" prop="sort" sortable="sort" width="100" />
        <el-table-column label="头像" sortable="profilePhoto">
          <template slot-scope="{ row }">
            <el-image :src="row.profilePhoto" class="resource-cover" fit="cover" style="height: 100px;width:160px;">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name" sortable="name" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{row}">
            <el-button v-permission="['CourseManagement.KnowledgeResources.Update']" round type="primary" icon="el-icon-edit" size="mini" @click="handleResourceAuthorEdit(1,row)">编辑
            </el-button>
            <el-button v-permission="['CourseManagement.KnowledgeResources.Delete']" round type="danger" icon="el-icon-delete" size="mini" @click="handleResourceAuthorDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getResourceAuthorList"
      />
    </el-card>
    <el-dialog :title="dialogTitle" :close-on-click-modal="false" :visible.sync="resourceAuthorDialog" width="800px">
      <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
        <el-form-item label="作者图片" prop="profilePhoto">
          <lz-upload-images
            v-if="resourceAuthorDialog"
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="uplaodFileType"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" />
        </el-form-item>
        <el-form-item label="介绍" prop="introduce">
          <!-- <tinymce v-model="form.introduce" :height="300" :width="500" /> -->
          <el-input v-model="form.introduce" type="textarea" />
        </el-form-item>
        <el-form-item label="详情" prop="detail">
          <tinymce v-model="form.detail" :height="400" :width="600" />

        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" :loading="dialogLoading" @click="resourceAuthorDialogSure">确 定</el-button>
        <el-button round @click="resourceAuthorDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  resourceAuthorList,
  addResourceAuthor,
  editResourceAuthor,
  deletesResourceAuthor
} from '@/api/resource'
import Pagination from '@/components/Pagination'
import LzUploadImages from '@/components/LzUploadImages'
import permission from '@/directive/permission'
import tinymce from '@/components/Tinymce'
export default {
  name: 'ResourceAuthor',
  directives: {
    permission
  },
  components: {
    Pagination,
    LzUploadImages,
    tinymce
  },
  data() {
    var checkProfilePhotoUrl = (rule, value, callback) => {
      if (this.form.profilePhoto === '') {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'CreationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 编辑
      isEdit: false,

      // 上传图片类型
      uplaodFileType: [],
      // 上传图片列表
      previewFileList: [],
      // dialog
      dialogTitle: '添加',
      resourceAuthorDialog: false,
      dialogLoading: false,
      // form
      form: {
        name: '',
        profilePhoto: '',
        sort: 0,
        introduce: '',
        detail: ''
      },
      formRules: {
        name: [{
          required: true,
          message: '请输入作者名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 200,
          message: '长度在 1 到 200 个字符',
          trigger: 'blur'
        }
        ],
        profilePhoto: [{
          required: true,
          validator: checkProfilePhotoUrl,
          trigger: 'blur'
        }]
      }

    }
  },
  created() {
    this.getResourceAuthorList()
  },
  methods: {
    resourceAuthorList(args) {
      return resourceAuthorList(args)
    },
    // 上传文件成功回调
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.profilePhoto = url
    },
    // 上传文件删除
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.profilePhoto = ''
    },
    // 编辑添加点击
    handleResourceAuthorEdit(t, row) {
      this.form = {
        name: '',
        profilePhoto: '',
        sort: 0,
        detail: '',
        introduce: ''
      }
      this.previewFileList = []
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      if (t === 0) {
        this.dialogTitle = '添加'
        this.isEdit = false
      } else {
        this.dialogTitle = '编辑'
        this.isEdit = true
        this.form.profilePhoto = row.profilePhoto
        this.form.name = row.name
        this.form.sort = row.sort
        this.form.introduce = row.introduce
        this.form.detail = row.detail
        this.form.id = row.id
        this.previewFileList.push({
          name: row.profilePhoto,
          url: row.profilePhoto
        })
      }
      this.resourceAuthorDialog = true
    },
    // 删除
    handleResourceAuthorDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletesResourceAuthor(row.id).then(res => {
          this.$message.success('删除成功')
          this.getResourceAuthorList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 编辑添加确定
    resourceAuthorDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.isEdit) {
            editResourceAuthor(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.dialogLoading = false
              this.resourceAuthorDialog = false
              this.getResourceAuthorList()
            }).catch(() => {
              this.$message.error('编辑失败')
            })
          } else {
            addResourceAuthor(this.form).then(res => {
              this.$message.success('添加成功')
              this.dialogLoading = false
              this.resourceAuthorDialog = false
              this.getResourceAuthorList()
            }).catch(() => {
              this.$message.error('添加失败')
            })
          }
        } else {
          return false
        }
      })
    },
    handleSearch() {
      this.getResourceAuthorList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getResourceAuthorList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceAuthorList()
    },
    getResourceAuthorList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceAuthorList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}

</script>

<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container" />
    <!--表单渲染-->
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :title="formTitle"
      width="600px"
      @close="cancel()"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="封面图" prop="url">
          <lz-upload-images
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="小程序" prop="appUrl">
          <lz-upload-images
            ref="weChatImg"
            :upload-id="'weChatImg'"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="weChatImgList"
            @response-fn="handleWeChatImgResponse"
            @remove-upload="handleWeChatImgRemove"
          />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="链接类型">
          <el-radio-group v-model="form.hrefType" @change="handleHrefTypeChange">
            <el-radio :label="0">直播</el-radio>
            <!-- <el-radio :label="1">课程</el-radio>
            <el-radio :label="2">培训</el-radio> -->
            <el-radio :label="3">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="链接内容">
          <el-select
            v-model="currentHrefItem"
            :disabled="form.hrefType === 3"
            filterable
            clearable
            remote
            :remote-method="remoteMethod"
            :loading="hrefListLoading"
            placeholder="请选择"
            @change="handleHrefContentChange"
          >
            <el-option v-for="item in hrefList" :key="item.id" :label="form.hrefType === 0 ? item.title : item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接" prop="href">
          <el-input v-model="form.href" :disabled="form.hrefType !== 3" placeholder="请输入跳转链接" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="显示">
          <el-radio-group v-model="form.isShow">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="介绍" prop="introduce">
          <el-input v-model="form.introduce" type="textarea" :rows="3" placeholder="请输入介绍" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round :loading="formLoading" type="primary" @click="save">确 定</el-button>
        <el-button round @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-row :gutter="15">
      <el-card class="box-card">
        <div slot="header" class="flex_between_box">
          <span>轮播图管理</span>
          <div>
            <el-button
              v-permission="['CourseManagement.RotationCharts.Create']"
              class="filter-item"
              size="small"
              type="primary"
              icon="el-icon-plus"
              round
              @click="handleCreate"
            >新增</el-button>
            <export-excel
              :header="['封面', '标题', '链接', '排序', '是否显示']"
              :filter-val="['url', 'title', 'href', 'sort', 'isShow']"
              :field="{ 4: ['否', '是'] }"
              :api-fn="cmsRotationChartList"
            />
          </div>
        </div>

        <!--表格渲染-->
        <el-table
          ref="multipleTable"
          v-loading="listLoading"
          :data="list"
          style="width: 100%"
          highlight-current-row
          @sort-change="sortChange"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55px" align="center" />
          <el-table-column prop="url" label="封面" width="200">
            <template slot-scope="{ row }">
              <!-- <img :src="row.url" width="160px;" height="90px"/>          -->
              <el-image :src="row.url" style="width: 160px; height: 90px" fit="cover">
                <div slot="error">
                  <div class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="title" sortable="custom" label="标题" />
          <el-table-column prop="href" sortable="custom" label="链接" />
          <el-table-column prop="sort" sortable="custom" label="排序" width="80" />
          <el-table-column prop="isShow" sortable="custom" label="是否显示" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isShow == true" size="mini" type="success">是</el-tag>
              <el-tag v-else size="mini" type="info">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200px">
            <template slot-scope="{ row }">
              <el-button
                v-permission="['CourseManagement.RotationCharts.Update']"
                type="primary"
                size="mini"
                round
                icon="el-icon-edit"
                @click="handleUpdate(row)"
              >编辑</el-button>
              <el-button
                v-permission="['CourseManagement.RotationCharts.Delete']"
                type="danger"
                size="mini"
                round
                icon="el-icon-delete"
                @click="handleDelete(row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="totalCount > 0"
          :total="totalCount"
          :page.sync="page"
          :limit.sync="listQuery.MaxResultCount"
          @pagination="getList"
        />
      </el-card>
    </el-row>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import LzUploadImages from '@/components/LzUploadImages'
import {
  cmsRotationChartList,
  cmsRotationChartAdd,
  cmsRotationChartDetail,
  cmsRotationChartEdit,
  cmsRotationChartDelete,
  courseList
} from '@/api/course'
import { courseLiveList } from '@/api/live'
import { trainsList } from '@/api/train'
export default {
  name: 'WebRotationChart',
  components: {
    Pagination,
    LzUploadImages
  },
  directives: {
    permission
  },
  data() {
    var checkUrl = (rule, value, callback) => {
      if (this.form.url === '' || this.form.url === null) {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    var checkAppUrl = (rule, value, callback) => {
      if (this.form.appUrl === '' || this.form.appUrl === null) {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        title: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        }],
        url: [{
          required: true,
          validator: checkUrl,
          trigger: 'change'
        }],
        appUrl: [{
          required: true,
          validator: checkAppUrl,
          trigger: 'change'
        }],
        introduce: [{
          required: false,
          max: 300,
          message: '长度不能超过300个字符',
          trigger: 'blur'
        }]
      },
      list: null,
      totalCount: 0,
      listLoading: true,
      listQuery: {
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10
      },
      page: 1,
      dialogFormVisible: false,
      formTitle: '',
      isEdit: false,
      form: {
        href: '',
        hrefType: 0,
        hrefId: '',
        title: '',
        introduce: '',
        sort: 0,
        isShow: true,
        url: '',
        appUrl: ''
      },
      formLoading: false,
      multipleSelection: [],
      previewFileList: [],
      weChatImgList: [],

      hrefList: [],
      hrefListLoading: false,
      currentHrefItem: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    cmsRotationChartList(args) {
      return cmsRotationChartList(args)
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.hrefListLoading = true
        var tmp = null
        var data = {
          SkipCount: 0,
          MaxResultCount: 999,
          Filter: query
        }
        if (this.form.hrefType === 0) {
          tmp = await courseLiveList(data)
        } else if (this.form.hrefType === 1) {
          tmp = await courseList(data)
        } else if (this.form.hrefType === 2) {
          tmp = await trainsList(data)
        }
        this.hrefList = tmp.items
        this.hrefListLoading = false
      } else {
        this.hrefList = []
      }
    },
    handleHrefTypeChange(val) {
      this.hrefList = []
      this.currentHrefItem = null
      this.form.href = ''
    },
    handleHrefContentChange(val) {
      console.log(12313)
      if (val) {
        if (this.form.hrefType === 0) {
          this.form.href = 'bglives?zbid=' + this.currentHrefItem
        }
      }
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      cmsRotationChartList(this.listQuery).then((response) => {
        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    handleCreate() {
      this.formTitle = '新增轮播图'
      this.isEdit = false
      this.form = {
        href: '',
        hrefType: 0,
        title: '',
        introduce: '',
        sort: 0,
        isShow: true,
        url: '',
        appUrl: ''
      }
      this.previewFileList = []
      this.weChatImgList = []
      this.dialogFormVisible = true
    },
    handleDelete(row) {
      if (row) {
        this.$confirm('是否删除' + row.title + '?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            cmsRotationChartDelete(row.id).then((response) => {
              const index = this.list.indexOf(row)
              this.list.splice(index, 1)
              this.$notify({
                title: '成功',
                message: '删除成功',
                type: 'success',
                duration: 2000
              })
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        if (this.multipleSelection.length == 0) {
          this.$message({
            message: '请选择要删除的行',
            type: 'warning'
          })
          return
        }
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.multipleSelection.forEach((element) => {
              cmsRotationChartDelete(element.id).then((response) => {
                const index = this.list.indexOf(element)
                this.list.splice(index, 1)
              })
            })
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      }
    },
    handleUpdate(row) {
      this.formTitle = '修改轮播图'
      this.isEdit = true
      if (row) {
        this.fetchData(row.id)
        this.dialogFormVisible = true
      } else {
        if (this.multipleSelection.length != 1) {
          this.$message({
            message: '编辑必须选择单行',
            type: 'warning'
          })
          return
        } else {
          this.fetchData(this.multipleSelection[0].id)
          this.dialogFormVisible = true
        }
      }
    },
    async fetchData(id) {
      const response = await cmsRotationChartDetail(id)
      this.previewFileList = []
      this.weChatImgList = []
      this.form = response
      if (this.form.url && this.form.url.length) {
        this.previewFileList.push({
          url: this.form.url
        })
      }
      if (this.form.appUrl && this.form.appUrl.length) {
        this.weChatImgList.push({
          url: this.form.appUrl
        })
      }
      if (this.form.href.match(RegExp(/bglives/))) {
        this.form.hrefType = 0
      } else {
        this.form.hrefType = 3
      }
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            cmsRotationChartEdit(this.form.id, this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
              .catch(() => {
                this.formLoading = false
              })
          } else {
            cmsRotationChartAdd(this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
              .catch(() => {
                this.formLoading = false
              })
          }
        }
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.handleFilter()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleFilter()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    cancel() {
      this.dialogFormVisible = false
      this.$refs.form.clearValidate()
    },
    handleAvatarSuccess(res, file) { },

    // 上传文件成功回调
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.url = url
    },
    // 上传文件删除
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.url = ''
    },
    handleWeChatImgResponse(url, fileForm) {
      this.weChatImgList.push(fileForm)
      this.form.appUrl = url
    },
    handleWeChatImgRemove(index) {
      this.weChatImgList = []
      this.form.appUrl = ''
    }
  }
}

</script>

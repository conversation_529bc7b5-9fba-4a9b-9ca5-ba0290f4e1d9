<template>
  <div />

</template>
<script>
// import { defineComponent } from '@vue/composition-api'

// import {
//   S3Client,
//   ListObjectsCommand,
//   CreateMultipartUploadCommand,
//   ListMultipartUploadsCommand,
//   GetObjectCommand,
//   UploadPartCommand,
//   CompleteMultipartUploadCommand,
//   AbortMultipartUploadCommand
// } from '@aws-sdk/client-s3'

export default {
  name: 'Upload',
  data() {
    return {
      // s3: null
    }
  },
  mounted() {
    this.initClient()
  },
  methods: {
    async initClient() {
      // const s3 = new S3Client({
      //   endpoint: 'h', // 存储文件的服务器的地址，无端口
      //   s3ForcePathStyle: true,
      //   signatureVersion: 'v4',
      //   region: 'us-east-1',
      //   forcePathStyle: true,
      //   credentials: {
      //     accessKeyId: '', // 访问登录名
      //     secretAccessKey: '' // 访问密码
      //   }
      // })
      // const list = await s3.send(
      //   new ListObjectsCommand({ Delimiter: '/', Bucket: '' })
      // )
      // console.log(list)
    }
  }
}
</script>

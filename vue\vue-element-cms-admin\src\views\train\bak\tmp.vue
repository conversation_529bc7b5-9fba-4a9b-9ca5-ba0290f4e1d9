<template>
  <div class="app-container">
    <el-card class="box-card ">
      <div slot="header">
        <!-- <a><i class="el-icon-arrow-left"></i></a> -->
        <span>  {{ form.name }}</span>
      </div>
      <!-- <span class="title_span">基础信息</span> -->
      <el-descriptions title="基础信息" border :column="2" label-class-name="descriptions_label">
        <el-descriptions-item label="培训名称">{{ form.name }}</el-descriptions-item>
        <el-descriptions-item label="培训时间">{{ form.startDate | formatDatetime }} 至 {{ form.endDate | formatDatetime }}</el-descriptions-item>
        <el-descriptions-item label="培训课时">{{ extraProperties.ClassHour }} 课时</el-descriptions-item>
        <el-descriptions-item label="培训包">{{ form.trainPackageName }}</el-descriptions-item>
        <el-descriptions-item label="公告">{{ form.notice }}</el-descriptions-item>
      </el-descriptions>
      <div style="margin-top: 20px;position: relative">
        <el-descriptions title="学习情况" />
        <el-tabs v-model="tabActiveName">
          <el-tab-pane label="培训学员" name="user">
            <!-- <el-descriptions :title="'学员数据统计（ 共 ' + listQuery.totalCount + ' 人 ）'">
              <template slot="extra">
                <el-button round type="primary" size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
              </template>
            </el-descriptions> -->
            <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList(0)">搜索</el-button>
            <el-button round type="primary" size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
            <el-table v-loading="listLoading" :data="list" highlight-current-row>
              <el-table-column label="学员姓名" prop="name" />
              <el-table-column label="用户名" prop="userName" />
              <el-table-column label="部门" prop="className" />
              <el-table-column label="学习时长" prop="learnDuration">
                <template slot-scope="{row}">
                  <span>{{ row.learnDuration | formatSecond }}</span>
                </template>
              </el-table-column>
              <el-table-column label="学习进度" prop="courseCompleteCount">
                <template slot-scope="{row}">
                  <el-link v-if="Number(extraProperties.CourseCount)" type="primary" @click="handleViewExamCompleteDetail(row)">{{ row.courseCompleteCount }}/{{ extraProperties.CourseCount }}</el-link>
                  <span v-else>{{ row.courseCompleteCount }}/{{ extraProperties.CourseCount }}</span>
                </template>
              </el-table-column>
              <el-table-column label="考试提交情况" prop="examCompleteCount">
                <template slot-scope="{row}">
                  <el-link v-if="Number(extraProperties.ExamCount)" type="primary" @click="handleViewExamAchieve(row)">{{ row.examCompleteCount }}/{{ extraProperties.ExamCount }}</el-link>
                  <span v-else>{{ row.examCompleteCount }}/{{ extraProperties.ExamCount }}</span>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="listQuery.totalCount > 0"
              :total="listQuery.totalCount"
              :page.sync="listQuery.page"
              :limit.sync="listQuery.MaxResultCount"
              @pagination="getTrainUser"
            />
          </el-tab-pane>
          <el-tab-pane label="培训考核" name="exam">
            <el-table :data="trainExamList" highlight-current-row>
              <el-table-column label="考试名称" prop="examName" min-width="200" />
              <el-table-column label="课程名称" prop="courseName" min-width="200" />
              <el-table-column label="开始时间" prop="startTime" width="180">
                <template slot-scope="{row}">
                  <span>{{ row.startTime | formatDateTime }}</span>
                </template>
              </el-table-column>
              <el-table-column label="结束时间" prop="endTime" width="180">
                <template slot-scope="{row}">
                  <span>{{ row.endTime | formatDateTime }}</span>
                </template>
              </el-table-column>
              <el-table-column label="考核时长" prop="examTimeLong" width="150">
                <template slot-scope="{row}">
                  <span>{{ row.examTimeLong }} 分钟</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" prop="examCompleteCount" width="400">
                <template slot-scope="{row}">
                  <el-button v-if="row.examinationId !== null" round type="primary" size="mini" icon="el-icon-view" @click="handleViewExamWithUser(row)">查看</el-button>
                  <el-button v-if="row.examinationId !== null" round type="primary" size="mini" icon="el-icon-edit" @click="handleBatchViewExam(row)">批量阅卷</el-button>
                  <el-button round type="warning" size="mini" icon="el-icon-document" @click="handlePublic(row)">
                    公布成绩</el-button>
                  <el-button round type="warning" size="mini" icon="el-icon-refresh-right" @click="handleContinueExam(row)">续考</el-button>
                </template>
              </el-table-column>
            </el-table>

          </el-tab-pane>
          <el-tab-pane label="培训课程" name="course">
            <el-table :data="trainCourseList" highlight-current-row>
              <!-- <el-table-column label="课程封面" sortable="coverUrl" width="200">
                <template slot-scope="{ row }">
                  <el-image :src="row.extraProperties.CourseCoverUrl" class="course-cover" fit="cover">
                    <div slot="error">
                      <div class="image-slot">
                        <i class="el-icon-picture-outline" />
                      </div>
                    </div>
                  </el-image>
                </template>
              </el-table-column> -->
              <el-table-column label="课程名称" prop="courseName" />
              <el-table-column label="课时" prop="classHour" width="150" />
              <el-table-column label="总时长" prop="resourceDuration" width="150">
                <template slot-scope="{row}">
                  <span>{{ row.resourceDuration | formatSecond }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="来源" prop="courseSource" width="150">
                <template slot-scope="{row}">
                  <span v-if="row.courseSource === 0">景格课程</span>
                  <span v-if="row.courseSource === 1">自建课程</span>
                </template>
              </el-table-column> -->
              <el-table-column label="操作" prop="examCompleteCount" width="150">
                <template slot-scope="{row}">
                  <el-button round type="primary" size="mini" icon="el-icon-view" @click="handleViewCourseUserDetail(row)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

    </el-card>
    <el-dialog title="学习详情" :visible.sync="examCompleteDetailDialog" width="1000px">
      <el-table v-loading="examDetailListLoading" :data="examDetailList" highlight-current-row>
        <el-table-column label="课程名称" prop="courseName" />
        <el-table-column label="学习时长" prop="courseLearnDuration">
          <template slot-scope="{row}">
            <span>{{ row.courseLearnDuration | timeFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="学习进度" prop="courseLearnProgress" sortable="courseLearnProgress">
          <template slot-scope="{row}">
            {{ row.courseLearnProgress.toFixed(2) }} %
          </template>
        </el-table-column>
        <!-- <el-table-column label="视频资源学习时长" prop="courseVideoLearnDuration">
          <template slot-scope="{row}">
            <span>{{ row.courseVideoLearnDuration | timeFormat }}</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="课程资源总数" prop="courseResCount" /> -->
        <el-table-column label="学习完成资源数" prop="completeResCount" />
        <el-table-column label="最后一次学习时间" prop="lastLearnTime">
          <template slot-scope="{row}">
            <span>{{ row.lastLearnTime | formatDateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否完成" prop="isComplete">
          <template slot-scope="{row}">
            <el-tag v-if="row.isComplete" type="success">已完成</el-tag>
            <el-tag v-else type="info">未完成</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="examCompleteDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="考核详情" :visible.sync="examAchieveDetailDialog" width="1000px">
      <el-table v-loading="examAchieveDetailListLoading" :data="examAchieveDetailList" highlight-current-row>
        <el-table-column label="考核名称" prop="name" />
        <el-table-column label="开始时间" prop="startDate" width="180">
          <template slot-scope="{row}">
            <span>{{ row.startDate | formatDateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endDate" width="180">
          <template slot-scope="{row}">
            <span>{{ row.endDate | formatDateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总分" prop="totalScore" width="80">
          <template slot-scope="{row}">
            <span>{{ row.totalScore }} 分</span>
          </template>
        </el-table-column>
        <el-table-column label="得分" prop="lastScore" width="80">
          <template slot-scope="{row}">
            <span>{{ row.lastScore }} 分</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="课程资源总数" prop="courseResCount" /> -->
        <el-table-column label="提交次数" prop="submitTimes" width="80">
          <template slot-scope="{row}">
            <span>{{ row.submitTimes }} 次</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{row}">
            <el-button :disabled="!row.submitTimes" type="primary" round size="mini" icon="el-icon-view" @click="handleViewExamAchieveDetail(0,row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="examAchieveDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 学生答题详情Dialog -->
    <!-- v-if="achievementDetailDialog" -->
    <el-dialog

      class="achievementDetailDialog"
      title="学生答题详情"
      :visible.sync="achievementDetailDialog"
      top="5vh"
      width="950px"
      @close="scoreDialogClosed"
    >
      <!-- <div class="student_exam_detail">
        <span class="exam_info">用户名:{{ currentUserInfo.userName }}</span><span class="exam_info">姓名:{{ currentUserInfo.name }}</span><span class="exam_info">考核名称:{{ currentUserInfo.examName }}</span><span class="exam_info">身份证号码:{{ currentUserInfo.indentityCode }}</span><span class="exam_info">班级:{{ currentUserInfo.className }}</span><span class="exam_info">成绩:{{ currentUserInfo.lastScore }}分</span>
      </div> -->
      <el-descriptions :column="4">
        <el-descriptions-item label="用户名">{{ currentUserInfo.userName }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ currentUserInfo.name }}</el-descriptions-item>
        <!-- <el-descriptions-item label="考核名称">{{ currentUserInfo.examName }}</el-descriptions-item> -->
        <el-descriptions-item label="部门">{{ currentUserInfo.className }}</el-descriptions-item>
        <el-descriptions-item label="成绩">{{ currentUserInfo.lastScore }}分</el-descriptions-item>
      </el-descriptions>
      <div v-loading="achievementDetailLoading">
        <div v-for="item in newQuestionList" :key="item.type" class="examPaper_achieve">
          <el-collapse v-model="collapseActive">
            <el-collapse-item :name="item.type">
              <template slot="title">
                <div v-if="item.items && item.items.length > 0">
                  <span v-if="item.type === 0 ">单选题</span>
                  <span v-if="item.type === 1 ">多选题</span>
                  <span v-if="item.type === 2 ">判断题</span>
                  <span v-if="item.type === 3 ">填空题</span>
                  <span v-if="item.type === 6 ">问答题</span>
                  <span v-if="item.items && item.items.length > 0">
                    <span>(每题{{ item.score }}分, 共{{ item.items.length }}题, 得分{{ item.lastScore }},
                      满分{{ item.totalScore }})</span>
                  </span>
                </div>
              </template>
              <div v-for="(question,index) in item.items" :key="index">
                <exam-result-preview :order="question.order" :data="question" @score-change="scoreChange" />
              </div>
            </el-collapse-item>
          </el-collapse>

        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button round size="medium" @click="achievementDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 考试参加学生 -->
    <el-dialog v-if="examUserDialog" class="examUserDialog" title="学生详情" :visible.sync="examUserDialog" top="5vh" width="1050px">
      <el-input v-model="examUserListQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
      <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList(1)">搜索</el-button>
      <el-button round size="small" type="primary" icon="el-icon-download" @click="handleExportUserAchive()">成绩导出</el-button>
      <el-table v-loading="examUserListLoading" :data="examUserList" size="medium" style="width: 100%" highlight-current-row @sort-change="sortChange">
        <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
        <el-table-column prop="userName" label="用户名" width="150" />
        <el-table-column prop="name" label="姓名" width="150" />
        <!-- <el-table-column prop="examName" label="考核名称" />
        <el-table-column prop="indentityCode" label="身份证号码" width="220" /> -->
        <el-table-column prop="className" label="部门" sortable="className" width="150" />
        <el-table-column prop="lastScore" label="成绩" sortable="lastScore" width="100">
          <template slot-scope="{row}">
            <span>{{ row.lastScore }} 分</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastAnswerTimeLong" label="答题时长" width="120">
          <template slot-scope="{row}">
            {{ row.lastAnswerTimeLong | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column prop="quitCount" label="退出次数" sortable="quitCount" width="100">
          <template slot-scope="{row}">
            <span>{{ row.quitCount }} 次</span>
          </template>
        </el-table-column>
        <el-table-column prop="isAnswered" label="考核状态" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.isAnswered === 0" size="medium" type="info">未进入考试</el-tag>
            <el-tag v-else-if="row.isAnswered === 1 && row.submitTimes===0" size="medium" type="success">正在考试</el-tag>
            <el-tag v-else-if="row.isAnswered === 1 && row.submitTimes>0" size="medium">已提交</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="left" width="120">
          <template slot-scope="{row}">
            <el-button
              :disabled="!row.submitTimes"
              round
              type="primary"
              size="mini"
              icon="el-icon-view"
              @click="handleViewExamAchieveDetail(1,row)"
            >
              查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="examUserListQuery.totalCount > 0"
        :total="examUserListQuery.totalCount"
        :page.sync="examUserListQuery.page"
        :limit.sync="examUserListQuery.MaxResultCount"
        @pagination="getExamUserList"
      />
    </el-dialog>
    <!-- 课程查看所有学生 -->
    <el-dialog v-if="trainCourseUserDialog" class="trainCourseUserDialog" title="学生详情" :visible.sync="trainCourseUserDialog" top="5vh" width="1150px">
      <el-input v-model="trainCourseUserListQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
      <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList(2)">搜索</el-button>
      <el-table v-loading="trainCourseUserListLoading" :data="trainCourseUserList" highlight-current-row @sort-change="courseUserSortChange">
        <el-table-column label="学员姓名" sortable="name" prop="name" />
        <el-table-column label="用户名" sortable="userName" prop="userName" />
        <el-table-column label="学习时长" sortable="courseLearnDuration" prop="courseLearnDuration" width="165">
          <template slot-scope="{row}">
            <span>{{ row.courseLearnDuration | timeFormat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="学习进度" prop="courseLearnProgress" sortable="courseLearnProgress">
          <template slot-scope="{row}">
            {{ row.courseLearnProgress.toFixed(2) }} %
          </template>
        </el-table-column>
        <!-- <el-table-column label="视频资源学习时长" sortable="courseVideoLearnDuration" prop="courseVideoLearnDuration" width="165">
          <template slot-scope="{row}">
            <span>{{ row.courseVideoLearnDuration | timeFormat }}</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="课程资源总数" prop="courseResCount" /> -->
        <el-table-column label="学习完成资源数" sortable="completeResCount" prop="completeResCount" width="165">
          <template slot-scope="{row}">
            {{ row.completeResCount }} 个
          </template>
        </el-table-column>
        <el-table-column label="最后一次学习时间" sortable="lastLearnTime" prop="lastLearnTime" width="180">
          <template slot-scope="{row}">
            <span>{{ row.lastLearnTime | formatDateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否完成" sortable="isComplete" prop="isComplete" width="100">
          <template slot-scope="{row}">
            <el-tag v-if="row.isComplete" type="success">已完成</el-tag>
            <el-tag v-else type="info">未完成</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="trainCourseUserListQuery.totalCount > 0"
        :total="trainCourseUserListQuery.totalCount"
        :page.sync="trainCourseUserListQuery.page"
        :limit.sync="trainCourseUserListQuery.MaxResultCount"
        @pagination="getCourseUserDetail"
      />
    </el-dialog>
    <el-dialog title="批量阅卷" :visible.sync="batchViewDialog" width="1000px" top="10vh">
      <el-form :model="batchViewQuestionForm" label-width="80px">
        <el-form-item label="选择题目">
          <el-select v-model="selectQuestionId" placeholder="请选择" style="width: 100%" @change="batchViewQuestionChange">
            <el-option v-for="item in subjectiveQuestionList" :key="item.id" :label="JSON.parse(item.questionStem).Title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="题目解析">
          <el-input v-model="batchViewQuestionForm.analysis" :disabled="true" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item>
          <el-tabs v-model="currentScored" @tab-click="handleScoreTableClick">
            <el-tab-pane label="未阅" name="scored">
              <el-table v-loading="batchListLoading" :data="batchQuestionList" size="mini">
                <el-table-column label="用户名" prop="userName" width="140px" />
                <el-table-column label="答案" prop="answer">
                  <template slot-scope="{row}">
                    <span v-if="batchViewQuestionForm.questionType === 6">{{ row.answer }}</span>
                    <span v-if="batchViewQuestionForm.questionType === 3 && row.answer.length" class="blank-item">
                      <span v-for="(str,i) in JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/)" :key="i">
                        {{ str }}<span v-if="i< JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/).length-1" class="answer">{{ row.answer ? JSON.parse(row.answer)[i] : '' }}</span>
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="分数" prop="score" width="120px">
                  <template slot-scope="{row}">
                    <el-select v-model="row.score" size="mini" @change="batchUserScoreChange(row)">
                      <el-option v-for="i in batchViewQuestionForm.score*2" :key="i" :label="i*0.5" :value="i*0.5" />
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="已阅" name="noScored">
              <el-table v-loading="batchListLoading" :data="batchQuestionList" size="mini">
                <el-table-column label="用户名" prop="userName" width="140px" />
                <el-table-column label="答案" prop="answer">
                  <template slot-scope="{row}">
                    <span v-if="batchViewQuestionForm.questionType === 6">{{ row.answer }}</span>
                    <span v-if="batchViewQuestionForm.questionType === 3 && row.answer" class="blank-item">
                      <span v-for="(str,i) in JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/)" :key="i">
                        {{ str }}<span v-if="i< JSON.parse(batchViewQuestionForm.questionStem).Title.split(/_+/).length-1" class="answer">{{ row.answer ? JSON.parse(row.answer)[i] : '' }}</span>
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="分数" prop="score" width="120px">
                  <template slot-scope="{row}">
                    <el-select v-model="row.score" size="mini" @change="batchUserScoreChange(row)">
                      <el-option v-for="i in batchViewQuestionForm.score*2" :key="i" :label="i*0.5" :value="i*0.5" />
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
          <pagination
            v-show="subjectiveQuestionListQuery.totalCount > 0"
            :total="subjectiveQuestionListQuery.totalCount"
            :page.sync="subjectiveQuestionListQuery.page"
            :limit.sync="subjectiveQuestionListQuery.MaxResultCount"
            @pagination="getBatchQuestionList"
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button round @click="batchViewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog class="studentDialog" title="指定学生续考" :visible.sync="studentDialog" width="800px">
      <div class="choice_stu" style="margin-bottom: 10px">
        <span style="font-size: 16px; color: #909399;line-height: 30px;">已选择学生:</span><span
          style="font-size: 16px;font-weight: bold;"
        >
          {{ continueStudents.length }}名学生 </span>
        <div style="float:right">
          <el-input v-model="examUserListQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
          <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList(1)">搜索</el-button>
        </div>
      </div>
      <el-table
        v-loading="examUserListLoading"
        size="small"
        :data="examUserList"
        border
        style="width: 100%"
        @selection-change="handleContinueStuChange"
      >
        <el-table-column type="selection" :selectable="checkSelectable" width="55" />
        <el-table-column prop="userName" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="className" label="部门" show-overflow-tooltip="" />
        <el-table-column prop="submitTimes" label="状态">
          <template slot-scope="{row}">
            <span v-if="row.submitTimes >0">已提交</span>
            <span v-if="row.submitTimes ===0">未提交</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="examUserListQuery.totalCount > 0"
        :total="examUserListQuery.totalCount"
        :page.sync="examUserListQuery.page"
        :limit.sync="examUserListQuery.MaxResultCount"
        @pagination="getExamUserList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" :loading="continueLoading" round type="primary" @click="continueBtn">确 定</el-button>
        <el-button size="medium" round @click="studentDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { subjectiveQuestionList, subjectiveQuestionScore, subjectiveQuestionAnswer, trainsUser, trainsDetail, trainsUserAll, trainsUserRecordList, trainsUserExamList, studentExamDetail, examQuestionsDetail, trainsExamList, studentExamResultList, trainAllCourse, trainCourseAllUser } from '@/api/train'
import { parseTime, formatSecond } from '@/utils'
import { publishScore } from '@/api/examPaper'
import {
  setContinueUser
} from '@/api/examniationUser'
import Pagination from '@/components/Pagination'
import ExamResultPreview from '@/components/ExamResultPreview'
export default {
  name: 'TrainStatistics',
  components: {
    Pagination,
    ExamResultPreview
  },
  data() {
    return {
      examinationId: null,
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: 'creationTime desc',
        TrainId: this.$route.query.id,
        page: 1,
        totalCount: 0
      },
      form: {
        trainPackageName: '',
        notice: '',
        name: '',
        startDate: '',
        endDate: ''
      },
      extraProperties: {
        CourseCount: 0,
        ExamCount: 0,
        ClassHour: 0
      },
      currentUserInfo: {
        userName: '',
        name: '',
        identityCode: '',
        className: '',
        examName: '',
        lastScore: 0,
        userId: ''
      },

      tabActiveName: 'user',
      // 学习进度
      examCompleteDetailDialog: false,
      examDetailList: [],
      examDetailListLoading: false,
      // 考试详情
      examAchieveDetailDialog: false,
      examAchieveDetailList: [],
      examAchieveDetailListLoading: false,
      // 试卷答题详情
      achievementDetailDialog: false,
      achievementDetailLoading: false,
      replyContent: [],
      // 单选集合
      singleQuestions: [],
      // 单选分数
      singleScore: 0,
      // 单选题得分
      singleLastScore: 0,
      // 判断集合
      judgeQuestions: [],
      // 判断分数
      judgeScore: 0,
      // 判断题得分
      judgeLastScore: 0,
      // 多选集合
      multipleQuestions: [],
      // 多选分数
      multipleScore: 0,
      // 多选题得分
      multipleLastScore: 0,
      // 填空集合
      blankQuestions: [],
      // 填空分数
      blankScore: 0,
      // 填空最后得分
      blankLastScore: 0,
      // 问答集合
      replayQuestions: [],
      // 问答分数
      replayScore: 0,
      // 问答最后得分
      replayLastScore: 0,
      questionList: [],
      // 新的合成数据
      newQuestionList: [],
      // 成绩预览折叠面板默认全部展开
      collapseActive: [0, 1, 2, 3, 6],

      // 考核
      trainExamName: '',
      trainExamList: [],
      trainCourseList: [],
      trainCourseUserList: [],
      trainCourseUserListLoading: false,
      trainCourseUserDialog: false,
      trainCourseUserListQuery: {
        Filter: '',
        Sorting: 'LastLearnTime desc',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1,
        TrainId: this.$route.query.id,
        CourseId: ''
      },
      examUserDialog: false,
      examUserList: [],
      examUserListLoading: false,
      examUserListQuery: {
        ExaminationId: '',
        Filter: '',
        Sorting: '',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1
      },
      // 批量阅卷
      currentScored: 'scored',
      batchViewDialog: false,
      // 学员答题答案
      batchListLoading: false,
      batchQuestionList: [],
      // 批量阅卷题目
      subjectiveQuestionList: [],
      subjectiveQuestionListQuery: {
        ExaminationId: '',
        QuestionId: '',
        IsScored: true,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 批量阅卷题目信息
      selectQuestionId: null,
      batchViewQuestionForm: {},
      // 单人评分入口
      isExam: false,

      // 续考
      studentDialog: false,
      continueStudentName: [],
      continueStudents: [],
      continueLoading: false

    }
  },
  created() {
    this.getTrainDetail(this.$route.query.id)
    this.getTrainUser()
    this.getTrainExamList()
    this.getTrainAllCourse()
  },
  methods: {
    // 查看学习进度
    handleViewExamCompleteDetail(row) {
      this.currentUserInfo.userId = row.userId
      this.currentUserInfo.userName = row.userName
      this.currentUserInfo.name = row.name
      this.currentUserInfo.identityCode = row.identityCode
      this.currentUserInfo.className = row.className
      this.examCompleteDetailDialog = true
      this.getExamCompleteDetail({ trainId: this.$route.query.id, userId: this.currentUserInfo.userId })
    },
    // 点击0/0 考核
    handleViewExamAchieve(row) {
      this.currentUserInfo.userId = row.userId
      this.currentUserInfo.userName = row.userName
      this.currentUserInfo.name = row.name
      this.currentUserInfo.identityCode = row.identityCode
      this.currentUserInfo.className = row.className
      this.examAchieveDetailDialog = true

      this.getExamAchieve({ trainId: this.$route.query.id, userId: this.currentUserInfo.userId })
    },
    // 查看试卷
    async handleViewExamAchieveDetail(t, row) {
      this.newQuestionList = []
      this.judgeQuestions = []
      this.singleQuestions = []
      this.blankQuestions = []
      this.replayQuestions = []
      this.multipleQuestions = []
      this.replyContent = []
      this.multipleLastScore = 0
      this.judgeLastScore = 0
      this.singleLastScore = 0
      this.blankLastScore = 0
      this.replayLastScore = 0
      this.currentUserInfo.examName = row.name
      this.currentUserInfo.lastScore = row.lastScore
      this.achievementDetailLoading = true
      this.achievementDetailDialog = true
      var form = {
        id: '',
        userId: ''
      }
      this.isExam = !!t
      if (t === 0) {
        this.examinationId = row.examinationId
        form.id = row.examinationId
        form.userId = this.currentUserInfo.userId
      } else if (t === 1) {
        // 信息
        this.currentUserInfo.userId = row.userId
        this.currentUserInfo.userName = row.userName
        this.currentUserInfo.name = row.name
        this.currentUserInfo.identityCode = row.identityCode
        this.currentUserInfo.className = row.className

        this.examinationId = this.examUserListQuery.ExaminationId
        form.id = this.examUserListQuery.ExaminationId
        form.userId = row.userId
      }

      try {
        const res = await studentExamDetail(form)
        if (res) {
          this.replyContent = JSON.parse(res.replyContent)
        }
      } catch (e) {
        this.replyContent = []
      }

      // await examQuestionsDetail(row.examinationId).then(res => {
      try {
        const response = await examQuestionsDetail(form.id)
        this.questionList = response
      } catch (e) {
        this.$message.error('获取试卷失败')
        this.questionList = []
      }

      // }).catch(() => {
      //   this.$message.error('获取试卷失败')
      //   this.questionList = []
      // })
      this.questionList.forEach(questionItem => {
        // 设置回显model 多选是数组
        if (questionItem.questionType === 1) {
          this.$set(questionItem, 'answerModel', [])
        } else {
          this.$set(questionItem, 'answerModel', null)
        }
        this.$set(questionItem, 'IsRight', false)
        if (this.replyContent !== null) {
          this.replyContent.forEach(replyItem => {
            // 如果ID相同判断类型  回显model赋值
            if (questionItem.id === replyItem.Q) {
              if (questionItem.questionType === 1 && replyItem.O) {
                questionItem.answerModel = replyItem.O
                this.multipleLastScore += replyItem.S
              } else if (questionItem.questionType === 0 && replyItem.O) {
                questionItem.answerModel = replyItem.O[0]
                this.singleLastScore += replyItem.S
              } else if (questionItem.questionType === 2) {
                questionItem.answerModel = replyItem.J
                this.judgeLastScore += replyItem.S
              } else if (questionItem.questionType === 3 && replyItem.BA) {
                questionItem.answerModel = replyItem.BA
                this.blankLastScore += replyItem.S
              } else if (questionItem.questionType === 6) {
                questionItem.answerModel = replyItem.RA
                this.replayLastScore += replyItem.S
              }

              questionItem.finalScore = replyItem.S
              questionItem.IsRight = replyItem.R
            }
          })
        } else {
          this.multipleLastScore = 0
          this.singleLastScore = 0
          this.judgeLastScore = 0
          this.blankLastScore = 0
          this.replayLastScore = 0
        }
      })
      this.questionList.forEach(questionItem => {
        if (questionItem.questionType === 0) {
          this.singleQuestions.push(questionItem)
          this.singleScore = questionItem.score
        } else if (questionItem.questionType === 1) {
          this.multipleQuestions.push(questionItem)
          this.multipleScore = questionItem.score
        } else if (questionItem.questionType === 2) {
          this.judgeQuestions.push(questionItem)
          this.judgeScore = questionItem.score
        } else if (questionItem.questionType === 3) {
          this.blankQuestions.push(questionItem)
          this.blankScore = questionItem.score
        } else if (questionItem.questionType === 6) {
          this.replayQuestions.push(questionItem)
          this.replayScore = questionItem.score
        }
      })
      if (this.judgeQuestions.length) {
        this.newQuestionList.push({
          type: 2,
          score: this.judgeScore,
          totalScore: this.judgeScore * this.judgeQuestions.length,
          lastScore: this.judgeLastScore,
          items: this.judgeQuestions
        })
      }
      if (this.singleQuestions.length) {
        this.newQuestionList.push({
          type: 0,
          score: this.singleScore,
          totalScore: this.singleScore * this.singleQuestions.length,
          lastScore: this.singleLastScore,
          items: this.singleQuestions
        })
      }
      if (this.multipleQuestions.length) {
        this.newQuestionList.push({
          type: 1,
          score: this.multipleScore,
          totalScore: this.multipleScore * this.multipleQuestions.length,
          lastScore: this.multipleLastScore,
          items: this.multipleQuestions
        })
      }
      if (this.blankQuestions.length) {
        this.newQuestionList.push({
          type: 3,
          score: this.blankScore,
          totalScore: this.blankScore * this.blankQuestions.length,
          lastScore: this.blankLastScore,
          items: this.blankQuestions
        })
      }
      if (this.replayQuestions.length) {
        this.newQuestionList.push({
          type: 6,
          score: this.replayScore,
          totalScore: this.replayScore * this.replayQuestions.length,
          lastScore: this.replayLastScore,
          items: this.replayQuestions
        })
      }
      this.achievementDetailLoading = false
    },
    handleViewExamWithUser(row) {
      this.trainExamName = row.examName
      this.examinationId = row.examinationId
      this.examUserDialog = true
      this.examUserListQuery.page = 1
      this.examUserListQuery.ExaminationId = row.examinationId
      this.getExamUserList()
    },
    // 批量阅卷
    async handleBatchViewExam(row) {
      // 请求数据
      //

      this.currentScored = 'scored'
      this.batchQuestionList = []
      this.subjectiveQuestionListQuery.IsScored = false
      this.subjectiveQuestionListQuery.QuestionId = null
      this.subjectiveQuestionListQuery.ExaminationId = row.examinationId
      this.subjectiveQuestionList = []
      const res = await subjectiveQuestionList(row.examinationId)
      this.subjectiveQuestionList = res

      this.batchViewDialog = true
    },
    // 批量阅卷题目变化
    async batchViewQuestionChange(val) {
      // this.batchViewQuestionForm = {}
      this.subjectiveQuestionListQuery.QuestionId = val
      this.subjectiveQuestionListQuery.page = 1
      this.batchViewQuestionForm = this.subjectiveQuestionList.find(item => { return item.id === this.selectQuestionId })
      // 获取最新列表
      this.getBatchQuestionList()
    },
    // 学生分数变化
    batchUserScoreChange(row) {
      // 提交数据
      var data = {
        examinationId: this.subjectiveQuestionListQuery.ExaminationId,
        questionId: this.subjectiveQuestionListQuery.QuestionId,
        userId: row.userId,
        score: row.score
      }
      subjectiveQuestionScore(data).then(res => {

      })
    },
    async handleScoreTableClick(val, event) {
      this.subjectiveQuestionListQuery.page = 1
      if (val.name === 'scored') {
        this.subjectiveQuestionListQuery.IsScored = false
      } else {
        this.subjectiveQuestionListQuery.IsScored = true
      }
      this.getBatchQuestionList()
    },
    scoreChange(id, val, oldVal, type) {
      var data = {
        examinationId: this.examinationId,
        questionId: id,
        userId: this.currentUserInfo.userId,
        score: val
      }
      subjectiveQuestionScore(data).then(res => {
        this.$message.success('评分成功')
        this.newQuestionList.forEach(item => {
          if (item.type === type) {
            item.lastScore = item.lastScore + val - oldVal
            this.currentUserInfo.lastScore = this.currentUserInfo.lastScore + val - oldVal
            return
          }
        })
      })
    },
    scoreDialogClosed() {
      if (this.isExam) {
        this.getExamUserList()
      } else {
        this.getExamAchieve({ trainId: this.$route.query.id, userId: this.currentUserInfo.userId })
      }
    },
    // 公布成绩
    handlePublic(row) {
      this.$confirm('公布成绩后无法取消, 是否确认公布?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        publishScore(row.examinationId).then(res => {
          this.$notify({
            title: '成功',
            message: '公布成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {

      })
    },
    handleContinueExam(row) {
      this.examUserListQuery.page = 1
      this.examUserListQuery.ExaminationId = row.examinationId
      this.getExamUserList()
      this.studentDialog = true
    },
    checkSelectable(row) {
      // return true
      return !(row.submitTimes > 0)
    },
    // 选择续考重考学生变化
    handleContinueStuChange(val) {
      this.continueStudents = val
    },
    continueBtn() {
      this.continueLoading = true
      var userIds = []
      if (!this.continueStudents || this.continueStudents.length === 0) {
        this.$message.warning('请选择续考学生')
        return
      }
      this.continueStudents.forEach(item => {
        userIds.push(item.userId)
      })
      var form = {
        examinationId: this.examUserListQuery.ExaminationId,
        userIds: userIds
      }
      setContinueUser(form).then(res => {
        this.continueLoading = false
        this.studentDialog = false
        this.$notify({
          title: '成功',
          message: '设置成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.continueLoading = false
      })
    },
    handleViewCourseUserDetail(row) {
      this.trainCourseUserListQuery.CourseId = row.courseId
      this.trainCourseUserDialog = true
      this.getCourseUserDetail()
    },
    handleRefreshList(t) {
      if (t === 0) {
        this.listQuery.page = 1
        this.getTrainUser()
      } else if (t === 1) {
        this.examUserListQuery.page = 1
        this.getExamUserList()
      } else if (t === 2) {
        this.trainCourseUserListQuery.page = 1
        this.getCourseUserDetail()
      }
    },
    // 列表排序
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getExamUserList()
        return
      }
      this.examUserListQuery.Sorting = prop + ' ' + order
      this.getExamUserList()
    },
    courseUserSortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getCourseUserDetail()
        return
      }
      this.trainCourseUserListQuery.Sorting = prop + ' ' + order
      this.getCourseUserDetail()
    },
    getBatchQuestionList() {
      this.batchListLoading = true
      this.subjectiveQuestionListQuery.SkipCount = (this.subjectiveQuestionListQuery.page - 1) * this.subjectiveQuestionListQuery.MaxResultCount
      subjectiveQuestionAnswer(this.subjectiveQuestionListQuery).then(res => {
        this.batchQuestionList = res.items
        this.batchListLoading = false
      })
    },
    getTrainDetail(id) {
      trainsDetail(id).then(res => {
        this.form.name = res.name
        this.form.startDate = res.startDate
        this.form.endDate = res.endDate
        this.form.notice = res.notice
        this.form.trainPackageName = res.trainPackageName
        this.extraProperties.CourseCount = res.extraProperties.CourseCount
        this.extraProperties.ExamCount = res.extraProperties.ExamCount
        this.extraProperties.ClassHour = res.extraProperties.ClassHour

        // this.getTrainExamList(res.trainPackageId)
        // this.getTtrainsCourseList(res.trainPackageId)
      })
    },
    getTrainUser() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsUser(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    getExamCompleteDetail(data) {
      this.examDetailListLoading = true
      trainsUserRecordList(data).then(res => {
        this.examDetailList = res.items
        this.examDetailListLoading = false
      }).catch(() => {
        this.examDetailListLoading = false
      })
    },
    getExamAchieve(data) {
      this.examAchieveDetailListLoading = true
      trainsUserExamList(data).then(res => {
        this.examAchieveDetailList = res.items
        this.examAchieveDetailListLoading = false
      }).catch(() => {
        this.examAchieveDetailListLoading = false
      })
    },
    handleExport() {
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['学员姓名', '用户名', '部门', '学习时长', '学习进度', '考试提交情况']
        const filterVal = ['name', 'userName', 'className', 'learnDuration', 'courseProgress', 'examProgress']
        // 成绩列表数据
        var list = []
        const res = await trainsUserAll(this.$route.query.id)
        // list = res.items+
        res.items.forEach(item => {
          list.push({
            ...item, learnDuration: formatSecond(item.learnDuration), courseProgress: this.extraProperties.CourseCount !== 0 ? item.courseCompleteCount + '/' + this.extraProperties.CourseCount : '--',
            examProgress: this.extraProperties.ExamCount !== 0 ? item.examCompleteCount + '/' + this.extraProperties.ExamCount : '--'
          })
        })
        // }).catch(() => {
        // })
        const data = this.formatJson(filterVal, list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.form.name + '_详情_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    async handleExportUserAchive() {
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['学员姓名', '用户名', '部门', '成绩', '答题时长', '退出次数', '考核状态']
        const filterVal = ['name', 'userName', 'className', 'lastScore', 'lastAnswerTimeLong', 'quitCount', 'examState']
        // 成绩列表数据
        var form = {
          ExaminationId: this.examUserListQuery.ExaminationId,
          Filter: '',
          Sorting: '',
          MaxResultCount: 999,
          SkipCount: 0
        }
        var list = []
        const res = await studentExamResultList(form)
        // list = res.items+
        res.items.forEach(item => {
          var state = ''
          if (item.isAnswered === 0) {
            state = '未进入考试'
          } else {
            if (item.submitTimes === 0) {
              state = '正在考试'
            } else {
              state = '已提交'
            }
          }
          list.push({
            ...item, lastAnswerTimeLong: formatSecond(item.lastAnswerTimeLong), examState: state
          })
        })
        // }).catch(() => {
        // })
        const data = this.formatJson(filterVal, list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.trainExamName + '_答题详情_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 获取培训下所有考核
    getTrainExamList() {
      trainsExamList(this.$route.query.id).then(res => {
        this.trainExamList = res.items
      })
    },
    // 获取培训包包含的课程
    getTrainAllCourse() {
      trainAllCourse(this.$route.query.id).then(res => {
        this.trainCourseList = res.items
      })
    },
    // 根据考核id获取学生列表
    getExamUserList() {
      this.examUserListLoading = true
      this.examUserListQuery.SkipCount = (this.examUserListQuery.page - 1) * this.examUserListQuery.MaxResultCount
      studentExamResultList(this.examUserListQuery).then(res => {
        this.examUserList = res.items
        this.examUserListQuery.totalCount = res.totalCount
        this.examUserListLoading = false
      }).catch(() => {
        this.examUserListLoading = false
      })
    },
    // 获取课程下所有学生
    getCourseUserDetail() {
      this.trainCourseUserListLoading = true
      this.trainCourseUserListQuery.SkipCount = (this.trainCourseUserListQuery.page - 1) * this.trainCourseUserListQuery.MaxResultCount
      trainCourseAllUser(this.trainCourseUserListQuery).then(res => {
        this.trainCourseUserList = res.items
        this.trainCourseUserListQuery.totalCount = res.totalCount
        this.trainCourseUserListLoading = false
      }).catch(() => {
        this.trainCourseUserListLoading = false
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          }
          if (j === 'totalTime') {
            return (this.$options.filters['secondToMin'](v[j]))
          }
          if (j === 'lastAnswerTimeLong') {
            return formatSecond(v[j])
          }
          if (j === 'username') {
            return v['user']['name']
          } else {
            return v[j]
          }
        })
      )
    }
  }
}

</script>
<style lang="scss" scoped>
.el-descriptions ::v-deep .descriptions_label {
  width: 120px
}
  .blank-item .answer{

        // height: 42px;
        // line-height: 42px;
        // border: none;
        // border-bottom: 1px solid #ccc;
        // // width: 100px;
        // padding: 1px 10px;
        // outline: none;
        text-decoration: underline #ccc;
        text-underline-offset: 5px;
        margin: 0 5px;

}
</style>

<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <div class="header_flex_box">
            <el-radio-group v-model="listQuery.Status" size="small" @change="handleRefreshList">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="1">上架</el-radio-button>
              <el-radio-button label="0">未上架</el-radio-button>
            </el-radio-group>
            <el-select
              v-model="listQuery.KnowledgeCategoryId"
              clearable
              placeholder="选择资源分类"
              size="small"
              @change="handleRefreshList"
            >
              <!-- <el-option label="资源分类" :value="''" /> -->
              <el-option v-for="item in resourceCategoryList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <!-- <el-select v-model="listQuery.Status" clearable placeholder="是否上架" size="small" @change="handleRefreshList">
                <el-option label="全部" :value="null" />
                <el-option label="上架" :value="1" />
                <el-option label="未上架" :value="0" />
              </el-select> -->
            <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
            <el-button
              v-permission="['CourseManagement.KnowledgeResources']"
              round
              size="small"
              type="success"
              icon="el-icon-search"
              @click="handleRefreshList(0)"
            >搜索</el-button>
            <el-button
              v-permission="['CourseManagement.KnowledgeResources.Update']"
              round
              size="small"
              type="primary"
              icon="el-icon-plus"
              @click="handleResourceEdit(0, 0)"
            >添加</el-button>
            <export-excel
              :header="['资源封面', '资源名称', '资源编号', '作者', '资源时长', '上架状态', '创建时间']"
              :filter-val="['thumbnailUrl', 'name', 'number', 'author', 'duration', 'status', 'creationTime']"
              :field="{ 4: [3], 5: ['未上架', '已上架'], 6: [2] }"
              :api-fn="resourceList"
            />
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-table v-loading="listLoading" :data="list" size="medium" highlight-current-row @sort-change="sortChange">
            <el-table-column label="资源封面" sortable="thumbnailUrl" width="100">
              <template slot-scope="{ row }">
                <el-image :src="row.thumbnailUrl" class="resource-cover" fit="cover">
                  <div slot="error">
                    <div class="image-slot">
                      <i class="el-icon-picture-outline" />
                    </div>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="资源名称" prop="name" sortable="name" min-width="200">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handlePreviewResource(row)">{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="资源编号" prop="number" sortable="number" width="100" />
            <el-table-column label="作者" prop="author" sortable="author" width="100" />
            <el-table-column label="资源时长" prop="duration" sortable="duration" width="100">
              <template slot-scope="{ row }">
                {{ row.duration | formatSecond }}
              </template>
            </el-table-column>
            <el-table-column label="上架状态" prop="status" sortable="status" width="120">
              <template slot-scope="{ row }">
                <el-tag v-if="row.status === 0" type="info">未上架</el-tag>
                <el-tag v-if="row.status === 1" type="success">已上架</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
              <template slot-scope="{row}">
                {{ row.creationTime | formatDateTime }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="300">
              <template slot-scope="{row}">
                <el-button
                  v-if="row.status === 0"
                  v-permission="['CourseManagement.KnowledgeResources.Update']"
                  round
                  size="mini"
                  type="primary"
                  icon="el-icon-edit"
                  @click="handleResourceEdit(1, row)"
                >编辑</el-button>
                <el-button
                  v-if="row.status === 1"
                  v-permission="['CourseManagement.KnowledgeResources.Update']"
                  round
                  size="mini"
                  type="warning"
                  icon="el-icon-bottom"
                  @click="handleResourcePublish(row)"
                >下架</el-button>
                <el-button
                  v-if="row.status === 0"
                  v-permission="['CourseManagement.KnowledgeResources.Update']"
                  round
                  size="mini"
                  type="success"
                  icon="el-icon-top"
                  @click="handleResourcePublish(row)"
                >上架</el-button>
                <el-button
                  v-if="row.status === 0"
                  v-permission="['CourseManagement.KnowledgeResources.Delete']"
                  round
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleResourceDelete(row)"
                >删除</el-button>
                <!-- <el-button v-if="row.status === 1" round type="primary" size="mini" icon="el-icon-view" @click="handleViewTenantResource(row)">查看</el-button> -->
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="listQuery.totalCount > 0"
            :total="listQuery.totalCount"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getResourceList"
          />
        </el-col>
      </el-row>
    </el-card>
    <el-dialog :title="isEdit ? '编辑' : '添加'" :close-on-click-modal="false" :visible.sync="resourceDialog" width="600px">

      <el-form ref="form" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="资源图片" prop="thumbnailUrl">
          <lz-upload-images
            v-if="resourceDialog"
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="uplaodFileType"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="资源视频" prop="url">
          <lz-upload-file
            class="uplaod_file"
            :limit="1"
            :file-size="1024"
            :file-type="['MP4', 'mp4']"
            :file-list="uploadResourceList"
            :btn-title="'上传视频'"
            @response-fn="handleFileResponse"
            @remove-upload="handleRemoveFile"
          />
        </el-form-item>
        <el-form-item label="资源编号" prop="number">
          <el-input v-model="form.number" />
        </el-form-item>
        <el-form-item label="资源名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="资源分类" prop="knowledgeCategoryId">
          <el-select v-model="form.knowledgeCategoryId" filterable clearable placeholder="选择资源分类" class="form-select">
            <el-option v-for="item in resourceCategoryList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="作者" prop="teacherId">
          <el-select
            v-model="form.teacherId"
            filterable
            clearable
            placeholder="选择作者"
            size="small"
            @change="handleAuthorChange"
          >
            <el-option v-for="item in resourceAuthorList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button :loading="dialogLoading" round type="primary" @click="resourceDialogSure">确 定</el-button>
        <el-button round @click="resourceDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源预览" append-to-body :visible.sync="resourcePreviewDialog" width="1000px" top="5vh">
      <!-- <preview-resource v-if="resourcePreviewDialog" ref="previewResource" :type="previewType" :url="previewUrl" /> -->
      <bos-resource-preview v-if="resourcePreviewDialog" :type="previewType" :url="previewUrl" />

    </el-dialog>
  </div>
</template>
<script>
import {
  resourceList,
  resourceCategoryList,
  resourceAdd,
  resourceDelete,
  resourcePublish,
  resourceEdit,
  resourceAuthorAllList
} from '@/api/resource'
import LzUploadImages from '@/components/LzUploadImages'
import LzUploadFile from '@/components/LzUploadFile'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import PreviewResource from '@/components/PreviewResource'
import BosResourcePreview from '@/components/BosResourcePreview'

import { getFileDownloadInfo, getBaoCloudResourceUrl, resourcePath } from '@/api/upload'
import { mapGetters } from 'vuex'
export default {
  name: 'ResourceList',
  components: {
    LzUploadImages,
    Pagination,
    LzUploadFile,
    PreviewResource,
    BosResourcePreview
  },

  directives: { permission },
  data() {
    var checkCoverUrl = (rule, value, callback) => {
      if (this.form.thumbnailUrl === '') {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    var checkFileUrl = (rule, value, callback) => {
      if (this.form.url === '') {
        callback(new Error('请上传视频'))
      } else {
        callback()
      }
    }
    return {
      // 自建资源
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Status: '',
        KnowledgeCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 资源分类列表
      resourceCategoryList: [],
      resourceAuthorList: [],
      // dialog
      resourceDialog: false,
      isEdit: false,
      dialogLoading: false,
      // 上传图片类型
      uplaodFileType: [],
      // 上传图片列表
      previewFileList: [],
      uploadResourceList: [],
      // form
      form: {
        url: '',
        localUrl: '',
        // 编号
        number: '',
        name: '',
        knowledgeCategoryId: '',
        author: '',
        teacherId: '',
        thumbnailUrl: '',
        duration: 0,
        size: 0,
        fileName: '',
        fileType: ''
      },
      formRules: {
        url: [{
          required: true,
          validator: checkFileUrl,
          trigger: 'blur'
        }],
        thumbnailUrl: [{
          required: true,
          validator: checkCoverUrl,
          trigger: 'blur'
        }],
        number: [{
          required: true,
          message: '请输入资源编号',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        name: [{
          required: true,
          message: '请输入资源名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 500,
          message: '长度在 1 到 500 个字符',
          trigger: 'blur'
        }
        ],
        knowledgeCategoryId: [{
          required: true,
          message: '请选择资源分类',
          trigger: 'change'
        }],
        teacherId: [{ required: true, message: '请选择作者', trigger: 'change' }
        ]
      },
      // 资源预览
      resourcePreviewDialog: false,
      previewUrl: '',
      previewType: ''
    }
  },
  computed: {
    ...mapGetters(['tenantPermission'])
  },
  created() {
    this.getResourceList()
    this.getResourceCategoryList()
  },
  methods: {
    resourceList(args) {
      return resourceList(args)
    },
    handleAuthorChange(val) {
      var item = this.resourceAuthorList.filter(item => item.id === val)
      if (item && item.length > 0) {
        this.form.author = item[0].name
      } else {
        this.form.author = ''
      }
    },
    handleResourceEdit(t, row) {
      if (this.resourceAuthorList.length === 0) {
        this.getResourceAuthorList()
      }
      this.previewFileList = []
      this.uploadResourceList = []
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.resetForm()
      this.isEdit = !!t
      if (this.isEdit) {
        this.form = JSON.parse(JSON.stringify(row))
        this.previewFileList.push({
          name: row.thumbnailUrl,
          url: row.thumbnailUrl
        })
        this.uploadResourceList.push({
          url: row.url,
          size: row.size,
          duration: row.duration,
          name: row.fileName
        })
      }
      this.resourceDialog = true
    },
    resourceDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.isEdit) {
            resourceEdit(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.resourceDialog = false
              this.dialogLoading = false
              this.getResourceList()
            }).catch(() => {
              this.$message.error('编辑失败')
              this.dialogLoading = false
            })
          } else {
            resourceAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.resourceDialog = false
              this.dialogLoading = false
              this.getResourceList()
            }).catch(() => {
              this.$message.error('添加失败')
              this.dialogLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleResourceDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resourceDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getResourceList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleResourcePublish(row) {
      let tipText = '是否确定上架?'
      if (row.status === 1) {
        tipText = '是否确定下架?'
      }
      this.$confirm(tipText, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resourcePublish(row.id, row.status === 0 ? 1 : 0).then(res => {
          this.$message.success('操作成功')
          this.getResourceList()
        }).catch(() => {
          // this.$message.error('操作失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getResourceList()
    },
    // 上传文件成功回调
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.thumbnailUrl = url
    },
    // 上传文件删除
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.thumbnailUrl = ''
    },
    handleFileResponse(url, fileForm) {
      this.uploadResourceList.push({
        url: url,
        localUrl: fileForm.localUrl,
        fileName: fileForm.fileName,
        name: fileForm.fileName,
        hash: fileForm.hash,
        extend: fileForm.fileType,
        size: fileForm.size,
        fileType: fileForm.fileType,
        resType: fileForm.resType,
        tranStatus: fileForm.tranStatus,
        documentId: fileForm.documentId,
        jobId: fileForm.jobId,
        durationInSecond: fileForm.durationInSecond
      })
      this.form.url = url
      this.form.localUrl = fileForm.localUrl
      this.form.duration = fileForm.durationInSecond
      this.form.size = fileForm.size
      this.form.fileName = fileForm.fileName
      this.form.fileType = fileForm.fileType
    },
    handleRemoveFile(file) {
      this.uploadResourceList = []
      this.form.url = ''
      this.form.localUrl = ''
      this.form.duration = 0
      this.form.size = 0
      this.form.fileName = ''
      this.form.fileType = ''
    },
    async handlePreviewResource(row) {
      this.previewType = 'video'
      this.previewUrl = encodeURIComponent(await resourcePath({ url: row.url }))
      this.resourcePreviewDialog = true

      // getBaoCloudResourceUrl(row.url).then(async res => {
      //   this.previewUrl = res.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://filehz.baosteel.com/pbktgf0006/')
      //   this.resourcePreviewDialog = true
      // })
      // const downloadInfo = await getFileDownloadInfo(row.url)
      // this.previewUrl = downloadInfo.downloadUrl
      // this.resourcePreviewDialog = true
    },
    resetForm() {
      this.form = {
        url: '',
        localUrl: '',
        // 编号
        number: '',
        name: '',
        knowledgeCategoryId: '',
        author: '',
        thumbnailUrl: '',
        duration: 0,
        size: 0
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getResourceList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceList()
    },
    getResourceList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.$message.error('获取列表失败')
        this.listLoading = false
      })
    },
    getResourceAuthorList() {
      resourceAuthorAllList().then(res => {
        this.resourceAuthorList = res.items
      }).catch(() => {
        this.$message.error('获取作者列表失败')
      })
    },
    getResourceCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      resourceCategoryList(data).then(res => {
        this.resourceCategoryList = res.items
      }).catch(() => {
        this.$message.error('获取分类失败')
        this.listLoading = false
      })
    }
  }
}
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-card class="box-card student_box_card">
        <div style="margin-bottom: 10px;">
          <el-cascader
            v-model="unselStusListQuery.classId"
            filterable
            :options="orgList"
            :props="cascaderProps"
            clearable
            size="mini"
            placeholder="请选择班级..."
            style="margin-right:10px"
            @change="handleUnselStusClassChange"
          />
          <!-- <el-cascader
            v-model="unselStusListQuery.classId"
            filterable
            :options="orgList"
            size="mini"
            placeholder="请选择班级..."
            clearable
            style="display: inline-block;margin-right:10px"
            @change="handleUnselStusClassChange"
          /> -->
          <el-input
            v-model="unselStusListQuery.keyword"
            style="display: inline-block;width: 100px"
            size="mini"
            placeholder="姓名/用户名"
            @input="unselStusSearchChange"
          />
          <div style="float:right">
            <el-button round size="mini" icon="el-icon-plus" type="success" plain @click="addStudents"> 添加
            </el-button>
          </div>
        </div>
        <el-table v-model="unselectedListLoading" :data="unselectedStus" max-height="418px" border style="width: 100%" size="mini" @selection-change="handleAddStudentChange">
          <el-table-column type="selection" width="40" />
          <!-- <el-table-column prop="name" label="姓名" header-align="left" /> -->
          <el-table-column prop="name" label="姓名" header-align="left">
            <template slot-scope="{row}">
              <span class="table_span">{{ row.name }}({{ row.userName }})</span>
            </template>
          </el-table-column>
          <el-table-column prop="extraProperties" label="部门" header-align="left">
            <template slot-scope="{row}">
              <span v-if="row.extraProperties" class="table_span">{{ row.extraProperties.OUName }}</span>
              <span v-else class="table_span">无分类</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="unselStusListQuery.totalCount > 0"
          class="mini_pagination"
          :small="true"
          :pager-count="5"
          :total="unselStusListQuery.totalCount"
          layout="total,sizes,prev, pager, next"
          :page-size="unselStusListQuery.pageSize"
          :page-sizes="[10,20,50,100,500, unselStusListQuery.totalCount]"
          :page.sync="unselStusListQuery.page"
          :limit.sync="unselStusListQuery.pageSize"
          @pagination="getUnselStus"
        />
      </el-card>
    </el-col>
    <el-col :span="12">
      <el-card class="box-card student_box_card">
        <div style="margin-bottom: 10px;">
          <el-cascader
            v-model="selStusListQuery.classId"
            filterable
            :options="orgList"
            :props="cascaderProps"
            clearable
            size="mini"
            placeholder="请选择班级..."
            style="margin-right:10px"
            @change="handleSelStusClassChange"
          />
          <!-- <el-cascader
            v-model="selStusListQuery.classId"
            filterable
            :options="orgList"
            size="mini"
            placeholder="请选择班级..."
            clearable
            style="margin-right:10px"
            @change="handleSelStusClassChange"
          /> -->
          <el-input
            v-model="selStusListQuery.keyword"
            style="display: inline-block;width: 100px"
            size="mini"
            placeholder="姓名/用户名"
            @input="selStusSearchChange"
          />
          <div style="float:right">
            <el-button round size="mini" icon="el-icon-minus" type="danger" plain @click="removeStudents">移除
            </el-button>
          </div>
        </div>
        <el-table v-model="selectedListLoading" :data="selectedStus" max-height="418px" border style="width: 100%" size="mini" @selection-change="handleRemoveStudentChange">
          <el-table-column type="selection" width="40" />
          <el-table-column prop="name" label="姓名" header-align="left">
            <template slot-scope="{row}">
              <span class="table_span">{{ row.name }}({{ row.userName }})</span>
            </template>
          </el-table-column>
          <el-table-column prop="extraProperties" label="部门" header-align="left">
            <template slot-scope="{row}">
              <span v-if="row.extraProperties" class="table_span">{{ row.extraProperties.OUName }}</span>
              <span v-else class="table_span">无分类</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="selStusListQuery.totalCount > 0"
          class="mini_pagination"
          :small="true"
          :pager-count="5"
          :total="selStusListQuery.totalCount"
          layout="total,sizes,prev, pager, next"
          :page-size="selStusListQuery.pageSize"
          :page-sizes="[10,20,50,100,500, selStusListQuery.totalCount]"
          :page.sync="selStusListQuery.page"
          :limit.sync="selStusListQuery.pageSize"
          @pagination="getSelStus"
        />
      </el-card>
    </el-col>
  </el-row>
</template>
<script>
import Pagination from '@/components/Pagination'
export default {
  name: 'ChooseUser',
  components: {
    Pagination
  },
  props: {
    // 班级数据(el-select)
    allOrg: {
      type: Array,
      require: true,
      default: null
    },
    // 全部学生源数据
    allStudent: {
      type: Array,
      require: true,
      default: null
    },
    currentSelectStudent: {
      type: Array,
      require: true,
      default: null
    }
  },
  data() {
    return {
      cascaderProps: {
        label: 'displayName',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      //
      orgList: [],
      // 全部学生(刚开始未选择学生数据源) 用来处理数据筛选的(会随着筛选变化)
      unchooseStudent: [],
      // 选择学生数据源 用来处理数据筛选的(会随着筛选变化)
      chooseStudent: [],
      // 筛选 未选择学生
      filterUnselStus: [],
      // 筛选 已选择学生
      filterSelStus: [],
      // 未选择学生当前页数据
      unselectedStus: [],
      unselectedListLoading: true,
      // 已选择学生当前页数据
      selectedStus: [],
      selectedListLoading: true,
      // 未选择学生选中后将要添加的学生
      currentAddStus: [],
      // 已选择学生选中后将要移除的学生
      currentRemoveStus: [],
      // 未选择学生分页筛选条件
      unselStusListQuery: {
        totalCount: 0,
        page: 1,
        skipCount: 0,
        pageSize: 10,
        keyword: '',
        classId: ''
      },
      // 已选择学生分页筛选条件
      selStusListQuery: {
        totalCount: 0,
        page: 1,
        skipCount: 0,
        pageSize: 10,
        keyword: '',
        classId: ''
      }
    }
  },
  created() {
    this.initClassList()
    this.initData()
  },
  methods: {
    // 添加学生
    addStudents() {
      // 选择学生是筛选状态  添加学生中有符合筛选条件的加入
      var reg = RegExp(this.selStusListQuery.keyword)
      this.filterSelStus = this.currentAddStus.filter(item => {
        if (this.selStusListQuery.classId) {
          return item.extraProperties.OUId === this.selStusListQuery.classId && (item.userName?.match(reg) || item.name?.match(reg))
        } else {
          return (item.userName?.match(reg) || item.name?.match(reg))
        }
        // if (this.selStusListQuery.classId !== [] && this.selStusListQuery.classId.length > 0) {
        //   return item.extraProperties.OUId === this.selStusListQuery.classId[1] && (item.userName?.match(reg) || item.name?.match(reg))
        // } else {
        //   return (item.userName?.match(reg) || item.name?.match(reg))
        // }
      })
      this.unchooseStudent = this.unchooseStudent.filter(item => {
        return this.currentAddStus.indexOf(item) === -1
      })
      this.$emit('user-change', this.chooseStudent)

      this.chooseStudent.push(...this.currentAddStus)
      this.currentAddStus = []

      this.changePage(0)
      this.getUnselStus()
      this.getSelStus()
    },
    // 移除学生
    removeStudents() {
      // 未选择学生是筛选状态  移除时学生中有符合筛选条件的加入
      var reg = RegExp(this.unselStusListQuery.Filter)
      this.filterUnselStus = this.currentRemoveStus.filter(item => {
        if (this.unselStusListQuery.classId) {
          return item.extraProperties.OUId === this.unselStusListQuery.classId && (item.userName?.match(reg) || item.name?.match(reg))
        } else {
          return (item.userName?.match(reg) || item.name?.match(reg))
        }
        // if (this.unselStusListQuery.classId !== [] && this.unselStusListQuery.classId.length > 0) {
        //   return item.extraProperties.OUId === this.unselStusListQuery.classId[1] && (item.userName?.match(reg) || item.name?.match(reg))
        // } else {
        //   return (item.userName?.match(reg) || item.name?.match(reg))
        // }
      })
      this.chooseStudent = this.chooseStudent.filter(item => {
        return this.currentRemoveStus.indexOf(item) === -1
      })
      this.$emit('user-change', this.chooseStudent)

      this.unchooseStudent.push(...this.currentRemoveStus)
      this.currentRemoveStus = []
      this.changePage(1)
      this.getUnselStus()
      this.getSelStus()
    },
    // 初始化专业班级
    initClassList() {
      // var classList = []

      this.orgList = this.deleteChildren(this.allOrg.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))

      // const result = nodes.map(item => {
      //   const children = item.children.map(every => {
      //     var { children, ...tmp } = every
      //     return tmp
      //   })
      //   return {
      //     ...item,
      //     children: children
      //   }
      // })
      // this.orgList = result
      // this.allOrg.forEach(item => {
      //   if (item.parentId == null) {
      //     this.orgList.push({
      //       value: item.id,
      //       label: item.displayName,
      //       children: []
      //     })
      //   } else {
      //     classList.push({
      //       value: item.id,
      //       parentId: item.parentId,
      //       label: item.displayName
      //     })
      //   }
      // })
      // this.orgList.forEach(orgItem => {
      //   classList.forEach(classItem => {
      //     if (orgItem.value === classItem.parentId) {
      //       orgItem.children.push(classItem)
      //     }
      //   })
      // })
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    // 初始化选择的学生和未选择的学生
    initData() {
      if (this.currentSelectStudent.length > 0) {
        this.unchooseStudent = this.allStudent.filter(item => {
          return !this.currentSelectStudent.includes(item)
        })
      } else {
        this.unchooseStudent = this.allStudent
      }
      this.chooseStudent = this.currentSelectStudent
      this.$emit('user-change', this.chooseStudent)
      this.getUnselStus()
      this.getSelStus()
    },
    // 获取单页未选择学生数据
    getUnselStus() {
      this.unselStusListQuery.skipCount = (this.unselStusListQuery.page - 1) * this.unselStusListQuery.pageSize
      var reg = RegExp(this.unselStusListQuery.keyword)
      this.filterUnselStus = this.unchooseStudent.filter(item => {
        if (this.unselStusListQuery.classId && this.unselStusListQuery.classId.length > 0) {
          return item.extraProperties.OUId === this.unselStusListQuery.classId && (item.userName?.match(reg) || item.name?.match(reg))
        } else {
          return (item.userName?.match(reg) || item.name?.match(reg))
        }
        // if (this.unselStusListQuery.classId !== [] && this.unselStusListQuery.classId.length > 0) {
        //   return item.extraProperties.OUId === this.unselStusListQuery.classId[1] && (item.userName?.match(reg) || item.name?.match(reg))
        // } else {
        //   return (item.userName?.match(reg) || item.name?.match(reg))
        // }
      })
      this.unselStusListQuery.totalCount = this.filterUnselStus.length
      this.unselectedStus = this.filterUnselStus.slice(this.unselStusListQuery.skipCount, this.unselStusListQuery
        .skipCount + this.unselStusListQuery.pageSize)
      this.unselectedListLoading = false
    },
    // 获取单页已选择学生数据
    getSelStus() {
      this.selStusListQuery.skipCount = (this.selStusListQuery.page - 1) * this.selStusListQuery.pageSize
      var reg = RegExp(this.selStusListQuery.keyword)
      this.filterSelStus = this.chooseStudent.filter(item => {
        if (this.selStusListQuery.classId && this.selStusListQuery.classId.length > 0) {
          return item.extraProperties.OUId === this.selStusListQuery.classId && (item.userName?.match(reg) || item.name?.match(reg))
        } else {
          return (item.userName?.match(reg) || item.name?.match(reg))
        }
        // if (this.selStusListQuery.classId !== [] && this.selStusListQuery.classId.length > 0) {
        //   return item.extraProperties.OUId === this.selStusListQuery.classId[1] && (item.userName?.match(reg) || item.name?.match(reg))
        // } else {
        //   return (item.userName?.match(reg) || item.name?.match(reg))
        // }
      })
      this.selStusListQuery.totalCount = this.filterSelStus.length
      this.selectedStus = this.filterSelStus.slice(this.selStusListQuery.skipCount, this.selStusListQuery
        .skipCount + this.selStusListQuery.pageSize)
      this.selectedListLoading = false
    },
    // 未选择学生中 选择将要添加的学生变化
    handleAddStudentChange(val) {
      this.currentAddStus = val
    },
    // 已选择学生中 选择将要移除的学生变化
    handleRemoveStudentChange(val) {
      this.currentRemoveStus = val
    },
    // 未选择学生搜索框变化
    unselStusSearchChange() {
      setTimeout(() => {
        this.getStus(0)
      }, 1500)
    },
    // 已选择学生搜索框变化
    selStusSearchChange() {
      setTimeout(() => {
        this.getStus(1)
      }, 1500)
    },
    // 未选择学生班级筛选变化
    handleUnselStusClassChange(val) {
      // if (this.unselStusListQuery.classId.length === 0) {
      //   this.unselStusListQuery.classId = []
      // }
      this.getStus(0)
    },
    // 已选择学生班级筛选变化
    handleSelStusClassChange(val) {
      // if (this.selStusListQuery.classId.length === 0) {
      //   this.selStusListQuery.classId = []
      // }
      this.getStus(1)
    },
    // 0获取未选择学生筛选数据 1 获取已选择学生筛选数据
    getStus(t) {
      if (t === 0) {
        this.unselStusListQuery.page = 1
        this.getUnselStus()
      } else {
        this.selStusListQuery.page = 1
        this.getSelStus()
      }
    },
    // 点击添加移除后 当前page - 1
    changePage(t) {
      if (t === 0) {
        // 添加
        if (this.unselStusListQuery.page > 1) {
          this.unselStusListQuery.page -= 1
        } else {
          this.unselStusListQuery.page = 1
        }
      } else {
        if (this.selStusListQuery.page > 1) {
          this.selStusListQuery.page -= 1
        } else {
          this.selStusListQuery.page = 1
        }
      }
    }
  }
}
</script>
<style scoped>
.mini_pagination {
  padding: 0 !important;
  position: absolute;
  bottom: 10px;
}
.student_box_card ::v-deep .el-card__body{
  height: 530px
}
</style>

<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="8" :md="8" :lg="6" :xl="4">
        <el-card class="box-card" style="max-height: 800px; overflow: auto">
          <el-tree :data="orgDatas" :props="defaultProps" highlight-current style="margin-top: 5px"
            @node-click="handleNodeClick" />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="16" :md="16" :lg="18" :xl="20">
        <el-card class="box-card">
          <div class="header_flex_box">
            <el-select v-model="listQuery.KnowledgeCategoryId" size="small" placeholder="选择资源分类"
              @change="handleRefreshList">
              <el-option label="全部" :value="''" />
              <el-option v-for="item in resourceCategoryList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索
            </el-button>
            <el-button :loading="permissionLoading" :disabled="permissionLoading" round size="small" type="success"
              icon="el-icon-check" @click="handleSubmitResourcePermission">确定授权</el-button>
          </div>
          <el-table ref="courseTable" v-loading="listLoading" :data="list" highlight-current-row height="620px"
            @sort-change="sortChange" @selection-change="handleResourceSelectedChange">
            <el-table-column type="selection" width="44" align="center" />
            <el-table-column label="资源封面" prop="knowledgeThumbnailUrl" sortable="knowledgeThumbnailUrl" width="100">
              <template slot-scope="{ row }">
                <el-image :src="row.knowledgeThumbnailUrl" class="resource-cover" fit="cover">
                  <div slot="error">
                    <div class="image-slot">
                      <i class="el-icon-picture-outline" />
                    </div>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="资源名称" prop="knowledgeName" sortable="knowledgeName" min-width="200" />
            <!-- <el-table-column label="资源编号" prop="number" sortable="number" width="100" />
            <el-table-column label="作者" prop="author" sortable="author" width="100" />
            <el-table-column label="资源时长" prop="duration" sortable="duration" width="100">
              <template slot-scope="{ row }">
                {{ row.duration | formatSecond }}
              </template>
            </el-table-column> -->

            <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
              <template slot-scope="{ row }">
                {{ row.creationTime | formatDateTime }}
              </template>
            </el-table-column>
            <el-table-column width="150">
              <template slot="header" slot-scope="{ row }">
                <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">允许学习
                </el-checkbox>
              </template>
              <template slot-scope="{ row }">
                <el-checkbox v-model="row.cloudLearn" @change="handleCloudLearnChange">允许学习
                </el-checkbox>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
            :limit.sync="listQuery.MaxResultCount" @pagination="getResourceList" />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :title="progressTitle" :visible="permissionLoading" :show-close="false" width="600px">
      <el-progress :percentage="progressValue" color="#409eff" />
    </el-dialog>
  </div>
</template>
<script>
import { classesUsers, classesData } from '@/api/user'
import {
  resourceCategoryList,
  resourceCenterList,
  resourcePermission
} from '@/api/resource'
import Pagination from '@/components/Pagination'
export default {
  name: 'ClassResourcePermission',
  components: {
    Pagination
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
        id: 'id'
        // isLeaf: "leaf"
      },
      // 班级树数据
      orgDatas: [],

      // 分类
      resourceCategoryList: [],

      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        KnowledgeCategoryId: '',
        FreeModel: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },

      isIndeterminate: false,
      checkAll: false,
      selectedResource: [],
      selectedClassId: null,
      selectedUsers: [],
      permissionLoading: false,
      progressValue: 0,
      progressTitle: '正在获取班级用户中，请等待...'
    }
  },
  created() {
    this.getTreeDatas()
    this.getResourceCategoryList()
    this.getResourceList()
  },
  methods: {
    async handleSubmitResourcePermission() {
      if (this.selectedResource.length === 0) {
        this.$message.warning('请选择至少一个资源')
        return
      }
      if (!this.selectedClassId) {
        this.$message.warning('请选择班级')
        return
      }
      this.progressTitle = '正在获取班级用户中，请等待...'

      this.selectedUsers = []
      var loopTime = []
      var successPermission = 0
      this.permissionLoading = true

      const maxCount = 500
      const totalRes = await classesUsers({ ClassId: this.selectedClassId, SkipCount: 0, MaxResultCount: maxCount })

      const times = totalRes.totalCount % maxCount === 0 ? totalRes.totalCount / maxCount : (Math.floor(totalRes.totalCount / maxCount) + 1)
      for (let i = 0; i < times; i++) {
        var data = {
          ClassId: this.selectedClassId,
          Filter: '',
          Sorting: 'creationtime desc',
          SkipCount: i * maxCount,
          MaxResultCount: maxCount
        }
        const res = await classesUsers(data)
        this.progressValue = parseInt((((i + 1) * maxCount / totalRes.totalCount) * 100).toFixed(0))
        res.items.forEach((sitem) => {
          this.selectedUsers.push({
            id: sitem.id,
            classId: sitem.extraProperties ? sitem.extraProperties.OUId : null,
            className: sitem.extraProperties ? sitem.extraProperties.OUName : '',
            userId: sitem.id,
            userName: sitem.userName,
            name: sitem.name
          })
        })
      }
      const count = 2000
      const len = this.selectedUsers.length % count === 0 ? this.selectedUsers.length / count : (Math.floor(this.selectedUsers.length / count) + 1)
      for (let i = 0; i < len; i++) {
        loopTime.push(i)
      }
      this.progressValue = 0
      this.progressTitle = '正在进行授权中，请等待...'
      for await (var item of this.selectedResource) {
        var form = {
          knowledgeCenterId: item.id,
          knowledgeCenterUsers: []
        }
        this.selectedUsers.forEach(sitem => {
          sitem.knowledgeCenterId = item.id
          sitem.knowledgeResourceId = item.knowledgeResourceId
          sitem.cloudLearn = item.cloudLearn
        })
        for await (var i of loopTime) {
          form.knowledgeCenterUsers = this.selectedUsers.slice(i * count, (i + 1) * count > this.selectedUsers.length ? this.selectedUsers.length : (i + 1) * count)
          await resourcePermission(form).then(res => {
            successPermission++
            this.progressValue = parseInt((successPermission / (len * this.selectedResource.length) * 100).toFixed(0))
          })
        }
      }
      this.$message.success('操作成功，数据正在后台导入中，请稍后查看结果')
      setTimeout(() => {
        this.$router.push({
          name: 'ResourceCenter'
        })
      }, 3000)
    },
    handleResourceSelectedChange(val) {
      this.selectedResource = val
    },
    handleCheckAllChange(val) {
      this.list.forEach((item, i) => {
        // this.selectedResource.forEach((sitem, j) => {
        //   if (item.id === sitem.id) {
        // this.$set(this.selectedResource, j, { ...sitem, cloudLearn: val })
        this.$set(this.list, i, { ...item, cloudLearn: val })
        //   }
        // })
      })
      this.list.forEach((item, i) => {
        // this.selectedCourse.forEach((sitem, j) => {
        //   if (item.id === sitem.id) {
        // this.$set(this.selectedCourse, j, { ...sitem, cloudLearn: val })
        this.$refs.courseTable.toggleRowSelection(item)

        //   }
        // })
      })
      // this.selectedStus = JSON.parse(JSON.stringify(this.selectedStus))
      this.isIndeterminate = false
    },
    handleCloudLearnChange(val) {
      // this.selectedStus = JSON.parse(JSON.stringify(this.selectedStus))
      const newArr = this.list.filter(item => item.cloudLearn === true)
      const checkedCount = newArr.length
      this.checkAll = checkedCount === this.list.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.list.length
    },
    async getTreeDatas() {
      const res = await classesData()
      this.orgDatas = res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
          // .map(ele => {
          //     return { id: ele.id, parentId: ele.parentId, label: ele.name}})
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, [])
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getResourceList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getResourceList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceList()
    },
    getResourceCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      resourceCategoryList(data).then((res) => {
        this.resourceCategoryList = res.items
      })
    },
    getResourceList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceCenterList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    handleNodeClick(data, node) {
      if (node.level === 1) {
        this.selectedClassId = null
      } else {
        this.selectedClassId = data.id
      }
    }
  }
}
</script>

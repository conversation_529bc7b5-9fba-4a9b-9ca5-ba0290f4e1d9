import axios from '@/axios'

// 题库
export function bankPackageList() {
  return axios.gets('/api/exams/questionBank/auth-packages')
}
// 知识点
export function bankPackageItemsList(data) {
  return axios.gets('/api/exams/questionBank/package-tasks', data)
}
// 根据题库知识点题目类型难易程度获取所有题目ID
export function questionIdsList(data) {
  return axios.gets('/api/exams/questionBank/compose-query', data)
}
// 题库树形结构
export function questionBankTree() {
  return axios.gets('/api/exams/questionBank/auth-packagechildren')
}

// 根据属性图节点树形获取题库题内容
export function questionBankList(data) {
  return axios.gets('/api/exams/questionBank', data)
}
// 查看题目
export function questionDetail(id) {
  return axios.gets(`/api/exams/questionBank/${id}`)
}

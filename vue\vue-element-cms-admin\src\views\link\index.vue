<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button size="small" round type="primary" icon="el-icon-plus" @click="handleEdit(0, 0)">添加</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
        <el-table-column label="排序" prop="sort" sortable="sort" width="100" />
        <el-table-column label="名称" prop="name" sortable="name" min-width="200" />
        <el-table-column label="链接" prop="url" sortable="url" show-overflow-tooltip />
        <el-table-column label="操作" width="220">
          <template slot-scope="{ row }">
            <el-button size="mini" round type="primary" icon="el-icon-edit" @click="handleEdit(1, row)">编辑</el-button>
            <el-button size="mini" round type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getList" />
    </el-card>
    <el-dialog :title="isEdit ? '编辑' : '添加'" :close-on-click-modal="false" :visible.sync="linkDialog" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">j
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item prop="url" label="链接">
          <el-input type="textarea" :rows="2" v-model="form.url" />
        </el-form-item>
        <el-form-item prop="sort" label="排序">
          <el-input-number v-model="form.sort" :min="1" step-strictly :step="1"></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleSaveSure">确 定</el-button>
        <el-button round @click="linkDialog = false">取 消</el-button>

      </span>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import { linkAdd, linkDelete, linkEdit, linkList } from '@/api/other'
export default {
  name: 'Link',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },

      linkDialog: false,
      isEdit: false,
      form: {
        sort: 1,
        name: '',
        url: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 1, max: 10, message: '长度应小于 10 个字符', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入链接', trigger: 'blur' },
          { min: 1, max: 200, message: '长度应小于 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {

    handleEdit(t, row) {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.form = {
        sort: 1,
        title: '',
        link: ''
      }
      this.isEdit = !!t
      if (this.isEdit) {
        this.form = JSON.parse(JSON.stringify(row))
      }
      this.linkDialog = true
    },
    handleSaveSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            linkEdit(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.linkDialog = false
              this.getList()
            }).catch(() => {
              this.$message.error('编辑失败')
            })
          } else {
            linkAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.linkDialog = false
              this.getList()
            }).catch(() => {
              this.$message.error('添加失败')
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        linkDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = false
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      linkList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }

  }
}
</script>

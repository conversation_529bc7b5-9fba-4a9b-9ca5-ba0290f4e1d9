<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-01 08:04:14
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-13 16:17:18
 * @FilePath: /vue-element-cms-admin/src/views/question-bank/components/TenantQuestionBank.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-row :gutter="15">
      <el-col :span="6">
        <el-card class="shadow_none card-padding-0">
          <div slot="header" class="clearfix">
            <el-button type="text" style="padding: 0;" @click="handleNodeClick()">全部</el-button>
            <el-button style="float: right;padding: 0;" type="text" @click="addCategoryDialog">添加分类</el-button>
          </div>
          <!-- <div solt="header" class="flex_between_box">
            <el-button
              type="text"
              style="margin-left: 23px"
              @click="handleNodeClick()"
            >全部</el-button>
            <el-button type="text" style="margin-right: 18px" @click="addCategoryDialog">添加</el-button>
          </div> -->
          <el-tree
            ref="tree1"
            v-loading="treeDataLoading"
            class="course-dir-tree"
            style="height: 811px;overflow: auto"
            :data="treeData"
            node-key="id"
            highlight-current
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="shadow_none">
          <div slot="header" class="flex_between_box">
            <span>{{ currentCategory }}</span>
            <div class="header_flex_box">
              <el-input
                v-model="listQuery.Filter"
                clearable
                size="small"
                placeholder="搜索..."
                style="width: 200px"
                class="filter-item"
                @keyup.enter.native="handleSearch"
              />
              <el-button
                class="filter-item"
                size="small"
                round
                type="success"
                icon="el-icon-search"
                @click="handleSearch"
              >搜索</el-button>
              <el-button
                :disabled="!showAddQuestion"
                round
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="handleAddQuestion"
              >添加题目</el-button>
              <el-button
                :disabled="!showAddQuestion"
                round
                type="primary"
                size="small"
                icon="el-icon-edit-outline"
                @click="handleBatchImportQuestion"
              >批量导题</el-button>
              <el-button round type="primary" size="small" icon="el-icon-top" @click="handleExcelImportClick">Excel批量导题
              </el-button>
              <!-- <el-upload ref="fileUpload" round class="upload-demo" action="" :on-change="handleChange"
                :on-remove="handleRemove" :on-exceed="handleExceed" :show-file-list="false"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                :auto-upload="false">
                <el-button :loading="importLoading" :disabled="importLoading" round size="small" icon="el-icon-top"
                  style="margin-left: 10px;" type="primary">Excel批量导题</el-button>
              </el-upload> -->
            </div>
          </div>
          <el-row>
            <el-col :span="3"> <span class="tagType">题型</span></el-col>
            <el-col :span="21"><el-radio-group v-model="listQuery.questionType" @change="radioChange">
              <el-radio :label="''" aria-checked="true">全部</el-radio>
              <el-radio :label="0">单选题</el-radio>
              <el-radio :label="1">多选题</el-radio>
              <el-radio :label="2">判断题</el-radio>
              <el-radio :label="3">填空题</el-radio>
              <el-radio :label="6">问答题</el-radio>
            </el-radio-group></el-col>

          </el-row>
          <el-row>
            <el-col :span="3">  <span class="tagType">难度</span></el-col>
            <el-col :span="21"><el-radio-group v-model="listQuery.difficulty" @change="radioChange">
              <el-radio :label="''" aria-checked="true">全部</el-radio>
              <el-radio :label="1">易</el-radio>
              <el-radio :label="2">偏易</el-radio>
              <el-radio :label="3">适中</el-radio>
              <el-radio :label="4">偏难</el-radio>
              <el-radio :label="5">难</el-radio>
            </el-radio-group></el-col>

          </el-row>
          <el-row v-for="tagType in tagTypeList">
            <el-col :span="3"><span class="tagType">{{ tagType.label }}</span></el-col>
            <el-col :span="21">
              <el-checkbox-group v-model="tagType.selectTag" @change="tagChange">
                <el-checkbox v-for="tag in tagType.tags" :label="tag.id">{{ tag.name }}</el-checkbox>
              </el-checkbox-group>
            </el-col>
          </el-row>
          <el-table v-loading="listLoading" :data="list" height="605px" highlight-current-row @sort-change="sortChange">
            <!-- <el-table-column
              type="index"
              width="50"
              label="序号"
            /> -->
            <el-table-column prop="questionType" label="类型" sortable="questionType" header-align="left" width="120px">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.questionType === 0" type="success" size="medium">单选题</el-tag>
                <el-tag v-if="scope.row.questionType === 1" size="medium">多选题</el-tag>
                <el-tag v-if="scope.row.questionType === 2" size="medium" type="warning">判断题</el-tag>
                <el-tag v-if="scope.row.questionType === 3" type="danger" size="medium">填空题</el-tag>
                <el-tag v-if="scope.row.questionType === 6" type="info" size="medium">问答题</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="questionStem" label="题干" sortable="questionStem" header-align="left" min-width="200">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handlePreviewQuestion(row)">{{
                  JSON.parse(row.questionStem).Title
                }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="difficulty" label="难度" sortable="difficulty" header-align="left" width="80px">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.difficulty === 1" type="success" size="medium">易</el-tag>
                <el-tag v-if="scope.row.difficulty === 2" type="success" size="medium">偏易</el-tag>
                <el-tag v-if="scope.row.difficulty === 3" type="warning" size="medium">适中</el-tag>
                <el-tag v-if="scope.row.difficulty === 4" type="danger" size="medium">偏难</el-tag>
                <el-tag v-if="scope.row.difficulty === 5" type="danger" size="medium">难</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="creationTime" label="创建时间" sortable="creationTime" header-align="left" width="160">
              <template slot-scope="{row}">
                <span>{{
                  row.creationTime | formatDatetime
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="" label="操作" header-align="left" width="200px">
              <template slot-scope="{ row }">
                <!-- <el-button
                  round
                  type="default"
                  size="mini"
                  icon="el-icon-view"
                  @click="handlePreviewQuestion(row)"
                >查看</el-button> -->
                <el-button round type="primary" size="mini" icon="el-icon-edit" @click="handleEditorQuestion(row)">编辑
                </el-button>
                <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleDeleteQuestion(row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="totalCount > 0"
            :total="totalCount"
            :page.sync="page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getQuestionBankList"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :title="isEdit == true ? '编辑分类' : '添加分类'"
      :close-on-click-modal="false"
      :visible.sync="categoryDialog"
      width="500px"
    >
      <el-form ref="categoryForm" :model="categoryForm" :rules="categoryFormRules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="categoryForm.name" type="text" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-debounce round type="primary" @click="handleSaveCategory()">确 定</el-button>
        <el-button round @click="categoryDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="isQuesEdit == true ? '编辑题目' : '添加题目'"
      :visible.sync="questionDialog"
      :close-on-click-modal="false"
      width="920px"
      top="5vh"
    >
      <question-add-or-edit
        ref="questionAddOrEdit"
        :data="questionData"
        :is-edit="isQuesEdit"
        @question-form="handleNewQuestionForm"
      />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="questionLoading" round type="primary" @click="handleQuestionSure()">确 定</el-button>
        <el-button round @click="questionDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="questionDetailDialog"
      class="questionDetailDialog"
      title="题目预览"
      :visible.sync="questionDetailDialog"
      width="950px"
    >
      <question-preview :data="questionDetailItem" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="questionDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="批量导题"
      :visible.sync="importQuestionDialog"
      :fullscreen="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      class="import-dialog"
    >
      <el-form v-if="showCategory" ref="categoryModel" :model="categoryModel" :rules="categoryRules" label-width="80px">
        <el-form-item label="选择分类" prop="categoryIds">
          <el-cascader
            v-model="categoryModel.categoryIds"
            filterable
            clearable
            :options="treeData"
            size="small"
            :props="{ value: 'id' }"
            @change="categoryHandleChange"
          />
        </el-form-item>
      </el-form>

      <iframe
        v-if="importQuestionDialog"
        id="iframe_box"
        ref="Import"
        class="iframe_box"
        src="../questionBank/portexc/Import.html"
      />
    </el-dialog>
    <el-dialog :title="progressTitle" :visible="excelImportDialog" :show-close="false" width="800px">
      <el-progress v-if="progressShow" :percentage="progressValue" color="#409eff" />
      <div class="header_flex_box">
        <el-button round size="small" icon="el-icon-bottom" type="primary">
          <a type="primary" href="/题库导入模板.xlsx">下载模板</a>
        </el-button>
        <el-upload
          ref="fileUpload"
          round
          class="upload-demo"
          action=""
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-exceed="handleExceed"
          :show-file-list="false"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
          :auto-upload="false"
        >
          <el-button
            :loading="importLoading"
            :disabled="importLoading"
            round
            size="small"
            icon="el-icon-top"
            style="margin-left: 10px;"
            type="primary"
          >Excel批量导题</el-button>
        </el-upload>
      </div>

      <el-table v-if="errorList.length" :data="errorList">
        <el-table-column label="试题类型" prop="试题类型" width="100" />
        <el-table-column label="一级分类" prop="一级分类" show-overflow-tooltip width="100" />
        <el-table-column label="二级分类" prop="二级分类" show-overflow-tooltip width="100" />
        <el-table-column label="三级分类" prop="三级分类" show-overflow-tooltip width="100" />
        <el-table-column label="试题题干" prop="试题题干" show-overflow-tooltip />

        <el-table-column label="错误信息" prop="errorInfo" width="160" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="excelImportDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import QuestionAddOrEdit from '@/components/QuestionAddOrEdit'
import QuestionPreview from '@/components/QuestionPreview'
import apiQuestionBank from '@/api/questionBank'
import apiQuesBankCategory from '@/api/questionBankCategory'

export default {
  name: 'TenantQuestionBank',
  components: {
    Pagination, QuestionAddOrEdit, QuestionPreview
  },
  data() {
    return {
      categoryFormRules: {
        name: [{
          required: true,
          message: '请输入分类名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ]
      },
      listQuery: {
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        questionType: '',
        difficulty: '',
        questionBankCategoryId: null,
        questionBankTagIds: ''
      },
      totalCount: 0,
      page: 1,
      list: [],
      listLoading: false,
      treeData: [],
      treeDataLoading: false,
      categoryDialog: false,
      categoryForm: {},
      isEdit: false,
      selectedTreeItemData: {},
      categoryEditDialog: false,
      isQuesEdit: false,
      questionData: {},
      questionDialog: false,
      questionLoading: false,
      currentCategory: '全部',
      questionDetailDialog: false,
      questionDetailItem: {},
      importQuestionDialog: false,
      // 不是二级节点就隐藏添加和批量导题
      showAddQuestion: false,
      categoryModel: {
        categoryIds: []
      },
      categoryRules: {
        categoryIds: [{ required: true, message: '请选择分类', trigger: 'change' }]
      },
      showCategory: true,

      excelImportDialog: false,
      importLoading: false,
      fileTemp: null,
      progressValue: 0,
      progressShow: false,
      progressTitle: 'Excel批量导入',
      errorList: [],
      // 标签查询
      tagTypeList: []
    }
  },
  beforeDestroy() {
    window.removeEventListener('bankSaveSuccess', this.bankSaveSuccess)
    window.removeEventListener('createQuesiton', this.importBankNext)
    window.removeEventListener('hiddenCategory', this.selectCategory)
  },
  mounted() {
    this.getCategorys()
    this.getQuestionBankList()
    // 导题 题库保存成功
    window.addEventListener('bankSaveSuccess', this.bankSaveSuccess)
    // 校验题库分类是否选择
    window.addEventListener('createQuesiton', this.importBankNext)
    // 确定已选择分类，隐藏分类
    window.addEventListener('hiddenCategory', this.selectCategory)
  },
  methods: {
    handleExcelImportQuestion() {

    },
    categoryHandleChange(val) {
      if (this.categoryModel.categoryIds.length > 1) {
        localStorage.setItem('categoryId', this.categoryModel.categoryIds[this.categoryModel.categoryIds.length - 1])
      } else {
        localStorage.removeItem('categoryId')
      }
    },
    handleNodeClick(data, node) {
      this.categoryModel.categoryIds = []
      if (data !== undefined) {
        if (!data.parentId) {
          this.showAddQuestion = false
        } else {
          this.showAddQuestion = true
        }
        this.listQuery.questionBankCategoryId = data.id
        this.currentCategory = data.label
        if (node.parent != null && node.parent.parent != null && node.parent.parent.level != 0) {
          this.categoryModel.categoryIds.push(node.parent.parent.data.id)
        }
        if (data.parentId != null) {
          this.categoryModel.categoryIds.push(data.parentId)
        }
        this.categoryModel.categoryIds.push(data.id)
      } else {
        this.listQuery.questionBankCategoryId = null
        this.currentCategory = '全部分类'
        this.hideAddQuestion = false
      }
      this.page = 1
      this.listQuery.questionBankTagIds = ''
      this.getQuestionBankList()
      this.getTagTypes()
    },
    handleSaveCategory() {
      this.$refs.categoryForm.validate((valid) => {
        if (valid) {
          if (!this.isEdit) {
            apiQuesBankCategory.createCategory(this.categoryForm).then(response => {
              this.$notify({ title: '成功', message: '保存成功', type: 'success', duration: 2000 })
              this.categoryDialog = false
              this.append(this.selectedTreeItemData, response)
              this.getCategorys()
            }).catch(() => {
            })
          } else {
            apiQuesBankCategory.updateCategory(this.categoryForm.id, this.categoryForm).then(response => {
              this.$notify({ title: '成功', message: '保存成功', type: 'success', duration: 2000 })
              this.categoryDialog = false
              this.selectedTreeItemData.label = response.name
              this.getCategorys()
            }).catch(() => {
            })
          }
        } else {
          return false
        }
      })
    },
    addCategoryDialog(treeItem) {
      this.isEdit = false
      this.selectedTreeItemData = treeItem
      this.categoryDialog = true
      this.categoryForm = { parentId: treeItem?.id }
      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.resetFields()
      }
    },
    updateCategoryDialog(treeItem) {
      this.isEdit = true
      this.selectedTreeItemData = treeItem
      this.categoryDialog = true
      this.categoryForm = { id: treeItem?.id, name: treeItem.label }
    },
    deleteCategory(node, row) {
      this.$confirm('是否要删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        apiQuesBankCategory.deleteCategory(row.id).then(responss => {
          this.remove(node, row)
          this.$notify({ title: '成功', message: '删除成功', type: 'success', duration: 2000 })
        })
      })
    },
    append(data, newData) {
      const newChild = { id: newData.id, label: newData.name, children: [] }
      if (newData.parentId === null) {
        this.treeData.push(newChild)
      } else {
        if (!data.children) {
          this.$set(data, 'children', [])
        }
        data.children.push(newChild)
      }
    },
    remove(node, data) {
      const parent = node.parent
      const children = parent.data.children || parent.data
      const index = children.findIndex(d => d.id === data.id)
      children.splice(index, 1)
    },
    renderContent(h, { node, data, store }) {
      if (node.level < 3) {
        return (
          <span class='custom-tree-node'>
            <span class='node-lable'>{node.label}</span>
            <span>
              <el-button class='normal_button' type='text' icon='el-icon-plus' size='mini' circle on-click={() => this.addCategoryDialog(data)} />
              <el-button class='normal_button' type='text' icon='el-icon-edit' size='mini' circle on-click={() => this.updateCategoryDialog(data)} />
              <el-button class='normal_button' type='text' icon='el-icon-delete' size='mini' circle on-click={() => this.deleteCategory(node, data)} />
            </span>
          </span>
        )
      } else {
        return (
          <span class='custom-tree-node'>
            <span class='node-lable'>{node.label}</span>
            <span>

              <el-button class='normal_button' type='text' icon='el-icon-edit' size='mini' circle on-click={() => this.updateCategoryDialog(data)} />
              <el-button class='normal_button' type='text' icon='el-icon-delete' size='mini' circle on-click={() => this.deleteCategory(node, data)} />
            </span>
          </span>)
      }
    },
    getCategorys() {
      this.treeDataLoading = true
      apiQuesBankCategory.getCategorys().then(response => {
        this.loadTree(response.items)
        this.treeDataLoading = false
      })
    },
    loadTree(items) {
      if (items === undefined) {
        return
      }
      this.treeData = []
      items.forEach((item) => {
        if (item.parentId === null) {
          var element = {
            id: item.id,
            label: item.name,
            parentId: item.parentId,
            children: []
          }
          element.hasChildren = false
          this.treeData.push(element)
        }
      })
      this.setChildren(this.treeData, items)
    },
    setChildren(roots, items) {
      roots.forEach((element) => {
        items.forEach((item) => {
          if (item.parentId === element.id) {
            if (!element.children) element.children = []
            element.children.push({
              id: item.id,
              label: item.name,
              parentId: item.parentId
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },
    handleSearch() {
      this.page = 1
      this.getQuestionBankList()
    },
    handleAddQuestion() {
      this.isQuesEdit = false
      this.questionData =
      {
        questionType: 0,
        questionStem: '{"Title":"请输入题干","Title_Imgs":null,"Type":0,"Options":[{"Order":1,"Title":"选项1","Images":null},{"Order":2,"Title":"选项2","Images":null},{"Order":3,"Title":"选项3","Images":null},{"Order":4,"Title":"选项4","Images":null}],"Remark":null}',
        answer: '{"OptionAnswers":[{"Order":1,"Title":"选项1"}],"JudgeAnswer":0,"OtherAnswer":[],"Remark":null}',
        difficulty: 1,
        categoryOptionVules: this.categoryModel.categoryIds
      }
      this.questionDialog = true
    },
    handlePreviewQuestion(row) {
      this.questionDetailItem = row
      this.questionDetailDialog = true
    },
    handleEditorQuestion(row) {
      this.isQuesEdit = true
      this.questionData = row
      this.questionData.categoryOptionVules = this.categoryModel.categoryIds
      this.questionDialog = true
    },
    handleDeleteQuestion(row) {
      if (!this.listQuery.questionBankCategoryId) {
        this.$message.warning('请选择分类')
        return
      }
      this.$confirm('此操作将永久删除该题目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        apiQuestionBank.deleteQuestionBank({ Id: row.id, CategoryId: this.listQuery.questionBankCategoryId }).then(response => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getQuestionBankList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleQuestionSure() {
      this.$refs.questionAddOrEdit.$emit('form-data')
    },
    handleNewQuestionForm(questionForm) {
      if (this.questionLoading) {
        return
      }
      this.questionLoading = true
      var formData = questionForm
      formData.questionType = questionForm.questionType
      formData.difficulty = questionForm.difficulty
      formData.questionBankCategoryId = questionForm.questionBankCategoryId
      var title_images = questionForm.questionStem.Title_Imgs
      var titleImgArr = []
      title_images.forEach(item => {
        titleImgArr.push(item.url)
      })
      if (titleImgArr) {
        questionForm.questionStem.Title_Imgs = titleImgArr.join(',')
        if (!questionForm.questionStem.Title_Imgs.length) {
          questionForm.questionStem.Title_Imgs = null
        }
      } else {
        questionForm.questionStem.Title_Imgs = null
      }
      if (questionForm.questionType !== 3 && questionForm.questionType !== 6) {
        var options = questionForm.questionStem.Options
        options.forEach(oItem => {
          if (oItem.Images) {
            var optionImgArr = []
            oItem.Images.forEach(item => {
              optionImgArr.push(item.url)
            })
            oItem.Images = optionImgArr.join(',')
            if (!oItem.Images.length) {
              oItem.Images = null
            }
          } else {
            oItem.Images = null
          }
        })
        formData.answer = JSON.stringify(questionForm.answer)
      } else {
        formData.answer = null
      }
      formData.questionStem = JSON.stringify(questionForm.questionStem)
      if (!this.isQuesEdit) {
        apiQuestionBank.createQuestionBank(formData).then(response => {
          this.$message({ message: '保存成功', type: 'success', showClose: true })
          this.getQuestionBankList()
          this.questionDialog = false
          this.questionLoading = false
        }).catch(() => {
          this.questionLoading = false
        })
      } else {
        apiQuestionBank.updateQuestionBank(this.questionData.id, formData).then(response => {
          this.$message({ message: '保存成功', type: 'success', showClose: true })
          this.getQuestionBankList()
          this.questionDialog = false
          this.questionLoading = false
        }).catch(() => {
          this.questionLoading = false
        })
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.handleFilter()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleFilter()
    },
    handleFilter() {
      this.page = 1
      this.getQuestionBankList()
    },
    getQuestionBankList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      apiQuestionBank.getQuestionBanks(this.listQuery).then(response => {
        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    radioChange() {
      this.getQuestionBankList()
    },
    tagChange() {
      this.listQuery.questionBankTagIds = ''
      for (var i = 0; i < this.tagTypeList.length; i++) {
        if (this.tagTypeList[i].selectTag.length > 0) {
          var _t = ''
          if (this.listQuery.questionBankTagIds.length > 0) {
            _t = this.listQuery.questionBankTagIds + ','
          }
          this.listQuery.questionBankTagIds = _t + this.tagTypeList[i].selectTag.toString()
        }
      }

      this.getQuestionBankList()
    },
    getTagTypes() {
      this.tagTypeList = []
      this.categoryModel.categoryIds.forEach(async(item) => {
        apiQuestionBank.tagTypes({
          MaxResultCount: 999,
          SkipCount: 0,
          Sorting: 'sort',
          QuestionBankCategoryId: item
        }).then(response => {
          response.items.forEach(async(item) => {
            var tags = await this.getTags(item.id)
            this.tagTypeList.push({
              label: item.name,
              value: item.id,
              selectTag: [],
              tags: tags
            })
          })
        })
      })
    },
    async getTags(typeId) {
      var tags = await apiQuestionBank.tags({
        MaxResultCount: 999,
        SkipCount: 0,
        Sorting: 'sort',
        QuestionBankTagTypeId: typeId
      })
      return tags.items
    },
    handleBatchImportQuestion() {
      this.showCategory = true
      this.importQuestionDialog = true
      if (this.categoryModel.categoryIds.length > 1) {
        localStorage.setItem('categoryId', this.categoryModel.categoryIds[this.categoryModel.categoryIds.length - 1])
        localStorage.setItem('userId', this.$store.getters.userId)
      } else {
        localStorage.removeItem('categoryId')
        localStorage.removeItem('userId')
      }
    },
    // 监听事件处理
    bankSaveSuccess(e) {
      if (e.detail === 'success') {
        this.importQuestionDialog = false
        this.$message.success('保存成功')
        this.getQuestionBankList()
      }
    },
    importBankNext(e) {
      if (this.$refs.categoryModel) {
        this.$refs.categoryModel.validate((valid) => {
          if (valid) {
            document.getElementById('iframe_box').contentWindow.postMessage(true, '*')
          } else {
            document.getElementById('iframe_box').contentWindow.postMessage(false, '*')
            return false
          }
        })
      }
    },
    selectCategory() {
      this.showCategory = false
    },
    handleChange(file, fileList) {
      this.importLoading = true
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.submitProgress = 0
          this.importfxx(0, this.fileTemp)
        } else {
          this.importUserLoading = false
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.importClassHourLoading = false
        this.importAchiveLoading = false
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    // 没啥用
    handleExceed() {
      this.importUserLoading = false
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    // 没啥用
    handleRemove(file, fileList) {
      this.fileTemp = null
    },
    handleExcelImportClick() {
      this.progressTitle = 'Excel批量导入'
      this.progressValue = 0
      this.excelImportDialog = true
    },
    importfxx(t, file) {
      this.errorList = []
      this.progressShow = true
      const _this = this
      this.file = file
      var reader = new FileReader()
      reader.onload = async function(e) {
        var binary = ''
        var wb // 读取完成的数据
        var bytes = new Uint8Array(reader.result)
        var length = bytes.byteLength
        var xlsx = require('xlsx')
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        wb = xlsx.read(binary, {
          type: 'binary'
        })
        _this.da = xlsx.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        if (_this.da.length > 0) {
          if (_this.da.length > 1000) {
            _this.$message.error('单次最多导入1000条数据')
            return
          }
          var tmp = {
            p_type: '2',
            questionBankCategoryId: null,
            question_list: [],
            userId: '0',
            welcome: '0'
          }
          _this.da.forEach((item, index) => {
            _this.progressTitle = '正在校验数据中，请等待...'
            _this.progressValue = parseInt(((index + 1) / _this.da.length * 100).toFixed(0))
            var err = '第' + index + '行，'
            var _question = {
              answer: null,
              difficulty: _this.difficultyWithChar(item['难易程度']),
              explain: item['试题解析'],
              option_list: [],
              type: _this.questionTypeWithChar(item['试题类型']),
              title: item['试题题干'],
              categoryName1: item['一级分类'],
              categoryName2: item['二级分类'],
              categoryName3: item['三级分类'],
              tags: []
            }
            for (const key in item) {
              if (key.indexOf('标签-') > -1) {
                _question.tags.push({
                  tagTypeName: key.replace('标签-', ''),
                  tagName: item[key]
                })
              }
            }
            if (!_question.difficulty) {
              _this.errorList.push({ ...item, errorInfo: err + '题目难度设置错误' })
              return
            }
            if (!_question.type) {
              _this.errorList.push({ ...item, errorInfo: err + '题目类型设置错误' })
              return
            }
            if (!_question.title) {
              _this.errorList.push({ ...item, errorInfo: err + '题目题干不能为空' })
              return
            }
            if (!_question.categoryName1) {
              _this.errorList.push({ ...item, errorInfo: err + '一级分类不能为空' })
              return
            }
            if (!_question.categoryName2) {
              _this.errorList.push({ ...item, errorInfo: err + '二级分类不能为空' })
              return
            }

            if (_question.type === 9 || _question.type === 6) {
              tmp.question_list.push(_question)
            } else {
              var _answers = (item['正确答案'])?.split('')
              for (let i = 0; i < 26; i++) {
                var _op = item['选项' + _this.charWithNumber(i + 1)]
                if (_op) {
                  _question.option_list.push({
                    title: _op,
                    selected: _answers.indexOf(_this.charWithNumber(i + 1)) !== -1 ? 1 : 0
                  })
                } else {
                  break
                }
              }
              if (_question.option_list.length < 1) {
                return _this.$message.error(err + '题目选项不能少于两个')
              }
              if (_question.type === 3) {
                if (_answers.length < 1) {
                  _this.errorList.push({ ...item, errorInfo: err + '多选题答案至少需要两个' })
                  return
                }
              } else if (_question.type === 2) {
                if (_answers.length !== 1) {
                  _this.errorList.push({ ...item, errorInfo: err + '单选题答案只能有一个' })
                  return
                }
              } else if (_question.type === 8) {
                if (_answers.length !== 1) {
                  _this.errorList.push({ ...item, errorInfo: err + '判断题答案只能有一个' })
                  return
                } else if (_question.option_list.length !== 2) {
                  _this.errorList.push({ ...item, errorInfo: err + '判断题选项只能有两个' })
                  return
                }
              }
              _question.answer = _answers.map(item => _this.numberWithChar(item))
              tmp.question_list.push(_question)
            }
          })
          if (_this.errorList.length) {
            _this.progressTitle = '数据错误'
            _this.progressShow = false
            _this.importLoading = false
          } else {
            _this.progressTitle = '正在导入试题中，请等待...'
          }
          if (!_this.errorList.length) {
            _this.progressValue = 0
            const count = 1000
            const len = tmp.question_list.length % count === 0 ? tmp.question_list.length / count : (Math.floor(tmp.question_list.length / count) + 1)
            var successCount = 0
            for (let i = 0; i < len; i++) {
              var form = {
                p_type: '2',
                questionBankCategoryId: null,
                question_list: tmp.question_list.slice(i * count, (i + 1) * count > tmp.question_list.length ? tmp.question_list.length : (i + 1) * count),
                userId: '0',
                welcome: '0'
              }

              await apiQuestionBank.impportQuertion(form).then(res => {
                successCount++
                _this.progressValue = parseInt((successCount / len * 100).toFixed(0))
              })
            }
            _this.$message.success('导入成功')
            _this.getCategorys()
            _this.getQuestionBankList()
            _this.importLoading = false
            _this.excelImportDialog = false
          }
        } else {
          this.$message.error('Excel未读取到数据')
        }
      }
      reader.readAsArrayBuffer(file)
    },
    charWithNumber(i) {
      switch (i) {
        case 1:
          return 'A'
        case 2:
          return 'B'
        case 3:
          return 'C'
        case 4:
          return 'D'
        case 5:
          return 'E'
        case 6:
          return 'F'
        case 7:
          return 'G'
        case 8:
          return 'H'
        case 9:
          return 'I'
        case 10:
          return 'J'
        case 11:
          return 'K'
        case 12:
          return 'L'
        case 13:
          return 'M'
        case 14:
          return 'N'
        case 15:
          return 'O'
        case 16:
          return 'P'
        case 17:
          return 'Q'
        case 18:
          return 'R'
        case 19:
          return 'S'
        case 20:
          return 'T'
        case 21:
          return 'U'
        case 22:
          return 'V'
        case 23:
          return 'W'
        case 24:
          return 'X'
        case 25:
          return 'Y'
        case 26:
          return 'Z'
        default:
          break
      }
    },
    numberWithChar(i) {
      switch (i) {
        case 'A':
          return 1
        case 'B':
          return 2
        case 'C':
          return 3
        case 'D':
          return 4
        case 'E':
          return 5
        case 'F':
          return 6
        case 'G':
          return 7
        case 'H':
          return 8
        case 'I':
          return 9
        case 'J':
          return 10
        case 'K':
          return 11
        case 'L':
          return 12
        case 'M':
          return 13
        case 'N':
          return 14
        case 'O':
          return 15
        case 'P':
          return 16
        case 'Q':
          return 17
        case 'R':
          return 18
        case 'S':
          return 19
        case 'T':
          return 20
        case 'U':
          return 21
        case 'V':
          return 22
        case 'W':
          return 23
        case 'X':
          return 24
        case 'Y':
          return 25
        case 'Z':
          return 26
        default:
          break
      }
    },
    difficultyWithChar(c) {
      if (c === '易') {
        return 1
      } else if (c === '偏易') {
        return 2
      } else if (c === '适中') {
        return 3
      } else if (c === '偏难') {
        return 4
      } else if (c === '难') {
        return 5
      } else {
        return null
      }
    },
    questionTypeWithChar(c) {
      if (c === '单选题') {
        return 2
      } else if (c === '多选题') {
        return 3
      } else if (c === '判断题') {
        return 8
      } else if (c === '填空题') {
        return 6
      } else if (c === '问答题') {
        return 9
      } else {
        return null
      }
    }
  }
}
</script>
<style  scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.iframe_box {
  width: 100%;
  height: 85vh;
  border: none;
}

.import-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 2px;
}

.el-card .el-row {
  position: relative;
  margin: 0px 0px 20px 0px;

  &:last-child {
    margin-bottom: 0;
  }
}
.tagType{
  margin-right: 20px;
  display: inline-block;
}
.el-checkbox-group {
    display: inline-block;
}
</style>

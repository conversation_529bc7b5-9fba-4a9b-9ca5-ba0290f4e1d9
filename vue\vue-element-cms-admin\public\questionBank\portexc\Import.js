﻿$(function () {
    var tdonline = 1;
    var order = 0;
    var request_url = new UrlSearch();
    var project_title = $("#title").val();
    if (request_url.title) {
        project_title = decodeURI(request_url.title);
    }

    if (p_type == "None") {
        p_type = 0;
    }

    var importPara = {
        difficulty: 1,
    };

    // 题型定义：[name: "[中文名]", en_name: "英文名"，type: "题型", custom_attr: 子题型属性{}]
    var question_type_obj_list =
        [
            { "name": "[单选题]", "en_name": "QUESTION_TYPE_SINGLE", "type": 2, "custom_attr": {} },
            { "name": "[多选题]", "en_name": "QUESTION_TYPE_MULTIPLE", "type": 3, "custom_attr": {} },
            { "name": "[填空题]", "en_name": "QUESTION_TYPE_BLANK", "type": 6, "custom_attr": { "blank_type": "single" } },
            { "name": "[多行填空题]", "en_name": "QUESTION_TYPE_BLANK", "type": 6, "custom_attr": {} },
            { "name": "[文本]", "en_name": "QUESTION_TYPE_TEXT", "type": 7, "custom_attr": {} },
            { "name": "[判断题]", "en_name": "QUESTION_TYPE_JUDGE", "type": 8, "custom_attr": {} },
            { "name": "[简答题]", "en_name": "QUESTION_TYPE_REPLY", "type": 9, "custom_attr": {} },
            { "name": "[表格题]", "en_name": "QUESTION_TYPE_TABLE", "type": 10, "custom_attr": {} },
            { "name": "[工作步骤]", "en_name": "QUESTION_TYPE_STEP", "type": 11, "custom_attr": {} }
        ];

    var DifficultyEnum_list =
        [
            { "name": "[易]", "type": 1 },
            { "name": "[偏易]", "type": 2 },
            { "name": "[适中]", "type": 3 },
            { "name": "[偏难]", "type": 4 },
            { "name": "[难]", "type": 5 },
        ];

    var diff_str = "";
    if (importPara.difficulty == 1) {
        diff_str = "[易]";
    }
    if (importPara.difficulty == 2) {
        diff_str = "[偏易]";
    }
    if (importPara.difficulty == 3) {
        diff_str = "[适中]";
    }
    if (importPara.difficulty == 4) {
        diff_str = "[偏难]";
    }
    if (importPara.difficulty == 5) {
        diff_str = "[难]";
    }

    // 使用codemirror编辑器
    var default_v = "1、在安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转（B）？" + diff_str + "\n" + "（A）45° " + "（B）90° " + "（C）120°" + "（D）180°\n" +
        "{{" +
        "这里填写答案解析1;" + "\n" +
        "这里填写答案解析2" +
        "}}" + "\n" + "\n" +
        "2、不符合服务理念定义的是（A）？" + diff_str + "[单选题]\n" + "A.挑选喜欢的客户\n" + "B.保证商品种类繁多\n" + "C.保证充足的商品补给\n" + "D.将店址选择在交通便利的地段\n" +
        "解析：这里填写不换行的答案解析" + "\n" + "\n" +
        "3、大众EA888曲轴皮带轮拆装时需要的专用工具有（ACD）？" + diff_str + "[多选题]\n" + "A、止动工具-T10355\n" + "B、装配工具-T10531/1\n" + "C、夹紧销T10531/2\n" + "D、夹紧销-T10531/3\n" +
        "\n" + "4、安装气缸盖固定螺栓时，扳手旋转90°，再一次旋转90° （√）" + diff_str + "[判断题]\n" +
        "\n" + "5、安装气缸盖固定螺栓时，扳手旋转45°，再一次旋转120° （×）" + diff_str + "[判断题]\n"+
        "\n" + "6、大众EA888曲轴皮带轮拆装时需要的专用工具有那些？" + diff_str + "[简答题]\n"+
        "\n" + "7、大众EA888曲轴皮带轮拆装时需要的专用工具有___,___" + diff_str + "[填空题]\n";

    var codem = CodeMirror(document.getElementById('editCon'), {
        mode: "",                 //模式
        value: default_v,         //初始值
        indentUnit: 4,
        lineWrapping: true,     //自动换行
        lineNumbers: true,       //添加行号
        fixedGutter: false,        //根据编辑器内容固定在左侧
    });

    codem.on("change", function () {
        mirror.dataSetFn();
    });

    var dset_order = { "question_list_order": [] };
    var dataSet = {
        "courseId": $("#courseid").val(), "dirId": $("#dirid").val(), "tasktype": $("#tasktype").val(), "allowredo": $("#allowredo").val(),
        "anony": $("#anony").val(), "timelimit": $("#timelimit").val(), "viewanswer": $("#viewanswer").val(), "quesbank": $("#quesbank").val(),
        "title": $("#title").val(), "welcome": "", "question_list": [], "type_id": request_url.project_func, "p_type": p_type
    };
    var widget_list = [];
    var mirror = {
        init: function () {
            this.dataSetFn();
        },
        dataSetFn: function () {
            var that = this;
            dataSet = {
                "courseId": $("#courseid").val(), "dirId": $("#dirid").val(), "tasktype": $("#tasktype").val(), "allowredo": $("#allowredo").val(),
                "anony": $("#anony").val(), "timelimit": $("#timelimit").val(), "viewanswer": $("#viewanswer").val(), "isDisorder": $("#isDisorder").val(), "quesbank": $("#quesbank").val(),
                "userId": $("#userid").val(),
                "title": $("#title").val(), "welcome": "", "question_list": [], "type_id": request_url.project_func, "p_type": p_type
            };
            dset_order = { "question_list_order": [] };
            this.delWidget();
            var line_list = codem.getValue().split('\n');
            if (line_list.join("").length === 0) {
                this.insetWidget(0, "请输入标题和题目");
            } else {
                line_list.push("line_end");
                var welcome = "";
                var question = {};
                var anyline = "";
                var anynum = 0;
                $.each(line_list, function (index, value) {
                    if (value === "" && anynum == 0) { return; }
                    if (dataSet.title === "") {
                        dataSet.title = value;
                    } else {
                        var type_dict = that.get_type(value);
                        if (type_dict.type) {
                            if (dataSet.question_list.length === 0) {
                                dataSet.welcome = welcome;
                            }
                            if (question.option_list) {
                                that.save_question(question);
                            }

                            //todo 判断题正确选项设置
                            var judgecheck = -1;
                            if (type_dict.type == 8) {   //判断题
                                if (that.Trim(type_dict.txt, "g").indexOf('(√)') >= 0 || that.Trim(type_dict.txt, "g").indexOf('（√）') >= 0) {
                                    judgecheck = 1;
                                    type_dict.txt = type_dict.txt.replace("√", " ");
                                } else if (that.Trim(type_dict.txt, "g").indexOf('(×)') >= 0 || that.Trim(type_dict.txt, "g").indexOf('（×）') >= 0) {
                                    judgecheck = 0;
                                    type_dict.txt = type_dict.txt.replace("×", " ");
                                }
                                else if ((that.Trim(type_dict.txt, "g").indexOf('(√)') >= 0 || that.Trim(type_dict.txt, "g").indexOf('（√）') >= 0) && (that.Trim(type_dict.txt, "g").indexOf('(×)') >= 0 || that.Trim(type_dict.txt, "g").indexOf('（×）') >= 0)) {
                                    judgecheck = -1;
                                    type_dict.txt = type_dict.txt.replace("√", " ");
                                    type_dict.txt = type_dict.txt.replace("×", " ");
                                }
                            }
                            // 填空题
                            if (type_dict.type == 6) {
                               type_dict.show_txt = ''
                               if(that.Trim(type_dict.txt, "g").indexOf('___') >= 0){
                                type_dict.show_txt = type_dict.txt.replace(/___/g, "<input type='text' class='blank_input'/>");
                               }
                            }
                            question = { "title": type_dict.txt, "type": type_dict.type, 'showTxt':type_dict.show_txt, "difficulty": type_dict.Difficulty, "en_name": type_dict.en_name, "custom_attr": type_dict.custom_attr, option_list: [], index_line: index, order: 0, "judgecheck": judgecheck, "answer": type_dict.answer, "answertxt": type_dict.answertxt, "explain": "" };
                        } else {
                            if (value != "line_end") {
                                if (!question.option_list) {
                                    welcome += type_dict.txt + ' ';
                                } else {
                                    var optxt = type_dict.txt.replace(/(^\s*)/g, "");
                                    optxt = optxt.replace(/(^\s*)|(\s*$)/g, "");//去除字符串前后空格

                                    var exp1 = optxt.indexOf("解析：");
                                    var exp2 = optxt.indexOf("解析:");
                                    if (exp1 >= 0 || exp2 >= 0) {
                                        if (exp1 >= 0) {
                                            var result = optxt.substr(exp1, optxt.length);
                                            optxt = optxt.replace(result, "");
                                            question.explain = result.replace("解析：", "");
                                        } else if (exp2 >= 0) {
                                            var result = optxt.substr(exp2, optxt.length);
                                            optxt = optxt.replace(result, "");
                                            question.explain = result.replace("解析:", "");
                                        }
                                    }
                                    else {
                                        if (optxt.indexOf("{{") >= 0) {
                                            if (optxt.indexOf("}}") >= 0) {
                                                anyline += optxt;
                                                question.explain = anyline.replace("{{", "").replace("}}", "");
                                                anyline = "";
                                                anynum = 0;
                                                return;
                                            }
                                            anyline += optxt + "<br/>";
                                            anynum = index + 1;
                                            return;
                                        }
                                        if (anynum == index) {
                                            if (optxt.indexOf("}}") >= 0) {
                                                anyline += optxt;
                                                question.explain = anyline.replace("{{", "").replace("}}", "");
                                                anyline = "";
                                                anynum = 0;
                                            } else {
                                                anyline += optxt + "<br/>";
                                                anynum = index + 1;
                                            }
                                            return;
                                        }
                                    }

                                    var oldtxt = optxt;
                                    optxt = that.Trim(optxt, "g");
                                    optxt = optxt.replaceAll('（', "(");
                                    optxt = optxt.replaceAll('）', ")");
                                    optxt = optxt.replaceAll('．', '.');

                                    //选项截取()
                                    var optionArray = [];
                                    if (optxt.indexOf('(A)') >= 0 || optxt.indexOf('（A）') >= 0) {
                                        var ary = [];
                                        if (optxt.indexOf('(A)') >= 0) { ary = optxt.split('(B)'); }
                                        else if (optxt.indexOf('（A）') >= 0) { ary = optxt.split('（B）'); }
                                        if (ary.length > 0) {
                                            optionArray.push(ary[0].substring(3, ary[0].length));
                                        }
                                    }
                                    if (optxt.indexOf('(B)') >= 0 || optxt.indexOf('（B）') >= 0) {
                                        var res = that.get_option("A", "B", "C", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(C)') >= 0 || optxt.indexOf('（C）') >= 0) {
                                        var res = that.get_option("B", "C", "D", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(D)') >= 0 || optxt.indexOf('（D）') >= 0) {
                                        var res = that.get_option("C", "D", "E", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(E)') >= 0 || optxt.indexOf('（E）') >= 0) {
                                        var res = that.get_option("D", "E", "F", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(F)') >= 0 || optxt.indexOf('（F）') >= 0) {
                                        var res = that.get_option("E", "F", "G", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(G)') >= 0 || optxt.indexOf('（G）') >= 0) {
                                        var res = that.get_option("F", "G", "H", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(I)') >= 0 || optxt.indexOf('（I）') >= 0) {
                                        var res = that.get_option("H", "I", "J", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(J)') >= 0 || optxt.indexOf('（J）') >= 0) {
                                        var res = that.get_option("I", "J", "K", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(K)') >= 0 || optxt.indexOf('（K）') >= 0) {
                                        var res = that.get_option("J", "K", "L", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(L)') >= 0 || optxt.indexOf('（L）') >= 0) {
                                        var res = that.get_option("K", "L", "M", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('(M)') >= 0 || optxt.indexOf('（M）') >= 0) {
                                        var res = that.get_option("L", "M", "N", optxt);
                                        optionArray.push(res);
                                    }

                                    //选项截取、.
                                    if (optxt.indexOf('A、') >= 0 || optxt.indexOf('A.') >= 0 || optxt.indexOf('A．') >= 0) {
                                        var ary = [];
                                        if (optxt.indexOf('A、') >= 0) { ary = optxt.split('B、'); }
                                        else if (optxt.indexOf('A.') >= 0) { ary = optxt.split('B.'); }
                                        else if (optxt.indexOf('A．') >= 0) { ary = optxt.split('B．'); }
                                        if (ary.length > 0) {
                                            optionArray.push(ary[0].substring(2, ary[0].length));
                                        }
                                    }
                                    if (optxt.indexOf('B、') >= 0 || optxt.indexOf('B.') >= 0 || optxt.indexOf('B．') >= 0) {
                                        var res = that.get_option_point("A", "B", "C", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('C、') >= 0 || optxt.indexOf('C.') >= 0 || optxt.indexOf('C．') >= 0) {
                                        var res = that.get_option_point("B", "C", "D", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('D、') >= 0 || optxt.indexOf('D.') >= 0 || optxt.indexOf('D．') >= 0) {
                                        var res = that.get_option_point("C", "D", "E", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('E、') >= 0 || optxt.indexOf('E.') >= 0 || optxt.indexOf('E．') >= 0) {
                                        var res = that.get_option_point("D", "E", "F", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('F、') >= 0 || optxt.indexOf('F.') >= 0 || optxt.indexOf('F．') >= 0) {
                                        var res = that.get_option_point("E", "F", "G", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('G、') >= 0 || optxt.indexOf('G.') >= 0 || optxt.indexOf('G．') >= 0) {
                                        var res = that.get_option_point("F", "G", "H", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('H、') >= 0 || optxt.indexOf('H.') >= 0 || optxt.indexOf('H．') >= 0) {
                                        var res = that.get_option_point("G", "H", "I", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('I、') >= 0 || optxt.indexOf('I.') >= 0 || optxt.indexOf('I．') >= 0) {
                                        var res = that.get_option_point("H", "I", "J", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('J、') >= 0 || optxt.indexOf('J.') >= 0 || optxt.indexOf('J．') >= 0) {
                                        var res = that.get_option_point("I", "J", "K", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('K、') >= 0 || optxt.indexOf('K.') >= 0 || optxt.indexOf('K．') >= 0) {
                                        var res = that.get_option_point("J", "K", "L", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('L、') >= 0 || optxt.indexOf('L.') >= 0 || optxt.indexOf('L．') >= 0) {
                                        var res = that.get_option_point("K", "L", "M", optxt);
                                        optionArray.push(res);
                                    }
                                    if (optxt.indexOf('M、') >= 0 || optxt.indexOf('M.') >= 0 || optxt.indexOf('M．') >= 0) {
                                        var res = that.get_option_point("L", "M", "N", optxt);
                                        optionArray.push(res);
                                    }

                                    if (optionArray.length > 0) {
                                        $.each(optionArray, function (index, value) {
                                            var selecteditem = 0;
                                            var option_obj = { 'title': optionArray[index], 'selected': selecteditem };
                                            question.option_list.push(option_obj);
                                        })
                                    }
                                    else {
                                        if (optxt != "") {
                                            var selecteditem = 0;
                                            var option_obj = { 'title': optxt, 'selected': selecteditem };
                                            question.option_list.push(option_obj);
                                        }
                                    }

                                }
                            } else {
                                question.type ? that.save_question(question) : dataSet.welcome = welcome;
                            }
                        }
                    }

                    if (($("#quesbank").val() == 1 || $("#quesbank").val() == 2) && dataSet.courseId <= 0) {
                        if (dataSet.question_list.length > 100) {
                            that.insetWidget(0, "每次导入题目最多支持100个");
                            dataSet.question_list = dataSet.question_list.slice(0, 100);
                            return false;
                        }
                    } else {
                        if (dataSet.question_list.length > 100) {
                            that.insetWidget(0, "每次导入题目最多支持100个");
                            dataSet.question_list = dataSet.question_list.slice(0, 100);
                            return false;
                        }
                    }

                });
                // debugger;
            }
            if (widget_list.length > 0) {
                codem.scrollTo(1, codem.getScrollInfo().top);
            }
            $.each(dataSet.question_list, function (index, value) {
                dataSet.question_list[index].title = dataSet.question_list[index].title.replaceAll('"', '“');
                dataSet.question_list[index].title = dataSet.question_list[index].title.replaceAll("'", "‘");
                if (dataSet.question_list[index].answer.length > 0) {
                    if (dataSet.question_list[index].answer.length > dataSet.question_list[index].option_list.length
                        || dataSet.question_list[index].answer[dataSet.question_list[index].answer.length - 1] > dataSet.question_list[index].option_list.length
                        || (dataSet.question_list[index].type == 2 && dataSet.question_list[index].answer.length > 1)) {
                        dataSet.question_list[index].answer = [];
                        var titles = dataSet.question_list[index].title;
                        var strs1 = titles.replace("（ ）", "（" + dataSet.question_list[index].answertxt + "）");
                        var strs2 = strs1.replace("( )", "(" + dataSet.question_list[index].answertxt + ")");
                        dataSet.question_list[index].title = strs2;
                    }
                }
            })
            var zxbox = document.getElementById('outbox').innerHTML;
            var conStr = juicer(zxbox, { "data": dataSet });
            $('.previewCon').html(conStr);
        },  // 生成数据Fn
        get_option: function (v1, v2, v3, optxt) {
            var ary = [];
            if (optxt.indexOf('(' + v2 + ')') >= 0) { ary = optxt.split('(' + v3 + ')'); }
            else if (optxt.indexOf('（' + v2 + '）') >= 0) { ary = optxt.split('（' + v3 + '）'); }
            if (ary.length > 0) {
                var bry = [];
                if (optxt.indexOf('(' + v1 + ')') >= 0) { bry = ary[0].split('(' + v1 + ')'); }
                else if (optxt.indexOf('（' + v1 + '）') >= 0) { bry = ary[0].split('（' + v1 + '）'); }
                if (bry.length > 0) {
                    var cry = [];
                    if (optxt.indexOf('(' + v2 + ')') >= 0) { cry = bry[1].split('(' + v2 + ')'); }
                    else if (optxt.indexOf('（' + v2 + '）') >= 0) { cry = bry[1].split('（' + v2 + '）'); }
                    if (cry.length > 0) {
                        return cry[1];
                    }
                } else {
                    return ary[0].substring(3, ary[0].length);
                }
            }
        }, //判断选项换行
        get_option_point: function (v1, v2, v3, optxt) {
            var ary = [];
            if (optxt.indexOf('' + v2 + '、') >= 0) { ary = optxt.split('' + v3 + '、'); }
            else if (optxt.indexOf('' + v2 + '.') >= 0) { ary = optxt.split('' + v3 + '.'); }
            else if (optxt.indexOf('' + v2 + '．') >= 0) { ary = optxt.split('' + v3 + '．'); }
            if (ary.length > 0) {
                var bry = [];
                if (optxt.indexOf('' + v1 + '、') >= 0) { bry = ary[0].split('' + v1 + '、'); }
                else if (optxt.indexOf('' + v1 + '.') >= 0) { bry = ary[0].split('' + v1 + '.'); }
                else if (optxt.indexOf('' + v1 + '．') >= 0) { bry = ary[0].split('' + v1 + '．'); }
                if (bry.length > 0) {
                    var cry = [];
                    if (optxt.indexOf('' + v2 + '、') >= 0) { cry = bry[1].split('' + v2 + '、'); }
                    else if (optxt.indexOf('' + v2 + '.') >= 0) { cry = bry[1].split('' + v2 + '.'); }
                    else if (optxt.indexOf('' + v2 + '．') >= 0) { cry = bry[1].split('' + v2 + '．'); }
                    if (cry.length > 0) {
                        return cry[1];
                    }
                } else {
                    return ary[0].substring(2, ary[0].length);
                }
            }
        }, //判断选项换行
        get_type: function (str) {
            var that = this;
            str = str.replace(/(^\s*)|(\s*$)/g, "");//去除字符串前后空格
            var type_dict = {};
            if (str.indexOf('[单选题]') >= 0 || str.indexOf('[多选题]') >= 0 || str.indexOf('[判断题]') >= 0 || str.indexOf('[文本]') >= 0 ||  str.indexOf('[填空题]') >= 0 || str.indexOf('[简答题]') >= 0) {
                for (var i = 0; i < question_type_obj_list.length; i++) {
                    var typeobj = question_type_obj_list[i];
                    if (str.slice(-typeobj.name.length) == typeobj.name) {
                        type_dict.txt = str.slice(0, str.length - typeobj.name.length);

                        //判断起始字符是否是 数字加、  
                        var startarray = type_dict.txt.split('、');
                        if (startarray != null && startarray.length > 1) {
                            var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                            if (r.test(startarray[0])) {
                                var replace = startarray[0] + '、';
                                type_dict.txt = type_dict.txt.replace(replace, '');
                            }
                        }
                        else {
                            var startarray = type_dict.txt.split('.');
                            if (startarray != null && startarray.length > 1) {
                                var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                                if (r.test(startarray[0])) {
                                    var replace = startarray[0] + '.';
                                    type_dict.txt = type_dict.txt.replace(replace, '');
                                }
                            }
                            else {
                                var starray = type_dict.txt.split('．');
                                if (starray != null && starray.length > 1) {
                                    var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                                    if (r.test(starray[0])) {
                                        var replace = starray[0] + '．';
                                        type_dict.txt = type_dict.txt.replace(replace, '');
                                    }
                                }
                            }
                        }

                        //判断是否包含括号
                        var qtitle = type_dict.txt;
                        var array = [];
                        var reg_en = /\(([^()]+)\)/g;
                        var reg_cn = /\（([^（）]+)\）/g;



                        while (r = reg_cn.exec(qtitle) || reg_en.exec(qtitle)) {
                            array.push(r[1]);
                        };

                        var answer = "";
                        if (array.length > 0) {
                            answer = array[array.length - 1];
                            if (qtitle.indexOf("()") >= 0 || qtitle.indexOf("（）") >= 0) {
                                answer = "";
                            }
                        }
                        if (array.length > 1 || that.checkRepeat(answer.replace(/(^\s*)|(\s*$)/g, "").toLocaleUpperCase())) {
                            answer = "";
                        }

                        var res_ans = [];
                        if (answer == "") {
                        }
                        else {
                            var Regx = /^[A-Za-z]*$/;
                            if (Regx.test(that.Trim(answer, "g"))) {
                                type_dict.txt = type_dict.txt.replaceAll(answer, " ");
                                if (answer.indexOf('A') >= 0 || answer.indexOf('a') >= 0) { res_ans.push(1); }
                                if (answer.indexOf('B') >= 0 || answer.indexOf('b') >= 0) { res_ans.push(2); }
                                if (answer.indexOf('C') >= 0 || answer.indexOf('c') >= 0) { res_ans.push(3); }
                                if (answer.indexOf('D') >= 0 || answer.indexOf('d') >= 0) { res_ans.push(4); }
                                if (answer.indexOf('E') >= 0 || answer.indexOf('e') >= 0) { res_ans.push(5); }
                                if (answer.indexOf('F') >= 0 || answer.indexOf('f') >= 0) { res_ans.push(6); }
                                if (answer.indexOf('G') >= 0 || answer.indexOf('g') >= 0) { res_ans.push(7); }
                                if (answer.indexOf('H') >= 0 || answer.indexOf('h') >= 0) { res_ans.push(8); }
                                if (answer.indexOf('I') >= 0 || answer.indexOf('i') >= 0) { res_ans.push(9); }
                                if (answer.indexOf('J') >= 0 || answer.indexOf('j') >= 0) { res_ans.push(10); }
                                if (answer.indexOf('K') >= 0 || answer.indexOf('k') >= 0) { res_ans.push(11); }
                                if (answer.indexOf('L') >= 0 || answer.indexOf('l') >= 0) { res_ans.push(12); }
                                if (answer.indexOf('M') >= 0 || answer.indexOf('m') >= 0) { res_ans.push(13); }
                                if (answer.indexOf('N') >= 0 || answer.indexOf('n') >= 0) { res_ans.push(14); }
                                if (answer.indexOf('O') >= 0 || answer.indexOf('o') >= 0) { res_ans.push(15); }
                                if (answer.indexOf('P') >= 0 || answer.indexOf('p') >= 0) { res_ans.push(16); }
                                if (answer.indexOf('Q') >= 0 || answer.indexOf('q') >= 0) { res_ans.push(17); }
                                if (answer.indexOf('R') >= 0 || answer.indexOf('r') >= 0) { res_ans.push(18); }
                                if (answer.indexOf('S') >= 0 || answer.indexOf('s') >= 0) { res_ans.push(19); }
                                if (answer.indexOf('T') >= 0 || answer.indexOf('t') >= 0) { res_ans.push(20); }
                                if (answer.indexOf('U') >= 0 || answer.indexOf('u') >= 0) { res_ans.push(21); }
                                if (answer.indexOf('V') >= 0 || answer.indexOf('v') >= 0) { res_ans.push(22); }
                                if (answer.indexOf('W') >= 0 || answer.indexOf('w') >= 0) { res_ans.push(23); }
                                if (answer.indexOf('X') >= 0 || answer.indexOf('x') >= 0) { res_ans.push(24); }
                                if (answer.indexOf('Y') >= 0 || answer.indexOf('y') >= 0) { res_ans.push(25); }
                                if (answer.indexOf('Z') >= 0 || answer.indexOf('z') >= 0) { res_ans.push(26); }
                            }
                        }

                        type_dict.answertxt = answer;
                        type_dict.answer = res_ans;

                        type_dict.type = typeobj.type;
                        type_dict.en_name = typeobj.en_name;
                        type_dict.custom_attr = typeobj.custom_attr;
                        break;

                    }

                }

            }
            else {
                //去除数字加.
                //判断起始字符是否是 数字加、
                var ishandle = 0;
                type_dict.txt = str;
                var startarray = type_dict.txt.split('、');
                if (startarray != null && startarray.length > 1) {
                    var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                    if (r.test(startarray[0])) {
                        var replace = startarray[0] + '、';
                        type_dict.txt = type_dict.txt.replace(replace, '');
                        ishandle = 1;
                    }
                    else {
                        var starray = type_dict.txt.split('.');
                        if (starray != null && starray.length > 1) {
                            var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                            if (r.test(starray[0])) {
                                var replace = starray[0] + '.';
                                type_dict.txt = type_dict.txt.replace(replace, '');
                                ishandle = 1;
                            }
                            else {
                                var starray = type_dict.txt.split('．');
                                if (starray != null && starray.length > 1) {
                                    var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                                    if (r.test(starray[0])) {
                                        var replace = starray[0] + '．';
                                        type_dict.txt = type_dict.txt.replace(replace, '');
                                        ishandle = 1;
                                    }
                                }
                            }
                        }
                        else {
                            var starray = type_dict.txt.split('．');
                            if (starray != null && starray.length > 1) {
                                var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                                if (r.test(starray[0])) {
                                    var replace = starray[0] + '．';
                                    type_dict.txt = type_dict.txt.replace(replace, '');
                                    ishandle = 1;
                                }
                            }
                        }
                    }
                }
                else {
                    var starray = type_dict.txt.split('.');
                    if (starray != null && starray.length > 1) {
                        var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                        if (r.test(starray[0])) {
                            var replace = starray[0] + '.';
                            type_dict.txt = type_dict.txt.replace(replace, '');
                            ishandle = 1;
                        }
                        else {
                            var starray = type_dict.txt.split('．');
                            if (starray != null && starray.length > 1) {
                                var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                                if (r.test(starray[0])) {
                                    var replace = starray[0] + '．';
                                    type_dict.txt = type_dict.txt.replace(replace, '');
                                    ishandle = 1;
                                }
                            }
                        }
                    }
                    else {
                        var starray = type_dict.txt.split('．');
                        if (starray != null && starray.length > 1) {
                            var r = /^\+?[1-9][0-9]*$/;　　//判断是否为正整数
                            if (r.test(starray[0])) {
                                var replace = starray[0] + '．';
                                type_dict.txt = type_dict.txt.replace(replace, '');
                                ishandle = 1;
                            }
                        }
                    }
                }
                if (ishandle == 1) {
                    //判断是否包含括号
                    var qtitle = type_dict.txt;
                    var array = [];
                    var reg_en = /\(([^()]+)\)/g;
                    var reg_cn = /\（([^（）]+)\）/g;

                    while (r = reg_cn.exec(qtitle) || reg_en.exec(qtitle)) {
                        array.push(r[1]);
                    };

                    var answer = "";
                    if (array.length > 0) {
                        answer = array[array.length - 1];
                        if (qtitle.indexOf("()") >= 0 || qtitle.indexOf("（）") >= 0) {
                            answer = "";
                        }
                    }
                    if (array.length > 1 || that.checkRepeat(answer.replace(/(^\s*)|(\s*$)/g, "").toLocaleUpperCase())) {
                        answer = "";
                    }

                    var res_ans = [];
                    if (answer == "") {
                    }
                    else {
                        var Regx = /^[A-Za-z]*$/;
                        if (Regx.test(that.Trim(answer, "g"))) {
                            type_dict.txt = type_dict.txt.replaceAll(answer, " ");
                            if (answer.indexOf('A') >= 0 || answer.indexOf('a') >= 0) { res_ans.push(1); }
                            if (answer.indexOf('B') >= 0 || answer.indexOf('b') >= 0) { res_ans.push(2); }
                            if (answer.indexOf('C') >= 0 || answer.indexOf('c') >= 0) { res_ans.push(3); }
                            if (answer.indexOf('D') >= 0 || answer.indexOf('d') >= 0) { res_ans.push(4); }
                            if (answer.indexOf('E') >= 0 || answer.indexOf('e') >= 0) { res_ans.push(5); }
                            if (answer.indexOf('F') >= 0 || answer.indexOf('f') >= 0) { res_ans.push(6); }
                            if (answer.indexOf('G') >= 0 || answer.indexOf('g') >= 0) { res_ans.push(7); }
                            if (answer.indexOf('H') >= 0 || answer.indexOf('h') >= 0) { res_ans.push(8); }
                            if (answer.indexOf('I') >= 0 || answer.indexOf('i') >= 0) { res_ans.push(9); }
                            if (answer.indexOf('J') >= 0 || answer.indexOf('j') >= 0) { res_ans.push(10); }
                            if (answer.indexOf('K') >= 0 || answer.indexOf('k') >= 0) { res_ans.push(11); }
                            if (answer.indexOf('L') >= 0 || answer.indexOf('l') >= 0) { res_ans.push(12); }
                            if (answer.indexOf('M') >= 0 || answer.indexOf('m') >= 0) { res_ans.push(13); }
                            if (answer.indexOf('N') >= 0 || answer.indexOf('n') >= 0) { res_ans.push(14); }
                            if (answer.indexOf('O') >= 0 || answer.indexOf('o') >= 0) { res_ans.push(15); }
                            if (answer.indexOf('P') >= 0 || answer.indexOf('p') >= 0) { res_ans.push(16); }
                            if (answer.indexOf('Q') >= 0 || answer.indexOf('q') >= 0) { res_ans.push(17); }
                            if (answer.indexOf('R') >= 0 || answer.indexOf('r') >= 0) { res_ans.push(18); }
                            if (answer.indexOf('S') >= 0 || answer.indexOf('s') >= 0) { res_ans.push(19); }
                            if (answer.indexOf('T') >= 0 || answer.indexOf('t') >= 0) { res_ans.push(20); }
                            if (answer.indexOf('U') >= 0 || answer.indexOf('u') >= 0) { res_ans.push(21); }
                            if (answer.indexOf('V') >= 0 || answer.indexOf('v') >= 0) { res_ans.push(22); }
                            if (answer.indexOf('W') >= 0 || answer.indexOf('w') >= 0) { res_ans.push(23); }
                            if (answer.indexOf('X') >= 0 || answer.indexOf('x') >= 0) { res_ans.push(24); }
                            if (answer.indexOf('Y') >= 0 || answer.indexOf('y') >= 0) { res_ans.push(25); }
                            if (answer.indexOf('Z') >= 0 || answer.indexOf('z') >= 0) { res_ans.push(26); }
                        }
                    }

                    if (res_ans.length > 1) {
                        type_dict.type = 3;
                        type_dict.en_name = "QUESTION_TYPE_MULTIPLE";
                        type_dict.custom_attr = {};
                    }
                    else if (res_ans.length == 1 || res_ans.length == 0) {
                        type_dict.type = 2;
                        type_dict.en_name = "QUESTION_TYPE_SINGLE";
                        type_dict.custom_attr = {};
                    }
                    else if (res_ans.length == 0 && (that.Trim(type_dict.txt, "g").indexOf('(√)') >= 0 || that.Trim(type_dict.txt, "g").indexOf('（√）') >= 0
                        || that.Trim(type_dict.txt, "g").indexOf('(×)') >= 0 || that.Trim(type_dict.txt, "g").indexOf('（×）') >= 0
                    )) {
                        //判断题
                        type_dict.type = 8;
                        type_dict.en_name = "QUESTION_TYPE_JUDGE";
                        type_dict.custom_attr = {};
                    }
                    // 简答题
                    // else if(){

                    // } 
                    // // 填空题
                    // else if () {
                        
                    // }
                    type_dict.answertxt = answer;
                    type_dict.answer = res_ans;
                }
            }

            if (type_dict.type === undefined) {
                type_dict.txt = str;
                type_dict.type = "";
                type_dict.en_name = "";
                type_dict.custom_attr = {};
            }

            // var reg_d = /\{.*\}/g; // 判断难易程度
            // var Difficulty_str = reg_d.exec(type_dict.txt)

            // 难易程度不为空时
            // var str_index = -1
            for (var i = 0; i < DifficultyEnum_list.length; i++) {
                if (type_dict.txt.indexOf(DifficultyEnum_list[i].name) >= 0) {
                    //str_index = type_dict.txt.indexOf(DifficultyEnum_list[i].name)
                    // debugger;
                    type_dict.Difficulty = DifficultyEnum_list[i].type;
                    type_dict.txt = type_dict.txt.replace(/(^\s*)|(\s*$)/g, "")
                    type_dict.txt = type_dict.txt.slice(0, type_dict.txt.length - DifficultyEnum_list[i].name.length);
                    break;
                }
            }
            // debugger;
            return type_dict;


        },  // 判断题型
        checkRepeat: function (str) {
            return /(.).*?\1/.test(str);
        },   //判断重复字母
        Trim: function (str, is_global) {
            var result;
            result = str.replace(/(^\s+)|(\s+$)/g, "");
            if (is_global.toLowerCase() == "g") {
                result = result.replace(/\s/g, "");
            }
            return result;
        },  //去除空格
        save_question: function (question) {
            var that = this;
            if ($.inArray(question.en_name, ["QUESTION_TYPE_SINGLE", "QUESTION_TYPE_MULTIPLE"]) != -1) {
               
                if (question.option_list.length === 0) {
                    that.insetWidget(question.index_line, "请输入至少一个选项");
                    lock = false;
                } else if (question.option_list.length > 26) {
                    question.option_list = question.option_list.slice(0, 26);
                    that.insetWidget(question.index_line, "选项不能超过26个");
                    lock = false;
                }
            } else if (question.en_name == "QUESTION_TYPE_BLANK") {
                if (question.option_list.length > 0) {
                    that.insetWidget(question.index_line, "填空题不需要设置选项");
                    lock = false;
                }
            }
            else if (question.en_name == "QUESTION_TYPE_JUDGE") {
                if (question.option_list.length > 0) {
                    that.insetWidget(question.index_line, "判断题不需要手动设置选项");
                    lock = false;
                }
                var option_obj1 = { 'title': '正确', 'selected': 0 };
                var option_obj2 = { 'title': '错误', 'selected': 0 };
                question.option_list.push(option_obj1);
                question.option_list.push(option_obj2);

            }

            if (question.en_name != "QUESTION_TYPE_TEXT") {
                dset_order.question_list_order.push(question);
                question.order = dset_order.question_list_order.length;
            }
            dataSet.question_list.push(question);
        },   // 保存题型及验证
        insetWidget: function (line, errorTxt) {
            var nd = document.createElement("div");
            nd.setAttribute('class', 'CodeMirror-linewidget');
            nd.setAttribute('cm-ignore-events', true);
            var nd_1 = document.createElement("div");
            nd_1.setAttribute('class', 'text-to-survey-editor-error');
            var t = document.createTextNode(errorTxt);
            nd_1.appendChild(t);
            nd.appendChild(nd_1);
            var xz = codem.addLineWidget(line, nd, { noHScroll: true });
            widget_list.push([xz, line]);
        }, // 插入提示语
        insetWidget_Option: function (line, errorTxt) {
            var nd = document.createElement("div");
            nd.setAttribute('class', ' CodeMirror-line ');
            var nd_1 = document.createElement("span");
            var t = document.createTextNode(errorTxt);
            nd_1.appendChild(t);
            nd.appendChild(nd_1);
            var xz = codem.addLineWidget(line, nd, { noHScroll: true });
            widget_list.push([xz, line]);
        },
        delWidget: function () {
            $.each(widget_list, function (i, j) {
                j[0].clear();
            });
            widget_list = [];
        },
        maptss: function (w, h, title, obj) {
            var con = $(obj).parent().html();
            var FixedTop = 240;
            if (h != "auto") {
                var zh = document.documentElement.clientHeight || document.body.clientHeight;
                FixedTop = (zh - h) / 2 + 50;
            }
            var wb = new jsbox({
                onlyid: "maptss",
                title: title,
                conw: w,
                conh: h,
                FixedTop: FixedTop,
                Opacity: .4,
                loads: true,
                content: con,
                range: true,
                mack: true
            }).show();
        }
    }
    mirror.init();

    // 预览页点击事件
    $(".QUESTION_TYPE_SINGLE").live("click", function () {
        $(this).parents(".options").find("i").removeClass("active");
        $(this).find("i").addClass('active');
    });
    $(".QUESTION_TYPE_MULTIPLE").live("click", function () {
        $(this).find("i").toggleClass('active');
    });
    $(".QUESTION_TYPE_JUDGE").live("click", function () {
        $(this).parents(".options").find("i").removeClass("active");
        $(this).find("i").addClass('active');
    });
    // 查看帮助
    $(".help_bt").live("click", function () {
        mirror.maptss(1000, 600, "格式要求", ".helpContx");
    });
    $(".help_lf").live("click", function () {
        mirror.maptss(1000, 600, "示例文档", ".helpCon");
    });
    window.addEventListener('message', function (messageEvent) {
        // 监听校验返回值 是否选择了分类 
        if (!messageEvent.data) {
            return
        }
        if (lock) {
            return false;
        }
        if (widget_list.length > 0) {
            mirror.maptss(420, "auto", "导入提示", ".erorr_prompt");
            return false;
        }
        //问题选项选中         
        var datatx = {}, optiontx = {};
        var i = 0;
        var isvalid = 1;
        $(".question_model").each(function () {
            datatx.index = i;
            var options = $(this).find(".options");
            var j = 0;
            var selected = 0;
            var sg_cnt = 0;
            $(this).find(".options").find(".option").each(function () {
                dataSet.question_list[i].explain = dataSet.question_list[i].explain.replaceAll('"', '“');
                dataSet.question_list[i].explain = dataSet.question_list[i].explain.replaceAll("'", "‘");
                if (dataSet.question_list[i].option_list[j] != null && dataSet.question_list[i].option_list[j].title != null) {
                    dataSet.question_list[i].option_list[j].title = dataSet.question_list[i].option_list[j].title.replaceAll('"', '“');
                    dataSet.question_list[i].option_list[j].title = dataSet.question_list[i].option_list[j].title.replaceAll("'", "‘");
                }
                if ($(this).find("i").hasClass('active')) {
                    if (dataSet.question_list[i].option_list[j] != null) {
                        dataSet.question_list[i].option_list[j].selected = 1;
                    }
                    selected = 1;
                    sg_cnt++;
                }
                j++;
            })
            if (dataSet.question_list[i].type == 2 && sg_cnt > 1) {
                isvalid = 0;
                var qt = i + 1;
                lock = false;
                loadMack({ off: 'on', Limg: 0, text: '第' + qt + '题为单选题，不能设置多个正确选项', set: 0 });
                setTimeout(function () { loadMack({ off: 'off', Limg: 0, text: '', set: 0 }) }, 2000);
                $(".previewCon").animate({ scrollTop: $(this).offset().top - 50 }, 1000);
                return false;
            }
            i++;
        })
        if (isvalid == 0) return;

        $("#create_project input[name=project_json]").val(JSON.stringify(dataSet));
        var reshtml = JSON.stringify(dataSet);
        reshtml = reshtml.replaceAll('<', '< ');
        var objs = JSON.parse(reshtml);
        objs.questionBankCategoryId = localStorage.getItem('categoryId')
        lock = true;
        loadMack({ off: 'on', Limg: 0, text: '题目生成中...', set: 0 });
        xhrPost('/api/exams/questionBankTenant/import', JSON.stringify(objs), function (data) {
            lock = false;
            $(".create_bt").unbind('click');
            $(window).unbind("beforeunload");
            localStorage.removeItem('keystamp');
            localStorage.setItem('keystamp', data);
            // 请求成功  通知Vue 隐藏分类Form
            window.top.dispatchEvent(new CustomEvent('hiddenCategory', {detail: true}))
            window.location.href = "../editexec/EditBank.html";
        }, function () {
            lock = false;
            loadMack({ off: 'off', Limg: 0, text: '创建失败', set: 0 });
            setTimeout(function () { loadMack({ off: 'on', Limg: 0, text: '创建失败,请检查文档格式', set: 0 }) }, 1000);
            setTimeout(function () { loadMack({ off: 'off', Limg: 0, text: '', set: 0 }) }, 2000);
        })
    })
    // 下一步
    var lock = false;
    $(".textProject .create_bt").click(function () {
        // 发送消息给Vue 点击下一步按钮了
        window.top.dispatchEvent(new CustomEvent('createQuesiton', { detail: 'success' }))

    });
    //返回
    $(".tccCon_a .go_back").live("click", function () {
        $(window).unbind("beforeunload");
        window.location.href = $(".head_nav .back").attr("go_url");
    });
    $(".tccCon_a .uniteC, .tccCon_a .close_bt").live("click", function () {
        $(".jsbox_close").click();
        if ($(this).hasClass("close_bt")) {
            codem.scrollTo(0, widget_list[0][1] * 21);
        }
    });
    $(window).bind("beforeunload", function () {
        return "您要放弃当前编辑的题目吗？";
    });
});
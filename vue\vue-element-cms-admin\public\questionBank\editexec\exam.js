﻿var exam = {
    //初始化
    init: function () {
        //
        this.dragFn();
        //
        this.sortFn();
        //
        this.fixFn();
        //
        this.menuFn();
        //
        this.titleEditFn();
        //
        this.listAllCtrlFn('.ui-questions-content-list', '.ui-up-btn', '.ui-down-btn', '.ui-clone-btn', '.ui-del-btn');
        //
        this.topicACtrlFn('.ui-questions-content-list', '.ui-add-item-btn', '.ui-batch-item-btn', '.ui-add-answer-btn');
        //
        this.moveTispFn('.ui-up-btn,.ui-down-btn,.ui-clone-btn,.ui-del-btn');
        this.moveTispFn('.ui-add-item-btn,.ui-batch-item-btn,.ui-add-answer-btn');

    },
    //拖拽
    dragFn: function () {
        var _this = this;
        var data = {}, addname = 0;
        $("#ui_sortable_exam li").draggable({
            /* containment:'#pageContentId',*/
            connectToSortable: '.ui-questions-content-list',
            cursorAt: { top: 18, left: 20 },
            helper: function (event) {
                addname++;
                data = {
                    type: $(this).children('a').attr('data-checkType'),//
                    name: 'q' + $(this).attr('data-uid') + '_' + addname,
                    //
                    itmetid: addname + parseInt(1000 * Math.random()),
                    items: [{
                        value: '0',
                        //
                        tid: addname + parseInt(1000 * Math.random())
                    }, {
                        value: '0',
                        //
                        tid: addname + parseInt(1000 * Math.random())
                    }]
                }
                
                return template($(this).attr('data-tempId'), data);
            },
            revert: 'invalid',
            start: function (event) {
                _this.titleDelFn();
            },
            drag: function (event) {
                //_this.removeAttr("style");
            },
            stop: function (event) {
                _this.orderFn($('.ui-questions-content-list'));
            }

        }).on('click', function (e) {
            addname++;
            data = {
                type: $(this).children('a').attr('data-checkType'),//
                //
                name: 'q' + $(this).attr('data-uid') + '_' + addname,
                //
                itmetid: addname + parseInt(1000 * Math.random()),
                items: [{
                    value: '0',
                    //
                    tid: addname + parseInt(1000 * Math.random())
                }, {
                    value: '0',
                    //
                    tid: addname + parseInt(1000 * Math.random())
                }]
            }
            $('.ui-questions-content-list').append(template($(this).attr('data-tempId'), data));
            _this.orderFn($('.ui-questions-content-list'));
            _this.sortFn();
            }).disableSelection();
    },
    //
    sortFn: function () {
        var _this = this;
        $('.ui-questions-content-list').sortable({
            handle: '.ui-drag-area',
            items: '>li',
            containment: '#pageContentId',
            opacity: 0.7,
            placeholder: 'ui-state-highlight',
            start: function (event) {
                exam.titleDelFn();
            },
            stop: function () {
                _this.orderFn($(this));
            },
            revert: 'invalid'
        });
    },
    //
    orderFn: function (obj) {
        obj.find('li.items-questions').each(function (i) {
            $(this).removeAttr('style');
            $(this).find('.module-menu h4').html("Q" + (i + 1));
        });
    },
    //
    fixFn: function () {
        var _this = this;
        $('#desktop_scroll').scroll(function () {
            _this.titleDelFn();
            var parentLeft = $('.exam-nav').parent().offset().left;
            if ($('.exam-nav').offset().top + 20 + $('.conditionItems').outerHeight() + $('.title').outerHeight() <= $(this).scrollTop()) {
                $('.exam-nav').css({ 'position': 'fixed', 'top': 0 + 'px', 'left': parentLeft + 'px' });
                $('.exam-nav').addClass('scrollCurr');
            } else {
                $('.exam-nav').removeAttr('style');
                $('.exam-nav').removeClass('scrollCurr');
            }

        });
    },
    //题目类型菜单
    menuFn: function () {
        $('.exam-item-title').on('click', function () {
            if ($(this).hasClass('curr')) {
                $(this).removeClass('curr');
                $(this).find('i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
                $(this).next('ul.exam-nav-list').stop().slideDown();
            } else {
                $(this).addClass('curr');
                $(this).find('i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
                $(this).next('ul.exam-nav-list').stop().slideUp();
            }
        });
    },
    //
    titleEditFn: function () {
        $(document).on('click', '.T_edit', function (event) {
            $('.cq-into-edit').remove();
            var data = {
                title: ''
            }
            if (!$('.cq-into-edit').size()) {
                $('body').append(template('drag_T_edit', data));
                $('.cq-into-edit').attr('data-gettid', $(this).attr('data-tid'));
            }
            if ($(this).hasClass('T_plugins')) {
                $('.cq-into-edit').append(template('T_edit_plugins', {}));
            }
            $('.cq-into-edit').css({
                'top': ($(this).offset().top - 1) + 'px',
                'left': ($(this).offset().left) + 'px',
                'width': $(this).outerWidth() + 'px',
            });
            if ($(this).hasClass('T-center')) {
                $('.cq-into-edit .cq-edit-title').css({
                    'text-align': 'center'
                });
            } else {
                $('.cq-into-edit .cq-edit-title').css({
                    'text-align': 'left'
                });
            }
            if ($(this).attr('data-font')) {
                $('.cq-into-edit .cq-edit-title').css({
                    'font-size': $(this).attr('data-font') + 'px'
                });
            } else {
                $('.cq-into-edit .cq-edit-title').css({
                    'font-size': ''
                });
            }
            $('.cq-into-edit .cq-edit-title').css({
                'min-height': $(this).height() + 'px',
                'padding-top': ($(this).outerHeight() - $(this).height()) / 2 + 'px',
                'padding-bottom': ($(this).outerHeight() - $(this).height()) / 2 + 'px'
            }).html($(this).html()).focus();

            $(document).one('click', function () {
                $('.cq-into-edit').remove();
            });
            $(document).on('click', '.cq-into-edit', function (e) {
                e.stopPropagation();
            });
            event.stopPropagation();
        });
        $(document).on('blur', '.cq-into-edit .cq-edit-title', function () {
            $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').html($('.cq-into-edit .cq-edit-title').html());
            var $itemInput = $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li').find('.input-check').find('input');
            if ($itemInput.size()) {
                $itemInput.val($('.cq-into-edit .cq-edit-title').html());
            }
        });
    },
    //
    titleDelFn: function () {
        if ($('.cq-into-edit').size()) {
            $('.T_edit[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').html($('.cq-into-edit .cq-edit-title').html());
            $('.cq-into-edit').hide();
        }

    },
    //
    moveTispFn: function (obj) {
        $(document).on('mousemove', obj, function (e) {
            var strTx = $(this).attr('data-tisp');
            var str = $('<div class="move-tisp-box"></div>');
            str.html(strTx);
            if (!$('.move-tisp-box').size()) {
                str.appendTo('body');
            }
            $('.move-tisp-box').css({ top: (e.pageY + 15) + 'px', left: (e.pageX + 15) + 'px' });
        });
        $(document).on('mouseout', obj, function (e) {
            $('.move-tisp-box').remove();
        });
    },
    //
    listAllCtrlFn: function (parentObj, upObj, downObj, cloneObj, delObj) {
        var _this = this;
        //
        $(document).on('click', parentObj + ' ' + upObj, function (e) {
            var $parentItems = $(this).closest('li.ui-module');
            if ($parentItems.prev('li.ui-module').size()) {
                $parentItems.insertBefore($parentItems.prev('li.ui-module'));
                _this.orderFn($(parentObj));
                _this.titleDelFn();
            } else {
                layer.msg('已经是顶部了');
            }
        });
        //
        $(document).on('click', parentObj + ' ' + downObj, function (e) {
            var $parentItems = $(this).closest('li.ui-module');
            if ($parentItems.next('li.ui-module').size()) {
                $parentItems.insertAfter($parentItems.next('li.ui-module'));
                _this.orderFn($(parentObj));
                _this.titleDelFn();
            } else {
                layer.msg('已经是底部了');
            }
        });
        //
        $(document).on('click', parentObj + ' ' + cloneObj, function (e) {
            var $parentItems = $(this).closest('li.ui-module');
            $parentItems.clone(true).insertAfter($parentItems);
            _this.orderFn($(parentObj));
            _this.titleDelFn();
        });
        //
        $(document).on('click', parentObj + ' ' + delObj, function (e) {
            abp.message.confirm(
                function (isConfirmed) {
                    if (isConfirmed) {
                        var $parentItems = $(this).closest('li.ui-module');
                        $parentItems.remove();
                        layer.msg('操作成功');
                        $('.move-tisp-box').remove();
                        _this.orderFn($(parentObj));
                        _this.titleDelFn();
                    }
                }
            );
        });
    },
    //
    topicACtrlFn: function (parentObj, addObj, batchAddObj, addAnswerObj) {
        //
        var $tid = 100 + parseInt(1000 * Math.random());
        $(document).on('click', parentObj + ' ' + addObj, function (e) {
            var $parentItems = $(this).closest('li.ui-module').find('.cq-unset-list');
            var $name = $.trim($parentItems.attr('data-nameStr'));
            $tid++;
            var data = {
                type: parseInt($parentItems.attr('data-checktype')),
                name: $name,
                index: $parentItems.children('li:last').index() + 1,
                items: [{ value: '0', tid: $tid }]
            }
            $parentItems.append(template('ui_additem_content', data));

        });
        //
        $(document).on('click', parentObj + ' ' + batchAddObj, function (e) {
            var $parentItems = $(this).closest('li.ui-module').find('.cq-unset-list');
            layer.msg('操作成功');
        });
        //
        $(document).on('click', parentObj + ' ' + addAnswerObj, function (e) {
            $(this).closest('li').css({ 'height': 'auto' });
            var $parentItems = $(this).closest('li.ui-module').find('.cq-unset-list');
            var $parentItems_blank = $(this).closest('li.ui-module').find('.describe-edit-content-blank');
            if (!$(this).closest('li.ui-module').find('.analysis_contx').size()) {
                $parentItems.after(template('analysis_tmp', {}));
                $parentItems_blank.after(template('analysis_tmp', {}));
            } else {
                $(this).closest('li.ui-module').find('.analysis_contx').remove();
            }
        });
    }
}

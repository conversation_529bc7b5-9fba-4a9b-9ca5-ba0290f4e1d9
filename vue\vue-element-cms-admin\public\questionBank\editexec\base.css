﻿* {
    margin: 0;
    padding: 0;
}

html, body {
    font-size: 12px;
    max-height: 960px;
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", "Source Han Sans SC", "Noto Sans CJK SC", "WenQuanYi Micro Hei", sans-serif;
}
/* -webkit-text-size-adjust:none; 解决Chrome浏览器最小只能显示12px字体的问题 */
body {
    -webkit-text-size-adjust: none;
    /*overflow: hidden;*/
    color: #333;
    background: #f6fafe;
}

a {
    text-decoration: none;
    color: inherit;
}

img {
    border: 0;
}

li {
    list-style: none;
}

input {
    border: 0;
    color: #333;
}

.clear {
    display: block;
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    clear: both;
    font-size: 0;
}

/* 去除链接及表单焦点虚线边框 */
a:focus, input[type=button], input[type=submit], input[type=image] {
    outline: none;
    cursor: pointer;
}

table {
    font-size: 12px;
}
/* 去除Chrome浏览器文本框选中后的黄色边框 */
input, button, select, textarea {
    outline: none;
    font-size: 12px;
}

textarea {
    resize: none;
}

/* 公用样式定义 */
h1, h2, h3, h4, h5, h6, h7 {
    font-size: 12px;
    font-weight: normal;
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", "Source Han Sans SC", "Noto Sans CJK SC", "WenQuanYi Micro Hei", sans-serif;
}

.relative {
    width: 100%;
    height: 100%;
    position: relative;
}

.left {
    float: left;
}

.right {
    float: right;
}

/*载入icons字体*/
@font-face {
    font-family: 'iconfont';
    src: url('font/iconfont.eot'); /* IE9*/
    src: url('font/iconfont.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('font/iconfont.woff') format('woff'), /* chrome、firefox */
    url('font/iconfont.ttf') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
    url('font/iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
}

.pagebox {
}


    /* 内容框架样式定义开始 */
    .pagebox font.red {
        color: #f00;
        font-weight: bold;
    }

    .pagebox font.gray {
        color: #aaa;
    }

    .pagebox font.green {
        color: #468c00;
        font-weight: bold;
    }

    .pagebox .title {
        padding: 20px;
    }

        .pagebox .title .name {
            padding-left: 10px;
            font-weight: bold;
            border-left: #fff 3px solid;
        }

        .pagebox .title .button {
            margin-left: 20px;
        }

            .pagebox .title .button a {
                padding: 2px 6px;
                margin: 0 5px;
                border: #fff 1px solid;
            }

                .pagebox .title .button a span {
                    padding-left: 28px;
                }

        .pagebox .title .function {
            margin-left: 20px;
        }

            .pagebox .title .function a:hover {
                text-decoration: underline;
            }

    .pagebox .area {
        padding: 5px 20px 20px 20px;
        margin-bottom: 20px;
    }

        .pagebox .area input[type=button] {
            margin: 0 5px;
            padding: 2px 6px;
            border: #fff 1px solid;
        }

        .pagebox .area table.data {
            width: 100%;
        }

            .pagebox .area table.data .text-right {
                text-align: right;
                /*position: relative;*/
                padding: 0 10px;
            }

            .pagebox .area table.data i:not(.icon-down) {
                display: inline-block;
                font-size: 14px;
            }

            .pagebox .area table.data .icon-schoolgirl {
                color: #FF7800;
            }

            .pagebox .area table.data .icon-schoolboy {
                color: #09c;
            }

            .pagebox .area table.data thead th {
                line-height: 50px;
                border-bottom: #fff 2px solid;
            }

            .pagebox .area table.data tbody td {
                text-align: center;
                line-height: 40px;
                border-bottom: #fff 1px dotted;
            }

                .pagebox .area table.data tbody td a:hover {
                    text-decoration: underline;
                }

            .pagebox .area table.data tbody tr:hover {
                background: #F9F9FA;
            }

            .pagebox .area table.data tbody tr:active {
                cursor: pointer;
            }

            .pagebox .area table.data .dataTabs-ctrol-down .dataTabs-ctrol-info {
                border: 1px dotted #fff;
                border-top: 0;
            }

            .pagebox .area table.data tbody tr:hover .dataTabs-ctrol-down .dataTabs-ctrol-info {
                background: #F9F9FA;
            }

            .pagebox .area table.data tfoot td {
                padding-top: 20px;
            }

                .pagebox .area table.data tfoot td.page {
                    text-align: right;
                }

                    .pagebox .area table.data tfoot td.page span, .pagebox .area table.data tfoot td.page a {
                        padding: 4px 6px;
                        margin: 0 5px;
                        border-bottom: #fff 2px solid;
                    }

                        .pagebox .area table.data tfoot td.page a:hover {
                            font-weight: bold;
                        }

                        .pagebox .area table.data tfoot td.page a.active {
                            font-weight: bold;
                        }

    .pagebox .respond {
        float: left;
        margin: 0 calc(15px*3/2) 20px 0;
        padding: 20px;
        width: calc(100%/3 - 55px);
    }

        .pagebox .respond:nth-child(3) {
            margin-right: 0;
        }

        .pagebox .respond h2 {
            padding-bottom: 5px;
            font-weight: bold;
            border-bottom: #fff 2px solid;
        }

        .pagebox .respond ul {
            margin-top: 10px;
        }

        .pagebox .respond li {
            height: 30px;
            line-height: 30px;
            border-bottom: #fff 1px dotted;
        }

            .pagebox .respond li h3 {
                float: left;
            }

            .pagebox .respond li span {
                float: right;
            }

            .pagebox .respond li font {
                padding: 0 5px;
            }


/* 统一模拟下拉菜单样式定义 */
.dc_select {
    display: inline-block;
    text-align: left;
}

    .dc_select > .dropbox > .down {
        float: right;
        width: 28px;
        display: inline-block;
        height: 28px;
        text-align: center;
        background-position: center center;
        line-height: 28px;
        margin-left: 0;
    }

.dc_down_list {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    border-top: 0px solid #ccc;
    /*position: absolute;*/
    z-index: 10000;
    background-color: white;
}

    .dc_down_list.up {
        border-bottom-width: 0px;
        border-top-width: 1px;
    }

    .dc_down_list li {
        margin-left: 0;
        height: 28px;
        line-height: 28px;
        font-size: 12px;
        border-top: 1px solid #fff;
        border-bottom: 1px solid #fff;
        cursor: pointer;
        overflow: hidden;
        word-break: keep-all;
        padding-left: 10px;
        padding-right: 10px; /*float:left;*/
    }

        .dc_down_list li.init {
            float: none;
        }

.dc_select > .dropbox {
    height: 28px;
    line-height: 28px;
    border: 1px solid #ccc;
    cursor: pointer;
}

    .dc_select > .dropbox .down {
        color: #ccc;
    }

    .dc_select > .dropbox > .text {
        color: black;
        padding-left: 10px;
        padding-right: 10px;
        overflow: hidden;
        word-break: keep-all;
        display: inline-block;
        line-height: 28px;
        margin-left: 0px;
        float: left;
    }

.dc_select.disabled > .dropbox > .text {
    background-color: #eee;
}

.dc_select.disabled > .dropbox {
    cursor: default;
}

.layui-layer-btn0 {
    background: #2e8ded !important;
}

.threed {
    color: #fafafa;
    letter-spacing: 0;
    text-shadow: 0px 1px 0px #999, 0px 1px 0px #888, 0px 1px 0px #777, 0px 1px 0px #666, 0px 1px 0px #555, 0px 1px 0px #444, 0px 1px 0px #333, 0px 1px 0px #001135;
}
.column-body {
    background: #f7f7f7;
}

.column-label {
    background: #ff7800;
    color: #fff;
}

table.group-topic-table th {
    background: #09c;
    color: #fff;
}

table.group-topic-table td .x-item {
    color: #FFA91B;
}

table.group-topic-table td.items-row-2,
.input-ctrol-items .in-tx {
    color: #0f94fd;
}

.exam-item-title {
    color: #333;
}

    .exam-item-title i {
        color: #FFA91B;
    }

table.group-record-data thead th {
    background: #fff2e0;
}

table.group-record-data tbody tr:nth-child(odd) {
    background: #f9f9f9;
}

table.group-record-data a.ctrl-item, table.group-record-data .ctrl-item {
    color: #666 !important;
}

table.group-record-data a {
    color: #09c;
}

.exam-section-title h1 .edit-btn {
    color: #09c;
}

.exam-section-abstract p.ab-tiem-1 {
    color: #767373;
}

.exam-section-abstract p.ab-tiem-2 {
    color: #666;
}

.topic-position-ctrol {
    background: #09c;
    color: #fff;
}

.topic-edit-items-list > li:hover {
    border-color: #09c !important;
}

.get-times-box span {
    background: #09c;
    color: #fff;
}

.edit-plug-box a:hover, .color-syle {
    color: #09c;
}

table.group-record-data a:hover, table.group-record-data .ctrl-item:hover {
    color: #09c !important;
}

.exam-tx-color, input.underline-tx {
    color: #09c !important;
    border-color: #09c !important;
}

.exam-tx-colorR {
    color: #f00 !important;
    border-color: #f00 !important;
}

textarea.exam-textarea, .exam-textarea {
    background: #f6f6f6 !important;
}

.answer-over-style label i {
    color: #ffa91b !important;
}

.verdict-active span, .judge-tx-info {
    color: #f00 !important;
}
.btnBlue, a.btnBlue {
    color: #fff;
    border: 1px solid #0f94fd;
    background-color: #0f94fd;
    border-radius:50%
}

    .btnBlue:hover, a.btnBlue:hover {
        color: #fff;
        background-color: #008fbf;
        border-color: #008fbf;
    }
.keylist{
    display: inline-block;
height: 24px;
line-height: 24px;
margin: 4px;
padding: 0 6px 0 8px;
background: #D7DCDF;
border-radius: 2px;
}
.delkey {
    float: right;
    margin-top: 5px;
    margin-left: 6px;
    cursor: pointer;
    display: inline-block;
    width: 13px;
    height: 13px;
    background-image: url('img/delete.png');
}
.blk_tb_set th{
    border-width:1px;
    border-style:solid;
    border-color:#ccc;
}
.blk_tb_set td{
    border-width:1px;
    border-style:solid;
    border-color:#ccc;
}
.blkselect {
 /*清除select默认样式*/
 appearance:none;
  -moz-appearance:none; 
 -webkit-appearance:none; 
 -ms-appearance:none;
  border:1px solid #CCC; 
  width:80px; 
  height:30px; /*自定义箭头的样式，记得背景一定要加 白色或其他*/ 
  background:url("../img/select.png") 
  no-repeat scroll right center #fff; /*ie下,原默认的箭头样式还是会显示，所以这里把自定义的样式给去除了*/ 
  background:#fff\9;
  color:#666;
  padding:8px; 
  outline:none;
}
<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-03-24 08:11:43
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-06-28 11:46:25
 * @FilePath: /vue-element-cms-admin/src/components/Tinymce/components/EditorImage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="upload-container">
    <el-button
      :style="{background:color,borderColor:color}"
      icon="el-icon-upload"
      size="mini"
      type="primary"
      @click=" dialogVisible=true"
    >
      上传图片
    </el-button>

    <el-dialog :visible.sync="dialogVisible">
      <lz-upload-images
        :limit="10"
        :file-size="500"
        :file-type="['jpg', 'png', 'jpeg']"
        :source-list="fileList"
        @response-fn="handleImageResponse"
        @remove-upload="handleRemoveFile"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          确定
        </el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
import LzUploadImages from '@/components/LzUploadImages'
export default {
  name: 'EditorSlideUpload',
  components: {
    LzUploadImages
  },
  props: {
    color: {
      type: String,
      default: '#1890ff'
    }
  },
  data() {
    return {
      dialogVisible: false,
      fileList: []
    }
  },
  methods: {

    handleSubmit() {
      if (this.fileList) {
        this.$emit('successCBK', this.fileList)
        this.fileList = []
        this.dialogVisible = false
      } else {
        this.$message.warning('请上传图片')
      }
    },
    // 上传文件成功回调
    handleImageResponse(url, fileForm) {
      this.fileList.push(fileForm)
    },
    // 上传文件删除
    handleRemoveFile(index) {
      this.fileList.splice(index, 1)
    }

  }
}

</script>

<style lang="scss" scoped>
  // .upload-container {
  //   .el-upload {
  //     width: 100%;

  //     .el-upload-dragger {
  //       width: 100%;
  //       height: 200px;
  //     }
  //   }
  // }

</style>

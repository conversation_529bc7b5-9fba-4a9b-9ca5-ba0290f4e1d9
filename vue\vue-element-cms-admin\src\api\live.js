/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-16 11:01:14
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-25 10:45:20
 * @FilePath: /vue-element-cms-admin/src/api/live.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/axios'

export function courseLiveList(data) {
  return axios.gets(`/api/lms/lives`, data)
}
export function addCourseLive(data) {
  return axios.posts(`/api/lms/lives`, data)
}
export function courseLivePublish(id) {
  return axios.posts(`/api/lms/lives/publish?id=` + id)
}
export function courseLivePlayBack(data) {
  return axios.posts(`/api/lms/lives/allow-play-back`, data)
}
export function editCourseLive(id, data) {
  return axios.puts(`/api/lms/lives/${id}`, data)
}
export function deletesCourseLive(id) {
  return axios.deletes(`/api/lms/lives/${id}`)
}
export function liveUserAll(data) {
  return axios.gets(`/api/lms/lives/users/all`, data)
}
export function liveUserList(data) {
  return axios.gets(`/api/lms/lives/users`, data)
}
export function liveUserBackRecord(data) {
  return axios.gets(`/api/lms/lives/user-back-records`, data)
}
export function liveDetailInfo(id) {
  return axios.gets(`/api/lms/lives/${id}`)
}

export function liveUserRecord_bjy(id) {
  return axios.posts(`/api/lms/lives/load-user-records?liveId=` + id)
}

export function liveUserBackRecord_bjy(data) {
  return axios.posts(`/api/lms/lives/user-back-records/get-bjy-back-records`, data)
}

export function generatePureVideo_bjy(id) {
  return axios.posts(`/api/lms/lives/generate-purevideo?liveId=` + id)
}

export function getPureVideo_bjy(id) {
  return axios.posts(`/api/lms/lives/get-purevideo?liveId=` + id)
}
// 替换回放
export function replaceBackVideo_bjy(data) {
  return axios.posts(`/api/lms/lives/replace-video`, data)
}

// 更新时长
export function replaceBackVideoTime_bjy(data) {
  return axios.posts(`/api/lms/lives/user-records`, data)
}

// 替换回放地址
export function replaceBackUrl(data) {
  return axios.posts(`/api/lms/lives/set-back-url`, data)
}

// 添加直播学员
export function addLiveUsers(data) {
  return axios.posts(`/api/lms/lives/users/add-users`, data)
}

// 删除直播学员
export function deleteLiveUsers(data) {
  return axios.posts(`/api/lms/lives/users/delete-users`, data)
}
export function deleteUnJoinLiveUsers(data) {
  return axios.posts(`/api/lms/lives/users/delete-unjoin-users?liveId=` + data)
}

//
export function liveExamInfo(data) {
  return axios.gets(`/api/lms/lives/exams`, data)
}

//
export function liveExamEdit(data) {
  return axios.posts(`/api/lms/lives/exams`, data)
}

//
export function updateClassHour(data) {
  return axios.posts(`/api/lms/lives/user-records/update-classhour?liveId=` + data)
}

export function publishLiveExam(data) {
  return axios.posts(`/api/lms/lives/exams/publish?liveId=` + data)
}

export function liveCategoryAdd(data) {
  return axios.posts(`/api/lms/lives/themes`, data)
}

export function liveCategoryList(data) {
  return axios.gets(`/api/lms/lives/themes`, data)
}

export function liveCategoryDelete(id) {
  return axios.deletes(`/api/lms/lives/themes/${id}`)
}

export function liveCategoryEdit(id, data) {
  return axios.puts(`/api/lms/lives/themes/${id}`, data)
}

export function liveCategoryAll() {
  return axios.gets(`/api/lms/lives/themes/all`)
}

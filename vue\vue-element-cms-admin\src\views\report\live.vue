<template>
  <div class="app-container">
    <el-card class="box-card">

      <div class="header_flex_box">
        <el-date-picker
          v-model="listQuery.StartTime"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          placeholder="选择日期时间"
          @change="handleChange"
        />
        <el-date-picker
          v-model="listQuery.EndTime"
          style="margin-left: 10px"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          placeholder="选择日期时间"
          @change="handleChange"
        />
        <el-select v-model="listQuery.HasClassHour" placeholder="请选择" style="margin-left: 10px" size="small">
          <el-option
            label="全部"
            value=""
          />
          <el-option
            :key="true"
            label="学时大于0"
            :value="true"
          />
        </el-select>
        <el-input v-model="listQuery.Filter" style="margin-left: 10px" clearable class="small_input" size="small" placeholder="输入名称搜索" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button round size="small" type="primary" icon="el-icon-download" @click="handExport">导出</el-button>
        <!-- <export-excel :header="['资源封面', '资源名称', '资源编号', '作者', '资源时长', '上架状态', '创建时间']"
              :filter-val="['thumbnailUrl', 'name', 'number', 'author', 'duration', 'status', 'creationTime']"
              :field="{ 4: [3], 5: ['未上架', '已上架'], 6: [2] }" :api-fn="resourceList" /> -->
      </div>

      <el-table v-loading="listLoading" :data="list" size="small" highlight-current-row @sort-change="sortChange">
        <el-table-column label="直播名称" prop="title" sortable="courseName" min-width="120" show-overflow-tooltip />
        <el-table-column label="课时" prop="classHour" sortable="className" width="100" />
        <el-table-column label="讲师" prop="lecturer" sortable="lecturer" width="100" show-overflow-tooltip />
        <el-table-column label="工号" prop="userName" sortable="userName" width="140" />
        <el-table-column label="姓名" prop="name" sortable="name" width="100" />

        <el-table-column label="部门" prop="classId" sortable="classId" min-width="150" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ cacheFindParent(row.classId).join('/') }}
          </template>
        </el-table-column>
        <el-table-column label="学时" prop="userClassHour" width="100" />
        <el-table-column label="开始时间" prop="startTime" sortable="startTime" width="150" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.startTime | formatDatetime }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endTime" sortable="endTime" width="150" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.endTime | formatDatetime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog title="正在准备导出中..." :visible="permissionLoading" :show-close="false" width="600px">
      <el-progress :percentage="progressValue" color="#409eff" />
    </el-dialog>
  </div>
</template>
<script>
import { liveReport } from '@/api/other'
import Pagination from '@/components/Pagination'
import { orgsData } from '@/api/user'
import { formatDatetime } from '@/utils/filters'
export default {
  name: 'ResourceList',
  components: {
    Pagination
  },

  data() {
    return {
      // 自建资源
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        StartTime: null,
        EndTime: null,
        HasClassHour: true,
        Sorting: 'LiveId,UserId',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      map: null,
      cacheDate: new Map(),

      progressValue: 0,
      permissionLoading: false
    }
  },
  computed: {
  },
  created() {
    this.loadClass()
  },
  methods: {
    async handExport() {
      var count2 = 100000
      if (this.listQuery.totalCount > count2) {
        this.$message.warning('数量超过' + count2 + ',不能导出')
        return
      }

      this.permissionLoading = true
      var totalList = []
      const count = 500
      var suc = 0
      const times = this.listQuery.totalCount % count === 0 ? this.listQuery.totalCount / count : (Math.floor(this.listQuery.totalCount / count) + 1)
      for (let i = 0; i < times; i++) {
        var data = {
          Filter: this.listQuery.Filter,
          StartTime: this.listQuery.StartTime,
          EndTime: this.listQuery.EndTime,
          HasClassHour: this.listQuery.HasClassHour,
          Sorting: this.listQuery.Sorting,
          SkipCount: i * count,
          MaxResultCount: count
        }
        const res = await liveReport(data)
        this.progressValue = parseInt((suc++ / times * 100).toFixed(0))
        totalList = totalList.concat(res.items)
      }
      totalList.forEach(item => {
        var orgs = this.cacheFindParent(item.classId)
        item.className1 = orgs.length ? orgs[0] : ''
        item.className2 = orgs.length > 1 ? orgs[1] : ''
        item.className3 = orgs.length > 2 ? orgs[2] : ''
        item.startTime = formatDatetime(item.startTime)
        item.endTime = formatDatetime(item.endTime)
      })
      this.permissionLoading = false
      import('@/vendor/Export2Excel').then((excel) => {
        const filterVal = ['title', 'classHour', 'lecturer', 'userName', 'name', 'className1', 'className2', 'className3', 'userClassHour', 'liveViewDuration', 'backPlayDuration', 'startTime', 'endTime']
        const header = ['直播名称', '课时', '讲师', '工号', '姓名', '一级部门', '二级部门', '三级部门', '学时', '直播时长', '回放时长', '开始时间', '结束时间']
        const data = this.formatJson(filterVal, totalList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: header,
          data,
          filename: '直播报表导出_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          if (j == 'liveViewDuration' || j == 'backPlayDuration') {
            return (v[j] / 60).toFixed(0)
          }
          return v[j]
        })
      )
    },
    handleChange(val) {
      if (val) {
        this.handleRefreshList()
      }
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      // if (this.listQuery.StartTime === null && this.listQuery.EndTime === null) {
      //   return
      // }
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      liveReport(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    async loadClass() {
      await orgsData().then(res => {
        // this.orgDatas = res.items
        this.map = new Map()
        res.items.forEach(item => {
          this.map.set(item.id, item)
        })
      })
    },
    cacheFindParent(id) {
      if (this.cacheDate.get(id)) {
        return this.cacheDate.get(id)
      }
      return this.findParent(id)
    },
    findParent(id) {
      const result = []
      if (!this.map) {
        return []
      }
      const d = this.map.get(id)
      if (d) {
        result.unshift(d?.displayName)
      }
      if (d?.parentId) {
        let next = this.map.get(d.parentId)
        while (next) {
          result.unshift(next.displayName)
          next = this.map.get(next.parentId)
        }
      }
      this.cacheDate.set(id, result)
      return result
    }
  }
}
</script>

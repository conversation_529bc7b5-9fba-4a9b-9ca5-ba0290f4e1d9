import * as login from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import axiosMethods from '../../axios/index.js'
const state = {
  token: getToken(),
  userId:'',
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  vmsTryTenantId: '',
  abpAuditLogging: [],
  tenantPermission: {
    isCreateCourse: false,
    isCreateQuestionBank: false
  },
  surname: '',
  userName: '',
  tenantName: ''
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_USERID: (state, userId) => {
    state.userId = userId
  },
  SET_USERNAME: (state, userName) => {
    state.userName = userName
  },
  SET_SURNAME: (state, surname) => {
    state.surname = surname
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_VmsTryTenantId: (state, vmsTryTenantId) => {
    state.vmsTryTenantId = vmsTryTenantId
  },
  SET_AbpAuditLogging: (state, abpAuditLogging) => {
    state.abpAuditLogging = abpAuditLogging
  },
  SET_TENANTPERMISSION: (state, info) => {
    state.tenantPermission.isCreateCourse = info.isCreateCourse
    state.tenantPermission.isCreateQuestionBank = info.isCreateQuestionBank
  },
  SET_TENANTNAME: (state, tenantName) => {
    state.tenantName = tenantName
  }

}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.token)
        setToken(data.token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  saveUserToken({ commit }, access_token) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', access_token)
      setToken(access_token)
      resolve()
    })
  },
  saveUserInfo({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      commit('SET_NAME', userInfo.name)
      commit('SET_USERID', userInfo.id)
      commit('SET_USERNAME', userInfo.userName)
      if (userInfo.surname === '0') {
        commit('SET_SURNAME', '0')
        commit('SET_AVATAR', require('@/assets/image/user-man.png'))
      } else if (userInfo.surname === '1') {
        commit('SET_SURNAME', '1')
        commit('SET_AVATAR', require('@/assets/image/user_woman.png'))
      } else {
        commit('SET_AVATAR', require('@/assets/image/user_n.png'))
      }

      commit('SET_SURNAME', userInfo.surname)
      resolve()
    })
  },
  userLogin({ commit }, data) { // 用户登录
    return new Promise((resolve, reject) => {
      axiosMethods.instancePosts('/connect/token', data)
        .then(response => {
          commit('SET_TOKEN', response.access_token)
          setToken(response.access_token)
          resolve()
        }).catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  getInfo({ commit }) {
    return new Promise((resolve, reject) => {
      axiosMethods.getUserInfo('/connect/userinfo')
        .then(response => {
          commit('SET_NAME', response.name)
          commit('SET_SUB', response.sub)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
    })
  },
  getPermissions({ commit }, role) {
    return new Promise((resolve, reject) => {
      axiosMethods.getPermissions('/api/abp/application-configuration')
        .then(response => {
          const data = {}
          data.roles = []
          for (var role in response.auth.grantedPolicies) {
            // if(response.auth.grantedPolicies[role]==true){
            // }
            data.roles.push(role)
          }
          if (!data.roles || data.roles.length <= 0) {
            reject('该用户没有任何权限！')
          }
          commit('SET_ROLES', data.roles)
          if (response.setting.values['VMSManagement.DefaultTenantId'] !== undefined) {
            commit('SET_VmsTryTenantId', response.setting.values['VMSManagement.DefaultTenantId'])
          }
          if (response.localization.values['AbpAuditLogging'] !== undefined) {
            commit('SET_AbpAuditLogging', response.localization.values['AbpAuditLogging'])
          }
          if (response.currentUser !== undefined) {
            commit('SET_NAME', response.currentUser.name)
            commit('SET_USERID', response.currentUser.id)
            commit('SET_USERNAME', response.currentUser.userName)

            if (response.currentUser.surName === '0') {
              commit('SET_SURNAME', '0')
              commit('SET_AVATAR', require('@/assets/image/user-man.png'))
            } else if (response.currentUser.surName === '1') {
              commit('SET_SURNAME', '1')
              commit('SET_AVATAR', require('@/assets/image/user_woman.png'))
            } else {
              commit('SET_AVATAR', require('@/assets/image/user_n.png'))
            }

            commit('SET_SURNAME', response.currentUser.surName)
          }
          //

          resolve(data)
        }).catch(error => {
          reject(error)
        })
    })
  },
  getTenantPermissions({ commit }, role) {
    return new Promise((resolve, reject) => {
      axiosMethods.getPermissions('/api/cms/course/tenantPermission/current')
        .then(response => {
          const data = {
            isCreateCourse: response.isCreateCourse,
            isCreateQuestionBank: response.isCreateQuestionBank
          }
          commit('SET_TENANTPERMISSION', data)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
    })
  },
  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      // logout(state.token).then(() => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resetRouter()

      // reset visited views and cached views
      // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
      dispatch('tagsView/delAllViews', null, { root: true })

      resolve()
      // }).catch(error => {
      //   reject(error)
      // })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },
  saveTenantname({ commit }, info) {
    return new Promise((resolve, reject) => {
      commit('SET_TENANTNAME', info)
      resolve()
    })
  },
  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      const { roles } = await dispatch('getInfo')

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })

      // dynamically add accessible routes
      router.addRoutes(accessRoutes)

      // reset visited views and cached views
      dispatch('tagsView/delAllViews', null, { root: true })

      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

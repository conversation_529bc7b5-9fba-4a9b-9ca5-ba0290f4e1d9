<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" v-permission="['AbpTenantManagement.Tenants']"  @click="handleSetLineChartData('tenant')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            租户数量
          </div>
          <count-to :start-val="0" :end-val="tenantNum" :duration="1000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" v-permission="['AbpIdentity.Users']" @click="handleSetLineChartData('user')">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="iconuser" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            用户数量
          </div>
          <count-to :start-val="0" :end-val="userNum" :duration="1000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" v-permission="['VMSManagement.TrialApplication']" @click="handleSetLineChartData('apply')">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="apply" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
             待处理申请
          </div>
          <count-to :start-val="0" :end-val="applyNum" :duration="1000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" v-permission="['AppUserManagement.Organization']" @click="handleSetLineChartData('author')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="author" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待处理授权
          </div>
          <count-to :start-val="0" :end-val="authorNum" :duration="1000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'
import permission from "@/directive/permission/index.js";
export default {
  data() {
    return {
      listQuery: {
        Filter: "",
        Sorting: "creationTime desc",
        SkipCount: 0,
        MaxResultCount: 1,
      },
      tenantNum: 0,
      userNum: 0,
      applyNum: 0,
      authorNum: 0
    }
  },
  components: {
    CountTo
  },
  directives: { permission },
  created() {
    this.getTenantNum();
    this.getUserNum();
    this.getApplyNum();
    this.getauthorNum();
  },
  methods: {
    getTenantNum() {
      this.$axios
        .gets("/api/apptenant/tenants",this.listQuery)
        .then((response) => {
          this.tenantNum = response.totalCount;
        });
    },

    getUserNum() {
      this.$axios.gets("/api/appuser/user",this.listQuery).then((response) => {
        this.userNum = response.totalCount;
      });
    },
    getApplyNum() {
      this.$axios
        .gets("/api/vms/trial",this.listQuery)
        .then((Response) => {
          this.applyNum = Response.totalCount;
        })
        .catch(() => {});
    },
    getauthorNum() {
      this.$axios
        .gets("/api/apptenant/tenants",this.listQuery)
        .then((response) => {
          this.authorNum = response.totalCount;
        });
    },
    handleSetLineChartData(type) {
      if(type === "tenant"){
        this.$router.push({
          name: "Tenant",
        });
      }
      if(type === "user"){
        this.$router.push({
          name: "User",
        });
      }
      if(type === "apply"){
        this.$router.push({
          name: "apply",
        });
      }
      if(type === "author"){
        this.$router.push({
          name: "organ-list",
        });
      }
      
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>

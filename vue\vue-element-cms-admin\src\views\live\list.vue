<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-radio-group v-model="listQuery.LiveStreamStatue" size="small" @change="handleRefreshList">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">未开始</el-radio-button>
          <el-radio-button :label="1">进行中</el-radio-button>
          <el-radio-button :label="2">已结束</el-radio-button>
        </el-radio-group>
        <el-select
          v-model="listQuery.liveThemeId"
          clearable
          placeholder="选择直播分类"
          size="small"
          @change="handleRefreshList"
        >
          <!-- <el-option label="资源分类" :value="''" /> -->
          <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button
          v-permission="['LiveManagement.LiveStreams.Create']"
          size="small"
          round
          type="primary"
          icon="el-icon-plus"
          @click="handleCourseLiveEdit(0, 0)"
        >添加</el-button>
        <export-excel
          :header="['封面', '标题', '直播规模', '时长', '课时', '开始时间', '结束时间', '状态']"
          :filter-val="['coverImage', 'title', 'userCount', 'timeLong', 'classHour', 'startTime', 'endTime', 'liveStreamStatue']"
          :field="{ 5: [2], 6: [2], 7: ['未开始', '进行中', '已结束'] }"
          :api-fn="courseLiveList"
        />
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
        <el-table-column label="封面" sortable="coverImage" width="150">
          <template slot-scope="{ row }">
            <el-image :src="row.coverImage" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="标题" prop="title" sortable="title" min-width="200">
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleLiveDetail(row)">{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="直播规模" prop="userCount" sortable="userCount" width="100">
          <template slot-scope="{ row }">
            {{ row.userCount }} 人
          </template>
        </el-table-column>
        <el-table-column label="时长" prop="timeLong" sortable="timeLong" width="100">
          <template slot-scope="{ row }">
            {{ row.timeLong }} 分钟
          </template>
        </el-table-column>
        <el-table-column label="课时" prop="classHour" sortable="classHour" width="100">
          <template slot-scope="{ row }">
            {{ row.classHour }}
          </template>
        </el-table-column>
        <el-table-column label="开始时间" prop="startTime" sortable="startTime" width="160">
          <template slot-scope="{ row }">
            {{ row.startTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endTime" sortable="endTime" width="160">
          <template slot-scope="{ row }">
            {{ row.endTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="liveStreamStatue" width="80">
          <template slot-scope="{ row }">
            <el-tag v-if="row.liveStreamStatue === 0" type="primary" size="mini">未开始</el-tag>
            <el-tag v-if="row.liveStreamStatue === 1" type="success" size="mini">进行中</el-tag>
            <el-tag v-if="row.liveStreamStatue === 2" type="info" size="mini">已结束</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400">
          <template slot-scope="{ row }">
            <el-button
              v-if="!row.publish"
              v-permission="['LiveManagement.LiveStreams.Update']"
              size="mini"
              round
              type="warning"
              icon="el-icon-s-promotion"
              @click="handleCourseLivePublish(row)"
            >{{ row.publish ? '取消发布' :
              '发布'
            }}</el-button>
            <el-button
              v-permission="['LiveManagement.LiveStreams.Update']"
              size="mini"
              round
              type="primary"
              icon="el-icon-edit"
              :loading="userLoading"
              @click="handleCourseLiveEdit(1, row)"
            >编辑</el-button>
            <el-button
              v-if="row.liveStreamStatue === 2"
              v-permission="['LiveManagement.LiveStreams.Update']"
              size="mini"
              round
              :type="row.allowPlayBack ? 'danger' : 'primary'"
              icon="el-icon-video-play"
              @click="handleCourseLivePlayBack(row)"
            >{{ row.allowPlayBack ? '禁止回放' : '允许回放' }}</el-button>
            <el-button
              v-permission="['LiveManagement.LiveStreams.Delete']"
              size="mini"
              round
              type="danger"
              icon="el-icon-delete"
              @click="handleCourseLiveDelete(row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getCourseLiveList"
      />
    </el-card>
    <el-dialog :title="isEdit ? '编辑' : '添加'" :close-on-click-modal="false" :visible.sync="liveDialog" width="1200px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-col :span="10">
          <el-form-item label="直播封面" prop="coverUrl">
            <lz-upload-images
              ref="previewFile"
              :limit="1"
              :file-size="500"
              :file-type="['jpg', 'png', 'jpeg']"
              :source-list="previewFileList"
              @response-fn="handleImageResponse"
              @remove-upload="handleRemoveUploadImage"
            />
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="直播主题" prop="title">
            <el-input v-model="form.title" :disabled="isPublish" />
          </el-form-item>
          <el-form-item prop="startTime" label="开始时间">
            <el-date-picker
              v-model="form.startTime"
              :disabled="isPublish"
              :picker-options="liveStartOptions"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              placeholder="选择日期时间"
              @change="handleTimeChange"
            />
          </el-form-item>
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
              v-model="form.endTime"
              :disabled="isPublish"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              placeholder="选择日期时间"
              @change="handleTimeChange"
            />
          </el-form-item>
          <el-form-item label="直播时长" prop="timeLong">
            <el-input-number v-model="form.timeLong" :disabled="isPublish" :min="0" /> 分钟
          </el-form-item>
          <el-form-item label="内容介绍" prop="description">
            <el-input v-model="form.description" type="textarea" :row="2" />
          </el-form-item>
          <el-form-item label="直播回放" prop="backType">
            <el-radio-group v-model="form.backType">
              <el-radio :label="0">默认</el-radio>
              <el-radio :label="2">纯视频</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="讲师" prop="lecturer">
            <el-input v-model="form.lecturer" />
          </el-form-item>
          <el-form-item label="直播讲师" prop="teacherId">
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectTeacher">选择讲师
            </el-button>
            <span v-if="form.teacherId" style="margin-left: 20px">已选择 1 人 </span>
            <el-tag
              v-if="selectTeacher != null && selectTeacher.name != null"
              type="primary"
              size="small"
              style="margin-left: 20px"
            >
              {{ selectTeacher.name }}
            </el-tag>
          </el-form-item>
          <el-form-item label="直播助教" prop="adminUsers">
            <el-button
              round
              size="small"
              type="primary"
              icon="el-icon-plus"
              :loading="userLoading || liveuserLoading"
              @click="handleSelectStudent(true)"
            >选择助教</el-button>
            <el-tag
              v-for="item in selectAdminUsers"
              :key="item.id"
              type="primary"
              size="small"
              style="margin-left: 20px"
            >{{ item.name }}</el-tag>
          </el-form-item>
          <el-form-item label="直播用户" prop="liveStreamUsers">
            <el-button
              round
              size="small"
              type="primary"
              icon="el-icon-plus"
              :loading="userLoading || liveuserLoading"
              @click="handleSelectStudent(false)"
            >选择用户</el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">选择班级</el-button>
            <span style="margin-left: 20px">已选择{{ selectUsers.length }}人</span>
          </el-form-item>
          <el-form-item label="直播规模" prop="userCount">
            <el-input-number v-model="form.userCount" /> 人
          </el-form-item>
        </el-col>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="liveLoading || userLoading || liveuserLoading" round type="primary" @click="handleLiveSure">确 定
        </el-button>
        <el-button round @click="liveDialog = false">取 消</el-button>

      </span>
    </el-dialog>
    <el-dialog
      :title="isSelectAdminUsers ? '选择助教' : '选择用户'"
      :visible.sync="chooseUserDialog"
      :close-on-click-modal="false"
      width="1000px"
    >
      <choose-user
        v-if="chooseUserDialog"
        :all-org="allOrg"
        :all-student="allStudent"
        :current-select-student="isSelectAdminUsers ? selectAdminUsers : selectUsers"
        @user-change="chooseStudent"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseUserDialog = false">取 消</el-button>
        <el-button :loading="dialogLoading" round type="primary" @click="handleChooseUserSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="chooseTeacherDialog"
      title="选择讲师"
      :visible.sync="chooseTeacherDialog"
      :close-on-click-modal="false"
      width="1000px"
    >
      <el-input v-model="teacherListQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
      <el-button size="small" round type="success" icon="el-icon-search" @click="handleTeacherRefreshList">搜索
      </el-button>
      <el-table
        ref="teacherTable"
        v-loading="teacherListLoading"
        :data="teacherList"
        :row-key="rowKey"
        style="width: 100%"
        size="small"
        highlight-current-row
        stripe
        @sort-change="teacherSortChange"
        @selection-change="handleTeacherSelectionChange"
        @row-click="handleTeacherRowClick"
      >
        <el-table-column type="selection" width="44px" highlight-current-row />
        <el-table-column label="用户名" prop="userName" sortable="userName" width="160px" show-overflow-tooltip />
        <el-table-column label="姓名" prop="name" sortable="name">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门" prop="extraProperties.OUName" sortable="custom">
          <template slot-scope="scope">
            <span>{{ scope.row.extraProperties.OUName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" prop="phoneNumber" sortable="phoneNumber" width="120px">
          <template slot-scope="scope">
            <span>{{ scope.row.phoneNumber }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" sortable="creationTime" label="创建日期" width="160px">
          <template slot-scope="scope">
            <span>{{ scope.row.creationTime | formatDateTime }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="teacherListQuery.totalCount > 0"
        :total="teacherListQuery.totalCount"
        :page.sync="teacherListQuery.page"
        :limit.sync="teacherListQuery.MaxResultCount"
        @pagination="getTeachList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="dialogLoading" round type="primary" @click="handleChooseTeachSure">确 定</el-button>
        <el-button round @click="chooseTeacherDialog = false">取 消</el-button>

      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  courseLiveList,
  addCourseLive,
  editCourseLive,
  deletesCourseLive,
  liveUserAll,
  courseLivePlayBack,
  courseLivePublish,
  liveCategoryList
} from '@/api/live'
import {
  loadNodes,
  getFilterUsers,
  getAllStudents,
  classesUsers
} from '@/api/user'
import LzUploadImages from '@/components/LzUploadImages'
import ChooseUser from '@/components/ChooseUser/choose.vue'
import ChooseClass from '@/components/ChooseClass'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import permission from '@/directive/permission'
import { parseTimeDate } from '@/utils/index'
export default {
  name: 'Live',
  directives: {
    permission
  },
  components: {
    Pagination,
    ChooseUser,
    LzUploadImages,
    ChooseClass
  },
  data() {
    var liveUserValid = (rule, value, callback) => {
      if (this.selectUsers.length === 0) {
        callback(new Error('请选择用户'))
      } else {
        callback()
      }
    }
    var adminUserValid = (rule, value, callback) => {
      if (this.selectAdminUsers.length === 0) {
        callback(new Error('请选择助教'))
      } else {
        callback()
      }
    }
    var liveStartTimeValid = (rule, value, callback) => {
      if (!this.form.startTime) {
        callback(new Error('请选择开始时间'))
      }
      // else if (moment(this.form.startTime).isBefore(moment(), 'minute')) {
      //   callback(new Error('请选择当前时间之后的的时间'))
      // }
      else {
        callback()
      }
    }
    var liveEndTimeValid = (rule, value, callback) => {
      if (!this.form.endTime) {
        callback(new Error('请选择结束时间'))
      } else if (moment(this.form.startTime).isAfter(this.form.endTime, 'minute')) {
        callback(new Error('结束时间应大于开始时间'))
      } else {
        callback()
      }
    }
    var liveLecturerValid = (rule, value, callback) => {
      if (this.form.teacherId === '' || this.form.teacherId === undefined || this.form.teacherId === null) {
        callback(new Error('请选择讲师'))
      } else {
        callback()
      }
    }
    return {
      categoryList: [],
      list: [],
      listLoading: false,
      listQuery: {
        LiveStreamStatue: null,
        liveThemeId: null,
        Filter: '',
        Sorting: 'startTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 上传图片列表
      previewFileList: [],
      // 班级数据
      allOrg: [],
      // 全部学生
      allStudent: [],
      // 选择用户Dialog
      chooseUserDialog: false,
      isSelectAdminUsers: false,
      selectUsers: [],
      selectAdminUsers: [],
      liveLoading: false,
      liveStartOptions: {
        disabledDate: (time) => {
          return new Date().getTime() - 8.64e7 > time.getTime()
        }
      },
      isEdit: false,
      isPublish: false,
      liveDialog: false,
      dialogTitle: '新增',
      dialogLoading: false,
      form: {
        title: '',
        description: '',
        coverImage: '',
        startTime: '',
        endTime: '',
        timeLong: '',
        teacherId: '',
        teacherName: '',
        lecturer: '',
        userCount: 0,
        allowPlayBack: true,
        liveStreamUsers: [],
        adminUsers: []
      },

      teacherList: [],
      teacherListLoading: false,
      chooseTeacherDialog: false,
      teacherListQuery: {
        OuId: null,
        GetChildren: true,
        Filter: '',
        Sorting: 'creationtime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      selectTeacher: null,
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 1, max: 50, message: '长度应小于 300 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入直播描述', trigger: 'blur' },
          { min: 1, max: 50, message: '长度应小于 600 个字符', trigger: 'blur' }
        ],
        teacherId: [{ required: true, validator: liveLecturerValid, trigger: 'blur' }],
        startTime: [{ required: true, validator: liveStartTimeValid, trigger: 'blur' }],
        endTime: [{ required: true, validator: liveEndTimeValid, trigger: 'blur' }],
        timeLong: [{ required: true, message: '请输入直播时长', trigger: 'blur' }],
        // userCount: [{ required: true, message: "请输入直播人数", trigger: 'blur' }],
        liveStreamUsers: [{ required: true, validator: liveUserValid, trigger: 'blur' }]
        // adminUsers: [{ required: true, validator: adminUserValid, trigger: 'blur' }]
      },
      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],
      userLoading: false,
      liveuserLoading: false
    }
  },
  created() {
    this.getCourseLiveList()
    this.getCategoryList()
    this.loadClassList()
  },
  methods: {
    courseLiveList(args) {
      return courseLiveList(args)
    },
    handleTimeChange() {
      if (this.form.startTime && this.form.endTime) {
        this.form.timeLong = moment(this.form.endTime).diff(moment(this.form.startTime), 'minutes')
      }
    },
    async handleCourseLiveEdit(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'LiveEdit'
        })
      } else {
        this.$router.push({
          name: 'LiveEdit',
          query: { id: row.id }
        })
      }
      return
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.resetForm()
      this.selectTeacher = null
      this.selectUsers = []
      this.selectAdminUsers = []
      this.previewFileList = []
      this.isEdit = !!t
      if (this.isEdit) {
        this.isPublish = row.publish
        this.form.id = row.id
        this.form.title = row.title
        this.form.description = row.description
        this.form.backType = row.backType
        this.form.coverImage = row.coverImage
        this.form.startTime = parseTimeDate(row.startTime)
        this.form.endTime = parseTimeDate(row.endTime)
        this.form.timeLong = row.timeLong
        this.form.lecturer = row.lecturer
        this.form.userCount = row.userCount
        this.form.teacherId = row.teacherId
        this.form.teacherName = row.teacherName
        this.form.allowPlayBack = row.allowPlayBack
        this.selectTeacher = {
          id: row.teacherId,
          name: row.teacherName
        }

        if (this.form.coverImage) {
          this.previewFileList.push({
            url: this.form.coverImage
          })
        }
        try {
          this.liveuserLoading = true
          const res = await liveUserAll({ liveStreamId: this.form.id })
          var tmp = [] // 助教
          var tmp2 = [] // 学员
          this.selectUsers = []
          res.items.forEach(item => {
            if (item.userRole === 0) {
              this.selectUsers.push({
                id: item.userId,
                userId: item.userId,
                name: item.name,
                userName: item.userName,
                classId: item.classId,
                className: item.className
              })
              tmp2.push(item)
            } else {
              this.selectAdminUsers.push({
                id: item.userId,
                userId: item.userId,
                name: item.name,
                userName: item.userName,
                classId: item.classId,
                className: item.className
              })
              // this.selectAdminUsers.push(item)
              tmp.push(item)
            }
          })

          this.form.liveStreamUsers = tmp2
          this.form.adminUsers = tmp
          this.liveuserLoading = false
        } catch {
          this.$message.warning('获取用户失败,请重试')
        }
      }
      this.liveDialog = true
    },
    handleLiveSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          this.liveLoading = true
          if (this.isEdit) {
            editCourseLive(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.liveDialog = false
              this.dialogLoading = false
              this.liveLoading = false
              this.getCourseLiveList()
            }).catch(() => {
              this.$message.error('编辑失败')
              this.dialogLoading = false
              this.liveLoading = false
            })
          } else {
            addCourseLive(this.form).then(res => {
              this.$message.success('添加成功')
              this.liveDialog = false
              this.dialogLoading = false
              this.liveLoading = false
              this.getCourseLiveList()
            }).catch(() => {
              this.$message.error('添加失败')
              this.dialogLoading = false
              this.liveLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleLiveDetail(row) {
      this.$router.push({
        name: 'LiveDetail',
        query: {
          id: row.id,
          title: row.title
        }
      })
    },
    handleCourseLivePublish(row) {
      this.$confirm('是否确定' + (!row.publish ? '发布?' : '取消发布?'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          courseLivePublish(row.id).then((res) => {
            this.$message.success((!row.publish ? '发布' : '取消发布') + '成功')
            this.getCourseLiveList()
          })
            .catch(() => {
              this.$message.error((!row.publish ? '发布' : '取消发布') + '失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    handleCourseLivePlayBack(row) {
      var info = row.allowPlayBack ? '禁止回放' : '允许回放'
      this.$confirm('是否确定' + (row.allowPlayBack ? '禁止回放?' : '允许回放?'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          courseLivePlayBack({ liveStreamId: row.id, allowPlayBack: !row.allowPlayBack }).then((res) => {
            this.$message.success('操作成功')
            this.getCourseLiveList()
          })
            .catch(() => {
              this.$message.error('操作失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    handleCourseLiveDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesCourseLive(row.id).then((res) => {
            this.$message.success('删除成功')
            this.getCourseLiveList()
          })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    // 选择学生
    handleSelectStudent(t) {
      this.isSelectAdminUsers = t
      this.chooseUserDialog = true
    },
    // 选择学生组件传值获取选择的学生
    chooseStudent(val) {
      if (this.isSelectAdminUsers) {
        this.selectAdminUsers = val
      } else {
        this.selectUsers = val
      }
    },
    // 选择用户确定
    handleChooseUserSure() {
      this.dialogSureLoading = true
      var tmp = []
      if (this.isSelectAdminUsers) {
        this.selectAdminUsers.forEach(item => {
          tmp.push({
            id: item.id,
            userId: item.id,
            classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
            className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
            userName: item.userName,
            name: item.name
          })
        })
        this.form.adminUsers = tmp
      } else {
        this.selectUsers.forEach(item => {
          tmp.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
            className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
            name: item.name
          })
        })
        this.form.liveStreamUsers = tmp
        this.form.userCount = this.selectUsers.length
      }

      this.dialogSureLoading = false
      this.chooseUserDialog = false
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var newArr = []
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        this.selectUsers = this.selectUsers.concat(res.items)
      }
      var setA = new Set()
      this.selectUsers = this.selectUsers.filter(item => {
        const result = setA.has(item.id)
        setA.add(item.id)
        return !result
      })
      this.selectUsers.forEach(item => {
        newArr.push({
          id: item.id,
          userId: item.id,
          userName: item.userName,
          classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
          className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
          name: item.name
        })
      })
      this.classLoading = false
      this.form.liveStreamUsers = newArr
      this.form.userCount = this.selectUsers.length
      this.chooseClassDialog = false
    },
    rowKey(row) {
      return row.id
    },
    handleSelectTeacher() {
      this.teacherListQuery.page = 1
      this.getTeachList()
      this.chooseTeacherDialog = true
    },
    handleTeacherSelectionChange(val) {
      this.selectTeacher = val[0]
      if (val.length > 1) {
        this.$refs.teacherTable.clearSelection()
        this.$refs.teacherTable.toggleRowSelection(val.pop())
      }
    },
    handleTeacherRowClick(row) {
      this.$refs.teacherTable.clearSelection()
      this.$refs.teacherTable.toggleRowSelection(row)
      this.selectTeacher = row
    },
    handleChooseTeachSure() {
      if (this.selectTeacher) {
        this.form.teacherId = this.selectTeacher.id
        this.form.teacherName = this.selectTeacher.name
        this.chooseTeacherDialog = false
      } else {
        this.$message.warning('请选择讲师')
      }
    },
    // 上传文件成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.form.coverImage = url
    },
    // 上传文件删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.form.coverImage = ''
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getCourseLiveList()
    },
    handleTeacherRefreshList() {
      this.teacherListQuery.page = 1
      this.getTeachList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getLiveUser()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseLiveList()
    },
    teacherSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getTeachList()
        return
      }
      this.teacherListQuery.Sorting = prop + ' ' + order
      this.getTeachList()
    },
    getCourseLiveList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseLiveList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    getTeachList() {
      this.teacherListLoading = true
      this.teacherListQuery.SkipCount = (this.teacherListQuery.page - 1) * this.teacherListQuery.MaxResultCount
      getFilterUsers(this.teacherListQuery).then((response) => {
        this.teacherList = response.items
        if (this.isEdit) {
          var teacher = this.teacherList.filter(item => item.id === this.form.teacherId)
          if (teacher && teacher.length) {
            this.$nextTick(() => {
              // 对勾
              this.$refs.teacherTable.toggleRowSelection(teacher[0])
              // 高亮
              this.$refs.teacherTable.setCurrentRow(teacher[0])
            })
          }
        }
        this.teacherListQuery.totalCount = response.totalCount
        this.teacherListLoading = false
      })
    },
    async loadClassList() {
      this.userLoading = true
      loadNodes().then(res => {
        this.allOrg = res.items
      })
      this.userLoading = false
    },
    resetForm() {
      this.form = {
        title: '',
        description: '',
        backType: 0,
        coverImage: '',
        startTime: '',
        endTime: '',
        timeLong: '',
        lecturer: '',
        userCount: '',
        liveStreamUsers: []
      }
    },
    getCategoryList() {
      liveCategoryList().then(res => {
        this.categoryList = res.items
      })
    }

  }
}
</script>

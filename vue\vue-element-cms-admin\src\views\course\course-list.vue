<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-row style="margin-bottom:20px;">
        <el-col :span="24">
          <div class="header_flex_box">
            <el-radio-group v-model="listQuery.Status" size="small" @change="handleRefreshList">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="1">上架</el-radio-button>
              <el-radio-button label="0">未上架</el-radio-button>
            </el-radio-group>
            <el-cascader
              v-model="listQuery.CourseCategoryId"
              filterable
              clearable
              :options="courseCategoryList"
              :props="cascaderProps"
              size="small"
              style="margin: 0 10px"
              clearable
              placeholder="请选择课程分类..."
              @change="handleRefreshList"
            />
            <!-- <el-select v-model="listQuery.CourseCategoryId" clearable placeholder="选择课程分类" size="small"
            @change="handleRefreshList">
            <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
            <!-- <el-select v-model="listQuery.Status" clearable placeholder="是否上架" size="small" @change="handleRefreshList">
                <el-option label="全部" :value="null" />
                <el-option label="上架" :value="1" />
                <el-option label="未上架" :value="0" />
              </el-select> -->
            <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList(0)">搜索
            </el-button>
            <el-button
              v-permission="['CourseManagement.Courses.Create']"
              round
              size="small"
              type="primary"
              icon="el-icon-plus"
              @click="handleCourseEdit(0, 0)"
            >添加</el-button>
            <export-excel
              :header="['课程封面', '课程名称', '课程编号', '课程讲师', '课时', '上架状态']"
              :filter-val="['coverUrl', 'name', 'number', 'lecturer', 'classHour', 'status']"
              :field="{ 5: [2] }"
              :api-fn="courseList"
            />
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-table v-loading="listLoading" :data="list" size="medium" highlight-current-row @sort-change="sortChange">
            <el-table-column label="课程封面" sortable="coverUrl" width="160">
              <template slot-scope="{ row }">
                <el-image :src="row.coverUrl" class="course-cover" fit="cover">
                  <div slot="error">
                    <div class="image-slot">
                      <i class="el-icon-picture-outline" />
                    </div>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="课程名称" prop="name" sortable="name" min-width="200">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleViewTenantCourse(row)">{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="课程编号" prop="number" sortable="number" width="100" />
            <el-table-column label="课程讲师" prop="lecturer" sortable="lecturer" width="100" />
            <el-table-column label="课时" prop="classHour" sortable="classHour" width="100" />
            <el-table-column label="上架状态" prop="status" sortable="status" width="120">
              <template slot-scope="{ row }">
                <el-tag v-if="row.status === 0" type="info">未上架</el-tag>
                <el-tag v-if="row.status === 1" type="success">已上架</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
              <template slot-scope="{row}">
                {{ row.creationTime | formatDateTime }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="300">
              <template slot-scope="{row}">
                <el-button
                  v-if="row.status === 0"
                  v-permission="['CourseManagement.Courses.Update']"
                  round
                  size="mini"
                  type="primary"
                  icon="el-icon-edit"
                  @click="handleCourseEdit(1, row)"
                >编辑</el-button>
                <el-button
                  v-if="row.status === 1"
                  v-permission="['CourseManagement.Courses.Publish']"
                  round
                  size="mini"
                  type="warning"
                  icon="el-icon-bottom"
                  @click="handleCoursePublish(row)"
                >下架</el-button>
                <el-button
                  v-if="row.status === 0"
                  v-permission="['CourseManagement.Courses.Publish']"
                  round
                  size="mini"
                  type="success"
                  icon="el-icon-top"
                  @click="handleCoursePublish(row)"
                >上架</el-button>
                <el-button
                  v-if="row.status === 0"
                  v-permission="['CourseManagement.Courses.Delete']"
                  round
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleCourseDelete(row)"
                >删除</el-button>
                <!-- <el-button v-if="row.status === 1" round type="primary" size="mini" icon="el-icon-view" @click="handleViewTenantCourse(row)">查看</el-button> -->
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="listQuery.totalCount > 0"
            :total="listQuery.totalCount"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.MaxResultCount"
            @pagination="getCourseList"
          />
        </el-col>
      </el-row>
    </el-card>
    <el-dialog
      v-loading="dialogLoading"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :visible.sync="courseDialog"
      width="600px"
    >

      <el-form ref="form" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="课程图片" prop="coverUrl">
          <LzUploadImages
            v-if="courseDialog"
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="uplaodFileType"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="课程编号" prop="number">
          <el-input v-model="form.number" />
        </el-form-item>
        <el-form-item label="课程名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="课程分类" prop="courseCategoryIds">
          <el-cascader
            v-model="form.courseCategoryIds"
            filterable
            clearable
            :options="courseCategoryList"
            :props="cascaderProps"
            style="width: 100%"
            placeholder="请选择课程分类..."
          />
          <!-- <el-select v-model="form.courseCategoryId" clearable placeholder="选择课程分类" class="form-select">
            <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
        </el-form-item>
        <el-form-item label="课程讲师" prop="lecturer">
          <el-input v-model="form.lecturer" />
        </el-form-item>
        <el-form-item label="课程课时" prop="classHour">
          <el-input-number v-model="form.classHour" :min="1" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="courseDialogSure">确 定</el-button>
        <el-button round @click="courseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  courseList,
  courseCategoryList,
  courseAdd,
  courseDelete,
  coursePublish
} from '@/api/course'
import LzUploadImages from '@/components/LzUploadImages'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import { mapGetters } from 'vuex'
export default {
  name: 'CourseList',
  components: {
    LzUploadImages,
    Pagination
  },

  directives: { permission },
  data() {
    var checkCoverUrl = (rule, value, callback) => {
      if (this.form.coverUrl === '') {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'children',
        checkStrictly: true,
        emitPath: false,
        multiple: true
      },
      // 自建课程
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Status: '',
        CourseCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 课程分类列表
      courseCategoryList: [],
      courseCategoryData: [],
      // dialog
      courseDialog: false,
      dialogTitle: '添加',
      dialogLoading: false,
      // 上传图片类型
      uplaodFileType: [],
      // 上传图片列表
      previewFileList: [],
      // form
      form: {
        coverUrl: '',
        // 编号
        number: '',
        name: '',
        courseCategoryIds: [],
        lecturer: '',
        classHour: 0
      },
      formRules: {
        coverUrl: [{
          required: true,
          validator: checkCoverUrl,
          trigger: 'blur'
        }],
        number: [{
          required: true,
          message: '请输入课程编号',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        name: [{
          required: true,
          message: '请输入课程名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 50,
          message: '长度在 1 到 50 个字符',
          trigger: 'blur'
        }
        ],
        courseCategoryIds: [{
          required: true,
          message: '请选择课程分类',
          trigger: 'change'
        }],
        lecturer: [{
          required: true,
          message: '请输入讲师名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['tenantPermission'])
  },
  created() {
    this.getCourseList()
    this.getCourseCategoryList()
  },
  methods: {
    courseList(args) {
      return courseList(args)
    },
    handleCourseEdit(t, row) {
      this.previewFileList = []
      if (t === 0) {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
        this.dialogTitle = '添加'
        this.courseDialog = true
      } else {
        this.$router.push({
          name: 'CourseEdit',
          query: {
            id: row.id
          }
        })
      }
    },
    courseDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true

          courseAdd(this.form).then(res => {
            this.$message.success('添加成功')
            this.courseDialog = false
            this.dialogLoading = false
            this.getCourseList()
          }).catch(() => {
            this.$message.error('添加失败')
            this.dialogLoading = false
          })
        } else {
          return false
        }
      })
    },
    handleCourseDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getCourseList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleCoursePublish(row) {
      let tipText = '是否确定上架?'
      if (row.status === 1) {
        tipText = '是否确定下架?'
      }
      this.$confirm(tipText, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        coursePublish(row.id, row.status === 0 ? 1 : 0).then(res => {
          this.$message.success('操作成功')
          this.getCourseList()
        }).catch(() => {
          // this.$message.error('操作失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleViewTenantCourse(row) {
      let courseCategoryName = ''
      for (let i = 0; i < this.courseCategoryData.length; i++) {
        if (this.courseCategoryData[i].id === row.courseCategoryId) {
          courseCategoryName = this.courseCategoryData[i].name
          break
        }
      }
      this.$router.push({
        name: 'CourseView',
        query: {
          id: row.id,
          name: row.name,
          from: 1,
          courseCategoryName: courseCategoryName
        }
      })
    },
    handleViewCourse(row) {
      this.$router.push({
        name: 'CourseView',
        query: {
          id: row.courseId,
          name: row.courseName,
          courseCategoryName: row.courseCategoryName
        }
      })
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getCourseList()
    },
    // 上传文件成功回调
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.coverUrl = url
    },
    // 上传文件删除
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.coverUrl = ''
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getCourseList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseList()
    },
    getCourseList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.$message.error('获取列表失败')
        this.listLoading = false
      })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryData = res.items
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    }
  }
}
</script>

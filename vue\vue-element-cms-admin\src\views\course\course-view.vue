<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>{{ pageTitle }} （ 总时长：{{ totalDuration }} ）</span>
      </div>
      <el-row>
        <!-- <el-col :span="6">
          <el-image :src="form.coverUrl" style="width: 300px; height: 200px" />
        </el-col> -->
        <el-col :span="24">
          <el-descriptions
            title="课程信息"
            style="margin-bottom: 30px;"
            label-class-name="course-info-label"
            :column="2"
          >
            <!-- <el-descriptions-item label="课程封面">
              <el-image :src="form.coverUrl" class="course-cover" fit="cover">
                <div slot="error">
                  <div class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </div>
              </el-image>
            </el-descriptions-item> -->
            <el-descriptions-item label="课程名称">{{ form.name }}</el-descriptions-item>
            <el-descriptions-item label="课程分类">{{ categoryName }}</el-descriptions-item>
            <el-descriptions-item label="课程讲师">{{ form.lecturer }}</el-descriptions-item>
            <el-descriptions-item label="课程课时">{{ form.classHour }}</el-descriptions-item>
            <!-- <el-descriptions-item label="课程介绍">
                  <div style="width: 800px;overflow: auto;border: 1px solid #eee;" v-html="form.introduce" />
                </el-descriptions-item> -->
          </el-descriptions>
        </el-col>
      </el-row>
      <el-descriptions
        title="课程内容"
        :column="1"
        border
      />
      <el-row :gutter="15">
        <el-col :xs="24" :sm="8" :md="6" :lg="6">
          <el-card class="shadow_none card-padding-0 card-header-small" shadow="always">
            <div slot="header">
              <span>课程目录</span>
            </div>
            <el-tree
              ref="tree"
              class="course-dir-tree"
              style="height: 766px;overflow: auto"
              :data="treeData"
              :props="treeProps"
              :highlight-current="true"
              default-expand-all
              :node-key="nodeKey"
              :expand-on-click-node="false"
              @node-click="handleTreeNodeClick"
            />
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="16" :md="18" :lg="18">
          <el-card class="shadow_none card-header-small" shadow="always">
            <div slot="header">
              <span>{{ currentNodeDataLabel }}</span>
            </div>
            <el-table :data="list" height="726px" highlight-current-row>
              <el-table-column label="序号" prop="sort" width="50" />
              <el-table-column label="资源名称" prop="name">
                <template slot-scope="{row}">
                  <el-link type="primary" @click="handlePreviewResource(row)"> {{ row.name }} </el-link>
                </template>
              </el-table-column>
              <el-table-column label="格式" prop="resType" width="80">
                <template slot-scope="{row}">
                  <span v-if="row.resType==='video'">视频</span>
                  <span v-if="row.resType==='html'">动画</span>
                  <span v-if="row.resType==='pdf'">PDF</span>
                  <span v-if="row.resType==='img'">图片</span>
                  <span v-if="row.resType===''||row.resType===null">{{ row.fileType }}</span>
                </template>
              </el-table-column>
              <el-table-column label="宝之云" prop="tranStatus" width="100">
                <template slot-scope="{row}">
                  <el-tag v-if="row.tranStatus===0" type="default" size="mini"> -- </el-tag>
                  <el-tag v-if="row.tranStatus===4" size="mini"> 待转码 </el-tag>
                  <el-tag v-if="row.tranStatus===1" type="warning" size="mini"> 上传中 </el-tag>
                  <el-tag v-if="row.tranStatus===2" type="success" size="mini"> 成功 </el-tag>
                  <el-tag v-if="row.tranStatus===3" type="danger" size="mini"> 失败 </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="大小" prop="duration" width="100">
                <template slot-scope="{row}">
                  <span>{{ row.size | formatFileSize }} </span>
                </template>
              </el-table-column>
              <el-table-column label="时长" prop="duration" width="160">
                <template slot-scope="{row}">
                  <span>{{ row.duration | formatSecond }} </span>
                </template>
              </el-table-column>

            </el-table>
          </el-card>
        </el-col>
      </el-row>

    </el-card>
    <el-dialog title="资源预览" append-to-body :visible.sync="resourcePreviewDialog" width="1000px" top="5vh">
      <bos-resource-preview v-if="resourcePreviewDialog&&previewSource===0" :type="previewType" :url="previewUrl" :doc-id="previewDocId" />
      <preview-resource
        v-if="resourcePreviewDialog&&previewSource===1&&previewUrl!=''"
        ref="previewResource"
        v-loading="loadFileContent"
        :type="previewType"
        :url="previewUrl"
      />
    </el-dialog>
  </div>
</template>
<script>
import {
  formatSecond
} from '@/utils/filters'
import {
  jgCourseDetail, courseCategoryDetail, getCourseResourceUrl, getCourseResourceHtmlUrl, courseDetail, courseDirectoryAllList, courseResourceList
} from '@/api/course'
import BosResourcePreview from '@/components/BosResourcePreview'
import PreviewResource from '@/components/PreviewResource'
import { getFileContent, getFileDownloadInfo, getBaoCloudResourceUrl } from '@/api/upload'

export default {
  name: 'CourseView',
  components: {
    BosResourcePreview,
    PreviewResource
  },
  data() {
    return {
      fromSelf: this.$route.query.from,
      pageTitle: this.$route.query.name,
      treeData: [],
      treeNodeDialog: false,
      treeProps: {
        id: this.$route.query.from ? 'id' : 'courseDirectoryId',
        label: this.$route.query.from ? 'name' : 'courseDirectoryName',
        children: this.$route.query.from ? 'children' : 'directories',
        resources: this.$route.query.from ? '' : 'resources'
      },
      nodeKey: this.$route.query.from ? 'id' : 'courseDirectoryId',
      currentNodeDataLabel: '全部',
      // 课程资源列表
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseId: this.$route.query.id,
        DirectoryId: null,
        Sorting: 'sort',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // form
      form: {
        coverUrl: '',
        // 编号
        number: '',
        name: '',
        courseCategoryId: '',
        lecturer: '',
        classHour: 1,
        introduce: ''
      },
      // courseCategoryList: [],
      totalDuration: 0,
      categoryName: '', // this.$route.query.courseCategoryName,

      // 资源预览
      resourcePreviewDialog: false,
      previewSource: 0, // 0：bce 1：local
      previewUrl: '',
      previewDocId: '',
      previewType: '',
      loadFileContent: false
    }
  },
  created() {
    this.getCourseDetail()
    // this.getCourseCategoryList()
    // this.getCourseResourceList()
  },
  methods: {
    handleTreeNodeClick(data) {
      if (this.fromSelf) {
        this.currentNodeDataLabel = data.name
        this.listQuery.DirectoryId = data.id
        this.getCourseResourceList()
      } else {
        this.list = data.resources
        this.currentNodeDataLabel = data.courseDirectoryName
      }
    },
    checkedFileTypeIsVideo(fileType) {
      var type = fileType.toLocaleLowerCase()
      var typeList = ['.mp4', '.avi', '.mov', '.rmvb', '.mpeg', '.flv']
      for (var i = 0; i < typeList.length; i++) {
        if (typeList[i].search(type) !== -1) {
          return true
        }
      }
      return false
    },
    getPreviewType(fileType) {
      switch (fileType) {
        case '.mp4':
          this.previewType = 'video'
          break
        case '.pdf':
          this.previewType = 'pdf'
          break
        case '.ppt':
        case '.doc':
          this.previewType = 'doc'
          break
        case '.png':
        case '.jpeg':
        case '.jpg':
          this.previewType = 'image'
          break
      }
    },
    async handlePreviewResource(row) {
      this.getPreviewType(row.fileType)
      if (row.jobId === 'local') { // 本地资源
        const downloadInfo = await getFileDownloadInfo(row.url)
        this.previewSource = 1
        this.resourcePreviewDialog = true
        if (row.resType === 'html') {
          this.previewType = 'h5'
          getCourseResourceHtmlUrl(downloadInfo.downloadUrl).then(data => {
            this.previewUrl = data
          })
        } else {
          this.previewUrl = downloadInfo.downloadUrl
        }
      } else if (row.jobId === 'AWS') {
        this.previewSource = 1
        getBaoCloudResourceUrl(row.url).then(res => {
          this.previewUrl = res.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://qcfile.ciep-pimp.com/pbktgf0006/')
          this.resourcePreviewDialog = true
        })
      } else if (
        row.jobId === 'hundun-video' ||
          row.jobId === 'geektime-video' ||
          row.jobId === 'geektime-html' ||
          row.jobId === 'ximalaya' ||
          row.jobId === 'pdfh5' ||
          row.jobId === 'videoh5'
      ) {
        this.previewType = row.resType
        this.previewSource = 1
        this.previewUrl = row.url
        this.resourcePreviewDialog = true
      } else {
        this.previewSource = 0
        if (this.fromSelf) {
          this.resourcePreviewDialog = true
          this.previewUrl = row.url
          this.previewDocId = row.documentId
        } else {
          getCourseResourceUrl(row.id).then(data => {
            if (row.resType === 'html') {
              this.previewType = 'h5'
            }
            this.resourcePreviewDialog = true
            this.previewUrl = data
          })
        }
      }
    },
    async getCourseDetail() {
      if (this.fromSelf) {
        const res = await courseDetail(this.$route.query.id)
        this.form.coverUrl = res.coverUrl
        this.form.number = res.number
        this.form.name = res.name
        this.form.lecturer = res.lecturer
        this.form.classHour = res.classHour
        this.form.introduce = res.introduce
        this.form.courseCategoryId = res.courseCategoryId
        this.totalDuration = formatSecond(res.resourceDuration)
        const response = await courseDirectoryAllList(this.$route.query.id)
        this.treeData = response.items.reduce((total, item, index, list) => {
          if (item.parentId === null) {
            total.push({
              ...item,
              children: list.filter(f => f.parentId === item.id)
            })
            // .map(ele => {
            //     return { id: ele.id, parentId: ele.parentId, label: ele.name}})
          } else {
            item.children = list.filter(f => f.parentId === item.id)
          }
          return total
        }, [])

        if (this.listQuery.DirectoryId) {
          this.$nextTick(() => {
            if (this.$refs.tree) {
              this.$refs.tree.setCurrentKey(this.listQuery.DirectoryId)
            }
          })
        } else if (this.treeData.length) {
          this.currentNodeDataLabel = this.treeData[0].name
          this.listQuery.DirectoryId = this.treeData[0].id
          this.$nextTick(() => {
            if (this.$refs.tree) {
              this.$refs.tree.setCurrentKey(this.treeData[0].id)
            }
          })
        }

        this.getCourseResourceList()
        if (res.courseCategoryIds) {
          res.courseCategoryIds.forEach(item => {
            courseCategoryDetail(item).then(res2 => {
              this.categoryName += res2.name + ' '
            })
          })
        }
      } else {
        const res = await jgCourseDetail(this.$route.query.id)
        this.form.coverUrl = res.coverUrl
        this.form.number = res.number
        this.form.name = res.name
        this.form.lecturer = res.lecturer
        this.form.classHour = res.classHour
        this.form.introduce = res.introduce
        this.form.courseCategoryId = res.courseCategoryId
        this.treeData = res.directoryResources
        this.$nextTick(() => {
          if (this.$refs.tree && this.treeData.length) {
            this.$refs.tree.setCurrentKey(this.treeData[0].courseDirectoryId)
            this.list = this.treeData[0].resources
          }
        })
        if (this.treeData) {
          this.currentNodeDataLabel = this.treeData[0].courseDirectoryName
          this.list = this.treeData[0].resources
        }
        this.totalDuration = formatSecond(res.resourceDuration)
      }
    },
    getCourseResourceList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseResourceList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}

</script>
<style scoped>
  .tinymce-container {
    width: 800px !important;
  }

  .is-without-controls {
    width: 80px;
  }

</style>

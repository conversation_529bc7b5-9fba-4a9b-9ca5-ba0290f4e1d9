<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 16:57:16
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-14 11:33:26
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/user/sign.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <export-excel
      :header="['签到时间']"
      :filter-val="['signTime']"
      :field="{ 0: [0] }"
      :query="{'UserId': userId, 'TrainId': trainId}"
      :paging="true"
      :api-fn="trainsSignRecord"
    />
    <el-table v-loading="listLoading" :data="list">
      <el-table-column label="签到时间" prop="signTime">
        <template slot-scope="{ row }">
          {{ row.signTime | formatDate }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getTrainSignRecord"
    />
  </div>
</template>
<script>
import { trainsSignRecord } from '@/api/train'
import Pagination from '@/components/Pagination'
export default {
  name: 'TrainSign',
  components: {
    Pagination
  },
  props: {
    userId: {
      reuqerd: true,
      type: String,
      default: ''
    },
    trainId: {
      reuqerd: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        TrainId: this.trainId,
        UserId: this.userId,
        Sorting: 'signtime',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      }

    }
  },
  created() {
    this.getTrainSignRecord()
  },
  methods: {
    trainsSignRecord(args) {
      return trainsSignRecord(args)
    },
    // 获取签到记录
    getTrainSignRecord() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsSignRecord(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      })
    }
  }
}
</script>

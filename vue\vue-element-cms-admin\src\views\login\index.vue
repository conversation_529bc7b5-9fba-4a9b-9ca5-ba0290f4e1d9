<template>
  <div class="login-container">
    <img src="../../assets/image/logo-bg.png" class="logo-bg1">
    <img src="../../assets/image/logo-bg.png" class="logo-bg2">

    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      autocomplete="on"
      label-position="left"
    >
      <div class="logo-pan">
        <img src="../../assets/image/logo.png">
      </div>
      <div class="title">汽车学园</div>
      <!-- <div class="title1">Wisdom training system</div> -->

      <!-- <h2>Sign In</h2> -->
      <!-- <div style="margin:30px 0;">
        <el-link :underline="false" :style="isScanLogin === false ? 'fontSize:18px' : 'fontSize:16px' " :type="isScanLogin === false ? '' : 'primary' " @click="loginModelChange(0)">账号登录</el-link>
        <span> | </span>
        <el-link :underline="false" :style="isScanLogin === true ? 'fontSize:18px' : 'fontSize:16px' " :type="isScanLogin === true ? '' : 'primary' " @click="loginModelChange(1)">扫码登录</el-link>
      </div> -->

      <!-- <span class="h2-txt"></span> -->
      <!-- <span> | </span> -->
      <!-- <span class="h2-txt">扫码登录</span> -->
      <div v-show="isScanLogin === false">
        <el-form-item prop="username" class="login_user_item">
          <el-input
            ref="username"
            v-model="loginForm.username"
            class="login_input login_user"
            placeholder="用户名/手机号"
            name="username"
            type="text"
            tabindex="2"
            autocomplete="on"
          ><i
            slot="prefix"
            style="display: flex; align-items: center"
          >
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
          </i>
          </el-input>
        </el-form-item>
        <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
          <el-form-item prop="password" class="login_pass_item">
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              class="login_input login_pass"
              :type="passwordType"
              :placeholder="$t('login.password')"
              name="password"
              tabindex="3"
              autocomplete="on"
              @keyup.native="checkCapslock"
              @blur="capsTooltip = false"
              @keyup.enter.native="handleLogin"
            ><i
              slot="prefix"
              style="display: flex; align-items: center"
            >
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
            </i>
            </el-input>
          </el-form-item>
        </el-tooltip>
        <el-link type="primary" @click="handleForgetPasswordClick">忘记密码?</el-link>
        <el-button
          class="login_btn"
          :loading="loading"
          type="primary"
          style="width: 100%; margin-bottom: 30px"
          @click.native.prevent="handleLogin"
        >
          {{ $t("login.logIn") }}
        </el-button>
        <div class="login_link_box">
          <el-image class="login_link_image" :src="require('@/assets/image/icon-code.png')" />
          <el-link class="login_link" :underline="false" type="primary" @click="loginModelChange(1)">扫码登录</el-link>
        </div>
      </div>
      <div v-show="isScanLogin === true" style="text-align:center">
        <!-- <vue-qr class="qr_div login_link_image" :text="scanCodeUrl" :margin="0" color-dark="#000000" color-light="#fff" :size="200" /> -->

        <el-image
          v-loading="scanCodeLoading"
          style="width: 200px; height: 200px;margin-bottom: 50px"
          :src="'data:image/jpg;base64,' + scanCodeUrl"
          fit="cover"
          @click="getLoginWeChatImage"
        />
        <span style="display:block;text-align:center;margin-bottom:20px;">请使用微信扫码登录</span>

        <div class="login_link_box">
          <el-image class="login_link_image" :src="require('@/assets/image/icon-user.png')" />
          <el-link class="login_link" :underline="false" type="primary" @click="loginModelChange(0)">账户登录</el-link>
        </div>
      </div>
    </el-form>

    <div class="footer">
      <div style="text-align: center;">
        <el-link type="primary" target="_blank" href="">服务协议</el-link>
        <span> | </span>
        <el-link type="primary" target="_blank" href="">法律声明与隐私保护</el-link>
      </div>
      <!-- <span>上海景格科技股份有限公司 版权所有</span> -->
      <span class="footer_span">Copyright © 2020 - 2021 Jingge. All Rights Reserved. | 沪ICP备11039732号-15 | 上海景格科技股份有限公司
      </span>
    </div>
    <el-dialog
      custom-class="customDialog"
      title="忘记密码"
      :center="true"
      :visible.sync="ForgetPassworddialogFormVisible"
      :close-on-click-modal="false"
      width="350px"
    >
      <el-form
        ref="forgetPasswordForm"
        :model="forgetPasswordForm"
        :rules="forgetPasswordRules"
        class="regist-form"
        auto-complete="on"
        label-position="left"
      >
        <!-- <div class="customDialogTitle">
          <h3 class="title">忘记密码</h3>
        </div> -->
        <el-form-item prop="phoneNumber">
          <span class="svg-container">
            <!-- <svg-icon icon-class="user" /> -->
          </span>
          <el-input
            ref="phoneNumber"
            v-model="forgetPasswordForm.phoneNumber"
            placeholder="请输入手机号"
            name="phoneNumber"
            type="text"
            tabindex="1"
            auto-complete="on"
          />
        </el-form-item>
        <el-form-item prop="resetToken">
          <el-row :gutter="20">
            <el-col :xs="12" :sm="14">
              <el-input
                ref="resetToken"
                v-model="forgetPasswordForm.resetToken"
                placeholder="请输入验证码"
                name="resetToken"
                type="text"
                tabindex="1"
                auto-complete="on"
              />
            </el-col>
            <el-col :xs="12" :sm="10">
              <el-button
                type="primary"
                class="verificateCodeButton"
                :disabled="verificateCodeButtonDisabled"
                :loading="loadingForm"
                @click="getVerificateCode()"
              >{{ verificateCodeButtonTitle }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item prop="password">
          <span class="svg-container">
            <!-- <svg-icon icon-class="password" /> -->
          </span>
          <el-input
            key="password"
            ref="password"
            v-model="forgetPasswordForm.password"
            type="password"
            placeholder="请输入新密码"
            name="password"
            tabindex="2"
            auto-complete="on"
          />
          <span class="show-pwd" @click="showPwd">
            <!-- <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" /> -->
          </span>
        </el-form-item>
        <el-form-item prop="checkPassword">
          <span class="svg-container">
            <!-- <svg-icon icon-class="password" /> -->
          </span>
          <el-input
            key="checkPassword"
            ref="checkPassword"
            v-model="forgetPasswordForm.checkPassword"
            type="password"
            placeholder="请输入新密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            @keyup.enter.native="handleForgetPassword"
          />
          <span class="show-pwd" @click="showPwd">
            <!-- <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" /> -->
          </span>
        </el-form-item>
      </el-form>
      <el-button
        :loading="loadingForm"
        type="primary"
        class="loginRegistButton"
        @click.native.prevent="handleForgetPassword"
      >保 存</el-button>
      <!-- <div slot="footer" class="dialog-footer"></div> -->
    </el-dialog>
  </div>
  <!-- 忘记密码 -->

</template>

<script>
import config from '@/config/config'
import {
  getTenantName,
  userLoginQRCode,
  watchLoginStatus,
  getForgetCode,
  restPassword
} from '@/api/user'
export default {
  name: 'Login',
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('请输入6位以上密码'))
      } else {
        callback()
      }
    }
    const validatePhoneNumber = (rule, value, callback) => {
      if (value == null && value === '' && value.length === 0) {
        callback(new Error('请输入手机号'))
      } else {
        var reg_tel = /^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])\d{8}$/
        var reg_Tel = new RegExp(reg_tel)
        if (!reg_Tel.test(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      }
    }
    const validateCode = (rule, value, callback) => {
      if (value.length !== 6) {
        callback(new Error('请输入正确的验证码'))
      } else {
        var numReg = /^[0-9]+$/
        var numRe = new RegExp(numReg)
        if (!numRe.test(value)) {
          callback(new Error('请输入正确的验证码'))
        } else {
          callback()
        }
      }
    }
    const validateCheckPassword = (rule, value, callback) => {
      if (value == null && value === '' && value.length === 0) {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.forgetPasswordForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      isScanLogin: false,
      loginForm: {
        tenant: undefined,
        username: '',
        password: '',
        client_id: config.client.client_id,
        client_secret: config.client.client_secret,
        grant_type: config.client.grant_type
      },
      loginRules: {
        username: [{
          required: true,
          message: '请输入账号',
          trigger: 'blur'
        }],
        password: [{
          required: true,
          message: '请输入密码',
          trigger: 'blur'
          // validator: validatePassword
        }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      scanCodeLoading: true,
      scanCodeUrl: '',
      loginInterval: null,

      ForgetPassworddialogFormVisible: false,
      // 验证码重新发送间隔时间
      resendVerificateCodeTime: 120,
      // 发送短信按钮是否禁用
      verificateCodeButtonDisabled: false,
      loadingForm: false,
      // 按钮标题
      verificateCodeButtonTitle: '发送验证码',
      forgetPasswordForm: {
        password: '',
        phoneNumber: '',
        resetToken: '',
        resetTokenId: 'ba65e752-96d7-adce-11ed-39f8aeb418c2'
      },
      forgetPasswordRules: {
        phoneNumber: [{
          required: true,
          trigger: 'blur',
          validator: validatePhoneNumber
        }],
        password: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }],
        resetToken: [{
          required: true,
          trigger: 'blur',
          validator: validateCode
        }],
        checkPassword: [{
          required: true,
          trigger: 'blur',
          validator: validateCheckPassword
        }]
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    checkCapslock(e) {
      const {
        key
      } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          var findTenantForm = {
            userName: this.loginForm.username
          }
          getTenantName(findTenantForm).then((res) => {
            this.loginForm.tenant = res
            this.$store.dispatch('user/userLogin', this.loginForm).then(() => {
              this.$router.push({
                path: this.redirect || '/train',
                query: this.otherQuery
              })
              this.loading = false
            }).catch(() => {
              this.loading = false
            })
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    handleForgetPasswordClick() {
      if (this.$refs.forgetPasswordForm) {
        this.$refs.forgetPasswordForm.resetFields()
      }
      this.restForgetPasswordForm()
      this.ForgetPassworddialogFormVisible = true
    },
    loginModelChange(t) {
      if (t === 0) {
        clearInterval(this.loginInterval)
        this.isScanLogin = false
      } else {
        this.isScanLogin = true

        this.getLoginWeChatImage()
      }
    },
    getLoginWeChatImage() {
      this.scanCodeLoading = true
      userLoginQRCode('ELearning').then(res => {
        this.scanCodeUrl = res.aCode
        this.scanCodeLoading = false
        this.watchLoginStatus(res.token)
      }).catch(() => {
        this.scanCodeLoading = false
        this.$message.error('获取登录二维码失败')
      })
    },
    watchLoginStatus(token) {
      this.loginInterval = setInterval(() => {
        watchLoginStatus(token, config.client.client_id, config.client.client_secret).then(res => {
          if (res.isSuccess) {
            clearInterval(this.loginInterval) // 清除计时器
            this.$store
              .dispatch('user/saveUserToken', JSON.parse(res.token).access_token)
              .then(() => {
                this.$router.push({
                  path: this.redirect || '/',
                  query: this.otherQuery
                })
                this.loading = false
              })
              .catch(() => {
                this.loading = false
              })
          }
        }).catch(() => {

        })
      }, 3000)
    },

    handleForgetPassword() {
      this.$refs.forgetPasswordForm.validate(valid => {
        if (valid) {
          this.loadingForm = true
          restPassword(this.forgetPasswordForm)
            .then(res => {
              if (!res.error) {
                this.loadingForm = false
                this.$message.success('修改成功')
                this.ForgetPassworddialogFormVisible = false
              } else {
                this.loadingForm = false
                this.$message.error(res.error.message)
              }
            })
            .catch(err => {
              this.loadingForm = false
            })
        } else {
          return false
        }
      })
    },
    getVerificateCode() {
      this.$refs.forgetPasswordForm.validateField(
        'phoneNumber',
        phoneNumber => {
          if (!phoneNumber) {
            this.loadingForm = true
            this.resendVerificateCodeTime = 120
            this.timer()
            this.loadingForm = false
            var data = {
              phone: this.forgetPasswordForm.phoneNumber,
              appName: 'VMSWeb'
            }
            getForgetCode(data).then(res => {
              this.forgetPasswordForm.resetTokenId = res
            })
          }
        }
      )
    },
    timer() {
      if (this.resendVerificateCodeTime > 0) {
        this.verificateCodeButtonDisabled = true
        this.resendVerificateCodeTime--
        this.verificateCodeButtonTitle = this.resendVerificateCodeTime + ' 秒'
        setTimeout(this.timer, 1000)
      } else {
        this.resendVerificateCodeTime = 0
        this.verificateCodeButtonTitle = '发送验证码'
        this.verificateCodeButtonDisabled = false
      }
    },
    restForgetPasswordForm() {
      this.forgetPasswordForm = {
        password: '',
        phoneNumber: '',
        resetToken: '',
        resetTokenId: 'ba65e752-96d7-adce-11ed-39f8aeb418c2'
      }
    }
  }
}

</script>
<style lang="scss">
  /* 修复input 背景不协调 和光标变色 */
  /* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

  /* reset element-ui css */

  .login-form {
    .el-input {

      // display: inline-block;
      input {
        background: transparent;
        border: 0px;
        -webkit-appearance: none;

        input {
          background-color: rgba(255, 255, 255, 0) !important;
        }

        &:-webkit-autofill {
          transition: background-color 5000s ease-in-out 0s;
          -webkit-text-fill-color: rgb(102, 100, 100) !important;
        }

        &:-webkit-autofill:focus {
          -webkit-text-fill-color: rgb(14, 118, 236) !important;
        }

        &:-webkit-autofill {
          -webkit-text-fill-color: rgb(102, 100, 100) !important;
        }
      }
    }
  }

</style>
<style lang="scss" scoped>
  html {
    height: 100%;
    background: #fff;
  }

  body {
    height: 100%;
  }

  .login-container {
    height: 100%;
    border-top: 7px solid #0096ff;
    position: relative;
    overflow: hidden;
    min-height: 700px;
  }

  .logo-bg1 {
    position: absolute;
    left: -1100px;
    top: 114px;
  }

  .logo-bg2 {
    position: absolute;
    right: -1100px;
    top: -700px;
  }

  .logo-pan {
    text-align: center;
    margin-bottom: 40px;
    margin-top: 40px;
  }

  .title {
    font-size: 36px;
    color: #14265d;
    text-align: center;
    margin-bottom: 35px;
  }

  .title1 {
    font-size: 16px;
    color: #14265d;
    font-weight: bold;
    margin: 0 auto 55px;
    text-align: center;
  }

  h2 {
    color: #2e62bc;
    margin: 0;
    font-size: 30px;
    text-align: left;
  }

  .h2-txt {
    color: #14265d;
    font-size: 18px;
    margin-bottom: 40px;
    display: inline-block;
    margin-top: 10px;
  }

  .login-form {
    // width: 400px;
    margin: auto;
    position: absolute;
    left: 50%;
    margin-left: -227px;
    top: 40%;
    margin-top: -300px;
    padding: 40px;

    .svg-container {
      font-size: 30px;
      vertical-align: middle;
      color: rgb(115, 116, 138);
    }
  }

  .login_input ::v-deep .el-input__inner {
    border-width: 0 0 2px 0;
    border-style: solid;
    border-radius: 0;
    padding-left: 45px;
    font-size: 16px;
    font-weight: bold;
  }

  .login_pass {
    margin-top: 15px;
  }

  .login_user ::v-deep.el-input__inner:focus-within {
    border-color: #0096ff;
    color: #0096ff;
  }

  .login_user_item ::v-deep .login_user:focus-within {
    border-color: #0096ff;
    color: #0096ff;

    .svg-container {
      color: #0096ff;
    }
  }

  .login_pass ::v-deep.el-input__inner:focus-within {
    border-color: #0096ff;
    color: #0096ff;
  }

  .login_pass_item ::v-deep .login_pass:focus-within {
    border-color: #0096ff;
    color: #0096ff;

    .svg-container {
      color: #0096ff;
    }
  }

  .login_btn {
    margin-top: 20px;
    border-radius: 24px;
    height: 44px;
    font-size: 16px;
  }

  .footer {
    position: absolute;
    bottom: 15px;
    width: 100%;

    .footer_span {
      display: inline-block;
      width: 100%;
      font-size: 14px;
      color: #ccc;
      line-height: 30px;
      text-align: center;
    }
  }

  .login_link_box {
    height: 30px;
    margin-top: 15px;
    text-align: center;

    .login_link_image {
      vertical-align: middle;
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }

    .login_link {
      font-size: 16px;
    }
  }

  .customDialog {
    border-radius: 15px;
    min-width: 300px;
    max-width: 350px;
  }

  .loginRegistButton {
    display: block;
    width: 100%;
    margin-bottom: 20px;
  }

  a {

    vertical-align: middle;
  }

  a:hover {
    color: #3ac991;
  }

</style>

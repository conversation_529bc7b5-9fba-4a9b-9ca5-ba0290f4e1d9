<template>
  <div>
    <el-descriptions :column="4">
      <el-descriptions-item label="用户名">{{ currentUserInfo.userName }}</el-descriptions-item>
      <el-descriptions-item label="姓名">{{ currentUserInfo.name }}</el-descriptions-item>
      <!-- <el-descriptions-item label="考核名称">{{ currentUserInfo.examName }}</el-descriptions-item> -->
      <el-descriptions-item label="部门">{{ currentUserInfo.className }}</el-descriptions-item>
      <el-descriptions-item label="成绩">{{ currentUserInfo.lastScore }}分</el-descriptions-item>
    </el-descriptions>
    <div v-loading="loading">
      <div v-for="item in newQuestionList" :key="item.type" class="examPaper_achieve">
        <el-collapse v-model="collapseActive">
          <el-collapse-item :name="item.type">
            <template slot="title">
              <div v-if="item.items && item.items.length > 0">
                <span v-if="item.type === 0 ">单选题</span>
                <span v-if="item.type === 1 ">多选题</span>
                <span v-if="item.type === 2 ">判断题</span>
                <span v-if="item.type === 3 ">填空题</span>
                <span v-if="item.type === 6 ">问答题</span>
                <span v-if="item.items && item.items.length > 0">
                  <span>(每题{{ item.score }}分, 共{{ item.items.length }}题, 得分{{ item.lastScore }},
                    满分{{ item.totalScore }})</span>
                </span>
              </div>
            </template>
            <div v-for="(question,index) in item.items" :key="index">
              <exam-result-preview :order="question.order" :data="question" @score-change="scoreChange" />
            </div>
          </el-collapse-item>
        </el-collapse>

      </div>
    </div>
  </div>
</template>
<script>
import ExamResultPreview from '@/components/ExamResultPreview'
import { studentExamDetail, examQuestionsDetail, subjectiveQuestionScore } from '@/api/train'
export default {
  name: 'TCourseRecord',
  components: {
    ExamResultPreview
  },
  props: {
    data: {
      reuqerd: true,
      type: Object,
      default: null
    },
    isExam: {
      reuqerd: true,
      type: Boolean,
      default: true
    },
    userInfo: {
      reuqerd: true,
      type: Object,
      default: () => {
        return {
          userId: '',
          userName: '',
          name: '',
          className: ''
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      currentUserInfo: {
        userName: this.userInfo.userName,
        name: this.userInfo.name,
        identityCode: '',
        className: this.userInfo.className,
        examName: '',
        lastScore: 0,
        userId: this.userInfo.userId
      },

      replyContent: [],
      // 单选集合
      singleQuestions: [],
      // 单选分数
      singleScore: 0,
      // 单选题得分
      singleLastScore: 0,
      // 判断集合
      judgeQuestions: [],
      // 判断分数
      judgeScore: 0,
      // 判断题得分
      judgeLastScore: 0,
      // 多选集合
      multipleQuestions: [],
      // 多选分数
      multipleScore: 0,
      // 多选题得分
      multipleLastScore: 0,
      // 填空集合
      blankQuestions: [],
      // 填空分数
      blankScore: 0,
      // 填空最后得分
      blankLastScore: 0,
      // 问答集合
      replayQuestions: [],
      // 问答分数
      replayScore: 0,
      // 问答最后得分
      replayLastScore: 0,
      questionList: [],
      // 新的合成数据
      newQuestionList: [],
      // 成绩预览折叠面板默认全部展开
      collapseActive: [0, 1, 2, 3, 6],

      examinationId: ''
    }
  },
  created() {
    this.getQuestionList()
  },
  methods: {
    async getQuestionList() {
      this.newQuestionList = []
      this.judgeQuestions = []
      this.singleQuestions = []
      this.blankQuestions = []
      this.replayQuestions = []
      this.multipleQuestions = []
      this.replyContent = []
      this.multipleLastScore = 0
      this.judgeLastScore = 0
      this.singleLastScore = 0
      this.blankLastScore = 0
      this.replayLastScore = 0
      this.currentUserInfo.examName = this.data.name
      this.currentUserInfo.lastScore = this.data.lastScore
      this.loading = true
      var form = {
        id: '',
        userId: ''
      }
      if (!this.isExam) {
        this.examinationId = this.data.examinationId
        form.id = this.data.examinationId
        form.userId = this.currentUserInfo.userId
      } else if (this.isExam) {
        // 信息
        // this.currentUserInfo.userId = this.data.userId
        // this.currentUserInfo.userName = this.data.userName
        // this.currentUserInfo.name = this.data.name
        // this.currentUserInfo.identityCode = this.data.identityCode
        // this.currentUserInfo.className = this.data.className

        // this.examinationId = this.examUserListQuery.ExaminationId
        // form.id = this.examUserListQuery.ExaminationId
        // form.userId = this.data.userId
      }
      try {
        const res = await studentExamDetail(form)
        if (res) {
          this.replyContent = JSON.parse(res.replyContent)
        }
      } catch (e) {
        this.replyContent = []
      }

      try {
        const response = await examQuestionsDetail(form.id)
        this.questionList = response
      } catch (e) {
        this.$message.error('获取试卷失败')
        this.questionList = []
      }
      this.questionList.forEach(questionItem => {
        // 设置回显model 多选是数组
        if (questionItem.questionType === 1) {
          this.$set(questionItem, 'answerModel', [])
        } else {
          this.$set(questionItem, 'answerModel', null)
        }
        this.$set(questionItem, 'IsRight', false)
        if (this.replyContent !== null) {
          this.replyContent.forEach(replyItem => {
            // 如果ID相同判断类型  回显model赋值
            if (questionItem.id === replyItem.Q) {
              if (questionItem.questionType === 1 && replyItem.O) {
                questionItem.answerModel = replyItem.O
                this.multipleLastScore += replyItem.S
              } else if (questionItem.questionType === 0 && replyItem.O) {
                questionItem.answerModel = replyItem.O[0]
                this.singleLastScore += replyItem.S
              } else if (questionItem.questionType === 2) {
                questionItem.answerModel = replyItem.J
                this.judgeLastScore += replyItem.S
              } else if (questionItem.questionType === 3 && replyItem.BA) {
                questionItem.answerModel = replyItem.BA
                this.blankLastScore += replyItem.S
              } else if (questionItem.questionType === 6) {
                questionItem.answerModel = replyItem.RA
                this.replayLastScore += replyItem.S
              }

              questionItem.finalScore = replyItem.S
              questionItem.IsRight = replyItem.R
            }
          })
        } else {
          this.multipleLastScore = 0
          this.singleLastScore = 0
          this.judgeLastScore = 0
          this.blankLastScore = 0
          this.replayLastScore = 0
        }
      })
      this.questionList.forEach(questionItem => {
        if (questionItem.questionType === 0) {
          this.singleQuestions.push(questionItem)
          this.singleScore = questionItem.score
        } else if (questionItem.questionType === 1) {
          this.multipleQuestions.push(questionItem)
          this.multipleScore = questionItem.score
        } else if (questionItem.questionType === 2) {
          this.judgeQuestions.push(questionItem)
          this.judgeScore = questionItem.score
        } else if (questionItem.questionType === 3) {
          this.blankQuestions.push(questionItem)
          this.blankScore = questionItem.score
        } else if (questionItem.questionType === 6) {
          this.replayQuestions.push(questionItem)
          this.replayScore = questionItem.score
        }
      })
      if (this.judgeQuestions.length) {
        this.newQuestionList.push({
          type: 2,
          score: this.judgeScore,
          totalScore: this.judgeScore * this.judgeQuestions.length,
          lastScore: this.judgeLastScore,
          items: this.judgeQuestions
        })
      }
      if (this.singleQuestions.length) {
        this.newQuestionList.push({
          type: 0,
          score: this.singleScore,
          totalScore: this.singleScore * this.singleQuestions.length,
          lastScore: this.singleLastScore,
          items: this.singleQuestions
        })
      }
      if (this.multipleQuestions.length) {
        this.newQuestionList.push({
          type: 1,
          score: this.multipleScore,
          totalScore: this.multipleScore * this.multipleQuestions.length,
          lastScore: this.multipleLastScore,
          items: this.multipleQuestions
        })
      }
      if (this.blankQuestions.length) {
        this.newQuestionList.push({
          type: 3,
          score: this.blankScore,
          totalScore: this.blankScore * this.blankQuestions.length,
          lastScore: this.blankLastScore,
          items: this.blankQuestions
        })
      }
      if (this.replayQuestions.length) {
        this.newQuestionList.push({
          type: 6,
          score: this.replayScore,
          totalScore: this.replayScore * this.replayQuestions.length,
          lastScore: this.replayLastScore,
          items: this.replayQuestions
        })
      }
      this.loading = false
    },
    scoreChange(id, val, oldVal, type) {
      var data = {
        examinationId: this.examinationId,
        questionId: id,
        userId: this.currentUserInfo.userId,
        score: val
      }
      subjectiveQuestionScore(data).then(res => {
        this.$message.success('评分成功')
        this.newQuestionList.forEach(item => {
          if (item.type === type) {
            item.lastScore = item.lastScore + val - oldVal
            this.currentUserInfo.lastScore = this.currentUserInfo.lastScore + val - oldVal
            return
          }
        })
      })
    }
  }
}
</script>

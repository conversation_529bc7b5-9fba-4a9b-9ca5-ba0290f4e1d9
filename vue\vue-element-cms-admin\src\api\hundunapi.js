import crypto from 'crypto'
import axios from 'axios'

// 混沌API文档：https://hundun-academy.feishu.cn/docs/doccnkgzBZnKm9pEoY12lVs8BIh#JEbjte

// 这个几个参数固定不变
const api_domain = 'https://partner.hundun.cn'
const app_id = 'd76d1f348e056e6f56f9126be5e17a71'
const app_key = 'jHonOzYlMLgEDsubewRt'
const timestamp = **********
const non_str = 'lBIudi'

const identify_type = '0' // 这个参数不用变

/**
 * 注册混沌用户账号
 * 混沌的注册API，一次只能注册一个学员账号，
 * 因此有多个学员账号 需要批量注册的话，需要循环调用 注册函数
 * @param {*} userData 学员数据  {user_no:学员ID, user_name:学员姓名}
 */
async function registUserAccount(userData) {
  const sign = makeSign(app_id, app_key, timestamp, non_str, {
    identify_id: userData.user_no,
    identify_type: identify_type,
    user_name: userData.user_name
  })

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async(resolve, reject) => {
    const res = await axios({
      method: 'post',
      url: api_domain + '/api/v2/user/signup',
      data: {
        AppId: app_id,
        nonstr: non_str,
        timestamp: timestamp,
        sign: sign,

        identify_id: userData.user_no,
        identify_type: identify_type,
        user_name: userData.user_name
        // department: 'test',
        // user_position: 'tester',
      }
    })
    console.log(res)
    resolve(res.data || null)
  })
}

/**
 * 批量注册
 * @param {*} totalList 账号数组
 */
export function batchRegistHundun(totalList) {
  let index = 0
  const t = window.setInterval(async() => {
    if (index >= totalList.length) {
      window.clearInterval(t)
      return
    }

    const index2 = index + 1
    const index3 = index2 > totalList.length ? totalList.length : index2

    const arr = totalList.slice(index, index3)

    const result = await registUserAccount(arr[0])

    if (result) {
      console.log(index, index3 - 1, arr)
      // eslint-disable-next-line require-atomic-updates
      index = index3
    }
  }, 1000 * 2)
}

/**
 * 计算签名
 * @param app_id 分配给企业的app_id，请管混沌的业务人员询问
 * @param app_key 分配给企业的app_key，请管混沌的业务人员询问
 * @param timestamp 发起请求的时间戳
 * @param non_str 随机字符串，作为"盐"。10位以内即可
 * @param params 其他所有传递的参数名和值
 * @returns {string}
 */
export function makeSign(app_id, app_key, timestamp, non_str, params = {}) {
  const md5 = crypto.createHash('md5')

  params['AppId'] = app_id
  params['timestamp'] = timestamp
  params['nonstr'] = non_str

  const keys = Object.keys(params).sort()
  const values = []
  for (let i = 0; i < keys.length; i++) {
    values.push(keys[i] + '=' + params[keys[i]])
  }
  return md5
    .update(
      app_id + timestamp.toString() + non_str + values.join('&') + app_key
    )
    .digest('hex')
}

// 使用
// let user_id = '174d311a-d7a8-9823-9332-3a04255f7608'; //汇智学园里的 userId
// let user_name = '***********';  //汇智学园里的 userName 或者 name，我用的 userName
// registUserAccount({user_no:user_id, user_name:user_name});

// batchRegist(/*totalList 数组*/);

// totalList:[
//     {
//       "user_no": "174d311a-d7a8-9823-9332-3a04255f7608", //汇智学园里的 userId
//       "user_name":"***********" //汇智学园里的 userName 或者 name，我用的 userName
//     },
//     {
//       "user_no": "d327ad8b-5f6a-7330-1e1c-3a032ac02bba",
//       "user_name": "test2"
//     }
//   ]

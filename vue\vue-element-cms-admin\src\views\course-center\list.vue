<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <!-- <el-select v-model="listQuery.CourseCategoryId" size="small" placeholder="选择课程分类" @change="handleRefreshList">
          <el-option label="全部" :value="''" />
          <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
        <el-cascader v-model="listQuery.CourseCategoryId" filterable clearable :options="courseCategoryList" :props="{'label': 'name','value': 'id','children': 'children','checkStrictly': true,'emitPath': false}" size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
        <!-- <el-select size="small"  placeholder="是否免费"  v-model="listQuery.FreeModel" @change="handleRefreshList">
          <el-option label="免费" :value="0" />
          <el-option label="收费" :value="1" />
        </el-select> -->
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['CourseManagement.CourseCenters.Create']" round size="small" type="primary" icon="el-icon-plus" @click="handleCourseCentenrEdit(0,0)">添加</el-button>
        <el-button v-permission="['CourseManagement.CourseCenters.Update']" round size="small" type="primary" icon="el-icon-edit" @click="handleBatchCoursePermission(0)">批量设置课程权限</el-button>
        <el-button v-permission="['CourseManagement.KnowledgeCenters.Update']" round size="small" type="primary" icon="el-icon-edit" @click="handleBatchCoursePermission(1)">按班级批量设置课程权限</el-button>
        <!-- <el-button round size="small" type="primary" @click="handleBatchUpdateClassHour">一键更新学时</el-button> -->
      </div>
      <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
        <el-table-column label="排序" prop="order" sortable="order" width="100" />
        <el-table-column label="课程封面" prop="courseCoverUrl" sortable="courseCoverUrl" width="160">
          <template slot-scope="{ row }">
            <el-image :src="row.courseCoverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" sortable="courseName" min-width="200" />
        <el-table-column label="学时" prop="classHour" sortable="classHour" width="100" />
        <!-- <el-table-column label="免费/收费" prop="freeModel" sortable="freeModel" width="150">
          <template slot-scope="{row}">
            <el-tag size="mini" v-if="row.freeModel === 0" type="success">免费</el-tag>
            <el-tag size="mini" v-if="row.freeModel === 1" type="warning">收费</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="330">
          <template slot-scope="{row}">
            <el-button v-permission="['CourseManagement.CourseCenters']" round size="mini" type="primary" icon="el-icon-view" @click="handleViewCourseRecord(row)">学习情况</el-button>
            <el-button v-permission="['CourseManagement.CourseCenters.Update']" round size="mini" type="primary" icon="el-icon-edit" @click="handleCourseCentenrEdit(1,row)">编辑</el-button>
            <el-button v-permission="['CourseManagement.CourseCenters.Delete']" round size="mini" type="danger" icon="el-icon-delete" @click="handleCourseCentenrDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getCourseCenterList"
      />
    </el-card>
    <el-dialog title="详情" :visible.sync="recordDialog" width="1000px">
      <el-input v-model="recordListQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
      <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList(1)">搜索</el-button>
      <el-table v-loading="recordListLoading" :data="recordList" size="small" highlight-current-row @sort-change="recordSortChange">
        <el-table-column label="用户名" prop="userName" sortable="userName" />
        <el-table-column label="姓名" prop="userTrueName" sortable="userTrueName" />
        <el-table-column label="资源总时长" prop="resourceDuration" sortable="resourceDuration">
          <template slot-scope="{row}">
            {{ row.resourceDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学习时长" prop="courseLearnDuration" sortable="courseLearnDuration">
          <template slot-scope="{row}">
            {{ row.courseLearnDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学时" prop="classHour" sortable="classHour">
          <template slot-scope="{row}">
            {{ row.classHour }}
          </template>
        </el-table-column>
        <el-table-column label="学习进度" prop="courseLearnProgress" sortable="courseLearnProgress">
          <template slot-scope="{row}">
            {{ row.courseLearnProgress.toFixed(2) }} %
          </template>
        </el-table-column>
        <!-- <el-table-column label="视频时长" prop="courseVideoLearnDuration" sortable="courseVideoLearnDuration">
          <template slot-scope="{row}">
            {{row.courseVideoLearnDuration | formatSecond}}
          </template>
        </el-table-column> -->
        <el-table-column label="最后学习时间" prop="lastLearnTime" sortable="lastLearnTime" width="160">
          <template slot-scope="{ row }">
            {{ row.lastLearnTime | formatDateTime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="recordListQuery.totalCount > 0"
        :total="recordListQuery.totalCount"
        :page.sync="recordListQuery.page"
        :limit.sync="recordListQuery.MaxResultCount"
        @pagination="getRecordList"
      />
    </el-dialog>
    <el-dialog :visible="permissionLoading" :show-close="false" width="600px">
      <el-progress :percentage="progressValue" color="#409eff" />
    </el-dialog>
  </div>
</template>
<script>
import { courseCenterList, courseCenterDelete, courseCategoryList, courseRecordList, courseList, updateCourseRecords } from '@/api/course'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import { reject } from 'q'
export default {
  name: 'CourseCenter',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseCategoryId: '',
        FreeModel: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 课程分类列表
      courseCategoryList: [],

      recordDialog: false,
      recordList: [],
      recordListLoading: false,
      recordListQuery: {
        CourseId: '',
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: '',
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      permissionLoading: false,
      progressValue: 0
    }
  },
  created() {
    this.getCourseCenterList()
    this.getCourseCategoryList()
  },
  methods: {
    handleCourseCentenrEdit(t, row) {
      if (t === 0) {
        var url = this.$router.resolve({
          name: 'CenterEdit'
        })
        window.open(url.href, '_blank')
      } else {
        var url = this.$router.resolve({
          name: 'CenterEdit',
          query: { id: row.id }
        })
        window.open(url.href, '_blank')
      }
    },
    handleCourseCentenrDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseCenterDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getCourseCenterList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleViewCourseRecord(row) {
      var url = this.$router.resolve({
        name: 'CourseCenterRecord',
        query: { id: row.id, name: row.courseName, courseId: row.courseId }
      })
      window.open(url.href, '_blank')
      // this.recordListQuery.CourseId = row.courseId
      // this.getRecordList()
      // this.recordDialog = true
    },
    // 批量设置课程权限
    handleBatchCoursePermission(t) {
      if (t === 0) {
        this.$router.push({
          name: 'CoursePermission'
        })
      } else {
        this.$router.push({
          name: 'ClassCoursePermission'
        })
      }
    },
    async handleBatchUpdateClassHour() {
      const count = 100
      let tmpList = []
      var successPermission = 0
      const len = this.listQuery.totalCount % count === 0 ? this.listQuery.totalCount / count : (Math.floor(this.listQuery.totalCount / count) + 1)
      this.permissionLoading = true
      for (let i = 0; i < len; i++) {
        const data = {
          Filter: '',
          CourseCategoryId: '',
          FreeModel: null,
          Sorting: 'creationTime desc',
          SkipCount: i * count,
          MaxResultCount: count,
          page: 1,
          totalCount: 0
        }
        await courseCenterList(data).then(res => {
          tmpList = tmpList.concat(res.items)
        })
      }

      for await (const item of tmpList) {
        var data = {
          courseId: item.courseId
        }
        await updateCourseRecords(data)
        successPermission++
        this.progressValue = parseInt(((successPermission / tmpList.length) * 100).toFixed(0))
        await this.wait(15000)
      }
      this.permissionLoading = false
    },
    wait(ms) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(ms)
        }, ms)
      })
    },
    handleRefreshList(t) {
      if (t === 1) {
        this.recordListQuery.page = 1
        this.getRecordList()
      } else {
        this.listQuery.page = 1
        this.getCourseCenterList()
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getCourseCenterList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseCenterList()
    },
    recordSortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getRecordList()
        return
      }
      this.recordListQuery.Sorting = prop + ' ' + order
      this.getRecordList()
    },
    getCourseCenterList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseCenterList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getRecordList(id) {
      this.recordListLoading = true
      this.recordListQuery.SkipCount = (this.recordListQuery.page - 1) * this.recordListQuery.MaxResultCount
      courseRecordList(this.recordListQuery).then(res => {
        this.recordList = res.items
        this.recordListQuery.totalCount = res.totalCount
        this.recordListLoading = false
      }).catch(() => {
        this.recordListLoading = false
      })
    }
  }
}
</script>

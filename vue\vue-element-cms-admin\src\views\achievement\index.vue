<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="clearfix" style="height: 20px">
        <!-- <span class="role-span">成绩管理</span> -->
        <div style="">
          <el-radio-group v-model="active" size="small" style="margin-right: 15px" @change="checkStatusChange">
            <el-radio-button label="0">全部</el-radio-button>
            <el-radio-button label="1">未开始</el-radio-button>
            <el-radio-button label="2">进行中</el-radio-button>
            <el-radio-button label="3">已完成</el-radio-button>
          </el-radio-group>
          <el-input
            v-model="listQuery.Keyword"
            clearable
            size="small"
            placeholder="搜索..."
            style="width: 200px"
            class="filter-item"
            @input="searchContentChange"
            @keyup.enter.native="searchHandle"
          />
          <el-button round class="filter-item" size="small" type="success" icon="el-icon-search" @click="searchHandle">
            搜索
          </el-button>
          <export-excel
            :header="['考核名称', '类型', '试卷名称', '开始时间', '结束时间', '考核时长', '总分', '参与人数']"
            :filter-val="['name', 'examinationMode', 'examPaperName', 'startDate', 'endDate', 'timeLong', 'totalScore', 'studentCount']"
            :field="{ 1: ['考试', '练习', '外部'], 3: [2], 4: [2], 8: ['未开始', '进行中', '已结束'] }"
            :api-fn="examList"
          />
        </div>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        size="medium"
        style="width: 100%"
        @sort-change="sortChange"
        @current-change="handleSelectionChange"
      >
        <!-- <el-table-column label="序号" type="index" width="50px" align="center" /> -->
        <el-table-column prop="name" label="考核名称" sortable="name" min-width="300" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span class="link-type" @click="handleCheck(row)">{{
              row.name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="examinationMode" label="类型" sortable="examinationMode" width="100">
          <template slot-scope="{row}">
            <span v-if="row.examinationMode === 0">
              考试
            </span>
            <span v-if="row.examinationMode === 1">
              练习
            </span>
            <span v-if="row.examinationMode === 2">
              外部
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="examPaperName"
          label="试卷名称"
          sortable="examPaperName"
          min-width="300"
          show-overflow-tooltip
        >
          <template slot-scope="{row}">
            <span v-if="row.examinationMode === 2">{{ row.examUrl }}</span>
            <span v-else>{{
              row.examPaperName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" sortable="startDate" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.startDate | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束时间" sortable="endDate" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.endDate | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="timeLong" label="考核时长" sortable="timeLong" width="120">
          <template slot-scope="{row}">
            <span>{{
              row.timeLong
            }}分钟</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalScore" sortable="totalScore" label="总分" width="100">
          <template slot-scope="{row}">
            <span>{{
              row.totalScore
            }}分</span>
          </template>
        </el-table-column>
        <el-table-column prop="studentCount" sortable="studentCount" label="参与人数" width="100">
          <template slot-scope="{row}">
            <span>{{
              row.studentCount
            }}人</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="考核状态" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.status === 1" size="medium">未开始</el-tag>
            <el-tag v-if="row.status === 2" size="medium" type="success">进行中</el-tag>
            <el-tag v-if="row.status === 3" size="medium" type="info">已完成</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="isPublishScore" sortable="isPublishScore" label="公布成绩" width="120">
          <template slot-scope="{row}">
            <el-tag v-if="row.isPublishScore === 0" type="info" size="medium">未公布</el-tag>
            <el-tag v-if="row.isPublishScore === 1" size="medium" type="success">已公布</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="430">
          <template slot-scope="{row}">
            <div style="display: flex">
              <el-button
                v-if="row.status !== 1"
                round
                type="primary"
                size="mini"
                icon="el-icon-view"
                style="margin-right: 10px"
                @click="handleCheck(row)"
              >
                查看</el-button>
              <el-button
                v-if="row.status === 3 && row.isPublishScore === 0 && row.answerViewTiming === 0"
                round
                style="margin-left: 0px"
                type="warning"
                size="mini"
                icon="el-icon-document"
                @click="handlePublic(row)"
              >
                公布成绩</el-button>
              <div v-if="row.examinationMode === 2" style="display: flex">
                <el-upload
                  ref="fileUpload"
                  round
                  class="upload-demo"
                  action=""
                  :on-change="handleChange"
                  :on-remove="handleRemove"
                  :on-exceed="handleExceed"
                  :show-file-list="false"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                  :auto-upload="false"
                >
                  <el-button
                    :loading="importLoading"
                    :disabled="importLoading"
                    round
                    size="mini"
                    icon="el-icon-top"
                    :style="{ 'margin-left': row.status === 3 ? '10px' : '0px' }"
                    type="primary"
                  >导入成绩</el-button>
                </el-upload>
                <el-button
                  size="mini"
                  round
                  type="primary"
                  icon="el-icon-edit"
                  style="margin-left: 10px;"
                  @click="handleUploadFile(row)"
                >上传附件</el-button>
              </div>
            </div>
            <!-- <el-button v-if="row.status === 2" round type="primary" size="mini" icon="el-icon-download" @click="handleExport(row)">
              导出</el-button> -->
          </template>
        </el-table-column>

      </el-table>
      <pagination
        v-show="totalCount > 0"
        :total="totalCount"
        :page.sync="page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog title="上传附件" :close-on-click-modal="false" :visible.sync="uploadResourceDialog" width="1200px">
      <el-form label-width="80px">
        <el-form-item label="上传资源" prop="url">
          <local-file-upload
            class="uplaod_file"
            :multiple="true"
            :file-size="1024"
            :file-type="uploadFileType"
            :file-list="uploadResourceList"
            :btn-title="'上传资源'"
            @response-fn="handleFileResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
      </el-form>
      <el-table :data="fileList">
        <el-table-column label="文件名称" prop="fileName">
          <template slot-scope="{row}">
            <span class="link-type" @click="handlePreviewResource(row)">{{ row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="文件类型" prop="fileType" />
        <el-table-column label="文件大小" prop="size">

          <template slot-scope="{row}">
            {{ row.size | formatFileSize }}
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="size" width="200">
          <template slot-scope="{row}">
            <el-button round type="primary" size="mini" icon="el-icon-download" @click="handleFileDownload(row)">下载
            </el-button>
            <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleFileDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="fileListQuery.totalCount > 0"
        :total="fileListQuery.totalCount"
        :page.sync="fileListQuery.page"
        :limit.sync="fileListQuery.MaxResultCount"
        @pagination="getFileList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="uploadResourceList.length === 0" round type="primary" @click="handleUploadResourceSure">确
          定</el-button>
        <el-button round @click="uploadResourceDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源预览" append-to-body :visible.sync="resourcePreviewDialog" width="1000px" top="5vh">
      <bos-resource-preview
        v-if="resourcePreviewDialog && previewSource === 0"
        :type="previewType"
        :url="previewUrl"
        :doc-id="previewDocId"
      />
      <preview-resource
        v-if="resourcePreviewDialog && previewSource === 1"
        ref="previewResource"
        :type="previewType"
        :url="previewUrl"
      />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { examList, publishScore, examPaperImportAchive, examPaperImportFileDelete, examPaperImportFile, examPaperImportFileList } from '@/api/examPaper'
import LocalFileUpload from '@/components/LocalFileUpload'
import PreviewResource from '@/components/PreviewResource'
export default {
  name: 'Achievement',
  components: {
    Pagination,
    LocalFileUpload,
    PreviewResource
  },
  data() {
    return {
      list: [],
      listLoading: false,
      // 考核列表 form
      listQuery: {
        Status: 0,
        Keyword: '',
        Sorting: 'startDate desc',
        MaxResultCount: 10,
        SkipCount: 0
      },
      totalCount: 3,
      page: 1,
      // 考试状态
      active: 0,

      fileTemp: null,
      importLoading: false,

      uploadResourceDialog: false,
      uploadResourceList: [],
      uploadFileType: ['pdf', 'mp4', 'zip', 'png', 'jpg', 'jpeg'],
      fileList: [],
      fileListLoading: false,
      fileListQuery: {
        Filter: '',
        OfflineCourseId: '',
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 资源预览
      resourcePreviewDialog: false,
      previewSource: 0, // 0：bce 1：local
      previewUrl: '',
      previewDocId: '',
      previewType: '',

      selectionExamination: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    examList(args) {
      return examList(args)
    },
    handleSelectionChange(row) {
      this.selectionExamination = row?.id
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.page - 1) * this.listQuery.MaxResultCount
      examList(this.listQuery).then(res => {
        this.list = res.items
        this.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => { this.listLoading = false })
    },
    // 查看考核详情跳转详情页面
    handleCheck(row) {
      this.$router.push({
        name: 'AchievementDetail',
        query: { 'examid': row.id, 'examTitle': row.name, 'status': row.status }
      })
    },
    // 搜索
    searchHandle() {
      this.page = 1
      this.getList()
    },
    // 搜索框清空 请求列表
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    // 公布成绩
    handlePublic(row) {
      this.$confirm('公布成绩后无法取消, 是否确认公布?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        publishScore(row.id).then(res => {
          this.$notify({
            title: '成功',
            message: '公布成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        }).catch(() => {
          this.$notify({
            title: '失败',
            message: '公布失败',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {

      })
    },
    // 导出学生成绩
    handleExport() {

    },
    // 状态变化 全部 未开始  进行中 ....
    checkStatusChange() {
      this.page = 1
      this.listQuery.Status = this.active
      this.getList()
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleChange(file, fileList) {
      this.importLoading = true
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.importfxx(this.fileTemp)
        } else {
          this.importLoading = false
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.importLoading = false
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    handleExceed() {
      this.importLoading = false
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    handleRemove(file, fileList) {
      this.fileTemp = null
    },
    importfxx(file) {
      const _this = this
      this.file = file
      var reader = new FileReader()
      reader.onload = function(e) {
        var binary = ''
        var wb // 读取完成的数据
        var bytes = new Uint8Array(reader.result)
        var length = bytes.byteLength
        var xlsx = require('xlsx')
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        wb = xlsx.read(binary, {
          type: 'binary'
        })
        _this.da = xlsx.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        /* excel数据合法性验证 前端 【 和数据库数据对比不在此范围内处理 放到后台代码中 】*/
        if (_this.da.length > 0) {
          if (_this.da.length > 2000) {
            _this.importLoading = false
            _this.$message.error('Excel数据大于2000条')
            return
          }

          var data = {
            examinationId: _this.selectionExamination,
            userRecords: []
          }
          _this.da.forEach(item => {
            data.userRecords.push({
              userName: item['用户名'],
              score: item['成绩']
            })
          })
          examPaperImportAchive(data).then(res => {
            _this.$message.success('导入成功')
            _this.importLoading = false
            _this.getList()
          }).catch(() => {
            _this.$message.error('导入失败')
            _this.importLoading = false
          })
        } else {
          this.$message.error('Excel未读取到数据')
        }
      }
      reader.readAsArrayBuffer(file)
    },
    // 上传附件
    handleUploadFile(row) {
      this.uploadResourceList = []
      this.fileListQuery = {
        Filter: '',
        examinationId: row.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      }

      this.getFileList()
      this.uploadResourceDialog = true
    },
    // 上传附件结果
    handleFileResponse(url, fileForm) {
      this.uploadResourceList.push({
        trainingProjectId: this.$route.query.projectId,
        url: url,
        localUrl: fileForm.localUrl,
        fileName: fileForm.fileName,
        name: fileForm.fileName,
        hash: fileForm.hash,
        extend: fileForm.fileType,
        size: fileForm.size,
        fileType: fileForm.fileType,
        resType: fileForm.resType,
        tranStatus: fileForm.tranStatus,
        documentId: fileForm.documentId,
        jobId: fileForm.jobId,
        durationInSecond: fileForm.durationInSecond
      })
    },
    // 删除文件
    handleRemoveUploadFile(file) {
      var index = this.uploadResourceList.indexOf(file)
      if (index > -1) {
        this.uploadResourceList.splice(index, 1)
      }
    },
    // 删除上传成功的文件
    handleFileDelete(row) {
      examPaperImportFileDelete(row.id).then(res => {
        this.getFileList()
      })
    },
    // 资源选择确定 进行上传
    async handleUploadResourceSure() {
      if (!this.uploadResourceList.length) {
        this.$message.warning('请上传资源')
        return
      }
      var data_list = []

      this.uploadResourceList.forEach((item, index) => {
        var data = {
          fileName: item.fileName,
          fileType: item.fileType,
          url: item.localUrl,
          size: item.size,
          examinationId: this.selectionExamination
        }
        data_list.push(data)
      })
      for await (var item of data_list) {
        await examPaperImportFile(item).then(res => {

        })
      }

      this.getFileList()
      this.uploadResourceList = []
    },
    // 资源预览
    async handlePreviewResource(row) {
      switch (row.fileType) {
        case '.mp4':
          this.previewType = 'video'
          break
        case '.pdf':
          this.previewType = 'pdf'
          break
        case '.ppt':
        case '.doc':
          this.previewType = 'doc'
          break
        case '.png':
        case '.jpeg':
        case '.jpg':
          this.previewType = 'image'
          break
      }
      if (row.fileType === '.zip') {
        this.$message.warning('暂不支持此类型文件')
        return
      }
      this.previewSource = 1
      this.previewUrl = row.url

      this.resourcePreviewDialog = true
    },
    getFileList() {
      this.fileListQuery.SkipCount = (this.fileListQuery.page - 1) * this.fileListQuery.MaxResultCount
      examPaperImportFileList(this.fileListQuery).then(res => {
        this.fileList = res.items
        this.fileListQuery.totalCount = res.totalCount
      })
    },
    handleFileDownload(row) {
      const link = document.createElement('a') // 自己创建的a标签
      link.href = row.url
      link.download = row.fileName
      link.style.display = 'none'
      link.setAttribute('target', '_blank')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

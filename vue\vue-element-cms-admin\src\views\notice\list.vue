<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-05 08:37:19
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-08-05 15:19:59
 * @FilePath: /vue-element-cms-admin/src/views/notice/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-radio-group v-model="listQuery.AnnouncementState" size="small" @change="handleRefreshList">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">未发布</el-radio-button>
          <el-radio-button :label="1">已发布</el-radio-button>
        </el-radio-group>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['NoticeManagement.Announcements.Create']" size="small" round type="primary" icon="el-icon-plus" @click="handleNoticeEdit(0, 0)">添加</el-button>
        <!-- <export-excel :header="['标题', '状态', '是否置顶', '发布时间']" :filter-val="['title', 'announcementState', 'order', 'publishDate']" :field="{1: ['未发布', '已发布'], 2: ['否', '是'], 3: [2]}" :api-fn="noticeList" /> -->
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
        <el-table-column label="封面图" width="120">
          <template slot-scope="{ row }">
            <el-image v-if="row.imgUrl" :src="row.imgUrl" style="width: 80px; height: 45px" fit="cover">
              <div slot="error">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
            <i v-else class="el-icon-picture-outline" />
          </template>
        </el-table-column>
        <el-table-column label="标题" prop="title" sortable="title" min-width="200">
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleNoticeEdit(1, row)">{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分类" prop="category" width="100">
          <template slot-scope="{ row }">
            <span>{{ getCategoryName(row.category) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="announcementState" sortable="announcementState" width="80">
          <template slot-scope="{ row }">
            <el-tag v-if="row.announcementState === 0" type="info" size="mini">未发布</el-tag>
            <el-tag v-if="row.announcementState === 1" type="primary" size="mini">已发布</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否置顶" prop="order" sortable="order" width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.order === 1" type="primary" size="mini">是</el-tag>
            <el-tag v-if="row.order === 0" type="info" size="mini">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="日期" prop="publishDate" sortable="publishDate" width="160">
          <template slot-scope="{ row }">
            {{ row.publishDate | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350">
          <template slot-scope="{ row }">
            <el-button
              v-if="!row.announcementState"
              size="mini"
              round
              type="warning"
              icon="el-icon-s-promotion"
              @click="handleNoticePublish(row)"
            >{{ row.publish ? '取消发布' : '发布' }}</el-button>
            <el-button v-permission="['NoticeManagement.Announcements.Update']" size="mini" round type="primary" icon="el-icon-edit" @click="handleNoticeEdit(1, row)">编辑
            </el-button>
            <el-button v-permission="['NoticeManagement.Announcements.Delete']" size="mini" round type="danger" icon="el-icon-delete" @click="handleNoticeDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
<script>
import { noticeList, noticePublish, noticeDelete } from '@/api/other'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import ExportExcel from '@/components/ExportExcel/index.vue'
export default {
  name: 'NoticeList',
  directives: {
    permission
  },
  components: {
    Pagination,
    ExportExcel
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        AnnouncementState: null,
        App: '',
        Type: 1,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getCategoryName(category) {
      const categoryMap = {
        0: '标准解读',
        1: '行业咨询',
        2: '培训资讯',
        3: '课程资讯'
      }
      return categoryMap[category] || '未分类'
    },
    noticeList(args) {
      return noticeList(args)
    },
    handleNoticePublish(row) {
      this.$confirm('是否确定' + (!row.announcementState ? '发布?' : '取消发布?'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        noticePublish(row.id).then((res) => {
          this.$message.success((!row.announcementState ? '发布' : '取消发布') + '成功')
          this.getList()
        }).catch(() => {
          this.$message.error((!row.announcementState ? '发布' : '取消发布') + '失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleNoticeEdit(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'NoticeEdit'
        })
      } else {
        const url = this.$router.resolve({
          name: 'NoticeEdit',
          query: {
            name: row.title,
            id: row.id
          }
        })
        window.open(url.href, '_blank')
      }
    },
    handleNoticeDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        noticeDelete(row.id).then((res) => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      noticeList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }

  }
}
</script>

<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>培训包编辑——{{ pageTitle }}</span>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础信息" name="info">
          <el-form ref="packInfoForm" v-loading="packInfoFormLoading" :model="packInfoForm" :rules="packInfoRules" label-width="150px" style="margin-top: 20px;">
            <el-form-item label="名称" prop="name">
              <el-input v-model="packInfoForm.name" />
            </el-form-item>
            <el-form-item label="分类" prop="categoryId">
              <el-cascader v-model="packInfoForm.categoryId" filterable clearable :options="categoryList" :props="{'label': 'name','value': 'id','children': 'children','checkStrictly': true,'emitPath': false}" size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
              <!-- <el-select v-model="packInfoForm.categoryId" clearable placeholder="选择课程分类" class="form-select">
                <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select> -->
            </el-form-item>
            <el-form-item>
              <el-button round type="primary" icon="el-icon-check" @click="handleSavePackInfo">保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="培训包内容" name="content">
          <el-descriptions class="descriptions">
            <div slot="title" class="flex_between_box">
              <span>课程设置（ 共 {{ coursePackInfo.courseNumber }} 个课程，总课时数：{{ coursePackInfo.totalClassHour }}，资源总时长：{{ coursePackInfo.totalResourceDuration | formatSecond }}，视频时长：{{ coursePackInfo.totalVideoDuration | formatSecond }} ）</span>
              <div>
                <span>按顺序学习</span>
                <el-switch v-model="learnInOrder" style="margin: 0 15px;" active-color="#13ce66" inactive-color="#ff4949" @change="handleLearnInOrderChange" />
                <el-button round size="small" type="success" icon="el-icon-plus" @click="handlePackAddCourseClick">添加课程</el-button>
              </div>
            </div>
          </el-descriptions>
          <el-table v-loading="listLoading" :data="list" max-height="500px" highlight-current-row>
            <el-table-column label="排序" prop="order" width="80" />
            <el-table-column label="课程封面" width="200">
              <template slot-scope="{ row }">
                <el-image v-if="row.extraProperties" :src="row.extraProperties.CourseCoverUrl" class="course-cover" fit="cover">
                  <div slot="error">
                    <div class="image-slot">
                      <i class="el-icon-picture-outline" />
                    </div>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="课程名称" prop="courseName">
              <template slot-scope="{row}">
                <span style="margin-right: 10px">{{ row.courseName }}</span>
                <span v-if="row.courseSource === 0 && row.courseExpireState === 1" class="text-danger">已到期</span>
              </template>
            </el-table-column>
            <el-table-column label="资源总时长" prop="resourceDuration" width="180">
              <template slot-scope="{row}">
                <span>{{ row.resourceDuration | formatSecond }}</span>
              </template>
            </el-table-column>
            <el-table-column label="课时数" prop="classHour" width="120" />
            <el-table-column label="操作" width="180">
              <template slot-scope="{row}">
                <el-button round size="mini" type="primary" icon="el-icon-setting" @click="handleCourseExamPaperSetting(row)">考试设置</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- <div style="margin-top: 20px">考试设置</div> -->
          <el-descriptions title="考试设置" style="margin-top: 20px" />
          <el-form ref="packExam" v-loading="packExamFormLoading" :model="packExamForm" :rules="examSettingRules" label-width="100px">
            <el-form-item label="线上考试">
              <el-switch v-model="packExamOnLine" active-color="#13ce66" inactive-color="#ff4949" />
            </el-form-item>
            <div v-if="packExamOnLine">
              <el-form-item label="选择试卷" prop="examPaperId">
                <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleChooseExamPaperClick(0)">选择试卷</el-button>
                <el-table ref="packPaperTable" :data="packPaperList" highlight-current-row>
                  <el-table-column label="试卷名称" prop="name" />
                  <el-table-column label="题数" prop="questionNumber" />
                  <el-table-column label="总分" prop="totalScore" />
                </el-table>
              </el-form-item>
              <el-form-item label="考试名称" prop="examName">
                <el-input v-model="packExamForm.examName" />
              </el-form-item>
              <el-form-item label="考试时长" prop="examTimeLong">
                <el-input-number v-model="packExamForm.examTimeLong" :min="1" /><span>  分钟</span>
              </el-form-item>
            </div>
            <el-form-item>
              <el-button round type="primary" icon="el-icon-check" @click="handleTrainCoursePackExamSave">保存</el-button>
            </el-form-item>

          </el-form>

        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-dialog title="考试设置" :close-on-click-modal="false" :visible.sync="examPaperSettingDialog">
      <el-form ref="courseExam" v-loading="courseSettingLoading" :model="courseExamForm" :rules="examSettingRules" label-width="100px">
        <el-form-item label="线上考试">
          <el-switch v-model="courseExamOnLine" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>
        <div v-if="courseExamOnLine">
          <el-form-item label="选择试卷">
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleChooseExamPaperClick(1)">选择试卷</el-button>
            <el-table ref="coursePaperTable" v-loading="courseExamPaperLoading" :data="coursePaperList" highlight-current-row>
              <el-table-column label="试卷名称" prop="name" />
              <el-table-column label="题数" prop="questionNumber" />
              <el-table-column label="总分" prop="totalScore" />
            </el-table>
          </el-form-item>
          <el-form-item label="考试时长">
            <el-input-number v-model="courseExamForm.examTimeLong" :min="1" /><span>  分钟</span>
          </el-form-item>
          <el-form-item label="考试名称" prop="examName">
            <el-input v-model="courseExamForm.examName" />
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round :loading="dialogLoading" type="primary" @click="handleCourseExamPaperSettingSure">确 定</el-button>
        <el-button round @click="examPaperSettingDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择试卷" :close-on-click-modal="false" :visible.sync="examPaperChooseDialog">
      <choose-exam-paper v-if="examPaperChooseDialog" :current-paper="currentPaperId" @response="handleCoursePackSelectExamPaper" />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="examPaperChooseDialog = false">确 定</el-button>
        <el-button round @click="examPaperChooseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { courseCategoryList } from '@/api/course'
import { trainsPackEdit, trainsPackDetail, trainsCoursePackList, trainsPackLearnInOrder, trainsPackExamEdit, trainsPackExamDetail, trainsPackExamAdd, trainsPackExamDelete } from '@/api/train'
import ChooseExamPaper from '@/components/ChooseExamPaper'
import { examPaperDetailInfo } from '@/api/examPaper'
import moment from 'moment'
export default {
  name: 'PackEdit',
  components: {
    ChooseExamPaper
  },
  data() {
    var examPaperCheck = (rule, value, callback) => {
      if (this.isCourseExamEdit) {
        if (this.courseExamForm.examPaperId === '') {
          callback(new Error('请选择课程试卷'))
        }
      } else {
        if (this.packExamForm.examPaperId === '') {
          callback(new Error('请选择培训包试卷'))
        }
      }

      callback()
    }
    return {
      activeName: this.$route.query.from === -1 ? 'content' : 'info',
      list: [],
      listLoading: false,
      packInfoFormLoading: false,
      // 基础信息 form
      packInfoForm: {
        name: '',
        categoryId: ''
      },
      packInfoRules: {
        name: [
          { required: true, message: '请输入培训包名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ]
      },
      coursePackInfo: {
        courseNumber: 0,
        totalClassHour: 0,
        totalResourceDuration: 0,
        totalVideoDuration: 0
      },
      // dialog确定按钮Loading
      dialogLoading: false,
      // card标题
      pageTitle: '',
      // 是否按顺序学习
      learnInOrder: false,
      // 分类列表
      categoryList: [],
      // 当前选择的试卷
      currentPaperId: '',
      currentPackExamPaperId: '',
      // 考核设置Dialog
      examPaperSettingDialog: false,
      examPaperSettingForm: {
        TrainPackageId: this.$route.query.id,
        CourseId: ''
      },
      // 选择试卷Dialog
      examPaperChooseDialog: false,
      // 试卷列表
      coursePaperList: [],
      packPaperList: [],
      // 课程考核设置
      courseExamOnLine: false,
      courseExamForm: {
        trainPackageId: this.$route.query.id,
        trainPackageCourseId: '',
        examPaperId: '',
        courseId: '',
        courseName: '',
        examName: '',
        examTimeLong: 60
      },
      examSettingRules: {
        examPaperId: [
          { required: true, validator: examPaperCheck }
        ],
        examName: [
          { required: true, message: '请输入考试名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      // 培训包考核设置
      packExamOnLine: false,
      packExamFormLoading: false,
      packExamForm: {
        trainPackageId: this.$route.query.id,
        examPaperId: '',
        courseId: '',
        courseName: '',
        examName: '',
        examTimeLong: 60
      },
      // 课程/培训包 当前考核设置是否是第一次
      // 是否是课程考核编辑
      isCourseExamEdit: false,
      // 是否是课程包考核编辑
      isPackExamEdit: false,
      // 课程考核编辑Id
      courseExamEditId: '',
      // 课程包考核编辑Id
      packExamEditId: '',
      // 当前是否是编辑课程
      courseCurrentEdit: true,
      // 课程设置dialogloading 试卷loading
      courseSettingLoading: false,
      courseExamPaperLoading: false
    }
  },
  created() {
    this.getTrainsPackDetail()
    this.getCourseCategoryList()
    this.getTtrainsCoursePackList()
    this.getTrainsPackExamDetail()
  },
  methods: {
    // 保存基本信息
    handleSavePackInfo() {
      this.$refs.packInfoForm.validate((valid) => {
        if (valid) {
          this.packInfoFormLoading = true
          trainsPackEdit(this.$route.query.id, this.packInfoForm).then(res => {
            this.$message.success('保存成功')
            this.packInfoFormLoading = false
            this.getTrainsPackDetail()
          }).catch(() => {
            this.packInfoFormLoading = false
            this.$message.error('保存失败')
          })
        } else {
          return false
        }
      })
    },
    // 按顺序学习
    handleLearnInOrderChange(val) {
      var form = {
        id: this.$route.query.id,
        learnInOrder: val
      }
      trainsPackLearnInOrder(form).then(res => {
        this.$message.success('修改成功')
      }).catch(() => {
        this.learnInOrder = !val
        this.$message.error('修改失败')
      })
    },
    // 添加课程 跳转页面
    handlePackAddCourseClick() {
      this.$router.push({
        name: 'CourseSelect',
        query: { id: this.$route.query.id, name: this.pageTitle }
      })
    },
    // 考核设置
    handleCourseExamPaperSetting(row) {
      this.courseSettingLoading = true
      this.courseExamForm.courseId = row.courseId
      this.courseExamForm.courseName = row.courseName
      this.courseExamForm.trainPackageCourseId = row.id
      this.examPaperSettingForm.CourseId = row.courseId
      this.getTrainsPackExamDetail()
      this.examPaperSettingDialog = true
    },
    //  选择试卷按钮点击
    handleChooseExamPaperClick(t) {
      if (t === 0) {
        this.currentPaperId = this.currentPackExamPaperId
        this.courseCurrentEdit = false
      } else {
        this.courseCurrentEdit = true
      }
      this.examPaperChooseDialog = true
    },
    // 选择试卷返回值
    handleCoursePackSelectExamPaper(item) {
      if (item) {
        if (this.courseCurrentEdit) {
          this.coursePaperList = []
          this.courseExamForm.examPaperId = item.id
          // this.courseExamForm.examName = item.name
          this.coursePaperList.push(item)
        } else {
          this.packPaperList = []
          this.packExamForm.examPaperId = item.id
          // this.courseExamForm.examName = item.name
          this.packPaperList.push(item)
        }
      }
    },
    // 课程考核设置确定
    handleCourseExamPaperSettingSure() {
      this.dialogLoading = true
      if (this.courseExamOnLine) {
        this.$refs.courseExam.validate((valid) => {
          if (valid) {
            if (this.isCourseExamEdit) {
              this.putExamSettingInfo(this.courseExamEditId, this.courseExamForm)
            } else {
              this.postCourseExamSettingInfo(this.courseExamForm)
            }
          } else {
            this.dialogLoading = false
            return false
          }
        })
      } else {
        // 删除记录
        if (this.isCourseExamEdit) {
          this.deleteExamSettingInfo(this.courseExamEditId)
        } else {
          this.dialogLoading = false
          this.examPaperSettingDialog = false
        }
      }
    },
    // 保存考试设置
    handleTrainCoursePackExamSave() {
      this.isCourseExamEdit = false
      if (this.packExamOnLine) {
        this.$refs.packExam.validate((valid) => {
          if (valid) {
            if (this.isPackExamEdit) {
              this.putExamSettingInfo(this.packExamEditId, this.packExamForm)
            } else {
              this.postExamSettingInfo(this.packExamForm)
            }
          } else {
            return false
          }
        })
      } else {
        // 删除记录
        if (this.isPackExamEdit) {
          this.deleteExamSettingInfo(this.packExamEditId)
        } else {
          this.$message.success('设置成功')
          this.dialogLoading = false
        }
      }
    },
    postExamSettingInfo(data) {
      this.packExamFormLoading = true
      trainsPackExamAdd(data).then(res => {
        this.packExamFormLoading = false
        this.$message.success('设置成功')
        this.packExamEditId = res.id
        this.isPackExamEdit = true
      }).catch(() => {
        this.packExamFormLoading = false
        this.$message.error('设置失败')
      })
    },
    putExamSettingInfo(id, data) {
      this.packExamFormLoading = true
      trainsPackExamEdit(id, data).then(res => {
        this.packExamFormLoading = false
        this.$message.success('设置成功')
      }).catch(() => {
        this.packExamFormLoading = false
        this.$message.error('设置失败')
      })
    },
    postCourseExamSettingInfo(data) {
      trainsPackExamAdd(data).then(res => {
        this.examPaperSettingDialog = false
        this.dialogLoading = false
        this.$message.success('设置成功')
      }).catch(() => {
        this.dialogLoading = false
        this.$message.error('设置失败')
      })
    },
    deleteExamSettingInfo(id) {
      this.packExamFormLoading = true
      trainsPackExamDelete(id).then(res => {
        this.dialogLoading = false
        this.packExamFormLoading = false
        this.$message.success('设置成功')
      }).catch(() => {
        this.packExamFormLoading = false
        this.dialogLoading = false
        this.$message.error('设置失败')
      })
    },
    // 获取培训包基础信息
    getTrainsPackDetail() {
      trainsPackDetail(this.$route.query.id).then(res => {
        this.packInfoForm.name = res.name
        this.packInfoForm.categoryId = res.categoryId
        this.pageTitle = res.name
        this.learnInOrder = res.learnInOrder
      })
    },
    // 获取课程分类
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.categoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    // 获取培训包包含的课程
    getTtrainsCoursePackList() {
      trainsCoursePackList(this.$route.query.id).then(res => {
        this.list = res.items
        this.coursePackInfo.courseNumber = this.list.length
        res.items.forEach(item => {
          this.coursePackInfo.totalClassHour += item.classHour
          this.coursePackInfo.totalResourceDuration += item.resourceDuration
          this.coursePackInfo.totalVideoDuration += item.videoDuration
        })
      })
    },
    // 获取考核设置详情
    async getTrainsPackExamDetail() {
      const res = await trainsPackExamDetail(this.examPaperSettingForm)
      if (res.items.length) {
        if (this.examPaperSettingForm.CourseId) {
          this.courseExamForm.examPaperId = res.items[0].examPaperId
          this.currentPaperId = res.items[0].examPaperId
          this.courseExamForm.examName = res.items[0].examName
          this.courseExamForm.examTimeLong = res.items[0].examTimeLong
          this.courseExamOnLine = true
          this.isCourseExamEdit = true
          this.courseExamEditId = res.items[0].id
          this.courseSettingLoading = false
          this.courseExamPaperLoading = true
        } else {
          this.packExamForm.examPaperId = res.items[0].examPaperId
          this.currentPackExamPaperId = res.items[0].examPaperId
          this.packExamForm.examName = res.items[0].examName
          this.packExamForm.examTimeLong = res.items[0].examTimeLong
          this.packExamOnLine = true
          this.isPackExamEdit = true
          this.packExamEditId = res.items[0].id
        }
        this.getCurrentExamPaper(res.items[0].examPaperId)
      } else {
        if (this.examPaperSettingForm.CourseId) {
          this.courseExamOnLine = false
          this.isCourseExamEdit = false
          this.courseSettingLoading = false
          this.coursePaperList = []
          this.courseExamForm.examName = ''
          this.courseExamForm.examTimeLong = 60
        } else {
          this.packExamOnLine = false
          this.isPackExamEdit = false
          this.packPaperList = []
        }
      }
    },
    getCurrentExamPaper(id) {
      this.coursePaperList = []

      examPaperDetailInfo(id).then(res => {
        if (res) {
          if (this.examPaperSettingForm.CourseId) {
            this.coursePaperList.push(res)
            this.courseExamPaperLoading = false
          } else {
            this.packPaperList.push(res)
          }
        }
      }).catch(() => {
      })
    },
    checkExpireDate(date) {
      if (date) {
        if (moment().isBefore(date, 'm')) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    }
  }
}

</script>
<style scoped>
.descriptions ::v-deep .el-descriptions__title {
  width: 100%;
}
</style>

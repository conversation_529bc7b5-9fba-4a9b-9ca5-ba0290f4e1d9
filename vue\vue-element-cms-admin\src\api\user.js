/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-01 08:04:14
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-11 11:14:43
 * @FilePath: /vue-element-cms-admin/src/api/user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/axios'

export function loadNodes() {
  return axios.gets('/api/appuser/organization/loadNodes')
}
export function getAllStudents() {
  return axios.gets('/api/appuser/users/all')
}
// 导出获取符合筛选条件的学生
export function getFilterUsers(data) {
  return axios.gets('/api/appuser/users', data)
}
export function getTenantName(data) {
  return axios.gets('/api/appuser/userLookup/findTenantByUserName', data)
}

export function userLoginQRCode(name) {
  return axios.gets('/api/wechat-management/mini-programs/login/pc-login-acode?miniProgramName=' + name)
}
export function watchLoginStatus(token, clientId, clientSecret) {
  return axios.gets(`/api/wechat-management/mini-programs/login/pc-login?Token=` + token + `&MiniProgramName=Default&ClientId=` + clientId + `&ClientSecret=` + clientSecret)
}
// 获取忘记密码验证码
export function getForgetCode(data) {
  return axios.posts('/api/account/send-password-reset-code', data)
}
// 重置密码
export function restPassword(data) {
  return axios.posts('/api/account/reset-password', data)
}
// 查找空间名称
export function findName() {
  return axios.gets('/api/apptenant/tenants/find-name')
}

// 获取部门列表
export function orgsData() {
  return axios.gets('/api/appuser/organization/loadNodes')
}

// 班级列表
export function classesData() {
  return axios.gets('/api/appuser/classes/all')
}
// 获取班级子目录
export function classesDetail(data) {
  return axios.gets(`/api/appuser/classes`, data)
}

// 添加班级
export function addClasses(data) {
  return axios.posts('/api/appuser/classes', data)
}

// 修改班级
export function editClasses(id, data) {
  return axios.puts(`/api/appuser/classes/${id}`, data)
}

// 删除班级
export function deleteClasses(id) {
  return axios.deletes(`/api/appuser/classes/${id}`)
}

// 班级所有用户
export function classesUsers(data) {
  return axios.gets('/api/appuser/classes/users', data)
}

// 删除班级用户
export function deleteClassesUsers(data) {
  return axios.posts('/api/appuser/classes/delete-users', data)
}

// 导入班级用户
export function importClassesUsers(data) {
  return axios.posts('/api/appuser/classes/import-users', data)
}

// 学时管理
export function periodList(data) {
  return axios.gets('/api/appuser/credit-hours', data)
}
// 学时详情
export function creditHourDetailList(data) {
  return axios.gets('/api/appuser/credit-hours/detail', data)
}
// 学时详情
export function creditHourUsersDetailList(data) {
  return axios.gets('/api/appuser/credit-hours/users-detail', data)
}
// 学时录入
export function offLineCourseList(data) {
  return axios.gets('/api/cms/offline-courses', data)
}
export function addOffLineCourse(data) {
  return axios.posts('/api/cms/offline-courses', data)
}

export function editOffLineCourse(id, data) {
  return axios.puts(`/api/cms/offline-courses/${id}`, data)
}

export function offLineCourseDetail(id) {
  return axios.gets(`/api/cms/offline-courses/${id}`)
}

export function deleteOffLineCourse(id) {
  return axios.deletes(`/api/cms/offline-courses/${id}`)
}

export function importOffLineCourseHour(data) {
  return axios.posts(`/api/cms/offline-courses/records`, data)
}

// 线下课程记录
export function offLineCourseUserList(data) {
  return axios.gets(`/api/cms/offline-courses/records`, data)
}
export function deleteOffLineCourseUser(id) {
  return axios.deletes(`/api/cms/offline-courses/records/${id}`)
}


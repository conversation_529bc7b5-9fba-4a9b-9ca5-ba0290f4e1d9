export default {
  route: {
    dashboard: '首页呀',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    errorLog: '错误日志',
    profile: '个人中心',
    tenant: '租户管理',
    systemManagement: '系统管理',
    user: '用户管理',
    role: '角色管理',
    dict: '数据字典',
    org: '组织机构',
    job: '岗位管理',
    employee: '职员管理',
    tAidAuth: '学校授权管理',
    uAidAuth: '用户授权管理',
    tryApp: '试用申请管理',
    tag: '标签管理',
    tagType: '标签类型管理',
    taskTag: '任务标签',
    virAid: '虚拟教具管理',
    log: '日志',
    userSetting: '系统设置',
    app: '应用',
    appAuth: '应用授权',
    appAuthauth: '空间应用授权',
    abp: 'Indentity Server',
    clients: 'Clients',
    indentity: 'Indentity Data',
    api: 'API Resources',
    rotationChart: '轮播图管理'
  },
  navbar: {
    dashboard: '首页呀',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    Dashboard: '首页'
  },
  login: {
    title: '汽车学园',
    logIn: '登录',
    tenant: '租户',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 el-tab 或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  errorLog: {
    tips: '请点击右上角bug小图标',
    description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',
    documentation: '文档介绍'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  }
}

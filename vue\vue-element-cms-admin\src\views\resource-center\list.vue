<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-select v-model="listQuery.KnowledgeCategoryId" size="small" placeholder="选择资源分类" @change="handleRefreshList">
          <el-option label="全部" :value="''" />
          <el-option v-for="item in resourceCategoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <!-- <el-select size="small"  placeholder="是否免费"  v-model="listQuery.FreeModel" @change="handleRefreshList">
          <el-option label="免费" :value="0" />
          <el-option label="收费" :value="1" />
        </el-select> -->
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button v-permission="['CourseManagement.KnowledgeCenters']" round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['CourseManagement.KnowledgeCenters.Update']" round size="small" type="primary" icon="el-icon-plus" @click="handleResourceCentenrEdit(0,0)">添加</el-button>
        <el-button v-permission="['CourseManagement.KnowledgeCenters.Update']" round size="small" type="primary" icon="el-icon-edit" @click="handleBatchResourcePermission(0)">批量设置资源权限</el-button>
        <el-button v-permission="['AppUserManagement.Classes']" round size="small" type="primary" icon="el-icon-edit" @click="handleBatchResourcePermission(1)">按班级批量设置课程权限</el-button>
        <export-excel :header="['资源封面', '资源名称', '创建时间']"
              :filter-val="['knowledgeThumbnailUrl', 'knowledgeName', 'creationTime']"
              :field="{ 2: [2] }" :api-fn="resourceCenterList" />
      </div>
      <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
        <el-table-column label="资源封面" prop="knowledgeThumbnailUrl" sortable="knowledgeThumbnailUrl" width="100">
          <template slot-scope="{ row }">
            <el-image :src="row.knowledgeThumbnailUrl" class="resource-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="资源名称" prop="knowledgeName" sortable="knowledgeName" min-width="200" />
        <!-- <el-table-column label="学习期限" prop="learningPeriod" sortable="learningPeriod" width="100" /> -->
        <!-- <el-table-column label="免费/收费" prop="freeModel" sortable="freeModel" width="150">
          <template slot-scope="{row}">
            <el-tag size="mini" v-if="row.freeModel === 0" type="success">免费</el-tag>
            <el-tag size="mini" v-if="row.freeModel === 1" type="warning">收费</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="{row}">
            <!-- <el-button round size="mini" type="primary" icon="el-icon-view" @click="handleViewResourceRecord(row)">学习情况</el-button> -->
            <el-button v-permission="['CourseManagement.KnowledgeCenters.Update']" round size="mini" type="primary" icon="el-icon-edit" @click="handleResourceCentenrEdit(1,row)">编辑</el-button>
            <el-button v-permission="['CourseManagement.KnowledgeCenters.Delete']" round size="mini" type="danger" icon="el-icon-delete" @click="handleResourceCentenrDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getResourceCenterList"
      />
    </el-card>
    <el-dialog title="详情" :visible.sync="recordDialog" width="1000px">
      <el-input v-model="recordListQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
      <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      <el-table v-loading="recordListLoading" :data="recordList" size="small" highlight-current-row @sort-change="recordSortChange">
        <el-table-column label="用户名" prop="userName" sortable="userName" />
        <el-table-column label="姓名" prop="userTrueName" sortable="userTrueName" />
        <el-table-column label="资源总时长" prop="resourceDuration" sortable="resourceDuration">
          <template slot-scope="{row}">
            {{ row.resourceDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学习时长" prop="resourceLearnDuration" sortable="resourceLearnDuration">
          <template slot-scope="{row}">
            {{ row.resourceLearnDuration | formatSecond }}
          </template>
        </el-table-column>
        <el-table-column label="学习进度" prop="resourceLearnProgress" sortable="resourceLearnProgress">
          <template slot-scope="{row}">
            {{ row.resourceLearnProgress.toFixed(2) }} %
          </template>
        </el-table-column>
        <!-- <el-table-column label="视频时长" prop="resourceVideoLearnDuration" sortable="resourceVideoLearnDuration">
          <template slot-scope="{row}">
            {{row.resourceVideoLearnDuration | formatSecond}}
          </template>
        </el-table-column> -->
        <el-table-column label="最后学习时间" prop="lastLearnTime" sortable="lastLearnTime">
          <template slot-scope="{ row }">
            {{ row.lastLearnTime | formatDateTime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="recordListQuery.totalCount > 0"
        :total="recordListQuery.totalCount"
        :page.sync="recordListQuery.page"
        :limit.sync="recordListQuery.MaxResultCount"
        @pagination="getRecordList"
      />
    </el-dialog>
  </div>
</template>
<script>
import { resourceCenterList, resourceCenterDelete, resourceCategoryList, resourceRecordList, resourceList } from '@/api/resource'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'ResourceCenter',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        KnowledgeCategoryId: '',
        FreeModel: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 资源分类列表
      resourceCategoryList: [],

      recordDialog: false,
      recordList: [],
      recordListLoading: false,
      recordListQuery: {
        ResourceId: '',
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: '',
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      }
    }
  },
  created() {
    this.getResourceCenterList()
    this.getResourceCategoryList()
  },
  methods: {
    resourceCenterList(args) {
      return resourceCenterList(args)
    },
    handleResourceCentenrEdit(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'ResourceEdit'
        })
      } else {
        this.$router.push({
          name: 'ResourceEdit',
          query: { id: row.id }
        })
      }
    },
    handleResourceCentenrDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resourceCenterDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getResourceCenterList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleViewResourceRecord(row) {
      this.recordListQuery.ResourceId = row.resourceId
      this.getRecordList()
      this.recordDialog = true
    },
    // 批量设置资源权限
    handleBatchResourcePermission(t) {
      if (t === 0) {
        this.$router.push({
          name: 'ResourcePermission'
        })
      } else {
        this.$router.push({
          name: 'ClassResourcePermission'
        })
      }
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getResourceCenterList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getResourceCenterList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceCenterList()
    },
    recordSortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getRecordList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceCenterList()
    },
    getResourceCenterList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceCenterList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    getResourceCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      resourceCategoryList(data).then(res => {
        this.resourceCategoryList = res.items
      })
    },
    getRecordList(id) {
      this.recordListLoading = true
      this.recordListQuery.SkipCount = (this.recordListQuery.page - 1) * this.recordListQuery.MaxResultCount
      resourceRecordList(this.recordListQuery).then(res => {
        this.recordList = res.items
        this.recordListQuery.totalCount = res.totalCount
        this.recordListLoading = false
      }).catch(() => {
        this.recordListLoading = false
      })
    }
  }
}
</script>

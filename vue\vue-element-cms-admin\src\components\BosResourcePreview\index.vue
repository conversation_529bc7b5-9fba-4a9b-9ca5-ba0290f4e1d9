<template>
  <div class="resource_preview">
    <!-- <video-player v-if="type === 'video'" ref="videoPlayer" class="video-player vjs-custom-skin" :playsinline="true" :options="playerOptions" /> -->
    <iframe
      v-if="type === 'video'"
      ref=""
      class="video_div"
      :src="'../player.html?url='+url"
    />
    <!-- <div v-if="type === 'video'" class="video_div">
      <div id="playercontainer" />
    </div> -->
    <iframe
      v-if="type === 'pdf'"
      ref=""
      class="doc_div"
      :src="'./pdfjs/web/viewer.html?file=' +url"
    />
    <iframe
      v-if="type === 'doc'"
      ref=""
      class="doc_div"
      :src="'../doc.html?url='+docId"
    />
    <iframe
      v-if="type === 'h5'"
      ref=""
      class="doc_div"
      :src="url"
    />
    <el-image v-if="type === 'image'" class="image_preview" fit="contain" :src="url" />
  </div>

</template>
<script>

export default {
  name: 'BosResourcePreview',
  components: {

  },
  props: {
    url: {
      type: String,
      require: true,
      default: ''
    },
    docId: {
      type: String,
      require: true,
      default: ''
    },
    type: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      player: null
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>
<style scoped>
.resource_preview{
    width: 100%;
    min-height: 600px;
}
.video_div{
  width: 100%;
  height: 600px;
  border: none;
}
.doc_div{
  width: 100%;
  height: 700px;
  border: 1px solid #eee;
}
.image_preview {
  width: 100%;
  height: 100%
}

</style>

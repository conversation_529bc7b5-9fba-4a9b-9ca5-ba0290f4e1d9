<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container" />
    <!--表单渲染-->
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :title="formTitle"
      width="600px"
      @close="cancel()"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="应用图标" prop="picUrl">
          <lz-upload-images
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="应用类型" prop="appType">
          <el-select v-model="form.appType" placeholder="请选择应用类型">
            <el-option label="Chat" :value="0" />
            <el-option label="Agent" :value="1" />
            <el-option label="Workflow" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="AI类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option label="专家" :value="0" />
            <el-option label="工具" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用地址" prop="url">
          <el-input v-model="form.url" placeholder="请输入应用地址" />
        </el-form-item>
        <el-form-item label="AppId" prop="appId">
          <el-input v-model="form.appId" placeholder="请输入AppId" />
        </el-form-item>
        <el-form-item label="AppKey" prop="appKey">
          <el-input v-model="form.appKey" placeholder="请输入AppKey" />
        </el-form-item>
        <el-form-item label="序号" prop="sort">
          <el-input-number v-model="form.sort" :min="0" />
        </el-form-item>
        <el-form-item label="介绍" prop="introduce">
          <el-input v-model="form.introduce" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round :loading="formLoading" type="primary" @click="save">确 定</el-button>
        <el-button round @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-row :gutter="15">
      <el-card class="box-card">
        <div slot="header" class="flex_between_box">
          <span>AI智能体管理</span>
          <div>
            <el-button
              class="filter-item"
              size="small"
              type="primary"
              icon="el-icon-plus"
              round
              @click="handleCreate"
            >新增</el-button>
          </div>
        </div>

        <!--表格渲染-->
        <el-table
          ref="multipleTable"
          v-loading="listLoading"
          :data="list"
          style="width: 100%"
          highlight-current-row
          @sort-change="sortChange"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55px" align="center" />
          <el-table-column prop="sort" sortable="sort" label="序号" />
          <el-table-column prop="picUrl" label="应用图标" width="200">
            <template slot-scope="{ row }">
              <el-image :src="row.picUrl" style="width: 120px; height: 160px" fit="cover">
                <div slot="error">
                  <div class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="name" sortable="custom" label="应用名称" />
          <el-table-column prop="type" sortable="custom" label="AI类型">
            <template slot-scope="scope">
              <span v-if="scope.row.type === 0">专家</span>
              <span v-else-if="scope.row.type === 1">工具</span>
            </template>
          </el-table-column>
          <el-table-column prop="appType" sortable="custom" label="应用类型">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.appType === 0" size="mini" type="success">Chat</el-tag>
              <el-tag v-else-if="scope.row.appType === 1" size="mini" type="warning">Agent</el-tag>
              <el-tag v-else size="mini" type="info">Workflow</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="url" sortable="custom" label="应用地址" />
          <!-- <el-table-column prop="appId" sortable="custom" label="AppId" />
          <el-table-column prop="appKey" sortable="custom" label="AppKey" /> -->
          <el-table-column label="操作" width="200px">
            <template slot-scope="{ row }">
              <el-button
                type="primary"
                size="mini"
                round
                icon="el-icon-edit"
                @click="handleUpdate(row)"
              >编辑</el-button>
              <el-button
                type="danger"
                size="mini"
                round
                icon="el-icon-delete"
                @click="handleDelete(row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="totalCount > 0"
          :total="totalCount"
          :page.sync="page"
          :limit.sync="listQuery.MaxResultCount"
          @pagination="getList"
        />
      </el-card>
    </el-row>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import LzUploadImages from '@/components/LzUploadImages'
import {
  getAgents,
  addAgent,
  updateAgent,
  deleteAgent
} from '@/api/ai'

export default {
  name: 'AgentsList',
  components: {
    Pagination,
    LzUploadImages
  },
  data() {
    var checkPicUrl = (rule, value, callback) => {
      if (this.form.picUrl === '' || this.form.picUrl === null) {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        name: [{
          required: true,
          message: '请输入应用名称',
          trigger: 'blur'
        }],
        picUrl: [{
          required: true,
          validator: checkPicUrl,
          trigger: 'change'
        }],
        url: [{
          required: true,
          message: '请输入应用地址',
          trigger: 'blur'
        }],
        // appId: [{
        //   required: true,
        //   message: '请输入AppId',
        //   trigger: 'blur'
        // }],
        appKey: [{
          required: true,
          message: '请输入AppKey',
          trigger: 'blur'
        }],
        appType: [{
          required: true,
          message: '请选择应用类型',
          trigger: 'change'
        }]
      },
      list: null,
      totalCount: 0,
      listLoading: true,
      listQuery: {
        Sorting: 'sort',
        SkipCount: 0,
        MaxResultCount: 10
      },
      page: 1,
      dialogFormVisible: false,
      formTitle: '',
      isEdit: false,
      form: {
        name: '',
        url: '',
        picUrl: '',
        appId: '',
        appKey: '',
        appType: 0,
        type: 0,
        sort: 0,
        introduce: ''
      },
      formLoading: false,
      multipleSelection: [],
      previewFileList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      getAgents(this.listQuery).then((response) => {
        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    handleCreate() {
      this.formTitle = '新增智能应用'
      this.isEdit = false
      this.form = {
        name: '',
        url: '',
        picUrl: '',
        appId: '',
        appKey: '',
        introduce: '',
        type: 0,
        sort: this.list.length + 1,
        appType: 0
      }
      this.previewFileList = []
      this.dialogFormVisible = true
    },
    handleDelete(row) {
      if (row) {
        this.$confirm('是否删除' + row.name + '?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteAgent(row.id).then((response) => {
              const index = this.list.indexOf(row)
              this.list.splice(index, 1)
              this.$notify({
                title: '成功',
                message: '删除成功',
                type: 'success',
                duration: 2000
              })
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        if (this.multipleSelection.length == 0) {
          this.$message({
            message: '请选择要删除的行',
            type: 'warning'
          })
          return
        }
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.multipleSelection.forEach((element) => {
              deleteAgent(element.id).then((response) => {
                const index = this.list.indexOf(element)
                this.list.splice(index, 1)
              })
            })
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      }
    },
    handleUpdate(row) {
      this.formTitle = '修改智能应用'
      this.isEdit = true
      if (row) {
        this.form = { ...row }
        if (this.form.picUrl && this.form.picUrl.length) {
          this.previewFileList = [{
            url: this.form.picUrl
          }]
        }
        this.dialogFormVisible = true
      } else {
        if (this.multipleSelection.length != 1) {
          this.$message({
            message: '编辑必须选择单行',
            type: 'warning'
          })
          return
        } else {
          this.form = { ...this.multipleSelection[0] }
          if (this.form.picUrl && this.form.picUrl.length) {
            this.previewFileList = [{
              url: this.form.picUrl
            }]
          }
          this.dialogFormVisible = true
        }
      }
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            updateAgent(this.form.id, this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
              .catch(() => {
                this.formLoading = false
              })
          } else {
            addAgent(this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
              .catch(() => {
                this.formLoading = false
              })
          }
        }
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.handleFilter()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleFilter()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    cancel() {
      this.dialogFormVisible = false
      this.$refs.form.clearValidate()
    },
    // 上传文件成功回调
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.picUrl = url
    },
    // 上传文件删除
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.picUrl = ''
    }
  }
}
</script>

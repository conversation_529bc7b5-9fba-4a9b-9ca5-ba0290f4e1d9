import axios from '@/axios'

// 获取云盘空间大小
export function cloudSpaceSize() {
  return axios.gets('/api/cloud-resource/my-resource/used')
}

// 获取STS-TOKEN
// ParentId
export function butcketStsToken() {
  return axios.gets('/api/cloud-resource/bce/sts-token')
}

// 获取上传地址
/**
  params {
    extend 文件后缀 .jpg
    hash 文件hash
    isPublish  图片为true 其他false
  }
 * @returns
 */
export function reourceUploadPath(data) {
  return axios.gets('/api/cloud-resource/bce/get-upload-path', data)
}

// 提交云盘记录
/**
  params {
    "hash": "string",
    "fileName": "string",
    "fileType": "string",
    "url": "string",
    "size": 0,
    "durationInSecond": 0,
    "thumbnailUrl": "string",
    "isPublic": true
  }
 * @returns
 */
export function cloudResource(data) {
  return axios.posts('/api/cloud-resource/resource', data)
}

// 根据hash获取文件地址
/**
 * params {
    "hash": "string"
  }
 * @returns
 */
export function cloudResourceUrl(data) {
  return axios.gets('/api/cloud-resource/resource/get-by-hash', data)
}

// 资源预览下载获取地址
// url
export function resourcePath(data) {
  return axios.gets(`/api/cloud-resource/bce/get-presigned-url`, data)
}

// 本地上传
export function uploadFile(data) {
  return axios.uploadfile('/api/file-management/file', data)
}

export function uploadFileProgress(data, fn) {
  return axios.uploadFileProgress('/api/file-management/file', data, fn)
}
export function getFileDownloadInfo(id) {
  return axios.gets(`/api/file-management/file/${id}/download-info`)
}

export function getFileContent(url) {
  return axios.localFileContent(url)
}

// 获取图片base64
export function getFileBase64(url) {
  return axios.gets(url)
}

export function getBaoCloudResourceUrl(url) {
  return axios.gets('api/cms/baocloud/get-presigned-url?url=' + url)
}

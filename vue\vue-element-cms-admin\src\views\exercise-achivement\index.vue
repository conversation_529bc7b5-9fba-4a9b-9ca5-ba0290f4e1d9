<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="clearfix" style="height: 20px">
        <div style="">
          <el-date-picker v-model="listQuery.Day" size="small" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期时间" @change="dateTimeChange" />
          <el-input v-model="listQuery.Filter" clearable size="small" placeholder="搜索..." style="width: 200px;margin-left: 10px;" class="filter-item" @input="searchContentChange" @keyup.enter.native="searchHandle" />
          <el-button round class="filter-item" size="small" type="success" icon="el-icon-search" @click="searchHandle">搜索
          </el-button>
          <el-button :loading="exportLoading" :disabled="exportLoading" round class="filter-item" size="small" type="success" icon="el-icon-download" @click="handleExportExerciseRecord">导出
          </el-button>
        </div>
      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" style="width: 100%" @sort-change="sortChange">
        <el-table-column prop="userTrueName" label="姓名" sortable="userTrueName" width="120" />
        <el-table-column prop="exercisePaperName" label="练习名称" sortable="exercisePaperName" min-width="200" />
        <el-table-column prop="startDate" label="开始时间" sortable="startDate" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.startDate | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="submitDate" label="提交时间" sortable="submitDate" width="160">
          <template slot-scope="{row}">
            <span>{{
              row.submitDate | formatDatetime
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="timeLong" label="练习时长" sortable="timeLong" width="120">
          <template slot-scope="{row}">
            <span>{{
              row.submitDate ? getDiffTime(row.submitDate,row.startDate) : '--'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalScore" sortable="totalScore" label="得分" width="100">
          <template slot-scope="{row}">
            <span>{{
              row.totalScore
            }}分</span>
          </template>
        </el-table-column>
        <el-table-column prop="rightCount" sortable="rightCount" label="正确题数" width="100" />
        <el-table-column prop="errorCount" sortable="errorCount" label="错误题数" width="100" />
        <!-- <el-table-column prop="quitCount" sortable="quitCount" label="未答题数" width="100" /> -->
        <el-table-column prop="rightRate" sortable="rightRate" label="正确率" width="100">
          <template slot-scope="{row}">
            <!-- <span>{{
              row.submitDate ? ((row.rightCount / (row.rightCount + row.errorCount + row.quitCount)*100).toFixed(2) + '%') : '--'
            }}</span> -->
            <span>{{ row.rightRate }} %</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { exerciseRecord } from '@/api/exercise'
import { formatSecond, formatDate, parseTimeDate, formatDateTime } from '@/utils'
import moment from 'moment'
export default {
  name: 'Achievement',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      // 考核列表 form
      listQuery: {
        ExerciseBankId: '',
        Filter: '',
        Sorting: 'creationTime desc',
        Day: '',
        MaxResultCount: 10,
        SkipCount: 0,
        page: 1,
        totalCount: 0
      },
      exportLoading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      exerciseRecord(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => { this.listLoading = false })
    },
    getDiffTime(time1, time2) {
      return formatSecond(moment(time1).diff(moment(time2), 's'))
    },
    dateTimeChange(val) {
      // this.listQuery.Day = this.dateTime + ''

      this.getList()
    },
    async handleExportExerciseRecord() {
      this.exportLoading = true
      var data = {
        ExerciseBankId: '',
        Filter: this.listQuery.Filter,
        Sorting: 'creationTime desc',
        Day: this.listQuery.Day,
        MaxResultCount: 500,
        SkipCount: 0,
        page: 1,
        totalCount: 0
      }
      const res = await exerciseRecord(data)
      var tmp = res.items
      data.totalCount = res.totalCount
      if (tmp.length < data.totalCount) {
        const len = data.totalCount % data.MaxResultCount === 0 ? data.totalCount / data.MaxResultCount : (Math.floor(data.totalCount / data.MaxResultCount) + 1)
        for (let i = 1; i < len; i++) {
          data.page = i + 1
          data.SkipCount = (data.page - 1) * data.MaxResultCount
          await exerciseRecord(data).then(res => {
            tmp = tmp.concat(res.items)
          })
        }
      }
      var list = []
      tmp.forEach(item => {
        list.push({
          ...item,
          startDate: formatDateTime(item.startDate),
          submitDate: formatDateTime(item.submitDate),
          timeLong: formatSecond(item.timeLong),
          rightRate: item.rightRate + '%'
        })
      })
      this.exportLoading = false
      import('@/vendor/Export2Excel').then((excel) => {
        const tHeader = ['姓名', '练习名称', '开始时间', '提交时间', '练习时长', '得分', '正确题数', '错误题数', '正确率']
        const filterVal = ['userTrueName', 'exercisePaperName', 'startDate', 'submitDate', 'timeLong', 'totalScore', 'rightCount', 'errorCount', 'rightRate']
        // 成绩列表数据
        // const list = Response.items;
        const data = this.formatJson(filterVal, list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: '练习记录_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    // 搜索
    searchHandle() {
      this.page = 1
      this.getList()
    },
    // 搜索框清空 请求列表
    searchContentChange(val) {
      if (val === '' || val.length === 0) {
        this.page = 1
        this.getList()
      }
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    }
  }
}
</script>

import axios from '@/axios'
// ---------------------------------------------------------
// 培训管理
export function trainsList(data) {
  return axios.gets('/api/cms/trains', data)
}

export function trainsDetail(id) {
  return axios.gets(`/api/cms/trains/${id}`)
}

export function trainsAdd(data) {
  return axios.posts('/api/cms/trains', data)
}

export function trainsAddUser(data) {
  return axios.posts('/api/cms/trains/users/add-many', data)
}

export function trainsDeleteUser(data) {
  return axios.posts('/api/cms/trains/users/delete-many', data)
}
export function trainUsersUpdate(data) {
  return axios.posts('/api/cms/trains/users/update?trainId=' + data)
}
export function trainsAddCourse(data) {
  return axios.posts('/api/cms/trains/courses', data)
}

export function trainsEditCourse(id, data) {
  return axios.puts(`/api/cms/trains/courses/${id}`, data)
}

export function trainsDeleteCourse(data) {
  return axios.posts(`/api/cms/trains/courses/delete-by-course`, data)
}

export function trainsCourseList(data) {
  return axios.gets('/api/cms/trains/courses', data)
}

export function trainsLiveList(data) {
  return axios.gets('/api/lms/lives/train', data)
}

export function trainsAddExam(data) {
  return axios.posts('/api/cms/trains/exams', data)
}

export function trainsEditExam(id, data) {
  return axios.puts(`/api/cms/trains/exams/${id}`, data)
}

export function trainsDeleteExam(id) {
  return axios.deletes(`/api/cms/trains/exams/${id}`)
}

export function trainsAddOffLineCourse(data) {
  return axios.posts('/api/cms/offline-courses', data)
}

export function trainsEditOffLineCourse(id, data) {
  return axios.puts(`/api/cms/offline-courses/${id}`, data)
}

export function trainsDeleteOffLineCourse(id) {
  return axios.deletes(`/api/cms/offline-courses/${id}`)
}

export function importOffLineCourseHour(data) {
  return axios.posts(`/api/cms/offline-courses/records`, data)
}

export function offLineCourseRecord(data) {
  return axios.gets(`/api/cms/offline-courses/records`, data)
}

export function uploadOffLineCourseFile(data) {
  return axios.posts(`/api/cms/offline-courses/files`, data)
}

export function deleteOffLineCourseFile(id) {
  return axios.deletes(`/api/cms/offline-courses/files/${id}`)
}

export function offLineCourseFileList(data) {
  return axios.gets(`/api/cms/offline-courses/files`, data)
}

export function trainsOffLineCourseList(data) {
  return axios.gets('/api/cms/offline-courses', data)
}

export function trainsOffLineExamList(data) {
  return axios.gets('/api/exams/offline', data)
}

export function offLineExamRecord(data) {
  return axios.gets('/api/exams/offline/records', data)
}

export function trainsAddOffLineExam(data) {
  return axios.posts('/api/exams/offline', data)
}

export function trainsEditOffLineExam(id, data) {
  return axios.puts(`/api/exams/offline/${id}`, data)
}

export function trainsDeleteOffLineExam(id, data) {
  return axios.deletes(`/api/exams/offline/${id}`, data)
}

export function importOffLineExamScore(data) {
  return axios.posts('/api/exams/offline/records', data)
}

export function offLineExamFileList(data) {
  return axios.gets('/api/exams/offline/files', data)
}

export function uploadOffLineExamFile(data) {
  return axios.posts('/api/exams/offline/files', data)
}

export function deleteOffLineExamFile(id) {
  return axios.deletes(`/api/exams/offline/files/${id}`)
}

export function trainsPublish(id) {
  return axios.posts('/api/cms/trains/publish?id=' + id)
}
export function trainsEdit(id, data) {
  return axios.puts(`/api/cms/trains/${id}`, data)
}

export function trainsDelete(id) {
  return axios.deletes(`/api/cms/trains/${id}`)
}

export function trainsUser(data) {
  return axios.gets(`/api/cms/trains/users`, data)
}
export function trainsUserAll(id) {
  return axios.gets(`/api/cms/trains/users/all`, { trainId: id })
}
export function trainsRecord(id) {
  return axios.gets(`/api/cms/public/train/user-record`, { trianId: id })
}

// 编辑后获取所有考试
export function trainsExamList(id) {
  return axios.gets(`/api/cms/trains/exams`, { TrainId: id })
}
// --------------------------------------------------------------
// 培训包管理
export function trainsPackList(data) {
  return axios.gets('/api/cms/trains/packages', data)
}

export function trainsPackDetail(id) {
  return axios.gets(`/api/cms/trains/packages/${id}`)
}

export function trainsPackAdd(data) {
  return axios.posts('/api/cms/trains/packages', data)
}

export function trainsPackEdit(id, data) {
  return axios.puts(`/api/cms/trains/packages/${id}`, data)
}

export function trainsPackDelete(id) {
  return axios.deletes(`/api/cms/trains/packages/${id}`)
}

export function trainsPackLearnInOrder(data) {
  return axios.posts('/api/cms/trains/packages/set-learn-in-order', data)
}
// 培训包是否有过期课程
export function trainsPackHasExpire(id) {
  return axios.gets(`/api/cms/trains/package-courses/has-expire-course`, { id: id })
}
// --------------------------------------------------------------
// 培训培训包管理

export function trainsCoursePackList(id) {
  return axios.gets('/api/cms/trains/package-courses', { TrainPackageId: id })
}

export function trainsCoursePackDetail(id) {
  return axios.gets(`/api/cms/trains/package-courses/${id}`)
}

export function trainsCoursePackAdd(data) {
  return axios.posts('/api/cms/trains/package-courses', data)
}

export function trainsCoursePackEdit(id, data) {
  return axios.puts(`/api/cms/trains/package-courses/${id}`, data)
}

export function trainsCoursePackDelete(id) {
  return axios.deletes(`/api/cms/trains/package-courses/${id}`)
}

export function trainsCoursePackUpdate(data) {
  return axios.posts('/api/cms/trains/package-courses/update-courses', data)
}

// --------------------------------------------------------------
// 培训包考试管理

export function trainsPackExamList(data) {
  return axios.gets('/api/cms/trains/package-exams', data)
}
// 考核设置详情
export function trainsPackExamDetail(data) {
  return axios.gets(`/api/cms/trains/package-exams`, data)
}
// 培训包所有考试
export function trainsPackExamAll(id) {
  return axios.gets(`/api/cms/trains/package-exams/all`, { TrainPackageId: id })
}

export function trainsPackExamAdd(data) {
  return axios.posts('/api/cms/trains/package-exams', data)
}

export function trainsPackExamEdit(id, data) {
  return axios.puts(`/api/cms/trains/package-exams/${id}`, data)
}

export function trainsPackExamDelete(id) {
  return axios.deletes(`/api/cms/trains/package-exams/${id}`)
}

// --------------------------------------------------------------
// 用户

export function trainsUserList(data) {
  return axios.gets('/api/cms/trains/users', data)
}

export function trainsUserRecordList(data) {
  return axios.gets('/api/cms/trains/user-records', data)
}

export function trainsUserExamList(data) {
  return axios.gets('/api/exams/trains/user-exams', data)
}

// 获取所有学生成绩列表
export function studentExamResultList(data) {
  return axios.gets('/api/exams/examusers/results', data)
}

// 学生答题详情 data (id,userId)
export function studentExamDetail(data) {
  return axios.gets('/api/exams/examusers/record', data)
}

// 获取试卷题目详情
export function examQuestionsDetail(id) {
  return axios.gets(`/api/exams/examinations/${id}/questions`)
}

// 获取培训下的所有课程
export function trainAllCourse(id) {
  return axios.gets(`/api/cms/trains/courses`, { TrainId: id })
}

// 获取培训课程全部学生的学习情况
export function trainCourseAllUser(data) {
  return axios.gets(`/api/cms/trains/user-records/get-by-course`, data)
}

// 获取主观题列表
export function subjectiveQuestionList(id) {
  return axios.gets(`/api/exams/examinations/${id}/sub-questions`)
}
// 提交学生主观题分数
export function subjectiveQuestionScore(data) {
  return axios.posts(`/api/exams/exam-user-scores`, data)
}
// 获取学生主观答题情况
export function subjectiveQuestionAnswer(data) {
  return axios.gets(`/api/exams/exam-user-scores/get-question-records`, data)
}

// 新培训统计
// 学员数据
export function trainsUserRecord(data) {
  return axios.gets(`/api/cms/trains/users/records`, data)
}
// 签到记录
export function trainsSignRecord(data) {
  return axios.gets(`/api/cms/trains/signs`, data)
}
// 必修课 选修课记录
export function trainsCourseRecord(data) {
  return axios.gets(`/api/cms/trains/user-records`, data)
}
// 线下课程记录
export function trainsOffLineCourseRecord(data) {
  return axios.gets(`/api/cms/offline-courses/records/users`, data)
}

export function trainsUserCourseRecord(data) {
  return axios.gets(`/api/cms/trains/user-records/course`, data)
}
// 获取用户直播课时
export function trainsUserLiveClassHour(data) {
  return axios.posts(`/api/lms/lives/users/train-users-records`, data)
}
// /api/lms/lives/users/train-users-records
// 获取单个用户直播培训记录
export function trainsUserLiveList(data) {
  return axios.gets(`/api/lms/lives/users/train-lives-users-records`, data)
}

// 获取用户某个直播记录
export function trainsUserLiveRecord(data) {
  return axios.gets(`/api/lms/lives/users/get-by-user`, data)
}

export function trainsLiveRecord(data) {
  return axios.gets(`/api/lms/lives/users/train-lives-records`, data)
}

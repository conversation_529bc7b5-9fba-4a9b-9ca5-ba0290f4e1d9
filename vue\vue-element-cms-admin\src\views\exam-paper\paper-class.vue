<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边组织机构树形列表-->
      <el-col :xs="9" :sm="8" :md="8" :lg="6" :xl="4">
        <el-card class="box-card" style="max-height: 800px; overflow: auto">
          <el-button type="text" style="margin-left: 23px" @click="handleNodeClick()">
            全部
          </el-button>
          <el-tree :data="orgDatas" :props="defaultProps" highlight-current style="margin-top: 5px"
            @node-click="handleNodeClick" />
        </el-card>
      </el-col>
      <el-col :xs="15" :sm="16" :md="16" :lg="18" :xl="20">
        <el-card class="box-card">
          <div class="header_flex_box">
            <el-input v-model="listQuery.Filter" clearable size="small" placeholder="搜索..." class="small_input"
              @keyup.enter.native="handleFilter" />
            <el-button round size="mini" type="success" icon="el-icon-search" @click="handleFilter">搜索</el-button>
            <el-button v-permission="['Exam.ExamPapers']" round size="mini" type="primary"
              icon="el-icon-plus" @click="handleUpdate(0, 0)">新增</el-button>
            <export-excel :header="['排序', '名称']" :filter-val="['sort', 'name']" :apiFn="examPaperCategoryList" />
          </div>

          <el-table ref="multipleTable" v-loading="listLoading" row-key="id" :data="list" stripe style="width: 100%">
            <el-table-column label="排序" prop="sort" width="100px" />
            <el-table-column label="名称" prop="name">
              <template slot-scope="{ row }">
                <span class="link-type" @click="handleUpdate(1, row)">{{
                    row.name
                }}</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="200">
              <template slot-scope="{ row }">
                <el-button round type="primary" size="mini" icon="el-icon-edit" @click="handleUpdate(1, row)">编辑
                </el-button>
                <el-button round type="danger" size="mini" :disabled="row.name === 'admin'" icon="el-icon-delete"
                  @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="totalCount > 0" :total="totalCount" :page.sync="page"
            :limit.sync="listQuery.MaxResultCount" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogFormVisible" :title="isEdit ? '编辑' : '添加'"
      width="520px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" style="width: 380px" />
        </el-form-item>
        <el-form-item label="上级">
          <el-radio-group v-model="form.isTop" :disabled="isEdit" width="140px">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.isTop === false" label="上级" prop="parentId">
          <el-select v-model="form.parentId" style="width: 380px">
            <el-option v-for="item in nodesOrgs" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="1" :step="1" step-strictly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="dialogFormVisible = false">取消</el-button>
        <el-button round :loading="formLoading" type="primary" @click="save">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import { examPaperCategoryAll, examPaperCategoryList, examPaperCategoryAdd, examPaperCategoryEdit, examPaperCategoryDelete, examPaperCategoryDetail } from '@/api/examPaper'
export default {
  name: 'PaperClass',
  components: { Pagination },
  directives: { permission },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      orgDatas: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      form: {
        name: null,
        parentId: null,
        isTop: true,
        sort: 1
      },
      list: [],
      totalCount: 0,
      listLoading: true,
      formLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        ParentId: '',
        SkipCount: 0,
        MaxResultCount: 10
      },
      page: 1,
      dialogFormVisible: false,
      isEdit: false,
      nodesOrgs: []
    }
  },
  created() {
    this.getList()
    this.getTreeDatas()
  },
  methods: {
    examPaperCategoryList(args) {
      return examPaperCategoryList(args)
    },
    getTreeDatas() {
      examPaperCategoryAll().then((response) => {
        this.nodesOrgs = response.items.filter(item => {
          return item.parentId === null
        })
        this.orgDatas = response.items.reduce((total, item, index, list) => {
          if (item.parentId === null) {
            total.push({
              ...item,
              children: list.filter((f) => f.parentId === item.id)
            })
          } else {
            item.children = list.filter((f) => f.parentId === item.id)
          }
          return total
        }, [])
      })
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.page - 1) * this.listQuery.MaxResultCount
      examPaperCategoryList(this.listQuery).then((response) => {
        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    save() {
      if (this.form.isTop) {
        this.form.parentId = null
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            examPaperCategoryEdit(this.form.id, this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
              this.getTreeDatas()
            })
              .catch(() => {
                this.formLoading = false
              })
          } else {
            examPaperCategoryAdd(this.form).then((response) => {
              this.formLoading = false
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
              this.getTreeDatas()
            })
              .catch(() => {
                this.formLoading = false
              })
          }
        }
      })
    },
    handleDelete(row) {
      this.$confirm('是否删除 ' + row.name + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        examPaperCategoryDelete(row.id).then((response) => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getTreeDatas()
        })
      })
    },
    handleUpdate(t, row) {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.restForm()
      this.isEdit = t ? true : false
      console.log(row)
      if (this.isEdit) {
        this.form.id = row.id
        this.form.name = row.name
        this.form.isTop = true
        this.form.sort = row.sort
        if (row.parentId !== null) {
          this.form.isTop = false
          this.nodesOrgs.forEach(item => {
            if (item.id === row.parentId) {
              this.form.parentId = row.parentId
            }
          })
        }

        this.dialogFormVisible = true
      }
      this.dialogFormVisible = true
    },

    handleNodeClick(data) {
      if (data) {
        this.listQuery.ParentId = data.id
      } else {
        this.listQuery.ParentId = null
      }

      this.getList()
    },
    restForm() {
      this.form = {
        name: null,
        parentId: null,
        isTop: true,
        sort: 1
      }
    }
  }
}
</script>

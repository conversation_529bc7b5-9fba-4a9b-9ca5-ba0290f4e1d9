import axios from '@/axios'

export function getCategorys() {
  return axios.gets('/api/exams/questionBank/category/all')
}

export function getDetailCategory(id) {
  return axios.gets(`/api/exams/questionBank/category/${id}`)
}

export function createCategory(data) {
  return axios.posts('/api/exams/questionBank/category', data)
}

export function updateCategory(id, data) {
  return axios.puts(`/api/exams/questionBank/category/${id}`, data)
}

export function deleteCategory(id) {
  return axios.deletes(`/api/exams/questionBank/category/${id}`)
}

export function getChildCategoryList(parentId) {
  return axios.gets(`/api/exams/questionBank/category/list/${parentId}`)
}

export default
{
  getCategorys, getDetailCategory, createCategory, updateCategory, deleteCategory, getChildCategoryList
}

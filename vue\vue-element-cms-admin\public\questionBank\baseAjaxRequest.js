﻿function xhrPost(path, data, succCallback, errorCallback) {
    baseAjax(path, data, 'POST', succCallback, errorCallback,true,"application/json")
}
function xhrPostFormData(path, data, succCallback, errorCallback) {
    baseAjax(path, data, 'POST', succCallback, errorCallback,true,false, false)
}
function noParameterXhrPost(path, succCallback, errorCallback) {
    baseAjax(path, {}, 'POST', succCallback, errorCallback)
}
function xhrGet(path, data, succCallback, errorCallback,isasync) {
    baseAjax(path, data, 'GET', succCallback, errorCallback,isasync)
}
function noParameterXhrGet(path, succCallback, errorCallback) {
    baseAjax(path, {}, 'GET', succCallback, errorCallback)
}
function getCookie(objName) {//获取指定名称的cookie的值
    var arrStr = document.cookie.split("; ");
    for (var i = 0; i < arrStr.length; i++) {
        var temp = arrStr[i].split("=");
        if (temp[0] == objName) return unescape(temp[1]);  //解码
    }
    return "";
}
function baseAjax(requestPath, requestData, requestType, succCallback, errorCallback, isasync,contentType,processData) {
    /*requestPath：请求路径
     requestData：请求参数，默认为空
     requestType：请求方式("POST" 或 "GET")， 默认为 "GET"
     succCallback：请求成功回调函数
     errorCallback：请求失败回调函数
     dataType：预期服务器返回的数据类型， 默认为 JSON */
    requestData = requestData || {}
    requestType = requestType || 'GET'
    processData = processData !== undefined ? processData : true
    isasync=isasync!=undefined?isasync:true
    dataType =  'JSON'
    authToken='Bearer ' + getCookie('CMS-Token');
    requestPath='http://qc.ciep-pimp.com'+requestPath
    // requestPath='http://************:21001'+requestPath
    // requestPath='http://localhost:21001'+requestPath
    // requestPath='http://**************:5427'+requestPath
    $.ajax({
        url: requestPath,               //请求地址
        type: requestType,              //请求类型
        data: requestData,              //请求数据
        timeout: 60000, 
        contentType:contentType,
        async:isasync,
        processData: processData,
        //dataType: dataType,
        headers:{
            'Authorization': authToken
        },            
        success: function (res) {
            if (succCallback) {
                succCallback(res);
            }
        },
        complete: function (res, status) {           
        },
        error: function () {                  
        }
    })
}

function switchLetter(para) {
    if (para == 0) return "A ";
    if (para == 1) return "B ";
    if (para == 2) return "C ";
    if (para == 3) return "D ";
    if (para == 4) return "E ";
    if (para == 5) return "F ";
    if (para == 6) return "G ";
    if (para == 7) return "H ";
    if (para == 8) return "I ";
    if (para == 9) return "J ";
    if (para == 10) return "K ";
    if (para == 11) return "L ";
    if (para == 12) return "M ";
    if (para == 13) return "N ";
    if (para == 14) return "O ";
    if (para == 15) return "P ";
    if (para == 16) return "Q ";
    if (para == 17) return "R ";
    if (para == 18) return "S ";
    if (para == 19) return "T ";
    if (para == 20) return "U ";
    if (para == 21) return "V ";
    if (para == 22) return "W ";
    if (para == 23) return "X ";
    if (para == 24) return "Y ";
    if (para == 25) return "Z ";
}

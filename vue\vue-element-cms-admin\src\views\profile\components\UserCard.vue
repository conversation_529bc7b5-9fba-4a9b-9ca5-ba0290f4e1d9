<template>
  <el-card style="margin-bottom: 20px">
    <div slot="header" class="clearfix">
      <span>个人信息</span>
    </div>

    <div class="user-profile">
      <div class="box-center">
        <pan-thumb
          :image="avatar"
          :height="'100px'"
          :width="'100px'"
          :hoverable="false"
        />
      </div>
      <div class="box-center">
        <div class="user-name text-center">{{ name }}</div>
        <div class="user-info text-center" style="margin-top:20px">
          <el-form label-width="60px">
            <el-row>
              <el-col
                :span="24"
              ><div class="grid-content bg-purple-dark">
                <el-form-item class="userInfoFormItem" label="用户名">
                  <label class="infoLabel">{{ userName }}</label>
                </el-form-item>
              </div></el-col>
            </el-row>
            <el-row>
              <el-col
                :span="24"
              ><div class="grid-content bg-purple-dark">
                <el-form-item class="userInfoFormItem" label="姓名">
                  <label class="infoLabel">{{ name }}</label>
                </el-form-item>
              </div></el-col>
            </el-row>
            <!-- <el-row>
              <el-col :span="24"
                ><div class="grid-content bg-purple-dark">
                  <el-form-item class="userInfoFormItem" label="手机号">
                    <label class="infoLabel">{{ user.phoneNumber }}</label>
                  </el-form-item>
                </div></el-col
              >
            </el-row> -->
            <!-- <el-row>
              <el-col
                :span="24"
              ><div class="grid-content bg-purple-dark">
                <el-form-item class="userInfoFormItem" label="邮箱">
                  <label class="infoLabel">{{ user.email }}</label>
                </el-form-item>
              </div></el-col>
            </el-row> -->
            <el-row>
              <el-col
                :span="24"
              ><div class="grid-content bg-purple-dark">
                <el-form-item class="userInfoFormItem" label="性别">
                  <label class="infoLabel">{{ surname === '0' ? '男' : '女' }}</label>
                </el-form-item>
              </div></el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import PanThumb from '@/components/PanThumb'
import { mapGetters } from 'vuex'
// import { Name } from 'public/resource/pdfjs/build/pdf.worker'
export default {
  components: { PanThumb },
  computed: {
    ...mapGetters(['name', 'avatar', 'roles', 'userName', 'surname'])
  },
  mounted() {

  }

}
</script>

<style lang="scss" scoped>
.infoLabel{
  color: #606266;
  font: 400 11px system-ui;
  letter-spacing: normal;
  word-spacing: normal;
  text-indent: 0px;
}
.userInfoFormItem{
  margin-bottom: 0px;
}
.box-center {
  margin: 0 auto;
  display: table;
}

.text-muted {
  color: #777;
}

.user-profile {
  .user-name {
    font-weight: bold;
  }

  .box-center {
    padding-top: 10px;
  }

  .user-role {
    padding-top: 10px;
    font-weight: 400;
    font-size: 14px;
  }

  .box-social {
    padding-top: 30px;

    .el-table {
      border-top: 1px solid #dfe6ec;
    }
  }

  .user-follow {
    padding-top: 20px;
  }
}

.user-bio {
  margin-top: 20px;
  color: #606266;

  span {
    padding-left: 4px;
  }

  .user-bio-section {
    font-size: 14px;
    padding: 15px 0;

    .user-bio-section-header {
      border-bottom: 1px solid #dfe6ec;
      padding-bottom: 10px;
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}
</style>

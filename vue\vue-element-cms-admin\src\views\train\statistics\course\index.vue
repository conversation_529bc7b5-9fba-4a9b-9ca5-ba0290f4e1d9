<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 16:12:57
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-06 17:04:41
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/course/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <export-excel
      :header="['课程名称', '课时', '已获学时人数', '未获学时人数']"
      :filter-val="['courseName', 'classHour', 'getClassHourUserCount', 'unGetClassHourUserCount']"
      :paging="false"
      :api-fn="trainsUserCourseRecord"
    />
    <el-table v-loading="listLoading" :data="list" highlight-current-row>
      <el-table-column label="课程名称" prop="courseName">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewCourseUserDetail(row)">{{ row.courseName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时" prop="classHour" width="150" />
      <el-table-column label="已获学时人数" prop="getClassHourUserCount" width="180" />
      <el-table-column label="未获学时人数" prop="unGetClassHourUserCount" width="180" />
    </el-table>
    <!-- 培训课程  学生详情 -->
    <el-dialog v-if="trainCourseUserDialog" class="trainCourseUserDialog" title="培训课程学生详情" :visible.sync="trainCourseUserDialog" top="5vh" width="1200px">
      <t-course-detail :train-id="trainId" :course-id="courseId" />
    </el-dialog>
  </div>
</template>
<script>
import { trainsUserCourseRecord } from '@/api/train'
import TCourseDetail from './detail.vue'
export default {
  name: 'TrainCourse',
  components: {
    TCourseDetail
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,

      trainCourseUserDialog: false,
      courseId: ''
    }
  },
  created() {
    this.getTrainAllCourse()
  },
  methods: {
    trainsUserCourseRecord() {
      return trainsUserCourseRecord({ TrainId: this.trainId })
    },
    handleViewCourseUserDetail(row) {
      this.courseId = row.courseId
      this.trainCourseUserDialog = true
    },
    // 获取培训包包含的课程
    getTrainAllCourse() {
      trainsUserCourseRecord({ TrainId: this.trainId }).then(res => {
        this.list = res.items
        if (res.items.length) {
          var tmp = 0
          res.items.forEach(item => {
            tmp += item.classHour
          })
          this.$emit('courseResponse', tmp)
        }
      })
    }
  }
}
</script>


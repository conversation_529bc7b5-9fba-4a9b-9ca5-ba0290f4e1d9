<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>直播详情——{{ form.title }}</span>
      </div>
      <el-descriptions title="基础信息" border :column="2" label-class-name="descriptions_label">
        <el-descriptions-item label="直播名称">{{
          form.title
        }}</el-descriptions-item>
        <el-descriptions-item label="讲师">{{
          form.lecturer
        }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{
          form.startTime | formatDatetime
        }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{
          form.endTime | formatDatetime
        }}</el-descriptions-item>
        <el-descriptions-item label="直播时长">{{ form.timeLong }} 分钟</el-descriptions-item>
        <el-descriptions-item label="有效期">{{
          form.closeDate | formatDatetime
        }}</el-descriptions-item>
        <el-descriptions-item label="课时">{{
          form.classHour
        }}</el-descriptions-item>
        <el-descriptions-item label="合格时长">{{
          form.passDuration / 60
        }}</el-descriptions-item>
        <el-descriptions-item label="直播规模">{{ form.userCount }} 人</el-descriptions-item>
        <el-descriptions-item label="考评通过才能获取学时">{{
          form.passExamGetHour ? "是" : "否"
        }}</el-descriptions-item>
        <el-descriptions-item label="是否学习完成才能考核">{{
          form.canExamAfterLearn ? "是" : "否"
        }}</el-descriptions-item>
        <el-descriptions-item label="描述">{{
          form.description
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions style="margin-top: 20px" title="个人信息" border :column="2" label-class-name="descriptions_label">
        <el-descriptions-item label="用户名">{{
          userRecord.userName
        }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{
          userRecord.name
        }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{
          userRecord.className
        }}</el-descriptions-item>
        <el-descriptions-item label="获取学时">{{
          userRecord.classHour
        }}</el-descriptions-item>
        <el-descriptions-item label="通过考核">{{
          userRecord.examPass ? '是' : '否'
        }}</el-descriptions-item>
        <el-descriptions-item label="考核分数">{{
          userRecord.examUserScore
        }}</el-descriptions-item>
        <el-descriptions-item label="参与时长">{{
          userRecord.liveViewDuration | formatSecond
        }}</el-descriptions-item>

        <el-descriptions-item label="回放观看时长">{{
          userRecord.backPlayDuration | formatSecond
        }}</el-descriptions-item>

      </el-descriptions>
    </el-card>
  </div>
</template>
<script>
import { liveDetailInfo } from '@/api/live'
import { trainsUserLiveRecord } from '@/api/train'
export default {
  name: 'TrainLiveStatistics',
  data() {
    return {
      form: {
        title: '',
        description: '',
        startTime: '',
        endTime: '',
        timeLong: '',
        lecturer: '',
        userCount: '',
        classHour: 0,
        closeDate: '',
        passDuration: 0,
        canExamAfterLearn: false,
        passExamGetHour: false
      },
      userRecord: {
        userName: '',
        name: '',
        className: '',
        liveViewDuration: 0,
        backPlayDuration: 0,
        examPass: false,
        classHour: 0,
        examUserScore: 0
      }
    }
  },
  created() {
    this.getLiveDetailInfo()
    this.getLiveUser()
  },
  methods: {

    getLiveUser() {
      trainsUserLiveRecord({
        userId: this.$route.query.userId,
        liveId: this.$route.query.id
      }).then((res) => {
        this.userRecord.userName = res.userName
        this.userRecord.name = res.name
        this.userRecord.className = res.className
        this.userRecord.liveViewDuration = res.liveViewDuration
        this.userRecord.backPlayDuration = res.backPlayDuration
        this.userRecord.examPass = res.examPass
        this.userRecord.classHour = res.classHour
        this.userRecord.examUserScore = res.examUserScore
      })
    },
    getLiveDetailInfo() {
      liveDetailInfo(this.$route.query.id).then((res) => {
        this.form.title = res.title
        this.form.description = res.description
        this.form.startTime = res.startTime
        this.form.endTime = res.endTime
        this.form.timeLong = res.timeLong
        this.form.lecturer = res.lecturer
        this.form.userCount = res.userCount
        this.form.closeDate = res.closeDate
        this.form.classHour = res.classHour
        this.form.passDuration = res.passDuration
        this.form.canExamAfterLearn = res.canExamAfterLearn
        this.form.passExamGetHour = res.passExamGetHour
      })
    }
  }
}
</script>
<style scoped>
.el-descriptions ::v-deep .descriptions_label {
  width: 180px;
}
</style>

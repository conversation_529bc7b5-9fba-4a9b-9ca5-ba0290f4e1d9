<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 16:57:00
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-07 15:33:35
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/user/live.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <export-excel
      :header="['直播标题', '学时']"
      :filter-val="['liveTitle', 'classHour']"
      :api-fn="trainsUserLiveList"
      :paging="false"
    />
    <el-table v-loading="listLoading" :data="list">

      <el-table-column label="直播标题" prop="liveTitle" min-width="200">
        <template slot-scope="{ row }">
          <span class="link-type" @click="handleLiveDetail(row)">{{ row.liveTitle }}</span>
        </template>
      </el-table-column>

      <el-table-column label="学时" prop="classHour">
        <template slot-scope="{ row }">
          {{ row.classHour }}
        </template>
      </el-table-column>

    </el-table>
  </div>
</template>
<script>
import { trainsUserLiveList } from '@/api/train'
export default {
  name: 'TCourseLive',
  components: {

  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    },
    userId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false

    }
  },
  created() {
    this.getTrainLiveList()
  },
  methods: {
    trainsUserLiveList() {
      return trainsUserLiveList({ TrainId: this.trainId, UserId: this.userId })
    },
    handleLiveDetail(row) {
      const url = this.$router.resolve({
        name: 'TrainLiveStatistics',
        query: { id: row.liveId, userId: this.userId }
      })

      window.open(url.href, '_blank')
    },
    // 获取单个用户培训直播记录
    getTrainLiveList() {
      trainsUserLiveList({ TrainId: this.trainId, UserId: this.userId })
        .then((res) => {
          this.list = res.items
        })
        .catch(() => {})
    }
  }
}
</script>

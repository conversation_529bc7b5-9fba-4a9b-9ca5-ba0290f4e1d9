<template>
  <div>
    <el-upload
      :id="uploadId"
      :class="limit === 1 ? 'jgUpload' : 'imageUpload'"
      list-type="picture-card"
      action="#"
      :disabled="uploading"
      :v-loading="uploading"
      :multiple="multiple"
      :show-file-list="true"
      :http-request="handleUpload"
      :before-upload="handleBeforeUpload"
      :on-change="handleChange"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :limit="limit"
      :file-list="sourceList"
      :file-type="fileType"
    >
      <i slot="default" class="el-icon-plus" />
      <div slot="file" slot-scope="{ file }">
        <img
          class="el-upload-list__item-thumbnail"
          :src="handlePreviewUrl(file.url)"
          alt=""
          style="position: absolute; object-fit: contain"
        >
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
    </el-upload>
    <el-image-viewer
      v-if="showViewer"
      :z-index="3000"
      :on-close="closeViewer"
      :url-list="viewerImgList"
    />
    <el-dialog
      class="preview_dialog"
      append-to-body
      :visible.sync="dialogVisible"
      width="60%"
      top="5vh"
    >
      <preview-resource
        v-if="dialogVisible"
        ref="previewResource"
        :type="previewType"
        :url="previewUrl"
      />
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {
  uploadFile,
  getFileDownloadInfo,
  getFileBase64,
  getFileContent
} from '@/api/upload'
import PreviewResource from '@/components/PreviewResource/index'
export default {
  name: 'LzUploadImages',
  components: {
    ElImageViewer,
    PreviewResource
  },
  props: {
    uploadId: {
      type: String,
      require: false,
      default: null
    },
    fileSize: {
      type: Number,
      require: true,
      default: 100
    },
    limit: {
      type: Number,
      require: false,
      default: 20
    },
    fileType: {
      type: Array,
      require: true,
      default: function() {
        return ['jpg', 'jpeg', 'png']
      }
    },
    sourceList: {
      type: Array,
      require: true,
      default: function() {
        return []
      }
    },
    multiple: {
      type: Boolean,
      require: false,
      default: true
    },
    // 图片显示大小
    width: {
      type: Number,
      default: 148
    },
    height: {
      type: Number,
      default: 148
    }
  },
  data() {
    return {
      showViewer: false,
      uploading: false,
      previewType: '',
      previewUrl: '',
      dialogVisible: false,
      stsToken: {},
      viewerImgList: []
    }
  },
  watch: {
    sourceList(newVal, oldVal) {
      this.setCssStyle()
    }
  },
  mounted() {
    this.$on('removeSuccess', (index) => {
      // this.sourceList.splice(index, 1)
    })
    this.$on('removeFail', (index) => {})
    this.setCssStyle()
  },
  created() {},

  methods: {
    // 一张图片的时候设置图片宽度
    setCssStyle() {
      if (this.limit === 1 && this.sourceList.length === 1) {
        this.$nextTick(() => {
          if (this.uploadId) {
            const str = '#' + this.uploadId + ' .el-upload--picture-card'
            document.querySelectorAll(str)[0].style.display = 'none'
          } else {
            document.querySelectorAll(
              '.jgUpload .el-upload--picture-card'
            )[0].style.display = 'none'
          }
        })
      } else if (this.limit === 1 && this.sourceList.length === 0) {
        this.$nextTick(() => {
          if (this.uploadId) {
            const str = '#' + this.uploadId + ' .el-upload--picture-card'
            document.querySelectorAll(str)[0].style.display = 'inline-block'
          } else {
            document.querySelectorAll(
              '.jgUpload .el-upload--picture-card'
            )[0].style.display = 'inline-block'
          }
        })
      }
    },
    handlePreviewUrl(url) {
      let fileExtension = ''
      if (url.lastIndexOf('.') > -1) {
        fileExtension = url.slice(url.lastIndexOf('.') + 1)
      }
      if (fileExtension === 'mp4') {
        // url = require('@/assets/image/video.png')
      }
      if (fileExtension === 'pdf') {
        // url = require('@/assets/image/pdf.png')
      }
      return url
    },
    async handlePictureCardPreview(file) {
      let showDialog = false
      let fileExtension = ''
      if (file.name?.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      if (file.url?.lastIndexOf('.') > -1) {
        fileExtension = file.url.slice(file.url.lastIndexOf('.') + 1)
      }
      this.previewType = this.checkFileFormat(fileExtension)
      if (this.previewType === 'video') {
        this.previewUrl = file.url
        showDialog = true
      }
      if (this.previewType === 'pdf') {
        this.previewType = 'pdf'
        this.previewUrl = file.url
        showDialog = true
      }
      if (showDialog) {
        this.dialogVisible = showDialog
        return
      }
      this.dialogVisible = showDialog
      var index = this.sourceList.indexOf(file)
      this.showViewer = true
      const tempImgList = []
      this.sourceList.forEach((item) => {
        let fileExtension = ''
        const _url = item.url
        if (_url.lastIndexOf('.') > -1) {
          fileExtension = _url.slice(_url.lastIndexOf('.') + 1)
        }
        if (fileExtension !== 'mp4' && fileExtension !== 'pdf') {
          tempImgList.push(_url)
        }
      })
      const temp = []
      for (let i = 0; i < index; i++) {
        temp.push(tempImgList.shift())
      }
      this.viewerImgList = tempImgList.concat(temp)
    },
    closeViewer() {
      this.showViewer = false
    },
    handleRemove(item, index) {
      var index = this.sourceList.indexOf(item)
      this.$emit('remove-upload', index)
    },
    // 上传大小
    handleBeforeUpload(file) {
      let fileFormat = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        fileFormat = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
      } else {
        fileFormat = file.type.indexOf('image') > -1
      }
      if (!fileFormat) {
        this.$message.error(
          `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.long = 0
      this.uploading = true
      return true
    },
    handleChange(file, fileList) {
      fileList.splice(fileList.indexOf(file))
    },
    // 文件个数超出
    handleExceed(files, fileList) {
      this.$message.error(`最多上传${this.limit}个文件`)
    },
    // 上传失败
    handleUploadError(err) {
      this.uploading = false
      this.$message.error('上传失败, 请重试')
      this.$emit('error', err)
    },
    // 上传成功回调
    handleUploadSuccess() {
      this.uploading = false
      this.$message.success('上传成功')
    },
    // has 是否根据hash找到  url 地址  uploadForm 文件信息(has true有id )
    uploadSuccessFn(response) {
      this.uploading = false
      let fileExtension = ''
      if (response.fileInfo.fileName.lastIndexOf('.') > -1) {
        fileExtension = response.fileInfo.fileName.slice(
          response.fileInfo.fileName.lastIndexOf('.') + 1
        )
      }
      const uploadForm = {
        url: response.downloadInfo.downloadUrl,
        fileName: response.fileInfo.fileName.substring(
          0,
          response.fileInfo.fileName.lastIndexOf('.')
        ),
        name: response.fileInfo.fileName,
        hash: response.fileInfo.hash,
        // extend: '.' + fileExtension,
        size: response.fileInfo.byteSize,
        fileType: '.' + fileExtension,
        durationInSecond: 0
      }
      this.$emit('response-fn', response.downloadInfo.downloadUrl, uploadForm)
    },
    handleUpload(upFile) {
      const file = upFile.file
      const formData = new FormData()
      formData.append('FileContainerName', 'default')
      formData.append('FileType', 2)
      formData.append('GenerateUniqueFileName', false)
      formData.append('OwnerUserId', this.$store.getters.userId)
      formData.append('File', file)
      uploadFile(formData)
        .then((response) => {
          this.uploadSuccessFn(response)
        })
        .catch((err) => {
          this.handleUploadError(err)
        })
    },
    // 检查文件格式
    checkFileFormat(fileExtension) {
      this.previewType = 'undefined'
      var imageList = ['jpg', 'png', 'jpeg']
      for (let i = 0; i < imageList.length; i++) {
        const imageItem = imageList[i]
        if (fileExtension.indexOf(imageItem) > -1) {
          return 'image'
        }
      }
      var videoList = ['mp4']
      for (let i = 0; i < videoList.length; i++) {
        const videoItem = videoList[i]
        if (fileExtension.indexOf(videoItem) > -1) {
          return 'video'
        }
      }
      var _fileList = ['pdf']
      for (let i = 0; i < _fileList.length; i++) {
        const fileItem = _fileList[i]
        if (fileExtension.indexOf(fileItem) > -1) {
          return 'pdf'
        }
      }
    }
  }
}
</script>
<style scoped>
/* .jgUpload  .el-upload-list--picture-card ::v-deep .el-upload-list__item {
    transition: none !important;
  } */

.jgUpload ::v-deep .el-upload-list__item {
  width: 300px;
  height: 200px;
  transition: none !important;
}
</style>

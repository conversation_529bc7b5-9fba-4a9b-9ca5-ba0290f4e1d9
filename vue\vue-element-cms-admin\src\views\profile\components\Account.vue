<template>
  <el-form ref="userForm" :model="userForm" :rules="accountrules" label-position="top">
    <el-form-item label="姓名" prop="name">
      <el-input v-model.trim="userForm.name" />
    </el-form-item>
    <el-form-item label="性别" prop="surname">
      <el-radio-group v-model="userForm.surname">
        <el-radio label="0">男</el-radio>
        <el-radio label="1">女</el-radio>
      </el-radio-group>
    </el-form-item>
    <!--<el-form-item label="邮箱">
      <el-input v-model.trim="userForm.email" />
    </el-form-item> -->
    <el-form-item>
      <el-button type="primary" @click="submit">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {

  data() {
    return {
      userForm: {
        name: '',
        surname: ''
        // email: ''
      },
      accountrules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // this.userForm.name = this.copyArray(this.user.name) ;
    // this.userForm.email = this.copyArray(this.user.email);
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      this.$axios
        .gets('/api/appuser/my-profile')
        .then((Response) => {
          this.userForm.name = Response.name
          this.userForm.surname = Response.surname

          // this.userForm.email = Response.email
          this.userForm.userName = Response.userName
        })
        .catch(() => {})
    },
    submit() {
      this.$refs['userForm'].validate((valid) => {
        if (valid) {
          this.$axios
            .puts('/api/appuser/my-profile', this.userForm)
            .then((Response) => {
              this.$notify({
                title: '成功',
                message: '更新成功',
                type: 'success',
                duration: 2000
              })
              this.$store.dispatch('user/saveUserInfo', {
                name: this.userForm.name,
                email: this.$store.getters.email,
                avatar: this.$store.getters.avatar,
                // avatar: Response.userImg,
                phoneNumber: this.$store.getters.phoneNumber,
                userName: this.$store.getters.userName,
                surname: this.userForm.surname
              })
            })
            .catch(() => {})
        }
      })
    },
    copyArray(arr) {
      return JSON.parse(JSON.stringify(arr))
    }
  }
}
</script>

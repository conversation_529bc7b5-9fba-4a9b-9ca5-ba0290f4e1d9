<template>
  <div>
    <el-select v-model="listQuery.ParentId" size="small" filterable clearable placeholder="请选择班级..." @change="handleClassChange">
      <el-option v-for="item in nodeList" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <el-table ref="classTable" v-loading="listLoading" :data="list" @sort-change="sortChange" @selection-change="handleChooseClassChange">
      <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
      <el-table-column prop="name" label="班级名称" sortable="name" header-align="left" />      <!-- <el-table-column prop="name" label="试卷名称" sortable="name" header-align="left" /> -->

      <el-table-column
        prop="creationTime"
        sortable="creationTime"
        label="创建日期"
        width="135px"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.creationTime | formatDatetime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getList"
    />
  </div>
</template>
<script>
import { getExamPaperList } from '@/api/examPaper'
import { classesData, classesDetail } from '@/api/user'
import Pagination from '@/components/Pagination'
export default {
  name: 'ChooseExamPaper',
  components: {
    Pagination },
  data() {
    return {
      listLoading: false,
      list: [],
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        ParentId: null,
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1
      },
      nodeList: []
    }
  },
  mounted() {
    this.getList()
    this.getNodeList()
  },
  methods: {
    handleClassChange() {
      this.listQuery.page = 1
      this.getList()
    },
    handleChooseClassChange(val) {
      this.$emit('response', val)
    },
    checkSelectable(row) {
      return row.parentId !== null
    },
    // 列表排序
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      classesDetail(this.listQuery).then(res => {
        this.list = res.items
        this.listLoading = false
        this.listQuery.totalCount = res.totalCount
      }).catch(() => {
        this.listLoading = false
      })
    },
    getNodeList() {
      classesData().then(res => {
        this.nodeList = res.items.filter(item => item.parentId === null)
      })
    }
  }
}
</script>

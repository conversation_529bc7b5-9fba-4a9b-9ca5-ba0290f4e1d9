<template>
  <div class="app-container">

    <div class="table">
      <el-card shadow="always">
        <div slot="header" class="clearfix">
          <span>添加试卷</span>
        </div>
        <div>
          <el-form
            ref="generatingForm1"
            :inline="true"
            :model="generatingForm"
            label-width="80px"
            :rules="generatingRules"
          >
            <el-form-item label="试卷编号">
              <el-input v-model="generatingForm.code" :disabled="true" class="input_color" />
            </el-form-item>
            <el-form-item label="试卷名称" prop="name">
              <el-input v-model="generatingForm.name" />
            </el-form-item>
            <el-form-item label="总分">
              <el-input v-model="generatingForm.totalScore" :disabled="true" />
            </el-form-item>
            <el-form-item label="分类">
              <el-cascader v-model="generatingForm.examPaperCategoryId" filterable clearable :options="categoryList" :props="{'label': 'name','value': 'id','checkStrictly': true,'emitPath': false}" style="margin: 0 10px" placeholder="请选择试卷分类..." />
              <!-- <el-select v-model="generatingForm.categoryId">
                <el-option v-for="item in categoryList" :key="item.id" :value="item.id">{{ item.title }}</el-option>
              </el-select> -->
            </el-form-item>
            <el-form-item label="标签">
              <el-select v-model="generatingForm.tag" filterable clearable placeholder="请选择试卷标签...">
                <el-option v-for="item in tagsList" :key="item.id" :value="item.name">{{ item.name }}</el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-divider />
        <div>
          <el-button
            class="filter-item"
            style="margin-top: 7px"
            size="mini"
            round
            type="success"
            icon="el-icon-plus"
            @click="handleEdit"
          >组卷策略
          </el-button>
          <span class="span-remark">可通过添加多个组卷策略，自动从题库抽题组成试卷</span>
          <div style="float: right">
            <el-form
              ref="generatingForm"
              :inline="true"
              :model="generatingForm"
              label-width="80px"
              :rules="generatingRules"
            >
              <el-form-item label="判断每题" prop="judgeScore">
                <el-input-number
                  v-model.number="generatingForm.judgeScore"
                  size="small"
                  :controls="false"
                  :min="0"
                  :step="0.1"
                  :precision="1"
                  class="num_input"
                  @input="scoreInput"
                /><span> 分</span>
              </el-form-item>
              <el-form-item label="单选每题" prop="singleScore">
                <el-input-number
                  v-model.number="generatingForm.singleScore"
                  size="small"
                  :controls="false"
                  :min="0"
                  :step="0.1"
                  :precision="1"
                  class="num_input"
                  @input="scoreInput"
                /><span> 分</span>
              </el-form-item>
              <el-form-item label="多选每题" prop="multiScore">
                <el-input-number
                  v-model.number="generatingForm.multiScore"
                  size="small"
                  :controls="false"
                  :min="0"
                  :step="0.1"
                  :precision="1"
                  class="num_input"
                  @input="scoreInput"
                /><span> 分</span>
              </el-form-item>
              <el-form-item label="填空每题" prop="multiScore">
                <el-input-number
                  v-model.number="generatingForm.blankScore"
                  size="small"
                  :controls="false"
                  :min="0"
                  :step="0.1"
                  :precision="1"
                  class="num_input"
                  @input="scoreInput"
                /><span> 分</span>
              </el-form-item>
              <el-form-item label="问答每题" prop="multiScore">
                <el-input-number
                  v-model.number="generatingForm.replyScore"
                  size="small"
                  :controls="false"
                  :min="0"
                  :step="0.1"
                  :precision="1"
                  class="num_input"
                  @input="scoreInput"
                /><span> 分</span>
              </el-form-item>
            </el-form>

          </div>
        </div>

        <el-table :data="questionList" size="medium" style="width: 100%" highlight-current-row>
          <el-table-column label="序号" type="index" width="50px" align="center" />
          <!-- <el-table-column prop="bankPackageParentName" label="题库" header-align="left" />
          <el-table-column prop="bankPackageName" label="知识点" header-align="left" /> -->
          <el-table-column prop="bankCategoryName" label="题库分类" header-align="left" />
          <el-table-column prop="bankTagsName" label="标签" header-align="left" />
          <el-table-column prop="type" label="题库类型" header-align="left" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.type === 0" type="success" size="medium">单选题</el-tag>
              <el-tag v-if="scope.row.type === 1" size="medium">多选题</el-tag>
              <el-tag v-if="scope.row.type === 2" size="medium" type="warning">判断题</el-tag>
              <el-tag v-if="scope.row.type === 3" size="medium" type="dangre">填空题</el-tag>
              <el-tag v-if="scope.row.type === 6" size="medium" type="info">问答题</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="题目难度" header-align="left" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.difficulty === null" size="medium" type="info" effect="dark">全部</el-tag>
              <el-tag v-if="scope.row.difficulty === 1" size="medium" type="success">易</el-tag>
              <el-tag v-if="scope.row.difficulty === 2" size="medium" type="info">偏易</el-tag>
              <el-tag v-if="scope.row.difficulty === 3" size="medium">适中</el-tag>
              <el-tag v-if="scope.row.difficulty === 4" size="medium" type="warning">偏难</el-tag>
              <el-tag v-if="scope.row.difficulty === 5" size="medium" type="danger">难</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="questionSelNum" label="题目数量" width="120" />
          <el-table-column prop="score" label="每题分数" width="120">
            <template slot-scope="{row}">
              <span v-if="row.type === 0">{{ generatingForm.singleScore }}分</span>
              <span v-if="row.type === 1">{{ generatingForm.multiScore }}分</span>
              <span v-if="row.type === 2">{{ generatingForm.judgeScore }}分</span>
              <span v-if="row.type === 3">{{ generatingForm.blankScore }}分</span>
              <span v-if="row.type === 6">{{ generatingForm.replyScore }}分</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalScore" label="合计分数" width="120">
            <template slot-scope="{row}">
              <span v-if="row.type === 0">{{ generatingForm.singleScore * row.questionSelNum }}分</span>
              <span v-if="row.type === 1">{{ generatingForm.multiScore * row.questionSelNum }}分</span>
              <span v-if="row.type === 2">{{ generatingForm.judgeScore * row.questionSelNum }}分</span>
              <span v-if="row.type === 3">{{ generatingForm.blankScore * row.questionSelNum }}分</span>
              <span v-if="row.type === 6">{{ generatingForm.replyScore * row.questionSelNum }}分</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" header-align="left" width="120">
            <template slot-scope="scope">
              <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.$index)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="text-align: center;margin-top:15px">
          <el-button
            v-loading.fullscreen.lock="fullscreenLoading"
            round
            size="medium"
            icon="el-icon-check"
            type="primary"
            @click="handleCreateSure"
          >
            提 交
          </el-button>
        </div>
      </el-card>
    </div>
    <el-dialog
      class="generatingDialog"
      title="抽提策略"
      :visible.sync="generatingDialog"
      :close-on-click-modal="false"
      width="600px"
    >
      <div class="tag-type">

        <el-form ref="questionBankForm" :model="questionBankForm" label-width="100px" :rules="questionBankRules">
          <!-- <el-form-item label="来源">
            <el-radio-group v-model="questionBankType" @change="handleQuestionTypeChange">
              <el-radio :label="0">景格题库</el-radio>
              <el-radio :label="1">自建题库</el-radio>
            </el-radio-group>
          </el-form-item> -->

          <!-- <el-form-item label="题库" prop="bankPackageParent">
            <el-select
              v-model="questionBankForm.bankPackageParent"
              clearable
              class="bigwidth_select"
              placeholder="请选择题库..."
              @change="questionBankChange"
            >
              <el-option
                v-for="item in bankList"
                :key="item.id"
                :label="item.name"
                :value="item.id + ',' + item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="知识点" prop="bankPackage">
            <el-select
              v-model="questionBankForm.bankPackage"
              clearable
              class="bigwidth_select"
              :loading="knowledgeLoading"
              multiple
              placeholder="请选择知识点..."
              size="medium"
              @change="questionKnowledgeChange()"
            >
              <el-option
                v-for="item in knowledgeList"
                :key="item.id"
                :label="item.name"
                :value="item.id + ',' + item.name"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="题库分类" prop="bankCategoryIds">
            <el-cascader
              ref="tree1"
              v-model="questionBankForm.bankCategoryIds"
              filterable
              clearable
              class="bigwidth_select"
              :options="bankCategoryList"
              size="small"
              :props="{ value: 'id' }"
              @change="bankCategoryChange"
            />
          </el-form-item>
          <el-form-item v-for="tagType in tagTypeList" class="" :label="tagType.tagTypeName" prop="Tags">
            <el-checkbox-group v-model="tagType.tagIds" @change="tagCheckboxChange(tagType)">
              <el-checkbox v-for="tag in tagType.tags" :label="tag.id">{{ tag.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="难度" prop="difficulty">
            <el-select
              v-model="questionBankForm.difficulty"
              clearable
              placeholder="请选择题目难度..."
              @change="questionDifficultyChange"
            >
              <el-option label="全部" :value="null">全部</el-option>
              <el-option label="易" :value="1">易</el-option>
              <el-option label="偏易" :value="2">偏易</el-option>
              <el-option label="适中" :value="3">适中</el-option>
              <el-option label="偏难" :value="4">偏难</el-option>
              <el-option label="难" :value="5">难</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="题目类型" prop="type">
            <el-select v-model="questionBankForm.type" clearable placeholder="请选择题目类型..." @change="questionTypeChange">
              <el-option label="单选题" :value="0">单选题</el-option>
              <el-option label="多选题" :value="1">多选题</el-option>
              <el-option label="判断题" :value="2">判断题</el-option>
              <el-option label="填空题" :value="3">填空题</el-option>
              <el-option label="问答题" :value="6">问答题</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="题目数量" prop="questionSelNum">
            <el-input v-model.number="questionBankForm.questionSelNum" class="num_input" /><span> / </span>
            <el-input v-model.number="questionBankForm.questionTotalNum" :disabled="true" class="num_input" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" :loading="formLoading" @click="sureBtn">确 定</el-button>
        <el-button round @click="generatingDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  questionBankCategory,
  questionBankCount,
  tags,
  tagTypes
} from '@/api/questionBank'
import {
  bankPackageList,
  bankPackageItemsList,
  questionIdsList
} from '@/api/jgQuestionBank'
import { createExamPaper, examPaperCategoryAll, examPaperTagAll } from '@/api/examPaper'
import { getCategorys } from '@/api/questionBankCategory'

import {
  Message
} from 'element-ui'
export default {
  name: 'GeneratingPaper',
  data() {
    var validateSelNum = (rule, value, callback) => {
      if (value < this.questionBankForm.questionTotalNum + 1 && value > 0) {
        callback()
      } else {
        callback(new Error('请输入大于0且小于总题数的数字'))
      }
    }
    var validateDifficulty = (rule, value, callback) => {
      if (value === null || value > 0) {
        callback()
      } else {
        callback(new Error('请选择难易程度'))
      }
    }
    var validateScore = (rule, value, callback) => {
      if (value > 0) {
        callback()
      } else {
        callback(new Error('分数必须大于0'))
      }
    }
    var validateExamName = (rule, value, callback) => {
      if (value !== '' && value.length > 0) {
        if (value.length > 30) {
          callback(new Error('请输入1到30个字符'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入试卷名称'))
      }
    }
    return {
      // 知识点List
      knowledgeList: [],
      // 知识点加载loading      // 抽题时题库变化  知识点禁用
      knowledgeLoading: false,
      // 题库List
      bankList: [],
      // 组题展示List
      list: [],
      // 确定组卷后全屏加载锁定操作
      fullscreenLoading: false,
      // 所有的抽提策略
      questionList: [],
      // 表单提交加载中状态
      formLoading: false,
      // 组卷策略Dialog
      generatingDialog: false,
      // 试卷分类列表
      categoryList: [],
      // 试卷标签列表
      tagsList: [],
      // 组卷策略 form
      generatingForm: {
        code: '',
        name: '',
        totalScore: '',
        examPaperCategoryId: '',
        tag: '',
        questionNumber: 0,
        singleScore: 2,
        judgeScore: 2,
        multiScore: 2,
        blankScore: 2,
        replyScore: 2,
        singleQuestionIds: [],
        judgeQuestionIds: [],
        multiQuestionIds: [],
        blankQuestionIds: [],
        replyQuestionIds: [],
        examPaperType: 1
      },
      questionBankForm: {
        // bankPackageParent: '',
        // bankPackage: [],
        // bankPackageParentId: '',
        // bankPackageParentName: '',
        // bankPackageId: [],
        // bankPackageName: '',
        bankCategoryIds: [], // 题库分类
        bankCategoryName: '',
        bankTagsName: '',
        difficulty: null,
        type: 0,
        questionTotalNum: 0,
        questionSelNum: null,
        questionIds: []
      },
      // 组卷策略校验
      questionBankRules: {
        // bankPackageParent: [{
        //   required: true,
        //   message: '请选择题库',
        //   trigger: 'change'
        // }],
        // bankPackage: [{
        //   required: true,
        //   message: '请选择知识点',
        //   trigger: 'change'
        // }],
        bankCategoryIds: [{
          required: true,
          message: '请选择分类',
          trigger: 'change'
        }],
        difficulty: [{
          required: true,
          validator: validateDifficulty,
          // message: '请选择难易程度',
          trigger: 'change'
        }],
        type: [{
          required: true,
          message: '请选择题目类型',
          trigger: 'change'
        }],
        questionSelNum: [{
          required: true,
          validator: validateSelNum,
          trigger: 'blur'
        }]
      },
      generatingRules: {
        name: [{
          required: true,
          validator: validateExamName,
          trigger: 'blur'
        }],
        singleScore: [{
          required: true,
          validator: validateScore,
          trigger: 'blur'
        }],
        judgeScore: [{
          required: true,
          validator: validateScore,
          trigger: 'blur'
        }],
        multiScore: [{
          required: true,
          validator: validateScore,
          trigger: 'blur'
        }]
      },
      // 0 景格题库 1 自建题库
      questionBankType: 1,
      bankCategoryList: [],
      tagTypeList: []
    }
  },
  created() {
    // 获取试卷随机码
    this.getAssessmentCode()
    // 试卷标题随机
    this.getExamPaperTitle()
    // 获取试卷分类
    this.getCategoryList()
    this.getTagList()

    this.getBankCategoryList()
    // this.getBankPackageList()
  },
  methods: {
    handleQuestionTypeChange(val) {
      this.questionBankForm.bankPackageParent = ''
      this.questionBankForm.bankPackage = []
      this.getBankPackageList()
    },
    // 获取题库一级目录
    getBankPackageList() {
      if (this.questionBankType === 0) {
        bankPackageList().then(res => {
          this.bankList = res.items
        })
      } else {
        this.getQuestionBankCategory('', 0)
      }
    },
    getQuestionBankCategory(id, t) {
      questionBankCategory(id).then(res => {
        if (t === 0) {
          // 0 一级目录
          this.bankList = res.items
        } else {
          // 知识点目录
          this.knowledgeList = res.items
          this.knowledgeLoading = false
        }
      })
    },
    getBankCategoryList() {
      getCategorys().then(response => {
        var items = response.items
        if (items === undefined) {
          return
        }
        this.bankCategoryList = []
        items.forEach((item) => {
          if (item.parentId === null) {
            var element = {
              id: item.id,
              label: item.name,
              parentId: item.parentId,
              children: []
            }
            element.hasChildren = false
            this.bankCategoryList.push(element)
          }
        })
        this.setChildren(this.bankCategoryList, items)
      })
    },
    setChildren(roots, items) {
      roots.forEach((element) => {
        items.forEach((item) => {
          if (item.parentId === element.id) {
            if (!element.children) element.children = []
            element.children.push({
              id: item.id,
              label: item.name,
              parentId: item.parentId
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },

    // 搜索
    searchHandle() {
    },
    // 组卷  组卷策略Dialog展示
    handleEdit() {
      if (this.$refs.questionBankForm) {
        this.restForm()
        // this.$refs.questionBankForm.resetFields()
        this.$nextTick(() => {
          this.$refs.questionBankForm.clearValidate()
        })
      }

      // if (this.$refs['questionBankForm'] !== undefined) {
      //   this.$refs['questionBankForm'].resetFields()
      // }
      // this.$nextTick(() => {
      //   this.$refs.questionBankForm.resetFields()
      // })
      this.generatingDialog = true
    },
    // 删除
    handleDelete(index) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.questionList.splice(index, 1)
        // 重新计算总分
        this.countQuestionTotalScore()
        this.$notify({
          title: '成功',
          message: '删除成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {

      })
    },
    // 题库选择变化 知识点跟着变化
    questionBankChange(val) {
      this.knowledgeLoading = true
      this.questionBankForm.bankPackage = ''
      this.questionBankForm.bankPackageParentId = this.questionBankForm.bankPackageParent.split(',')[0]
      this.questionBankForm.bankPackageParentName = this.questionBankForm.bankPackageParent.split(',')[1]
      if (this.questionBankType === 0) {
        var form = {
          questionBankPackageId: this.questionBankForm.bankPackageParentId
        }
        bankPackageItemsList(form).then(res => {
          this.knowledgeList = res.items
          this.knowledgeLoading = false
        })
      } else {
        this.getQuestionBankCategory(this.questionBankForm.bankPackageParentId, 1)
      }
    },
    // 知识点跟着变化
    questionKnowledgeChange(val) {
      this.questionBankForm.bankPackageId = []
      this.questionBankForm.bankPackage.forEach((item, index) => {
        if (index === 0) {
          this.questionBankForm.bankPackageName = item.split(',')[1]
        } else {
          this.questionBankForm.bankPackageName += ',' + item.split(',')[1]
        }
        this.questionBankForm.bankPackageId.push(item.split(',')[0])
      })
      this.getQuestionIdsList()
    },
    // 题库分类选择
    bankCategoryChange(val) {
      var node = this.$refs.tree1.getCheckedNodes()
      if (node != undefined && node.length > 0) {
        this.questionBankForm.bankCategoryName = node[0].pathLabels.join('/')
      }

      this.getTagTypes()
      this.getQuestionIdsList()
    },
    getTagTypes() {
      this.tagTypeList = []
      this.questionBankForm.bankCategoryIds.forEach(async(v) => {
        tagTypes({
          MaxResultCount: 999,
          SkipCount: 0,
          Sorting: 'sort',
          QuestionBankCategoryId: v
        }).then(response => {
          response.items.forEach(async(item) => {
            var tags = await this.getTags(item.id)
            var _type = {
              tagTypeName: item.name,
              tagTypeId: item.id,
              tagIds: [],
              tags: tags
            }
            this.tagTypeList.push(_type)
          })
        })
      })
    },
    async getTags(typeId) {
      var _tags = await tags({
        MaxResultCount: 999,
        SkipCount: 0,
        Sorting: 'sort',
        QuestionBankTagTypeId: typeId
      })
      return _tags.items
    },
    tagCheckboxChange() {
      this.getQuestionIdsList()
    },
    // 题目难度变化
    questionDifficultyChange() {
      this.getQuestionIdsList()
    },
    // 题目类型变化
    questionTypeChange(val) {
      this.getQuestionIdsList()
    },
    // 组卷策略表单提交
    sureBtn() {
      this.$refs.questionBankForm.validate((valid) => {
        if (valid) {
          var repeat = false
          this.formLoading = true
          // 判断表单是否有重复
          if (this.questionList && this.questionList.length !== 0) {
            for (let index = 0; index < this.questionList.length; index++) {
              const item = this.questionList[index]
              if (item.bankCategoryIds.toString() === this.questionBankForm.bankCategoryIds.toString()) {
                if (item.difficulty === this.questionBankForm.difficulty || item.difficulty === null || this.questionBankForm.difficulty === null) {
                  if (item.type === this.questionBankForm.type) {
                    repeat = true
                    break
                  }
                }
              }
            }
          }
          if (repeat === false) {
            this.questionBankForm.questionIds = this.getRandomArrayElements(this.questionBankForm.questionIds, this.questionBankForm.questionSelNum)
            this.questionList.push(this.questionBankForm)
            // 选题增加 计算总分数
            this.countQuestionTotalScore()
            this.generatingDialog = false
          } else {
            Message.error('抽提策略有重复')
          }
          this.formLoading = false
        } else {
          return false
        }
      })
    },
    // 单题分数变化
    scoreInput() {
      this.countQuestionTotalScore()
    },
    // 计算总分数
    countQuestionTotalScore() {
      this.generatingForm.singleQuestionIds = []
      this.generatingForm.judgeQuestionIds = []
      this.generatingForm.multiQuestionIds = []
      this.generatingForm.blankQuestionIds = []
      this.generatingForm.replyQuestionIds = []
      this.questionList.forEach(item => {
        if (item.type === 0) {
          this.generatingForm.singleQuestionIds = this.generatingForm.singleQuestionIds.concat(item.questionIds)
        }
        if (item.type === 1) {
          this.generatingForm.multiQuestionIds = this.generatingForm.multiQuestionIds.concat(item.questionIds)
        }
        if (item.type === 2) {
          this.generatingForm.judgeQuestionIds = this.generatingForm.judgeQuestionIds.concat(item.questionIds)
        }
        if (item.type === 3) {
          this.generatingForm.blankQuestionIds = this.generatingForm.blankQuestionIds.concat(item.questionIds)
        }
        if (item.type === 6) {
          this.generatingForm.replyQuestionIds = this.generatingForm.replyQuestionIds.concat(item.questionIds)
        }
      })
      // 计算总分数
      this.generatingForm.totalScore = this.generatingForm.singleScore * this.generatingForm.singleQuestionIds.length + this.generatingForm.judgeScore * this.generatingForm.judgeQuestionIds.length + this.generatingForm.multiScore * this.generatingForm.multiQuestionIds.length + this.generatingForm.blankScore * this.generatingForm.blankQuestionIds.length + this.generatingForm.replyScore * this.generatingForm.replyQuestionIds.length
    },
    // 计算总题数
    countQuestionTotalNum() {
      this.generatingForm.questionNumber = this.generatingForm.judgeQuestionIds.length + this.generatingForm.singleQuestionIds.length + this.generatingForm.multiQuestionIds.length + this.generatingForm.blankQuestionIds.length + this.generatingForm.replyQuestionIds.length
    },
    // 组卷点击 检查有多少个重复题
    checkQuestionRepeat() {
      var repeatNum = 0
      repeatNum = this.checkArrayRepeatNum(this.generatingForm.judgeQuestionIds) + this.checkArrayRepeatNum(this.generatingForm.singleQuestionIds) + this.checkArrayRepeatNum(this.generatingForm.multiQuestionIds) + this.checkArrayRepeatNum(this.generatingForm.blankQuestionIds) + this.checkArrayRepeatNum(this.generatingForm.replyQuestionIds)
      return repeatNum
    },
    checkArrayRepeatNum(arr) {
      var flagArray = []
      var num = 0
      for (let i = 0; i < arr.length; i++) {
        if (flagArray[arr[i]]) {
          num++
        }
        flagArray[arr[i]] = true
      }
      return num
    },

    // 确定组卷
    handleCreateSure() {
      this.$refs['generatingForm1'].validate((valid) => {
        if (valid) {
          this.$refs.generatingForm.validate((valid) => {
            if (valid) {
              if (this.questionList && this.questionList.length > 0) {
                this.fullscreenLoading = true
                this.countQuestionTotalNum()
                // 题目重复
                if (this.checkQuestionRepeat()) {
                  this.$notify({
                    title: '提示',
                    message: '有' + this.checkQuestionRepeat() + '题重复,组卷后可编辑试卷替换重复题目',
                    type: 'warning',
                    duration: 0
                  })
                }
                // 调用创建试卷接口
                createExamPaper(this.generatingForm).then(res => {
                  this.fullscreenLoading = false
                  this.$notify({
                    title: '成功',
                    message: '组卷成功',
                    type: 'success',
                    duration: 2000
                  })
                  this.$router.push({
                    name: 'ExamPaper'
                  })
                }).catch(() => {
                  this.fullscreenLoading = false
                })
              } else {
                Message.error('请至少抽取一道题')
              }
            } else {
              Message.error('请填写正确的数据')
              return false
            }
          })
        } else {
          return false
        }
      })
    },
    // 根据筛选条件获取题的ID
    getQuestionIdsList() {
      if (this.questionBankType === 0) {
        // var form = {
        //   QuestionBankPackageParentId: this.questionBankForm.bankPackageParentId,
        //   QuestionBankPackageIds: this.questionBankForm.bankPackageId.toString(),
        //   Difficulty: this.questionBankForm.difficulty,
        //   QuestionType: this.questionBankForm.type
        // }
        // questionIdsList(form).then(res => {
        //   this.questionBankForm.questionIds = res.questionIds
        //   this.questionBankForm.questionTotalNum = res.questionNumber
        // })
      } else {
        if (this.questionBankForm.bankCategoryIds.length <= 0) {
          return
        }
        var form = {
          QuestionBankCategoryIds: this.questionBankForm.bankCategoryIds[this.questionBankForm.bankCategoryIds.length - 1],
          Difficulty: this.questionBankForm.difficulty,
          QuestionType: this.questionBankForm.type,
          TagIds: ''
        }
        var selNames = []
        this.tagTypeList.forEach(item => {
          if (item.tagIds.length > 0) {
            var _t = ''
            if (form.TagIds.length > 0) {
              _t = form.TagIds + ','
            }
            form.TagIds = _t + item.tagIds.toString()
            var _names = item.tags.filter(x => item.tagIds.indexOf(x.id) > -1)
            _names.forEach(n => {
              selNames.push(n.name)
            })
          }
        })
        this.questionBankForm.bankTagsName = selNames.length > 0 ? selNames.toString() : '--'
        questionBankCount(form).then(res => {
          this.questionBankForm.questionIds = res.questionIds
          this.questionBankForm.questionTotalNum = res.questionNumber
        })
      }
    },
    // 数组中随机取固定个数
    getRandomArrayElements(arr, count) {
      var shuffled = arr.slice(0)
      var i = arr.length
      var min = i - count
      var temp; var index
      while (i-- > min) {
        index = Math.floor((i + 1) * Math.random())
        temp = shuffled[index]
        shuffled[index] = shuffled[i]
        shuffled[i] = temp
      }
      return shuffled.slice(min)
    },
    // 列表排序
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getTaskList()
        return
      }
      this.query_data.Sorting = prop + ' ' + order
      this.getTaskList()
    },
    // 根据时间生成随机码 年月日时分秒2位随机数
    getAssessmentCode() {
      const now = new Date()
      const year = now.getFullYear()
      let month = now.getMonth() + 1
      let day = now.getDate()
      let hour = now.getHours()
      let minutes = now.getMinutes()
      let seconds = now.getSeconds()
      String(month).length < 2 ? (month = String('0' + month)) : month
      String(day).length < 2 ? (day = String('0' + day)) : day
      String(hour).length < 2 ? (hour = String('0' + hour)) : hour
      String(minutes).length < 2 ? (minutes = String('0' + minutes)) : minutes
      String(seconds).length < 2 ? (seconds = String('0' + seconds)) : seconds
      const yyyyMMddHHmmss = `${year}${month}${day}${hour}${minutes}${seconds}`
      this.generatingForm.code = yyyyMMddHHmmss + Math.random().toString().substr(2, 2)
      // return yyyyMMddHHmmss + Math.random().toString().substr(2, 2)
    },
    // 获取试卷标题随机码 新建试卷_4位
    getExamPaperTitle() {
      this.generatingForm.name = '新建试卷_' + Math.random().toString().substr(2, 4)
    },
    // 比较两个数组中是否有相同的元素
    compareArray(attendUid, dataattendUid) {
      var result = []
      console.log(dataattendUid)
      var c = dataattendUid.toString()
      for (var i = 0; i < attendUid.length; i++) {
        if (c.indexOf(attendUid[i].toString()) > -1) {
          for (var j = 0; j < dataattendUid.length; j++) {
            if (attendUid[i] === dataattendUid[j]) {
              result.push(attendUid[i])
              break
            }
          }
        }
      }
      if (result && result.length > 0) {
        return true
      }
      return false
    },
    getTagList() {
      examPaperTagAll().then(res => {
        this.tagsList = res.items
      })
    },
    async getCategoryList() {
      const res = await examPaperCategoryAll()

      this.categoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    restForm() {
      this.questionBankForm = {
        // bankPackageParent: '',
        // bankPackage: [],
        // bankPackageParentId: '',
        // bankPackageParentName: '',
        // bankPackageId: [],
        // bankPackageName: '',
        bankCategoryIds: [], // 题库分类
        bankCategoryName: '',
        bankTagsName: '',
        difficulty: null,
        type: 0,
        questionTotalNum: 0,
        questionSelNum: null,
        questionIds: []
      }
      this.tagTypeList = []
    },
    restGenerForm() {
      this.generatingForm = {
        code: '',
        name: '',
        totalScore: '',
        singleScore: 2,
        judgeScore: 2,
        multiScore: 2,
        singleQuestionIds: [],
        judgeQuestionIds: [],
        multiQuestionIds: [],
        tag: '',
        examPaperCategoryId: ''
      }
    }
  }

}

</script>
<style scoped>
.input_color ::v-deep .el-input__inner {
  color: #337ab7;
}

.num_input {
  width: 70px;
}

.num_input ::v-deep .el-input {
  display: inline-block;
  width: 70px;
}

.num_input ::v-deep .el-input__inner {
  width: 70px;
}

.span-remark {
  display: inline-block;
  font-size: 13px;
  margin-left: 10px;
  color: #909399;
}

.bigwidth_select {
  width: 400px;
}

/* .knowledge ::v-deep .el-select__tags-text {
  display: inline-block;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.knowledge ::v-deep .el-select .el-tag__close.el-icon-close {
  top: -7px;
} */
</style>

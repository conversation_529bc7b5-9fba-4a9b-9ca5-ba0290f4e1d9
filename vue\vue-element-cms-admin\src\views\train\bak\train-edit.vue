<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>{{ trainTitle }}</span>
      </div>
      <el-form ref="form" v-loading="formLoading" :model="form" :rules="formRules" label-width="150px">
        <!-- <span class="title_span">基础信息</span> -->
        <el-descriptions title="基础信息" />
        <el-form-item prop="name" label="培训名称">
          <el-input v-model="form.name" :disabled="isEdit" />
        </el-form-item>
        <el-form-item prop="startDate" label="培训开始时间">
          <el-date-picker v-model="form.startDate" :disabled="isEdit" :picker-options="trainPickerStartOptions" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item prop="endDate" label="培训结束时间">
          <el-date-picker v-model="form.endDate" :disabled="isEdit" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item prop="notice" label="培训公告">
          <el-input v-model="form.notice" type="textarea" :rows="2" />
        </el-form-item>
        <!-- <span class="title_span">课程设置</span> -->
        <el-descriptions title="课程设置" />
        <el-form-item prop="trainPackageId" label="培训包">
          <el-button :disabled="isEdit" round size="small" type="primary" icon="el-icon-plus" @click="handleChooseTrainPackClick">培训包选择</el-button>
          <el-tag v-if="form.trainPackageName" style="margin-left: 20px">{{ form.trainPackageName }}</el-tag>

          <!-- <span style="margin-left: 20px">{{ form.trainPackageName }}</span> -->
        </el-form-item>
        <el-form-item prop="learnInOrder" label="顺序学习">
          <el-switch v-model="form.learnInOrder" :disabled="form.trainPackageId === '' || isEdit" active-color="#13ce66" inactive-color="#ff4949" @change="handleLearnInOrderChange" />
        </el-form-item>
        <!-- <el-form-item prop="trainExams" label="考试时间"> -->
        <!-- <el-form ref="dateForm" :model="form" :rules="formRules"> -->
        <el-descriptions title="考试设置" />
        <el-table :data="form.trainExams" max-height="500px" style="margin-bottom: 20px" highlight-current-row>
          <el-table-column label="考试名称" prop="examName" min-width="200" />
          <el-table-column label="考试时长" prop="examTimeLong" min-width="100">
            <template slot-scope="{row}">
              {{ row.examTimeLong }} 分钟
            </template>
          </el-table-column>
          <el-table-column label="考试时间" prop="time" min-width="500">
            <template slot-scope="scope">
              <el-form-item
                class="dateItem"
                :prop="'trainExams.' + scope.$index + '.time'"
                :rules="formRules.dateRules"
                :inline-message="true"
              >
                <el-date-picker
                  v-model="scope.row.time"
                  :disabled="isEdit"
                  size="small"
                  type="datetimerange"
                  range-separator="至"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
        <!-- </el-form> -->
        <!-- </el-form-item> -->
        <!-- <span class="title_span">学员设置</span> -->
        <el-descriptions title="学员设置" />
        <el-form-item prop="trainUsers" label="选择学员">
          <el-button :loading="userLoading" :disabled="userLoading" round size="small" type="primary" icon="el-icon-plus" @click="handleSelectStudent">选择学员</el-button>
          <el-button v-permission="['AppUserManagement.Classes']" round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">选择班级</el-button>
          <span style="margin-left: 20px">已选择{{ selectUsers.length }}人</span>
        </el-form-item>
        <el-form-item>
          <el-button round type="primary" icon="el-icon-check" @click="handleSaveTrain">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-dialog title="选择培训包" :close-on-click-modal="false" :visible.sync="trainPackDialog" width="800px">
      <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
      <el-button round size="small" type="success" clear icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      <el-table ref="trainPackTable" v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange" @selection-change="handleChooseTrainPackChange" @row-click="handleTrainPackRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="培训包名称" prop="name" sortable="name" min-width="200" />
        <el-table-column label="分类" prop="categoryName" sortable="categoryName" min-width="200" />
        <el-table-column label="课程数" prop="courseCount" sortable="courseCount" width="120" />
        <el-table-column label="总课时数" prop="classHour" sortable="classHour" width="120" />
        <!-- <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column> -->
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getTrainsCoursePackList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseTrainPackSure">确 定</el-button>
        <el-button round @click="trainPackDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择学生" :visible.sync="chooseUserDialog" :close-on-click-modal="false" width="1000px">
      <choose-user v-if="chooseUserDialog" :all-org="allOrg" :all-student="allStudent" :current-select-student="selectUsers" @user-change="chooseStudent" />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseUserSure">确 定</el-button>
        <el-button round @click="chooseUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="alertDialog" width="450px">
      <el-result icon="warning" sub-title="培训包中有过期课程，请删除过期课程后选择" style="padding: 0px">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="alertDialog = false">确 定</el-button>
        </template>
      </el-result>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { trainsPackList, trainsPackExamAll, trainsExamList, trainsAdd, trainsEdit, trainsDetail, trainsUser, trainsPackHasExpire } from '@/api/train'
import {
  loadNodes,
  getAllStudents,
  findName,
  classesUsers
} from '@/api/user'
import { parseTimeDate } from '@/utils/index'
import ChooseUser from '@/components/ChooseUser/choose.vue'
import ChooseClass from '@/components/ChooseClass'
import moment from 'moment'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'TrainEdit',
  directives: {
    permission
  },
  components: {
    ChooseUser,
    Pagination,
    ChooseClass
  },
  data() {
    var trainStartDateValid = (rule, value, callback) => {
      if (!this.form.startDate) {
        callback(new Error('请选择开始时间'))
      } else {
        callback()
      }
    }
    var trainEndDateValid = (rule, value, callback) => {
      if (!this.form.endDate) {
        callback(new Error('请选择结束时间'))
      } else if (moment(this.form.startDate).isAfter(this.form.endDate, 'minute')) {
        callback(new Error('结束时间应大于开始时间'))
      } else {
        callback()
      }
    }
    var trainPackValid = async(rule, value, callback) => {
      if (this.form.trainPackageId === '') {
        callback(new Error('请选择培训包'))
      } else {
        const hasExpire = await trainsPackHasExpire(this.form.trainPackageId)
        if (hasExpire) {
          this.alertDialog = true
          callback(new Error('培训包中有过期课程，请删除培训包中的过期课程后再选择该培训包'))
        } else {
          callback()
        }
      }
    }
    var trainExamsDateCheck = (rule, value, callback) => {
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请选择时间'))
      } else {
        if (moment(value[0]).isBetween(this.form.startDate, this.form.endDate, 'minute', []) && moment(value[1]).isBetween(this.form.startDate, this.form.endDate, 'minute', [])) {
          if (moment(value[0]).isBefore(value[1], 'minute')) {
            var index = Number(rule.field.replace(/\D/g, ''))
            var examTimeLong = this.form.trainExams[index].examTimeLong
            if (moment(value[1]).diff(moment(value[0]), 'm') > examTimeLong) {
              callback()
            } else {
              callback(new Error('间隔时长应大于或等于考核时长'))
            }
          } else {
            callback(new Error('开始时间不能大于结束时间'))
          }
        } else {
          callback(new Error('请选择培训时间范围内的时间'))
        }
      }
    }
    var trainUserValid = (rule, value, callback) => {
      if (this.selectUsers.length === 0) {
        callback(new Error('请选择用户'))
      } else {
        callback()
      }
    }
    return {
      trainTitle: this.$route.query.name,
      isEdit: !!this.$route.query.id,
      formLoading: false,
      form: {
        name: '',
        startDate: '',
        endDate: '',
        notice: '',
        trainPackageId: '',
        trainPackageName: '',
        resourceDuration: 0,
        videoDuration: 0,
        learnInOrder: false,
        userCount: 0,
        trainExams: [],
        trainUsers: []
      },
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      trainPackDialog: false,
      selectTrainPack: {},
      // 班级数据
      allOrg: [],
      // 全部学生
      allStudent: [],
      userLoading: false,
      // 选择用户Dialog
      chooseUserDialog: false,
      selectUsers: [],
      trainPickerStartOptions: {
        disabledDate: (time) => {
          return new Date().getTime() - 8.64e7 > time.getTime()
        }
      },
      packPickerStartOptions: {
        disabledDate: (time) => {
          return new Date().getTime() - 8.64e7 > time.getTime()
        }
      },
      formRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        startDate: [{ required: true, validator: trainStartDateValid, trigger: 'blur' }],
        endDate: [{ required: true, validator: trainEndDateValid, trigger: 'blur' }],
        notice: [{ min: 0, max: 200, message: '请输入200字以内的字符', trigger: 'blur' }],
        trainPackageId: [{ required: true, validator: trainPackValid, trigger: 'blur' }],
        // trainExams: [{ required: true, validator: trainExamValid }],
        dateRules: [{ required: true, validator: trainExamsDateCheck, trigger: 'blur' }],
        trainUsers: [{ required: true, validator: trainUserValid, trigger: 'blur' }]
      },
      alertDialog: false,

      chooseClassDialog: false,
      classLoading: false,
      selectedClass: []
    }
  },
  created() {
    if (this.$route.query.id) {
      this.loadClassList()
      this.getTrainDetail(this.$route.query.id)
    } else {
      this.loadClassList()
    }
  },
  methods: {
    // 选择培训包
    handleChooseTrainPackClick() {
      this.trainPackDialog = true
      this.getTrainsCoursePackList()
    },
    // 培训包变化
    handleChooseTrainPackChange(val) {
      this.selectTrainPack = val[0]
      if (val.length > 1) {
        this.$refs.trainPackTable.clearSelection()
        this.$refs.trainPackTable.toggleRowSelection(val.pop())
      }
    },
    // 选择学生
    handleSelectStudent() {
      this.chooseUserDialog = true
    },
    // 选择学生组件传值获取选择的学生
    chooseStudent(val) {
      this.selectUsers = val
    },
    // 选择用户确定
    handleChooseUserSure() {
      this.dialogSureLoading = true
      var tmp = []
      this.selectUsers.forEach(item => {
        tmp.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
          className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
          indentityCode: item.indentityCode,
          name: item.name,
          userName: item.userName,
          studentIDNumber: item.studentIDNumber,
          studentNumber: item.studentNumber
        })
      })

      this.form.trainUsers = tmp
      this.dialogSureLoading = false
      this.chooseUserDialog = false
    },
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        this.selectUsers = this.selectUsers.concat(res.items)
      }
      var setA = new Set()
      this.selectUsers = this.selectUsers.filter(item => {
        const result = setA.has(item.id)
        setA.add(item.id)
        return !result
      })
      var formArr = []
      this.selectUsers.forEach((item, index) => {
        formArr.push({
          id: item.id,
          userId: item.id,
          classId: item.extraProperties ? item.extraProperties.OUId : (item.classId || null),
          className: item.extraProperties ? item.extraProperties.OUName : (item.className || null),
          indentityCode: item.indentityCode,
          name: item.name,
          userName: item.userName,
          studentIDNumber: item.studentIDNumber,
          studentNumber: item.studentNumbertrainsUser
        })
      })

      this.classLoading = false
      this.form.trainUsers = formArr
      this.chooseClassDialog = false
    },
    // 添加编辑保存
    handleSaveTrain() {
      this.$refs.form.validate(async(valid) => {
        if (valid) {
          this.formLoading = true
          this.form.trainExams.forEach(item => {
            item.startTime = item.time[0]
            item.endTime = item.time[1]
          })

          if (this.$route.query.id) {
            this.form.userCount = this.form.trainUsers.length
            trainsEdit(this.$route.query.id, this.form).then(res => {
              this.$message.success('修改成功')
              this.formLoading = false
            }).catch(() => {
              this.$message.error('修改失败')
              this.formLoading = false
            })
          } else {
            try {
              if (!this.$store.getters.tenantName.length) {
                const tenantName = await findName()
                this.$store.dispatch('user/saveTenantname', tenantName)
                this.form.tenantName = tenantName
              } else {
                this.form.tenantName = this.$store.getters.tenantName
              }

              this.form.userCount = this.form.trainUsers.length
              trainsAdd(this.form).then(res => {
                this.$message.success('添加成功')
                this.formLoading = false
                this.$router.go(-1)
              }).catch(() => {
                this.formLoading = false
                this.$message.error('添加失败')
              })
            } catch {
              this.formLoading = false
              this.$message.error('添加失败')
            }
          }
        } else {
          return false
        }
      })
    },
    // 培训包单行点击
    handleTrainPackRowClick(row) {
      this.$refs.trainPackTable.clearSelection()
      this.$refs.trainPackTable.toggleRowSelection(row)
      this.selectTrainPack = row
    },
    // 培训包选择确定
    handleChooseTrainPackSure() {
      if (this.selectTrainPack) {
        this.form.trainPackageId = this.selectTrainPack.id
        this.form.trainPackageName = this.selectTrainPack.name
        this.form.learnInOrder = this.selectTrainPack.learnInOrder
        this.form.resourceDuration = this.selectTrainPack.resourceDuration
        this.form.videoDuration = this.selectTrainPack.videoDuration
        this.form.classHour = this.selectTrainPack.classHour

        this.getTrainExamAllList(this.form.trainPackageId)
        this.trainPackDialog = false
      } else {
        this.$message.warning('请选择培训包')
      }
    },
    // 按顺序学习
    handleLearnInOrderChange(val) {
      if (!this.form.trainPackageId) {
        this.form.learnInOrder = !val
        this.$message.warning('请选择培训包')
      }
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getTrainsList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getTrainsList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getTrainsCoursePackList()
    },
    // 获取培训包列表
    getTrainsCoursePackList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      trainsPackList(this.listQuery).then(res => {
        this.list = res.items
        if (this.form.trainPackageId) {
          for (var i = 0; i < this.list.length; i++) {
            if (this.form.trainPackageId === this.list[i].id) {
              this.$nextTick(() => {
                this.$refs.trainPackTable.toggleRowSelection(this.list[i])
              })
              break
            }
          }
        }
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    // 添加时 获取培训包下所有课程
    getTrainExamAllList(id) {
      this.form.trainExams = []
      var newDataArr = []
      trainsPackExamAll(id).then(res => {
        res.items.forEach(item => {
          newDataArr.push({ ...item, time: ['', ''] })
        })
        this.form.trainExams = JSON.parse(JSON.stringify(newDataArr))
      })
    },
    // 获取培训详情
    getTrainDetail(id) {
      trainsDetail(id).then(res => {
        this.form.name = res.name
        this.form.startDate = parseTimeDate(res.startDate)
        this.form.endDate = parseTimeDate(res.endDate)
        this.form.learnInOrder = res.learnInOrder
        this.form.notice = res.notice
        this.form.resourceDuration = res.resourceDuration
        this.form.videoDuration = res.videoDuration
        this.form.trainPackageId = res.trainPackageId
        this.form.trainPackageName = res.trainPackageName
        if (res.extraProperties) {
          this.form.classHour = res.extraProperties.classHour
        }
        this.getTrainExamList()
      })
    },
    // 获取培训下所有考试信息
    getTrainExamList() {
      trainsExamList(this.$route.query.id).then(res => {
        this.form.trainExams = []
        res.items.forEach(item => {
          this.form.trainExams.push({ ...item, time: [parseTimeDate(item.startTime), parseTimeDate(item.endTime)] })
        })
      })
    },
    // 获取参加培训的学生
    getTrainUser() {
      this.userLoading = true
      var form = {
        Filter: '',
        SkipCount: 0,
        MaxResultCount: 999,
        Sorting: 'creationTime desc',
        TrainId: this.$route.query.id,
        isAll: true
      }
      this.selectUsers = []
      trainsUser(form).then(res => {
        res.items.forEach(item => {
          this.selectUsers.push({
            id: item.userId,
            userId: item.userId,
            classId: item.classId,
            className: item.className,
            indentityCode: item.indentityCode,
            name: item.name,
            userName: item.userName,
            studentIDNumber: item.studentIDNumber,
            studentNumber: item.studentNumber
          })
        })
      })
      this.userLoading = false
    },
    async loadClassList() {
      loadNodes().then(res => {
        this.allOrg = res.items
      })
      if (this.$route.query.id) {
        this.getTrainUser()
      }
    }
  }
}
</script>
<style scoped>
.dateItem {
  margin-bottom: 0px;
}
.dateItem ::v-deep .el-form-item__content {
  margin-left: 0px !important;
}
</style>

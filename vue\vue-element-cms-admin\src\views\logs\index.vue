<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <div class="header_flex_box">
          <el-select
            v-model="selectClient"
            clearable
            filterable
            placeholder="请选择应用端"
            size="small"
            @change="handleFilter"
          >
            <el-option
              v-for="item in clientList"
              :key="item.clientId"
              :label="item.clientName"
              :value="item.clientId"
            />
          </el-select>
          <el-input
            v-model="listQuery.UserName"
            placeholder="请输入用户名"
            style="width: 150px"
            size="small"
            class="filter-item"
            @keyup.enter.native="handleFilter"
          />

          <el-button
            round
            class="filter-item"
            size="mini"
            type="success"
            icon="el-icon-search"
            @click="handleFilter"
          >搜索</el-button>
          <export-excel :header="['空间名称', '用户名', '用户端', '操作内容', '操作时间', 'IP', '方法']"
              :filter-val="['tenantName', 'userName', 'clientName', 'url', 'executionTime', 'clientIpAddress', 'httpMethod']"
              :field="{ 4: [2]}" :api-fn="logList" />
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="list"
        style="width: 100%"
        @sort-change="sortChange"
      >
        <el-table-column
          prop="tenantName"
          sortable="custom"
          label="空间名称"
          width="150px"
        />
        <el-table-column
          prop="userName"
          sortable="custom"
          label="用户名"
          width="120px"
        />

        <el-table-column
          :show-overflow-tooltip="true"
          sortable="custom"
          prop="clientName"
          label="应用端"
          width="180px"
        />

        <el-table-column
          prop="url"
          sortable="custom"
          label="操作内容"
        />
        <el-table-column
          prop="executionTime"
          sortable="custom"
          label="操作时间"
          width="180px"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.executionTime | formatDateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="clientIpAddress"
          sortable="custom"
          label="IP"
          width="130px"
        />
        <el-table-column
          prop="httpMethod"
          sortable="custom"
          label="方法"
          align="center"
          width="80px"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.httpMethod == 'POST'" type="success">
              提交
              <!-- {{
                  scope.row.httpMethod
                }} -->
            </el-tag>
            <el-tag v-else-if="scope.row.httpMethod == 'GET'">
              查询
              <!-- {{
                  scope.row.httpMethod
                }} -->
            </el-tag>
            <el-tag v-else-if="scope.row.httpMethod == 'PUT'" type="warning">
              更新
              <!-- {{ scope.row.httpMethod }} -->
            </el-tag>
            <el-tag v-else-if="scope.row.httpMethod == 'DELETE'" type="danger">
              删除
              <!-- {{ scope.row.httpMethod }} -->
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column sortable label="操作" width="100px">
          <template slot-scope="scope">
            <el-button
              round
              size="mini"
              icon="el-icon-view"
              type="primary"
              @click="auditLogDetail(scope.$index, scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="totalCount > 0"
        :total="totalCount"
        :page.sync="page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog
      class="applyDialog"
      title="详细信息"
      :visible.sync="applyDialog"
      width="800px"
    >
      <el-tabs value="first" type="border-card">
        <el-tab-pane label="全部" name="first">
          <el-form label-position="left">
            <el-form-item label="Http状态">
              <span>
                <el-tag
                  v-if="logDetail.httpStatusCode < '300'"
                  type="success"
                >{{ logDetail.httpStatusCode }}</el-tag>
                <el-tag
                  v-else-if="logDetail.httpStatusCode < '500'"
                  type="warning"
                >{{ logDetail.httpStatusCode }}</el-tag>
                <el-tag
                  v-else-if="logDetail.httpStatusCode <= '600'"
                  type="danger"
                >{{ logDetail.httpStatusCode }}</el-tag>
              </span>
            </el-form-item>
            <el-form-item label="Http方法">
              <span>
                <el-tag v-if="logDetail.httpMethod == 'POST'" type="success">
                  {{ logDetail.httpMethod }}
                </el-tag>
                <el-tag v-else-if="logDetail.httpMethod == 'GET'">{{
                  logDetail.httpMethod
                }}</el-tag>
                <el-tag
                  v-else-if="logDetail.httpMethod == 'PUT'"
                  type="warning"
                >{{ logDetail.httpMethod }}</el-tag>
                <el-tag
                  v-else-if="logDetail.httpMethod == 'DELETE'"
                  type="danger"
                >{{ logDetail.httpMethod }}</el-tag>
              </span>
            </el-form-item>
            <el-form-item label="Url">
              <span>{{ logDetail.url }}</span>
            </el-form-item>
            <el-form-item label="应用名称">
              <span>{{ logDetail.applicationName }}</span>
            </el-form-item>
            <el-form-item label="用户名">
              <span>{{ logDetail.userName }}</span>
            </el-form-item>
            <el-form-item label="执行时间">
              <span>{{ logDetail.executionTime | formatDateTime }}</span>
            </el-form-item>
            <el-form-item label="执行耗时">
              <span>{{ logDetail.executionDuration }}</span>
            </el-form-item>
            <el-form-item label="浏览器">
              <span>{{ logDetail.browserInfo }}</span>
            </el-form-item>
            <el-form-item label="IP地址">
              <span>{{ logDetail.clientIpAddress }}</span>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="操作" name="second">
          <el-collapse
            v-for="(item, index) in logDetail.actions"
            :key="index"
            v-model="collapseActiveName"
            :data="logDetail.actions"
            accordion
          >
            <el-collapse-item :title="item.serviceName" :name="item.id">
              <el-form label-position="left">
                <el-form-item label="日志ID">
                  <span>{{ item.auditLogId }}</span>
                </el-form-item>
                <el-form-item label="执行时间">
                  <span>{{ item.executionTime | formatDateTime }}</span>
                </el-form-item>
                <el-form-item label="耗时">
                  <span>{{ item.executionDuration }}</span>
                </el-form-item>
                <el-form-item label="方法">
                  <span>{{ item.methodName }}</span>
                </el-form-item>
                <el-form-item label="参数">
                  <span>{{ item.parameters }}</span>
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane label="实体改变" name="third">
          <el-collapse
            v-for="(item, index) in logDetail.entityChanges"
            :key="index"
            v-model="entityActiveName"
            :data="logDetail.entityChanges"
            accordion
          >
            <el-collapse-item
              :title="item.entityTypeFullName + item.changeType"
              :name="item.id"
            >
              <template slot="title">
                <span>{{ item.entityTypeFullName }}</span>
                <span>——</span>
                <span>
                  <el-tag
                    v-if="item.changeType == 0"
                    type="success"
                  >Created</el-tag>
                  <el-tag
                    v-else-if="item.changeType == 1"
                    type="warning"
                  >Updated</el-tag>
                  <el-tag
                    v-else-if="item.changeType == 2"
                    type="danger"
                  >Deleted</el-tag>
                </span>
                <span>——</span>
                <span>{{ item.changeTime | formatDateTime }}</span>
              </template>
              <el-table ref="table" :data="item.propertyChanges" highlight-current-row>
                <el-table-column prop="propertyName" label="属性名称" />
                <el-table-column prop="originalValue" label="更改前" />
                <el-table-column prop="newValue" label="更改后" />
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="applyDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      class="applyDialog"
      title="详细信息"
      :visible.sync="entityChangeApplyDialog"
      width="800px"
    >
      <el-collapse>
        <el-collapse-item
          :value="entityChangeLogDetail.id"
          :name="entityChangeLogDetail.id"
        >
          <template slot="title">
            <span>{{ entityChangeLogDetail.entityTypeFullName }}</span>
            <span>——</span>
            <span>
              <el-tag
                v-if="entityChangeLogDetail.changeType == 0"
                type="success"
              >Created</el-tag>
              <el-tag
                v-else-if="entityChangeLogDetail.changeType == 1"
                type="warning"
              >Updated</el-tag>
              <el-tag
                v-else-if="entityChangeLogDetail.changeType == 2"
                type="danger"
              >Deleted</el-tag>
            </span>
            <span>——</span>
            <span>{{ entityChangeLogDetail.changeTime | formatDateTime }}</span>
          </template>
          <el-table ref="table" :data="entityChangeLogDetail.propertyChanges" highlight-current-row>
            <el-table-column prop="propertyName" label="属性名称" />
            <el-table-column prop="originalValue" label="更改前" />
            <el-table-column prop="newValue" label="更改后" />
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="entityChangeApplyDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { logList, logDetail, entityChanges, entityChangesDetail } from '@/api/log'
export default {
  name: 'Logs',
  components: {
    Pagination
  },
  data() {
    return {
      applyDialog: false,
      list: null,
      totalCount: 0,
      listLoading: true,
      listQuery: {
        Url: null,
        UserName: null,
        ApplicationName: null,
        CorrelationId: null,
        HttpMethod: null,
        HttpStatusCode: null,
        MaxExecutionDuration: null,
        MinExecutionDuration: null,
        HasException: null,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        ClientId: null
      },
      page: 1,
      activeName: 'log',
      entityList: null,
      entityTotalCount: 0,
      entityListLoading: true,
      entityListQuery: {
        EntityTypeFullName: null,
        EntityChangeType: null,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10
      },
      entityPage: 1,
      logDetail: {
        HttpStatusCode: null,
        HttpMethod: null,
        Url: null,
        ClientName: null,
        UserName: null,
        ExecutionDuration: null,
        ExecutionTime: null,
        BrowserInfo: null,
        CorrelationId: null,
        ClientIpAddress: null
      },
      collapseActiveName: '',
      entityActiveName: '',
      entityChangeApplyDialog: false,
      entityChangeLogDetail: {
        AuditLogId: null,
        ChangeTime: null,
        ChangeType: null,
        EntityId: null,
        EntityTypeFullName: null
      },
      clientList: [
        { clientId: 'course-public-web-client', clientName: '汽车学园学习网站' },
        { clientId: 'course-wechat', clientName: '汽车学园小程序' },
        { clientId: 'course-admin-client', clientName: '汽车学园管理后台' }
      ],
      selectClient: null
    }
  },
  created() {
    this.selectClient = this.clientList[0].clientId
    this.getList()
    // this.getEntityList();
  },
  methods: {
    logList(args) {
      return logList(args)
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.page - 1) * 10
      this.listQuery.ClientId = this.selectClient

      logList(this.listQuery).then((response) => {
        response.items.forEach((element) => {
          if (!element.lastModificationTime) {
            element.lastModificationTime = element.creationTime
            var data = element.url
            var data2 = data
            if (data != null) {
              var arr = data.split('/')

              if (arr[arr.length - 1] && arr[arr.length - 1].length === 36) {
                data2 = data.replace('/' + arr[arr.length - 1], '')
              }
              if (arr[arr.length - 2] && arr[arr.length - 2].length === 36) {
                data2 = data.replace('/' + arr[arr.length - 2], '')
              }
            }

            if (this.$store.getters.abpAuditLogging) {
              var key = 'AuditLogs:' + element.httpMethod + ':' + data2
              if (this.$store.getters.abpAuditLogging[key]) {
                element.url = this.$store.getters.abpAuditLogging[key]
              }
              if (this.$store.getters.abpAuditLogging[element.clientId]) {
                element.clientName = this.$store.getters.abpAuditLogging[
                  element.clientId
                ]
              }
            }
          }
        })

        this.list = response.items
        this.totalCount = response.totalCount
        this.listLoading = false
      })
        .catch(() => {
          this.listLoading = false
        })
    },
    getEntityList() {
      this.entityListLoading = true
      this.entityListQuery.SkipCount = (this.entityPage - 1) * 10
      entityChanges(this.entityListQuery).then((response) => {
        response.items.forEach((element) => {
          if (!element.lastModificationTime) {
            element.lastModificationTime = element.creationTime
          }
        })
        this.entityList = response.items
        this.entityTotalCount = response.totalCount
        this.entityListLoading = false
      })
        .catch(() => {
          this.entityListLoading = false
        })
    },
    handleFilter() {
      this.page = 1
      this.getList()
    },
    entityHandleFilter() {
      this.entityPage = 1
      this.getEntityList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.handleFilter()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.handleFilter()
    },
    entitySortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.entityHandleFilter()
        return
      }
      this.entityListQuery.Sorting = prop + ' ' + order
      this.entityHandleFilter()
    },
    auditLogDetail(e, item) {
      this.applyDialog = true
      logDetail(item.id).then((Response) => {
        this.logDetail = Response
        if (this.logDetail.actions.length > 0) {
          this.collapseActiveName = this.logDetail.actions[0].id
        }
        if (this.logDetail.entityChanges.length > 0) {
          this.entityActiveName = this.logDetail.entityChanges[0].id
        }
      })
        .catch(() => {})
    },
    entityChangeDetail(e, item) {
      this.entityChangeApplyDialog = true
      entityChangesDetail(item.id).then((Response) => {
        this.entityChangeLogDetail = Response
      })
        .catch(() => {})
    }
  }
}
</script>

<style>
.box_card ::v-deep .el-card__header {
  border-bottom: none;
  padding-bottom:0px ;
}
.table-expand {
  font-size: 0;
}

.table-expand label {
  width: 70px;
  color: #99a9bf;
}

.table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

.table-expand .el-form-item__content {
  font-size: 12px;
}
</style>

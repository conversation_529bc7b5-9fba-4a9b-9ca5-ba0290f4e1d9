<template>
  <div>
    <export-excel
      :header="['课程名称', '资源总时长', '学习时长', '学习进度', '学习状态', '考评状态', '学时状态', '课程学时', '获得学时', '开始学习时间', '最后学习时间']"
      :filter-val="['courseName', 'resourceDuration','courseLearnDuration', 'courseLearnProgress', 'isComplete', 'examPass', 'classHour','classHour', 'classHour', 'creationTime', 'lastLearnTime']"
      :field="{ 1: [3], 2: [3], 3: ['%'], 4: ['未完成', '已完成'], 5: ['未通过', '已通过'], 6: ['未获学时', '已获学时'], 9: [2], 10: [2] }"
      :api-fn="trainsCourseRecord"
      :paging="false"
    />

    <el-table v-loading="listLoading" :data="list" size="small" highlight-current-row>
      <el-table-column label="课程名称" prop="courseName" min-width="200" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span class="link-type" @click="handleRecordDetail(row)">{{ row.courseName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源总时长" prop="resourceDuration" width="140">
        <template slot-scope="{ row }">
          {{ row.resourceDuration | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column label="学习时长" prop="courseLearnDuration" width="140">
        <template slot-scope="{ row }">
          {{ row.courseLearnDuration | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column label="学习进度" prop="courseLearnProgress" width="120">
        <template slot-scope="{ row }">
          {{ row.courseLearnProgress.toFixed(2) }} %
        </template>
      </el-table-column>
      <el-table-column label="学习状态" prop="isComplete">
        <template slot-scope="{ row }">
          <el-tag v-if="row.isComplete" size="small" type="success">已完成</el-tag>
          <el-tag v-else size="small" type="info">未完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="考评状态" prop="examPass" width="120">
        <template slot-scope="{ row }">
          <el-tag v-if="row.examPass" size="small" type="success">已通过</el-tag>
          <el-tag v-else size="small" type="info">未通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="学时状态" prop="classHour" width="120">
        <template slot-scope="{ row }">
          <el-tag v-if="row.classHour > 0" size="small" type="success">已获学时</el-tag>
          <el-tag v-else size="small" type="info">未获学时</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="课程学时" width="120">
        <template slot-scope="{ row }">
          {{ row.classHour }}
        </template>
      </el-table-column>
      <el-table-column label="获得学时" prop="classHour" width="120">
        <template slot-scope="{ row }">
          {{ row.classHour }}
        </template>
      </el-table-column>
      <el-table-column label="开始学习时间" prop="creationTime" width="160">
        <template slot-scope="{ row }">
          {{ row.creationTime | formatDateTime }}
        </template>
      </el-table-column>
      <el-table-column label="最后学习时间" prop="lastLearnTime" width="160">
        <template slot-scope="{ row }">
          {{ row.lastLearnTime | formatDateTime }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 个人必修课  选修课 课程学习记录  点击必修课 选修课弹出 -->
    <el-dialog
      v-if="recordDetailDialog"
      :title="courseName + ' 课程学习详情'"
      append-to-body
      :visible.sync="recordDetailDialog"
      top="5vh"
      width="1200px"
    >
      <t-course-detail :user-id="userId" :course-id="courseId" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="recordDetailDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import TCourseDetail from './course-detail.vue'
import { trainsCourseRecord } from '@/api/train'
export default {
  name: 'TCourseRecord',
  components: {
    TCourseDetail
  },
  props: {
    listQuery: {
      reuqerd: true,
      type: Object,
      default: function() {
        return {
          TrainId: '',
          UserId: '',
          IsRequired: true
        }
      }
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,

      recordDetailDialog: false,
      userId: '',
      courseId: '',
      courseName: ''
    }
  },
  created() {
    this.getTrainCourseRecord()
  },
  methods: {
    trainsCourseRecord() {
      return trainsCourseRecord(this.listQuery)
    },
    handleRecordDetail(row) {
      this.userId = this.listQuery.UserId
      this.courseId = row.courseId
      this.courseName = row.courseName
      this.recordDetailDialog = true
    },
    // 必修课 选修课 课程记录
    getTrainCourseRecord() {
      this.listLoading = true
      trainsCourseRecord(this.listQuery).then(res => {
        this.list = res.items
        this.listLoading = false
      })
    }
  }
}
</script>


import axios from '@/axios'
// --------------------------------------------------------------------
// 资源列表
export function resourceList(data) {
  return axios.gets('/api/cms/knowledges/resources', data)
}

export function resourceAdd(data) {
  return axios.posts('/api/cms/knowledges/resources', data)
}

// 资源详情
export function resourceDetail(id) {
  return axios.gets(`/api/cms/knowledges/resources/${id}`)
}

// 修改资源
export function resourceEdit(id, data) {
  return axios.puts(`/api/cms/knowledges/resources/${id}`, data)
}
// 删除资源
export function resourceDelete(id) {
  return axios.deletes(`/api/cms/knowledges/resources/${id}`)
}

// 资源上架
export function resourcePublish(id, status) {
  return axios.puts(`/api/cms/knowledges/resources/${id}/publish?status=${status}`)
}
// -----------------------------------------------------------------------------------------
// 分类列表
export function resourceCategoryList(data) {
  return axios.gets('/api/cms/knowledges/category', data)
}

// 资源分类添加
/**
 *
 * @param {
 * "name": "string",
 * "sort": 0
 *} data
 * @returns
 */
export function resourceCategoryAdd(data) {
  return axios.posts('/api/cms/knowledges/category', data)
}
// 资源分类详情
export function resourceCategoryDetail(id) {
  return axios.gets(`/api/cms/knowledges/category/${id}`)
}
// 资源分类修改
export function resourceCategoryEdit(id, data) {
  return axios.puts(`/api/cms/knowledges/category/${id}`, data)
}
// 资源分类删除
export function resourceCategoryDelete(id) {
  return axios.deletes(`/api/cms/knowledges/category/${id}`)
}

// -----------------------------------------------------------------------------------------
// 资源中心

// // 已上架未添加到资源中心列表
// export function unResourceCenterList(data) {
//   return axios.gets('/api/cms/knowledges/center/unselect', data)
// }
// 资源中心列表
export function resourceCenterList(data) {
  return axios.gets('/api/cms/knowledges/center', data)
}

export function unresourceCenterList(data) {
  return axios.gets('/api/cms/knowledges/center/unselect', data)
}
// 资源中心添加
export function resourceCenterAdd(data) {
  return axios.posts('/api/cms/knowledges/center', data)
}
// 资源中心详情
export function resourceCenterDetail(id) {
  return axios.gets(`/api/cms/knowledges/center/${id}`)
}
// 资源中心修改
export function resourceCenterEdit(id, data) {
  return axios.puts(`/api/cms/knowledges/center/${id}`, data)
}
// 资源中心删除
export function resourceCenterDelete(id) {
  return axios.deletes(`/api/cms/knowledges/center/${id}`)
}
// 资源全部学员
export function resourceCenterAllUser(id) {
  return axios.gets(`/api/cms/knowledges/center/all-users?knowledgeCenterId=${id}`)
}
// 资源学员分页列表
export function resourceCenterUserList(data) {
  return axios.gets(`/api/cms/knowledges/center/users`, data)
}
// 资源中心删除学员
export function knowledgeCenterUserDelete(data) {
  return axios.posts(`api/cms/knowledges/center/delete-users`, data)
}
// 资源中心批量授权提交
export function resourcePermission(data) {
  return axios.posts(`api/cms/knowledges/center/update-users`, data)
}

// -------------------------------------------------------
// 资源评论
export function resourceCommentList(data) {
  return axios.gets(`/api/cms/knowledges/comment`, data)
}
export function addResourceComment(data) {
  return axios.posts(`/api/cms/knowledges/comment`, data)
}
export function editResourceComment(id, data) {
  return axios.puts(`/api/cms/knowledges/comment/${id}`, data)
}
export function deletesResourceComment(id) {
  return axios.deletes(`/api/cms/knowledges/comment/${id}`)
}
// -------------------------------------------------------
// 作者管理
export function resourceAuthorList(data) {
  return axios.gets(`/api/cms/knowledges/teachers`, data)
}
export function addResourceAuthor(data) {
  return axios.posts(`/api/cms/knowledges/teachers`, data)
}
export function editResourceAuthor(id, data) {
  return axios.puts(`/api/cms/knowledges/teachers/${id}`, data)
}
export function deletesResourceAuthor(id) {
  return axios.deletes(`/api/cms/knowledges/teachers/${id}`)
}
export function resourceAuthorAllList() {
  return axios.gets(`/api/cms/knowledges/teachers/all`)
}

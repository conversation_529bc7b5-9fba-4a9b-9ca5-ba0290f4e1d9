<template>
  <div class="app-container">

    <el-card v-loading="courseSelectLoading" class="box-card">
      <div slot="header">
        <span> 培训包课程管理—— {{ pageTitle }}</span>
      </div>
      <el-descriptions
        style="margin-bottom: 20px;"
        label-class-name="my-label"
        content-class-name="my-content"
        :column="4"
        border
      >
        <el-descriptions-item label="已选课程数">{{ selectCourseCenterList.length }} 个</el-descriptions-item>
        <el-descriptions-item label="总课时数">{{ totalClassHour }} 课时</el-descriptions-item>
        <el-descriptions-item label="总时长">{{ totalResourceDuration | formatSecond }}</el-descriptions-item>
        <el-descriptions-item label="视频总时长">{{ totalVideoDuration | formatSecond }}</el-descriptions-item>
      </el-descriptions>
      <div style="margin-bottom: 20px;">
        <el-button round type="success" size="small" icon="el-icon-plus" @click="handleChooseCourseClick"> 添加课程 </el-button>
        <el-button round size="small" type="success" icon="el-icon-plus" @click="offLineCourseDialog = true">添加线下课程</el-button>
        <span
          style="display: inline-block;font-size: 13px;margin-left: 10px;color: #909399;"
        >按住鼠标上下拖动资源可对资源进行排序</span>
      </div>

      <el-table ref="courseTable" row-key="courseId" :data="selectCourseCenterList" size="medium" height="600px" highlight-current-row>
        <el-table-column label="排序" width="60px">
          <template>
            <svg-icon icon-class="drag" class="meta-item__icon" />
          </template>
        </el-table-column>
        <el-table-column label="课程封面" prop="courseCoverUrl" sortable="courseCoverUrl" width="160">
          <template slot-scope="{ row }">
            <el-image v-if="row.trainCourseType === 0" :src="row.courseCoverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" sortable="courseName" min-width="200" />
        <el-table-column label="选修" prop="isRequired">
          <template slot-scope="{row}">
            <el-select v-model="row.isRequired">
              <el-option :value="true" label="必修" />
              <el-option :value="false" label="选修" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="学时" prop="classHour" sortable="classHour" width="100" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="课时数" prop="classHour" width="150" />
        <el-table-column label="操作" width="320">
          <template slot-scope="{row}">
            <el-button round type="primary" size="mini" icon="el-icon-top" @click="handleMoveCourseCenter(0,row)">置顶</el-button>
            <el-button round type="primary" size="mini" icon="el-icon-bottom" @click="handleMoveCourseCenter(1,row)">置底</el-button>
            <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleRemoveCourseCenter(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px;text-align: center;">
        <el-button round type="primary" size="small" icon="el-icon-check" @click="handleCenterSelectCourseSure">确定选择</el-button>
        <el-button round type="" size="small" icon="el-icon-back" @click="handleGoBack">取消</el-button>
      </div>
    </el-card>
    <el-dialog title="选择课程" :close-on-click-modal="false" :visible.sync="courseSelectDialog" width="1100px" top="5vh">
      <el-cascader v-model="listQuery.CourseCategoryId" filterable clearable :options="courseCategoryList" :props="{'label': 'name','value': 'id','children': 'children','checkStrictly': true,'emitPath': false}" size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
      <!-- <el-select v-model="listQuery.CourseCategoryId" size="small" placeholder="选择课程分类" @change="handleRefreshList">
        <el-option label="全部" :value="''" />
        <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select> -->
      <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
      <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      <el-table
        v-loading="listLoading"
        :data="list"
        @sort-change="sortChange"
        @selection-change="handleChooseCourseCenterChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="课程封面" prop="courseCoverUrl" sortable="courseCoverUrl" width="160">
          <template slot-scope="{ row }">
            <el-image :src="row.courseCoverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" sortable="courseName" min-width="200" />
        <el-table-column label="学时" prop="classHour" sortable="classHour" width="100" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="有效期" prop="endTime" sortable="dueTime" width="200">
          <template slot-scope="{row}">
            {{ row.endTime | formatDateTime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseCourseSure">确 定</el-button>
        <el-button round @click="courseSelectDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="offLineCourseDialog">
      <el-form ref="form" label-width="150px">
        <el-form-item prop="name" label="课程名称">
          <el-input />
        </el-form-item>
        <el-form-item prop="startDate" label="课程简介">
          <el-input type="textarea" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="recordDetailDialog = false">关  闭</el-button>
        <el-button round @click="handleOffLineCourseSure">确  定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  courseCenterList,
  courseCategoryList
} from '@/api/course'
import { trainsAddCourse, trainsCourseList } from '@/api/train'
import Sortable from 'sortablejs'
import Pagination from '@/components/Pagination'
import { ITEM_STYLE_KEY_MAP } from 'echarts/lib/model/mixin/itemStyle'
export default {
  name: 'CourseSelect',
  components: {
    Pagination
  },
  data() {
    return {
      pageTitle: this.$route.query.name,
      courseSelectLoading: false,
      list: [],

      listLoading: false,
      listQuery: {
        Filter: '',
        CourseCategoryId: '',
        FreeModel: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      categoryList: [],

      courseSelectDialog: false,
      currentSelectCourseCenter: [],
      selectCourseCenterList: [],

      totalClassHour: 0,
      totalResourceDuration: 0,
      totalVideoDuration: 0,
      // 列表拖拽排序
      sortable: null,

      offLineCourseDialog: false
    }
  },
  created() {
    this.getTrainsCoursePackList()
    this.getCourseCategoryList()
    this.getCourseCenterList()
  },
  methods: {
    handleChooseCourseClick() {
      this.currentSelectCourseCenter = []
      // 刷新课程列表
      this.listQuery.CourseCategoryId = ''
      this.listQuery.page = 1
      this.getCourseCategoryList()
      this.getCourseCenterList()
      this.courseSelectDialog = true
    },

    handleResourceCategoryChange(val) {
      this.listQuery.CourseCategoryId = val
      this.listQuery.page = 1
      this.getList()
    },
    handleChooseCourseCenterChange(val) {
      this.currentSelectCourseCenter = val
    },
    handleChooseCourseSure() {
      if (this.currentSelectCourseCenter.length) {
        this.selectCourseCenterList.push(...this.currentSelectCourseCenter)
        this.totalClassHour = 0
        this.totalResourceDuration = 0
        this.totalVideoDuration = 0
        // 去重
        this.selectCourseCenterList = this.selectCourseCenterList.reduce((prev, cur, index, arr) => {
          const findIndex = prev.findIndex(i => i.id === cur.id)
          if (findIndex === -1) {
            cur.order = index + 1
            prev.push({ ...cur, isRequired: true, trainCourseType: 0 })
            this.totalClassHour += cur.classHour
            this.totalResourceDuration += cur.resourceDuration
            this.totalVideoDuration += cur.videoDuration
          }
          return prev
        }, [])
        this.$nextTick(() => {
          this.setSort()
        })
        this.courseSelectDialog = false
      } else {
        this.$message.warning('请选择课程')
      }
    },
    handleOffLineCourseSure() {
      this.selectCourseCenterList.push({ id: '1111-1111-1111-1111', courseName: '线下课程', isRequired: false, trainCourseType: 2 })
    },
    handleMoveCourseCenter(t, row) {
      if (t === 0) {
        this.selectCourseCenterList.unshift(this.selectCourseCenterList.splice(this.selectCourseCenterList.findIndex(item => item.courseId === row.courseId), 1)[0])
      } else {
        this.selectCourseCenterList.push(this.selectCourseCenterList.splice(this.selectCourseCenterList.findIndex(item => item.courseId === row.courseId), 1)[0])
      }
      this.selectCourseCenterList.forEach((item, index) => {
        item.order = index
      })
    },
    handleRemoveCourseCenter(row) {
      this.selectCourseCenterList.splice(this.selectCourseCenterList.findIndex(item => item.courseId === row.courseId), 1)
      this.totalClassHour -= row.classHour
      this.totalResourceDuration -= row.resourceDuration
      this.totalVideoDuration -= row.videoDuration
      this.selectCourseCenterList.forEach((item, index) => {
        item.order = index + 1
      })
    },
    handleCenterSelectCourseSure() {
      if (this.selectCourseCenterList.length) {
        this.courseSelectLoading = true
        var form = {
          trainId: this.$route.query.id,
          trainCourses: []
        }
        this.selectCourseCenterList.forEach((item, index) => {
          form.trainCourses.push({
            trainId: this.$route.query.id,
            order: index,
            courseId: item.id,
            courseName: item.courseName,
            expireDate: item.expireDate,
            trainCourseType: 0,
            isRequired: item.isRequired
          })
        })
        trainsAddCourse(form).then(res => {
          this.courseSelectLoading = false
          this.$message.success('保存成功')
          this.handleGoBack()
        }).catch(res => {
          this.courseSelectLoading = false
          this.$message.error('保存失败')
        })
      } else {
        this.$message.warning('课程数量为空,请选择课程')
      }
    },
    getList() {
      this.getCourseCenterList()
    },
    handleGoBack() {
      this.$router.push({
        name: 'TrainNewEdits',
        query: { id: this.$route.query.id, from: -1 }

      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getTrainsCoursePackList() {
      trainsCourseList({ TrainId: this.$route.query.id }).then(res => {
        res.items.forEach((item, index) => {
          this.selectCourseCenterList.push({
            trainPackageId: this.$route.query.id,
            courseId: item.courseId,
            courseName: item.courseName,
            courseSource: item.courseSource,
            classHour: item.classHour,
            resourceDuration: item.resourceDuration,
            videoDuration: item.videoDuration,
            expireDate: item.expireDate,
            leftDays: item.leftDays,
            courseExpireState: item.courseExpireState,
            ExtraProperties: item.extraProperties,
            order: item.order
          })
          this.totalClassHour += item.classHour
          this.totalResourceDuration += item.resourceDuration
          this.totalVideoDuration += item.videoDuration
        })
        this.$nextTick(() => {
          this.setSort()
        })
      })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    },
    getCourseCenterList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseCenterList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.$message.error('获取列表失败')
        this.listLoading = false
      })
    },

    setSort() {
      const el = this.$refs.courseTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function(dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const targetRow = this.selectCourseCenterList.splice(evt.oldIndex, 1)[0]
          this.selectCourseCenterList.splice(evt.newIndex, 0, targetRow)
          // for show the changes, you can delete in you code
          this.selectCourseCenterList.forEach((item, index) => {
            item.order = index + 1
          })
        }
      })
    }
  }
}
</script>
<style >
.items {
  list-style: none;
  flex: 1;
  margin: 0;
  padding: 0;
  cursor: pointer;
}
.item {
  float: left;
  padding: 0 8px;
  margin: 0 12px 8px 0;
  border-radius: 6px;
  line-height: 30px;
  color: black;
  cursor: pointer;
}

.filtter_span {
  width: 60px;
  margin-right: 6px;
  font-weight: 700;
  font-size: 16px;
    /* margin-right: 15px;
    width: 80px; */
  }

  /* .filtter_radio {
    margin-bottom: 10px;
  } */
  .my-content{
font-size: 20px;
text-align: center !important;
  }
  .my-label{
text-align: center !important;
  }
</style>

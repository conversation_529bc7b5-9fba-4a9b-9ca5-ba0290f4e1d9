<template>
  <div class="app-container">
    <el-card class="box-card el-card_header_border_0">
      <div slot="header" class="header_flex_box">
        <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
        <el-button v-permission="['CourseManagement.KnowledgeCategorys']" round size="small" type="success" icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        <el-button v-permission="['CourseManagement.KnowledgeCategorys.Update']" round size="small" type="primary" icon="el-icon-plus" @click="handleResourceCategoryEdit(0,0)">添加</el-button>
        <export-excel :header="['排序', '分类名称', '创建时间']"
              :filter-val="['sort', 'name', 'creationTime']"
              :field="{ 2: [2] }" :api-fn="resourceCategoryList" />
      </div>
      <el-table v-loading="listLoading" :data="list" size="medium" highlight-current-row @sort-change="sortChange">
        <el-table-column label="排序" prop="sort" sortable="sort" width="120" />
        <el-table-column label="分类名称" prop="name" sortable="name" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="{row}">
            <el-button v-permission="['CourseManagement.KnowledgeCategorys.Update']" round type="primary" icon="el-icon-edit" size="mini" @click="handleResourceCategoryEdit(1,row)">编辑
            </el-button>
            <el-button v-permission="['CourseManagement.KnowledgeCategorys.Delete']" round type="danger" icon="el-icon-delete" size="mini" @click="handleResourceCategoryDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getResourceCategoryList"
      />
    </el-card>
    <el-dialog :title="dialogTitle" :close-on-click-modal="false" :visible.sync="resourceCategoryDialog" width="500px">
      <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" :loading="dialogLoading" @click="resourceCategoryDialogSure">确 定</el-button>
        <el-button round @click="resourceCategoryDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  resourceCategoryList,
  resourceCategoryEdit,
  resourceCategoryAdd,
  resourceCategoryDelete
} from '@/api/resource'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'ResourceClassify',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        Sorting: 'sort',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 编辑
      editId: '',
      isEdit: false,
      // dialog
      dialogTitle: '添加',
      resourceCategoryDialog: false,
      dialogLoading: false,
      // form
      form: {
        name: '',
        sort: 1
      },
      formRules: {
        name: [{
          required: true,
          message: '请输入资源类别名称',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 200,
          message: '长度在 1 到 200 个字符',
          trigger: 'blur'
        }
        ]
      }

    }
  },
  created() {
    this.getResourceCategoryList()
  },
  methods: {
    resourceCategoryList(args) {
      return resourceCategoryList(args)
    },
    // 编辑添加点击
    handleResourceCategoryEdit(t, row) {
      this.form = {
        name: '',
        sort: 1
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      if (t === 0) {
        this.dialogTitle = '添加'
        this.isEdit = false
        this.form.sort = this.listQuery.totalCount + 1
      } else {
        this.dialogTitle = '编辑'
        this.isEdit = true
        this.editId = row.id
        this.form.name = row.name
        this.form.sort = row.sort
      }
      this.resourceCategoryDialog = true
    },
    // 删除
    handleResourceCategoryDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resourceCategoryDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getResourceCategoryList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 编辑添加确定
    resourceCategoryDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.isEdit) {
            resourceCategoryEdit(this.editId, this.form).then(res => {
              this.$message.success('编辑成功')
              this.dialogLoading = false
              this.resourceCategoryDialog = false
              this.getResourceCategoryList()
            }).catch(() => {
              this.$message.error('编辑失败')
            })
          } else {
            resourceCategoryAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.dialogLoading = false
              this.resourceCategoryDialog = false
              this.getResourceCategoryList()
            }).catch(() => {
              this.$message.error('添加失败')
            })
          }
        } else {
          return false
        }
      })
    },
    handleSearch() {
      this.getResourceCategoryList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getResourceCategoryList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getResourceCategoryList()
    },
    getResourceCategoryList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resourceCategoryList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      })
    }
  }
}

</script>

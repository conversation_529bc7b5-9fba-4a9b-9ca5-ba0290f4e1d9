<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-01 08:04:14
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-01 10:36:39
 * @FilePath: /vue-element-cms-admin/src/views/train/train-statistics.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-card class="box-card ">
      <div slot="header">
        <!-- <a><i class="el-icon-arrow-left"></i></a> -->
        <span>  {{ form.name }}</span>
      </div>

      <!-- <span class="title_span">基础信息</span> -->
      <el-descriptions title="基础信息" border :column="2" label-class-name="descriptions_label">
        <el-descriptions-item label="培训名称">{{ form.name }}</el-descriptions-item>
        <el-descriptions-item label="培训时间">{{ form.startDate | formatDatetime }} 至 {{ form.endDate | formatDatetime }}</el-descriptions-item>
        <el-descriptions-item label="培训课时">{{ form.classHour }} 课时（培训课程 + 培训直播）</el-descriptions-item>
        <el-descriptions-item label="公告">{{ form.notice }}</el-descriptions-item>
      </el-descriptions>
      <statistics :train-id="trainId" :train-name="form.name" @totalResponse="getTotalClassHour" />

    </el-card>

  </div>
</template>
<script>
import { trainsDetail } from '@/api/train'
import Statistics from './statistics'
export default {
  name: 'TrainStatistics',
  components: {
    Statistics
  },
  data() {
    return {
      trainId: this.$route.query.id,
      form: {
        notice: '',
        name: '',
        startDate: '',
        endDate: '',
        classHour: 0
      }

    }
  },
  created() {
    this.getTrainDetail(this.$route.query.id)
  },
  methods: {
    getTrainDetail(id) {
      trainsDetail(id).then(res => {
        this.form.name = res.name
        this.form.startDate = res.startDate
        this.form.endDate = res.endDate
        this.form.notice = res.notice
      })
    },
    getTotalClassHour(val) {
      this.form.classHour = val
    }
  }
}

</script>
<style lang="scss" scoped>
.el-descriptions ::v-deep .descriptions_label {
  width: 120px
}
  .blank-item .answer{

        // height: 42px;
        // line-height: 42px;
        // border: none;
        // border-bottom: 1px solid #ccc;
        // // width: 100px;
        // padding: 1px 10px;
        // outline: none;
        text-decoration: underline #ccc;
        text-underline-offset: 5px;
        margin: 0 5px;

}
</style>

<template>
  <div class="app-container">
    <el-card shadow="never">
      <div slot="header">
        <span>课程评分</span>
      </div>
      <div class="header_flex_box">
        <el-select v-model="listQuery.CourseId" size="small" clearable>
          <el-option v-for="item in courseList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <export-excel :header="['课程名称', '评分', '用户', '创建时间']"
          :filter-val="['courseName', 'score', 'author',  'creationTime']"
          :field="{ 3: [2] }" :api-fn="courseScoreList" />
      </div>
      <el-table v-loading="listLoading" :data="list" size="small" @sort-change="sortChange">
        <el-table-column label="课程名称" prop="courseName" sortable="courseName" />
        <!-- <el-table-column label="内容" prop="content" sortable="content" show-overflow-tooltip /> -->
        <el-table-column label="评分" prop="score" sortable="score" width="250">
          <template slot-scope="{row}">
            <el-rate v-model="row.score" disabled :allow-half="true" show-score text-color="#ff9900" :score-template="row.score + '分'" />
          </template>
        </el-table-column>
        <el-table-column label="用户" prop="author" sortable="author" width="150" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{ row }">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="110">
          <template slot-scope="{ row }">
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleCourseScoreDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page" :limit.sync="listQuery.MaxResultCount" @pagination="getCourseScoreList" />
    </el-card>
  </div>
</template>
<script>
import {
  courseList,
  addCourseScore,
  courseScoreList,
  deletesCourseScore
} from '@/api/course'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'Score',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        Filter: '',
        CourseId: null,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      courseList: []

    }
  },
  created() {
    // this.addCourseScore()
    this.getCourseScoreList()
    this.getCourseList()
  },
  methods: {
    courseScoreList(args) {
      return courseScoreList(args)
    },
    handleCourseScoreDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletesCourseScore(row.id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getCourseScoreList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },

    handleRefreshList() {
      this.listQuery.page = 1
      this.getCourseScoreList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getCourseScoreList()
    },
    getCourseScoreList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      courseScoreList(this.listQuery)
        .then((res) => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    addCourseScore() {
      var data = {
        courseId: '5c35d565-3e2a-92d5-5171-3a02b1f97228',
        courseName: '测试课程',
        content: '课程挺好测试',
        score: 10
      }
      addCourseScore(data).then((res) => {
        this.courseList = res.items
      })
        .catch(() => {
          this.$message.error('获取课程列表失败')
        })
    },
    getCourseList() {
      var data = {
        Filter: '',
        Status: '',
        CourseCategoryId: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      courseList(data)
        .then((res) => {
          this.courseList = res.items
        })
        .catch(() => {
          this.$message.error('获取课程列表失败')
        })
    }
  }
}
</script>

import axios from '@/axios'

// 获取日志列表
/**
 *
 * @param {
 * Url: null,
 * UserName: null,
 * ApplicationName: null,
 * CorrelationId: null,
 * HttpMethod: null,
 * HttpStatusCode: null,
 * MaxExecutionDuration: null,
 * MinExecutionDuration: null,
 * HasException: null,
 * Sorting: null,
 * SkipCount: 0,
 * MaxResultCount: 10,
 * ClientId: null
 * } data
 * @returns
 */
export function logList(data) {
  return axios.gets(`/api/auditLogging/logs`, data)
}

// 获取日志详情
export function logDetail(id) {
  return axios.gets(`/api/auditLogging/logs/${id}`)
}

// 实体改变

export function entityChanges(data) {
  return axios.gets(`/api/auditLogging/logs/entity-changes`, data)
}

export function entityChangesDetail(id) {
  return axios.gets(`/api/auditLogging/logs/entity-changes/${id}`)
}

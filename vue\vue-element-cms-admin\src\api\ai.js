
import axios from '@/axios'

// 获取智能应用列表
// 参数
// {
//    "Sorting": ‘’,
//    "SkipCount": 0,
//    "MaxResultCount": 10,
// }
// 返回值
// {
//   "items": [
//     {
//       "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
//       "creationTime": "2025-06-25T05:19:14.333Z",
//       "name": "string",
//       "url": "string",
//       "picUrl": "string",
//       "appId": "string",
//       "appKey": "string",
//       "appType": 0 // 应用类型 0：chat 1 agent 2 workflow
//     }
//   ],
//   "totalCount": 0
// }
export function getAgents(data) {
  return axios.gets('/api/cms/AiAppInfos', data)
}

// 添加智能应用
// 参数
// {
//   "name": "string",
//   "url": "string",
//   "picUrl": "string",
//   "appId": "string",
//   "appKey": "string",
//   "appType": 0 // 应用类型 0：chat 1 agent 2 workflow
// }
export function addAgent(data) {
  return axios.posts('/api/cms/AiAppInfos', data)
}

// 修改智能应用
export function updateAgent(id, data) {
  return axios.puts(`/api/cms/AiAppInfos/${id}`, data)
}

// 删除智能应用
export function deleteAgent(id) {
  return axios.deletes(`/api/cms/AiAppInfos/${id}`)
}


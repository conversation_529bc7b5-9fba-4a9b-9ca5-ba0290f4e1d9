<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-06-22 16:13:32
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-06 17:07:21
 * @FilePath: /vue-element-cms-admin/src/views/train/statistics/live/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <export-excel
      :header="['直播名称', '课时', '已获学时人数', '未获学时人数']"
      :filter-val="['liveTitle', 'classHour', 'getClassHourUserCount', 'unGetClassHourUserCount']"
      :paging="false"
      :api-fn="trainsLiveRecord"
    />
    <el-table :data="list" highlight-current-row>
      <el-table-column label="直播名称" prop="liveTitle">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewLiveUserDetail(row)">{{ row.liveTitle }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时" prop="classHour" width="150" />
      <el-table-column label="已获学时人数" prop="getClassHourUserCount" width="180" />
      <el-table-column label="未获学时人数" prop="unGetClassHourUserCount" width="180" />
    </el-table>
    <!-- 培训直播  学生详情 -->
    <el-dialog v-if="trainLiveUserDialog" class="trainLiveUserDialog" title="培训直播学生详情" :visible.sync="trainLiveUserDialog" top="5vh" width="1200px">
      <t-live-detail :live-id="liveId" :class-hour="classHour" />
    </el-dialog>
  </div>
</template>
<script>
import { trainsLiveRecord } from '@/api/train'
import TLiveDetail from './detail.vue'
export default {
  name: 'TrainCourse',
  components: {
    TLiveDetail
  },
  props: {
    trainId: {
      required: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,

      trainLiveUserDialog: false,
      liveId: '',
      classHour: 0
    }
  },
  created() {
    this.getTrainLiveList()
  },
  methods: {
    trainsLiveRecord() {
      return trainsLiveRecord({ TrainId: this.trainId })
    },
    handleViewLiveUserDetail(row) {
      // this.liveId = row.liveId
      // this.classHour = row.classHour
      // this.trainLiveUserDialog = true
      const url = this.$router.resolve({
        name: 'LiveDetail',
        query: {
          id: row.liveId,
          title: row.liveTitle
        }
      })
      window.open(url.href, '_blank')
    },
    // 获取培训直播
    getTrainLiveList() {
      trainsLiveRecord({ TrainId: this.trainId })
        .then((res) => {
          this.list = res.items
          if (res.items.length) {
            var tmp = 0
            res.items.forEach(item => {
              tmp += item.classHour
            })
            this.$emit('liveResponse', tmp)
          }
        })
        .catch(() => {})
    }
  }
}
</script>


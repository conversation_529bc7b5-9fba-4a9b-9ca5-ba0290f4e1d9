<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>{{ pageTitle }}</span>
      </div>
      <el-tabs v-model="trainSet">
        <el-tab-pane name="trainInfo" label="基础信息">
          <el-form ref="form" :model="form" :rules="formRules" label-width="150px">
            <!-- <span class="title_span">基础信息</span> -->
            <!-- <el-descriptions title="基础信息" /> -->
            <el-form-item prop="name" label="培训名称">
              <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="封面图" prop="imgUrl">
              <lz-upload-images
                ref="previewFile"
                :limit="1"
                :file-size="500"
                :file-type="['jpg', 'png', 'jpeg']"
                :source-list="previewTrainImg"
                @response-fn="handleTrainImageResponse"
                @remove-upload="handleRemoveTrainImage"
              />
            </el-form-item>
            <el-form-item prop="startDate" label="培训开始时间">
              <el-date-picker
                v-model="form.startDate"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                placeholder="选择日期时间"
              />
            </el-form-item>
            <el-form-item prop="endDate" label="培训结束时间">
              <el-date-picker
                v-model="form.endDate"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                placeholder="选择日期时间"
              />
            </el-form-item>
            <el-form-item label="学员数量" prop="userCount">
              <el-input-number v-model="form.userCount" :step="1" :min="0" step-strictly />
              人
            </el-form-item>
            <el-form-item prop="learnInOrder" label="顺序学习">
              <el-switch
                v-model="form.learnInOrder"
                style="margin: 0 15px"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item prop="isShowCourse" label="显示课程">
              <el-radio-group v-model="form.isShowCourse">
                <el-radio :label="false">否</el-radio>
                <el-radio :label="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="isShowLive" label="显示直播">
              <el-radio-group v-model="form.isShowLive">
                <el-radio :label="false">否</el-radio>
                <el-radio :label="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="isShowExam" label="显示考核">
              <el-radio-group v-model="form.isShowExam">
                <el-radio :label="false">否</el-radio>
                <el-radio :label="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="notice" label="培训公告">
              <tinymce v-if="showContent" id="tinymce" v-model="form.notice" :value="form.notice" :height="400" :width="700" />
              <!-- <el-input v-model="form.notice" type="textarea" :rows="2" /> -->
            </el-form-item>
            <el-form-item>
              <el-button round type="primary" icon="el-icon-check" @click="handleSaveTrainInfo">保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" name="trainContent" label="培训内容管理">
          <el-tabs v-model="trainContentSet">
            <el-tab-pane name="trainCourse" label="培训课程">
              <div class="flex_between_box">
                <div>
                  <el-button round size="small" type="success" icon="el-icon-plus" @click="handleAddCourseCenter">添加课程
                  </el-button>
                  <export-excel
                    :header="['排序', '课程封面', '课程名称', '选修', '课时数']"
                    :filter-val="['order', 'coverUrl', 'name', 'isRequired', 'classHour',]"
                    :field="{ 3: ['选修', '必修'] }"
                    :paging="false"
                    :api-fn="trainsCourseList"
                  />
                </div>
              </div>
              <el-table :data="list" max-height="500px" highlight-current-row>
                <el-table-column label="排序" prop="order" width="80" />
                <el-table-column label="课程封面" width="200">
                  <template slot-scope="{ row }">
                    <el-image :src="row.coverUrl" class="course-cover" fit="cover">
                      <div slot="error">
                        <div class="image-slot">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column label="课程名称" prop="name">
                  <template slot-scope="{ row }">
                    <span style="margin-right: 10px">{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="选修" prop="isRequired">
                  <template slot-scope="{ row }">
                    <span>{{ row.isRequired ? "必修" : "选修" }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="课时数" prop="classHour" width="120" />
                <el-table-column label="操作" width="240">
                  <template slot-scope="{ row }">
                    <el-button
                      round
                      size="mini"
                      type="primary"
                      icon="el-icon-setting"
                      @click="handleCourseEdit(0, row)"
                    >
                      编辑</el-button>
                    <el-button
                      size="mini"
                      round
                      type="danger"
                      icon="el-icon-delete"
                      @click="deleteTrainCourse(row.courseId, 0)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane name="trainLive" label="直播课程">
              <div class="flex_between_box">
                <div>
                  <el-button round size="small" type="success" icon="el-icon-plus" @click="handleTrainEditLive(0, 0)">添加直播</el-button>
                  <export-excel
                    :header="['封面', '标题', '直播规模', '时长', '课时', '开始时间', '结束时间', '状态']"
                    :filter-val="['coverImage', 'title', 'userCount', 'timeLong', 'classHour', 'startTime', 'endTime', 'liveStreamStatue']"
                    :field="{ 5: [2], 6: [2], 7: ['未开始', '进行中', '已结束'] }"
                    :paging="false"
                    :api-fn="trainsLiveList"
                  />
                </div>
              </div>
              <el-table :data="liveList">
                <el-table-column label="封面" width="150">
                  <template slot-scope="{ row }">
                    <el-image :src="row.coverImage" class="course-cover" fit="cover">
                      <div slot="error">
                        <div class="image-slot">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column label="标题" prop="title" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span class="link-type" @click="handleLiveDetail(row)">{{ row.title }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="直播规模" prop="userCount" width="120">
                  <template slot-scope="{ row }">
                    {{ row.userCount }} 人
                  </template>
                </el-table-column>
                <el-table-column label="时长" prop="author" width="100">
                  <template slot-scope="{ row }">
                    {{ row.timeLong }} 分钟
                  </template>
                </el-table-column>
                <el-table-column label="课时" prop="classHour" width="100">
                  <template slot-scope="{ row }">
                    {{ row.classHour }}
                  </template>
                </el-table-column>
                <el-table-column label="开始时间" prop="startTime" width="160">
                  <template slot-scope="{ row }">
                    {{ row.startTime | formatDateTime }}
                  </template>
                </el-table-column>
                <el-table-column label="结束时间" prop="endTime" width="160">
                  <template slot-scope="{ row }">
                    {{ row.endTime | formatDateTime }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="liveStreamStatue" width="80">
                  <template slot-scope="{ row }">
                    <el-tag v-if="row.liveStreamStatue === 0" type="primary" size="mini">未开始</el-tag>
                    <el-tag v-if="row.liveStreamStatue === 1" type="success" size="mini">进行中</el-tag>
                    <el-tag v-if="row.liveStreamStatue === 2" type="info" size="mini">已结束</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="400">
                  <template slot-scope="{ row }">
                    <el-button
                      v-if="!row.publish"
                      size="mini"
                      round
                      type="warning"
                      icon="el-icon-s-promotion"
                      @click="handleCourseLivePublish(row)"
                    >{{ row.publish ? '取消发布' : '发布' }}</el-button>
                    <el-button
                      size="mini"
                      round
                      type="primary"
                      icon="el-icon-edit"
                      @click="handleTrainEditLive(1, row)"
                    >编辑</el-button>
                    <el-button
                      v-if="row.liveStreamStatue === 2"
                      size="mini"
                      round
                      :type="row.allowPlayBack ? 'danger' : 'primary'"
                      icon="el-icon-video-play"
                      @click="handleCourseLivePlayBack(row)"
                    >{{ row.allowPlayBack ? '禁止回放' : '允许回放' }}</el-button>
                    <el-button
                      size="mini"
                      round
                      type="danger"
                      icon="el-icon-delete"
                      @click="handleCourseLiveDelete(row)"
                    >删除</el-button>
                    <el-button
                      v-if="row.liveStreamStatue === 2"
                      size="mini"
                      round
                      type="primary"
                      @click="handleSyncPlayBackTime(row)"
                    >同步回放时长</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane name="trainOffCourse" label="线下课程">
              <div class="flex_between_box">
                <div>
                  <el-button
                    round
                    size="small"
                    type="success"
                    icon="el-icon-plus"
                    @click="handleEditOffLineCourse(0, 0)"
                  >添加课程</el-button>
                  <export-excel
                    :header="['封面', '课程名称', '用户规模', '时长', '课时', '开始时间', '结束时间', '讲师', '类型']"
                    :filter-val="['coverUrl', 'name', 'userCount', 'timeLong', 'classHour', 'startTime', 'endTime', 'lecturer', 'creditHourType']"
                    :field="{ 5: [1], 6: [1], 8: ['面授课程', '网络课程'] }"
                    :paging="false"
                    :api-fn="trainsOffLineCourseList"
                  />
                </div>
              </div>
              <el-table
                :data="offLineCourseList"
                highlight-current-row
                @current-change="handleCurrentOffLineCourseChange"
              >
                <el-table-column label="封面" prop="coverUrl" width="160">
                  <template slot-scope="{ row }">
                    <el-image :src="row.coverUrl" class="course-cover" fit="cover">
                      <div slot="error">
                        <div class="image-slot">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column label="课程名称" prop="name" show-overflow-tooltip />
                <el-table-column label="用户规模" prop="userCount" width="120" />
                <el-table-column label="时长" prop="timeLong" width="80" />
                <el-table-column label="课时" prop="classHour" width="80" />
                <el-table-column label="开始时间" prop="startTime" width="160">
                  <template slot-scope="{ row }">
                    <span>{{ row.startTime | formatDatetime }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="结束时间" prop="endTime" width="160">
                  <template slot-scope="{ row }">
                    <span>{{ row.endTime | formatDatetime }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="讲师" prop="lecturer" width="120" />
                <el-table-column label="类型" prop="creditHourType" width="120">
                  <template slot-scope="{ row }">
                    <span v-if="row.creditHourType">网络课程</span>
                    <span v-else>面授课程</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="400">
                  <template slot-scope="{ row }">
                    <div class="header_flex_box" style="margin-top: 10px">
                      <el-button
                        size="mini"
                        round
                        type="primary"
                        icon="el-icon-edit"
                        @click="handleEditOffLineCourse(1, row)"
                      >编辑</el-button>
                      <el-upload
                        ref="fileUpload"
                        round
                        class="upload-demo"
                        action=""
                        :on-change="handleChange"
                        :on-remove="handleRemove"
                        :on-exceed="handleExceed"
                        :show-file-list="false"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                        :auto-upload="false"
                      >
                        <el-button
                          :loading="importClassHourLoading"
                          :disabled="importClassHourLoading"
                          round
                          size="small"
                          icon="el-icon-top"
                          style="margin-left: 10px;"
                          type="primary"
                        >导入学时</el-button>
                      </el-upload>
                      <el-button
                        size="mini"
                        round
                        type="primary"
                        icon="el-icon-edit"
                        style="margin-left: 10px;"
                        @click="handleUploadFile(0, row)"
                      >上传附件</el-button>
                      <el-button
                        size="mini"
                        round
                        type="danger"
                        icon="el-icon-delete"
                        @click="handleOffLineCourseDelete(row)"
                      >删除</el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" name="trainUser" label="学员设置">
          <div class="header_flex_box">
            <el-input
              v-model="selectUsersListQuery.Filter"
              size="small"
              class="small_input"
              clearable
              placeholder="输入名称搜索"
            />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshSelectUserList">搜索
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectTrainUser">选择学员
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">按班级选择学员
            </el-button>
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleTrainUserDelete">批量删除
            </el-button>
            <el-button
              :loading="exportUserLoading"
              round
              class="filter-item"
              size="mini"
              type="success"
              icon="el-icon-download"
              @click="exportHandle"
            >用户导出
            </el-button>
            <el-button round size="mini" type="primary" icon="" @click="handleTrainUserUpdate">用户信息同步
            </el-button>
          </div>
          <el-table
            v-loading="selectUsersListLoading"
            :data="selectUsersList"
            max-height="700px"
            size="small"
            @selection-change="handleDeleteUserChange"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column label="用户名" prop="userName" />
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="部门" prop="className" />
            <!-- <el-table-column label="类型" prop="trainUserType">
              <template slot-scope="{row}">
                {{ row.trainUserType ? '临时' : '正式' }}
              </template>
            </el-table-column> -->
          </el-table>
          <pagination
            v-show="selectUsersListQuery.totalCount > 0"
            :total="selectUsersListQuery.totalCount"
            :page.sync="selectUsersListQuery.page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :limit.sync="selectUsersListQuery.MaxResultCount"
            @pagination="getTrainUserList"
          />
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" name="trainExam" label="考试设置">
          <el-tabs>
            <el-tab-pane label="线上考核">
              <el-alert
                v-if="form.isPublish&&trainExamForm.examinationId==null&& isEditTrainExam"
                title="考核正在创建中,请稍后刷新查看结果，若长时间未成功，建议关闭考核后重新保存"
                type="warning"
              />
              <el-alert
                v-if="form.isPublish&&trainExamForm.examinationId!=null"
                title="考核创建成功"
                type="success"
              />
              <el-form ref="trainExamForm" :model="trainExamForm" :rules="trainExamFormRules" label-width="160px">
                <el-form-item label="考核设置">
                  <el-switch v-model="examSet" active-color="#13ce66" inactive-color="#ff4949" />
                </el-form-item>
                <div v-if="examSet">

                  <el-form-item label="选择试卷" prop="examPaperId">
                    <el-button
                      :disabled="isEditTrainExam"
                      round
                      size="small"
                      type="primary"
                      icon="el-icon-plus"
                      @click="handleChooseExamPaperClick"
                    >选择试卷</el-button>
                    <el-table ref="liveExamPaperTable" :data="trainExamPaperList" highlight-current-row>
                      <el-table-column label="试卷名称" prop="name" />
                      <el-table-column label="题数" prop="questionNumber" />
                      <el-table-column label="总分" prop="totalScore" />
                    </el-table>
                  </el-form-item>
                  <el-form-item label="考评名称" prop="examName">
                    <el-input v-model="trainExamForm.examName" :disabled="isEditTrainExam" />
                  </el-form-item>
                  <el-form-item prop="startTime" label="开始时间">
                    <el-date-picker
                      v-model="trainExamForm.startTime"
                      :disabled="isEditTrainExam"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                  <el-form-item prop="endTime" label="结束时间">
                    <el-date-picker
                      v-model="trainExamForm.endTime"
                      :disabled="isEditTrainExam"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm"
                      placeholder="选择日期时间"
                    />
                  </el-form-item>
                  <el-form-item label="考评时长" prop="examTimeLong">
                    <el-input-number
                      v-model="trainExamForm.examTimeLong"
                      :disabled="isEditTrainExam"
                      :min="0"
                      :step="1"
                      step-strictly
                    />
                  </el-form-item>
                  <el-form-item v-if="trainExamPaperList.length" label="合格分数" prop="passScore">
                    <el-input-number
                      v-model="trainExamForm.passScore"
                      :min="0"
                      :step="0.1"
                      :max="trainExamPaperList[0].totalScore"
                      step-strictly
                    />
                  </el-form-item>
                  <el-form-item label="最大提交次数" prop="allowSubmitTimes">
                    <el-input-number v-model="trainExamForm.allowSubmitTimes" :min="0" :step="1" step-strictly />
                  </el-form-item>
                </div>
                <el-form-item>
                  <el-button round type="primary" icon="el-icon-check" :loading="saveExamLoading" @click="handleSaveTrainExam">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="线下考核">
              <el-input
                v-model="offLineExamListQuery.Filter"
                clearable
                class="small_input"
                size="small"
                placeholder="输入名称搜索"
              />
              <el-button round size="small" type="success" clear icon="el-icon-search" @click="handleRefreshExamList">搜索
              </el-button>
              <el-button round size="small" type="success" icon="el-icon-plus" @click="handleEditOffExam(0, 0)">添加考核
              </el-button>
              <export-excel
                :header="['考核名称']"
                :filter-val="['name']"
                :query="{ 'TrainId': $route.query.id }"
                :api-fn="trainsOffLineExamList"
              />
              <el-table
                v-loading="offLineExamListLoading"
                :data="offLineExamList"
                highlight-current-row
                @current-change="handleCurrentOffLineExamChange"
              >
                <el-table-column label="考核名称" prop="name" min-width="200" show-overflow-tooltip />

                <el-table-column label="操作">
                  <template slot-scope="{ row }">
                    <div class="header_flex_box" style="margin-top: 10px">
                      <el-button
                        size="mini"
                        round
                        type="primary"
                        icon="el-icon-edit"
                        @click="handleEditOffExam(1, row)"
                      >
                        编辑</el-button>
                      <el-upload
                        ref="fileUpload"
                        round
                        class="upload-demo"
                        action=""
                        :on-change="handleAchiveChange"
                        :on-remove="handleRemove"
                        :on-exceed="handleExceed"
                        :show-file-list="false"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                        :auto-upload="false"
                      >
                        <el-button
                          :loading="importAchiveLoading"
                          :disabled="importAchiveLoading"
                          round
                          size="small"
                          icon="el-icon-top"
                          style="margin-left: 10px;"
                          type="primary"
                        >导入成绩</el-button>
                      </el-upload>
                      <el-button
                        size="mini"
                        round
                        type="primary"
                        icon="el-icon-edit"
                        style="margin-left: 10px;"
                        @click="handleUploadFile(1, row)"
                      >上传附件</el-button>
                      <el-button
                        size="mini"
                        round
                        type="danger"
                        icon="el-icon-delete"
                        @click="handleOffLineExamDelete(row)"
                      >删除</el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="offLineExamListQuery.totalCount > 0"
                :total="offLineExamListQuery.totalCount"
                :page.sync="offLineExamListQuery.page"
                :limit.sync="offLineExamListQuery.MaxResultCount"
                @pagination="getTrainOffLineExamList"
              />
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-dialog :title="isEditOffLineCourse ? '编辑线下课程' : '添加线下课程'" :visible.sync="offLineCourseDialog">
      <el-form ref="offLineCourseForm" :model="offLineCourseForm" :rules="offLineCourseFormRules" label-width="150px">
        <el-form-item label="封面">
          <lz-upload-images
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList"
            @response-fn="handleImageResponse"
            @remove-upload="handleRemoveUploadImage"
          />
        </el-form-item>
        <el-form-item label="课程名称" prop="name">
          <el-input v-model="offLineCourseForm.name" />
        </el-form-item>
        <el-form-item label="课程类型" prop="creditHourType">
          <el-radio-group v-model="offLineCourseForm.creditHourType" :disabled="isEditOffLineCourse">
            <el-radio :label="0">面授课程</el-radio>
            <el-radio :label="1">网络课程</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="offLineCourseForm.startTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="offLineCourseForm.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item label="课程介绍" prop="introduce">
          <el-input v-model="offLineCourseForm.introduce" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item v-if="!offLineCourseForm.creditHourType" label="用户规模" prop="userCount">
          <el-input-number v-model="offLineCourseForm.userCount" :step="1" step-strictly :min="0" />
        </el-form-item>
        <el-form-item label="时长" prop="timeLong">
          <el-input-number v-model="offLineCourseForm.timeLong" :step="1" step-strictly :min="0">分钟</el-input-number>
        </el-form-item>
        <el-form-item label="课时" prop="classHour">
          <el-input-number v-model="offLineCourseForm.classHour" :step="0.1" step-strictly :min="0" />
        </el-form-item>
        <el-form-item label="讲师" prop="lecturer">
          <el-input v-model="offLineCourseForm.lecturer" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleOffLineCourseEditSure">确 定</el-button>
        <el-button round @click="offLineCourseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择学员" :visible.sync="selectUserDialog" :close-on-click-modal="false" width="1000px">
      <select-user v-if="selectUserDialog" @response-fn="handleSelectUserResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="selectUserDialogLoading" round type="primary" @click="handleSelectUserSure">确 定</el-button>
        <el-button round @click="selectUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择课程" :visible.sync="chooseCourseDialog" :close-on-click-modal="false" width="1000px">
      <choose-course-center v-if="chooseCourseDialog" @response="handleChooseCourseResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseCourseDialog = false">取 消</el-button>
        <el-button :loading="chooseCourseLoading" round type="primary" @click="handleChooseCourseSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择试卷" :close-on-click-modal="false" :visible.sync="examPaperChooseDialog">
      <choose-exam-paper
        v-if="examPaperChooseDialog"
        :current-paper="currentPaperId"
        @response="handleTrainSelectExamPaper"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleTrainSelectExamPaperSure">确 定</el-button>
        <el-button round @click="examPaperChooseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="课程编辑" :close-on-click-modal="false" :visible.sync="courseEditDialog">
      <el-form ref="courseEditForm" v-model="courseEditForm" label-width="160px">
        <el-form-item label="选修" prop="isRequired">
          <el-select v-model="courseEditForm.isRequired">
            <el-option :value="true" label="必修" />
            <el-option :value="false" label="选修" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="courseEditForm.order" :min="0" :step="1" step-strictly />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleCourseEditSure">确 定</el-button>
        <el-button round @click="courseEditDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="上传附件" :close-on-click-modal="false" :visible.sync="uploadResourceDialog" width="1200px">
      <el-form label-width="80px">
        <el-form-item label="上传资源" prop="url">
          <local-file-upload
            class="uplaod_file"
            :multiple="true"
            :file-size="1024"
            :file-type="uploadFileType"
            :file-list="uploadResourceList"
            :btn-title="'上传资源'"
            @response-fn="handleFileResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
      </el-form>
      <el-table :data="fileList">
        <el-table-column label="文件名称" prop="fileName">
          <template slot-scope="{row}">
            <span class="link-type" @click="handlePreviewResource(row)">{{ row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="文件类型" prop="fileType" />
        <el-table-column label="文件大小" prop="size">

          <template slot-scope="{row}">
            {{ row.size | formatFileSize }}
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="size" width="200">
          <template slot-scope="{row}">
            <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleFileDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="fileListQuery.totalCount > 0"
        :total="fileListQuery.totalCount"
        :page.sync="fileListQuery.page"
        :limit.sync="fileListQuery.MaxResultCount"
        @pagination="get0ffLineFileList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="uploadResourceList.length === 0" round type="primary" @click="handleUploadResourceSure">确
          定</el-button>
        <el-button round @click="uploadResourceDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="资源预览" append-to-body :visible.sync="resourcePreviewDialog" width="1000px" top="5vh">
      <bos-resource-preview
        v-if="resourcePreviewDialog && previewSource === 0"
        :type="previewType"
        :url="previewUrl"
        :doc-id="previewDocId"
      />
      <preview-resource
        v-if="resourcePreviewDialog && previewSource === 1"
        ref="previewResource"
        :type="previewType"
        :url="previewUrl"
      />
    </el-dialog>
    <el-dialog
      :title="offLineExamEdit ? '编辑' : '添加'"
      :close-on-click-modal="false"
      :visible.sync="offLineExamDialog"
      width="800"
    >
      <el-form ref="offLineExamForm" v-model="offLineExamForm" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="offLineExamForm.name" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleEditOffExamSure">确 定</el-button>
        <el-button round @click="offLineExamDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 同步回放时长 --->
    <el-dialog title="设置同步时间段" :visible.sync="SyncPlayBackTimeDialog" width="400px">
      <el-form size="small">
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="UserBackRecordQuery.StartTime"
            size="small"
            style="margin-left: 10px"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择结束时间"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="UserBackRecordQuery.EndTime"
            size="small"
            style="margin-left: 10px"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择结束时间"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="LiveUserBackRecordsloading" round type="primary" :disabled="LiveUserBackRecordsloading" @click="loadLiveUserBackRecords">确 定
        </el-button>
        <el-button round @click="SyncPlayBackTimeDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { uploadOffLineExamFile, offLineExamFileList, deleteOffLineExamFile, importOffLineExamScore, trainsAdd, trainsDetail, trainsAddUser, trainsUser, trainsUserAll, trainsDeleteUser, trainUsersUpdate, trainsDeleteCourse, trainsDeleteOffLineCourse, trainsCourseList, trainsOffLineCourseList, trainsAddCourse, trainsLiveList, trainsAddExam, trainsEditExam, trainsDeleteOffLineExam, trainsDeleteExam, trainsExamList, trainsEditCourse, trainsAddOffLineCourse, trainsEditOffLineCourse, importOffLineCourseHour, uploadOffLineCourseFile, offLineCourseFileList, deleteOffLineCourseFile, trainsEdit, trainsOffLineExamList, trainsAddOffLineExam, trainsEditOffLineExam } from '@/api/train'
import { examPaperDetailInfo, deleteExamination, updateExamAllowsubmittimes } from '@/api/examPaper'
import { classesUsers, findName } from '@/api/user'
import { courseLivePublish, deletesCourseLive, courseLivePlayBack, liveUserBackRecord_bjy } from '@/api/live'
import ChooseClass from '@/components/ChooseClass'
import ChooseExamPaper from '@/components/ChooseExamPaper'
import SelectUser from '@/components/ChooseUser/select.vue'
import Pagination from '@/components/Pagination'
import ChooseCourseCenter from '@/components/ChooseCourseCenter'
import LocalFileUpload from '@/components/LocalFileUpload'
import PreviewResource from '@/components/PreviewResource'
import LzUploadImages from '@/components/LzUploadImages'
import tinymce from '@/components/Tinymce'
import { parseTimeDate } from '@/utils'
import moment from 'moment'
export default {
  name: 'TrainEdit',
  components: {
    ChooseClass,
    SelectUser,
    Pagination,
    ChooseCourseCenter,
    ChooseExamPaper,
    LocalFileUpload,
    PreviewResource,
    LzUploadImages,
    tinymce
  },
  data() {
    var validateStartDate = (rule, value, callback) => {
      if (this.form.startDate.length === 0) {
        callback(new Error('请选择开始时间'))
      } else {
        callback()
      }
    }
    var validateEndDate = (rule, value, callback) => {
      if (this.form.endDate.length === 0) {
        callback(new Error('请选择结束时间'))
      } else {
        callback()
      }
    }
    var validateExamPaper = (rule, value, callback) => {
      if (this.trainExamForm.examPaperId.length === 0) {
        callback(new Error('请选择试卷'))
      } else {
        callback()
      }
    }
    return {
      trainSet: this.$route.query.from ? 'trainContent' : 'trainInfo',
      trainContentSet:
        this.$route.query.from === -2 ? 'trainLive' : 'trainCourse',
      isEdit: !!this.$route.query.id,
      pageTitle: this.$route.query.id ? this.$route.query.name : '新增',
      form: {
        tenantName: '',
        name: '',
        startDate: '',
        endDate: '',
        isShowCourse: true,
        isShowExam: true,
        isShowLive: true,
        isPublish: false,
        notice: '',
        learnInOrder: false,
        userCount: 0,
        imgUrl: ''
      },
      previewTrainImg: [],
      showContent: true,
      formRules: {
        name: [
          { required: true, message: '请输入培训名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度应小于 50 个字符', trigger: 'blur' }
        ],
        startDate: [{
          required: true, validator: validateStartDate, trigger: 'blur'
        }],
        endDate: [{
          required: true, validator: validateEndDate, trigger: 'blur'
        }]
        // notice: [
        //   { required: true, message: '请输入培训简介', trigger: 'blur' },
        //   { min: 1, max: 600, message: '长度应小于 600 个字符', trigger: 'blur' }
        // ]
      },
      list: [],
      liveList: [],
      examSet: false,

      // 用户管理
      exportUserLoading: false,
      selectUserDialog: false,
      selectUserDialogLoading: false,
      currentSelectUsers: [],
      selectUsersList: [],
      selectUsersListLoading: false,
      selectUsersListQuery: {
        Filter: '',
        TrainId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        IsAll: false,
        page: 1,
        totalCount: 0
      },
      currentDeleteUsers: [],
      // 选择班级
      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],
      // 选择课程中心
      chooseCourseDialog: false,
      chooseCourseLoading: false,
      currentSelectCourse: [],
      // 考核form
      trainExamPaperList: [],
      trainExamForm: {
        trainId: this.$route.query.id,
        examName: '',
        examTimeLong: 0,
        startTime: '',
        endTime: '',
        allowSubmitTimes: 0,
        passScore: 0,
        examPaperId: ''
      },
      isEditTrainExam: false,
      trainExamFormRules: {
        examPaperId: [
          { required: true, validator: validateExamPaper, trigger: 'blur' }
        ],
        examName: [
          { required: true, message: '请输入考核名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度应小于 50 个字符', trigger: 'blur' }
        ],
        startTime: [{
          required: true, message: '请选择时间', trigger: 'blur'
        }
        ],
        endTime: [{
          required: true, message: '请选择时间', trigger: 'blur'
        }
        ]
      },
      saveExamLoading: false,
      // 试卷选择
      examPaperChooseDialog: false,
      currentPaperId: '',
      currentSelectPaper: null,
      // 课程编辑
      courseEditDialog: false,
      courseEditForm: {
        isRequired: false,
        order: 0
      },
      courseEditFormRules: {

      },
      // 线下课程
      offLineCourseDialog: false,
      isEditOffLineCourse: false,
      previewFileList: [],
      offLineCourseForm: {
        coverUrl: '',
        name: '',
        introduce: '',
        lecturer: '',
        classHour: 0,
        startTime: '',
        endTime: '',
        timeLong: 0,
        userCount: 0,
        creditHourType: 0
      },
      offLineCourseFormRules: {
        name: [
          { required: true, message: '请输入课程名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度应小于 50 个字符', trigger: 'blur' }
        ]
      },
      offLineCourseList: [],

      importClassHourLoading: false,
      importOffLineCourseId: '',
      fileTemp: null,
      // 上传附件
      // 上传资源Dialog
      uploadResourceDialog: false,
      uploadResourceList: [],
      uploadFileType: ['pdf', 'mp4', 'zip', 'png', 'jpg', 'jpeg'],
      fileList: [],
      fileListLoading: false,
      fileListQuery: {
        Filter: '',
        OfflineCourseId: '',
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      // 资源预览
      resourcePreviewDialog: false,
      previewSource: 0, // 0：bce 1：local
      previewUrl: '',
      previewDocId: '',
      previewType: '',

      // 线下考核
      offLineExamList: [],
      offLineExamListLoading: false,
      offLineExamListQuery: {
        Filter: '',
        TrainId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      offLineExamEdit: false,
      offLineExamForm: {
        name: '',
        trainId: this.$route.query.id
      },
      offLineExamDialog: false,

      importAchiveLoading: false,
      importOffLineExamId: '',
      SyncPlayBackTimeDialog: false,
      UserBackRecordQuery: {
        IsAuto: false,
        LiveStreamId: '',
        StartTime: '',
        EndTime: ''
      },
      LiveUserBackRecordsloading: false
    }
  },
  created() {
    if (this.$route.query.id) {
      this.showContent = false
      this.getTrainDetail()
      this.getTrainCourseList()
      this.getTrainLiveList()
      this.getTrainOffLineCourse()
      this.getTrainUserList()
      this.getTrainExamInfo()
      this.getTrainOffLineExamList()
    }
  },
  methods: {
    // 选择同步时间段
    handleSyncPlayBackTime(row) {
      this.UserBackRecordQuery.LiveStreamId = row.id
      this.UserBackRecordQuery.StartTime = ''
      this.UserBackRecordQuery.EndTime = ''
      // console.log(row)
      this.SyncPlayBackTimeDialog = true
    },
    // 同步回放时长
    loadLiveUserBackRecords() {
      // console.log(this.UserBackRecordQuery)
      var days = moment(this.UserBackRecordQuery.EndTime).diff(this.UserBackRecordQuery.StartTime, 'days')
      for (var i = 0; i <= days; i++) {
        var start = moment(this.UserBackRecordQuery.StartTime).add(i, 'days').format('YYYY-MM-DD 00:00:00')
        var end = moment(this.UserBackRecordQuery.StartTime).add(i, 'days').format('YYYY-MM-DD 23:59:59')
        var data = {
          IsAuto: this.UserBackRecordQuery.IsAuto,
          LiveStreamId: this.UserBackRecordQuery.LiveStreamId,
          StartTime: start,
          EndTime: end
        }
        liveUserBackRecord_bjy(data).then((res) => {

        }).catch(() => {
          this.$message.error('数据同步失败')
        })
      }
      this.$message.success('数据同步中，稍后刷新页面查看')
      this.SyncPlayBackTimeDialog = false
    },
    trainsCourseList() {
      return trainsCourseList({ TrainId: this.$route.query.id, SkipCount: 0, MaxResultCount: 999 })
    },
    trainsLiveList() {
      return trainsLiveList({ TrainId: this.$route.query.id, SkipCount: 0, MaxResultCount: 999 })
    },
    trainsOffLineCourseList() {
      return trainsOffLineCourseList({ TrainId: this.$route.query.id, SkipCount: 0, MaxResultCount: 999 })
    },
    trainsOffLineExamList(args) {
      return trainsOffLineExamList(args)
    },
    // 基础信息编辑
    async handleSaveTrainInfo() {
      if (!this.$store.getters.tenantName.length) {
        const tenantName = '中国汽车研究协会'// await findName()
        // this.$store.dispatch('user/saveTenantname', tenantName)
        this.form.tenantName = tenantName
      } else {
        this.form.tenantName = this.$store.getters.tenantName
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.$route.query.id) {
            trainsEdit(this.$route.query.id, this.form).then((res) => {
              this.getTrainDetail()
              this.$message.success('保存成功')
            })
          } else {
            trainsAdd(this.form).then((res) => {
              this.$router.push({
                name: 'Train'
              })
            })
          }
        } else {
          return false
        }
      })
    },
    // 添加课程 跳转页面
    handleAddCourseCenter() {
      this.currentSelectCourse = []
      this.chooseCourseDialog = true
      // this.$router.push({
      //   name: 'TrainCourseSelect',
      //   query: { id: this.$route.query.id, name: this.pageTitle }
      // })
    },
    // 添加线下考核
    handleEditOffExam(t, row) {
      this.offLineExamForm = {
        name: '',
        trainId: this.$route.query.id
      }
      this.offLineExamEdit = !!t
      if (this.offLineExamEdit) {
        this.offLineExamForm.name = row.name
        this.offLineExamForm.trainId = this.$route.query.id
        this.offLineExamForm.id = row.id
      }
      this.offLineExamDialog = true
    },
    handleEditOffExamSure() {
      if (!this.offLineExamForm.name.length) {
        this.$message.warning('请输入名称')
        return
      }
      if (this.offLineExamEdit) {
        trainsEditOffLineExam(this.offLineExamForm.id, this.offLineExamForm).then(res => {
          this.$message.success('编辑成功')
          this.getTrainOffLineExamList()
          this.offLineExamDialog = false
        }).catch(() => {
          this.$message.error('编辑失败')
        })
      } else {
        trainsAddOffLineExam(this.offLineExamForm).then(res => {
          this.$message.success('保存成功')
          this.getTrainOffLineExamList()
          this.offLineExamDialog = false
        }).catch(() => {
          this.$message.error('保存失败')
        })
      }
    },
    handleOffLineExamDelete(row) {
      this.$confirm('是否确定删除 ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          trainsDeleteOffLineExam(row.id).then((res) => {
            this.$message.success('操作成功')
            this.getTrainOffLineExamList()
          })
            .catch(() => {
              this.$message.error('操作失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    handleCurrentOffLineExamChange(val) {
      if (val) {
        this.importOffLineExamId = val.id
      }
    },
    // 编辑直播
    handleTrainEditLive(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'TrainLiveSelect',
          query: { trainId: this.$route.query.id, name: this.pageTitle }
        })
      } else {
        this.$router.push({
          name: 'TrainLiveSelect',
          query: { trainId: this.$route.query.id, id: row.id, name: this.pageTitle }
        })
      }

      // this.$router.push({
      //   name: 'TrainLiveSelect',
      //   query: { id: this.$route.query.id, name: this.pageTitle }
      // })
    },
    // 直播详情
    handleLiveDetail(row) {
      const url = this.$router.resolve({
        name: 'LiveDetail',
        query: {
          id: row.id,
          title: row.title
        }
      })
      window.open(url.href, '_blank')
      // this.$router.push({
      //   name: 'LiveDetail',
      //   query: {
      //     id: row.id,
      //     title: row.title
      //   }
      // })
    },
    handleCourseLivePlayBack(row) {
      this.$confirm('是否确定' + (row.allowPlayBack ? '禁止回放?' : '允许回放?'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          courseLivePlayBack({ liveStreamId: row.id, allowPlayBack: !row.allowPlayBack }).then((res) => {
            this.$message.success('操作成功')
            this.getTrainLiveList()
          })
            .catch(() => {
              this.$message.error('操作失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    // 课程编辑
    handleCourseEdit(t, row) {
      this.courseEditForm = row
      this.courseEditDialog = true
    },
    // 发布 取消发布直播
    handleCourseLivePublish(row) {
      this.$confirm('是否确定' + (!row.publish ? '发布?' : '取消发布?'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          courseLivePublish(row.id).then((res) => {
            this.$message.success((!row.publish ? '发布' : '取消发布') + '成功')
            this.getTrainLiveList()
          })
            .catch(() => {
              this.$message.error((!row.publish ? '发布' : '取消发布') + '失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    // 删除直播 先删直播管理的 再删关联
    handleCourseLiveDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        await deletesCourseLive(row.id).then((res) => {

        }).catch(() => {
          this.$message.error('删除失败')
        })
        this.deleteTrainCourse(row.id, 1)
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 课程中心选择课程结果
    handleChooseCourseResponse(val) {
      this.currentSelectCourse = val
    },
    // 课程中心选择课程确定
    handleChooseCourseSure() {
      if (this.currentSelectCourse.length) {
        this.chooseCourseLoading = true
        var form = {
          trainId: this.$route.query.id,
          trainCourses: []
        }
        this.currentSelectCourse.forEach((item, index) => {
          form.trainCourses.push({
            trainId: this.$route.query.id,
            order: index + this.list.length + 1,
            courseId: item.courseId,
            courseName: item.courseName,
            expireDate: item.endTime,
            trainCourseType: 0,
            isRequired: item.isRequired
          })
        })
        trainsAddCourse(form).then(res => {
          this.chooseCourseLoading = false
          this.$message.success('保存成功')
          this.chooseCourseDialog = false
          this.getTrainCourseList()
        }).catch(res => {
          this.chooseCourseLoading = false
          this.$message.error('保存失败')
        })
      } else {
        this.$message.warning('课程数量为空,请选择课程')
      }
    },
    // 课程编辑
    handleCourseEditSure() {
      // this.$refs.courseEditForm.validate((valid) => {
      //   if (valid) {
      trainsEditCourse(this.courseEditForm.id, this.courseEditForm).then(res => {
        this.courseEditDialog = false
        this.$message.success('保存成功')
        this.getTrainCourseList()
      })
      //   } else {
      //     return false
      //   }
      // })
    },
    // 上传文件成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.offLineCourseForm.coverUrl = url
    },
    // 上传文件删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.offLineCourseForm.coverUrl = ''
    },
    // 添加线下课程
    handleEditOffLineCourse(t, row) {
      this.isEditOffLineCourse = !!t
      if (this.$refs.offLineCourseForm) {
        this.$refs.offLineCourseForm.resetFields()
      }
      this.offLineCourseForm = {
        coverUrl: '',
        name: '',
        introduce: '',
        lecturer: '',
        classHour: 0,
        startTime: '',
        endTime: '',
        timeLong: 0,
        userCount: 0,
        creditHourType: 0
      }
      if (this.isEditOffLineCourse) {
        this.offLineCourseForm = JSON.parse(JSON.stringify(row))
        if (row.coverUrl) {
          this.previewFileList = []
          this.previewFileList.push({
            url: row.coverUrl
          })
        }
      }
      this.offLineCourseDialog = true
    },
    // 编辑线下课程
    handleOffLineCourseEditSure() {
      this.$refs.offLineCourseForm.validate(async(valid) => {
        if (valid) {
          // if (!this.offLineCourseForm.creditHourType) {
          //   this.offLineCourseForm.userCount = 0
          // }
          if (this.isEditOffLineCourse) {
            trainsEditOffLineCourse(this.offLineCourseForm.id, this.offLineCourseForm).then(res => {
              this.offLineCourseDialog = false
              this.$message.success('编辑成功')
              this.getTrainOffLineCourse()
            })
          } else {
            const res = await trainsAddOffLineCourse(this.offLineCourseForm)
            var form = {
              trainId: this.$route.query.id,
              trainCourses: []
            }
            form.trainCourses = [{
              trainId: this.$route.query.id,
              order: 0,
              courseId: res.id,
              courseName: this.offLineCourseForm.name,
              expireDate: null,
              trainCourseType: 2,
              isRequired: false
            }]
            trainsAddCourse(form).then(res => {
              this.offLineCourseDialog = false
              this.$message.success('添加成功')
              this.getTrainOffLineCourse()
            }).catch(res => {
              this.$message.success('添加失败')
            })
          }
        } else {
          return false
        }
      })
    },
    // 删除线下课程 先删除线下课程 然后删除关联
    handleOffLineCourseDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        await trainsDeleteOffLineCourse(row.id).then((res) => {

        }).catch(() => {
          this.$message.error('删除失败')
        })
        this.deleteTrainCourse(row.id, 2)
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 选择用户
    handleSelectTrainUser(t) {
      this.currentSelectUsers = []

      this.selectUserDialog = true
    },
    // 选择用户结果
    handleSelectUserResponse(val) {
      this.currentSelectUsers = val
    },
    // 选择用户确定
    handleSelectUserSure() {
      if (this.currentSelectUsers.length) {
        this.selectUserDialogLoading = true
        var form = {
          trainId: this.$route.query.id,
          users: []
        }
        this.currentSelectUsers.forEach((item) => {
          form.users.push({
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : ''
            // identityCode: item.identityCode,
            // studentNumber: item.studentNumber,
            // studentIDNumber: item.studentIDNumber
          })
        })
        trainsAddUser(form).then(res => {
          this.$message.success('添加成功')
          this.selectUserDialogLoading = false
          this.selectUserDialog = false
          this.getTrainUserList()
        }).catch(() => {
          this.selectUserDialogLoading = false
          this.$message.error('添加失败')
        })
      } else {
        this.$message.warning(
          this.isSelectAdminUsers ? '请选择助教' : '请选择学员'
        )
      }
    },
    // 批量删除用户
    handleDeleteUserChange(val) {
      this.currentDeleteUsers = val
    },
    // 确定删除用户
    handleTrainUserDelete() {
      if (this.currentDeleteUsers.length) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            var form = {
              trainId: this.$route.query.id,
              trainUserIds: []
            }
            this.currentDeleteUsers.forEach((item) => {
              form.trainUserIds.push(item.id)
            })
            trainsDeleteUser(form)
              .then((res) => {
                this.$message.success('删除成功')
                this.currentDeleteUsers = []
                this.getTrainUserList()
              })
              .catch(() => {
                this.$message.error('删除失败')
              })
          })
          .catch(() => {
            this.$message.info('已取消删除')
          })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleTrainUserUpdate() {
      trainUsersUpdate(this.$route.query.id).then(res => {
        this.$message.success('操作成功，请稍后刷新查看结果')
      })
    },
    // 班级选择用户
    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    // 选择班级
    handleChooseClass(val) {
      this.selectedClass = val
    },
    // 按班级获取学员添加用户
    async handleChooseClassSure() {
      this.classLoading = true
      var form = {
        trainId: this.$route.query.id,
        users: []
      }
      for await (var classItem of this.selectedClass) {
        const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        res.items.forEach((item) => {
          form.users.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : ''
          })
        })
      }
      var setA = new Set()
      form.users = form.users.filter(item => {
        const result = setA.has(item.userId)
        setA.add(item.userId)
        return !result
      })
      trainsAddUser(form)
        .then((res) => {
          this.$message.success('添加成功')
          this.classLoading = false
          this.chooseClassDialog = false
          this.getTrainUserList()
        })
        .catch(() => {
          this.classLoading = false
          this.$message.error('添加失败')
        })
    },
    // 选择试卷
    handleChooseExamPaperClick() {
      this.examPaperChooseDialog = true
    },
    // 选择试卷结果
    handleTrainSelectExamPaper(val) {
      this.currentSelectPaper = val
    },
    // 选择试卷确定
    handleTrainSelectExamPaperSure() {
      this.trainExamPaperList = []
      if (this.currentSelectPaper) {
        this.trainExamForm.examPaperId = this.currentSelectPaper.id
        this.currentPaperId = this.currentSelectPaper.id
        this.trainExamPaperList.push(this.currentSelectPaper)
      }
      this.examPaperChooseDialog = false
    },
    // 保存考试 存在考试删除   不存在添加 存在考试编辑
    handleSaveTrainExam() {
      this.$refs.trainExamForm.validate(async(valid) => {
        if (valid) {
          if (this.trainExamForm.id) {
            if (this.examSet) {
              if (this.trainExamForm.examinationId != null) {
                await updateExamAllowsubmittimes({
                  id: this.trainExamForm.examinationId,
                  allowSubmitTimes: this.trainExamForm.allowSubmitTimes
                }).then(res => {
                  trainsEditExam(this.trainExamForm.id, this.trainExamForm).then(res => {
                    this.$message.success('保存成功')
                    this.getTrainExamInfo()
                  })
                })
              } else {
                trainsEditExam(this.trainExamForm.id, this.trainExamForm).then(res => {
                  this.$message.success('保存成功')
                  this.getTrainExamInfo()
                })
              }
            } else {
              if (this.trainExamForm.examinationId != null) {
                deleteExamination(this.trainExamForm.examinationId).then(res => {
                  trainsDeleteExam(this.trainExamForm.id).then(res => {
                    this.$message.success('保存成功')
                    this.getTrainExamInfo()
                  })
                }).catch(() => {

                })
              } else {
                trainsDeleteExam(this.trainExamForm.id).then(res => {
                  this.$message.success('保存成功')
                  this.getTrainExamInfo()
                })
              }
            }
          } else {
            if (this.examSet) {
              this.saveExamLoading = true
              var that = this
              trainsAddExam(this.trainExamForm).then(res => {
                setTimeout(function() {
                  that.$message.success('保存成功')
                  that.getTrainExamInfo()
                  that.saveExamLoading = false
                }, 1000 * 3)
              })
            } else {
              this.$message.success('保存成功')
            }
          }
        } else {
          return false
        }
      })
    },
    // 线下课程点击上传时需要获取当前id  根据点击按钮行变化获取id
    handleCurrentOffLineCourseChange(val) {
      if (val) {
        this.importOffLineCourseId = val.id
      }
    },
    // 导入学时
    handleChange(file, fileList) {
      this.importClassHourLoading = true
      this.importAchiveLoading = true
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.submitProgress = 0
          this.importfxx(0, this.fileTemp)
        } else {
          this.importUserLoading = false
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.importClassHourLoading = false
        this.importAchiveLoading = false
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    // 导入成绩
    handleAchiveChange(file, fileList) {
      this.importAchiveLoading = true
      this.fileTemp = file.raw
      if (this.fileTemp) {
        if (
          this.fileTemp.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type === 'application/vnd.ms-excel'
        ) {
          this.submitProgress = 0
          this.importfxx(1, this.fileTemp)
        } else {
          this.importUserLoading = false
          this.$message({
            type: 'warning',
            message: '附件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.importUserLoading = false
        this.$message({
          type: 'warning',
          message: '请上传附件！'
        })
      }
    },
    // 没啥用
    handleExceed() {
      this.importUserLoading = false
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      })
      return
    },
    // 没啥用
    handleRemove(file, fileList) {
      this.fileTemp = null
    },
    // 导入学时EXCEL解析
    importfxx(t, file) {
      const _this = this
      this.file = file
      var reader = new FileReader()
      reader.onload = async function(e) {
        var binary = ''
        var wb // 读取完成的数据
        var bytes = new Uint8Array(reader.result)
        var length = bytes.byteLength
        var xlsx = require('xlsx')
        for (var i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        wb = xlsx.read(binary, {
          type: 'binary'
        })
        _this.da = xlsx.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])

        if (_this.da.length > 0) {
          if (t === 0) {
            if (_this.importOffLineCourseId.length === 0) {
              _this.$message.warning('请选择线下课程')
              return
            }
            var tmp = []
            _this.da.forEach(item => {
              tmp.push({
                userId: item['用户ID'],
                userTrueName: item['姓名'],
                userName: item['用户名'],
                classId: item['部门ID'],
                className: item['部门'],
                classHour: item['学时'],
                offlineCourseId: _this.importOffLineCourseId
              })
            })
            for await (var i of tmp) {
              await importOffLineCourseHour(i).then(res => {

              }).catch(() => {

              })
            }
            _this.$message.success('导入成功')
            _this.importClassHourLoading = false
          } else {
            if (_this.importOffLineExamId.length === 0) {
              this.$message.warning('请选择线下考核')
              return
            }
            var tmp = []
            _this.da.forEach(item => {
              tmp.push({
                userId: item['用户ID'],
                userName: item['用户名'],
                userTrueName: item['姓名'],
                className: item['部门'],
                score: item['成绩'],
                offlineExamId: _this.importOffLineExamId
              })
            })
            for await (var i of tmp) {
              await importOffLineExamScore(i).then(res => {

              }).catch(() => {

              })
            }
            _this.$message.success('导入成功')
            _this.importClassHourLoading = false
          }
        } else {
          this.$message.error('Excel未读取到数据')
        }
      }
      reader.readAsArrayBuffer(file)
    },
    // EXCEL导出培训学生 添加学时 成为导入线下课程学时的模板
    async exportHandle() {
      this.exportUserLoading = true
      var allList = []
      const res = await trainsUserAll(this.$route.query.id)
      allList = res.items
      allList.forEach(item => {
        item.classHour = '0'
        item.achieve = '0'
      })
      this.exportUserLoading = false
      import('@/vendor/Export2Excel').then((excel) => {
        const tHeader = ['用户ID', '部门ID', '用户名', '姓名', '部门', '学时', '成绩']
        const filterVal = ['userId', 'classId', 'userName', 'name', 'className', 'classHour', 'achieve']
        // 成绩列表数据
        // const list = Response.items;
        const data = this.formatJson(filterVal, allList)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.$route.query.name + '_用户_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // EXCEL导出
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    // 上传附件
    handleUploadFile(t, row) {
      this.uploadResourceList = []
      if (t === 0) {
        this.fileListQuery = {
          Filter: '',
          OfflineCourseId: row.id,
          Sorting: '',
          SkipCount: 0,
          MaxResultCount: 10,
          page: 1,
          totalCount: 0
        }
      } else {
        this.fileListQuery = {
          Filter: '',
          OfflineExamId: row.id,
          Sorting: '',
          SkipCount: 0,
          MaxResultCount: 10,
          page: 1,
          totalCount: 0
        }
      }
      this.get0ffLineFileList()
      this.uploadResourceDialog = true
    },
    // 上传附件结果
    handleFileResponse(url, fileForm) {
      this.uploadResourceList.push({
        trainingProjectId: this.$route.query.projectId,
        url: url,
        localUrl: fileForm.localUrl,
        fileName: fileForm.fileName,
        name: fileForm.fileName,
        hash: fileForm.hash,
        extend: fileForm.fileType,
        size: fileForm.size,
        fileType: fileForm.fileType,
        resType: fileForm.resType,
        tranStatus: fileForm.tranStatus,
        documentId: fileForm.documentId,
        jobId: fileForm.jobId,
        durationInSecond: fileForm.durationInSecond
      })
    },
    // 删除文件
    handleRemoveUploadFile(file) {
      var index = this.uploadResourceList.indexOf(file)
      if (index > -1) {
        this.uploadResourceList.splice(index, 1)
      }
    },
    // 删除上传成功的文件
    handleFileDelete(row) {
      if (this.trainSet === 'trainExam') {
        deleteOffLineExamFile(row.id).then(res => {
          this.get0ffLineFileList()
        })
      } else if (this.trainContentSet === 'trainOffCourse') {
        deleteOffLineCourseFile(row.id).then(res => {
          this.get0ffLineFileList()
        })
      }
    },
    // 资源选择确定 进行上传
    async handleUploadResourceSure() {
      if (!this.uploadResourceList.length) {
        this.$message.warning('请上传资源')
        return
      }
      var data_list = []
      if (this.trainSet === 'trainExam') {
        this.uploadResourceList.forEach((item, index) => {
          var data = {
            fileName: item.fileName,
            fileType: item.fileType,
            url: item.localUrl,
            size: item.size,
            offlineExamId: this.fileListQuery.OfflineExamId
          }
          data_list.push(data)
        })
        for await (var item of data_list) {
          await uploadOffLineExamFile(item).then(res => {

          })
        }
      } else if (this.trainContentSet === 'trainOffCourse') {
        this.uploadResourceList.forEach((item, index) => {
          var data = {
            fileName: item.fileName,
            fileType: item.fileType,
            url: item.localUrl,
            size: item.size,
            offlineCourseId: this.fileListQuery.OfflineCourseId
          }
          data_list.push(data)
        })
        for await (var item of data_list) {
          await uploadOffLineCourseFile(item).then(res => {

          })
        }
      }
      this.get0ffLineFileList()
      this.uploadResourceList = []
    },
    // 资源预览
    async handlePreviewResource(row) {
      switch (row.fileType) {
        case '.mp4':
          this.previewType = 'video'
          break
        case '.pdf':
          this.previewType = 'pdf'
          break
        case '.ppt':
        case '.doc':
          this.previewType = 'doc'
          break
        case '.png':
        case '.jpeg':
        case '.jpg':
          this.previewType = 'image'
          break
      }
      if (row.fileType === '.zip') {
        this.$message.warning('暂不支持此类型文件')
        return
      }
      this.previewSource = 1
      this.previewUrl = row.url

      this.resourcePreviewDialog = true
    },
    // 刷新用户管理列表
    handleRefreshSelectUserList() {
      this.selectUsersListQuery.page = 1
      this.getTrainUserList()
    },
    // 选择培训包
    // 获取培训包包含的课程
    getTrainCourseList() {
      trainsCourseList({ TrainId: this.$route.query.id }).then((res) => {
        this.list = res.items
      })
    },
    // 获取培训直播
    getTrainLiveList() {
      trainsLiveList({ TrainId: this.$route.query.id, SkipCount: 0, MaxResultCount: 999 })
        .then((res) => {
          this.liveList = res.items
        })
        .catch(() => { })
    },
    // 获取培训线下课程
    getTrainOffLineCourse() {
      trainsOffLineCourseList({ TrainId: this.$route.query.id, SkipCount: 0, MaxResultCount: 999 })
        .then((res) => {
          this.offLineCourseList = res.items
        })
        .catch(() => { })
    },
    // 获取线下考核
    getTrainOffLineExamList() {
      this.offLineExamListLoading = true
      this.offLineExamListQuery.SkipCount = (this.offLineExamListQuery.page - 1) * this.offLineExamListQuery.MaxResultCount
      trainsOffLineExamList(this.offLineExamListQuery).then(res => {
        this.offLineExamList = res.items
        this.offLineExamListQuery.totalCount = res.totalCount
        this.offLineExamListLoading = false
      }).catch(() => { this.offLineExamListLoading = false })
    },
    // 获取线下课程上传的文件列表
    get0ffLineFileList() {
      this.fileListLoading = true
      this.fileListQuery.SkipCount = (this.fileListQuery.page - 1) * this.fileListQuery.MaxResultCount
      if (this.trainSet === 'trainExam') {
        offLineExamFileList(this.fileListQuery).then(res => {
          this.fileList = res.items
          this.fileListQuery.totalCount = res.totalCount
          this.fileListLoading = false
        }).catch(() => {
          this.fileListLoading = false
        })
      } else if (this.trainContentSet === 'trainOffCourse') {
        offLineCourseFileList(this.fileListQuery).then(res => {
          this.fileList = res.items
          this.fileListQuery.totalCount = res.totalCount
          this.fileListLoading = false
        }).catch(() => {
          this.fileListLoading = false
        })
      }
    },
    // 获取线下考核上传文件
    get0ffLineExamFileList() {
      this.fileListLoading = true
      this.fileListQuery.SkipCount = (this.fileListQuery.page - 1) * this.fileListQuery.MaxResultCount
      offLineExamFileList(this.fileListQuery).then(res => {
        this.fileList = res.items
        this.fileListQuery.totalCount = res.totalCount
        this.fileListLoading = false
      }).catch(() => {
        this.fileListLoading = false
      })
    },
    // 获取培训用户 trainUser
    getTrainUserList() {
      this.selectUsersListLoading = true
      this.selectUsersListQuery.SkipCount = (this.selectUsersListQuery.page - 1) * this.selectUsersListQuery.MaxResultCount
      trainsUser(this.selectUsersListQuery).then((res) => {
        this.selectUsersList = res.items
        this.selectUsersListQuery.totalCount = res.totalCount
        this.selectUsersListLoading = false
      }).catch(() => {
        this.selectUsersListLoading = false
      })
    },
    // 获取培训详情 trainInfo
    getTrainDetail() {
      trainsDetail(this.$route.query.id).then(res => {
        this.form.name = res.name
        if (this.$route.query.id) {
          this.pageTitle = res.name
        }

        this.form.startDate = parseTimeDate(res.startDate)
        this.form.endDate = parseTimeDate(res.endDate)
        this.form.learnInOrder = res.learnInOrder
        this.form.notice = res.notice
        this.form.isShowCourse = res.isShowCourse
        this.form.isShowLive = res.isShowLive
        this.form.isShowExam = res.isShowExam
        this.form.isPublish = res.isPublish
        this.form.userCount = res.userCount
        this.form.imgUrl = res.imgUrl

        this.showContent = true
        if (res.extraProperties) {
          this.form.tenantName = res.extraProperties.TenantName
        }
        if (this.form.imgUrl && this.form.imgUrl.length) {
          this.previewTrainImg = [{
            url: this.form.imgUrl
          }]
        }
      })
    },
    handleTrainImageResponse(url, fileForm) {
      this.previewTrainImg.push(fileForm)
      this.form.imgUrl = url
    },
    handleRemoveTrainImage(index) {
      this.previewTrainImg = []
      this.form.imgUrl = ''
    },
    // 获取培训考核详情
    getTrainExamInfo() {
      trainsExamList(this.$route.query.id).then(res => {
        if (res.items.length) {
          this.examSet = true
          this.isEditTrainExam = true
          this.trainExamForm = {
            trainId: this.$route.query.id,
            examName: res.items[0].examName,
            examTimeLong: res.items[0].examTimeLong,
            startTime: parseTimeDate(res.items[0].startTime),
            endTime: parseTimeDate(res.items[0].endTime),
            allowSubmitTimes: res.items[0].allowSubmitTimes,
            passScore: res.items[0].passScore,
            id: res.items[0].id,
            examinationId: res.items[0].examinationId,
            examPaperId: res.items[0].examPaperId,
            passCount: res.items[0].passCount
          }
          this.currentPaperId = res.items[0].examPaperId
          this.getPaperInfoDetail(res.items[0].examPaperId)
        } else {
          this.examSet = false
          this.isEditTrainExam = false
          this.currentPaperId = ''
          this.trainExamPaperList = []
          this.trainExamForm = {
            trainId: this.$route.query.id,
            examName: '',
            examTimeLong: 0,
            startTime: '',
            endTime: '',
            allowSubmitTimes: 0,
            passScore: 0
          }
        }
      })
    },
    // 获取试卷详情
    getPaperInfoDetail(id) {
      this.trainExamPaperList = []
      examPaperDetailInfo(id).then(res => {
        this.trainExamPaperList.push(res)
      })
    },
    handleRefreshExamList() {
      this.offLineExamListQuery.page = 1
      this.getTrainOffLineExamList()
    },
    // 删除培训课程关联
    deleteTrainCourse(id, t) {
      trainsDeleteCourse({ trainId: this.$route.query.id, courseId: id }).then(res => {
        this.$message.success('删除成功')
        if (t === 0) {
          this.getTrainCourseList()
        } else if (t === 1) {
          this.getTrainLiveList()
        } else if (t === 2) {
          this.getTrainOffLineCourse()
        }
      })
    }
  }
}
</script>


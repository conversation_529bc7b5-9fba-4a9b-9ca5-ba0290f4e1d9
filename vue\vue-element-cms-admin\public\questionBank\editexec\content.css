﻿@charset "utf-8";
/* CSS Document */

.cotrlBtn {
    width: auto;
    text-align: center;
    display: inline-block;
    height: 32px;
    line-height: 14px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 2px;
    padding: 7px 16px;
    margin-bottom: 0;
    font-weight: normal;
    white-space: nowrap;
    vertical-align: middle;
    background-image: none;
    -webkit-appearance: button;
    -webkit-user-select: none;
}

a.cotrlBtn {
    height: 14px;
    line-height: 14px;
    text-decoration: none;
    border: none;
    outline: none;
    font-size: 12px;
    border-radius: 2px;
    font-weight: normal;
    white-space: nowrap;
    vertical-align: middle;
    background-image: none;
    -webkit-appearance: none;
    -webkit-user-select: none;
}

.btnDefault, a.btnDefault {
    color: #666;
    border: 1px solid #ddd;
    background-color: #f7f7f7;
}

    .btnDefault:hover, a.btnDefault:hover {
        color: #555;
        background-color: #fff;
        border-color: #ddd;
    }

.cotrlBtn:active,
.cotrlBtn.active,
a.cotrlBtn:active,
a.cotrlBtn.active {
    background-image: none;
    outline: 0;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.color-green {
    color: #008000;
}


.home-desktop {
    overflow-y: auto;
    background:#f6fafe;
}


/*查询条件样式*/
.condition-box {
    padding: 20px;
    background: #fff;
    margin-bottom: 2px;
}

    .condition-box .condition-items {
        width: auto;
        height: 32px;
        line-height: 32px;
        display: inline;
        float: left;
        margin: 3px;
        margin-right: 20px;
    }

        .condition-box .condition-items .condition-title {
            width: auto;
            display: inline-block;
            margin-right: 5px;
        }

.condition-txt[type=text], .condition-txt[type=password], .condition-txt[type=email], .condition-txt[type=search] {
    padding: 0 5px;
    width: 150px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ddd;
    display: inline-block;
    position: relative;
    top: 2px;
}

.condition-box .condition-items .cotrlBtn {
    margin-left: 5px;
}

.condition-box .condition-items .dc_select,
.condition-box .condition-items .condition-title,
.condition-box .condition-items .condition-txt,
.condition-box .condition-items .cotrlBtn {
    vertical-align: middle;
}

.set-user-info-menu li {
    height: 30px;
    line-height: 30px;
    margin-bottom: 3px;
    background: #f8f8f8;
}

    .set-user-info-menu li a {
        display: block;
        padding: 0 5px;
        color: #666;
        text-align: center;
        font-size: 14px;
    }

.setItem-section {
    height: 30px;
    line-height: 30px;
    padding: 0 5px;
    display: inline-block;
    color: #666;
    font-size: 14px;
}

/*在线考试*/
.create-questions {
    width: 78%;
    padding-right: 3%;
    position: relative;
    float: right;
    margin-bottom:20px;
}

.exam-nav {
    width: 15%;
    margin-top: 10px;
    background: #fff;
    z-index: 8;
    position: absolute;
    /*min-width:200px;*/
}

.exam-navRight {
    width: 50px;
    background: #fff;
    position: absolute;
    right: 0;
    z-index: 8;
    /*position: fixed;*/
}

.exam-item-title {
    line-height: 60px;
    height: 60px;
    /* padding: 5px 0 5px 0; */
    text-align: center;
    font-size: 16px;
    border-bottom: 1px solid #e2e2e2;
    color: #333;
    cursor: pointer;
    font-weight: 600;
}

.exam-item-info {
    line-height: 60px;
    height: 60px;
    /*padding: 5px 0 5px 0;*/
    text-align: center;
    font-size: 16px;
    border-bottom: 1px solid #e2e2e2;
    color: #333;
    font-weight:600;
}


.exam-item-sub {
    line-height: 60px;
    height: 60px;
    /*padding: 5px 0 5px 0;*/
    text-align: center;
    font-size: 16px;
    border-bottom: 1px solid #e2e2e2;
    color: #333;
    cursor:pointer
}

    .exam-item-title i {
        margin-left: 5px;
    }

.exam-item-info i {
    margin-left: 5px;
    color: #FFA91B
}

.exam-nav-list > li:not(.ui-module) {
    line-height: 60px;
    height: 60px;
    font-size: 14px;
    padding: 2px 0;
    background: #fff;
    border-bottom: 1px solid #e2e2e2;
    text-align:center;
}

    .exam-nav-list > li:not(.ui-module) a {
        display: block;
        padding: 0 8px;
        color: #666;
    }

        .exam-nav-list > li:not(.ui-module) a i {
            margin-right: 3px;
            font-size: 14px;
        }

    .exam-nav-list > li:not(.ui-module):hover {
        /* box-shadow: 0px 0px 20px 10px rgba(255,255,255,1); */
        -webkit-animation: blurS 0.2s linear 1;
        -moz-animation: blurS 0.2s linear 1;
        animation: blurS 0.2s linear 1;
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -o-transform: scale(1.1);
        -ms-transform: scale(1.1);
        transform: scale(1.1);
        /* opacity: 0.8; */
        box-shadow: 0 0 2px rgba(0,0,0,0.2);
    }

    .exam-nav-list > li:not(.ui-module):active {
        /* box-shadow: 0px 0px 20px 10px rgba(255,255,255,1); */
        -webkit-animation: blurS 0.2s linear 1;
        -moz-animation: blurS 0.2s linear 1;
        animation: blurS 0.2s linear 1;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
        /* opacity: 0.8; */
        box-shadow: 0 0 2px rgba(0,0,0,0.2);
    }

.questions-head-title {
    width: auto;
    min-height: 50px;
    padding-left: 55px;
    background: #fff;
}

    .questions-head-title h4 {
        width: 100%;
        padding: 10px 5px;
        min-height: 20px;
        line-height: 20px;
        position: relative;
        top: 5px;
        bottom: 5px;
        text-align: center;
    }

table.questions-items-title {
    width: 100%;
    height: 50px;
    background: #fff;
    border-collapse: collapse;
    border-spacing: 0;
    border-top: 1px solid #e2e2e2;
    border-bottom: 1px solid #e2e2e2;
}

    table.questions-items-title td.module-menu {
        width: 40px;
        height: 40px;
        border-right: 1px solid #e2e2e2;
    }

    table.questions-items-title td {
        padding: 5px;
    }

        table.questions-items-title td .cq-title {
            width: 92%;
            min-height: 20px;
            line-height: 20px;
            padding: 10px 5px;
        }

.ui-questions-content-list {
    min-height: 120px;
}

    .ui-questions-content-list > li.ui-module,
    .exam-nav-list > li.ui-module {
        width: 100%;
        background: #fff;
        margin: 25px 0;
        color: #666;
    }

        .ui-questions-content-list > li.ui-module .theme-type,
        .exam-nav-list > li.ui-module .theme-type {
            position: relative;
            padding-left: 50px;
        }

    .ui-questions-content-list > li.items-questions .theme-type {
        min-height: 190px;
    }

    .ui-questions-content-list > li.ui-module .theme-type .module-menu,
    .exam-nav-list > li.ui-module .theme-type .module-menu {
        position: absolute;
        width: 50px;
        height: 100%;
        left: 0;
        top: 0;
        border-right: 1px solid #e2e2e2;
    }

        .ui-questions-content-list > li.ui-module .theme-type .module-menu h4 {
            height: 60px;
            line-height: 60px;
            margin: 1px 0;
            font-size: 16px;
            overflow: hidden;
            text-align: center;
        }

        .ui-questions-content-list > li.ui-module .theme-type .module-menu .module-ctrl {
            width: 30px;
            text-align: center;
            margin: 0 auto;
            margin-top: 10px;
        }

            .ui-questions-content-list > li.ui-module .theme-type .module-menu .module-ctrl a {
                display: none;
                height: 24px;
                line-height: 24px;
                margin: 20px auto;
                font-size: 16px;
                color: #999;
            }

    .ui-questions-content-list > li.ui-module .ui-drag-area,
    .exam-nav-list > li.ui-module .ui-drag-area {
        width: auto;
        min-height: 30px;
        padding: 5px;
        cursor: move;
        /*border-bottom: 1px solid #eee;*/
    }

    .ui-questions-content-list > li.ui-module .ui-img-list,
    .exam-nav-list > li.ui-module .ui-img-list {
        /*width: 100%;*/
        padding: 5px;
        cursor: pointer;
        border-bottom: 1px solid #e2e2e2;
        margin-right: 30px;
    }

    .ui-questions-content-list > li.ui-module .ui-img-list img,
    .exam-nav-list > li.ui-module .ui-img-list img{
        width: 120px;
        height: 120px;
    }

    .ui-questions-content-list > li.ui-module .ui-img-option-list,
    .exam-nav-list > li.ui-module .ui-img-option-list {
        width: 100%;
        padding: 4px;
        cursor: pointer;
        border-bottom: 1px solid #e2e2e2;
        margin-left: -30px;
    }

        .ui-questions-content-list > li.ui-module .ui-img-option-list img,
        .exam-nav-list > li.ui-module .ui-img-option-list img {
            width: 120px;
            height: 120px;
        }


    .ui-questions-content-list > li.ui-module .cq-title,
    .exam-nav-list > li.ui-module .cq-title {
        width: 80%;
        min-height: 40px;
        line-height: 40px;
        padding: 5px;
        font-size: 16px;
    }

    .ui-questions-content-list > li.ui-module .cq-items-content,
    .exam-nav-list > li.ui-module .cq-items-content {
        padding: 5px;
        white-space: normal;
        word-wrap: break-word;
    }

    .ui-questions-content-list > li.ui-module .cq-items-ctrl {
        font-size: 16px;
        margin-top: 15px;
        /* height: 26px; */
    }

        .ui-questions-content-list > li.ui-module .cq-items-ctrl a {
            display: inline-block;
            width: 20px;
            height: 20px;
            line-height: 20px;
            color: #999;
            margin-right: 20px;
        }

    .ui-questions-content-list > li.ui-state-highlight {
        height: 15px;
        background: #F0AD4E;
    }

    .ui-questions-content-list > li.ui-module .theme-type:hover .module-menu .module-ctrl a {
        display: block;
    }

    .ui-questions-content-list > li.ui-module .theme-type:hover .cq-items-ctrl a {
        display: inline-block;
        visibility: visible;
    }

    .ui-questions-content-list > li.ui-module .ui-drag-area:active .T_edit {
        background: none !important;
    }

    .ui-questions-content-list > li.ui-module .cq-unset-list > li, .exam-nav-list > li.ui-module .cq-unset-list > li {
        padding-left: 22px;
        position: relative;
    }

    .ui-questions-content-list > li.ui-module .cq-unset-list li > .input-check, .exam-nav-list > li.ui-module .cq-unset-list li > .input-check {
        width: 25px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        left: 0px;
        top: 10px;
    }

        .ui-questions-content-list > li.ui-module .cq-unset-list li > .input-check input, .exam-nav-list > li.ui-module .cq-unset-list li > .input-check input {
            position: relative;
            top: 4px;
        }

    .ui-questions-content-list > li.ui-module .cq-unset-list li, .exam-nav-list > li.ui-module .cq-unset-list li {
        line-height: 21px;
        margin: 10px 0 0 5px;
        font-size: 14px;
    }

        .ui-questions-content-list > li.ui-module .cq-unset-list li .cq-answer-content {
            /*max-width: 800px;*/
            margin-left: 12px;
            margin-right: 12px;
            min-height: 32px;
            width: 80%;
            padding-top: 10px;
            margin-top: 5px;
        }

        .ui-questions-content-list > li.ui-module .cq-unset-list li label, .exam-nav-list > li.ui-module .cq-unset-list li label {
            display: inline-block;
        }


.exam-nav-list > li.ui-module {
    z-index: 88;
}

    .exam-nav-list > li.ui-module .theme-type .module-menu h4,
    .exam-nav-list > li.ui-module .theme-type .module-menu .module-ctrl,
    .exam-nav-list > li.ui-module .theme-type .module-menu .module-ctrl a {
        display: none;
    }

.exam-nav-list > li.items-questions .theme-type {
    height: 160px;
}

.ui-questions-content-list > li.ui-module .cq-items-ctrl a,
.exam-nav-list > li.ui-module .cq-items-ctrl a {
    visibility: hidden;
}

.cq-into-edit {
    position: absolute;
    background: #fff;
    z-index: 999;
}

    .cq-into-edit .cq-edit-title {
        min-height: 21px;
        line-height: 21px;
        /*min-width: 750px;*/
        padding: 0px 3px;
        outline: none;
        font-size:16px;
    }

.move-tisp-box {
    position: absolute;
    text-align: center;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    background: rgba(0,0,0,0.6);
    color: #fff;
    font-size: 14px;
    border-radius: 5px;
    z-index: 88;
}

.edit-plug-box {
    width: auto;
    height: 40px;
    line-height: 40px;
    padding: 0px 10px;
    text-align: center;
    /*position: absolute;*/
    top: -41px;
    right: 0px;
    background: #fff;
    border: 1px solid #e2e2e2;
    text-align: right;
    border-top:none;
}

    .edit-plug-box a {
        padding: 0 10px;
        display: inline-block;
        font-size: 18px;
        /*cursor:pointer*/
        vertical-align: middle;
    }

textarea.exam-textarea, .exam-textarea {
    width: 99%;
    min-height: 120px;
    padding: 5px;
    line-height: 1.5;
    color: #666;
    border: 1px solid #ddd;
    word-break: break-all;
    font-size: 14px;
    resize: none;
    margin-left: -5px;
    border-top:none;
}

.describe-edit-content {
    min-height: 100px;
    padding: 0 3px;
}

.describe-edit-content-blank {
    /*min-height: 100px;*/
    padding: 0 3px;
}

.cq-score {
    border: 1px solid #e2e2e2;
    text-align: center;
    width: 80px;
    height: 32px;
    border-radius:3px;
    font-size:16px;
    color:#0f89fd;
    margin-right:15px;
}

.totalScores {
    text-align: center;
    width: 25px;
    color:#0f89fd;
}

.imgupstyle {
    width: 35px;
    height: 25px;
    cursor: pointer;
    background:url('./img/exc_img.png');
    position:relative;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
    margin-right:-10px;
}
.imgupstyle_img{
      width: 220px;
    height: 60px;
    cursor: pointer;
    background:url('./img/exc_img.png');
    position:relative;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
    margin-right:-10px;
}
.t_score{
   padding: 0 8px;
   height:60px;
   line-height:60px;
   text-align:center;
   font-size:14px;
}

.input_struct{
    text-align:center;
    border-bottom:1px solid #ccc;
    width:300px;
    height:50px;
    margin-left:150px;
    margin-top:75px;
    font-size:18px;
}

.input_struct_sc{
    text-align:center;
    border-bottom:1px solid #ccc;
    width:200px;
    margin-left:20px;
    margin-top:30px;
    font-size:18px;
    background:#f5f5f5;
}
.input_struct_sc_span{
    margin-left: 100px;
    font-size: 16px;
}

.input_struct_sp_tl{
    margin-left: 130px;
    font-size: 16px;
}

.input_struct_sc_tl{
    width: 200px;
    margin-left: 10px;
    margin-top: 15px;
    font-size: 18px;
    display: inline-block;
    font-weight:600
}

.cata_child{
    margin-bottom:40px;
    margin-top:20px !important;
}
.es_input{
    border: 1px solid #e2e2e2;
    min-height: 45px;
    border-radius: 3px;
    font-size: 14px;
    z-index: 2;
    padding-left:8px;
}
.icon_anysis{
    vertical-align: middle;
    /*margin-left: 20px;*/
    margin-bottom: 2px;
}
.configs{
    margin-top:15px !important;
    padding-bottom: 20px;
    padding-left: 20px;
}
.config_line{
    margin:10px;
    display:inline-block;
}
.config_res{
    margin-right:5px !important;
}
.config_txt{
     margin-left:5px !important;
}
.qs_info{
    display:inline-block;
    margin-left:30px;
    font-size:14px;
}
.qs_base{
    text-align: center;
    margin-top: 15px;
    font-size: 16px;
    height: 30px;
    background: #f5f5f5;
    width: 500px;
    margin-left: 30px;
    padding-top: 10px;
}
.qs_div{
    width: 500px;
    background: #f5f5f5;
    margin-left: 30px;
    padding-bottom: 30px;
    margin-top: 20px;
}
.qs_tl_info{
    display:inline-block;line-height: 30px;
}
.qs_blk_txt{
    border: 1px solid #ccc;
    text-align: center;
    font-size:14px;
    border-radius: 2px;
    height: 30px;
}

.qs_blk_txtarea{
   font-size:14px;
   padding: 5px;
   border-radius: 2px;
}
.qs_blk_tr{
   height:60px;
}
.qs_trblk_txtarea{
   font-size:14px;
   padding: 5px;
   border-radius: 2px;
}
.icon_alg_txt{font-size:12px;vertical-align:middle;color:#2894f8;margin-left:5px;}

@media screen and (min-width:1000px) {
}

@media screen and (min-width:1280px) {
}

@media screen and (min-width:1360px) {
}

@media screen and (min-width:1440px) {
}

@media screen and (min-width:1680px) {
}

@media screen and (min-width:1920px) {
}

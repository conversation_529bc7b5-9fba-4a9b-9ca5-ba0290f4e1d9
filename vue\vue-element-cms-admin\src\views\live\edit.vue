<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span>添加/编辑直播</span>
      </div>
      <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
        <el-tab-pane label="基础信息" name="liveInfo">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-col :span="10">
              <el-form-item label="直播封面" prop="coverUrl">
                <lz-upload-images
                  ref="previewFile"
                  :limit="1"
                  :file-size="500"
                  :file-type="['jpg', 'png', 'jpeg']"
                  :source-list="previewFileList"
                  @response-fn="handleImageResponse"
                  @remove-upload="handleRemoveUploadImage"
                />
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <el-form-item label="直播主题" prop="title">
                <el-input v-model="form.title" />
              </el-form-item>
              <el-form-item label="直播分类" prop="liveThemeId">
                <el-select v-model="form.liveThemeId" placeholder="选择直播分类" clearable filterable>
                  <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item prop="startTime" label="开始时间">
                <el-date-picker
                  v-model="form.startTime"
                  :disabled="isPublish"
                  :picker-options="liveStartOptions"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间"
                  @change="handleTimeChange"
                />
              </el-form-item>
              <el-form-item prop="endTime" label="结束时间">
                <el-date-picker
                  v-model="form.endTime"
                  :disabled="isPublish"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间"
                  @change="handleTimeChange"
                />
              </el-form-item>
              <el-form-item label="直播时长" prop="timeLong">
                <el-input-number v-model="form.timeLong" :disabled="isPublish" :min="0" /> 分钟
              </el-form-item>
              <el-form-item label="内容介绍" prop="description">
                <el-input v-model="form.description" type="textarea" :row="2" />
              </el-form-item>
              <el-form-item label="直播回放" prop="backType">
                <el-radio-group v-model="form.backType">
                  <el-radio :label="0">默认</el-radio>
                  <el-radio :label="2">纯视频</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="讲师" prop="lecturer">
                <el-input v-model="form.lecturer" />
              </el-form-item>
              <el-form-item label="直播讲师" prop="teacherId">
                <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectTeacher">选择讲师
                </el-button>
                <span v-if="form.teacherId" style="margin-left: 20px">已选择 1 人 </span>
                <el-tag
                  v-if="selectTeacher != null && selectTeacher.name != null"
                  type="primary"
                  size="small"
                  style="margin-left: 20px"
                >
                  {{ selectTeacher.name }}
                </el-tag>
              </el-form-item>
              <el-form-item label="直播规模" prop="userCount">
                <el-input-number v-model="form.userCount" :step="1" :min="0" step-strictly /> 人
              </el-form-item>
              <el-form-item label="有效期" prop="closeDate">
                <el-date-picker
                  v-model="form.closeDate"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间"
                />
              </el-form-item>
              <el-form-item label="课时" prop="classHour">
                <el-input-number v-model="form.classHour" :min="0" :step="0.1" step-strictly />
              </el-form-item>
              <el-form-item label="合格时长" prop="passDuration">
                <el-input-number v-model="passDuration" :min="0" :step="1" step-strictly /> 分钟
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="form.incloudBackPlay">回放观看时长是否记录合格时长</el-checkbox>
              </el-form-item>

              <el-form-item>
                <el-button :loading="liveLoading" round type="primary" @click="handleLiveSure">保 存</el-button>
              </el-form-item>
            </el-col>

          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="用户管理" name="liveUser">
          <div class="header_flex_box">
            <el-input
              v-model="selectUsersListQuery.Filter"
              size="small"
              class="small_input"
              clearable
              placeholder="输入名称搜索"
            />
            <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshSelectUserList">搜索
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectLiveUser(false)">选择学员
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectClass">按班级选择学员
            </el-button>
            <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleSelectLiveUser(true)">选择助教
            </el-button>
            <el-button round size="mini" type="danger" icon="el-icon-delete" @click="handleLiveUserDelete">批量删除
            </el-button>
            <export-excel
              :header="['序号', '姓名', '用户名', '部门', '角色']"
              :filter-val="['userNumber', 'name', 'userName', 'className', 'userRole']"
              :query="{ 'LiveStreamId': $route.query.id }"
              :field="{ 4: ['学生', '助教'] }"
              :api-fn="liveUserList"
            />
          </div>
          <el-table
            v-loading="selectUsersListLoading"
            :data="selectUsersList"
            max-height="700px"
            size="small"
            @sort-change="handleSelectUserSortChange"
            @selection-change="handleDeleteUserChange"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column label="序号" prop="userNumber" sortable="userNumber" />
            <el-table-column label="姓名" prop="name" sortable="name" />
            <el-table-column label="用户名" prop="userName" sortable="userName" />
            <el-table-column label="部门" prop="className" sortable="className" />
            <el-table-column label="角色" prop="userRole" sortable="userRole">
              <template slot-scope="{row}">
                {{ row.userRole ? '助教' : '学生' }}
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="selectUsersListQuery.totalCount > 0"
            :total="selectUsersListQuery.totalCount"
            :page.sync="selectUsersListQuery.page"
            :page-sizes="[10, 20, 50, 100, 500, 1000]"
            :limit.sync="selectUsersListQuery.MaxResultCount"
            @pagination="getLiveUserList"
          />
        </el-tab-pane>
        <el-tab-pane v-if="isEdit" label="直播考评" name="liveExam">
          <el-form ref="liveExamForm" :model="liveExamForm" :rules="liveExamFormRules" label-width="160px">
            <el-form-item label="直播考评">
              <el-switch v-model="liveExam" active-color="#13ce66" inactive-color="#ff4949" />
            </el-form-item>
            <div v-if="liveExam">
              <el-form-item label="选择试卷" prop="examPaperId">
                <el-button round size="small" type="primary" icon="el-icon-plus" @click="handleChooseExamPaperClick">
                  选择试卷</el-button>
                <el-table ref="liveExamPaperTable" :data="liveExamPaperList" highlight-current-row>
                  <el-table-column label="试卷名称" prop="name" />
                  <el-table-column label="题数" prop="questionNumber" />
                  <el-table-column label="总分" prop="totalScore" />
                </el-table>
              </el-form-item>
              <el-form-item label="考评名称" prop="liveExam.examName">
                <el-input v-model="liveExamForm.liveExam.examName" />
              </el-form-item>
              <el-form-item v-if="liveExamPaperList.length" label="合格分数" prop="order">
                <el-input-number
                  v-model="liveExamForm.liveExam.passScore"
                  :max="liveExamPaperList[0].totalScore"
                  :step="0.1"
                  step-strictly
                />
              </el-form-item>
              <!-- <el-form-item label="考评时长" prop="order">
                <el-input-number v-model="liveExamForm.liveExam.examTimeLong" />
              </el-form-item> -->
              <el-form-item label="考评通过才能获取学时" prop="order" label-width="160px">
                <el-radio-group v-model="liveExamForm.passExamGetHour">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否学习完成才能考评" prop="canExamAfterLearn" label-width="160px">
                <el-radio-group v-model="liveExamForm.canExamAfterLearn">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>

            </div>
            <el-form-item>
              <el-button round :loading="saveLoading" type="primary" icon="el-icon-check" @click="handleSaveLiveExam">保存
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

    </el-card>
    <el-dialog title="选择班级" :visible.sync="chooseClassDialog" :close-on-click-modal="false" width="1000px">
      <choose-class v-if="chooseClassDialog" @response="handleChooseClass" />
      <span slot="footer" class="dialog-footer">
        <el-button round @click="chooseClassDialog = false">取 消</el-button>
        <el-button :loading="classLoading" round type="primary" @click="handleChooseClassSure">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="chooseTeacherDialog"
      title="选择讲师"
      :visible.sync="chooseTeacherDialog"
      :close-on-click-modal="false"
      width="1000px"
    >
      <el-input v-model="teacherListQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
      <el-button size="small" round type="success" icon="el-icon-search" @click="handleTeacherRefreshList">搜索
      </el-button>
      <el-table
        ref="teacherTable"
        v-loading="teacherListLoading"
        :data="teacherList"
        :row-key="rowKey"
        style="width: 100%"
        size="small"
        highlight-current-row
        stripe
        @sort-change="teacherSortChange"
        @selection-change="handleTeacherSelectionChange"
        @row-click="handleTeacherRowClick"
      >
        <el-table-column type="selection" width="44px" highlight-current-row />
        <el-table-column label="用户名" prop="userName" sortable="userName" width="160px" show-overflow-tooltip />
        <el-table-column label="姓名" prop="name" sortable="name">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门" prop="extraProperties.OUName" sortable="custom">
          <template slot-scope="scope">
            <span>{{ scope.row.extraProperties.OUName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" prop="phoneNumber" sortable="phoneNumber" width="120px">
          <template slot-scope="scope">
            <span>{{ scope.row.phoneNumber }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" sortable="creationTime" label="创建日期" width="160px">
          <template slot-scope="scope">
            <span>{{ scope.row.creationTime | formatDateTime }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="teacherListQuery.totalCount > 0"
        :total="teacherListQuery.totalCount"
        :page.sync="teacherListQuery.page"
        :limit.sync="teacherListQuery.MaxResultCount"
        @pagination="getTeachList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="dialogLoading" round type="primary" @click="handleChooseTeachSure">确 定</el-button>
        <el-button round @click="chooseTeacherDialog = false">取 消</el-button>

      </span>
    </el-dialog>
    <el-dialog title="选择试卷" :close-on-click-modal="false" :visible.sync="examPaperChooseDialog">
      <choose-exam-paper
        v-if="examPaperChooseDialog"
        :current-paper="currentPaperId"
        @response="handleCourseSelectExamPaper"
      />
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleCourseSelectExamPaperSure">确 定</el-button>
        <el-button round @click="examPaperChooseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="isSelectAdminUsers ? '选择助教' : '选择用户'"
      :visible.sync="selectUserDialog"
      :close-on-click-modal="false"
      width="1000px"
    >
      <select-user v-if="selectUserDialog" :all-org="allOrg" @response-fn="handleSelectUserResponse" />
      <span slot="footer" class="dialog-footer">
        <el-button :loading="selectUserDialogLoading" round type="primary" @click="handleSelectUserSure">确 定</el-button>
        <el-button round @click="selectUserDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="progressTitle" :visible="permissionLoading" :show-close="false" width="600px">
      <el-progress :percentage="progressValue" color="#409eff" />
    </el-dialog>
  </div>
</template>
<script>
import {
  addCourseLive,
  editCourseLive,
  liveDetailInfo,
  liveUserAll,
  liveUserList,
  addLiveUsers,
  deleteLiveUsers,
  liveExamEdit,
  liveExamInfo,
  liveCategoryList
} from '@/api/live'
import {
  loadNodes,
  getFilterUsers,
  getAllStudents,
  classesUsers
} from '@/api/user'
import { examPaperDetailInfo } from '@/api/examPaper'
import Pagination from '@/components/Pagination'
import LzUploadImages from '@/components/LzUploadImages'
import ChooseClass from '@/components/ChooseClass'
import ChooseExamPaper from '@/components/ChooseExamPaper'
import permission from '@/directive/permission'
import SelectUser from '@/components/ChooseUser/select.vue'
import { parseTimeDate } from '@/utils/index'
import moment from 'moment'

export default {
  name: 'CenterEdit',
  directives: {
    permission
  },
  components: {
    Pagination,
    LzUploadImages,
    ChooseClass,
    ChooseExamPaper,
    SelectUser
  },
  data() {
    var liveUserValid = (rule, value, callback) => {
      if (this.selectUsers.length === 0) {
        callback(new Error('请选择用户'))
      } else {
        callback()
      }
    }
    var adminUserValid = (rule, value, callback) => {
      if (this.selectAdminUsers.length === 0) {
        callback(new Error('请选择助教'))
      } else {
        callback()
      }
    }
    var liveStartTimeValid = (rule, value, callback) => {
      if (!this.form.startTime) {
        callback(new Error('请选择开始时间'))
      }
      // else if (moment(this.form.startTime).isBefore(moment(), 'minute')) {
      //   callback(new Error('请选择当前时间之后的的时间'))
      // }
      else {
        callback()
      }
    }
    var liveEndTimeValid = (rule, value, callback) => {
      if (!this.form.endTime) {
        callback(new Error('请选择结束时间'))
      } else if (moment(this.form.startTime).isAfter(this.form.endTime, 'minute')) {
        callback(new Error('结束时间应大于开始时间'))
      } else {
        callback()
      }
    }
    var liveLecturerValid = (rule, value, callback) => {
      if (this.form.teacherId === '' || this.form.teacherId === undefined || this.form.teacherId === null) {
        callback(new Error('请选择讲师'))
      } else {
        callback()
      }
    }
    var validateExamPaper = (rule, value, callback) => {
      if (this.liveExamForm.liveExam.examPaperId.length === 0) {
        callback(new Error('请选择试卷'))
      } else {
        callback()
      }
    }
    return {
      activeTabName: 'liveInfo',
      categoryList: [],
      // 上传图片列表
      previewFileList: [],
      // 班级数据
      allOrg: [],
      // 选择用户Dialog
      chooseUserDialog: false,
      isSelectAdminUsers: false,
      selectUsers: [],
      selectAdminUsers: [],
      liveStartOptions: {
        disabledDate: (time) => {
          return new Date().getTime() - 8.64e7 > time.getTime()
        }
      },
      form: {
        title: '',
        liveThemeId: '',
        description: '',
        coverImage: '',
        startTime: '',
        endTime: '',
        timeLong: '',
        backType: 0,
        teacherId: '',
        teacherName: '',
        lecturer: '',
        userCount: 0,
        allowPlayBack: true,
        liveStreamUsers: [],
        adminUsers: [],
        passDuration: 0,
        closeDate: '',
        classHour: 0,
        incloudBackPlay: false
      },
      passDuration: 0,
      isEdit: this.$route.query.id,

      teacherList: [],
      teacherListLoading: false,
      chooseTeacherDialog: false,
      teacherListQuery: {
        OuId: null,
        GetChildren: true,
        Filter: '',
        Sorting: 'creationtime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      selectTeacher: null,
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 1, max: 300, message: '长度应小于 300 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入直播描述', trigger: 'blur' },
          { min: 1, max: 600, message: '长度应小于 600 个字符', trigger: 'blur' }
        ],
        teacherId: [{ required: true, validator: liveLecturerValid, trigger: 'blur' }],
        startTime: [{ required: true, validator: liveStartTimeValid, trigger: 'blur' }],
        endTime: [{ required: true, validator: liveEndTimeValid, trigger: 'blur' }],
        timeLong: [{ required: true, message: '请输入直播时长', trigger: 'blur' }],
        // userCount: [{ required: true, message: "请输入直播人数", trigger: 'blur' }],
        liveStreamUsers: [{ required: true, validator: liveUserValid, trigger: 'blur' }]
        // liveThemeId: [{ required: true, message: '请选择分类', trigger: 'change' }]
        // adminUsers: [{ required: true, validator: adminUserValid, trigger: 'blur' }]
      },

      chooseClassDialog: false,
      classLoading: false,
      selectedClass: [],
      // userLoading: false,
      // liveuserLoading: false,
      dialogLoading: false,
      isPublish: false,
      liveLoading: false,

      liveExamPaperList: [],
      saveLoading: false,
      liveExam: false,
      liveExamForm: {
        liveId: this.$route.query.id,
        passExamGetHour: false,
        canExamAfterLearn: false,
        liveExam: {
          liveId: this.$route.query.id,
          examName: '',
          examTimeLong: 5,
          examPaperId: '',
          passScore: 0
        }
      },
      liveExamFormRules: {
        examPaperId: [{ required: true, validator: validateExamPaper, trigger: 'blur' }],
        liveExam: {
          examName: [
            { required: true, message: '请输入考核名称', trigger: 'blur' },
            { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' }
          ]
        }
      },
      examPaperChooseDialog: false,
      currentPaperId: '',
      currentSelectPaper: null,

      selectUserDialog: false,
      selectUserDialogLoading: false,
      currentSelectUsers: [],
      selectUsersList: [],
      selectUsersListLoading: false,
      selectUsersListQuery: {
        Filter: '',
        LiveStreamId: this.$route.query.id,
        Sorting: 'userNumber',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      currentDeleteUsers: [],

      permissionLoading: false,
      progressValue: 0,
      progressTitle: '正在获取班级用户中，请等待...'
    }
  },
  created() {
    this.loadClassList()
    if (this.$route.query.id) {
      this.getLiveDetailInfo()
      this.getLiveUserList()
      this.getLiveExamDetail()
    }
    this.getCategoryList()
    // this.getUnCourseList()
  },
  methods: {
    liveUserList(args) {
      return liveUserList(args)
    },
    handleTabClick() {

    },
    handleSelectLiveUser(t) {
      this.currentSelectUsers = []
      this.isSelectAdminUsers = t

      this.selectUserDialog = true
    },
    handleSelectUserResponse(val) {
      this.currentSelectUsers = val
    },
    handleSelectUserSure() {
      if (this.currentSelectUsers.length) {
        this.selectUserDialogLoading = true
        var form = {
          liveId: this.$route.query.id,
          userRole: this.isSelectAdminUsers ? 2 : 0,
          liveStreamUsers: []
        }
        this.currentSelectUsers.forEach(item => {
          form.liveStreamUsers.push({
            id: item.id,
            userId: item.id,
            userName: item.userName,
            name: item.name,
            classId: item.extraProperties ? item.extraProperties.OUId : null,
            className: item.extraProperties ? item.extraProperties.OUName : ''
          })
        })
        addLiveUsers(form).then(res => {
          this.$message.success('添加成功')
          this.selectUserDialogLoading = false
          this.selectUserDialog = false
          this.getLiveUserList()
        }).catch(() => {
          this.selectUserDialogLoading = false
          this.$message.error('添加失败')
        })
      } else {
        this.$message.warning(this.isSelectAdminUsers ? '请选择助教' : '请选择学员')
      }
    },
    handleDeleteUserChange(val) {
      this.currentDeleteUsers = val
    },
    handleLiveUserDelete() {
      if (this.currentDeleteUsers.length) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var form = {
            liveId: this.$route.query.id,
            liveStreamUserIds: []
          }
          this.currentDeleteUsers.forEach(item => {
            form.liveStreamUserIds.push(item.id)
          })
          deleteLiveUsers(form).then(res => {
            this.$message.success('删除成功')
            this.currentDeleteUsers = []
            this.getLiveUserList()
          }).catch(() => {
            this.$message.error('删除失败')
          })
        }).catch(() => {
          this.$message.info('已取消删除')
        })
      } else {
        this.$message.warning('请选择学员')
      }
    },
    handleRefreshSelectUserList() {
      this.selectUsersListQuery.page = 1
      this.getLiveUserList()
    },
    handleTimeChange() {
      if (this.form.startTime && this.form.endTime) {
        this.form.timeLong = moment(this.form.endTime).diff(moment(this.form.startTime), 'minutes')
      }
    },

    handleLiveSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          this.liveLoading = true
          this.form.passDuration = this.passDuration * 60
          if (this.isEdit) {
            // this.form.liveType = 1
            editCourseLive(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.dialogLoading = false
              this.liveLoading = false
            }).catch(() => {
              this.$message.error('编辑失败')
              this.dialogLoading = false
              this.liveLoading = false
            })
          } else {
            addCourseLive(this.form).then(res => {
              this.$message.success('添加成功')
              this.dialogLoading = false
              this.liveLoading = false
              this.$router.go(-1)
            }).catch(() => {
              this.$message.error('添加失败')
              this.dialogLoading = false
              this.liveLoading = false
            })
          }
        } else {
          return false
        }
      })
    },

    handleSelectClass() {
      this.selectedClass = []
      this.chooseClassDialog = true
    },
    handleChooseClass(val) {
      this.selectedClass = val
    },
    async handleChooseClassSure() {
      this.classLoading = true
      var selectedUsers = []
      var loopTime = []
      var successPermission = 0
      this.permissionLoading = true
      this.progressTitle = '正在获取班级用户中，请等待...'
      for await (var classItem of this.selectedClass) {
        const maxCount = 500
        const totalRes = await classesUsers({ ClassId: classItem.id, SkipCount: 0, MaxResultCount: maxCount })
        successPermission++
        const times = totalRes.totalCount % maxCount === 0 ? totalRes.totalCount / maxCount : (Math.floor(totalRes.totalCount / maxCount) + 1)
        for (let i = 0; i < times; i++) {
          var data = {
            ClassId: classItem.id,
            Filter: '',
            Sorting: 'creationtime desc',
            SkipCount: i * maxCount,
            MaxResultCount: maxCount
          }
          this.progressTitle = '正在获取班级用户中，请等待...'
          const res = await classesUsers(data)
          this.progressValue = parseInt((((i / times) * (successPermission / this.selectedClass.length)) * 100).toFixed(0))
          res.items.forEach((sitem) => {
            selectedUsers.push({
              id: sitem.id,
              classId: sitem.extraProperties ? sitem.extraProperties.OUId : null,
              className: sitem.extraProperties ? sitem.extraProperties.OUName : '',
              userId: sitem.id,
              userName: sitem.userName,
              name: sitem.name
            })
          })
        }

        // const res = await classesUsers({ ClassId: classItem.id, IsAll: true })
        // res.items.forEach(item => {
        //   form.liveStreamUsers.push({
        //     id: item.id,
        //     userId: item.id,
        //     userName: item.userName,
        //     name: item.name,
        //     classId: item.extraProperties ? item.extraProperties.OUId : null,
        //     className: item.extraProperties ? item.extraProperties.OUName : ''
        //   })
        // })
      }
      //  var form = {
      //   liveId: this.$route.query.id,
      //   userRole: 0,
      //   liveStreamUsers: []
      // }
      var setA = new Set()
      selectedUsers = selectedUsers.filter(item => {
        const result = setA.has(item.userId)
        setA.add(item.userId)
        return !result
      })

      const count = 1000
      const len = selectedUsers.length % count === 0 ? selectedUsers.length / count : (Math.floor(selectedUsers.length / count) + 1)
      for (let i = 0; i < len; i++) {
        loopTime.push(i)
      }
      this.progressValue = 0
      successPermission = 0
      this.progressTitle = '正在导入中，请等待...'
      for await (var i of loopTime) {
        var data = {
          liveId: this.$route.query.id,
          userRole: 0,
          liveStreamUsers: []
        }
        data.liveStreamUsers = selectedUsers.slice(i * count, (i + 1) * count > selectedUsers.length ? selectedUsers.length : (i + 1) * count)
        await addLiveUsers(data).then(res => {
          successPermission++
          this.progressValue = parseInt((successPermission / loopTime.length * 100).toFixed(0))
        })
      }

      this.permissionLoading = false
      this.$message.success('添加成功')
      this.classLoading = false
      this.chooseClassDialog = false
      this.getLiveUserList()
      // addLiveUsers(form).then(res => {
      //   this.$message.success('添加成功')
      //   this.classLoading = false
      //   this.chooseClassDialog = false
      //   this.getLiveUserList()
      // }).catch(() => {
      //   this.classLoading = false
      //   this.$message.error('添加失败')
      // })
    },

    rowKey(row) {
      return row.id
    },
    handleSelectTeacher() {
      this.teacherListQuery.page = 1
      this.getTeachList()
      this.chooseTeacherDialog = true
    },
    handleTeacherSelectionChange(val) {
      this.selectTeacher = val[0]
      if (val.length > 1) {
        this.$refs.teacherTable.clearSelection()
        this.$refs.teacherTable.toggleRowSelection(val.pop())
      }
    },
    handleTeacherRowClick(row) {
      this.$refs.teacherTable.clearSelection()
      this.$refs.teacherTable.toggleRowSelection(row)
      this.selectTeacher = row
    },
    handleChooseTeachSure() {
      if (this.selectTeacher) {
        this.form.teacherId = this.selectTeacher.id
        this.form.teacherName = this.selectTeacher.name
        this.chooseTeacherDialog = false
      } else {
        this.$message.warning('请选择讲师')
      }
    },
    // 上传文件成功回调
    handleImageResponse(url, fileForm) {
      this.previewFileList = []
      this.previewFileList.push(fileForm)
      this.form.coverImage = url
    },
    // 上传文件删除
    handleRemoveUploadImage(index) {
      this.previewFileList = []
      this.form.coverImage = ''
    },

    handleChooseExamPaperClick() {
      this.examPaperChooseDialog = true
    },
    handleCourseSelectExamPaper(val) {
      this.currentSelectPaper = val
    },
    handleCourseSelectExamPaperSure() {
      this.liveExamPaperList = []
      if (this.currentSelectPaper) {
        this.liveExamForm.liveExam.examPaperId = this.currentSelectPaper.id
        this.currentPaperId = this.currentSelectPaper.id
        this.liveExamPaperList.push(this.currentSelectPaper)
      }
      this.examPaperChooseDialog = false
    },
    handleSaveLiveExam() {
      if (this.liveExam === false) {
        this.liveExamForm.liveExam = null
        this.liveExamPaperList = []
        this.currentPaperId = []
        this.currentSelectPaper = null
      }
      if (this.courseExam === true) {
        this.liveExamForm.liveExam.liveId = this.$route.query.id
      }
      this.$refs.liveExamForm.validate((valid) => {
        if (valid) {
          liveExamEdit(this.liveExamForm).then(res => {
            this.$message.success('保存成功')
            if (!this.liveExamForm.liveExam) {
              this.liveExamForm.liveExam = {
                liveId: this.$route.query.id,
                examName: '',
                examTimeLong: 5,
                examPaperId: '',
                passScore: 0
              }
            }
          })
        } else {
          return false
        }
      })
    },
    handleTeacherRefreshList() {
      this.teacherListQuery.page = 1
      this.getTeachList()
    },
    teacherSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getTeachList()
        return
      }
      this.teacherListQuery.Sorting = prop + ' ' + order
      this.getTeachList()
    },
    handleSelectUserSortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getLiveUserList()
        return
      }
      this.selectUsersListQuery.Sorting = prop + ' ' + order
      this.getLiveUserList()
    },
    getTeachList() {
      this.teacherListLoading = true
      this.teacherListQuery.SkipCount = (this.teacherListQuery.page - 1) * this.teacherListQuery.MaxResultCount
      getFilterUsers(this.teacherListQuery).then((response) => {
        this.teacherList = response.items
        if (this.isEdit) {
          var teacher = this.teacherList.filter(item => item.id === this.form.teacherId)
          if (teacher && teacher.length) {
            this.$nextTick(() => {
              // 对勾
              this.$refs.teacherTable.toggleRowSelection(teacher[0])
              // 高亮
              this.$refs.teacherTable.setCurrentRow(teacher[0])
            })
          }
        }
        this.teacherListQuery.totalCount = response.totalCount
        this.teacherListLoading = false
      })
    },
    async loadClassList() {
      // this.userLoading = true
      loadNodes().then(res => {
        this.allOrg = res.items
      })
      // this.userLoading = false
    },
    resetForm() {
      this.form = {
        title: '',
        liveThemeId: '',
        description: '',
        coverImage: '',
        startTime: '',
        endTime: '',
        timeLong: '',
        backType: 0,
        teacherId: '',
        teacherName: '',
        lecturer: '',
        userCount: 0,
        allowPlayBack: true,
        liveStreamUsers: [],
        adminUsers: [],
        passDuration: 0,
        closeDate: '',
        classHour: 0,
        incloudBackPlay: false
      }
    },
    async getLiveDetailInfo() {
      try {
        const res = await liveDetailInfo(this.$route.query.id)
        this.isPublish = res.publish
        this.form.id = res.id
        this.form.title = res.title
        this.form.description = res.description
        var backType = res.backType
        this.form.backType = backType
        this.form.coverImage = res.coverImage
        this.form.startTime = parseTimeDate(res.startTime)
        this.form.endTime = parseTimeDate(res.endTime)
        this.form.timeLong = res.timeLong
        this.form.lecturer = res.lecturer
        this.form.userCount = res.userCount
        this.form.teacherId = res.teacherId
        this.form.teacherName = res.teacherName
        this.form.allowPlayBack = res.allowPlayBack
        this.form.passDuration = res.passDuration
        this.form.closeDate = parseTimeDate(res.closeDate)
        this.form.classHour = res.classHour
        this.form.incloudBackPlay = res.incloudBackPlay
        this.form.liveThemeId = res.liveThemeId

        this.liveExamForm.canExamAfterLearn = res.canExamAfterLearn
        this.liveExamForm.passExamGetHour = res.passExamGetHour
        this.passDuration = res.passDuration / 60
        this.selectTeacher = {
          id: res.teacherId,
          name: res.teacherName
        }

        if (this.form.coverImage) {
          this.previewFileList.push({
            url: this.form.coverImage
          })
        }
      } catch {
        this.$message.warning('获取信息失败,请重试')
      }
    },
    getLiveUserList() {
      this.selectUsersListLoading = true
      this.selectUsersListQuery.SkipCount = (this.selectUsersListQuery.page - 1) * this.selectUsersListQuery.MaxResultCount
      liveUserList(this.selectUsersListQuery).then(res => {
        this.selectUsersList = res.items
        this.selectUsersListQuery.totalCount = res.totalCount
        this.selectUsersListLoading = false
      })
    },
    getLiveExamDetail() {
      var data = {
        Filter: '',
        LiveId: this.$route.query.id,
        Sorting: '',
        SkipCount: 0,
        MaxResultCount: 10
      }
      liveExamInfo(data).then(res => {
        if (res.items.length === 0) {
          this.liveExam = false
        } else {
          this.liveExam = true
          this.liveExamPaperList = res.items
          this.liveExamForm.liveExam.examName = res.items[0].examName
          this.liveExamForm.liveExam.examTimeLong = res.items[0].examTimeLong
          this.liveExamForm.liveExam.examPaperId = res.items[0].examPaperId
          this.currentPaperId = res.items[0].examPaperId
          this.liveExamForm.liveExam.passScore = res.items[0].passScore
          this.getPaperInfoDetail(res.items[0].examPaperId)
        }
      })
    },
    // 获取试卷详情
    getPaperInfoDetail(id) {
      this.liveExamPaperList = []
      examPaperDetailInfo(id).then(res => {
        this.liveExamPaperList.push(res)
      })
    },
    getCategoryList() {
      var data = {
        SkipCount: 0,
        MaxResultCount: 999
      }
      liveCategoryList(data).then(res => {
        this.categoryList = res.items
      }).then(res => {
      })
    }
  }
}

</script>

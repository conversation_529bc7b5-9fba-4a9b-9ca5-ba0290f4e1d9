<template>
  <div class="app-container">
    <el-card shadow="always" class="box-card el-card_header_border_0">
      <div slot="header">
        <el-cascader
          size="small"
          class="filter-item"
          :options="categoryList"
          :props="{ checkStrictly: true }"
          clearable
          style="width:200px"
          @change="handleSearch"
        />
        <el-input v-model="listQuery.Filter" size="small" class="filter-item" style="width: 200px;margin-left: 10px;" placeholder="输入名称搜索" />
        <el-button size="small" class="filter-item" round type="success" icon="el-icon-search" @click="handleSearch(0)">搜索</el-button>
        <el-button
          size="small"
          round
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd()"
        >添加</el-button>
      </div>

      <el-table v-loading="listLoading" :data="list" highlight-current-row @sort-change="sortChange">
        <el-table-column label="序号" prop="sort" sortable="sort" />
        <el-table-column label="名称" prop="name" sortable="name" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime">
          <template slot-scope="{ row }">
            <span>{{ row.creationTime | formatDatetime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button type="primary" round size="mini" icon="el-icon-edit" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" round size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog
      :title="isEdit == true ? '编辑' : '添加'"
      :close-on-click-modal="false"
      :visible.sync="dialog"
      width="600px"
    >
      <el-form
        ref="form1"
        :model="form"
        :rules="formRules"
        size=""
        label-width="100px"
      >
        <el-form-item label="题库分类" prop="questionBankCategoryIds">
          <el-cascader
            v-model="form.questionBankCategoryIds"
            :options="categoryList"
            :props="{ checkStrictly: true }"
            clearable
            style="width:100%"
          />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="序号" prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" round @click="handleSave()">确 定</el-button>
        <el-button round @click="dialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import apiQuestionBank from '@/api/questionBank'
import apiQuesBankCategory from '@/api/questionBankCategory'
import Pagination from '@/components/Pagination'

export default {
  name: 'QuestionBankTagType',
  components: {
    Pagination
  },
  data() {
    return {
      formRules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          },
          {
            min: 1,
            max: 30,
            message: '长度在 1 到 30 个字符',
            trigger: 'blur'
          }
        ],
        questionBankCategoryIds: [
          {
            required: true,
            message: '请选择分类',
            trigger: 'change'
          }]
      },
      list: [],
      listQuery: {
        totalCount: 0,
        page: 1,
        MaxResultCount: 10,
        Sorting: '',
        SkipCount: 0,
        Filter: '',
        QuestionBankCategoryId: null
      },
      categoryList: [],
      allCategory: [],
      listLoading: false,
      dialog: false,
      form: {
        id: '',
        name: '',
        sort: 1,
        questionBankCategoryId: null,
        questionBankCategoryIds: []
      },
      isEdit: false
    }
  },
  mounted() {
    this.getList()
    this.getCategorys()
  },
  methods: {
    handleSearch(val) {
      this.listQuery.page = 1
      if (val != 0) {
        this.listQuery.QuestionBankCategoryId = val[val.length - 1]
      }
      if (val.length == 0) {
        this.listQuery.QuestionBankCategoryId = null
      }
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount =
        (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      apiQuestionBank.tagTypes(this.listQuery).then(response => {
        this.list = response.items
        this.listQuery.totalCount = response.totalCount
        this.listLoading = false
      })
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    getCategorys() {
      apiQuesBankCategory.getCategorys().then(response => {
        this.loadOptions(response.items)
        this.allCategory = response.items
      })
    },
    loadOptions(items) {
      items.forEach(item => {
        if (item.parentId === null) {
          var element = {
            value: item.id,
            label: item.name
          }
          this.categoryList.push(element)
        }
      })
      this.setChildren(this.categoryList, items)
    },
    setChildren(roots, items) {
      roots.forEach(element => {
        items.forEach(item => {
          if (item.parentId === element.value) {
            if (!element.children) element.children = []
            element.children.push({
              value: item.id,
              label: item.name
            })
          }
        })
        if (element.children) {
          this.setChildren(element.children, items)
        }
      })
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        name: '',
        sort: 1,
        questionBankCategoryId: null,
        questionBankCategoryIds: []
      }
      this.dialog = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = row
      this.form.questionBankCategoryIds = this.getCategoryParents(row.questionBankCategoryId, [row.questionBankCategoryId])
      this.dialog = true
    },
    getCategoryParents(cid, arr) {
      var pid = null
      this.allCategory.forEach(item => {
        if (item.id == cid) {
          pid = item.parentId
          if (pid != null) arr.unshift(pid)
        }
      })
      if (pid != null) {
        this.getCategoryParents(pid, arr)
      }
      return arr
    },
    handleSave() {
      this.$refs.form1.validate(valid => {
        if (valid) {
          this.form.questionBankCategoryId = this.form.questionBankCategoryIds[this.form.questionBankCategoryIds.length - 1]
          if (!this.isEdit) {
            apiQuestionBank.tagTypeCreate(this.form).then(response => {
              this.$notify({
                title: '成功',
                message: '保存成功',
                type: 'success',
                duration: 2000
              })
              this.dialog = false
              this.getList()
            })
          } else {
            apiQuestionBank
              .tagTypeUpdate(this.form.id, this.form)
              .then(response => {
                this.$notify({
                  title: '成功',
                  message: '保存成功',
                  type: 'success',
                  duration: 2000
                })
                this.dialog = false
                this.getList()
              })
          }
        }
      })
    },
    handleDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        apiQuestionBank.tagTypeDelete(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    }
  }
}
</script>

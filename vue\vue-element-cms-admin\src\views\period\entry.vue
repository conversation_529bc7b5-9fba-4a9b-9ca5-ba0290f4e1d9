<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-radio-group v-model="listQuery.CreditHourType" size="small" @change="handleRefreshList">
          <el-radio-button :label="0">面授课程</el-radio-button>
          <el-radio-button :label="1">网络课程</el-radio-button>
        </el-radio-group>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="搜索..." />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button size="small" round type="primary" icon="el-icon-plus" @click="handleEdit(0, 0)">添加</el-button>
        <export-excel :header="['封面', '课程名称', '用户规模', '时长', '课时', '开始时间', '结束时间', '讲师', '类型']"
          :filter-val="['coverUrl', 'name', 'userCount', 'timeLong', 'classHour', 'startTime', 'endTime', 'lecturer', 'creditHourType']"
          :query="{ 'CreditHourType': listQuery.CreditHourType }" :field="{ 5: [1], 6: [1], 8: ['面授课程', '网络课程'] }"
          :api-fn="offLineCourseList" />
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
        <el-table-column label="封面" prop="coverUrl" width="160" sortable="coverUrl">
          <template slot-scope="{ row }">
            <el-image :src="row.coverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="name" sortable="name" show-overflow-tooltip />
        <el-table-column label="用户规模" prop="userCount" sortable="userCount" width="120" />
        <el-table-column label="时长" prop="timeLong" sortable="timeLong" width="120" />
        <el-table-column label="课时" prop="classHour" sortable="classHour" width="120" />
        <el-table-column label="开始时间" prop="startTime" sortable="startTime" width="160">
          <template slot-scope="{ row }">
            <span>{{ row.startTime | formatDatetime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endTime" sortable="endTime" width="160">
          <template slot-scope="{ row }">
            <span>{{ row.endTime | formatDatetime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="讲师" prop="lecturer" sortable="lecturer" width="120" />
        <el-table-column label="类型" prop="creditHourType" sortable="creditHourType" width="80">
          <template slot-scope="{ row }">
            <span v-if="row.creditHourType">网络课程</span>
            <span v-else>面授课程</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="{ row }">
            <el-button size="small" round type="primary" icon="el-icon-edit" @click="handleEdit(1, row)">编辑</el-button>
            <el-button size="small" round type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>

        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getList" />
    </el-card>
    <el-dialog v-if="courseDialog" :title="isEdit ? '添加' : '编辑'" :visible.sync="courseDialog" top="5vh" width="800px">
      <el-form ref="form" v-model="form">
        <el-form-item label="封面">
          <lz-upload-images ref="previewFile" :limit="1" :file-size="500" :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList" @response-fn="handleImageResponse"
            @remove-upload="handleRemoveUploadImage" />
        </el-form-item>
        <el-form-item label="课程名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="课程类型" prop="creditHourType">
          <el-radio-group v-model="form.creditHourType">
            <el-radio :label="0">面授课程</el-radio>
            <el-radio :label="1">网络课程</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker v-model="form.startTime" type="datetime" format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm" placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker v-model="form.endTime" type="datetime" format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm" placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item label="直播规模" prop="userCount">
          <el-input-number v-model="form.userCount" :step="1" step-strictly :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="时长" prop="timeLong">
          <el-input-number v-model="form.timeLong" :step="1" step-strictly :min="0">分钟</el-input-number>
        </el-form-item>
        <el-form-item label="课时" prop="classHour">
          <el-input-number v-model="form.userCoclassHourunt" :step="0.1" step-strictly :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="讲师" prop="lecturer">
          <el-input v-model="form.lecturer"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleSave">确 定</el-button>
        <el-button round @click="courseDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index.js'
import { deleteOffLineCourse, offLineCourseList } from '@/api/user'
export default {
  name: 'Period',
  directives: { permission },
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        CreditHourType: 0,
        Filter: '',
        TrainId: '',
        page: 1,
        totalCount: 0,
        SkipCount: 0,
        MaxResultCount: 10,
        Sorting: ''
      },

      courseDialog: false,
      previewFileList: [],
      form: {
        coverUrl: '',
        name: '',
        introduce: '',
        lecturer: '',
        classHour: '',
        startTime: '',
        endTime: '',
        timeLong: 0,
        userCount: 0,
        creditHourType: 0
      }

    }
  },
  created() {
    this.getList()
  },
  methods: {
    offLineCourseList(args) {
      return offLineCourseList(args)
    },

    handleEdit(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'PeriodEdit'
        })
      } else {
        this.$router.push({
          name: 'PeriodEdit',
          query: { id: row.id, name: row.name }
        })
      }
    },
    handleDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOffLineCourse(row.id).then(res => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = false
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      offLineCourseList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>

<template>
  <div>
    <el-input v-model="listQuery.Filter" clearable class="small_input" size="small" placeholder="输入名称搜索" />
    <el-button round size="small" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
    <el-button round size="small" type="primary" icon="el-icon-download" @click="handleExportUserAchive">成绩导出
    </el-button>
    <el-table
      v-loading="listLoading"
      :data="list"
      size="medium"
      style="width: 100%"
      highlight-current-row
      @sort-change="sortChange"
    >
      <el-table-column prop="userName" label="用户名" sortable="userName" width="120" />
      <el-table-column prop="name" label="姓名" sortable="name" width="140" />
      <el-table-column prop="className" label="部门" sortable="className" show-overflow-tooltip />

      <el-table-column prop="lastAnswerTimeLong" label="答题时长" width="120">
        <template slot-scope="{row}">
          {{ row.lastAnswerTimeLong | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column prop="quitCount" label="退出次数" sortable="quitCount" width="100">
        <template slot-scope="{row}">
          <span>{{ row.quitCount }} 次</span>
        </template>
      </el-table-column>
      <el-table-column prop="submitTimes" label="答题次数" sortable="submitTimes" width="100">
        <template slot-scope="{row}">
          {{ row.submitTimes }}
        </template>
      </el-table-column>
      <el-table-column prop="lastScore" label="成绩" sortable="lastScore" width="100">
        <template slot-scope="{row}">
          <span>{{ row.lastScore }} 分</span>
        </template>
      </el-table-column>
      <el-table-column prop="lastScore" sortable="lastScore" label="是否合格" width="120">
        <template slot-scope="{row}">
          <span>{{ (row.lastScore > passScore || row.lastScore === passScore) ? '合格' : '不合格' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isAnswered" label="考核状态" sortable="submitTimes" width="120">
        <template slot-scope="{row}">
          <el-tag v-if="row.submitTimes > 0" size="medium">已提交</el-tag>
          <el-tag v-else-if="row.isAnswered === 0" size="medium" type="info">未进入考试</el-tag>
          <el-tag v-else-if="row.isAnswered === 1 && row.submitTimes === 0" size="medium" type="success">正在考试</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getExamUserList"
    />
  </div>
</template>
<script>
import { studentExamResultList } from '@/api/train'
import Pagination from '@/components/Pagination'
import { formatSecond } from '@/utils'
export default {
  name: 'TrainExamList',
  components: {
    Pagination
  },
  props: {
    examinationId: {
      reuqerd: true,
      type: String,
      default: ''
    },
    trainExamName: {
      reuqerd: true,
      type: String,
      default: ''
    },
    passScore: {
      reuqerd: true,
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        ExaminationId: this.examinationId,
        Filter: '',
        Sorting: '',
        MaxResultCount: 10,
        SkipCount: 0,
        totalCount: 0,
        page: 1
      }
    }
  },
  created() {
    this.getExamUserList()
  },
  methods: {
    async handleExportUserAchive() {
      import('@/vendor/Export2Excel').then(async(excel) => {
        const tHeader = ['用户名', '姓名', '部门', '答题时长', '退出次数', '答题次数', '成绩', '是否合格']
        const filterVal = ['userName', 'name', 'className', 'lastAnswerTimeLong', 'quitCount', 'submitTimes', 'lastScore', 'examState']
        // 成绩列表数据
        const maxCount = 500
        var listForm = {
          ExaminationId: this.examinationId,
          Filter: '',
          Sorting: '',
          MaxResultCount: maxCount,
          SkipCount: 0
        }
        var list = []
        const res = await studentExamResultList(listForm)
        const times = res.totalCount % maxCount === 0 ? res.totalCount / maxCount : (Math.floor(res.totalCount / maxCount) + 1)
        var totalList = res.items
        for (let i = 1; i < times; i++) {
          listForm = {
            ExaminationId: this.examinationId,
            Sorting: '',
            SkipCount: i * maxCount,
            MaxResultCount: maxCount
          }
          const res2 = await studentExamResultList(listForm)
          totalList = totalList.concat(res2.items)
        }

        totalList.forEach(item => {
          list.push({
            ...item, lastAnswerTimeLong: formatSecond(item.lastAnswerTimeLong), examState: item.lastScore >= this.passScore ? '合格' : '不合格'
          })
        })

        const data = this.formatJson(filterVal, list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.trainExamName + '_答题详情_' + obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getExamUserList()
    },
    // 列表排序
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getExamUserList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getExamUserList()
    },
    // 根据考核id获取学生列表
    getExamUserList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      studentExamResultList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>

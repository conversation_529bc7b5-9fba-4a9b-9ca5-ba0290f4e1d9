<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="flex_between_box">
        <span>推荐课程管理——{{ title }}</span>
        <div>
          <el-button round v-permission="['CourseManagement.RecommendCourses.Create']" size="small" type="primary"
          icon="el-icon-plus" @click="handleRecommendCourseEdit(0, 0)">添加</el-button>
          <export-excel :header="['序号', '课程封面', '课程名称', '创建时间']"
              :filter-val="['sort', 'coverUrl', 'courseName', 'creationTime']"
              :query="{'RecommendCategoryId': $route.query.id}"
              :field="{ 3: [2]}" :api-fn="recommendCourseList" />
        </div>
      </div>
      <!-- <div class="header_flex_box">
        <el-select v-model="listQuery.RecommendCategoryId" >
        <el-option v-for="item in recommendCategoryList" :label="item.name" :value="item.id" :key="item.id">
        </el-option>
      </el-select>
        <el-input   placeholder="输入名称搜索" v-model="listQuery.Filter" />
        <el-button round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button round type="primary" icon="el-icon-plus" @click="handleRecommendCourseEdit(0,0)">添加</el-button>
      </div> -->
      <el-table :data="list" v-loading="listLoading" @sort-change="sortChange" highlight-current-row>
        <el-table-column label="序号" prop="sort" sortable="sort" width="100" />
        <el-table-column label="课程封面" prop="coverUrl" sortable="coverUrl" width="200">
          <template slot-scope="{ row }">
            <el-image :src="row.coverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" sortable="courseName" />
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200px">
          <template slot-scope="{row}">
            <el-button round v-permission="['CourseManagement.RecommendCourses.Update']" size="mini" type="primary"
              icon="el-icon-edit" @click="handleRecommendCourseEdit(1, row)">编辑
            </el-button>
            <el-button round v-permission="['CourseManagement.RecommendCourses.Delete']" size="mini" type="danger"
              icon="el-icon-delete" @click="handleRecommendCourseDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="listQuery.totalCount > 0" :total="listQuery.totalCount" :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount" @pagination="getRecommendCourseList" />
    </el-card>
    <el-dialog :title="dialogTitle" :close-on-click-modal="false" :visible.sync="recommendCourseDialog" width="450px">
      <el-form ref="form" :model="form" :rules="formRules" label-width="80px" label-position="right">
        <el-form-item label="课程中心" prop="courseCenterId">
          <el-button round type="primary" size="small" icon="el-icon-plus" @click="handleChooseCourseCenter">选择课程
          </el-button>
        </el-form-item>
        <el-form-item v-if="form.courseCenterId" label="课程名称">
          <span>{{ form.courseName }}</span>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort"></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" :loading="dialogLoading" @click="recommendCourseDialogSure">确 定</el-button>
        <el-button round @click="recommendCourseDialog = false">取 消</el-button>

      </span>
    </el-dialog>
    <el-dialog title="选择课程" :close-on-click-modal="false" :visible.sync="chooseCourseCenterDialog" width="1000px">
      <div class="header_flex_box">
        <el-cascader v-model="courseCenterListQuery.CourseCategoryId" filterable clearable :options="courseCategoryList"
          :props="{ 'label': 'name', 'value': 'id', 'children': 'children', 'checkStrictly': true, 'emitPath': false }"
          size="small" style="margin: 0 10px" placeholder="请选择课程分类..." @change="handleRefreshList" />
        <!-- <el-select size="small"  placeholder="选择课程分类"  v-model="courseCenterListQuery.CourseCategoryId" @change="handleRefreshList">
          <el-option label="全部" :value="''"></el-option>
          <el-option v-for="item in courseCategoryList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select> -->
        <!-- <el-select size="small"  placeholder="是否免费"  v-model="courseCenterListQuery.FreeModel" @change="handleRefreshList">
          <el-option label="免费" :value="0" />
          <el-option label="收费" :value="1" />
        </el-select> -->
        <el-input size="small" class="small_input" placeholder="输入名称搜索" v-model="courseCenterListQuery.Filter" />
        <el-button round size="small" type="primary" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
      </div>
      <el-table size="small" ref="courseCenterTable" v-loading="courseCenterListLoading" :data="courseCenterList"
        @selection-change="handleChooseCourseCenterChange" highlight-current-row>
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="课程封面" prop="coverUrl" width="200">
          <template slot-scope="{ row }">
            <el-image :src="row.coverUrl" class="course-cover" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="courseName" />
        <!-- <el-table-column label="免费/收费" prop="freeModel" width="120">
          <template slot-scope="{row}">
            <el-tag size="mini" v-if="row.freeModel === 0" type="success">免费</el-tag>
            <el-tag size="mini" v-if="row.freeModel === 1" type="warning">收费</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="创建时间" prop="creationTime" sortable="creationTime" width="160">
          <template slot-scope="{row}">
            {{ row.creationTime | formatDateTime }}
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="courseCenterListQuery.totalCount > 0" :total="courseCenterListQuery.totalCount"
        :page.sync="courseCenterListQuery.page" :limit.sync="courseCenterListQuery.MaxResultCount"
        @pagination="getCourseCenterList" />

      <span slot="footer" class="dialog-footer">
        <el-button round type="primary" @click="handleChooseCourseCenterSure">确 定</el-button>
        <el-button round @click="chooseCourseCenterDialog = false">取 消</el-button>

      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  recommendCourseList,
  unrecommendCourseList,
  recommendCourseDelete,
  recommendCourseAdd,
  recommendCourseEdit,
  recommendCategoryList,
  courseCategoryList
} from '@/api/course'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
export default {
  name: 'WebRecommendCourse',
  directives: {
    permission
  },
  components: {
    Pagination
  },
  data() {
    var checkCourseCenter = (rule, value, callback) => {
      if (this.form.courseCenterId === '') {
        callback(new Error('请选择课程中心'));
      } else {
        callback();
      }
    };
    return {
      title: this.$route.query.title,
      list: [],
      listLoading: false,
      listQuery: {
        RecommendCategoryId: this.$route.query.id,
        MaxResultCount: 10,
        SkipCount: 0,
        Sorting: 'creationTime desc',
        page: 1,
        totalCount: 0
      },
      // dialog
      dialogTitle: '热门课程添加',
      recommendCourseDialog: false,
      form: {
        recommendCategoryId: this.$route.query.id,
        courseCenterId: '',
        courseId: '',
        sort: 1,
        courseName: ''
      },
      formRules: {
        courseCenterId: [{ required: true, validator: checkCourseCenter, trigger: 'blur' }],
      },
      dialogLoading: false,

      // 热门分类列表
      recommendCategoryList: [],
      // 选择课程中心Dialog
      chooseCourseCenterDialog: false,
      // 课程中心列表
      courseCenterList: [],
      courseCenterListLoading: false,
      courseCenterListQuery: {
        RecommendCategoryId: this.$route.query.id,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      },
      selectedCourseCenter: [],

      // 编辑
      isEdit: false,
      editId: '',
      courseCategoryList: []
    }
  },
  created() {
    this.getRecommendCourseList()
    this.getRecommendCategoryList()
    this.getCourseCategoryList()
    // this.getCourseCenterList()
  },
  methods: {
    recommendCourseList(args) {
      return recommendCourseList(args)
    },
    // 编辑添加
    handleRecommendCourseEdit(t, row) {
      this.form = {
        recommendCategoryId: this.$route.query.id,
        courseCenterId: '',
        courseId: '',
        sort: 1,
        courseName: ''
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      if (t === 0) {
        this.dialogTitle = '热门课程添加'
        this.isEdit = false
      } else {
        this.dialogTitle = '热门课程编辑'
        this.isEdit = true
        this.editId = row.id
        this.form.recommendCategoryId = row.recommendCategoryId
        this.form.courseCenterId = row.courseCenterId
        this.form.courseId = row.courseId
        this.form.courseName = row.courseName
        this.form.sort = row.sort
      }
      this.recommendCourseDialog = true
    },
    // 确定提交
    recommendCourseDialogSure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.dialogLoading) {
            return
          }
          this.dialogLoading = true
          if (this.isEdit) {
            recommendCourseEdit(this.editId, this.form).then(res => {
              this.$message.success('编辑成功')
              this.getRecommendCourseList()
              this.recommendCourseDialog = false
              this.dialogLoading = false
            }).catch(() => {
              this.dialogLoading = false
            })
          } else {

            recommendCourseAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.recommendCourseDialog = false
              this.getRecommendCourseList()
              this.dialogLoading = false
            }).catch(() => {
              this.dialogLoading = false
            })
          }

        } else {
          return false;
        }
      });
    },
    handleRecommendCourseDelete(row) {
      this.$confirm('是否确认删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        recommendCourseDelete(row.id).then(res => {
          this.$message.success('删除成功')
          this.getRecommendCourseList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => { });
    },

    // 选择课程中心
    handleChooseCourseCenter() {
      this.getCourseCenterList()
      this.chooseCourseCenterDialog = true
    },
    // 选择课程中心变化
    handleChooseCourseCenterChange(val) {
      if (val.length > 1) {
        this.$refs.courseCenterTable.clearSelection()
        this.$nextTick(() => {
          this.$refs.courseCenterTable.toggleRowSelection(val.pop())
        });
      } else {
        this.selectedCourseCenter = val
      }
    },
    handleChooseCourseCenterSure() {
      if (this.selectedCourseCenter.length > 0) {
        this.form.courseId = this.selectedCourseCenter[0].courseId
        this.form.courseCenterId = this.selectedCourseCenter[0].courseCenterId
        this.form.courseName = this.selectedCourseCenter[0].courseName
        this.chooseCourseCenterDialog = false
      } else {
        this.$message.warning('请选择课程中心')
      }
    },
    handleRefreshList() {
      this.courseCenterListQuery.page = 1
      this.getCourseCenterList()
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data;
      if (!prop || !order) {
        this.getRecommendCourseList();
        return;
      }
      this.listQuery.Sorting = prop + " " + order;
      this.getRecommendCourseList();
    },
    getRecommendCourseList() {
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      this.listLoading = true
      recommendCourseList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      })
    },
    getRecommendCategoryList() {
      var form = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      recommendCategoryList(form).then(res => {
        this.recommendCategoryList = res.items
      })
    },
    getCourseCenterList() {
      this.courseCenterListQuery.SkipCount = (this.courseCenterListQuery.page - 1) * this.courseCenterListQuery.MaxResultCount
      this.courseCenterListLoading = true
      unrecommendCourseList(this.courseCenterListQuery).then(res => {
        this.courseCenterList = res.items
        this.courseCenterListQuery.totalCount = res.totalCount
        this.courseCenterListLoading = false
      }).catch(() => {
        this.$message.error('获取课程失败')
        this.courseCenterListLoading = false
      })
    },
    async getCourseCategoryList() {
      var data = {
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 999
      }
      const res = await courseCategoryList(data)
      this.courseCategoryList = this.deleteChildren(res.items.reduce((total, item, index, list) => {
        if (item.parentId === null) {
          total.push({
            ...item,
            children: list.filter((f) => f.parentId === item.id)
          })
        } else {
          item.children = list.filter((f) => f.parentId === item.id)
        }
        return total
      }, []))
    },
    deleteChildren(arr) {
      const childs = arr
      for (let i = childs.length; i--; i > 0) {
        if (childs[i].children) {
          if (childs[i].children.length) {
            this.deleteChildren(childs[i].children)
          } else {
            delete childs[i].children
          }
        }
      }
      return arr
    }

  }
}

</script>

<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-04 17:13:47
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-14 13:35:11
 * @FilePath: /vue-element-cms-admin/src/components/ExportExcel/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="display: inline-block">
    <el-button :loading="loading" :disabled="loading" size="small" style="margin: 0 10px" round type="primary"
      icon="el-icon-download" @click="initData">导出</el-button>
  </div>
</template>
<script>
import { formatDate, formatDateTime, formatDatetime, formatSecond } from '@/utils'
export default {
  name: 'ExportEcel',
  components: {
  },
  props: {
    header: {
      required: true,
      type: Array,
      default: function () {
        return []
      }
    },
    filterVal: {
      required: true,
      type: Array,
      default: function () {
        return []
      }
    },
    paging: {
      required: false,
      type: Boolean,
      default: true
    },
    apiFn: {
      required: true,
      type: Function,
      default: null
    },
    query: {
      required: false,
      type: Object,
      default: function () {
        return {}
      }
    },
    field: {
      required: false,
      type: Object,
      default: function () {
        return {}
      }
    }
    // data: {
    //   required: true,
    //   type: Object,
    //   default: function() {
    //     return {}
    //   }
    // }
  },
  data() {
    return {
      list: [],
      loading: false,
      fieldKeys: Object.keys(this.field)
    }
  },
  mounted() {
  },
  methods: {
    async initData() {
      this.list = []
      const count = 500
      this.loading = true
      console.log(this.query)
      if (this.paging) {
        const totalRes = await this.apiFn({ ...this.query, ...{ SkipCount: 0, MaxResultCount: 1 }})
        const times = totalRes.totalCount % count === 0 ? totalRes.totalCount / count : (Math.floor(totalRes.totalCount / count) + 1)
        console.log(totalRes.totalCount, times)
        for (let i = 0; i < times; i++) {
          var data = {
            SkipCount: i * count,
            MaxResultCount: count
          }
          // { ...this.query, ...data }
          const res = await this.apiFn({ ...this.query, ...data })
          this.list = this.list.concat(res.items)
        }
      } else {
        console.log(111111)
        const res = await this.apiFn({...this.query})
        this.list = res.items
      }
      console.log(this.list, this.paging)
      this.loading = false
      this.handleExportExcel()
    },
    handleExportExcel() {
      import('@/vendor/Export2Excel').then((excel) => {
        const data = this.formatJson(this.filterVal, this.list)
        var obj = new Date()
        excel.export_json_to_excel({
          header: this.header,
          data,
          filename: obj.toLocaleDateString(),
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    formatJson(filterVal, list) {
      return list.map((v) =>
        filterVal.map((j, index) => {
          const k = this.fieldKeys.indexOf(index + '')
          if (k !== -1) {
            if (this.field[index].length > 1) {
              return v[j] > 1 ? this.field[index][2] : (v[j] ? this.field[index][1] : this.field[index][0])
            } else {
              // 0 时间 年月日
              // 1 年月日 时分
              // 2 年月日 时分秒
              // 3 秒数转化 时间格式
              // 4 角色转化
              // 字符串 添加字符串
              console.log('我来了', this.field[index].length, parseInt(this.field[index][0]))
              if (parseInt(this.field[index][0]) || parseInt(this.field[index][0]) === 0) {
                switch (this.field[index][0]) {
                  case 0:
                    return formatDate(v[j])
                  case 1:
                    return formatDateTime(v[j])
                  case 2:
                    return formatDatetime(v[j])
                  case 3:
                    return formatSecond(v[j])
                  case 4:
                    return this.roleTransformation(v[j])
                  default:
                    break
                }
              } else {
                return v[j] + this.field[index][0]
              }
            }
          } else {
            const arr = j.split('.')
            let res = v
            arr.forEach(item => {
              if (res[item] || res[item] === 0) {
                res = res[item]
              } else {
                res = ''
              }
            })
            return res
            // return eval('v.' + j)
            // return v[j]
          }
        })
      )
    },
    roleTransformation(val) {
      if (Object.prototype.toString.call(val) === '[object String]') {
        var rolesList = val.split(',')
        var roleName = ''
        rolesList.forEach(item => {
          if (item === 'student') {
            roleName += '学生' + ','
          } else if (item === 'teacher') {
            roleName += '老师' + ','
          } else if (item === 'admin') {
            roleName += '管理员' + ','
          } else {
            roleName += item + ','
          }
        })
        return this.rtrim(roleName, ',')
      } else {
        return val
      }
    },
    rtrim(val, char, type) {
      if (char) {
        if (type === 'left') {
          return val.replace(new RegExp('^\\' + char + '+', 'g'), '')
        } else if (type === 'right') {
          return val.replace(new RegExp('\\' + char + '+$', 'g'), '')
        }
        return val.replace(new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'), '')
      }
      return val.replace(/^\s+|\s+$/g, '')
    }
  }
}
</script>

<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-05 15:08:19
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-20 08:53:09
 * @FilePath: /vue-element-cms-admin/src/views/exam-paper/paper-tags.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-card shadow="always" class="box-card el-card_header_border_0">
      <div slot="header">
        <el-input
          v-model="listQuery.Filter"
          clearable
          size="small"
          placeholder="搜索..."
          class="small_input"
          @keyup.enter.native="handleRefreshList"
        />
        <el-button round size="mini" type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button type="primary" round size="small" icon="el-icon-plus" @click="handleEdit(0, 0)">添加</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list" size="small" @sort-change="sortChange">
        <el-table-column prop="sort" sortable="sort" label="排序" width="100" />
        <el-table-column prop="coverImage" label="封面" width="200">
          <template slot-scope="{ row }">
            <!-- <img :src="row.url" width="160px;" height="90px"/>          -->
            <el-image :src="row.coverImage" style="width: 160px; height: 90px" fit="cover">
              <div slot="error">
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" sortable="title" label="标题" show-overflow-tooltip width="300" />
        <el-table-column prop="description" sortable="description" label="描述" show-overflow-tooltip />

        <el-table-column label="操作" width="200px">
          <template slot-scope="{row}">
            <el-button round type="primary" size="mini" icon="el-icon-edit" @click="handleEdit(1, row)">编辑</el-button>
            <el-button round type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
    <el-dialog :close-on-click-modal="false" :visible.sync="classDialog" :title="isEdit ? '编辑' : '添加'" width="520px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="封面图" prop="coverImage">
          <lz-upload-images
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="1" step-strictly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round type="" @click="classDialog = false">取消</el-button>
        <el-button round :loading="formLoading" type="primary" @click="handleSave">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
import LzUploadImages from '@/components/LzUploadImages'
import { liveCategoryAdd, liveCategoryDelete, liveCategoryEdit, liveCategoryList } from '@/api/live'
export default {
  name: 'LiveCategory',
  directives: {
    permission
  },
  components: {
    Pagination,
    LzUploadImages
  },
  data() {
    var coverImageValidate = (rule, value, callback) => {
      if (this.form.coverImage === '' || this.form.coverImage === null) {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      list: [],
      listLoading: false,
      listQuery: {
        MaxResultCount: 10,
        SkipCount: 0,
        Sorting: '',
        page: 1,
        totalCount: 0
      },
      classDialog: false,
      isEdit: false,
      form: {
        title: '',
        description: '',
        sort: 1,
        coverImage: ''
      },
      formLoading: false,
      rules: {
        coverImage: [{ required: true, validator: coverImageValidate, trigger: 'blur' }],
        title: [{ required: true, message: '请输入分类名称', trigger: 'blur' }, { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }]
      },

      previewFileList: []
    }
  },

  created() {
    this.getList()
  },
  methods: {
    liveCategoryList() {
      return liveCategoryList()
    },
    handleEdit(t, row) {
      this.previewFileList = []
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.form = {
        title: '',
        description: '',
        sort: 1,
        coverImage: ''
      }
      this.isEdit = !!t
      if (this.isEdit) {
        this.form.id = row.id
        this.form.title = row.title
        this.form.coverImage = row.coverImage
        this.form.sort = row.sort
        this.form.description = row.description
        if (this.form.coverImage) {
          this.previewFileList.push({
            url: this.form.coverImage
          })
        }
      }
      this.classDialog = true
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.isEdit) {
            console.log(this.form)
            liveCategoryEdit(this.form.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.classDialog = false
              this.formLoading = false
              this.getList()
            }).catch(() => {
              this.formLoading = false
              this.$message.error('编辑失败')
            })
          } else {
            liveCategoryAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.classDialog = false
              this.formLoading = false
              this.getList()
            }).catch(() => {
              this.$message.error('添加失败')
              this.formLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('是否删除 ' + row.title + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        liveCategoryDelete(row.id).then((response) => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    // 上传文件成功回调
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.coverImage = url
    },
    // 上传文件删除
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.coverImage = ''
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      liveCategoryList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).then(res => {
        this.listLoading = false
      })
    }
  }
}

</script>

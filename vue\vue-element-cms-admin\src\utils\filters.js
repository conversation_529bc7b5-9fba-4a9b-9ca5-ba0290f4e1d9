
import moment from 'moment'

export function formatDateTime(value) {
  if (value) {
    return moment(String(value)).format('YYYY-MM-DD HH:mm:ss')
  } else {
    return '--'
  }
}
export function formatDatetime(value) {
  if (value) {
    return moment(String(value)).format('YYYY-MM-DD HH:mm')
  } else {
    return '--'
  }
}

export function timeFormat(duration) {
  if (!duration) {
    return '00:00:00'
  }
  var h = Number.parseInt(duration / 3600)
  var m = Number.parseInt(duration % 3600 / 60)
  var s = Number.parseInt(duration % 3600 % 60)
  if (h < 10) {
    h = '0' + h
  } else if (h === 0) {
    h = '00'
  }
  if (m < 10) {
    m = '0' + m
  } else if (m === 0) {
    m = '00'
  }
  if (s < 10) {
    s = '0' + s
  } else if (s === 0) {
    s = '00'
  }
  return h + ':' + m + ':' + s
}
export function formatDate(value) {
  if (value) {
    return moment(String(value)).format('YYYY-MM-DD')
  }
}
// export function secondToMin(value) {
//   if (value < 60) {
//     return value + ' 秒'
//   } else {
//     var m = parseInt(value / 60)
//     var s = value % 60
//     return m + ' 分 ' + s + ' 秒'
//   }
// }

export function requestDate(dateTime, fmt) {
  var dateTime = new Date(dateTime)
  var o = {
    'M+': dateTime.getMonth() + 1, // 月份
    'd+': dateTime.getDate(), // 日
    'h+': dateTime.getHours(), // 小时
    'm+': dateTime.getMinutes(), // 分
    's+': dateTime.getSeconds(), // 秒
    'q+': Math.floor((dateTime.getMonth() + 3) / 3), // 季度
    'S': dateTime.getMilliseconds() // 毫秒
  }

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (dateTime.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (var k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
    }
  }
  return fmt
}
// 判断某个时间是否在时间范围内

export function timeRange(row) {
  if (!row.startDate && !row.endDate) {
    return '未知'
  }
  // 未开始
  if (moment(row.startDate).unix() > moment().unix()) {
    return '未开始'
  }
  // 已结束
  if (moment(row.endDate).unix() < moment().unix()) {
    return '已结束'
  }
  // 进行中
  if (moment(row.startDate).unix() < moment().unix() && moment(row.endDate).unix() > moment().unix()) {
    return '进行中'
  }
  return '未知'
}

export function formatSecond(value) {
  if (value) {
    // var day = Math.floor(value / (24 * 3600)) // Math.floor()向下取整
    // var hour = Math.floor((value - day * 24 * 3600) / 3600)
    // var minute = Math.floor((value - day * 24 * 3600 - hour * 3600) / 60)
    // var second = value - day * 24 * 3600 - hour * 3600 - minute * 60
    var day = Math.floor(value / (24 * 3600)) // Math.floor()向下取整
    var hour = Math.floor((value - day * 24 * 3600) / 3600)
    var minute = Math.floor((value - day * 24 * 3600 - hour * 3600) / 60)
    var second = value - day * 24 * 3600 - hour * 3600 - minute * 60

    if (second < 10) second = '0' + second
    if (minute < 10) minute = '0' + minute
    if (hour < 10) hour = '0' + hour

    return hour + ':' + minute + ':' + second
  }
  return '--'
}

export function formatFileSize(value) {
  if (value) {
    return parseFloat(value / 1024 / 1024).toFixed(2) + 'MB'
  }
}

export function secondToMin(value) {
  if (value) {
    var day = Math.floor(value / (24 * 3600)) // Math.floor()向下取整
    var hour = Math.floor((value - day * 24 * 3600) / 3600)
    var minute = Math.floor((value - day * 24 * 3600 - hour * 3600) / 60)
    var second = Math.floor(value - day * 24 * 3600 - hour * 3600 - minute * 60)

    if (second < 10) second = '0' + second
    if (minute < 10) minute = '0' + minute
    if (hour < 10) hour = '0' + hour

    return hour + ':' + minute + ':' + second
  }
  return '--'
}

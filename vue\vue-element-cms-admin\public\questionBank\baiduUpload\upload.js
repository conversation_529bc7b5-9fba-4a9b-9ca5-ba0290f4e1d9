var ak = ''
var sk = ''
var sessionToken = ''
var md5String = ''
BaiduUpload = function () {
 // var _config;
  //初始化应用百度云上传控件
  var InitUploadCommon = function (config) {
    //_config=config;
    getStsToken();
    $("#"+config.browse_button).change(function (e) {

      var blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,
          file = this.files[0],
          chunkSize = 2097152,                             // Read in chunks of 2MB
          chunks = Math.ceil(file.size / chunkSize),
          currentChunk = 0,
          spark = new SparkMD5.ArrayBuffer(),
          fileReader = new FileReader();

      fileReader.onload = function (e) {

          spark.append(e.target.result);                   // Append array buffer
          currentChunk++;

          if (currentChunk < chunks) {
              loadNext();
          } else {
              var hash = spark.end()
              md5String = hash
              var pathForm = {
                  extend: '.' + file.name.slice(file.name.lastIndexOf('.') + 1),
                  hash: hash,
                  isPublic: true
              }
              getUploadPath(pathForm, file,config)
          }
      };

      fileReader.onerror = function () {
          console.warn('oops, something went wrong.');
      };

      function loadNext() {
          var start = currentChunk * chunkSize,
              end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;

          fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
      }

      loadNext();
  })

  function getUploadPath(pathform, file,_config) {
    xhrGet('/api/cloud-resource/bce/get-upload-path', pathform, function (res) {
            var bucket = res.slice(0, res.indexOf('/'))
            var key = res.slice(res.indexOf('/') + 1)            
            uploadFile(file, md5String, bucket, key,_config)
    }, function (res) {
    })
  
}
function getStsToken() {
    if(sessionToken.length==0)
    {
        xhrGet('/api/cloud-resource/bce/sts-token', null, function (res) {
            ak = res.accessKeyId
            sk = res.secretAccessKey
            sessionToken = res.sessionToken
        }, function (res) {
        })
    }
}
function uploadFile(file, md5String, bucket, key,_config) {
    var config = {
        credentials: {
            ak: ak, // 您的AK
            sk: sk // 您的SK
        },
        sessionToken: sessionToken,
        // endpoint: 'https://bj.bcebos.com' // 传入Bucket所在区域域名
        endpoint: 'http://bj.bcebos.com' // 传入Bucket所在区域域名
    }
    var renameFile = new File([file], md5String + '.' + file.name.slice(file.name.lastIndexOf('.') + 1), {
        type: file.type
    })
    const url = config.endpoint + '/' + bucket + '/' + key
    const client = new baidubce.sdk.BosClient(config)
    var reader = new FileReader()
    reader.readAsArrayBuffer(renameFile)
    let blob = null
    const ele = this
    var options = {
        'Content-Disposition': 'attachment;filename="' + encodeURIComponent(file.name) + '"' // 指示回复的内容该以何种形式展示
    }
    reader.onload = function (e) {
        if (typeof e.target.result === 'object') {
            blob = new Blob([e.target.result])
        } else {
            blob = e.target.result
        }

        client
            .putObjectFromBlob(bucket, key, blob, options)
            .then(response => {
                $('#' + _config.result + " .imgDiv1").remove();

                if (_config.result == "result_img") {
                    var data = {
                        type: 5
                    }
                    $('.ui-questions-content-list').append(template("drag_choice", data));
                }

                var html = '';
                html += '<div class="box" style="margin-bottom:0px">';
                html += '<img src=' + url + ' class="img-thumbnail img-rounded img-responsive"  style="width: 160px; height: 100px;" />';
                html += ' <div class="box-content">';
                html += ' <ul class="icon">';
                html += ' <li><a href="javascript:;" class="viewPic"><img src="/questionBank/editexec/img/exc_see.png" class="icn_img" data-tisp="查看大图" /></a></li>';
                //html += ' <li><a href="javascript:;" class="delPic_img"><img src="/Common/editexec/img/exc_delpic.png" class="icn_img" data-tisp="删除" /></a></li>';
                html += ' </ul>';
                html += ' </div>';
                html += ' </div>';

                var html1 = '';
                html1 += '<div class="col-md-3 box" style="margin-bottom:0px">';
                html1 += '<img src=' + url + ' class="img-thumbnail img-rounded img-responsive"  style="width: 160px; height: 100px;" />';
                html1 += ' <div class="box-content">';
                html1 += ' <ul class="icon">';
                html1 += ' <li><a href="javascript:;" class="viewPic"><img src="/questionBank/editexec/img/exc_see.png" class="icn_img" data-tisp="查看大图" /></a></li>';
                html1 += ' <li><a href="javascript:;" class="delPic_img"><img src="/questionBank/editexec/img/exc_delpic.png" class="icn_img" data-tisp="删除" /></a></li>';
                html1 += ' </ul>';
                html1 += ' </div>';
                html1 += ' </div>';

                var resHtml = '';
                resHtml += '<div class="col-md-3">';
                resHtml += '<img src=' + url + ' class="img-thumbnail img-rounded img-responsive"  style="width: 160px; height: 100px;" />';
                resHtml += ' </div>';

                if (_config.result == "result_img") {
                    $('.imgadds').each(function () {
                        if ($(this).html().indexOf('img') < 0) {
                            $(this).append(html);
                        }
                    })
                }
                else {
                    $('#' + _config.result).append(resHtml);
                }
                //同名文件取消可以再次上传
                $('#' + _config.browse_button).val("");

                $('.ui-img-list[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').append(html1);
                var $itemInput = $('.cq-answer-content[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li').find('.input-check').find('input');
                var $imageInput = $('.cq-answer-content[data-tid=' + $('.cq-into-edit').attr('data-gettid') + ']').closest('li').find('.ui-img-option-list');
                if ($itemInput.size()) {
                    $imageInput.append(html1);
                }


            })
            .catch(fail => {
                
            })
    }
}

  }
  //初始化组件
  var InitCommonFun = function (config) { }
  InitCommonFun.prototype = {
      init: function (config) {
          this.InitUpload = new InitUploadCommon(config);
      },
  };
  return InitCommonFun;
}();
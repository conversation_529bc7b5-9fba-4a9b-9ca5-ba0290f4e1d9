/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.0 (2019-10-17)
 */
!function(d){"use strict";var x=function(){return(x=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function u(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(t[r[o]]=n[r[o]])}return t}function w(){}function y(n){return n}var i=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},v=function(n){return function(){return n}};function l(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}function m(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,n)}}function o(n){return function(){throw new Error(n)}}function t(n){return n()}function n(){return f}var e,c=v(!1),a=v(!0),f=(e={fold:function(n,e){return n()},is:c,isSome:c,isNone:a,getOr:g,getOrThunk:s,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:v(null),getOrUndefined:v(undefined),or:g,orThunk:s,map:n,each:w,bind:n,exists:c,forall:a,filter:n,equals:r,equals_:r,toArray:function(){return[]},toString:v("none()")},Object.freeze&&Object.freeze(e),e);function r(n){return n.isNone()}function s(n){return n()}function g(n){return n}function S(n,t){return jn(n,function(n,e){return{k:e,v:t(n,e)}})}function p(n,e){return Vn.call(n,e)}function h(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};function r(n){return Number(e.replace(t,"$"+n))}return ee(r(1),r(2))}function b(n,e){return function(){return e===n}}function T(n,e){return function(){return e===n}}function O(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e}(n)===e}}function k(n,e){return-1<function(n,e){return Te.call(n,e)}(n,e)}function E(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1}function C(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}}function D(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t}function M(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t}function I(n,e,t){return C(n,function(n){t=e(t,n)}),t}function R(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t))return An.some(o)}return An.none()}function F(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return An.some(t)}return An.none()}function A(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!ye(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);Oe.apply(e,n[t])}return e}function B(n,e){var t=ke(n,e);return A(t)}function V(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0}function N(n){var e=Se.call(n,0);return e.reverse(),e}function j(n,e){return D(n,function(n){return!k(e,n)})}function _(n){return[n]}function P(n,e){var t=String(e).toLowerCase();return R(n,function(n){return n.search(t)})}function H(n,e){return-1!==n.indexOf(e)}function z(e){return function(n){return H(n,e)}}function L(){return Fe.get()}function G(n,e){Ye(n,n.element(),e,{})}function U(n,e,t){Ye(n,n.element(),e,t)}function $(n){G(n,_e())}function W(n,e,t){Ye(n,e,t,{})}function X(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}}function q(n){return n.dom().nodeName.toLowerCase()}function Y(e){return function(n){return function(n){return n.dom().nodeType}(n)===e}}function K(n){var e=tt(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)}function J(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return C(e,function(n,e){r[n]=v(t[e])}),r}}function Q(n){return n.slice(0).sort()}function Z(e,n){if(!ye(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");C(n,function(n){if(!he(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}function nn(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Z("required",o),Z("optional",i),function(n){var t=Q(n);R(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}(u),function(e){var t=Bn(e);V(o,function(n){return k(t,n)})||function(n,e){throw new Error("All required keys ("+Q(n).join(", ")+") were not specified. Specified keys were: "+Q(e).join(", ")+".")}(o,t);var n=D(t,function(n){return!k(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+Q(n).join(", "))}(n);var r={};return C(o,function(n){r[n]=v(e[n])}),C(i,function(n){r[n]=v(Object.prototype.hasOwnProperty.call(e,n)?An.some(e[n]):An.none())}),r}}function en(n,e,t){return 0!=(n.compareDocumentPosition(e)&t)}function tn(n,e){var t=n.dom();if(t.nodeType!==ut)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function rn(n){return n.nodeType!==ut&&n.nodeType!==ct||0===n.childElementCount}function on(n,e){var t=e===undefined?d.document:e.dom();return rn(t)?[]:ke(t.querySelectorAll(n),Je.fromDom)}function un(n,e){var t=e===undefined?d.document:e.dom();return rn(t)?An.none():An.from(t.querySelector(n)).map(Je.fromDom)}function cn(n,e){return n.dom()===e.dom()}function an(n){return Je.fromDom(n.dom().ownerDocument)}function fn(n){return An.from(n.dom().parentNode).map(Je.fromDom)}function sn(n,e){var t=n.dom().childNodes;return An.from(t[e]).map(Je.fromDom)}function ln(e,t){fn(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}function dn(n,e){(function(n){return An.from(n.dom().nextSibling).map(Je.fromDom)})(n).fold(function(){fn(n).each(function(n){ft(n,e)})},function(n){ln(n,e)})}function mn(e,t){(function(n){return sn(n,0)})(e).fold(function(){ft(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})}function gn(e,n){C(n,function(n){ft(e,n)})}function pn(n){n.dom().textContent="",C(at(n),function(n){st(n)})}function hn(n,e){ft(n.element(),e.element())}function vn(e,n){var t=e.components();!function(n){C(n.components(),function(n){return st(n.element())}),pn(n.element()),n.syncComponents()}(e);var r=j(t,n);C(r,function(n){lt(n),e.getSystem().removeFromWorld(n)}),C(n,function(n){n.getSystem().isConnected()?hn(e,n):(e.getSystem().addToWorld(n),hn(e,n),K(e.element())&&dt(n)),e.syncComponents()})}function yn(e){var n=fn(e.element()).bind(function(n){return e.getSystem().getByDom(n).toOption()});!function(n){lt(n),st(n.element()),n.getSystem().removeFromWorld(n)}(e),n.each(function(n){n.syncComponents()})}function bn(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)bt.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}}function xn(n){return St.defaultedThunk(v(n))}function wn(e){return function(n){return p(n,e)?An.from(n[e]):An.none()}}function Sn(n,e){return wn(e)(n)}function Tn(n,e){var t={};return t[n]=e,t}function On(n,e){return function(n,t){var r={};return Nn(n,function(n,e){k(t,e)||(r[e]=n)}),r}(n,e)}function kn(n,e){return function(e,t){return function(n){return p(n,e)?n[e]:t}}(n,e)}function En(n,e){return Tn(n,e)}function Cn(n){return function(n){var e={};return C(n,function(n){e[n.key]=n.value}),e}(n)}function Dn(n,e){var t=function(n){var e=[],t=[];return C(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}(n);return 0<t.errors.length?function(n){return vt.error(A(n))}(t.errors):function(n,e){return 0===n.length?vt.value(e):vt.value(xt(e,wt.apply(undefined,n)))}(t.values,e)}function Mn(n,e){return function(n,e){return p(n,e)&&n[e]!==undefined&&null!==n[e]}(n,e)}var In,Rn,Fn=function(t){function n(){return o}function e(n){return n(t)}var r=v(t),o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:a,isNone:c,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:n,orThunk:n,map:function(n){return Fn(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?o:f},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(c,function(n){return e(t,n)})}};return o},An={some:Fn,none:n,from:function(n){return null===n||n===undefined?f:Fn(n)}},Bn=Object.keys,Vn=Object.hasOwnProperty,Nn=function(n,e){for(var t=Bn(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},jn=function(n,r){var o={};return Nn(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},_n=function(n,t){var r=[];return Nn(n,function(n,e){r.push(t(n,e))}),r},Pn=v("touchstart"),Hn=v("touchmove"),zn=v("touchend"),Ln=v("mousedown"),Gn=v("mousemove"),Un=v("mouseup"),$n=v("mouseover"),Wn=v("keydown"),Xn=v("keyup"),qn=v("input"),Yn=v("change"),Kn=v("click"),Jn=v("transitionend"),Qn=v("selectstart"),Zn=function(n){function e(){return t}var t=n;return{get:e,set:function(n){t=n},clone:function(){return Zn(e())}}},ne=function(){return ee(0,0)},ee=function(n,e){return{major:n,minor:e}},te={nu:ee,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?ne():h(n,t)},unknown:ne},re="Edge",oe="Chrome",ie="Opera",ue="Firefox",ce="Safari",ae=function(n){var e=n.current;return{current:e,version:n.version,isEdge:b(re,e),isChrome:b(oe,e),isIE:b("IE",e),isOpera:b(ie,e),isFirefox:b(ue,e),isSafari:b(ce,e)}},fe={unknown:function(){return ae({current:undefined,version:te.unknown()})},nu:ae,edge:v(re),chrome:v(oe),ie:v("IE"),opera:v(ie),firefox:v(ue),safari:v(ce)},se="Windows",le="Android",de="Solaris",me="FreeBSD",ge=function(n){var e=n.current;return{current:e,version:n.version,isWindows:T(se,e),isiOS:T("iOS",e),isAndroid:T(le,e),isOSX:T("OSX",e),isLinux:T("Linux",e),isSolaris:T(de,e),isFreeBSD:T(me,e)}},pe={unknown:function(){return ge({current:undefined,version:te.unknown()})},nu:ge,windows:v(se),ios:v("iOS"),android:v(le),linux:v("Linux"),osx:v("OSX"),solaris:v(de),freebsd:v(me)},he=O("string"),ve=O("object"),ye=O("array"),be=O("boolean"),xe=O("function"),we=O("number"),Se=Array.prototype.slice,Te=Array.prototype.indexOf,Oe=Array.prototype.push,ke=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r},Ee=(xe(Array.from)&&Array.from,function(n,t){return P(n,t).map(function(n){var e=te.detect(n.versionRegexes,t);return{current:n.name,version:e}})}),Ce=function(n,t){return P(n,t).map(function(n){var e=te.detect(n.versionRegexes,t);return{current:n.name,version:e}})},De=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Me=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return H(n,"edge/")&&H(n,"chrome")&&H(n,"safari")&&H(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,De],search:function(n){return H(n,"chrome")&&!H(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return H(n,"msie")||H(n,"trident")}},{name:"Opera",versionRegexes:[De,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:z("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:z("firefox")},{name:"Safari",versionRegexes:[De,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(H(n,"safari")||H(n,"mobile/"))&&H(n,"applewebkit")}}],Ie=[{name:"Windows",search:z("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return H(n,"iphone")||H(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:z("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:z("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:z("linux"),versionRegexes:[]},{name:"Solaris",search:z("sunos"),versionRegexes:[]},{name:"FreeBSD",search:z("freebsd"),versionRegexes:[]}],Re={browsers:v(Me),oses:v(Ie)},Fe=Zn(function(n,e){var t=Re.browsers(),r=Re.oses(),o=Ee(t,n).fold(fe.unknown,fe.nu),i=Ce(r,n).fold(pe.unknown,pe.nu);return{browser:o,os:i,deviceType:function(n,e,t,r){var o=n.isiOS()&&!0===/ipad/i.test(t),i=n.isiOS()&&!o,u=n.isiOS()||n.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),f=i||u&&!a,s=e.isSafari()&&n.isiOS()&&!1===/safari/i.test(t),l=!f&&!a&&!s;return{isiPad:v(o),isiPhone:v(i),isTablet:v(a),isPhone:v(f),isTouch:v(c),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:v(s),isDesktop:v(l)}}(i,o,n,e)}}(d.navigator.userAgent,function(n){return d.window.matchMedia(n).matches})),Ae={tap:v("alloy.tap")},Be=v("alloy.focus"),Ve=v("alloy.blur.post"),Ne=v("alloy.paste.post"),je=v("alloy.receive"),_e=v("alloy.execute"),Pe=v("alloy.focus.item"),He=Ae.tap,ze=L().deviceType.isTouch()?Ae.tap:Kn,Le=v("alloy.longpress"),Ge=v("alloy.system.init"),Ue=v("alloy.system.attached"),$e=v("alloy.system.detached"),We=v("alloy.focusmanager.shifted"),Xe=v("alloy.highlight"),qe=v("alloy.dehighlight"),Ye=function(n,e,t,r){var o=x({target:e},r);n.getSystem().triggerEvent(t,e,S(o,v))},Ke=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:v(n)}},Je={fromHtml:function(n,e){var t=(e||d.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw d.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Ke(t.childNodes[0])},fromTag:function(n,e){var t=(e||d.document).createElement(n);return Ke(t)},fromText:function(n,e){var t=(e||d.document).createTextNode(n);return Ke(t)},fromDom:Ke,fromPoint:function(n,e,t){var r=n.dom();return An.from(r.elementFromPoint(e,t)).map(Ke)}},Qe=(d.Node.ATTRIBUTE_NODE,d.Node.CDATA_SECTION_NODE,d.Node.COMMENT_NODE,d.Node.DOCUMENT_NODE),Ze=(d.Node.DOCUMENT_TYPE_NODE,d.Node.DOCUMENT_FRAGMENT_NODE,d.Node.ELEMENT_NODE),nt=d.Node.TEXT_NODE,et=(d.Node.PROCESSING_INSTRUCTION_NODE,d.Node.ENTITY_REFERENCE_NODE,d.Node.ENTITY_NODE,d.Node.NOTATION_NODE,"undefined"!=typeof d.window?d.window:Function("return this;")(),Y(Ze)),tt=Y(nt),rt=X(function(){return ot(Je.fromDom(d.document))}),ot=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Je.fromDom(e)},it=function(n,e){return en(n,e,d.Node.DOCUMENT_POSITION_CONTAINED_BY)},ut=Ze,ct=Qe,at=(L().browser.isIE(),function(n){return ke(n.dom().childNodes,Je.fromDom)}),ft=(J("element","offset"),function(n,e){n.dom().appendChild(e.dom())}),st=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},lt=function(n){G(n,$e());var e=n.components();C(e,lt)},dt=function(n){var e=n.components();C(e,dt),G(n,Ue())},mt=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),K(n.element())&&dt(e),n.syncComponents()},gt=function(n,e,t){t(n,e.element());var r=at(e.element());C(r,function(n){e.getByDom(n).each(dt)})},pt=function(t){return{is:function(n){return t===n},isValue:a,isError:c,getOr:v(t),getOrThunk:v(t),getOrDie:v(t),or:function(n){return pt(t)},orThunk:function(n){return pt(t)},fold:function(n,e){return e(t)},map:function(n){return pt(n(t))},mapError:function(n){return pt(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return An.some(t)}}},ht=function(t){return{is:c,isValue:c,isError:a,getOr:y,getOrThunk:function(n){return n()},getOrDie:function(){return o(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return ht(t)},mapError:function(n){return ht(n(t))},each:w,bind:function(n){return ht(t)},exists:c,forall:a,toOption:An.none}},vt={value:pt,error:ht,fromOption:function(n,e){return n.fold(function(){return ht(e)},pt)}},yt=function(u){if(!ye(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return C(u,function(n,r){var e=Bn(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!ye(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=Bn(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!V(c,function(n){return k(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){d.console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},bt=Object.prototype.hasOwnProperty,xt=bn(function(n,e){return ve(n)&&ve(e)?xt(n,e):e}),wt=bn(function(n,e){return e}),St=yt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Tt=St.strict,Ot=St.asOption,kt=St.defaultedThunk,Et=St.mergeWithThunk,Ct=(yt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return wn(n)}),Dt=function(n,e){return Sn(n,e)};(Rn=In=In||{})[Rn.Error=0]="Error",Rn[Rn.Value=1]="Value";function Mt(n,e,t){return n.stype===In.Error?e(n.serror):t(n.svalue)}function It(n){return{stype:In.Value,svalue:n}}function Rt(n){return{stype:In.Error,serror:n}}function Ft(n){return i(gr,A)(n)}function At(n){return ve(n)&&100<Bn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Bt(n,e){return gr([{path:n,getErrorInfo:e}])}function Vt(n,e,t){return Sn(e,t).fold(function(){return function(n,e,t){return Bt(n,function(){return'Could not find valid *strict* value for "'+e+'" in '+At(t)})}(n,t,e)},dr)}function Nt(n,e,t){var r=Sn(n,e).fold(function(){return t(n)},y);return dr(r)}function jt(u,c,n,a){return n.fold(function(r,t,n,o){function i(n){var e=o.extract(u.concat([r]),a,n);return vr(e,function(n){return Tn(t,a(n))})}function e(n){return n.fold(function(){var n=Tn(t,a(An.none()));return dr(n)},function(n){var e=o.extract(u.concat([r]),a,n);return vr(e,function(n){return Tn(t,a(An.some(n)))})})}return n.fold(function(){return pr(Vt(u,c,r),i)},function(n){return pr(Nt(c,r,n),i)},function(){return pr(function(n,e){return dr(Sn(n,e))}(c,r),e)},function(n){return pr(function(e,n,t){var r=Sn(e,n).map(function(n){return!0===n?t(e):n});return dr(r)}(c,r,n),e)},function(n){var e=n(c),t=vr(Nt(c,r,v({})),function(n){return xt(e,n)});return pr(t,i)})},function(n,e){var t=e(c);return dr(Tn(n,a(t)))})}function _t(r){return{extract:function(e,n,t){return hr(r(t,n),function(n){return function(n,e){return Bt(n,function(){return e})}(e,n)})},toString:function(){return"val"},toDsl:function(){return wr.itemOf(r)}}}function Pt(n){var i=Or(n),u=M(n,function(e,n){return n.fold(function(n){return xt(e,En(n,!0))},v(e))},{});return{extract:function(n,e,t){var r=be(t)?[]:function(e){var n=Bn(e);return D(n,function(n){return Mn(e,n)})}(t),o=D(r,function(n){return!Mn(u,n)});return 0===o.length?i.extract(n,e,t):function(n,e){return Bt(n,function(){return"There are unsupported fields: ["+e.join(", ")+"] specified"})}(n,o)},toString:i.toString,toDsl:i.toDsl}}function Ht(t,i){function u(n,e){return function(o){return{extract:function(t,r,n){var e=ke(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return xr(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return wr.arrOf(o)}}}(_t(t)).extract(n,y,e)}return{extract:function(t,r,o){var n=Bn(o),e=u(t,n);return pr(e,function(n){var e=ke(n,function(n){return Tr.field(n,n,Tt(),i)});return Or(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return wr.setOf(t,i)}}}function zt(e,t,r,n,o){return Dt(n,o).fold(function(){return function(n,e,t){return Bt(n,function(){return'The chosen schema: "'+t+'" did not exist in branches: '+At(e)})}(e,n,o)},function(n){return n.extract(e.concat(["branch: "+o]),t,r)})}function Lt(n,o){return{extract:function(e,t,r){return Dt(r,n).fold(function(){return function(n,e){return Bt(n,function(){return'Choice schema did not contain choice key: "'+e+'"'})}(e,n)},function(n){return zt(e,t,r,o,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+Bn(o)},toDsl:function(){return wr.choiceOf(n,o)}}}function Gt(e){return _t(function(n){return e(n).fold(gr,dr)})}function Ut(e,n){return Ht(function(n){return sr(e(n))},n)}function $t(n,e,t){return lr(function(n,e,t,r){var o=e.extract([n],t,r);return yr(o,function(n){return{input:r,errors:n}})}(n,e,y,t))}function Wt(n){return n.fold(function(n){throw new Error(Mr(n))},y)}function Xt(n,e,t){return Wt($t(n,e,t))}function qt(n,e){return Lt(n,S(e,Or))}function Yt(n){return Cr(n,n,Tt(),kr())}function Kt(n,e){return Cr(n,n,Tt(),e)}function Jt(n,e){return Cr(n,n,Tt(),Or(e))}function Qt(n){return Cr(n,n,Ot(),kr())}function Zt(n,e){return Cr(n,n,Ot(),e)}function nr(n,e){return Zt(n,Or(e))}function er(n,e){return Zt(n,Pt(e))}function tr(n,e){return Cr(n,n,xn(e),kr())}function rr(n,e,t){return Cr(n,n,xn(e),t)}function or(n,e){return Er(n,e)}function ir(n,e){return cn(n.element(),e.event().target())}function ur(n){if(!Mn(n,"can")&&!Mn(n,"abort")&&!Mn(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return Xt("Extracting event.handler",Pt([tr("can",v(!0)),tr("abort",v(!1)),tr("run",w)]),n)}function cr(t){var n=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return I(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}}(t,function(n){return n.can}),e=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return I(e,function(n,e){return n||r(e).apply(undefined,t)},!1)}}(t,function(n){return n.abort});return ur({can:n,abort:e,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];C(t,function(n){n.run.apply(undefined,e)})}})}var ar,fr,sr=function(n){return n.fold(Rt,It)},lr=function(n){return Mt(n,vt.error,vt.value)},dr=It,mr=function(n){var e=[],t=[];return C(n,function(n){Mt(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},gr=Rt,pr=function(n,e){return n.stype===In.Value?e(n.svalue):n},hr=function(n,e){return n.stype===In.Error?e(n.serror):n},vr=function(n,e){return n.stype===In.Value?{stype:In.Value,svalue:e(n.svalue)}:n},yr=function(n,e){return n.stype===In.Error?{stype:In.Error,serror:e(n.serror)}:n},br=function(n,e){var t=mr(n);return 0<t.errors.length?Ft(t.errors):function(n,e){return 0<n.length?dr(xt(e,wt.apply(undefined,n))):dr(e)}(t.values,e)},xr=function(n){var e=mr(n);return 0<e.errors.length?Ft(e.errors):dr(e.values)},wr=yt([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),Sr=yt([{field:["name","presence","type"]},{state:["name"]}]),Tr=yt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Or=function(r){return{extract:function(n,e,t){return function(e,t,n,r){var o=ke(n,function(n){return jt(e,t,n,r)});return br(o,{})}(n,t,r,e)},toString:function(){return"obj{\n"+ke(r,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return wr.objOf(ke(r,function(n){return n.fold(function(n,e,t,r){return Sr.field(n,t,r)},function(n,e){return Sr.state(n)})}))}}},kr=v(_t(dr)),Er=Tr.state,Cr=Tr.field,Dr=_t(dr),Mr=function(n){return"Errors: \n"+function(n){var e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return ke(e,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors)+"\n\nInput object: "+At(n.input)},Ir=v(Dr),Rr=(ar=xe,fr="function",_t(function(n){var e=typeof n;return ar(n)?dr(n):gr("Expected type: "+fr+" but got: "+e)}));function Fr(n,e,t,r,o){return n(t,r)?An.some(t):xe(o)&&o(t)?An.none():e(t,r,o)}function Ar(n,e,t){for(var r=n.dom(),o=xe(t)?t:v(!1);r.parentNode;){r=r.parentNode;var i=Je.fromDom(r);if(e(i))return An.some(i);if(o(i))break}return An.none()}function Br(n,e,t){return Fr(function(n,e){return e(n)},Ar,n,e,t)}function Vr(n,o){var i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Je.fromDom(n.childNodes[e]);if(o(t))return An.some(t);var r=i(n.childNodes[e]);if(r.isSome())return r}return An.none()};return i(n.dom())}function Nr(n){return Cn(n)}function jr(n,e){return{key:n,value:ur({abort:e})}}function _r(n,e){return{key:n,value:ur({run:e})}}function Pr(n,e,t){return{key:n,value:ur({run:function(n){e.apply(undefined,[n].concat(t))}})}}function Hr(n){return function(t){return{key:n,value:ur({run:function(n,e){ir(n,e)&&t(n,e)}})}}}function zr(n,e,t){return function(t,r){return _r(t,function(n,e){n.getSystem().getByUid(r).each(function(n){!function(n,e,t,r){n.getSystem().triggerEvent(t,e,r.event())}(n,n.element(),t,e)})})}(n,e.partUids[t])}function Lr(n){return _r(n,function(n,e){e.cut()})}function Gr(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Io(i)}},n}function Ur(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function $r(t,r,o){return Do(function(n,e){o(n,t,r)})}function Wr(o,i,u){return function(n,e,t){var r=t.toString(),o=r.indexOf(")")+1,i=r.indexOf("("),u=r.substring(i+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Io(u.slice(0,1).concat(u.slice(3)))}},n}(function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:v(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},u,i)}function Xr(n){return{key:n,value:undefined}}function qr(n){var e=Xt("Creating behaviour: "+n.name,Vo,n);return function(n,e,t,r,o,i){var u=Pt(n),c=nr(e,[er("config",n)]);return Ro(u,c,e,t,r,o,i)}(e.fields,e.name,e.active,e.apis,e.extra,e.state)}function Yr(n,e,t){if(!(he(t)||be(t)||we(t)))throw d.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function Kr(n,e,t){Yr(n.dom(),e,t)}function Jr(n,e){var t=n.dom();Nn(e,function(n,e){Yr(t,e,n)})}function Qr(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t}function Zr(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)}function no(n,e){n.dom().removeAttribute(e)}function eo(n,e){var t=Qr(n,e);return t===undefined||""===t?[]:t.split(" ")}function to(n){return n.dom().classList!==undefined}function ro(n,e){return function(n,e,t){var r=eo(n,e).concat([t]);return Kr(n,e,r.join(" ")),!0}(n,"class",e)}function oo(n,e){return function(n,e,t){var r=D(eo(n,e),function(n){return n!==t});return 0<r.length?Kr(n,e,r.join(" ")):no(n,e),!1}(n,"class",e)}function io(n,e){to(n)?n.dom().classList.add(e):ro(n,e)}function uo(n){0===(to(n)?n.dom().classList:function(n){return eo(n,"class")}(n)).length&&no(n,"class")}function co(n,e){to(n)?n.dom().classList.remove(e):oo(n,e),uo(n)}function ao(n,e){return to(n)&&n.dom().classList.contains(e)}function fo(n,e,t){co(n,t),io(n,e)}function so(n){n.dom().focus()}function lo(n){n.dom().blur()}function mo(n){var e=n!==undefined?n.dom():d.document;return An.from(e.activeElement).map(Je.fromDom)}function go(e){return mo(an(e)).filter(function(n){return e.dom().contains(n.dom())})}function po(n){return n.dom().innerHTML}function ho(n,e){var t=an(n).dom(),r=Je.fromDom(t.createDocumentFragment()),o=function(n,e){var t=(e||d.document).createElement("div");return t.innerHTML=n,at(Je.fromDom(t))}(e,t);gn(r,o),pn(n),ft(n,r)}function vo(n){return function(n,e){return Je.fromDom(n.dom().cloneNode(e))}(n,!1)}function yo(n){return function(n){var e=Je.fromTag("div"),t=Je.fromDom(n.dom().cloneNode(!0));return ft(e,t),po(e)}(vo(n))}function bo(n){return yo(n)}function xo(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e}function wo(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return An.none()}var So,To,Oo,ko=function(n,e,t){return Br(n,function(n){return e(n).isSome()},t).bind(e)},Eo=Hr(Ue()),Co=Hr($e()),Do=Hr(Ge()),Mo=(So=_e(),function(n){return _r(So,n)}),Io=function(n){return ke(n,function(n){return function(n,e){return function(n,e,t){return""===e||!(n.length<e.length)&&n.substr(t,t+e.length)===e}(n,e,n.length-e.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Ro=function(t,n,r,o,e,i,u){function c(n){return Mn(n,r)?n[r]():An.none()}var a=S(e,function(n,e){return Wr(r,n,e)}),f=S(i,function(n,e){return Gr(n,e)}),s=x(x(x({},f),a),{revoke:l(Xr,r),config:function(n){var e=Xt(r+"-config",t,n);return{key:r,value:{config:e,me:s,configAsRaw:X(function(){return Xt(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return Dt(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(Ur({}))},name:function(){return r},handlers:function(n){return c(n).map(function(n){return kn("events",function(n,e){return{}})(o)(n.config,n.state)}).getOr({})}});return s},Fo={init:function(){return Ao({readState:function(){return"No State required"}})}},Ao=function(n){return n},Bo=function(n){return Cn(n)},Vo=Pt([Yt("fields"),Yt("name"),tr("active",{}),tr("apis",{}),tr("state",Fo),tr("extra",{})]),No=Pt([Yt("branchKey"),Yt("branches"),Yt("name"),tr("active",{}),tr("apis",{}),tr("state",Fo),tr("extra",{})]),jo=v(undefined),_o=/* */Object.freeze({toAlpha:function(n,e,t){fo(n.element(),e.alpha,e.omega)},toOmega:function(n,e,t){fo(n.element(),e.omega,e.alpha)},isAlpha:function(n,e,t){return ao(n.element(),e.alpha)},isOmega:function(n,e,t){return ao(n.element(),e.omega)},clear:function(n,e,t){co(n.element(),e.alpha),co(n.element(),e.omega)}}),Po=[Yt("alpha"),Yt("omega")],Ho=qr({fields:Po,name:"swapping",apis:_o}),zo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Lo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Go=function(n){var e=d.document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=d.document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,d.window,0,0,0,0,0,!1,!1,!1,!1,0,null),d.document.body.appendChild(e),e.dispatchEvent(t),d.document.body.removeChild(e)},Uo={formatChanged:v("formatChanged"),orientationChanged:v("orientationChanged"),dropupDismissed:v("dropupDismissed")},$o=/* */Object.freeze({events:function(e){return Nr([_r(je(),function(o,i){var u=e.channels,n=function(n,e){return e.universal()?n:D(n,function(n){return k(e.channels(),n)})}(Bn(u),i);C(n,function(n){var e=u[n],t=e.schema,r=Xt("channel["+n+"] data\nReceiver: "+bo(o.element()),t,i.data());e.onReceive(o,r)})})])}}),Wo="unknown";(Oo=To=To||{})[Oo.STOP=0]="STOP",Oo[Oo.NORMAL=1]="NORMAL",Oo[Oo.LOGGING=2]="LOGGING";function Xo(e,n,t){switch(Dt(vi.get(),e).orThunk(function(){var n=Bn(vi.get());return wo(n,function(n){return-1<e.indexOf(n)?An.some(vi.get()[n]):An.none()})}).getOr(To.NORMAL)){case To.NORMAL:return t(bi());case To.LOGGING:var r=function(e,t){var r=[],o=(new Date).getTime();return{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();k(["mousemove","mouseover","mouseout",Ge()],e)||d.console.log(e,{event:e,time:n-o,target:t.dom(),sequence:ke(r,function(n){return k(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+bo(n.target)+")":n.outcome})})}}}(e,n),o=t(r);return r.write(),o;case To.STOP:return!0}}function qo(n,e,t){return Xo(n,e,t)}function Yo(n,e,t){return function(){var n=new Error;if(n.stack===undefined)return;var e=n.stack.split("\n");R(e,function(e){return 0<e.indexOf("alloy")&&!E(yi,function(n){return-1<e.indexOf(n)})}).getOr(Wo)}(),Cr(e,e,t,Gt(function(t){return vt.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})}))}function Ko(n){return Yo(0,n,xn(w))}function Jo(n){return Yo(0,n,xn(An.none))}function Qo(n){return Yo(0,n,Tt())}function Zo(n){return Yo(0,n,Tt())}function ni(n,e){return or(n,v(e))}function ei(n){return or(n,y)}function ti(n,e,t){var r=e.aria;r.update(n,r,t.get())}function ri(e,n,t){n.toggleClass.each(function(n){t.get()?io(e.element(),n):co(e.element(),n)})}function oi(n,e,t){Ci(n,e,t,!t.get())}function ii(n,e,t){t.set(!0),ri(n,e,t),ti(n,e,t)}function ui(n,e,t){t.set(!1),ri(n,e,t),ti(n,e,t)}function ci(n,e,t){Ci(n,e,t,e.selected)}function ai(n,e){e.ignore||(so(n.element()),e.onFocus(n))}function fi(n){return n.style!==undefined&&xe(n.style.getPropertyValue)}function si(n,e,t){if(!he(t))throw d.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);fi(n)&&n.style.setProperty(e,t)}function li(n,e){var t=n.dom();Nn(e,function(n,e){si(t,e,n)})}function di(n,e){var t=n.dom(),r=d.window.getComputedStyle(t).getPropertyValue(e),o=""!==r||K(n)?r:Gi(t,e);return null===o?undefined:o}function mi(n,e){var t=n.dom(),r=Gi(t,e);return An.from(r).filter(function(n){return 0<n.length})}function gi(n,e){!function(n,e){fi(n)&&n.style.removeProperty(e)}(n.dom(),e),Zr(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(Qr(n,"style"))&&no(n,"style")}function pi(n){return n.dom().offsetWidth}var hi,vi=Zn({}),yi=["alloy/data/Fields","alloy/debugging/Debugging"],bi=v({logEventCut:w,logEventStopped:w,logNoParent:w,logEventNoHandlers:w,logEventResponse:w,write:w}),xi=v([Yt("menu"),Yt("selectedMenu")]),wi=v([Yt("item"),Yt("selectedItem")]),Si=(v(Or(wi().concat(xi()))),v(Or(wi()))),Ti=Jt("initSize",[Yt("numColumns"),Yt("numRows")]),Oi=v(Ti),ki=[Kt("channels",Ut(vt.value,Pt([Qo("onReceive"),tr("schema",Ir())])))],Ei=qr({fields:ki,name:"receiving",active:$o}),Ci=function(n,e,t,r){(r?ii:ui)(n,e,t)},Di=/* */Object.freeze({onLoad:ci,toggle:oi,isOn:function(n,e,t){return t.get()},on:ii,off:ui,set:Ci}),Mi=/* */Object.freeze({exhibit:function(n,e,t){return Ur({})},events:function(n,e){var t=function(e,t,r){return Mo(function(n){r(n,e,t)})}(n,e,oi),r=$r(n,e,ci);return Nr(A([n.toggleOnExecute?[t]:[],[r]]))}}),Ii=function(n,e,t){Kr(n.element(),"aria-expanded",t)},Ri=[tr("selected",!1),Qt("toggleClass"),tr("toggleOnExecute",!0),rr("aria",{mode:"none"},qt("mode",{pressed:[tr("syncWithExpanded",!1),ni("update",function(n,e,t){Kr(n.element(),"aria-pressed",t),e.syncWithExpanded&&Ii(n,e,t)})],checked:[ni("update",function(n,e,t){Kr(n.element(),"aria-checked",t)})],expanded:[ni("update",Ii)],selected:[ni("update",function(n,e,t){Kr(n.element(),"aria-selected",t)})],none:[ni("update",w)]}))],Fi=qr({fields:Ri,name:"toggling",active:Mi,apis:Di,state:(hi=!1,{init:function(){var e=Zn(hi);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(hi)},readState:function(){return e.get()}}}})}),Ai=function(t,r){return Ei.config({channels:En(Uo.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},Bi=function(n){return Ei.config({channels:En(Uo.orientationChanged(),{onReceive:n})})},Vi=function(n,e){return{key:n,value:{onReceive:e}}},Ni="tinymce-mobile",ji={resolve:function(n){return Ni+"-"+n},prefix:v(Ni)},_i=/* */Object.freeze({focus:ai,blur:function(n,e){e.ignore||lo(n.element())},isFocused:function(n){return function(n){var e=an(n).dom();return n.dom()===e.activeElement}(n.element())}}),Pi=/* */Object.freeze({exhibit:function(n,e){var t=e.ignore?{}:{attributes:{tabindex:"-1"}};return Ur(t)},events:function(t){return Nr([_r(Be(),function(n,e){ai(n,t),e.stop()})].concat(t.stopMousedown?[_r(Ln(),function(n,e){e.event().prevent()})]:[]))}}),Hi=[Ko("onFocus"),tr("stopMousedown",!1),tr("ignore",!1)],zi=qr({fields:Hi,name:"focusing",active:Pi,apis:_i}),Li=function(n,e,t){var r=n.dom();si(r,e,t)},Gi=function(n,e){return fi(n)?n.style.getPropertyValue(e):""};function Ui(r,o){function n(n){var e=o(n);if(e<=0||null===e){var t=di(n,r);return parseFloat(t)||0}return e}function i(o,n){return I(n,function(n,e){var t=di(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)}return{set:function(n,e){if(!we(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();fi(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}function $i(n){return vu.get(n)}function Wi(n,e,t){return D(function(n,e){for(var t=xe(e)?e:c,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=Je.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)}function Xi(n,e){return D(function(e){return fn(e).map(at).map(function(n){return D(n,function(n){return!cn(e,n)})}).getOr([])}(n),e)}function qi(n,e){return on(e,n)}function Yi(n){return un(n)}function Ki(n,e,t){return Ar(n,function(n){return tn(n,e)},t)}function Ji(n,e){return un(e,n)}function Qi(n,e,t){return Fr(tn,Ki,n,e,t)}function Zi(n,e,t){var r=N(n.slice(0,e)),o=N(n.slice(e+1));return R(r.concat(o),t)}function nu(n,e,t){var r=N(n.slice(0,e));return R(r,t)}function eu(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return R(o.concat(r),t)}function tu(n,e,t){var r=n.slice(e+1);return R(r,t)}function ru(t){return function(n){var e=n.raw();return k(t,e.which)}}function ou(n){return function(e){return V(n,function(n){return n(e)})}}function iu(n){return!0===n.raw().shiftKey}function uu(n){return!0===n.raw().ctrlKey}function cu(n,e){return{matches:n,classification:e}}function au(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o}function fu(n,e,t){return n<=e?e:t<=n?t:n}function su(t,r,n,o){var e=qi(t.element(),"."+r.highlightClass);C(e,function(e){E(o,function(n){return n.element()===e})||(co(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),G(n,qe())}))})}function lu(n,e,t,r){su(n,e,0,[r]),bu(n,e,t,r)||(io(r.element(),e.highlightClass),e.onHighlight(n,r),G(r,Xe()))}function du(t,e,n,r){var o=qi(t.element(),"."+e.itemClass);return F(o,function(n){return ao(n,e.highlightClass)}).bind(function(n){var e=au(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})}function mu(n,e,t){e.exists(function(e){return t.exists(function(n){return cn(n,e)})})||U(n,We(),{prevFocus:e,newFocus:t})}function gu(){function o(n){return go(n.element())}return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element());var r=o(n);mu(n,t,r)}}}var pu,hu,vu=Ui("height",function(n){var e=n.dom();return K(n)?e.getBoundingClientRect().height:e.offsetHeight}),yu=m(iu),bu=function(n,e,t,r){return ao(r.element(),e.highlightClass)},xu=function(n,e,t,r){var o=qi(n.element(),"."+e.itemClass);return An.from(o[r]).fold(function(){return vt.error("No element found with index "+r)},n.getSystem().getByDom)},wu=function(e,n,t){return Ji(e.element(),"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Su=function(e,n,t){var r=qi(e.element(),"."+n.itemClass);return(0<r.length?An.some(r[r.length-1]):An.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Tu=function(e,n,t){var r=qi(e.element(),"."+n.itemClass);return xo(ke(r,function(n){return e.getSystem().getByDom(n).toOption()}))},Ou=/* */Object.freeze({dehighlightAll:function(n,e,t){return su(n,e,0,[])},dehighlight:function(n,e,t,r){bu(n,e,t,r)&&(co(r.element(),e.highlightClass),e.onDehighlight(n,r),G(r,qe()))},highlight:lu,highlightFirst:function(e,t,r){wu(e,t).each(function(n){lu(e,t,r,n)})},highlightLast:function(e,t,r){Su(e,t).each(function(n){lu(e,t,r,n)})},highlightAt:function(e,t,r,n){xu(e,t,r,n).fold(function(n){throw new Error(n)},function(n){lu(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Tu(e,t);R(o,n).each(function(n){lu(e,t,r,n)})},isHighlighted:bu,getHighlighted:function(e,n,t){return Ji(e.element(),"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:wu,getLast:Su,getPrevious:function(n,e,t){return du(n,e,0,-1)},getNext:function(n,e,t){return du(n,e,0,1)},getCandidates:Tu}),ku=[Yt("highlightClass"),Yt("itemClass"),Ko("onHighlight"),Ko("onDehighlight")],Eu=qr({fields:ku,name:"highlighting",apis:Ou});(hu=pu=pu||{}).OnFocusMode="onFocus",hu.OnEnterOrSpaceMode="onEnterOrSpace",hu.OnApiMode="onApi";function Cu(n,e,t,i,u){function c(e,t,n,r,o){return function(n,e){return R(n,function(n){return n.matches(e)}).map(function(n){return n.classification})}(n(e,t,r,o),t.event()).bind(function(n){return n(e,t,r,o)})}var r={schema:function(){return n.concat([tr("focusManager",gu()),rr("focusInside","onFocus",Gt(function(n){return k(["onFocus","onEnterOrSpace","onApi"],n)?vt.value(n):vt.error("Invalid value for focusInside")})),ni("handler",r),ni("state",e),ni("sendFocusIn",u)])},processKey:c,toEvents:function(r,o){var n=r.focusInside!==pu.OnFocusMode?An.none():u(r).map(function(t){return _r(Be(),function(n,e){t(n,r,o),e.stop()})});return Nr(n.toArray().concat([_r(Wn(),function(n,e){c(n,e,t,r,o).fold(function(){!function(e,t){var n=ru([32].concat([13]))(t.event());r.focusInside===pu.OnEnterOrSpaceMode&&n&&ir(e,t)&&u(r).each(function(n){n(e,r,o),t.stop()})}(n,e)},function(n){e.stop()})}),_r(Xn(),function(n,e){c(n,e,i,r,o).each(function(n){e.stop()})})]))}};return r}function Du(n){function i(n,e){var t=n.visibilitySelector.bind(function(n){return Qi(e,n)}).getOr(e);return 0<$i(t)}function e(e,t){(function(n,e){var t=qi(n.element(),e.selector),r=D(t,function(n){return i(e,n)});return An.from(r[e.firstTabstop])})(e,t).each(function(n){t.focusManager.set(e,n)})}function u(e,n,t,r,o){return o(n,t,function(n){return function(n,e){return i(n,e)&&n.useTabstopAt(e)}(r,n)}).fold(function(){return r.cyclic?An.some(!0):An.none()},function(n){return r.focusManager.set(e,n),An.some(!0)})}function c(e,n,t,r){var o=qi(e.element(),t.selector);return function(n,e){return e.focusManager.get(n).bind(function(n){return Qi(n,e.selector)})}(e,t).bind(function(n){return F(o,l(cn,n)).bind(function(n){return u(e,o,n,t,r)})})}var t=[Qt("onEscape"),Qt("onEnter"),tr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),tr("firstTabstop",0),tr("useTabstopAt",v(!0)),Qt("visibilitySelector")].concat([n]),r=v([cu(ou([iu,ru([9])]),function(n,e,t,r){var o=t.cyclic?Zi:nu;return c(n,0,t,o)}),cu(ru([9]),function(n,e,t,r){var o=t.cyclic?eu:tu;return c(n,0,t,o)}),cu(ru([27]),function(e,t,n,r){return n.onEscape.bind(function(n){return n(e,t)})}),cu(ou([yu,ru([13])]),function(e,t,n,r){return n.onEnter.bind(function(n){return n(e,t)})})]),o=v([]);return Cu(t,Fo.init,r,o,function(){return An.some(e)})}function Mu(n){return"input"===q(n)&&"radio"!==Qr(n,"type")||"textarea"===q(n)}function Iu(n,e,t){return Mu(t)&&ru([32])(e.event())?An.none():function(n,e,t){return W(n,t,_e()),An.some(!0)}(n,0,t)}function Ru(n,e){return An.some(!0)}function Fu(n,e,t){return t.execute(n,e,n.element())}function Au(n){var t=Zn(An.none());return Ao({readState:function(){return t.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(An.some({numRows:v(n),numColumns:v(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})}function Bu(e,t){return function(n){return"rtl"===qc(n)?t:e}}function Vu(i){return function(n,e,t,r){var o=i(n.element());return Yc(o,n,e,t,r)}}function Nu(n,e){var t=Bu(n,e);return Vu(t)}function ju(n,e){var t=Bu(e,n);return Vu(t)}function _u(o){return function(n,e,t,r){return Yc(o,n,e,t,r)}}function Pu(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function Hu(n,e,t){var r=l(cn,e),o=qi(n,t);return function(e,n){return F(e,n).map(function(n){return Zc({index:n,candidates:e})})}(D(o,Pu),r)}function zu(n,e){return F(n,function(n){return cn(e,n)})}function Lu(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?An.some(t[e]):An.none()})}function Gu(o,n,i,u,c){return Lu(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=au(e,c,0,t-1);return An.some({row:v(n),column:v(r)})})}function Uu(i,n,u,c,a){return Lu(i,n,c,function(n,e){var t=au(n,a,0,u-1),r=t===u-1?i.length-t*c:c,o=fu(e,0,r-1);return An.some({row:v(t),column:v(o)})})}function $u(e,t,n){Ji(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})}function Wu(o){return function(n,e,t,r){return Hu(n,e,t.selector).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}}function Xu(n,e,t,r){return t.captureTab?An.some(!0):An.none()}function qu(n,e,t,o){var i=function(n,e,t){var r=au(e,o,0,t.length-1);return r===n?An.none():function(n){return"button"===q(n)&&"disabled"===Qr(n,"disabled")}(t[r])?i(n,r,t):An.from(t[r])};return Hu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates();return i(e,e,t)})}function Yu(e,t,r){return function(n,e){return e.focusManager.get(n).bind(function(n){return Qi(n,e.selector)})}(e,r).bind(function(n){return r.execute(e,t,n)})}function Ku(e,t){t.getInitial(e).orThunk(function(){return Ji(e.element(),t.selector)}).each(function(n){t.focusManager.set(e,n)})}function Ju(n,e,t){return qu(n,t.selector,e,-1)}function Qu(n,e,t){return qu(n,t.selector,e,1)}function Zu(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove?Yu(n,e,t):An.some(!0)})}}function nc(n,e,t,r){return t.onEscape(n,e)}function ec(n,e,t){return An.from(n[e]).bind(function(n){return An.from(n[t]).map(function(n){return la({rowIndex:e,columnIndex:t,cell:n})})})}function tc(n,e,t,r){var o=n[e].length,i=au(t,r,0,o-1);return ec(n,e,i)}function rc(n,e,t,r){var o=au(t,r,0,n.length-1),i=n[o].length,u=fu(e,0,i-1);return ec(n,o,u)}function oc(n,e,t,r){var o=n[e].length,i=fu(t+r,0,o-1);return ec(n,e,i)}function ic(n,e,t,r){var o=fu(t+r,0,n.length-1),i=n[o].length,u=fu(e,0,i-1);return ec(n,o,u)}function uc(e,t){t.previousSelector(e).orThunk(function(){var n=t.selectors;return Ji(e.element(),n.cell)}).each(function(n){t.focusManager.set(e,n)})}function cc(n,e){return function(o,t,i){var u=i.cycles?n:e;return Qi(t,i.selectors.row).bind(function(n){var e=qi(n,i.selectors.cell);return zu(e,t).bind(function(t){var r=qi(o,i.selectors.row);return zu(r,n).bind(function(n){var e=function(n,e){return ke(n,function(n){return qi(n,e.selectors.cell)})}(r,i);return u(e,n,t).map(function(n){return n.cell()})})})})}}function ac(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})}function fc(e,t){Ji(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})}function sc(n,e,t){return qu(n,t.selector,e,-1)}function lc(n,e,t){return qu(n,t.selector,e,1)}function dc(e,n){return function(n,e,t){return rr(n,e,Or(t))}(e,{},ke(n,function(n){return function(e,t){return Cr(e,e,Ot(),_t(function(n){return gr("The field: "+e+" is forbidden. "+t)}))}(n.name(),"Cannot configure "+n.name()+" for "+e)}).concat([or("dump",y)]))}function mc(n){return n.dump}function gc(n,e){return x(x({},n.dump),Bo(e))}function pc(n,e,t,r){return t.uiType===ja?function(n,e,t,r){return n.exists(function(n){return n!==t.owner})?_a.single(!0,v(t)):Dt(r,t.name).fold(function(){throw new Error("Unknown placeholder component: "+t.name+"\nKnown: ["+Bn(r)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(t,null,2))},function(n){return n.replace()})}(n,0,t,r):_a.single(!1,v(t))}function hc(e,t,n,r){var o=S(r,function(n,e){return function(n,e){var t=!1;return{name:v(n),required:function(){return e.fold(function(n,e){return n},function(n,e){return n})},used:function(){return t},replace:function(){if(!0===t)throw new Error("Trying to use the same placeholder more than once: "+n);return t=!0,e}}}(e,n)}),i=function(e,t,n,r){return B(n,function(n){return Pa(e,t,n,r)})}(e,t,n,o);return Nn(o,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))}),i}function vc(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Ga+String(e)}function yc(n){function e(n){return n.name}return n.fold(e,e,e,e)}function bc(t,r){return function(n){var e=Xt("Converting part type",r,n);return t(e)}}function xc(n,e,t,r){return xt(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))}function wc(o,n){var e={};return C(n,function(n){(function(n){return n.fold(An.some,An.none,An.some,An.some)})(n).each(function(t){var r=uf(o,t.pname);e[t.name]=function(n){var e=Xt("Part: "+t.name+" in "+o,Or(t.schema),n);return x(x({},r),{config:n,validated:e})}})}),e}function Sc(n,e,t){return function(n,t,e){var i={},r={};return C(e,function(n){n.fold(function(r){i[r.pname]=Ha(!0,function(n,e,t){return r.factory.sketch(xc(n,r,e,t))})},function(n){var e=t.parts[n.name];r[n.name]=v(n.factory.sketch(xc(t,n,e[of()]),e))},function(r){i[r.pname]=Ha(!1,function(n,e,t){return r.factory.sketch(xc(n,r,e,t))})},function(o){i[o.pname]=za(!0,function(e,n,t){var r=e[o.name];return ke(r,function(n){return o.factory.sketch(xt(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:v(i),externals:v(r)}}(0,e,t)}function Tc(n,e,t){return hc(An.some(n),e,e.components,t)}function Oc(n,e,t){var r=e.partUids[t];return n.getSystem().getByUid(r).toOption()}function kc(n,e,t){return Oc(n,e,t).getOrDie("Could not find part: "+t)}function Ec(e,n){var t=function(n){return ke(n,yc)}(n);return Cn(ke(t,function(n){return{key:n,value:e+"-"+n}}))}function Cc(e){return Cr("partUids","partUids",Et(function(n){return Ec(n.uid,e)}),Ir())}function Dc(n){return En(cf,n)}function Mc(r){return function(n,e){var t=e.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Io(i.slice(1))}},n}(function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(undefined,[n.getApis()].concat([n].concat(e)))},r)}function Ic(n){return vc(n)}function Rc(n,e,t,r,o){var i=function(n,e){return(0<n.length?[Jt("parts",n)]:[]).concat([Yt("uid"),tr("dom",{}),tr("components",[]),ei("originalSpec"),tr("debug.sketcher",{})]).concat(e)}(r,o);return Xt(n+" [SpecSchema]",Pt(i.concat(e)),t)}function Fc(n,e,t,r,o){var i=gf(o),u=function(n){return B(n,function(n){return n.fold(An.none,An.some,An.none,An.none).map(function(n){return Jt(n.name,n.schema.concat([ei(of())]))}).toArray()})}(t),c=Cc(t),a=Rc(n,e,i,u,[c]),f=Sc(0,a,t);return r(a,Tc(n,a,f.internals()),i,f.externals())}var Ac,Bc,Vc,Nc,jc,_c,Pc,Hc,zc,Lc,Gc=Du(or("cyclic",v(!1))),Uc=Du(or("cyclic",v(!0))),$c=[tr("execute",Iu),tr("useSpace",!1),tr("useEnter",!0),tr("useControlEnter",!1),tr("useDown",!1)],Wc=Cu($c,Fo.init,function(n,e,t,r){var o=t.useSpace&&!Mu(n.element())?[32]:[],i=t.useEnter?[13]:[],u=t.useDown?[40]:[],c=o.concat(i).concat(u);return[cu(ru(c),Fu)].concat(t.useControlEnter?[cu(ou([uu,ru([13])]),Fu)]:[])},function(n,e,t,r){return t.useSpace&&!Mu(n.element())?[cu(ru([32]),Ru)]:[]},function(){return An.none()}),Xc=/* */Object.freeze({flatgrid:Au,init:function(n){return n.state(n)}}),qc=function(n){return"rtl"===di(n,"direction")?"rtl":"ltr"},Yc=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},Kc=_u,Jc=_u,Qc=_u,Zc=nn(["index","candidates"],[]),na=[Yt("selector"),tr("execute",Iu),Jo("onEscape"),tr("captureTab",!1),Oi()],ea=Wu(function(n,e,t,r){return Gu(n,e,t,r,-1)}),ta=Wu(function(n,e,t,r){return Gu(n,e,t,r,1)}),ra=Wu(function(n,e,t,r){return Uu(n,e,t,r,-1)}),oa=Wu(function(n,e,t,r){return Uu(n,e,t,r,1)}),ia=v([cu(ru([37]),Nu(ea,ta)),cu(ru([39]),ju(ea,ta)),cu(ru([38]),Kc(ra)),cu(ru([40]),Jc(oa)),cu(ou([iu,ru([9])]),Xu),cu(ou([yu,ru([9])]),Xu),cu(ru([27]),function(n,e,t,r){return t.onEscape(n,e)}),cu(ru([32].concat([13])),function(e,t,r,n){return function(n,e){return e.focusManager.get(n).bind(function(n){return Qi(n,e.selector)})}(e,r).bind(function(n){return r.execute(e,t,n)})})]),ua=v([cu(ru([32]),Ru)]),ca=Cu(na,Au,ia,ua,function(){return An.some($u)}),aa=[Yt("selector"),tr("getInitial",An.none),tr("execute",Iu),Jo("onEscape"),tr("executeOnMove",!1),tr("allowVertical",!0)],fa=v([cu(ru([32]),Ru)]),sa=Cu(aa,Fo.init,function(n,e,t,r){var o=[37].concat(t.allowVertical?[38]:[]),i=[39].concat(t.allowVertical?[40]:[]);return[cu(ru(o),Zu(Nu(Ju,Qu))),cu(ru(i),Zu(ju(Ju,Qu))),cu(ru([13]),Yu),cu(ru([32]),Yu),cu(ru([27]),nc)]},fa,function(){return An.some(Ku)}),la=nn(["rowIndex","columnIndex","cell"],[]),da=[Jt("selectors",[Yt("row"),Yt("cell")]),tr("cycles",!0),tr("previousSelector",An.none),tr("execute",Iu)],ma=cc(function(n,e,t){return tc(n,e,t,-1)},function(n,e,t){return oc(n,e,t,-1)}),ga=cc(function(n,e,t){return tc(n,e,t,1)},function(n,e,t){return oc(n,e,t,1)}),pa=cc(function(n,e,t){return rc(n,t,e,-1)},function(n,e,t){return ic(n,t,e,-1)}),ha=cc(function(n,e,t){return rc(n,t,e,1)},function(n,e,t){return ic(n,t,e,1)}),va=v([cu(ru([37]),Nu(ma,ga)),cu(ru([39]),ju(ma,ga)),cu(ru([38]),Kc(pa)),cu(ru([40]),Jc(ha)),cu(ru([32].concat([13])),function(e,t,r){return go(e.element()).bind(function(n){return r.execute(e,t,n)})})]),ya=v([cu(ru([32]),Ru)]),ba=Cu(da,Fo.init,va,ya,function(){return An.some(uc)}),xa=[Yt("selector"),tr("execute",Iu),tr("moveOnTab",!1)],wa=v([cu(ru([38]),Qc(sc)),cu(ru([40]),Qc(lc)),cu(ou([iu,ru([9])]),function(n,e,t){return t.moveOnTab?Qc(sc)(n,e,t):An.none()}),cu(ou([yu,ru([9])]),function(n,e,t){return t.moveOnTab?Qc(lc)(n,e,t):An.none()}),cu(ru([13]),ac),cu(ru([32]),ac)]),Sa=v([cu(ru([32]),Ru)]),Ta=Cu(xa,Fo.init,wa,Sa,function(){return An.some(fc)}),Oa=[Jo("onSpace"),Jo("onEnter"),Jo("onShiftEnter"),Jo("onLeft"),Jo("onRight"),Jo("onTab"),Jo("onShiftTab"),Jo("onUp"),Jo("onDown"),Jo("onEscape"),tr("stopSpaceKeyup",!1),Qt("focusIn")],ka=Cu(Oa,Fo.init,function(n,e,t){return[cu(ru([32]),t.onSpace),cu(ou([yu,ru([13])]),t.onEnter),cu(ou([iu,ru([13])]),t.onShiftEnter),cu(ou([iu,ru([9])]),t.onShiftTab),cu(ou([yu,ru([9])]),t.onTab),cu(ru([38]),t.onUp),cu(ru([40]),t.onDown),cu(ru([37]),t.onLeft),cu(ru([39]),t.onRight),cu(ru([32]),t.onSpace),cu(ru([27]),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[cu(ru([32]),Ru)]:[]},function(n){return n.focusIn}),Ea=Gc.schema(),Ca=Uc.schema(),Da=sa.schema(),Ma=ca.schema(),Ia=ba.schema(),Ra=Wc.schema(),Fa=Ta.schema(),Aa=ka.schema(),Ba=(Lc=Xt("Creating behaviour: "+(zc={branchKey:"mode",branches:/* */Object.freeze({acyclic:Ea,cyclic:Ca,flow:Da,flatgrid:Ma,matrix:Ia,execution:Ra,menu:Fa,special:Aa}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element(),e.element())},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){Mn(t,"setGridSize")?t.setGridSize(r,o):d.console.error("Layout does not support setGridSize")}},state:Xc}).name,No,zc),Ac=qt(Lc.branchKey,Lc.branches),Bc=Lc.name,Vc=Lc.active,Nc=Lc.apis,jc=Lc.extra,_c=Lc.state,Hc=nr(Bc,[Zt("config",Pc=Ac)]),Ro(Pc,Hc,Bc,Vc,Nc,jc,_c)),Va=dc,Na=gc,ja="placeholder",_a=yt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Pa=function(i,u,c,a){return pc(i,0,c,a).fold(function(n,e){var t=e(u,c.config,c.validated),r=Dt(t,"components").getOr([]),o=B(r,function(n){return Pa(i,u,n,a)});return[x(x({},t),{components:o})]},function(n,e){var t=e(u,c.config,c.validated);return c.validated.preprocess.getOr(y)(t)})},Ha=_a.single,za=_a.multiple,La=v(ja),Ga=0,Ua=yt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),$a=tr("factory",{sketch:y}),Wa=tr("schema",[]),Xa=Yt("name"),qa=Cr("pname","pname",kt(function(n){return"<alloy."+vc(n.name)+">"}),Ir()),Ya=or("schema",function(){return[Qt("preprocess")]}),Ka=tr("defaults",v({})),Ja=tr("overrides",v({})),Qa=Or([$a,Wa,Xa,qa,Ka,Ja]),Za=Or([$a,Wa,Xa,qa,Ka,Ja]),nf=Or([$a,Ya,Xa,Yt("unit"),qa,Ka,Ja]),ef=bc(Ua.required,Qa),tf=bc(Ua.optional,Za),rf=bc(Ua.group,nf),of=v("entirety"),uf=function(n,e){return{uiType:La(),owner:n,name:e}},cf=vc("alloy-premade"),af=v("alloy-id-"),ff=v("data-alloy-id"),sf=af(),lf=ff(),df=function(n,e){Object.defineProperty(n.dom(),lf,{value:e,writable:!0})},mf=function(n){var e=et(n)?n.dom()[lf]:null;return An.from(e)},gf=function(n){return n.hasOwnProperty("uid")?n:x(x({},n),{uid:Ic("uid")})};function pf(n){var e=Xt("Sketcher for "+n.name,Ps,n),t=S(e.apis,Mc),r=S(e.extraApis,function(n,e){return Gr(n,e)});return x(x({name:v(e.name),partFields:v([]),configFields:v(e.configFields),sketch:function(n){return function(n,e,t,r){var o=gf(r);return t(Rc(n,e,o,[],[]),o)}(e.name,e.configFields,e.factory,n)}},t),r)}function hf(n){var e=Xt("Sketcher for "+n.name,Hs,n),t=wc(e.name,e.partFields),r=S(e.apis,Mc),o=S(e.extraApis,function(n,e){return Gr(n,e)});return x(x({name:v(e.name),partFields:v(e.partFields),configFields:v(e.configFields),sketch:function(n){return Fc(e.name,e.configFields,e.partFields,e.factory,n)},parts:v(t)},r),o)}function vf(n){var e=Je.fromHtml(n),t=at(e),r=function(n){var e=n.dom().attributes!==undefined?n.dom().attributes:[];return I(e,function(n,e){var t;return"class"===e.name?n:x(x({},n),((t={})[e.name]=e.value,t))},{})}(e),o=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(e),i=0===t.length?{}:{innerHtml:po(e)};return x({tag:q(e),classes:o,attributes:r},i)}function yf(n){return{dom:Gs(n)}}function bf(n){return Bo([Fi.config({toggleClass:ji.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Ai(n,function(n,e){(e?Fi.on:Fi.off)(n)})])}function xf(n,e){var t=e.ui.registry.getAll().icons;return An.from(t[n]).fold(function(){return Gs('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return Gs('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})}function wf(n){return tf({name:n+"-edge",overrides:function(r){return r.model.manager.edgeActions[n].fold(function(){return{}},function(t){var n=Nr([Pr(Pn(),t,[r])]),e=Nr([Pr(Ln(),t,[r]),Pr(Gn(),function(n,e){e.mouseIsDown.get()&&t(n,e)},[r])]);return{events:Ws?n:e}})}})}function Sf(n,e,t){e.store.manager.onLoad(n,e,t)}function Tf(n,e,t){e.store.manager.onUnload(n,e,t)}function Of(){var n=Zn(null);return Ao({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function kf(){var i=Zn({}),u=Zn({});return Ao({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Dt(i.get(),n).orThunk(function(){return Dt(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};C(n,function(e){r[e.value]=e,Dt(e,"meta").each(function(n){Dt(n,"text").each(function(n){o[n]=e})})}),i.set(x(x({},e),r)),u.set(x(x({},t),o))},clear:function(){i.set({}),u.set({})}})}function Ef(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)}function Cf(n,e){sl.set(n,e)}function Df(n){return sl.get(n)}function Mf(n){var e=n.event().raw();if(ml){var t=e;return t.touches!==undefined&&1===t.touches.length?An.some(t.touches[0]).map(function(n){return dl(n.clientX,n.clientY)}):An.none()}var r=e;return r.clientX!==undefined?An.some(r).map(function(n){return dl(n.clientX,n.clientY)}):An.none()}function If(n){return n.model.minX}function Rf(n){return n.model.minY}function Ff(n){return n.model.minX-1}function Af(n){return n.model.minY-1}function Bf(n){return n.model.maxX}function Vf(n){return n.model.maxY}function Nf(n){return n.model.maxX+1}function jf(n){return n.model.maxY+1}function _f(n,e,t){return e(n)-t(n)}function Pf(n){return _f(n,Bf,If)}function Hf(n){return _f(n,Vf,Rf)}function zf(n){return Pf(n)/2}function Lf(n){return Hf(n)/2}function Gf(n){return n.stepSize}function Uf(n){return n.snapToGrid}function $f(n){return n.snapStart}function Wf(n){return n.rounded}function Xf(n,e){return n[e+"-edge"]!==undefined}function qf(n){return Xf(n,"left")}function Yf(n){return Xf(n,"right")}function Kf(n){return Xf(n,"top")}function Jf(n){return Xf(n,"bottom")}function Qf(n){return n.model.value.get()}function Zf(n){return{x:v(n)}}function ns(n){return{y:v(n)}}function es(n,e){return{x:v(n),y:v(e)}}function ts(n,e){U(n,gl(),{value:e})}function rs(n,e,t,r){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-r)}function os(n,e,t,r){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+r)}function is(n,e,t){return Math.max(e,Math.min(t,n))}function us(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.step,u=n.snap,c=n.snapStart,a=n.rounded,f=n.hasMinEdge,s=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=f?e-1:e,p=s?t+1:t;if(o<l)return g;if(d<o)return p;var h=function(n,e,t){return Math.min(t,Math.max(n,e))-e}(o,l,d),v=is(h/m*r+e,g,p);return u&&e<=v&&v<=t?function(u,t,c,a,n){return n.fold(function(){var n=u-t,e=Math.round(n/a)*a;return is(t+e,t-1,c+1)},function(n){var e=(u-n)%a,t=Math.round(e/a),r=Math.floor((u-n)/a),o=Math.floor((c-n)/a),i=n+Math.min(o,r+t)*a;return Math.max(n,i)})}(v,e,t,i,c):a?Math.round(v):v}function cs(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,c=n.maxBound,a=n.maxOffset,f=n.centerMinEdge,s=n.centerMaxEdge;return o<e?i?0:f:t<o?u?c:s:(o-e)/r*a}function as(n){return n.element().dom().getBoundingClientRect()}function fs(n,e){return n[e]}function ss(n){var e=as(n);return fs(e,pl)}function ls(n){var e=as(n);return fs(e,"right")}function ds(n){var e=as(n);return fs(e,"top")}function ms(n){var e=as(n);return fs(e,"bottom")}function gs(n){var e=as(n);return fs(e,"width")}function ps(n){var e=as(n);return fs(e,"height")}function hs(n,e,t){return(n+e)/2-t}function vs(n,e){var t=as(n),r=as(e),o=fs(t,pl),i=fs(t,"right"),u=fs(r,pl);return hs(o,i,u)}function ys(n,e){var t=as(n),r=as(e),o=fs(t,"top"),i=fs(t,"bottom"),u=fs(r,"top");return hs(o,i,u)}function bs(n,e){U(n,gl(),{value:e})}function xs(n){return{x:v(n)}}function ws(n,e,t){var r={min:If(e),max:Bf(e),range:Pf(e),value:t,step:Gf(e),snap:Uf(e),snapStart:$f(e),rounded:Wf(e),hasMinEdge:qf(e),hasMaxEdge:Yf(e),minBound:ss(n),maxBound:ls(n),screenRange:gs(n)};return us(r)}function Ss(t){return function(n,e){return function(n,e,t){var r=(0<n?os:rs)(Qf(t).x(),If(t),Bf(t),Gf(t));return bs(e,xs(r)),An.some(r)}(t,n,e).map(function(){return!0})}}function Ts(n,e,t,r,o,i){var u=function(e,n,t,r,o){var i=gs(e),u=r.bind(function(n){return An.some(vs(n,e))}).getOr(0),c=o.bind(function(n){return An.some(vs(n,e))}).getOr(i),a={min:If(n),max:Bf(n),range:Pf(n),value:t,hasMinEdge:qf(n),hasMaxEdge:Yf(n),minBound:ss(e),minOffset:0,maxBound:ls(e),maxOffset:i,centerMinEdge:u,centerMaxEdge:c};return cs(a)}(e,i,t,r,o);return ss(e)-ss(n)+u}function Os(n,e){U(n,gl(),{value:e})}function ks(n){return{y:v(n)}}function Es(n,e,t){var r={min:Rf(e),max:Vf(e),range:Hf(e),value:t,step:Gf(e),snap:Uf(e),snapStart:$f(e),rounded:Wf(e),hasMinEdge:Kf(e),hasMaxEdge:Jf(e),minBound:ds(n),maxBound:ms(n),screenRange:ps(n)};return us(r)}function Cs(t){return function(n,e){return function(n,e,t){var r=(0<n?os:rs)(Qf(t).y(),Rf(t),Vf(t),Gf(t));return Os(e,ks(r)),An.some(r)}(t,n,e).map(function(){return!0})}}function Ds(n,e,t,r,o,i){var u=function(e,n,t,r,o){var i=ps(e),u=r.bind(function(n){return An.some(ys(n,e))}).getOr(0),c=o.bind(function(n){return An.some(ys(n,e))}).getOr(i),a={min:Rf(n),max:Vf(n),range:Hf(n),value:t,hasMinEdge:Kf(n),hasMaxEdge:Jf(n),minBound:ds(e),minOffset:0,maxBound:ms(e),maxOffset:i,centerMinEdge:u,centerMaxEdge:c};return cs(a)}(e,i,t,r,o);return ds(e)-ds(n)+u}function Ms(n,e){U(n,gl(),{value:e})}function Is(n,e){return{x:v(n),y:v(e)}}function Rs(t,r){return function(n,e){return function(n,e,t,r){var o=0<n?os:rs,i=e?Qf(r).x():o(Qf(r).x(),If(r),Bf(r),Gf(r)),u=e?o(Qf(r).y(),Rf(r),Vf(r),Gf(r)):Qf(r).y();return Ms(t,Is(i,u)),An.some(i)}(t,r,n,e).map(function(){return!0})}}function Fs(e,t,r,n){return $s.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{},n)}function As(n){return[function(o){function i(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"}return jl.sketch({dom:Gs('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[jl.parts()["left-edge"](yf('<div class="${prefix}-hue-slider-black"></div>')),jl.parts().spectrum({dom:Gs('<div class="${prefix}-slider-gradient-container"></div>'),components:[yf('<div class="${prefix}-slider-gradient"></div>')],behaviours:Bo([Fi.config({toggleClass:ji.resolve("thumb-active")})])}),jl.parts()["right-edge"](yf('<div class="${prefix}-hue-slider-white"></div>')),jl.parts().thumb({dom:Gs('<div class="${prefix}-slider-thumb"></div>'),behaviours:Bo([Fi.config({toggleClass:ji.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t.x());Li(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){Fi.on(e)},onDragEnd:function(n,e){Fi.off(e)},onInit:function(n,e,t,r){var o=i(r.x());Li(e.element(),"background-color",o)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},sliderBehaviours:Bo([Bi(jl.refresh)])})}(n)]}function Bs(n){var e=n.selection.getStart(),t=Je.fromDom(e),r=Je.fromDom(n.getBody()),o=function(e,n){return(et(n)?An.some(n):fn(n).filter(et)).map(function(n){return Br(n,function(n){return mi(n,"font-size").isSome()},e).bind(function(n){return mi(n,"font-size")}).getOrThunk(function(){return di(n,"font-size")})}).getOr("")}(function(n){return cn(r,n)},t);return R(zl,function(n){return o===n}).getOr("medium")}function Vs(n){return[yf('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),function(n){return Hl({onChange:n.onChange,sizes:Gl,category:"font",getInitialValue:n.getInitialValue})}(n),yf('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')]}function Ns(n){var e=function t(n){return n.uid!==undefined}(n)&&Mn(n,"uid")?n.uid:Ic("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOption()},asSpec:function(){return x(x({},n),{uid:e})}}}var js,_s,Ps=Pt([Yt("name"),Yt("factory"),Yt("configFields"),tr("apis",{}),tr("extraApis",{})]),Hs=Pt([Yt("name"),Yt("factory"),Yt("configFields"),Yt("partFields"),tr("apis",{}),tr("extraApis",{})]),zs=pf({name:"Button",factory:function(n){function t(e){return Dt(n.dom,"attributes").bind(function(n){return Dt(n,e)})}var e=function(n){function e(n,e){e.stop(),$(n)}var t=L().deviceType.isTouch()?[_r(He(),e)]:[_r(Kn(),e),_r(Ln(),function(n,e){e.cut()})];return Nr(A([n.map(function(t){return _r(_e(),function(n,e){t(n),e.stop()})}).toArray(),t]))}(n.action),r=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:e,behaviours:Na(n.buttonBehaviours,[zi.config({}),Ba.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:t("role").getOr("button")};var n=t("type").getOr("button"),e=t("role").map(function(n){return{role:n}}).getOr({});return x({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[tr("uid",undefined),Yt("dom"),tr("components",[]),Va("buttonBehaviours",[zi,Ba]),Qt("action"),Qt("role"),tr("eventOrder",{})]}),Ls=qr({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return Nr([jr(Qn(),v(!0))])},exhibit:function(n,e){return Ur({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Gs=function(n){var e=function(n,r){return n.replace(/\$\{([^{}]*)\}/g,function(n,e){var t=r[e];return function(n){var e=typeof n;return"string"==e||"number"==e}(t)?t.toString():n})}(n,{prefix:ji.prefix()});return vf(e)},Us=function(n,e,t,r){return zs.sketch({dom:xf(n,r),action:e,buttonBehaviours:xt(Bo([Ls.config({})]),t)})},$s={forToolbar:Us,forToolbarCommand:function(n,e){return Us(e,function(){n.execCommand(e)},{},n)},forToolbarStateAction:function(n,e,t,r){var o=bf(t);return Us(e,r,o,n)},forToolbarStateCommand:function(n,e){var t=bf(e);return Us(e,function(){n.execCommand(e)},t,n)},getToolbarIconButton:xf},Ws=L().deviceType.isTouch(),Xs=tf({schema:[Yt("dom")],name:"label"}),qs=wf("top-left"),Ys=wf("top"),Ks=wf("top-right"),Js=wf("right"),Qs=wf("bottom-right"),Zs=wf("bottom"),nl=wf("bottom-left"),el=[Xs,wf("left"),Js,Ys,Zs,qs,Ks,nl,Qs,ef({name:"thumb",defaults:v({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Nr([zr(Pn(),n,"spectrum"),zr(Hn(),n,"spectrum"),zr(zn(),n,"spectrum"),zr(Ln(),n,"spectrum"),zr(Gn(),n,"spectrum"),zr(Un(),n,"spectrum")])}}}),ef({schema:[or("mouseIsDown",function(){return Zn(!1)})],name:"spectrum",overrides:function(t){function r(e,n){return o.getValueFromEvent(n).map(function(n){return o.setValueFrom(e,t,n)})}var o=t.model.manager,n=Nr([_r(Pn(),r),_r(Hn(),r)]),e=Nr([_r(Ln(),r),_r(Gn(),function(n,e){t.mouseIsDown.get()&&r(n,e)})]);return{behaviours:Bo(Ws?[]:[Ba.config({mode:"special",onLeft:function(n){return o.onLeft(n,t)},onRight:function(n){return o.onRight(n,t)},onUp:function(n){return o.onUp(n,t)},onDown:function(n){return o.onDown(n,t)}}),zi.config({})]),events:Ws?n:e}}})],tl=/* */Object.freeze({onLoad:Sf,onUnload:Tf,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),rl=/* */Object.freeze({events:function(t,r){var n=t.resetOnDom?[Eo(function(n,e){Sf(n,t,r)}),Co(function(n,e){Tf(n,t,r)})]:[$r(t,r,Sf)];return Nr(n)}}),ol=/* */Object.freeze({memory:Of,dataset:kf,manual:function(){return Ao({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),il=[Qt("initialValue"),Yt("getFallbackEntry"),Yt("getDataKey"),Yt("setValue"),ni("manager",{setValue:Ef,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){Ef(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:kf})],ul=[Yt("getValue"),tr("setValue",w),Qt("initialValue"),ni("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:w,state:Fo.init})],cl=[Qt("initialValue"),ni("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:Of})],al=[rr("store",{mode:"memory"},qt("mode",{memory:cl,manual:ul,dataset:il})),Ko("onSetValue"),tr("resetOnDom",!1)],fl=qr({fields:al,name:"representing",active:rl,apis:tl,extra:{setValueFrom:function(n,e){var t=fl.getValue(e);fl.setValue(n,t)}},state:ol}),sl=Ui("width",function(n){return n.dom().offsetWidth}),ll=function(t,r){return{left:v(t),top:v(r),translate:function(n,e){return ll(t+n,r+e)}}},dl=ll,ml=L().deviceType.isTouch(),gl=v("slider.change.value"),pl="left",hl=Ss(-1),vl=Ss(1),yl=An.none,bl=An.none,xl={"top-left":An.none(),top:An.none(),"top-right":An.none(),right:An.some(function(n,e){ts(n,Zf(Nf(e)))}),"bottom-right":An.none(),bottom:An.none(),"bottom-left":An.none(),left:An.some(function(n,e){ts(n,Zf(Ff(e)))})},wl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=ws(n,e,t),o=xs(r);return bs(n,o),r},setToMin:function(n,e){var t=If(e);bs(n,xs(t))},setToMax:function(n,e){var t=Bf(e);bs(n,xs(t))},findValueOfOffset:ws,getValueFromEvent:function(n){return Mf(n).map(function(n){return n.left()})},findPositionOfValue:Ts,setPositionFromValue:function(n,e,t,r){var o=Qf(t),i=Ts(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=Df(e.element())/2;Li(e.element(),"left",i-u+"px")},onLeft:hl,onRight:vl,onUp:yl,onDown:bl,edgeActions:xl}),Sl=An.none,Tl=An.none,Ol=Cs(-1),kl=Cs(1),El={"top-left":An.none(),top:An.some(function(n,e){ts(n,ns(Af(e)))}),"top-right":An.none(),right:An.none(),"bottom-right":An.none(),bottom:An.some(function(n,e){ts(n,ns(jf(e)))}),"bottom-left":An.none(),left:An.none()},Cl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Es(n,e,t),o=ks(r);return Os(n,o),r},setToMin:function(n,e){var t=Rf(e);Os(n,ks(t))},setToMax:function(n,e){var t=Vf(e);Os(n,ks(t))},findValueOfOffset:Es,getValueFromEvent:function(n){return Mf(n).map(function(n){return n.top()})},findPositionOfValue:Ds,setPositionFromValue:function(n,e,t,r){var o=Qf(t),i=Ds(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),u=$i(e.element())/2;Li(e.element(),"top",i-u+"px")},onLeft:Sl,onRight:Tl,onUp:Ol,onDown:kl,edgeActions:El}),Dl=Rs(-1,!1),Ml=Rs(1,!1),Il=Rs(-1,!0),Rl=Rs(1,!0),Fl={"top-left":An.some(function(n,e){ts(n,es(Ff(e),Af(e)))}),top:An.some(function(n,e){ts(n,es(zf(e),Af(e)))}),"top-right":An.some(function(n,e){ts(n,es(Nf(e),Af(e)))}),right:An.some(function(n,e){ts(n,es(Nf(e),Lf(e)))}),"bottom-right":An.some(function(n,e){ts(n,es(Nf(e),jf(e)))}),bottom:An.some(function(n,e){ts(n,es(zf(e),jf(e)))}),"bottom-left":An.some(function(n,e){ts(n,es(Ff(e),jf(e)))}),left:An.some(function(n,e){ts(n,es(Ff(e),Lf(e)))})},Al=/* */Object.freeze({setValueFrom:function(n,e,t){var r=ws(n,e,t.left()),o=Es(n,e,t.top()),i=Is(r,o);return Ms(n,i),i},setToMin:function(n,e){var t=If(e),r=Rf(e);Ms(n,Is(t,r))},setToMax:function(n,e){var t=Bf(e),r=Vf(e);Ms(n,Is(t,r))},getValueFromEvent:function(n){return Mf(n)},setPositionFromValue:function(n,e,t,r){var o=Qf(t),i=Ts(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=Ds(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),c=Df(e.element())/2,a=$i(e.element())/2;Li(e.element(),"left",i-c+"px"),Li(e.element(),"top",u-a+"px")},onLeft:Dl,onRight:Ml,onUp:Il,onDown:Rl,edgeActions:Fl}),Bl=L().deviceType.isTouch(),Vl=[tr("stepSize",1),tr("onChange",w),tr("onChoose",w),tr("onInit",w),tr("onDragStart",w),tr("onDragEnd",w),tr("snapToGrid",!1),tr("rounded",!0),Qt("snapStart"),Kt("model",qt("mode",{x:[tr("minX",0),tr("maxX",100),or("value",function(n){return Zn(n.mode.minX)}),Yt("getInitialValue"),ni("manager",wl)],y:[tr("minY",0),tr("maxY",100),or("value",function(n){return Zn(n.mode.minY)}),Yt("getInitialValue"),ni("manager",Cl)],xy:[tr("minX",0),tr("maxX",100),tr("minY",0),tr("maxY",100),or("value",function(n){return Zn({x:v(n.mode.minX),y:v(n.mode.minY)})}),Yt("getInitialValue"),ni("manager",Al)]})),dc("sliderBehaviours",[Ba,fl])].concat(Bl?[]:[or("mouseIsDown",function(){return Zn(!1)})]),Nl=L().deviceType.isTouch(),jl=hf({name:"Slider",configFields:Vl,partFields:el,factory:function(i,n,e,t){function u(n){return kc(n,i,"thumb")}function c(n){return kc(n,i,"spectrum")}function r(n){return Oc(n,i,"left-edge")}function o(n){return Oc(n,i,"right-edge")}function a(n){return Oc(n,i,"top-edge")}function f(n){return Oc(n,i,"bottom-edge")}function s(n,e){m.setPositionFromValue(n,e,i,{getLeftEdge:r,getRightEdge:o,getTopEdge:a,getBottomEdge:f,getSpectrum:c})}function l(n,e){d.value.set(e);var t=u(n);return s(n,t),i.onChange(n,t,e),An.some(!0)}var d=i.model,m=d.manager,g=[_r(Pn(),function(n,e){i.onDragStart(n,u(n))}),_r(zn(),function(n,e){i.onDragEnd(n,u(n))})],p=[_r(Ln(),function(n,e){e.stop(),i.onDragStart(n,u(n)),i.mouseIsDown.set(!0)}),_r(Un(),function(n,e){i.onDragEnd(n,u(n))})],h=Nl?g:p;return{uid:i.uid,dom:i.dom,components:n,behaviours:gc(i.sliderBehaviours,A([Nl?[]:[Ba.config({mode:"special",focusIn:function(n){return Oc(n,i,"spectrum").map(Ba.focusIn).map(v(!0))}})],[fl.config({store:{mode:"manual",getValue:function(n){return d.value.get()}}}),Ei.config({channels:{"mouse.released":{onReceive:function(t,n){function e(){Oc(t,i,"thumb").each(function(n){var e=d.value.get();i.onChoose(t,n,e)})}if(Nl)e();else{var r=i.mouseIsDown.get();i.mouseIsDown.set(!1),r&&e()}}}}})]])),events:Nr([_r(gl(),function(n,e){l(n,e.event().value())}),Eo(function(n,e){var t=d.getInitialValue();d.value.set(t);var r=u(n);s(n,r);var o=c(n);i.onInit(n,r,o,d.value.get())})].concat(h)),apis:{resetToMin:function(n){m.setToMin(n,i)},resetToMax:function(n){m.setToMax(n,i)},changeValue:l,refresh:s},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),_l=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return Fs(n,"color-levels",function(){return As(e)},r)},Pl=Pt([Yt("getInitialValue"),Yt("onChange"),Yt("category"),Yt("sizes")]),Hl=function(n){var o=Xt("SizeSlider",Pl,n);return jl.sketch({dom:{tag:"div",classes:[ji.resolve("slider-"+o.category+"-size-container"),ji.resolve("slider"),ji.resolve("slider-size-container")]},onChange:function(n,e,t){var r=t.x();!function(n){return 0<=n&&n<o.sizes.length}(r)||o.onChange(r)},onDragStart:function(n,e){Fi.on(e)},onDragEnd:function(n,e){Fi.off(e)},model:{mode:"x",minX:0,maxX:o.sizes.length-1,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},stepSize:1,snapToGrid:!0,sliderBehaviours:Bo([Bi(jl.refresh)]),components:[jl.parts().spectrum({dom:Gs('<div class="${prefix}-slider-size-container"></div>'),components:[yf('<div class="${prefix}-slider-size-line"></div>')]}),jl.parts().thumb({dom:Gs('<div class="${prefix}-slider-thumb"></div>'),behaviours:Bo([Fi.config({toggleClass:ji.resolve("thumb-active")})])})]})},zl=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Ll={candidates:v(zl),get:function(n){return function(e){return F(zl,function(n){return n===e})}(Bs(n)).getOr(2)},apply:function(e,n){(function(n){return An.from(zl[n])})(n).each(function(n){!function(n,e){Bs(n)!==e&&n.execCommand("fontSize",!1,e)}(e,n)})}},Gl=Ll.candidates(),Ul=window.Promise?window.Promise:(js=$l.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){d.setTimeout(n,1)},_s=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},$l.prototype["catch"]=function(n){return this.then(null,n)},$l.prototype.then=function(t,r){var o=this;return new $l(function(n,e){Xl.call(o,new Jl(t,r,n,e))})},$l.all=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var a=Array.prototype.slice.call(1===n.length&&_s(n[0])?n[0]:n);return new $l(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},$l.resolve=function(e){return e&&"object"==typeof e&&e.constructor===$l?e:new $l(function(n){n(e)})},$l.reject=function(t){return new $l(function(n,e){e(t)})},$l.race=function(o){return new $l(function(n,e){for(var t=0,r=o;t<r.length;t++)r[t].then(n,e)})},$l);function $l(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],Ql(n,Wl(ql,this),Wl(Yl,this))}function Wl(n,e){return function(){return n.apply(e,arguments)}}function Xl(r){var o=this;null!==this._state?js(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function ql(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void Ql(Wl(e,n),Wl(ql,this),Wl(Yl,this))}this._state=!0,this._value=n,Kl.call(this)}catch(t){Yl.call(this,t)}}function Yl(n){this._state=!1,this._value=n,Kl.call(this)}function Kl(){for(var n=0,e=this._deferreds;n<e.length;n++){var t=e[n];Xl.call(this,t)}this._deferreds=[]}function Jl(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function Ql(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}function Zl(n){return function e(t){return new Ul(function(n){var e=new d.FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}(n).then(function(n){return n.split(",")[1]})}function nd(o,i){(function(n){return Zl(n)})(i).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(vc("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})}function ed(t){var e=Ns({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:Nr([Lr(Kn()),_r(Yn(),function(n,e){(function(n){var e=n.event(),t=e.raw().target.files||e.raw().dataTransfer.files;return An.from(t[0])})(e).each(function(n){nd(t,n)})})])});return zs.sketch({dom:$s.getToolbarIconButton("image",t),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})}function td(n){return n.dom().textContent}function rd(n){return 0<n.length}function od(n){return n===undefined||null===n?"":n}function id(n,e,t){return t.text.toOption().filter(rd).fold(function(){return function(n){return Qr(n,"href")===td(n)}(n)?An.some(e):An.none()},An.some)}function ud(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)}function cd(n){return n.dom().value}function ad(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e}function fd(n){return x(x({},function(n){return Bo([zi.config({onFocus:!1===n.selectOnFocus?w:function(n){var e=n.element(),t=cd(e);e.dom().setSelectionRange(0,t.length)}})])}(n)),gc(n.inputBehaviours,[fl.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return cd(n.element())},setValue:function(n,e){cd(n.element())!==e&&ad(n.element(),e)}},onSetValue:n.onSetValue})]))}function sd(n,e){var t=Ns(vm.sketch({inputAttributes:{placeholder:wm.translate(e)},onSetValue:function(n,e){G(n,qn())},inputBehaviours:Bo([mm.config({find:An.some}),xm.config({}),Ba.config({mode:"execution"})]),selectOnFocus:!1})),r=Ns(zs.sketch({dom:Gs('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);fl.setValue(e,"")}}));return{name:n,spec:gm.sketch({dom:Gs('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:Bo([Fi.config({toggleClass:ji.resolve("input-container-empty")}),mm.config({find:function(n){return An.some(t.get(n))}}),sm("input-clearing",[_r(qn(),function(n){var e=t.get(n);(0<fl.getValue(e).length?Fi.off:Fi.on)(n)})])])})}}function ld(n,e,t){e.disabled&&Tm(n,e)}function dd(n,e){return!0===e.useNative&&k(Sm,q(n.element()))}function md(n){Kr(n.element(),"disabled","disabled")}function gd(n){no(n.element(),"disabled")}function pd(n){Kr(n.element(),"aria-disabled","true")}function hd(n){Kr(n.element(),"aria-disabled","false")}function vd(e,n,t){n.disableClass.each(function(n){co(e.element(),n)}),(dd(e,n)?gd:hd)(e),n.onEnabled(e)}function yd(n,e){return dd(n,e)?function(n){return Zr(n.element(),"disabled")}(n):function(n){return"true"===Qr(n.element(),"aria-disabled")}(n)}function bd(n){return"<alloy.field."+n+">"}function xd(){function e(){t.get().each(function(n){n.destroy()})}var t=Zn(An.none());return{clear:function(){e(),t.set(An.none())},isSet:function(){return t.get().isSome()},set:function(n){e(),t.set(An.some(n))},run:function(n){t.get().each(n)}}}function wd(){var e=Zn(An.none());return{clear:function(){e.set(An.none())},set:function(n){e.set(An.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}}function Sd(n){function r(e,n,t){return zs.sketch({dom:Gs('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){U(n,u,{direction:e})},buttonBehaviours:Bo([Cm.config({disableClass:ji.resolve("toolbar-navigation-disabled"),disabled:!t})])})}function o(n,o){var i=qi(n.element(),"."+ji.resolve("serialised-dialog-screen"));Ji(n.element(),"."+ji.resolve("serialised-dialog-chain")).each(function(r){0<=c.state.currentScreen.get()+o&&c.state.currentScreen.get()+o<i.length&&(mi(r,"left").each(function(n){var e=parseInt(n,10),t=Df(i[0]);Li(r,"left",e-o*t+"px")}),c.state.currentScreen.set(c.state.currentScreen.get()+o))})}function i(e){var n=qi(e.element(),"input");An.from(n[c.state.currentScreen.get()]).each(function(n){e.getSystem().getByDom(n).each(function(n){!function(n,e){n.getSystem().triggerFocus(e,n.element())}(e,n.element())})});var t=f.get(e);Eu.highlightAt(t,c.state.currentScreen.get())}var u="navigateEvent",e=Or([Yt("fields"),tr("maxFieldIndex",n.fields.length-1),Yt("onExecute"),Yt("getInitialValue"),or("state",function(){return{dialogSwipeState:wd(),currentScreen:Zn(0)}})]),c=Xt("SerialisedDialog",e,n),a=Ns(Im(function(t){return{dom:Gs('<div class="${prefix}-serialised-dialog"></div>'),components:[gm.sketch({dom:Gs('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:ke(c.fields,function(n,e){return e<=c.maxFieldIndex?gm.sketch({dom:Gs('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[r(-1,"previous",0<e),t.field(n.name,n.spec),r(1,"next",e<c.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:Bo([Bi(function(n,e){!function(n,e){Ji(n.element(),"."+ji.resolve("serialised-dialog-chain")).each(function(n){Li(n,"left",-c.state.currentScreen.get()*e.width+"px")})}(n,e)}),Ba.config({mode:"special",focusIn:function(n){i(n)},onTab:function(n){return o(n,1),An.some(!0)},onShiftTab:function(n){return o(n,-1),An.some(!0)}}),sm("form-events",[Eo(function(e,n){c.state.currentScreen.set(0),c.state.dialogSwipeState.clear();var t=f.get(e);Eu.highlightFirst(t),c.getInitialValue(e).each(function(n){fl.setValue(e,n)})}),Mo(c.onExecute),_r(Jn(),function(n,e){"left"===e.event().raw().propertyName&&i(n)}),_r(u,function(n,e){var t=e.event().direction();o(n,t)})])])}})),f=Ns({dom:Gs('<div class="${prefix}-dot-container"></div>'),behaviours:Bo([Eu.config({highlightClass:ji.resolve("dot-active"),itemClass:ji.resolve("dot-item")})]),components:B(c.fields,function(n,e){return e<=c.maxFieldIndex?[yf('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Gs('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),f.asSpec()],behaviours:Bo([Ba.config({mode:"special",focusIn:function(n){var e=a.get(n);Ba.focusIn(e)}}),sm("serializer-wrapper-events",[_r(Pn(),function(n,e){var t=e.event();c.state.dialogSwipeState.set(Rm(t.raw().touches[0].clientX))}),_r(Hn(),function(n,e){var t=e.event();c.state.dialogSwipeState.on(function(n){e.event().prevent(),c.state.dialogSwipeState.set(Fm(n,t.raw().touches[0].clientX))})}),_r(zn(),function(r){c.state.dialogSwipeState.on(function(n){var e=a.get(r),t=-1*Am(n);o(e,t)})})])])}}function Td(e){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+bo(e().element())+" is not in context.")}}return{debugInfo:v("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:v(!1)}}function Od(n,o){var i={};return Nn(n,function(n,r){Nn(n,function(n,e){var t=kn(e,[])(i);i[e]=t.concat([o(r,n)])})}),i}function kd(n){return n.cHandler}function Ed(n,e){return{name:v(n),handler:v(e)}}function Cd(n,e,t){var r=x(x({},t),function(n,e){var t={};return C(n,function(n){t[n.name()]=n.handlers(e)}),t}(e,n));return Od(r,Ed)}function Dd(n){var i=function(n){return xe(n)?{can:v(!0),abort:v(!1),run:n}:n}(n);return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}}function Md(n,e,t){var r=e[t];return r?function(u,c,n,a){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[c](),r=e[c](),o=a.indexOf(t),i=a.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(a,null,2));return o<i?-1:i<o?1:0});return vt.value(t)}catch(r){return vt.error([r])}}("Event: "+t,"name",n,r).map(function(n){var e=ke(n,function(n){return n.handler()});return cr(e)}):function(n,e){return vt.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(ke(e,function(n){return n.name()}),null,2)])}(t,n)}function Id(n){return $t("custom.definition",Or([Cr("dom","dom",Tt(),Or([Yt("tag"),tr("styles",{}),tr("classes",[]),tr("attributes",{}),Qt("value"),Qt("innerHtml")])),Yt("components"),Yt("uid"),tr("events",{}),tr("apis",{}),Cr("eventOrder","eventOrder",function(n){return St.mergeWithThunk(v(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),Ir()),Qt("domModification")]),n)}function Rd(e,n){C(n,function(n){io(e,n)})}function Fd(e,n){C(n,function(n){co(e,n)})}function Ad(n,e){return function(e,n){var t=ke(n,function(n){return nr(n.name(),[Yt("config"),tr("state",Fo)])}),r=$t("component.behaviours",Or(t),e.behaviours).fold(function(n){throw new Error(Mr(n)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(n){return n});return{list:n,data:S(r,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})}}(n,e)}function Bd(n){var e=function(n){var e=kn("behaviours",{})(n),t=D(Bn(e),function(n){return e[n]!==undefined});return ke(t,function(n){return e[n].me})}(n);return Ad(n,e)}function Vd(n,e,t){var r=function(n){return x(x({},n.dom),{uid:n.uid,domChildren:ke(n.components,function(n){return n.element()})})}(n),o=function(n){return n.domModification.fold(function(){return Ur({})},Ur)}(n),i={"alloy.base.modification":o};return function(n,e){return x(x({},n),{attributes:x(x({},n.attributes),e.attributes),styles:x(x({},n.styles),e.styles),classes:n.classes.concat(e.classes)})}(r,0<e.length?function(e,n,t,r){var o=x({},n);C(t,function(n){o[n.name()]=n.exhibit(e,r)});function i(n){return M(n,function(n,e){return x(x({},e.modification),n)},{})}var u=Od(o,function(n,e){return{name:n,modification:e}}),c=M(u.classes,function(n,e){return e.modification.concat(n)},[]),a=i(u.attributes),f=i(u.styles);return Ur({classes:c,attributes:a,styles:f})}(t,i,e,r):o)}function Nd(n,e,t){var r={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,e,t,r){var o=Cd(n,t,r);return zm(o,e)}(t,n.eventOrder,e,r).getOrDie()}function jd(t){function n(){return s}var r=Zn(Pm),e=Wt(Id(t)),o=Bd(t),i=function(n){return n.list}(o),u=function(n){return n.data}(o),c=function(n){var e=Je.fromTag(n.tag);Jr(e,n.attributes),Rd(e,n.classes),li(e,n.styles),n.innerHtml.each(function(n){return ho(e,n)});var t=n.domChildren;return gn(e,t),n.value.each(function(n){ad(e,n)}),n.uid,df(e,n.uid),e}(Vd(e,i,u)),a=Nd(e,i,u),f=Zn(e.components),s={getSystem:r.get,config:function(n){var e=u;return(xe(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return xe(u[n.name()])},spec:v(t),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return e.apis},connect:function(n){r.set(n)},disconnect:function(){r.set(Td(n))},element:v(c),syncComponents:function(){var n=at(c),e=B(n,function(n){return r.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(e)},components:f.get,events:v(a)};return s}function _d(n){var e=_m(n),t=e.events,r=u(e,["events"]),o=function(n){var e=kn("components",[])(n);return ke(e,Um)}(r),i=x(x({},r),{events:x(x({},jm),t),components:o});return vt.value(jd(i))}function Pd(n){var e=Je.fromText(n);return Lm({element:e})}function Hd(n){(go(n.element()).isNone()||zi.isFocused(n))&&(zi.isFocused(n)||zi.focus(n),U(n,Wm,{item:n}))}function zd(n){U(n,Xm,{item:n})}function Ld(n,e,t,r){var o=n.getSystem().build(r);mt(n,o,t)}function Gd(n,e,t,r){var o=ig(n);R(o,function(n){return cn(r.element(),n.element())}).each(yn)}function Ud(e,n,t,r,o){var i=ig(e);return An.from(i[r]).map(function(n){return Gd(e,0,0,n),o.each(function(n){Ld(e,0,function(n,e){!function(n,e,t){sn(n,t).fold(function(){ft(n,e)},function(n){ln(n,e)})}(n,e,r)},n)}),n})}function $d(n,e){var t={};Nn(n,function(n,e){C(n,function(n){t[n]=e})});var r=e,o=function(n){return jn(n,function(n,e){return{k:n,v:e}})}(e),i=S(o,function(n,e){return[e].concat(cg(t,r,o,e))});return S(t,function(n){return Dt(i,n).getOr([n])})}function Wd(n,e,t,r){return Dt(e.routes,r.start).bind(function(n){return Dt(n,r.destination)})}function Xd(t,r,n){(function(e,t,r){return mg(e,t).bind(function(n){return dg(e,t,r,n)})})(t,r,n).each(function(n){var e=n.transition;co(t.element(),e.transitionClass),no(t.element(),r.destinationAttr)})}function qd(n,e,t,r){Xd(n,e,t),Zr(n.element(),e.stateAttr)&&Qr(n.element(),e.stateAttr)!==r&&e.onFinish(n,r),Kr(n.element(),e.stateAttr,r)}function Yd(n){return Dt(n,"format").getOr(n.title)}function Kd(n){return Mn(n,"items")?function(n){var e=xt(On(n,["items"]),{menu:!0}),t=Tg(n.items);return{item:e,menus:xt(t.menus,En(n.title,t.items)),expansions:xt(t.expansions,En(n.title,n.title))}}(n):{item:n,menus:{},expansions:{}}}function Jd(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]}function Qd(n){var e=n.toolbar!==undefined?n.toolbar:Cg;return ye(e)?Dg(e):Jd(e)}function Zd(n){function e(){n.stopPropagation()}function t(){n.preventDefault()}var r=Je.fromDom(n.target),o=i(t,e);return function(n,e,t,r,o,i,u){return{target:v(n),x:v(e),y:v(t),stop:r,prevent:o,kill:i,raw:v(u)}}(r,n.clientX,n.clientY,e,t,o,n)}function nm(n,e,t,r,o){var i=function(e,t){return function(n){e(n)&&t(Zd(n))}}(t,r);return n.dom().addEventListener(e,i,o),{unbind:l(Rg,n,e,i,o)}}function em(n,e,t){return function(n,e,t,r){return nm(n,e,t,r,!1)}(n,e,Fg,t)}function tm(n,e,t){return function(n,e,t,r){return nm(n,e,t,r,!0)}(n,e,Fg,t)}function rm(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:v(e)}}var om,im,um=function(n){var e=Je.fromDom(n.selection.getStart());return Qi(e,"a")},cm={getInfo:function(n){return um(n).fold(function(){return function(n){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:An.none()}}(n)},function(n){return function(n){var e=td(n),t=Qr(n,"href"),r=Qr(n,"title"),o=Qr(n,"target");return{url:od(t),text:e!==t?od(e):"",title:od(r),target:od(o),link:An.some(n)}}(n)})},applyInfo:function(e,o){o.url.toOption().filter(rd).fold(function(){!function(e,n){n.link.bind(y).each(function(n){e.execCommand("unlink")})}(e,o)},function(t){var r=function(n,e){var t={};return t.href=n,e.title.toOption().filter(rd).each(function(n){t.title=n}),e.target.toOption().filter(rd).each(function(n){t.target=n}),t}(t,o);o.link.bind(y).fold(function(){var n=o.text.toOption().filter(rd).getOr(t);e.insertContent(e.dom.createHTML("a",r,e.dom.encode(n)))},function(e){var n=id(e,t,o);Jr(e,r),n.each(function(n){!function(n,e){n.dom().textContent=e}(e,n)})})})},query:um},am=L(),fm=function(n,e){(am.os.isAndroid()?ud:t)(e,n)},sm=function(n,e){return{key:n,value:{config:{},me:function(n,e){var t=Nr(e);return qr({fields:[Yt("enabled")],name:n,active:{events:v(t)}})}(n,e),configAsRaw:v({}),initialConfig:{},state:Fo}}},lm=/* */Object.freeze({getCurrent:function(n,e,t){return e.find(n)}}),dm=[Yt("find")],mm=qr({fields:dm,name:"composing",apis:lm}),gm=pf({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,r=u(e,["attributes"]);return{uid:n.uid,dom:x({tag:"div",attributes:x({role:"presentation"},t)},r),components:n.components,behaviours:mc(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[tr("components",[]),dc("containerBehaviours",[]),tr("events",{}),tr("domModification",{}),tr("eventOrder",{})]}),pm=pf({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:Na(t.dataBehaviours,[fl.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),mm.config({find:An.some})]),events:Nr([Eo(function(n,e){fl.setValue(n,t.getInitialValue())})])}},configFields:[Yt("uid"),Yt("dom"),Yt("getInitialValue"),Va("dataBehaviours",[fl,mm])]}),hm=v([Qt("data"),tr("inputAttributes",{}),tr("inputStyles",{}),tr("tag","input"),tr("inputClasses",[]),Ko("onSetValue"),tr("styles",{}),tr("eventOrder",{}),dc("inputBehaviours",[fl,zi]),tr("selectOnFocus",!0)]),vm=pf({name:"Input",configFields:hm(),factory:function(n,e){return{uid:n.uid,dom:function(n){return{tag:n.tag,attributes:x({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}(n),components:[],behaviours:fd(n),eventOrder:n.eventOrder}}}),ym=/* */Object.freeze({exhibit:function(n,e){return Ur({attributes:Cn([{key:e.tabAttr,value:"true"}])})}}),bm=[tr("tabAttr","data-alloy-tabstop")],xm=qr({fields:bm,name:"tabstopping",active:ym}),wm=tinymce.util.Tools.resolve("tinymce.util.I18n"),Sm=["input","button","textarea","select"],Tm=function(e,n,t){n.disableClass.each(function(n){io(e.element(),n)}),(dd(e,n)?md:pd)(e),n.onDisabled(e)},Om=/* */Object.freeze({enable:vd,disable:Tm,isDisabled:yd,onLoad:ld,set:function(n,e,t,r){(r?Tm:vd)(n,e,t)}}),km=/* */Object.freeze({exhibit:function(n,e,t){return Ur({classes:e.disabled?e.disableClass.map(_).getOr([]):[]})},events:function(t,n){return Nr([jr(_e(),function(n,e){return yd(n,t)}),$r(t,n,ld)])}}),Em=[tr("disabled",!1),tr("useNative",!0),Qt("disableClass"),Ko("onDisabled"),Ko("onEnabled")],Cm=qr({fields:Em,name:"disabling",active:km,apis:Om}),Dm=[dc("formBehaviours",[fl])],Mm=function(r,n,e){return{uid:r.uid,dom:r.dom,components:n,behaviours:gc(r.formBehaviours,[fl.config({store:{mode:"manual",getValue:function(n){var e=function(n,e){var t=n.getSystem();return S(e.partUids,function(n,e){return v(t.getByUid(n))})}(n,r);return S(e,function(n,e){return n().bind(function(n){return function(n,e){return n.fold(function(){return vt.error(e)},vt.value)}(mm.getCurrent(n),"missing current")}).map(fl.getValue)})},setValue:function(t,n){Nn(n,function(e,n){Oc(t,r,n).each(function(n){mm.getCurrent(n).each(function(n){fl.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return Oc(n,r,e).bind(mm.getCurrent)}}}},Im=(Mc(function(n,e,t){return n.getField(e,t)}),function(n){var t,e=(t=[],{field:function(n,e){return t.push(n),function(n,e,t){return{uiType:La(),owner:n,name:e,config:t,validated:{}}}("form",bd(n),e)},record:function(){return t}}),r=n(e),o=e.record(),i=ke(o,function(n){return ef({name:n,pname:bd(n)})});return Fc("form",Dm,i,Mm,r)}),Rm=function(n){return{xValue:n,points:[]}},Fm=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},Am=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},Bm=X(function(t,r){return[{label:"the link group",items:[Sd({fields:[sd("url","Type or paste URL"),sd("text","Link text"),sd("title","Link title"),sd("target","Link target"),function(n){return{name:n,spec:pm.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return An.none()}})}}("link")],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return An.some(cm.getInfo(r))},onExecute:function(n){var e=fl.getValue(n);cm.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}]}),Vm=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],Nm=Nr([(om=Be(),im=function(n,e){var t=e.event().originator(),r=e.event().target();return!function(n,e,t){return cn(e,n.element())&&!cn(e,t)}(n,t,r)||(d.console.warn(Be()+" did not get interpreted by the desired target. \nOriginator: "+bo(t)+"\nTarget: "+bo(r)+"\nCheck the "+Be()+" event handlers"),!1)},{key:om,value:ur({can:im})})]),jm=/* */Object.freeze({events:Nm}),_m=y,Pm=Td(),Hm=function(n,e){return function(n,e){return{cHandler:n,purpose:v(e)}}(l.apply(undefined,[n.handler].concat(e)),n.purpose())},zm=function(n,i){var e=_n(n,function(r,o){return(1===r.length?vt.value(r[0].handler()):Md(r,i,o)).map(function(n){var e=Dd(n),t=1<r.length?D(i[o],function(e){return E(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return En(o,function(n,e){return{handler:n,purpose:v(e)}}(e,t))})});return Dn(e,{})},Lm=function(n){var e=Xt("external.component",Pt([Yt("element"),Qt("uid")]),n),t=Zn(Td());e.uid.each(function(n){df(e.element,n)});var r={getSystem:t.get,config:An.none,hasConfigured:v(!1),connect:function(n){t.set(n)},disconnect:function(){t.set(Td(function(){return r}))},getApis:function(){return{}},element:v(e.element),spec:v(n),readState:v("No state"),syncComponents:w,components:v([]),events:v({})};return Dc(r)},Gm=Ic,Um=function(e){return function(n){return Dt(n,cf)}(e).fold(function(){var n=e.hasOwnProperty("uid")?e:x({uid:Gm("")},e);return _d(n).getOrDie()},function(n){return n})},$m=Dc,Wm="alloy.item-hover",Xm="alloy.item-focus",qm=v(Wm),Ym=v(Xm),Km=[Yt("data"),Yt("components"),Yt("dom"),tr("hasSubmenu",!1),Qt("toggling"),Va("itemBehaviours",[Fi,zi,Ba,fl]),tr("ignoreFocus",!1),tr("domModification",{}),ni("builder",function(n){return{dom:n.dom,domModification:x(x({},n.domModification),{attributes:x(x(x({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Na(n.itemBehaviours,[n.toggling.fold(Fi.revoke,function(n){return Fi.config(x({aria:{mode:"checked"}},n))}),zi.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){zd(n)}}),Ba.config({mode:"execution"}),fl.config({store:{mode:"memory",initialValue:n.data}}),sm("item-type-events",[_r(ze(),$),Lr(Ln()),_r($n(),Hd),_r(Pe(),zi.focus)])]),components:n.components,eventOrder:n.eventOrder}}),tr("eventOrder",{})],Jm=[Yt("dom"),Yt("components"),ni("builder",function(n){return{dom:n.dom,components:n.components,events:Nr([function(n){return _r(n,function(n,e){e.stop()})}(Pe())])}})],Qm=v([ef({name:"widget",overrides:function(e){return{behaviours:Bo([fl.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),Zm=[Yt("uid"),Yt("data"),Yt("components"),Yt("dom"),tr("autofocus",!1),tr("ignoreFocus",!1),Va("widgetBehaviours",[fl,zi,Ba]),tr("domModification",{}),Cc(Qm()),ni("builder",function(t){function r(n){return Oc(n,t,"widget").map(function(n){return Ba.focusIn(n),n})}function n(n,e){return Mu(e.event().target())||t.autofocus&&e.setSource(n.element()),An.none()}var e=Sc(0,t,Qm()),o=Tc("item-widget",t,e.internals());return{dom:t.dom,components:o,domModification:t.domModification,events:Nr([Mo(function(n,e){r(n).each(function(n){e.stop()})}),_r($n(),Hd),_r(Pe(),function(n,e){t.autofocus?r(n):zi.focus(n)})]),behaviours:Na(t.widgetBehaviours,[fl.config({store:{mode:"memory",initialValue:t.data}}),zi.config({ignore:t.ignoreFocus,onFocus:function(n){zd(n)}}),Ba.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:jo(),onLeft:n,onRight:n,onEscape:function(n,e){return zi.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element()),An.none()):(zi.focus(n),An.some(!0))}})])}})],ng=qt("type",{widget:Zm,item:Km,separator:Jm}),eg=v([rf({factory:{sketch:function(n){var e=Xt("menu.spec item",ng,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:x(x({},e),{uid:Ic("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),tg=v([Yt("value"),Yt("items"),Yt("dom"),Yt("components"),tr("eventOrder",{}),dc("menuBehaviours",[Eu,fl,mm,Ba]),rr("movement",{mode:"menu",moveOnTab:!0},qt("mode",{grid:[Oi(),ni("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[ni("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Yt("rowSelector")],menu:[tr("moveOnTab",!0),ni("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),Kt("markers",Si()),tr("fakeFocus",!1),tr("focusManager",gu()),Ko("onHighlight")]),rg=v("alloy.menu-focus"),og=hf({name:"Menu",configFields:tg(),partFields:eg(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:gc(n.menuBehaviours,[Eu.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),fl.config({store:{mode:"memory",initialValue:n.value}}),mm.config({find:An.some}),Ba.config(n.movement.config(n,n.movement))]),events:Nr([_r(Ym(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){Eu.highlight(e,n),t.stop(),U(e,rg(),{menu:e,item:n})})}),_r(qm(),function(n,e){var t=e.event().item();Eu.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),ig=function(n,e){return n.components()},ug=qr({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,e,t,r){Ld(n,0,ft,r)},prepend:function(n,e,t,r){Ld(n,0,mn,r)},remove:Gd,replaceAt:Ud,replaceBy:function(e,n,t,r,o){var i=ig(e);return F(i,r).bind(function(n){return Ud(e,0,0,n,o)})},set:function(e,n,t,r){!function(n,t){var r=an(t),e=mo(r).bind(function(e){function n(n){return cn(e,n)}return n(t)?An.some(t):Vr(t,n)}),o=n(t);e.each(function(e){mo(r).filter(function(n){return cn(n,e)}).fold(function(){so(e)},w)})}(function(){var n=ke(r,e.getSystem().build);vn(e,n)},e.element())},contents:ig})}),cg=function(t,r,o,n){return Dt(o,n).bind(function(n){return Dt(t,n).bind(function(n){var e=cg(t,r,o,n);return An.some([n].concat(e))})}).getOr([])},ag=function(n){return"prepared"===n.type?An.some(n.menu):An.none()},fg={init:function(){function o(t){return function(n,e){for(var t=Bn(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return An.some(u)}return An.none()}(i.get(),function(n,e){return n===t})}var i=Zn({}),u=Zn({}),c=Zn({}),a=Zn(An.none()),f=Zn({}),s=function(n){return e(n).bind(ag)},e=function(n){return Dt(u.get(),n)},t=function(n){return Dt(i.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(x(x({},u.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){a.set(An.some(n)),i.set(t),u.set(e),f.set(r);var o=$d(r,t);c.set(o)},expand:function(t){return Dt(i.get(),t).map(function(n){var e=Dt(c.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return Dt(c.get(),n)},collapse:function(n){return Dt(c.get(),n).bind(function(n){return 1<n.length?An.some(n.slice(1)):An.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=f.get();return j(Bn(e),n)},getPrimary:function(){return a.get().bind(s)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),c.set({}),a.set(An.none())},isClear:function(){return a.get().isNone()},getTriggeringPath:function(n,r){var e=D(t(n).toArray(),function(n){return s(n).isSome()});return Dt(c.get(),n).bind(function(n){var t=N(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var r=n[t];if(!r.isSome())return An.none();e.push(r.getOrDie())}return An.some(e)}(B(t,function(n,e){return function(n,t,r){return s(n).bind(function(e){return o(n).bind(function(n){return t(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:r}})})})}(n,r,t.slice(0,e+1)).fold(function(){return a.get().is(n)?[]:[An.none()]},function(n){return[An.some(n)]})}))})}}},extractPreparedMenu:ag},sg=v("collapse-item"),lg=pf({name:"TieredMenu",configFields:[Zo("onExecute"),Zo("onEscape"),Qo("onOpenMenu"),Qo("onOpenSubmenu"),Qo("onRepositionMenu"),Ko("onCollapseMenu"),tr("highlightImmediately",!0),Jt("data",[Yt("primary"),Yt("menus"),Yt("expansions")]),tr("fakeFocus",!1),Ko("onHighlight"),Ko("onHover"),Jt("markers",[Yt("backgroundMenu")].concat(xi()).concat(wi())),Yt("dom"),tr("navigateOnHover",!0),tr("stayInDom",!1),dc("tmenuBehaviours",[Ba,Eu,mm,ug]),tr("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(c,n){function r(r,o,n){return S(n,function(n,e){function t(){return og.sketch(x(x({dom:n.dom},n),{value:e,items:n.items,markers:c.markers,fakeFocus:c.fakeFocus,onHighlight:c.onHighlight,focusManager:c.fakeFocus?function(){function o(n){return Eu.getHighlighted(n).map(function(n){return n.element()})}return{get:o,set:function(e,n){var t=o(e);e.getSystem().getByDom(n).fold(w,function(n){Eu.highlight(e,n)});var r=o(e);mu(e,t,r)}}}():gu()}))}return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})}function a(n){return fl.getValue(n).value}function u(e,n){Eu.highlight(e,n),Eu.getHighlighted(n).orThunk(function(){return Eu.getFirst(n)}).each(function(n){W(e,n.element(),Pe())})}function f(e,n){return xo(ke(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?An.some(n.menu):An.none()})}))}function s(e,n,t){var r=f(n,n.otherMenus(t));C(r,function(n){Fd(n.element(),[c.markers.backgroundMenu]),c.stayInDom||ug.remove(e,n)})}function l(n,r){var e=function(r){return o.get().getOrThunk(function(){var t={},n=qi(r.element(),"."+c.markers.item),e=D(n,function(n){return"true"===Qr(n,"aria-haspopup")});return C(e,function(n){r.getSystem().getByDom(n).each(function(n){var e=a(n);t[e]=n})}),o.set(An.some(t)),t})}(n);Nn(e,function(n,e){var t=k(r,e);Kr(n.element(),"aria-expanded",t)})}function d(r,o,i){return An.from(i[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return An.none();var e=n.menu,t=f(o,i.slice(1));return C(t,function(n){io(n.element(),c.markers.backgroundMenu)}),K(e.element())||ug.append(r,$m(e)),Fd(e.element(),[c.markers.backgroundMenu]),u(r,e),s(r,o,i),An.some(e)})})}var m,e,o=Zn(An.none()),g=fg.init(),i=function(n){return S(c.data.menus,function(n,e){return B(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(e=m=m||{})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";function p(o,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=a(i);return g.expand(n).bind(function(r){return l(o,r),An.from(r[0]).bind(function(t){return g.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var r=n.getSystem().build(t.nbMenu());return g.setMenuBuilt(e,r),r}(o,t,n);return K(e.element())||ug.append(o,$m(e)),c.onOpenSubmenu(o,i,e,N(r)),u===m.HighlightSubmenu?(Eu.highlightFirst(e),d(o,g,r)):(Eu.dehighlightAll(e),An.some(i))})})})}function h(e,t){var n=a(t);return g.collapse(n).bind(function(n){return l(e,n),d(e,g,n).map(function(n){return c.onCollapseMenu(e,t,n),n})})}function t(t){return function(e,n){return Qi(n.getSource(),"."+c.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}}function v(n){return Eu.getHighlighted(n).bind(Eu.getHighlighted)}var y=Nr([_r(rg(),function(t,r){var n=r.event().item();g.lookupItem(a(n)).each(function(){var n=r.event().menu();Eu.highlight(t,n);var e=a(r.event().item());g.refresh(e).each(function(n){return s(t,g,n)})})}),Mo(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===a(n).indexOf("collapse-item")&&h(e,n),p(e,n,m.HighlightSubmenu).fold(function(){c.onExecute(e,n)},function(){})})}),Eo(function(e,n){(function(n){var e=r(n,c.data.primary,c.data.menus),t=i();return g.setContents(c.data.primary,e,c.data.expansions,t),g.getPrimary()})(e).each(function(n){ug.append(e,$m(n)),c.onOpenMenu(e,n),c.highlightImmediately&&u(e,n)})})].concat(c.navigateOnHover?[_r(qm(),function(n,e){var t=e.event().item();!function(e,n){var t=a(n);g.refresh(t).bind(function(n){return l(e,n),d(e,g,n)})}(n,t),p(n,t,m.HighlightParent),c.onHover(n,t)})]:[])),b={collapseMenu:function(e){v(e).each(function(n){h(e,n)})},highlightPrimary:function(e){g.getPrimary().each(function(n){u(e,n)})},repositionMenus:function(r){g.getPrimary().bind(function(e){return v(r).bind(function(n){var e=a(n),t=function(n){return _n(n,function(n){return n})}(g.getMenus()),r=xo(ke(t,fg.extractPreparedMenu));return g.getTriggeringPath(e,function(n){return function(n,e,t){return wo(e,function(n){if(!n.getSystem().isConnected())return An.none();var e=Eu.getCandidates(n);return R(e,function(n){return a(n)===t})})}(0,r,n)})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){(function(n){return An.from(n.components()[0]).filter(function(n){return"menu"===Qr(n.element(),"role")})})(r).each(function(n){c.onRepositionMenu(r,n,[])})},function(n){var e=n.primary,t=n.triggeringPath;c.onRepositionMenu(r,e,t)})}};return{uid:c.uid,dom:c.dom,markers:c.markers,behaviours:gc(c.tmenuBehaviours,[Ba.config({mode:"special",onRight:t(function(n,e){return Mu(e.element())?An.none():p(n,e,m.HighlightSubmenu)}),onLeft:t(function(n,e){return Mu(e.element())?An.none():h(n,e)}),onEscape:t(function(n,e){return h(n,e).orThunk(function(){return c.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){g.getPrimary().each(function(n){W(e,n.element(),Pe())})}}),Eu.config({highlightClass:c.markers.selectedMenu,itemClass:c.markers.menu}),mm.config({find:function(n){return Eu.getHighlighted(n)}}),ug.config({})]),eventOrder:c.eventOrder,apis:b,events:y}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:En(n,e),expansions:{}}},collapseItem:function(n){return{value:vc(sg()),meta:{text:n}}}}}),dg=function(n,e,t,r){return Wd(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},mg=function(n,e,t){var r=n.element();return Zr(r,e.destinationAttr)?An.some({start:Qr(n.element(),e.stateAttr),destination:Qr(n.element(),e.destinationAttr)}):An.none()},gg=/* */Object.freeze({findRoute:Wd,disableTransition:Xd,getCurrentRoute:mg,jumpTo:qd,progressTo:function(t,r,o,i){!function(n,e){Zr(n.element(),e.destinationAttr)&&(Kr(n.element(),e.stateAttr,Qr(n.element(),e.destinationAttr)),no(n.element(),e.destinationAttr))}(t,r);var n=function(n,e,t,r){return{start:Qr(n.element(),e.stateAttr),destination:r}}(t,r,0,i);dg(t,r,o,n).fold(function(){qd(t,r,o,i)},function(n){Xd(t,r,o);var e=n.transition;io(t.element(),e.transitionClass),Kr(t.element(),r.destinationAttr,i)})},getState:function(n,e,t){var r=n.element();return Zr(r,e.stateAttr)?An.some(Qr(r,e.stateAttr)):An.none()}}),pg=/* */Object.freeze({events:function(o,i){return Nr([_r(Jn(),function(t,n){var r=n.event().raw();mg(t,o).each(function(e){Wd(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(qd(t,o,i,e.destination),o.onTransition(t,e))})})})}),Eo(function(n,e){qd(n,o,i,o.initialState)})])}}),hg=[tr("destinationAttr","data-transitioning-destination"),tr("stateAttr","data-transitioning-state"),Yt("initialState"),Ko("onTransition"),Ko("onFinish"),Kt("routes",Ut(vt.value,Ut(vt.value,Pt([er("transition",[Yt("property"),Yt("transitionClass")])]))))],vg=qr({fields:hg,name:"transitioning",active:pg,apis:gg,extra:{createRoutes:function(n){var r={};return Nn(n,function(n,e){var t=e.split("<->");r[t[0]]=En(t[1],n),r[t[1]]=En(t[0],n)}),r},createBistate:function(n,e,t){return Cn([{key:n,value:En(e,t)},{key:e,value:En(n,t)}])},createTristate:function(n,e,t,r){return Cn([{key:n,value:Cn([{key:e,value:r},{key:t,value:r}])},{key:e,value:Cn([{key:n,value:r},{key:t,value:r}])},{key:t,value:Cn([{key:n,value:r},{key:e,value:r}])}])}}}),yg=ji.resolve("scrollable"),bg={register:function(n){io(n,yg)},deregister:function(n){co(n,yg)},scrollable:v(yg)},xg=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[ji.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:ji.resolve("format-matches"),selected:t},itemBehaviours:Bo(o?[]:[Ai(n,function(n,e){(e?Fi.on:Fi.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},wg=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[zs.sketch({dom:{tag:"div",classes:[ji.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[ji.resolve("styles-collapse-icon")]}},Pd(n)]:[Pd(n)],action:function(n){if(r){var e=t().get(n);lg.collapseMenu(e)}}}),{dom:{tag:"div",classes:[ji.resolve("styles-menu-items-container")]},components:[og.parts().items({})],behaviours:Bo([sm("adhoc-scrollable-menu",[Eo(function(n,e){Li(n.element(),"overflow-y","auto"),Li(n.element(),"-webkit-overflow-scrolling","touch"),bg.register(n.element())}),Co(function(n){gi(n.element(),"overflow-y"),gi(n.element(),"-webkit-overflow-scrolling"),bg.deregister(n.element())})])])}],items:e,menuBehaviours:Bo([vg.config({initialState:"after",routes:vg.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},Sg=function(r){var n=function(r,o){var n=wg("Styles",[].concat(ke(r.items,function(n){return xg(Yd(n),n.title,n.isSelected(),n.getPreview(),Mn(r.expansions,Yd(n)))})),o,!1),e=S(r.menus,function(n,e){var t=ke(n,function(n){return xg(Yd(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",Mn(r.expansions,Yd(n)))});return wg(e,t,o,!0)}),t=xt(e,En("styles",n));return{tmenu:lg.tieredData("styles",t,r.expansions)}}(r.formats,function(){return e}),e=Ns(lg.sketch({dom:{tag:"div",classes:[ji.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=fl.getValue(e);return r.handle(e,t.value),An.none()},onEscape:function(){return An.none()},onOpenMenu:function(n,e){var t=Df(n.element());Cf(e.element(),t),vg.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=Df(n.element()),o=Ki(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();Cf(t.element(),r),vg.progressTo(i,"before"),vg.jumpTo(t,"after"),vg.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Ki(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();vg.progressTo(o,"after"),vg.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:n.tmenu,markers:{backgroundMenu:ji.resolve("styles-background-menu"),menu:ji.resolve("styles-menu"),selectedMenu:ji.resolve("styles-selected-menu"),item:ji.resolve("styles-item"),selectedItem:ji.resolve("styles-selected-item")}}));return e.asSpec()},Tg=function(n){return M(n,function(n,e){var t=Kd(e);return{menus:xt(n.menus,t.menus),items:[t.item].concat(n.items),expansions:xt(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Og={expand:Tg},kg=function(r,n){function o(n){return function(){return r.formatter.match(n)}}function i(n){return function(){return r.formatter.getCssText(n)}}var e=Dt(n,"style_formats").getOr(Vm),t=function(n){return ke(n,function(n){if(Mn(n,"items")){var e=t(n.items);return xt(function(n){return xt(n,{isSelected:v(!1),getPreview:v("")})}(n),{items:e})}return Mn(n,"format")?function(n){return xt(n,{isSelected:o(n.format),getPreview:i(n.format)})}(n):function(n){var e=vc(n.title),t=xt(n,{format:e,isSelected:o(e),getPreview:i(e)});return r.formatter.register(e,t),t}(n)})};return t(e)},Eg=function(t,n,r){var e=function(e,n){var t=function(n){return B(n,function(n){return n.items===undefined?!Mn(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<t(n.items).length?[n]:[]})},r=t(n);return Og.expand(r)}(t,n);return Sg({formats:e,handle:function(n,e){t.undoManager.transact(function(){Fi.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},Cg=["undo","bold","italic","link","image","bullist","styleselect"],Dg=function(n){return B(n,function(n){return ye(n)?Dg(n):Jd(n)})},Mg=function(e,r){function n(n){return function(){return $s.forToolbarCommand(r,n)}}function t(n){return function(){return $s.forToolbarStateCommand(r,n)}}function o(n,e,t){return function(){return $s.forToolbarStateAction(r,n,e,t)}}function i(){return Eg(r,h,function(){r.fire("scrollIntoView")})}function u(n,e){return{isSupported:function(){var e=r.ui.registry.getAll().buttons;return n.forall(function(n){return Mn(e,n)})},sketch:e}}var c=n("undo"),a=n("redo"),f=t("bold"),s=t("italic"),l=t("underline"),d=n("removeformat"),m=o("unlink","link",function(){r.execCommand("unlink",null,!1)}),g=o("unordered-list","ul",function(){r.execCommand("InsertUnorderedList",null,!1)}),p=o("ordered-list","ol",function(){r.execCommand("InsertOrderedList",null,!1)}),h=kg(r,r.settings);return{undo:u(An.none(),c),redo:u(An.none(),a),bold:u(An.none(),f),italic:u(An.none(),s),underline:u(An.none(),l),removeformat:u(An.none(),d),link:u(An.none(),function(){return function(e,t){return $s.forToolbarStateAction(t,"link","link",function(){var n=Bm(e,t);e.setContextToolbar(n),fm(t,function(){e.focusToolbar()}),cm.query(t).each(function(n){t.selection.select(n.dom())})})}(e,r)}),unlink:u(An.none(),m),image:u(An.none(),function(){return ed(r)}),bullist:u(An.some("bullist"),g),numlist:u(An.some("numlist"),p),fontsizeselect:u(An.none(),function(){return function(n,e){var t={onChange:function(n){Ll.apply(e,n)},getInitialValue:function(){return Ll.get(e)}};return Fs(n,"font-size",function(){return Vs(t)},e)}(e,r)}),forecolor:u(An.none(),function(){return _l(e,r)}),styleselect:u(An.none(),function(){return $s.forToolbar("style-formats",function(n){r.fire("toReading"),e.dropup().appear(i,Fi.on,n)},Bo([Fi.config({toggleClass:ji.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Ei.config({channels:Cn([Vi(Uo.orientationChanged(),Fi.off),Vi(Uo.dropupDismissed(),Fi.off)])})]),r)})}},Ig=function(n,t){var e=Qd(n),r={};return B(e,function(n){var e=!Mn(r,n)&&Mn(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},Rg=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},Fg=v(!0),Ag=tinymce.util.Tools.resolve("tinymce.util.Delay"),Bg=rm,Vg=function(r,e){var n=Je.fromDom(r),o=null,t=em(n,"orientationchange",function(){Ag.clearInterval(o);var n=rm(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){Ag.clearInterval(o);var e=r.innerHeight,t=0;o=Ag.setInterval(function(){e!==r.innerHeight?(Ag.clearInterval(o),n(An.some(r.innerHeight))):20<t&&(Ag.clearInterval(o),n(An.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Ng=function(n){var e=L().os.isiOS(),t=rm(n).isPortrait();return e&&!t?n.screen.height:n.screen.width};function jg(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?An.none():An.some(e.touches[0])}function _g(t){var r=Zn(An.none()),o=Zn(!1),i=function n(t,r){var o=null;return{cancel:function(){null!==o&&(d.clearTimeout(o),o=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];o=d.setTimeout(function(){t.apply(null,n),o=null},r)}}}(function(n){t.triggerEvent(Le(),n),o.set(!0)},400),u=Cn([{key:Pn(),value:function(t){return jg(t).each(function(n){i.cancel();var e={x:v(n.clientX),y:v(n.clientY),target:t.target};i.schedule(t),o.set(!1),r.set(An.some(e))}),An.none()}},{key:Hn(),value:function(n){return i.cancel(),jg(n).each(function(e){r.get().each(function(n){!function(n,e){var t=Math.abs(n.clientX-e.x()),r=Math.abs(n.clientY-e.y());return 5<t||5<r}(e,n)||r.set(An.none())})}),An.none()}},{key:zn(),value:function(e){i.cancel();return r.get().filter(function(n){return cn(n.target(),e.target())}).map(function(n){return o.get()?(e.prevent(),!1):t.triggerEvent(He(),e)})}}]);return{fireIfReady:function(e,n){return Dt(u,n).bind(function(n){return n(e)})}}}var Pg=function(t){var e=_g({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return em(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return em(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Hg=6<=L().os.version.major,zg=function(r,e,t){function o(n){return!cn(n.start(),n.finish())||n.soffset()!==n.foffset()}function n(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(o);t.getByDom(e).each(!0===(n||mo(u).filter(function(n){return"input"===q(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?Fi.on:Fi.off)}var i=Pg(r),u=an(e),c=[em(r.body(),"touchstart",function(n){r.onTouchContent(),i.fireTouchstart(n)}),i.onTouchmove(),i.onTouchend(),em(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){lo(r.body())}),r.onToEditing(w),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!=t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0==Hg?[]:[em(Je.fromDom(r.win()),"blur",function(){t.getByDom(e).each(Fi.off)}),em(u,"select",n),em(r.doc(),"selectionchange",n)]);return{destroy:function(){C(c,function(n){n.unbind()})}}},Lg=function(n,e){var t=parseInt(Qr(n,e),10);return isNaN(t)?0:t};function Gg(n){return Mp.getOption(n)}function Ug(n){return function(n){return Gg(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||k(Ip,q(n))}function $g(n,e,t){var r=n.document.createRange();return function(t,n){n.fold(function(n){t.setStartBefore(n.dom())},function(n,e){t.setStart(n.dom(),e)},function(n){t.setStartAfter(n.dom())})}(r,e),function(t,n){n.fold(function(n){t.setEndBefore(n.dom())},function(n,e){t.setEnd(n.dom(),e)},function(n){t.setEndAfter(n.dom())})}(r,t),r}function Wg(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i}function Xg(n){return{left:v(n.left),top:v(n.top),right:v(n.right),bottom:v(n.bottom),width:v(n.width),height:v(n.height)}}function qg(n,e,t){return e(Je.fromDom(t.startContainer),t.startOffset,Je.fromDom(t.endContainer),t.endOffset)}function Yg(n,e){return function(n,e){var t=e.ltr();return t.collapsed?e.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Np.rtl(Je.fromDom(n.endContainer),n.endOffset,Je.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return qg(0,Np.ltr,t)}):qg(0,Np.ltr,t)}(0,function(o,n){return n.match({domRange:function(n){return{ltr:v(n),rtl:An.none}},relative:function(n,e){return{ltr:X(function(){return $g(o,n,e)}),rtl:X(function(){return An.some($g(o,e,n))})}},exact:function(n,e,t,r){return{ltr:X(function(){return Wg(o,n,e,t,r)}),rtl:X(function(){return An.some(Wg(o,t,r,n,e))})}}})}(n,e))}function Kg(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}function Jg(t,r,n,e,o){function i(n){var e=t.dom().createRange();return e.setStart(r.dom(),n),e.collapse(!0),e}var u=function(n){return Mp.get(n)}(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var i=r,u=1;u<o;u++){var c=n(u),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(n){return i(n).getBoundingClientRect()},n,e,o.right,u);return i(c)}function Qg(n){return Vr(n,Ug)}function Zg(n){return _p(n,Ug)}function np(n,e){return e-n.left<n.right-e}function ep(n,e,t){var r=n.dom().createRange();return r.selectNode(e.dom()),r.collapse(t),r}function tp(e,n,t){var r=e.dom().createRange();r.selectNode(n.dom());var o=r.getBoundingClientRect(),i=np(o,t);return(!0===i?Qg:Zg)(n).map(function(n){return ep(e,n,i)})}function rp(n,e,t){var r=e.dom().getBoundingClientRect(),o=np(r,t);return An.some(ep(n,e,o))}function op(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var i=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return jp(n,e,u,c)}(n,e,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))}function ip(n,e){var t=q(n);return"input"===t?Ap.after(n):k(["br","img"],t)?0===e?Ap.before(n):Ap.after(n):Ap.on(n,e)}function up(n,e,t,r){var o=function(n,e,t,r){var o=an(n).dom().createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o}(n,e,t,r),i=cn(n,t)&&e===r;return o.collapsed&&!i}function cp(n,e,t,r,o){!function(n,e){An.from(n.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(e)})}(n,Wg(n,e,t,r,o))}function ap(n,e,t,r,o){!function(u,n){Yg(u,n).match({ltr:function(n,e,t,r){cp(u,n,e,t,r)},rtl:function(n,e,t,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(o.extend)try{!function(n,e,t,r,o,i){e.collapse(t.dom(),r),e.extend(o.dom(),i)}(0,o,n,e,t,r)}catch(i){cp(u,t,r,n,e)}else cp(u,t,r,n,e)}})}(n,function(n,e,t,r){var o=ip(n,e),i=ip(t,r);return Vp.relative(o,i)}(e,t,r,o))}function fp(n){var e=Je.fromDom(n.anchorNode),t=Je.fromDom(n.focusNode);return up(e,n.anchorOffset,t,n.focusOffset)?An.some(Rp.create(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return An.some(Rp.create(Je.fromDom(e.startContainer),e.startOffset,Je.fromDom(t.endContainer),t.endOffset))}return An.none()}(n)}function sp(n){return An.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(fp)}function lp(n,e){return function(n){var e=n.getClientRects(),t=0<e.length?e[0]:n.getBoundingClientRect();return 0<t.width||0<t.height?An.some(t).map(Xg):An.none()}(function(i,n){return Yg(i,n).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}})}(n,e))}function dp(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:v(2),height:n.height}}function mp(n){return{left:v(n.left),top:v(n.top),right:v(n.right),bottom:v(n.bottom),width:v(n.width),height:v(n.height)}}function gp(t){if(t.collapsed){var r=Je.fromDom(t.startContainer);return fn(r).bind(function(n){var e=Vp.exact(r,t.startOffset,n,function(n){return"img"===q(n)?1:Gg(n).fold(function(){return at(n).length},function(n){return n.length})}(n));return lp(t.startContainer.ownerDocument.defaultView,e).map(dp).map(_)}).getOr([])}return ke(t.getClientRects(),mp)}function pp(n,e){Kr(n,zp,e)}function hp(n){return{top:v(n.top()),bottom:v(n.top()+n.height())}}function vp(n,e){var t=function(n){return Lg(n,zp)}(e),r=n.innerHeight;return r<t?An.some(t-r):An.none()}function yp(n){return An.some(Je.fromDom(n.dom().contentWindow.document.body))}function bp(n){return An.some(Je.fromDom(n.dom().contentWindow.document))}function xp(n){return An.from(n.dom().contentWindow)}function wp(n){return xp(n).bind(sp)}function Sp(n){return n.getFrame()}function Tp(n,t){return function(e){return e[n].getOrThunk(function(){var n=Sp(e);return function(){return t(n)}})()}}function Op(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return em(e,r,n)}})}function kp(n){return{left:v(n.left),top:v(n.top),right:v(n.right),bottom:v(n.bottom),width:v(n.width),height:v(n.height)}}function Ep(t,r){var o=null;return{cancel:function(){null!==o&&(d.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&d.clearTimeout(o),o=d.setTimeout(function(){t.apply(null,n),o=null},r)}}}function Cp(n){return"true"===Qr(n,ch)?function(n){return 0<n.dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(n)}(n):function(n){return 0<n.dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(n)}(n)}var Dp,Mp=function uy(t,r){var e=function(n){return t(n)?An.from(n.dom().nodeValue):An.none()};return{get:function(n){if(!t(n))throw new Error("Can only get "+r+" value of a "+r+" node");return e(n).getOr("")},getOption:e,set:function(n,e){if(!t(n))throw new Error("Can only set raw "+r+" value of a "+r+" node");n.dom().nodeValue=e}}}(tt,"text"),Ip=["img","br"],Rp={create:J("start","soffset","finish","foffset")},Fp=yt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Ap={before:Fp.before,on:Fp.on,after:Fp.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(y,y,y)}},Bp=yt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Vp={domRange:Bp.domRange,relative:Bp.relative,exact:Bp.exact,exactFromRange:function(n){return Bp.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){return function(n){return Je.fromDom(n.dom().ownerDocument.defaultView)}(function(n){return n.match({domRange:function(n){return Je.fromDom(n.startContainer)},relative:function(n,e){return Ap.getStart(n)},exact:function(n,e,t,r){return n}})}(n))},range:Rp.create},Np=yt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),jp=function(n,e,t,r){return tt(e)?function(e,t,r,o){var n=e.dom().createRange();n.selectNode(t.dom());var i=n.getClientRects();return wo(i,function(n){return Kg(n,r,o)?An.some(n):An.none()}).map(function(n){return Jg(e,t,r,o,n)})}(n,e,t,r):function(e,n,t,r){var o=e.dom().createRange(),i=at(n);return wo(i,function(n){return o.selectNode(n.dom()),Kg(o.getBoundingClientRect(),t,r)?jp(e,n,t,r):An.none()})}(n,e,t,r)},_p=function(n,i){var u=function(n){for(var e=at(n),t=e.length-1;0<=t;t--){var r=e[t];if(i(r))return An.some(r);var o=u(r);if(o.isSome())return o}return An.none()};return u(n)},Pp=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?gp(e.getRangeAt(0)):[]}),Hp=function(n){n.focus();var e=Je.fromDom(n.document.body);(mo().exists(function(n){return k(["input","textarea"],q(n))})?function(n){Ag.setTimeout(function(){n()},0)}:t)(function(){mo().each(lo),so(e)})},zp="data-"+ji.resolve("last-outer-height"),Lp=function(n,r){var e=Je.fromDom(r.document.body),t=em(Je.fromDom(n),"resize",function(){vp(n,e).each(function(t){(function(n){var e=Pp(n);return 0<e.length?An.some(e[0]).map(hp):An.none()})(r).each(function(n){var e=function(n,e,t){return e.top()>n.innerHeight||e.bottom()>n.innerHeight?Math.min(t,e.bottom()-n.innerHeight+50):0}(r,n,t);0!==e&&r.scrollTo(r.pageXOffset,r.pageYOffset+e)})}),pp(e,n.innerHeight)});pp(e,n.innerHeight);return{toEditing:function(){Hp(r)},destroy:function(){t.unbind()}}},Gp={getBody:Tp("getBody",yp),getDoc:Tp("getDoc",bp),getWin:Tp("getWin",xp),getSelection:Tp("getSelection",wp),getFrame:Sp,getActiveApi:function(c){var a=Sp(c);return yp(a).bind(function(u){return bp(a).bind(function(i){return xp(a).map(function(o){var n=Je.fromDom(i.dom().documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return function(n){return sp(n).map(function(n){return Vp.exact(n.start(),n.soffset(),n.finish(),n.foffset())})}(o).bind(function(n){return lp(o,n).orThunk(function(){return function(n){return sp(n).filter(function(n){return cn(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?An.some(e).map(kp):An.none()})}(o)})})}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,r){ap(o,n,e,t,r)}}),r=c.clearSelection.getOrThunk(function(){return function(){!function(n){n.getSelection().removeAllRanges()}(o)}});return{body:v(u),doc:v(i),win:v(o),html:v(n),getSelection:l(wp,a),setSelection:t,clearSelection:r,frame:v(a),onKeyup:Op(c,i,"onKeyup","keyup"),onNodeChanged:Op(c,i,"onNodeChanged","SelectionChange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})})})}},Up="data-ephox-mobile-fullscreen-style",$p="position:absolute!important;",Wp="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",Xp=L().os.isAndroid(),qp=function(n,e){function t(r){return function(n){var e=Qr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(Kr(n,Up,t),Kr(n,"style",r))}}var r=function(n,e,t){return Wi(n,function(n){return tn(n,e)},t)}(n,"*"),o=B(r,function(n){return function(n,e){return Xi(n,function(n){return tn(n,e)})}(n,"*")}),i=function(n){var e=di(n,"background-color");return e!==undefined&&""!==e?"background-color:"+e+"!important":"background-color:rgb(255,255,255)!important;"}(e);C(o,t("display:none!important;")),C(r,t($p+Wp+i)),t((!0===Xp?"":$p)+Wp+i)(n)},Yp=function(){var n=function(n){return on(n)}("["+Up+"]");C(n,function(n){var e=Qr(n,Up);"no-styles"!==e?Kr(n,"style",e):no(n,"style"),no(n,Up)})},Kp=function(){var e=Yi("head").getOrDie(),n=Yi('meta[name="viewport"]').getOrThunk(function(){var n=Je.fromTag("meta");return Kr(n,"name","viewport"),ft(e,n),n}),t=Qr(n,"content");return{maximize:function(){Kr(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?Kr(n,"content",t):Kr(n,"content","user-scalable=yes")}}},Jp=function(e,n){var t=Kp(),r=xd(),o=xd();return{enter:function(){n.hide(),io(e.container,ji.resolve("fullscreen-maximized")),io(e.container,ji.resolve("android-maximized")),t.maximize(),io(e.body,ji.resolve("android-scroll-reload")),r.set(Lp(e.win,Gp.getWin(e.editor).getOrDie("no"))),Gp.getActiveApi(e.editor).each(function(n){qp(e.container,n.body()),o.set(zg(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),co(e.container,ji.resolve("fullscreen-maximized")),co(e.container,ji.resolve("android-maximized")),Yp(),co(e.body,ji.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Qp=function(n,e){var t=Ns(gm.sketch({dom:Gs('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Bo([Fi.config({toggleClass:ji.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),r=function(t,r){var o=null;return{cancel:function(){null!==o&&(d.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=d.setTimeout(function(){t.apply(null,n),o=null},r))}}}(n,200);return gm.sketch({dom:Gs('<div class="${prefix}-disabled-mask"></div>'),components:[gm.sketch({dom:Gs('<div class="${prefix}-content-container"></div>'),components:[zs.sketch({dom:Gs('<div class="${prefix}-content-tap-section"></div>'),components:[t.asSpec()],action:function(n){r.throttle()},buttonBehaviours:Bo([Fi.config({toggleClass:ji.resolve("mask-tap-icon-selected")})])})]})]})},Zp=Or([Jt("editor",[Yt("getFrame"),Qt("getBody"),Qt("getDoc"),Qt("getWin"),Qt("getSelection"),Qt("setSelection"),Qt("clearSelection"),Qt("cursorSaver"),Qt("onKeyup"),Qt("onNodeChanged"),Qt("getCursorBox"),Yt("onDomChanged"),tr("onTouchContent",w),tr("onTapContent",w),tr("onTouchToolstrip",w),tr("onScrollToCursor",v({unbind:w})),tr("onScrollToElement",v({unbind:w})),tr("onToEditing",v({unbind:w})),tr("onToReading",v({unbind:w})),tr("onToolbarScrollStart",y)]),Yt("socket"),Yt("toolstrip"),Yt("dropup"),Yt("toolbar"),Yt("container"),Yt("alloy"),or("win",function(n){return an(n.socket).dom().defaultView}),or("body",function(n){return Je.fromDom(n.socket.dom().ownerDocument.body)}),tr("translate",y),tr("setReadOnly",w),tr("readOnlyOnInit",v(!0))]),nh=function(n){var e=Xt("Getting AndroidWebapp schema",Zp,n);Li(e.toolstrip,"width","100%");var t=Um(Qp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};ft(e.container,t.element());var o=Jp(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:w,enter:o.enter,exit:o.exit,destroy:w}},eh=v([Yt("dom"),tr("shell",!0),dc("toolbarBehaviours",[ug])]),th=v([tf({name:"groups",overrides:function(n){return{behaviours:Bo([ug.config({})])}}})]),rh=hf({name:"Toolbar",configFields:eh(),partFields:th(),factory:function(e,n,t,r){var o=function(n){return e.shell?An.some(n):Oc(n,e,"groups")},i=e.shell?{behaviours:[ug.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid,dom:e.dom,components:i.components,behaviours:gc(e.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,e){o(n).fold(function(){throw d.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){ug.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),oh=v([Yt("items"),(Dp=["itemSelector"],Jt("markers",ke(Dp,Yt))),dc("tgroupBehaviours",[Ba])]),ih=v([rf({name:"items",unit:"item"})]),uh=hf({name:"ToolbarGroup",configFields:oh(),partFields:ih(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,components:e,behaviours:gc(n.tgroupBehaviours,[Ba.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),ch="data-"+ji.resolve("horizontal-scroll"),ah={exclusive:function(n,e){return em(n,"touchmove",function(n){Qi(n.target(),e).filter(Cp).fold(function(){n.raw().preventDefault()},w)})},markAsHorizontal:function(n){Kr(n,ch,"true")}};function fh(){function e(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Gs('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Bo([sm("adhoc-scrollable-toolbar",!0===n.scrollable?[Do(function(n,e){Li(n.element(),"overflow-x","auto"),ah.markAsHorizontal(n.element()),bg.register(n.element())})]:[])]),components:[gm.sketch({components:[uh.parts().items({})]})],markers:{itemSelector:"."+ji.resolve("toolbar-group-item")},items:n.items}}function t(){rh.setGroups(r,o.get()),Fi.off(r)}var r=Um(rh.sketch({dom:Gs('<div class="${prefix}-toolbar"></div>'),components:[rh.parts().groups({})],toolbarBehaviours:Bo([Fi.config({toggleClass:ji.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),Ba.config({mode:"cyclic"})]),shell:!0})),n=Um(gm.sketch({dom:{classes:[ji.resolve("toolstrip")]},components:[$m(r)],containerBehaviours:Bo([Fi.config({toggleClass:ji.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),o=Zn([]);return{wrapper:v(n),toolbar:v(r),createGroups:function(n){return ke(n,i(uh.sketch,e))},setGroups:function(n){o.set(n),t()},setContextToolbar:function(n){Fi.on(r),rh.setGroups(r,n)},restoreToolbar:function(){Fi.isOn(r)&&t()},refresh:function(){},focus:function(){Ba.focusIn(r)}}}function sh(n,e){ug.append(n,$m(e))}function lh(n,e){ug.remove(n,e)}function dh(e,n){return n.getAnimationRoot.fold(function(){return e.element()},function(n){return n(e)})}function mh(n){return n.dimension.property}function gh(n,e){return n.dimension.getDimension(e)}function ph(n,e){var t=dh(n,e);Fd(t,[e.shrinkingClass,e.growingClass])}function hh(n,e){co(n.element(),e.openClass),io(n.element(),e.closedClass),Li(n.element(),mh(e),"0px"),pi(n.element())}function vh(n,e){co(n.element(),e.closedClass),io(n.element(),e.openClass),gi(n.element(),mh(e))}function yh(n,e,t,r){t.setCollapsed(),Li(n.element(),mh(e),gh(e,n.element())),pi(n.element()),ph(n,e),hh(n,e),e.onStartShrink(n),e.onShrunk(n)}function bh(n,e,t,r){var o=r.getOrThunk(function(){return gh(e,n.element())});t.setCollapsed(),Li(n.element(),mh(e),o),pi(n.element());var i=dh(n,e);co(i,e.growingClass),io(i,e.shrinkingClass),hh(n,e),e.onStartShrink(n)}function xh(n,e,t){var r=gh(e,n.element());("0px"===r?yh:bh)(n,e,t,An.some(r))}function wh(n,e,t){var r=dh(n,e),o=ao(r,e.shrinkingClass),i=gh(e,n.element());vh(n,e);var u=gh(e,n.element());(o?function(){Li(n.element(),mh(e),i),pi(n.element())}:function(){hh(n,e)})(),co(r,e.shrinkingClass),io(r,e.growingClass),vh(n,e),Li(n.element(),mh(e),u),t.setExpanded(),e.onStartGrow(n)}function Sh(n,e,t){var r=dh(n,e);return!0===ao(r,e.growingClass)}function Th(n,e,t){var r=dh(n,e);return!0===ao(r,e.shrinkingClass)}function Oh(e,t){var r=Um(gm.sketch({dom:{tag:"div",classes:[ji.resolve("dropup")]},components:[],containerBehaviours:Bo([ug.config({}),jh.config({closedClass:ji.resolve("dropup-closed"),openClass:ji.resolve("dropup-open"),shrinkingClass:ji.resolve("dropup-shrinking"),growingClass:ji.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),ug.set(n,[])},onGrown:function(n){e(),t()}}),Bi(function(n,e){o(w)})])})),o=function(n){d.window.requestAnimationFrame(function(){n(),jh.shrink(r)})};return{appear:function(n,e,t){!0===jh.hasShrunk(r)&&!1===jh.isTransitioning(r)&&d.window.requestAnimationFrame(function(){e(t),ug.set(r,[n()]),jh.grow(r)})},disappear:o,component:v(r),element:r.element}}function kh(n){return 8===n.raw().which&&!k(["input","textarea"],q(n.target()))&&!function(n,e,t){return Qi(n,e,t).isSome()}(n.target(),'[contenteditable="true"]')}function Eh(e,n){var t=Xt("Getting GUI events settings",Ph,n),r=L().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],o=_g(t),i=ke(r.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return em(e,n,function(e){o.fireIfReady(e,n).each(function(n){n&&e.kill()}),t.triggerEvent(n,e)&&e.kill()})}),u=Zn(An.none()),c=em(e,"paste",function(e){o.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),t.triggerEvent("paste",e)&&e.kill(),u.set(An.some(d.setTimeout(function(){t.triggerEvent(Ne(),e)},0)))}),a=em(e,"keydown",function(n){t.triggerEvent("keydown",n)?n.kill():!0===t.stopBackspace&&kh(n)&&n.prevent()}),f=function(n,e){return _h?tm(n,"focus",e):em(n,"focusin",e)}(e,function(n){t.triggerEvent("focusin",n)&&n.kill()}),s=Zn(An.none()),l=function(n,e){return _h?tm(n,"blur",e):em(n,"focusout",e)}(e,function(n){t.triggerEvent("focusout",n)&&n.kill(),s.set(An.some(d.setTimeout(function(){t.triggerEvent(Ve(),n)},0)))});return{unbind:function(){C(i,function(n){n.unbind()}),a.unbind(),f.unbind(),l.unbind(),c.unbind(),u.get().each(d.clearTimeout),s.get().each(d.clearTimeout)}}}function Ch(n,e){var t=Dt(n,"target").map(function(n){return n()}).getOr(e);return Zn(t)}function Dh(n,r,e,t,o,i){var u=n(r,t),c=function(n,e){var t=Zn(!1),r=Zn(!1);return{stop:function(){t.set(!0)},cut:function(){r.set(!0)},isStopped:t.get,isCut:r.get,event:v(n),setSource:e.set,getSource:e.get}}(e,o);return u.fold(function(){return i.logEventNoHandlers(r,t),Hh.complete()},function(e){var t=e.descHandler();return kd(t)(c),c.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),Hh.stopped()):c.isCut()?(i.logEventCut(r,e.element(),t.purpose()),Hh.complete()):fn(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),Hh.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),Hh.resume(n)})})}function Mh(n,e,t){var r=function(n){var e=Zn(!1);return{stop:function(){e.set(!0)},cut:w,isStopped:e.get,isCut:v(!1),event:v(n),setSource:o("Cannot set source of a broadcasted event"),getSource:o("Cannot get source of a broadcasted event")}}(e);return C(n,function(n){var e=n.descHandler();kd(e)(r)}),r.isStopped()}var Ih,Rh=function(n){return Um(zs.sketch({dom:Gs('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},Fh=function(){return Um(gm.sketch({dom:Gs('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Bo([ug.config({})])}))},Ah=function(n,e,t,r){(!0===t?Ho.toAlpha:Ho.toOmega)(r),(t?sh:lh)(n,e)},Bh=/* */Object.freeze({refresh:function(n,e,t){if(t.isExpanded()){gi(n.element(),mh(e));var r=gh(e,n.element());Li(n.element(),mh(e),r)}},grow:function(n,e,t){t.isExpanded()||wh(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&xh(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&yh(n,e,t)},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:Sh,isShrinking:Th,isTransitioning:function(n,e,t){return!0===Sh(n,e)||!0===Th(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?xh:wh)(n,e,t)},disableTransitions:ph}),Vh=/* */Object.freeze({exhibit:function(n,e){var t=e.expanded;return Ur(t?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:En(e.dimension.property,"0px")})},events:function(t,r){return Nr([function(n,e){return Hr(n)(e)}(Jn(),function(n,e){e.event().raw().propertyName===t.dimension.property&&(ph(n,t),r.isExpanded()&&gi(n.element(),t.dimension.property),(r.isExpanded()?t.onGrown:t.onShrunk)(n))})])}}),Nh=[Yt("closedClass"),Yt("openClass"),Yt("shrinkingClass"),Yt("growingClass"),Qt("getAnimationRoot"),Ko("onShrunk"),Ko("onStartShrink"),Ko("onGrown"),Ko("onStartGrow"),tr("expanded",!1),Kt("dimension",qt("property",{width:[ni("property","width"),ni("getDimension",function(n){return Df(n)+"px"})],height:[ni("property","height"),ni("getDimension",function(n){return $i(n)+"px"})]}))],jh=qr({fields:Nh,name:"sliding",active:Vh,apis:Bh,state:/* */Object.freeze({init:function(n){var e=Zn(n.expanded);return Ao({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:l(e.set,!1),setExpanded:l(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),_h=L().browser.isFirefox(),Ph=Pt([(Ih="triggerEvent",Kt(Ih,Rr)),tr("stopBackspace",!0)]),Hh=yt([{stopped:[]},{resume:["element"]},{complete:[]}]),zh=function(e,t,r,n,o,i){return Dh(e,t,r,n,o,i).fold(function(){return!0},function(n){return zh(e,t,r,n,o,i)},function(){return!1})},Lh=function(n,e,t,r,o){var i=Ch(t,r);return zh(n,e,t,r,i,o)},Gh=J("element","descHandler"),Uh=function(n,e){return{id:v(n),descHandler:v(e)}};function $h(){var i={};return{registerId:function(r,o,n){Nn(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=Hm(n,r),i[e]=t})},unregisterId:function(t){Nn(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return Dt(i,n).map(function(n){return _n(n,function(n,e){return Uh(e,n)})}).getOr([])},find:function(n,e,t){var r=Ct(e)(i);return ko(t,function(n){return function(t,r){return mf(r).fold(function(){return An.none()},function(n){var e=Ct(n);return t.bind(e).map(function(n){return Gh(r,n)})})}(r,n)},n)}}}function Wh(){function r(n){var e=n.element();return mf(e).fold(function(){return function(n,e){var t=vc(sf+n);return df(e,t),t}("uid-",n.element())},function(n){return n})}var o=$h(),i={},u=function(n){mf(n.element()).each(function(n){delete i[n],o.unregisterId(n)})};return{find:function(n,e,t){return o.find(n,e,t)},filter:function(n){return o.filterByType(n)},register:function(n){var e=r(n);Mn(i,e)&&function(n,e){var t=i[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+bo(t.element())+"\nCannot use it for: "+bo(n.element())+"\nThe conflicting element is"+(K(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];o.registerId(t,e,n.events()),i[e]=n},unregister:u,getById:function(n){return Ct(n)(i)}}}var Xh=function(t){function r(e){return fn(t.element()).fold(function(){return!0},function(n){return cn(e,n)})}function o(n,e){return u.find(r,n,e)}function i(t){var n=u.filter(je());C(n,function(n){var e=n.descHandler();kd(e)(t)})}var u=Wh(),n=Eh(t.element(),{triggerEvent:function(e,t){return qo(e,t.target(),function(n){return function(n,e,t,r){var o=t.target();return Lh(n,e,t,o,r)}(o,e,t,n)})}}),c={debugInfo:v("real"),triggerEvent:function(e,t,r){qo(e,t,function(n){Lh(o,e,r,t,n)})},triggerFocus:function(e,t){mf(e).fold(function(){so(e)},function(n){qo(Be(),e,function(n){!function(n,e,t,r,o){var i=Ch(t,r);Dh(n,e,t,r,i,o)}(o,Be(),{originator:v(t),kill:w,prevent:w,target:v(e)},e,n)})})},triggerEscape:function(n,e){c.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:Um,addToGui:function(n){f(n)},removeFromGui:function(n){s(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){a(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:v(!0)},e=function(n){n.connect(c),tt(n.element())||(u.register(n),C(n.components(),e),c.triggerEvent(Ge(),n.element(),{target:v(n.element())}))},a=function(n){tt(n.element())||(C(n.components(),a),u.unregister(n)),n.disconnect()},f=function(n){!function(n,e){mt(n,e,ft)}(t,n)},s=function(n){yn(n)},l=function(n){i({universal:v(!0),data:v(n)})},d=function(n,e){i({universal:v(!1),channels:v(n),data:v(e)})},m=function(n,e){var t=u.filter(n);return Mh(t,e)},g=function(n){return u.getById(n).fold(function(){return vt.error(new Error('Could not find component with uid: "'+n+'" in system.'))},vt.value)},p=function(n){var e=mf(n).getOr("not found");return g(e)};return e(t),{root:v(t),element:t.element,destroy:function(){n.unbind(),st(t.element())},add:f,remove:s,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:a,broadcast:l,broadcastOn:d,broadcastEvent:m}},qh=v(ji.resolve("readonly-mode")),Yh=v(ji.resolve("edit-mode"));function Kh(n){var e=Um(gm.sketch({dom:{classes:[ji.resolve("outer-container")].concat(n.classes)},containerBehaviours:Bo([Ho.config({alpha:qh(),omega:Yh()})])}));return Xh(e)}var Jh=function(n,e){var t=Je.fromTag("input");li(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),ft(n,t),so(t),e(t),st(t)},Qh=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Zh=function(n,e){mo().each(function(n){cn(n,e)||lo(n)}),n.focus(),so(Je.fromDom(n.document.body)),Qh(n)},nv={stubborn:function(n,e,t,r){function o(){Zh(e,r)}var i=em(t,"keydown",function(n){k(["input","textarea"],q(n.target()))||o()});return{toReading:function(){Jh(n,lo)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){function o(){lo(r)}return{toReading:function(){o()},toEditing:function(){Zh(e,r)},onToolbarTouch:function(){o()},destroy:w}}},ev=function(e,r,t,o,n){function i(){r.run(function(n){n.refreshSelection()})}function u(n,e){var t=n-o.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})}function c(){r.run(function(n){n.clearSelection()})}function a(){e.getCursorBox().each(function(n){u(n.top(),n.height())}),r.run(function(n){n.syncHeight()})}var f=Pg(e),s=Ep(a,300),l=[e.onKeyup(function(){c(),s.throttle()}),e.onNodeChanged(i),e.onDomChanged(s.throttle),e.onDomChanged(i),e.onScrollToCursor(function(n){n.preventDefault(),s.throttle()}),e.onScrollToElement(function(n){n.element(),u(r,o)}),e.onToEditing(function(){r.run(function(n){n.toEditing()})}),e.onToReading(function(){r.run(function(n){n.toReading()})}),em(e.doc(),"touchend",function(n){cn(e.html(),n.target())||cn(e.body(),n.target())}),em(t,"transitionend",function(n){"height"===n.raw().propertyName&&function(){var e=$i(t);r.run(function(n){n.setViewportOffset(e)}),i(),a()}()}),tm(t,"touchstart",function(n){r.run(function(n){n.highlightSelection()}),function(e){r.run(function(n){n.onToolbarTouch(e)})}(n),e.onTouchToolstrip()}),em(e.body(),"touchstart",function(n){c(),e.onTouchContent(),f.fireTouchstart(n)}),f.onTouchmove(),f.onTouchend(),em(e.body(),"click",function(n){n.kill()}),em(t,"touchmove",function(){e.onToolbarScrollStart()})];return{destroy:function(){C(l,function(n){n.unbind()})}}};var tv,rv,ov,iv,uv={},cv={exports:uv};tv=undefined,rv=uv,ov=cv,iv=undefined,function(n){"object"==typeof rv&&void 0!==ov?ov.exports=n():"function"==typeof tv&&tv.amd?tv([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function s(i,u,c){function a(e,n){if(!u[e]){if(!i[e]){var t="function"==typeof iv&&iv;if(!n&&t)return t(e,!0);if(f)return f(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[e]={exports:{}};i[e][0].call(o.exports,function(n){return a(i[e][1][n]||n)},o,o.exports,s,i,u,c)}return u[e].exports}for(var f="function"==typeof iv&&iv,n=0;n<c.length;n++)a(c[n]);return a}({1:[function(n,e,t){var r,o,i=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(n){if(r===setTimeout)return setTimeout(n,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(e){try{return r.call(null,n,0)}catch(e){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(n){o=c}}();var f,s=[],l=!1,d=-1;function m(){l&&f&&(l=!1,f.length?s=f.concat(s):d=-1,s.length&&g())}function g(){if(!l){var n=a(m);l=!0;for(var e=s.length;e;){for(f=s,s=[];++d<e;)f&&f[d].run();d=-1,e=s.length}f=null,l=!1,function t(n){if(o===clearTimeout)return clearTimeout(n);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(n);try{return o(n)}catch(e){try{return o.call(null,n)}catch(e){return o.call(this,n)}}}(n)}}function p(n,e){this.fun=n,this.array=e}function h(){}i.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new p(n,e)),1!==s.length||l||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],s(n,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,i._immediateFn(function(){var n=1===r._state?o.onFulfilled:o.onRejected;if(null!==n){var e;try{e=n(r._value)}catch(t){return void c(o.promise,t)}u(o.promise,e)}else(1===r._state?u:c)(o.promise,r._value)})):r._deferreds.push(o)}function u(n,e){try{if(e===n)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if(e instanceof i)return n._state=3,n._value=e,void a(n);if("function"==typeof t)return void s(function r(n,e){return function(){n.apply(e,arguments)}}(t,e),n)}n._state=1,n._value=e,a(n)}catch(o){c(n,o)}}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function f(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function s(n,e){var t=!1;try{n(function(n){t||(t=!0,u(e,n))},function(n){t||(t=!0,c(e,n))})}catch(r){if(t)return;t=!0,c(e,r)}}var n,t;n=this,t=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new f(n,e,t)),t},i.all=function(n){var a=Array.prototype.slice.call(n);return new i(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(o){return new i(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},i._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,f){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}f.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},f.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},f.clearTimeout=f.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},f.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},f.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},f._unrefActive=f.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},f.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),f.clearImmediate(e))}),e},f.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function av(n){d.setTimeout(function(){throw n},0)}function fv(n,e,t){return Math.abs(n-e)<=t?An.none():n<e?An.some(n+t):An.some(n-t)}function sv(e,t){return wo([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return function(n,e){return n?An.some(e):An.none()}(e<=n.width&&t<=n.height,n.keyboard)}).getOr({portrait:t/5,landscape:e/4})}function lv(n){var e=Bg(n).isPortrait(),t=function(n){return sv(n.screen.width,n.screen.height)}(n),r=e?t.portrait:t.landscape;return(e?n.screen.height:n.screen.width)-n.innerHeight>r?0:r}function dv(n,e){var t=an(n).dom().defaultView;return $i(n)+$i(e)-lv(t)}function mv(n){return Lg(n,Rv)}function gv(n,e){var t=function(n){return Qr(n,Fv)}(n);return Iv.fixed(n,t,e)}function pv(n,e){return Iv.scroller(n,e)}function hv(n){var e=mv(n);return("true"===Qr(n,Av)?pv:gv)(n,e)}function vv(n,e,t){var r=an(n).dom().defaultView.innerHeight;return Kr(n,Bv,r+"px"),r-e-t}function yv(n){var e=mi(n,"top").getOr("0");return parseInt(e,10)}function bv(n){return parseInt(n.dom().scrollTop,10)}function xv(n,e){var t=e+jv(n)+"px";Li(n,"top",t)}var wv=cv.exports.boltExport,Sv=function(n){var t=An.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){C(n,u)},u=function(e){t.each(function(n){d.setTimeout(function(){e(n)},0)})};return n(function(n){t=An.some(n),i(e),e=[]}),{get:r,map:function(t){return Sv(function(e){r(function(n){e(t(n))})})},isReady:o}},Tv={nu:Sv,pure:function(e){return Sv(function(n){n(e)})}},Ov=function(t){function n(n){t().then(n,av)}return{map:function(n){return Ov(function(){return t().then(n)})},bind:function(e){return Ov(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return Ov(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return Tv.nu(n)},toCached:function(){var n=null;return Ov(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},kv=function(n){return Ov(function(){return new wv(n)})},Ev=function(n){return Ov(function(){return wv.resolve(n)})},Cv=function(){var f=null;return{animate:function(r,o,n,i,e,t){function u(n){c=!0,e(n)}var c=!1;Ag.clearInterval(f);function a(n){Ag.clearInterval(f),u(n)}f=Ag.setInterval(function(){var t=r();fv(t,o,n).fold(function(){Ag.clearInterval(f),u(o)},function(n){if(i(n,a),!c){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(Ag.clearInterval(f),u(o))}})},t)}}},Dv=dv,Mv=function(n,e,t){var r=dv(e,t),o=$i(e)+$i(t)-r;Li(n,"padding-bottom",o+"px")},Iv=yt([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Rv="data-"+ji.resolve("position-y-fixed"),Fv="data-"+ji.resolve("y-property"),Av="data-"+ji.resolve("scrolling"),Bv="data-"+ji.resolve("last-window-height"),Vv=function(n){var e=qi(n,"["+Rv+"]");return ke(e,hv)},Nv=function(r,o,i,u){function n(){var n=t.innerHeight;return function(n){return Lg(n,Bv)}(r)<n}function e(){if(d){var n=$i(i),e=$i(u),t=vv(r,n,e);Kr(r,Rv,n+"px"),Li(r,"height",t+"px"),Mv(o,r,u)}}var t=an(r).dom().defaultView,c=function(n){var e=Qr(n,"style");li(n,{position:"absolute",top:"0px"}),Kr(n,Rv,"0px"),Kr(n,Fv,"top");return{restore:function(){Kr(n,"style",e||""),no(n,Rv),no(n,Fv)}}}(i),a=$i(i),f=$i(u),s=function(n,e,t){var r=Qr(t,"style");bg.register(t),li(t,{position:"absolute",height:e+"px",width:"100%",top:n+"px"}),Kr(t,Rv,n+"px"),Kr(t,Av,"true"),Kr(t,Fv,"top");return{restore:function(){bg.deregister(t),Kr(t,"style",r||""),no(t,Rv),no(t,Av),no(t,Fv)}}}(a,vv(r,a,f),r),l=function(n){var e=Qr(n,"style");li(n,{position:"absolute",bottom:"0px"}),Kr(n,Rv,"0px"),Kr(n,Fv,"bottom");return{restore:function(){Kr(n,"style",e||""),no(n,Rv),no(n,Fv)}}}(u),d=!0;return Mv(o,r,u),{setViewportOffset:function(n){Kr(r,Rv,n+"px"),e()},isExpanding:n,isShrinking:m(n),refresh:e,restore:function(){d=!1,c.restore(),s.restore(),l.restore()}}},jv=mv,_v=Cv(),Pv="data-"+ji.resolve("last-scroll-top"),Hv=function(t,r,o){return kv(function(n){var e=l(bv,t);_v.animate(e,r,15,function(n){t.dom().scrollTop=n,Li(t,"top",yv(t)+15+"px")},function(){t.dom().scrollTop=r,Li(t,"top",o+"px"),n(r)},10)})},zv=function(o,i){return kv(function(n){var e=l(bv,o);Kr(o,Pv,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);_v.animate(e,i,r,function(n,e){Lg(o,Pv)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,Kr(o,Pv,n))},function(){o.dom().scrollTop=i,Kr(o,Pv,i),n(i)},10)})},Lv=function(i,u){return kv(function(n){function e(n){Li(i,"top",n+"px")}var t=l(yv,i),r=Math.abs(u-t()),o=Math.ceil(r/10);_v.animate(t,u,o,e,function(){e(u),n(u)},10)})},Gv=function(e,t,r){var o=an(e).dom().defaultView;return kv(function(n){xv(e,r),xv(t,r),o.scrollTo(0,r),n(r)})};function Uv(i,n){return n(function(t){var r=[],o=0;0===i.length?t([]):C(i,function(n,e){n.get(function(e){return function(n){r[e]=n,++o>=i.length&&t(r)}}(e))})})}function $v(n,r){return n.fold(function(n,e,t){return function(n,e,t,r){return Li(n,e,t+r+"px"),Ev(r)}(n,e,r,t)},function(n,e){return function(n,e,t){var r=e+t,o=mi(n,"top").getOr(t),i=r-parseInt(o,10),u=n.dom().scrollTop+i;return Hv(n,u,r)}(n,r,e)})}function Wv(e,t,n,r,o,i){var u=function f(t){var r=Zn(Tv.pure({}));return{start:function(e){var n=Tv.nu(function(n){return t(e).get(n)});r.set(n)},idle:function(n){r.get().get(function(){n()})}}}(function(n){return Gv(e,t,n)}),c=Ep(function(){u.idle(function(){qv(n,r.pageYOffset).get(function(){(function(){var n=Pp(i);return An.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?An.some({top:v(e),bottom:v(e+n.height())}):An.none()})})().each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),u.start(0),o.refresh()})})},1e3),a=em(Je.fromDom(r),"scroll",function(){r.pageYOffset<0||c.throttle()});return qv(n,r.pageYOffset).get(y),{unbind:a.unbind}}var Xv=function(n,e,t,r,o){var i=Dv(e,t),u=l(Qh,n);i<r||i<o?zv(e,e.dom().scrollTop-i+o).get(u):r<0&&zv(e,e.dom().scrollTop+r).get(u)},qv=function(n,e){var t=Vv(n);return function(n){return Uv(n,kv)}(ke(t,function(n){return $v(n,e)}))},Yv=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),c=n.keyboardType(),a=n.outerWindow(),f=n.dropup(),s=Nv(r,e,o,f),l=c(n.outerBody(),t,rt(),u,o,i),d=Vg(a,{onChange:w,onReady:s.refresh});d.onAdjustment(function(){s.refresh()});var m=em(Je.fromDom(a),"resize",function(){s.isExpanding()&&s.refresh()}),g=Wv(o,r,n.outerBody(),a,s,t),p=function v(t,e){var n=t.document,r=Je.fromTag("div");function o(n){var e=Je.fromTag("span");return Rd(e,[ji.resolve("layer-editor"),ji.resolve("unfocused-selection")]),li(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e}io(r,ji.resolve("unfocused-selections")),ft(Je.fromDom(n.documentElement),r);var i=em(r,"touchstart",function(n){n.prevent(),Zh(t,e),u()}),u=function(){pn(r)};return{update:function(){u();var n=Pp(t),e=ke(n,o);gn(r,e)},isActive:function(){return 0<at(r).length},destroy:function(){i.unbind(),st(r)},clear:u}}(t,u),h=function(){p.clear()};return{toEditing:function(){l.toEditing(),h()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:h,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){Xv(t,r,f,n,e)},updateToolbarPadding:w,setViewportOffset:function(n){s.setViewportOffset(n),Lv(r,n).get(y)},syncHeight:function(){Li(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:s.refresh,destroy:function(){s.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),Jh(rt(),lo)}}},Kv=function(r,n){var o=Kp(),i=wd(),u=wd(),c=xd(),a=xd();return{enter:function(){n.hide();var t=Je.fromDom(d.document);Gp.getActiveApi(r.editor).each(function(n){i.set({socketHeight:mi(r.socket,"height"),iframeHeight:mi(n.frame(),"height"),outerScroll:d.document.body.scrollTop}),u.set({exclusives:ah.exclusive(t,"."+bg.scrollable())}),io(r.container,ji.resolve("fullscreen-maximized")),qp(r.container,n.body()),o.maximize(),Li(r.socket,"overflow","scroll"),Li(r.socket,"-webkit-overflow-scrolling","touch"),so(n.body());var e=nn(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);c.set(Yv(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:w,outerBody:r.body,outerWindow:r.win,keyboardType:nv.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),c.run(function(n){n.syncHeight()}),a.set(ev(n,c,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){c.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),a.clear(),c.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Li(r.socket,"height",n)}),n.iframeHeight.each(function(n){Li(r.editor.getFrame(),"height",n)}),d.document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),co(r.container,ji.resolve("fullscreen-maximized")),Yp(),bg.deregister(r.toolbar),gi(r.socket,"overflow"),gi(r.socket,"-webkit-overflow-scrolling"),lo(r.editor.getFrame()),Gp.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Jv=function(n){var e=Xt("Getting IosWebapp schema",Zp,n);Li(e.toolstrip,"width","100%"),Li(e.container,"position","relative");var t=Um(Qp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}},o=Kv(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:w}};function Qv(n,e,t){n.system().broadcastOn([Uo.formatChanged()],{command:e,state:t})}function Zv(m){return{getNotificationManagerImpl:function(){return{open:v({progressBar:{value:w},close:w,text:w,getEl:v(null),moveTo:w,moveRel:w,settings:{}}),close:w,reposition:w,getArgs:v({})}},renderUI:function(){var n=m.getElement(),e=ey(m);!1===function(n){return!1===n.settings.skin}(m)?(m.contentCSS.push(e.content),zo.DOM.styleSheetLoader.load(e.ui,ry(m))):ry(m)();function t(){m.fire("ScrollIntoView")}var f=L().os.isAndroid()?function c(n){var e=Kh({classes:[ji.resolve("android-container")]}),t=fh(),r=xd(),o=Rh(r),i=Fh(),u=Oh(w,n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:v(e),element:e.element,init:function(n){r.set(nh(n))},exit:function(){r.run(function(n){n.exit(),ug.remove(i,o)})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Ah(i,o,n,e.root())},socket:v(i),dropup:v(u)}}(t):function a(n){var e=Kh({classes:[ji.resolve("ios-container")]}),t=fh(),r=xd(),o=Rh(r),i=Fh(),u=Oh(function(){r.run(function(n){n.refreshStructure()})},n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:v(e),element:e.element,init:function(n){r.set(Jv(n))},exit:function(){r.run(function(n){ug.remove(i,o),n.exit()})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Ah(i,o,n,e.root())},socket:v(i),dropup:v(u)}}(t);!function(n,e){gt(n,e,dn)}(Je.fromDom(n),f.system());function s(n,e,t,r){!1===r&&m.selection.collapse();var o=i(n,e,t);f.setToolbarGroups(!0===r?o.readOnly:o.main),m.setMode(!0===r?"readonly":"design"),m.fire(!0===r?oy():iy()),f.updateMode(r)}function l(n,e){return m.on(n,e),{unbind:function(){m.off(n)}}}var r=n.ownerDocument.defaultView,d=Vg(r,{onChange:function(){f.system().broadcastOn([Uo.orientationChanged()],{width:Ng(r)})},onReady:w}),i=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}};return m.on("init",function(){f.init({editor:{getFrame:function(){return Je.fromDom(m.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:w}},onToReading:function(n){return l(oy(),n)},onToEditing:function(n){return l(iy(),n)},onScrollToCursor:function(e){m.on("ScrollIntoView",function(n){e(n)});return{unbind:function(){m.off("ScrollIntoView"),d.destroy()}}},onTouchToolstrip:function(){n()},onTouchContent:function(){(function(n){return go(n).bind(function(n){return f.system().getByDom(n).toOption()})})(Je.fromDom(m.editorContainer.querySelector("."+ji.resolve("toolbar")))).each($),f.restoreToolbar(),n()},onTapContent:function(n){var e=n.target();if("img"===q(e))m.selection.select(e.dom()),n.kill();else if("a"===q(e)){f.system().getByDom(Je.fromDom(m.editorContainer)).each(function(n){Ho.isAlpha(n)&&Go(e.dom())})}}},container:Je.fromDom(m.editorContainer),socket:Je.fromDom(m.contentAreaContainer),toolstrip:Je.fromDom(m.editorContainer.querySelector("."+ji.resolve("toolstrip"))),toolbar:Je.fromDom(m.editorContainer.querySelector("."+ji.resolve("toolbar"))),dropup:f.dropup(),alloy:f.system(),translate:w,setReadOnly:function(n){s(a,c,u,n)},readOnlyOnInit:function(){return!1}});var n=function(){f.dropup().disappear(function(){f.system().broadcastOn([Uo.dropupDismissed()],{})})},e={label:"The first group",scrollable:!1,items:[$s.forToolbar("back",function(){m.selection.collapse(),f.exit()},{},m)]},t={label:"Back to read only",scrollable:!1,items:[$s.forToolbar("readonly-back",function(){s(a,c,u,!0)},{},m)]},r=Mg(f,m),o=Ig(m.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=Zn([{label:"the action group",scrollable:!0,items:o},i]),c=Zn([{label:"The read only mode group",scrollable:!0,items:[]},i]),a=Zn({backToMask:[e],backToReadOnly:[t]});ty(f,m)}),m.on("remove",function(){f.exit()}),m.on("detach",function(){!function(e){var n=at(e.element());C(n,function(n){e.getByDom(n).each(lt)}),st(e.element())}(f.system()),f.system().destroy()}),{iframeContainer:f.socket().element().dom(),editorContainer:f.element().dom()}}}}var ny=tinymce.util.Tools.resolve("tinymce.EditorManager"),ey=function(n){var e=Dt(n.settings,"skin_url").fold(function(){return ny.baseURL+"/skins/ui/oxide"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},ty=function(r,n){var e=Bn(n.formatter.get());C(e,function(e){n.formatter.formatChanged(e,function(n){Qv(r,e,n)})}),C(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Qv(r,t,n)})})},ry=(v(["x-small","small","medium","large","x-large"]),function(n){function e(){n._skinLoaded=!0,n.fire("SkinLoaded")}return function(){n.initialized?e():n.on("init",e)}}),oy=v("toReading"),iy=v("toEditing");!function cy(){Lo.add("mobile",Zv)}()}(window);
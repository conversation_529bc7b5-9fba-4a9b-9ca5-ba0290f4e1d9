<template>
  <div>
    <export-excel
      :header="['资源名称', '开始学习时间', '结束学习时间', '资源学习时长', '学习进度']"
      :filter-val="['courseResourceName', 'creationTime','lastLearnTime', 'resLearnDuration', 'lastLearnProgress']"
      :field="{ 1: [2], 2: [2], 3: [3], 4: ['%'] }"
      :api-fn="userDetailRecordList"
      :query="listQuery"
      :paging="false"
    />
    <el-table
      v-loading="listLoading"
      :data="list"
      size="small"
      @sort-change="sortChange"
    >
      <el-table-column label="资源名称" prop="courseResourceName" sortable="courseResourceName" show-overflow-tooltip />
      <el-table-column label="开始学习时间" prop="creationTime" sortable="creationTime" width="140">
        <template slot-scope="{ row }">
          {{ row.creationTime | formatDateTime }}
        </template>
      </el-table-column>
      <el-table-column label="结束学习时间" prop="lastLearnTime" sortable="lastLearnTime" width="140">
        <template slot-scope="{ row }">
          {{ row.lastLearnTime | formatDateTime }}
        </template>
      </el-table-column>
      <el-table-column label="资源学习时长" prop="resLearnDuration" sortable="resLearnDuration" width="140">
        <template slot-scope="{ row }">
          {{ row.resLearnDuration | formatSecond }}
        </template>
      </el-table-column>
      <el-table-column label="学习进度" prop="lastLearnProgress" sortable="lastLearnProgress" width="120">
        <template slot-scope="{ row }">
          {{ row.lastLearnProgress.toFixed(2) }} %
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="listQuery.totalCount > 0"
      :total="listQuery.totalCount"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.MaxResultCount"
      @pagination="getRecordDetailList"
    />
  </div>
</template>
<script>
import { userDetailRecordList } from '@/api/course'
import Pagination from '@/components/Pagination'
export default {
  name: 'TCourseDetail',
  components: {
    Pagination
  },
  props: {
    userId: {
      reuqerd: true,
      type: String,
      default: ''
    },
    courseId: {
      reuqerd: true,
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        UserId: this.userId,
        CourseId: this.courseId,
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0

      }

    }
  },
  created() {
    this.getRecordDetailList()
  },
  methods: {
    userDetailRecordList(args) {
      return userDetailRecordList(args)
    },
    sortChange(data) {
      const { prop, order } = data
      if (!prop || !order) {
        this.getRecordDetailList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getRecordDetailList()
    },
    // // 获取必修课 选修课 课程记录详细信息
    getRecordDetailList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      userDetailRecordList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>

